import { create } from "zustand";

// Define the Policy interface
export interface Policy {
  id: number;
  attach_content: File | string | null;
  uuid: string;
  title: string;
  category: string;
  author_name: string;
  job_title: string;
  description: string;
  created_at: string;
  updated_at: string;
  study: {
    uuid: string;
    name: string;
    iras: string;
    description: string;
  };
  date_c?: string;
  policy_state: 'active' | 'superseded';
  access_level: 'patientAndStaff' | 'staff';
  policy_version: number;
  attachment_url?: string;
}

interface PolicyState {
  policy: Policy | null;
  searchTerm: string; // 🔥 Ajout de searchTerm
  setPolicy: (policy: Policy) => void;
  setSearchTerm: (term: string) => void; // 🔥 Ajout de setSearchTerm
}

export const usePolicyStore = create<PolicyState>((set) => ({
  policy: null,
  searchTerm: "", // ✅ Initialisation du searchTerm
  setPolicy: (policy) => set({ policy }),
  setSearchTerm: (term) => set({ searchTerm: term }), // ✅ Fonction pour modifier searchTerm
}));

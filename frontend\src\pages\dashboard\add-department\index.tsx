import "./adddepartment.css";
import { useNavigate } from "react-router-dom";
import React, { useEffect, useState, useRef } from "react"; // Import React and hooks
import AddDepHospModal from "@/components/modal/AddDepHospModal";
import NurtifyText from "@components/NurtifyText.tsx";
import NurtifyInput from "@components/NurtifyInput.tsx";
import NurtifyTextArea from "@components/NurtifyTextArea.tsx";
import { motion, AnimatePresence } from "framer-motion"; // Import motion and AnimatePresence
import { Building, MapPin, CheckCircle, Search, Loader2, Info } from "lucide-react"; // Import new icons
import { useCreateDepartmentMutation } from "@/hooks/department.query";
import { useCurrentUserQuery } from "@/hooks/user.query";

import { CountrySelect } from "react-country-state-city";
import "react-country-state-city/dist/react-country-state-city.css";

type Country = {
  id: number;
  name: string;
  iso2: string;
};

export default function AddDepartment() {
  const { data: currentUser } = useCurrentUserQuery();
  const hospitalUuid = currentUser?.hospital?.uuid || "";
  const hospitalName = currentUser?.hospital?.name || "";
  const createDepartmentMutation = useCreateDepartmentMutation();
  const [currentStep, setCurrentStep] = useState(1);
  const [showPopup, setShowPopup] = useState<boolean>(false);
  const [newDepartmentUuid, setNewDepartmentUuid] = useState<string | null>(null);
  const navigate = useNavigate();
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [warnings, setWarnings] = useState<Record<string, string>>({});
  const [warningTimeout, setWarningTimeout] = useState<NodeJS.Timeout | null>(null);
  

  const [formData, setFormData] = useState({
    name: "",
    phone_number: "",
    extension: "",
    hospital_uuid: "",
    primary_address: "",
    secondary_address: "",
    postcode: "",
    country: "United Kingdom",
  });

  // --- Postcode Autocomplete & Lookup States ---
  const [autocompleteSuggestions, setAutocompleteSuggestions] = useState<string[]>([]);
  const [showPostcodeAutocomplete, setShowPostcodeAutocomplete] = useState(false);
  const [isPostcodeApiLoading, setIsPostcodeApiLoading] = useState(false);
  const [postcodeApiError, setPostcodeApiError] = useState<string | null>(null);
  const autocompletePostcodeRef = useRef<HTMLUListElement>(null); // Ref for the autocomplete list

  // Debounce ref for postcode input
  const postcodeDebounceTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  const API_BASE_URL = 'https://api.postcodes.io';
  // --- End Postcode Autocomplete & Lookup States ---

  useEffect(() => {
    if (hospitalUuid) {
      setFormData(prev => ({
        ...prev,
        hospital_uuid: hospitalUuid,
      }));
    }
  }, [hospitalUuid]);

  const validateField = (name: string, value: string) => {
    const maxLengths: Record<string, number> = {
      name: 50,
      phone_number: 20,
      extension: 20,
      primary_address: 100, // Assuming max length for address
      secondary_address: 100, // Assuming max length for address
      postcode: 20,
      country: 50, // Increased max length for country name
    };

    // Fields that are required
    const requiredFields = [
      "name",
      "phone_number",
      "extension",
      "primary_address",
      "secondary_address",
      "postcode",
      "country",
    ];

    if (requiredFields.includes(name) && !value.trim()) {
      return `${name.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase())} is required`;
    }

    if (maxLengths[name] && value.length > maxLengths[name]) {
      return `${name.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase())} must not exceed ${maxLengths[name]} characters`;
    }
    return '';
  };

  const handleChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>
  ) => {
    const { name, value } = e.target;

    if (name === "phone_number" || name === "extension") {
      const nonNumericChars = /[^0-9]/;
      if (nonNumericChars.test(value)) {
        if (warningTimeout) {
          clearTimeout(warningTimeout);
        }
        setWarnings(prev => ({
          ...prev,
          [name]: "Only numbers are allowed"
        }));
        const timeoutId = setTimeout(() => {
          setWarnings(prev => ({
            ...prev,
            [name]: ""
          }));
        }, 3000);
        setWarningTimeout(timeoutId);
        return;
      }
    }

    setFormData(prev => ({
      ...prev,
      [name]: value,
    }));

    if ((name === "phone_number" || name === "extension") && value === "") {
      setWarnings(prev => ({
        ...prev,
        [name]: ""
      }));
    }
    
    // Validate on change
    const error = validateField(name, value);
    setErrors(prev => ({
      ...prev,
      [name]: error
    }));
  };

  const [selectedCountry, setSelectedCountry] = useState<Country | null>({
      id: 232,
      name: "United Kingdom",
      iso2: "GB"
    });

  const handleCountrySelectChange = (val: Country | null) => {
    console.log("Selected country:", val);
    setSelectedCountry(val);
    setFormData((prev) => ({
      ...prev,
      country: val ? val.name : "", // Always store the name string
    }));
    setErrors(prev => ({
      ...prev,
      country: val ? '' : 'Country is required'
    }));
  };

  // --- Postcode Autocomplete & Lookup Functions ---
  const fetchPostcodeAutocompleteSuggestions = async (query: string) => {
    setIsPostcodeApiLoading(true);
    setPostcodeApiError(null);
    try {
      const response = await fetch(`${API_BASE_URL}/postcodes/${query}/autocomplete`);
      const data = await response.json();

      if (data.status === 200) {
        setAutocompleteSuggestions(data.result || []);
        setShowPostcodeAutocomplete(true);
      } else {
        setAutocompleteSuggestions([]);
        if (data.status !== 404) { // Don't show error for "not found" during autocomplete
          setPostcodeApiError(data.error);
        }
      }
    } catch {
      setPostcodeApiError('Failed to fetch postcode suggestions. Please try again.');
      setAutocompleteSuggestions([]);
    } finally {
      setIsPostcodeApiLoading(false);
    }
  };

  const fetchFullAddressDetails = async (pc: string) => {
    setIsPostcodeApiLoading(true);
    setPostcodeApiError(null);
    try {
      const response = await fetch(`${API_BASE_URL}/postcodes/${pc}`);
      const data = await response.json();

      if (data.status === 200 && data.result) {
        const result = data.result;
        setFormData(prev => ({
          ...prev,
          primary_address: result.line_1 || result.admin_county || result.parish || '',
          secondary_address: result.line_2 || result.admin_ward || '',
          country: result.country || 'United Kingdom',
          postcode: result.postcode
        }));
        if (result.country === 'United Kingdom') {
          setSelectedCountry({ id: 232, name: "United Kingdom", iso2: "GB" });
        }
        setShowPostcodeAutocomplete(false);
      } else {
        setPostcodeApiError(data.error || 'Postcode not found or invalid.');
        setFormData(prev => ({
          ...prev,
          primary_address: '',
          secondary_address: '',
        }));
      }
    } catch {
      setPostcodeApiError('Failed to lookup postcode details. Please try again.');
      setFormData(prev => ({
        ...prev,
        primary_address: '',
        secondary_address: '',
      }));
    } finally {
      setIsPostcodeApiLoading(false);
    }
  };

  // Effect for postcode autocomplete debouncing
  useEffect(() => {
    if (postcodeDebounceTimeoutRef.current) {
      clearTimeout(postcodeDebounceTimeoutRef.current);
    }

    if (formData.postcode.length > 1) {
      postcodeDebounceTimeoutRef.current = setTimeout(() => {
        fetchPostcodeAutocompleteSuggestions(formData.postcode);
      }, 300);
    } else {
      setAutocompleteSuggestions([]);
      setShowPostcodeAutocomplete(false);
    }

    return () => {
      if (postcodeDebounceTimeoutRef.current) {
        clearTimeout(postcodeDebounceTimeoutRef.current);
      }
    };
  }, [formData.postcode]);

  // Handle postcode input change (specific handler)
  const handlePostcodeInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData((prev) => ({
      ...prev,
      [name]: value,
    }));
    const error = validateField(name, value);
    setErrors(prev => ({
      ...prev,
      [name]: error
    }));
  };

  // Handle selecting a suggestion
  const handlePostcodeSuggestionSelect = (suggestion: string) => {
    setFormData(prev => ({
      ...prev,
      postcode: suggestion,
    }));
    setShowPostcodeAutocomplete(false);
    fetchFullAddressDetails(suggestion); // Immediately fetch full details
  };

  // Handle click outside for postcode autocomplete
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (autocompletePostcodeRef.current && !autocompletePostcodeRef.current.contains(event.target as Node)) {
        setShowPostcodeAutocomplete(false);
      }
    };
    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);
  // --- End Postcode Autocomplete & Lookup Functions ---


  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    // Validate all fields
    const newErrors: Record<string, string> = {};
    const fieldsToValidate: (keyof typeof formData)[] = [
        "name", "phone_number", "extension",
        "primary_address", "secondary_address", "postcode", "country"
    ];

    fieldsToValidate.forEach(key => {
      const error = validateField(key, formData[key]);
      if (error) newErrors[key] = error;
    });

    // Check for hospital_uuid (though it's auto-filled, good to be robust)
    if (!formData.hospital_uuid.trim()) {
        newErrors["hospital_uuid"] = "Hospital association is missing. Please refresh or contact support.";
    }

    setErrors(newErrors);

    if (Object.keys(newErrors).length === 0) {
      try {
        const formattedData = {
          ...formData,
          name: formData.name.trim().replace(/\s+/g, '_')
        };

        const response = await createDepartmentMutation.mutateAsync(formattedData);
        const departmentUuid = response.uuid;
        setNewDepartmentUuid(departmentUuid);
        setShowPopup(true);
      } catch (error) {
        console.error("Error creating department:", error);
        // Potentially set a general error message here
      }
    } else {
      // Move to the step with errors
      const step1Fields: (keyof typeof formData)[] = ['name', 'phone_number', 'extension'];
      const hasStep1Errors = step1Fields.some(field => newErrors[field]);
      setCurrentStep(hasStep1Errors ? 1 : 2);
    }
  };

  const nextStep = () => {
    console.log("country", formData.country);
    const currentStepErrors: Record<string, string> = {};
    let fieldsToValidate: (keyof typeof formData)[] = []; // Define fieldsToValidate here

    if (currentStep === 1) {
      fieldsToValidate = ['name', 'phone_number', 'extension'];
    } else if (currentStep === 2) { // Add validation for step 2 fields
      fieldsToValidate = ['primary_address', 'secondary_address', 'postcode', 'country'];
    }

    fieldsToValidate.forEach(field => {
      const error = validateField(field, formData[field]);
      if (error) currentStepErrors[field] = error;
    });

    setErrors(prev => ({...prev, ...currentStepErrors}));

    if (Object.keys(currentStepErrors).length === 0) {
      setCurrentStep(currentStep + 1);
    }
  };

  const prevStep = () => {
    if (currentStep > 1) {
      setCurrentStep(currentStep - 1);
    }
  };

  const handlePopupYes = () => {
    if (newDepartmentUuid) {
      navigate(`/org/dashboard/department-details/${newDepartmentUuid}/departmentAdmins`);
    }
    setShowPopup(false);
  };

  const handlePopupNo = () => {
    navigate("/org/dashboard/department", { replace: true });
    setShowPopup(false);
  };

  // Define maxLengths for direct use in input components
  const inputMaxLengths = {
    name: 50,
    phone_number: 20,
    extension: 20,
    primary_address: 100,
    secondary_address: 100,
    postcode: 20,
    country: 50,
  };

  return (
    <div className="add-department-container">
      <div className="add-department-header">
        <div className="add-department-title">
          <h1>
            <Building size={24} style={{ marginRight: "10px" }} />
            Add New Department
          </h1>
        </div>
        <div className="add-department-subtitle">
          <h6>Create and manage department information in your network</h6>
        </div>
      </div>

      {/* Progress Indicator */}
      <div className="department-progress">
        <div
          className={`department-step ${currentStep >= 1 ? "active" : ""} ${
            currentStep > 1 ? "completed" : ""
          }`}
        >
          <div className="department-step-indicator">
            {currentStep > 1 ? <CheckCircle size={16} /> : 1}
          </div>
          <div className="department-step-label">Department Info</div>
        </div>
        <div
          className={`department-step ${currentStep >= 2 ? "active" : ""}`}
        >
          <div className="department-step-indicator">2</div>
          <div className="department-step-label">Address Details</div>
        </div>
      </div>

      <form className="add-department-form" onSubmit={handleSubmit}>
        {/* Step 1: Department Information */}
        {currentStep === 1 && (
          <motion.div
            className="department-form-section"
            initial={{ opacity: 0, x: -20 }}
            animate={{ opacity: 1, x: 0 }}
            exit={{ opacity: 0, x: 20 }}
            transition={{ duration: 0.3 }}
          >
            <h3 className="department-form-section-title">
              <Building size={18} style={{ marginRight: "8px", verticalAlign: "middle" }} />
              Department Information
            </h3>
            <div className="row y-gap-30">
              <div className="col-md-6">
                <NurtifyText label="Department Name*" />
                <NurtifyInput
                  type="text"
                  name="name"
                  value={formData.name}
                  onChange={handleChange}
                  placeholder="Enter Department Name here"
                  maxLength={inputMaxLengths.name}
                />
                {errors.name && <div className="field-error">{errors.name}</div>}
              </div>
              <div className="col-md-6">
                <NurtifyText label="Hospital Name*" />
                <NurtifyInput
                  type="text"
                  name="hospital_name_display" // Use a different name to avoid confusion with formData
                  value={hospitalName}
                  disabled
                  placeholder="Hospital Name"
                />
                {/* No error display needed for a disabled field based on auto-fetched data */}
              </div>
            </div>
            <div className="row y-gap-30" style={{ marginTop: "20px" }}>
              <div className="col-md-6">
                <NurtifyText label="Phone*" />
                <NurtifyInput
                  type="text" // Changed from "number" for better UX with phone numbers
                  name="phone_number"
                  value={formData.phone_number}
                  onChange={handleChange}
                  placeholder="Enter Phone Number here"
                  maxLength={inputMaxLengths.phone_number}
                />
                {errors.phone_number && <div className="field-error">{errors.phone_number}</div>}
                {warnings.phone_number && <div className="field-warning">{warnings.phone_number}</div>}
              </div>
              <div className="col-md-6">
                <NurtifyText label="Extension*" />
                <NurtifyInput
                  type="text" // Changed from "number" for better UX
                  name="extension"
                  value={formData.extension}
                  onChange={handleChange}
                  placeholder="Enter Extension here"
                  maxLength={inputMaxLengths.extension}
                />
                {errors.extension && <div className="field-error">{errors.extension}</div>}
                {warnings.extension && <div className="field-warning">{warnings.extension}</div>}
              </div>
            </div>

            <div className="department-form-actions">
              <button
                type="button"
                className="department-btn-submit"
                onClick={nextStep}
              >
                Next
              </button>
            </div>
          </motion.div>
        )}

        {/* Step 2: Address Information */}
        {currentStep === 2 && (
          <motion.div
            className="department-form-section"
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            exit={{ opacity: 0, x: -20 }}
            transition={{ duration: 0.3 }}
          >
            <h3 className="department-form-section-title">
              <MapPin size={18} style={{ marginRight: "8px", verticalAlign: "middle" }} />
              Address Information
            </h3>
            <div className="row y-gap-30">
              <div className="col-md-6">
                <NurtifyText label="Country*" />
                <CountrySelect
                  containerClassName="form-group"
                  inputClassName=""
                  defaultValue={selectedCountry as any}
                  onChange={handleCountrySelectChange as any}
                  placeHolder="Select Country"
                  showFlag={true}
                />
                {errors.country && <div className="field-error">{errors.country}</div>}
              </div>
              <div className="col-md-6 relative"> {/* Added relative for positioning autocomplete */}
                <NurtifyText label="Postcode*" />
                <NurtifyInput
                  type="text"
                  name="postcode"
                  value={formData.postcode}
                  onChange={handlePostcodeInputChange} // Use specific handler for postcode
                  onFocus={() => formData.postcode.length > 1 && setShowPostcodeAutocomplete(true)}
                  placeholder="Enter Postcode here"
                  maxLength={inputMaxLengths.postcode}
                />
                {errors.postcode && <div className="field-error">{errors.postcode}</div>}

                {isPostcodeApiLoading && (
                  <div className="absolute z-20 w-full mt-1 p-2 bg-white rounded-xl shadow-lg flex items-center justify-center text-blue-600 text-sm">
                    <Loader2 className="animate-spin mr-2" size={16} />
                    <span>Searching...</span>
                  </div>
                )}

                <AnimatePresence>
                  {showPostcodeAutocomplete && autocompleteSuggestions.length > 0 && (
                    <motion.ul
                      ref={autocompletePostcodeRef}
                      initial={{ opacity: 0, y: -10 }}
                      animate={{ opacity: 1, y: 0 }}
                      exit={{ opacity: 0, y: -10 }}
                      transition={{ duration: 0.2 }}
                      style={{ listStyleType: 'none', paddingLeft: 0, margin: 0 }}
                      className="absolute z-10 w-full mt-1 bg-white border border-gray-200 rounded-xl shadow-lg max-h-40 overflow-y-auto"
                    >
                      {autocompleteSuggestions.map((suggestion, index) => (
                        <motion.li
                          key={index}
                          whileHover={{ backgroundColor: '#f0f4f8' }}
                          onClick={() => handlePostcodeSuggestionSelect(suggestion)}
                          className="cursor-pointer p-2 border-b border-gray-100 last:border-b-0 text-gray-800 text-sm"
                        >
                          <Search className="inline-block mr-2 text-gray-400" size={14} /> {String(suggestion)}
                        </motion.li>
                      ))}
                    </motion.ul>
                  )}
                </AnimatePresence>

                {postcodeApiError && (
                  <motion.div
                    initial={{ opacity: 0, y: -10 }}
                    animate={{ opacity: 1, y: 0 }}
                    className="mt-2 flex items-center p-2 bg-red-100 text-red-700 rounded-xl text-xs"
                  >
                    <Info className="mr-1" size={14} />
                    <span>{postcodeApiError}</span>
                  </motion.div>
                )}
              </div>
            </div>

            <div className="row y-gap-30" style={{ marginTop: "20px" }}>
              <div className="col-md-6">
                <NurtifyText label="Address Line 1*" />
                <NurtifyTextArea // Using TextArea for addresses usually makes sense
                  name="primary_address"
                  value={formData.primary_address}
                  onChange={handleChange}
                  placeholder="Enter Address Line 1"
                  maxLength={inputMaxLengths.primary_address}
                />
                {errors.primary_address && <div className="field-error">{errors.primary_address}</div>}
              </div>
              <div className="col-md-6">
                <NurtifyText label="Address Line 2*" />
                <NurtifyTextArea // Using TextArea
                  name="secondary_address"
                  value={formData.secondary_address}
                  onChange={handleChange}
                  placeholder="Enter Address Line 2"
                  maxLength={inputMaxLengths.secondary_address}
                />
                {errors.secondary_address && <div className="field-error">{errors.secondary_address}</div>}
              </div>
            </div>

            <div className="department-form-actions">
              <button
                type="button"
                className="department-btn-cancel"
                onClick={prevStep}
              >
                Back
              </button>
              <button
                type="submit"
                className="department-btn-submit"
                disabled={createDepartmentMutation.isPending}
              >
                {createDepartmentMutation.isPending ? "Submitting..." : "Add Department"}
              </button>
            </div>
          </motion.div>
        )}
      </form>
      {showPopup && (
        <AddDepHospModal
          type="Department"
          isOpen={showPopup}
          onNo={handlePopupNo}
          onYes={handlePopupYes}
        />
      )}
    </div>
  );
}

/* Pending Request Page Styles */

.patient-details-container {
  background-color: #fff;
  border-radius: 16px;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.05);
  padding: 24px;
}

.patient-details-header {
  margin-bottom: 24px;
}

.page-title {
  font-size: 28px;
  font-weight: 600;
  color: #111827;
  margin-bottom: 8px;
}

.loading {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
  width: 100%;
  font-size: 18px;
  color: #6B7280;
}

/* Filter styles */
.filter-section {
  background-color: #f9fafb;
  border-radius: 12px;
  padding: 16px;
  margin-bottom: 24px;
}

.filter-group {
  margin-bottom: 16px;
}

.detail-label {
  font-weight: 500;
  color: #4B5563;
  margin-bottom: 6px;
}

.filter-tag {
  padding: 6px 12px;
  border-radius: 20px;
  border: 1px solid #e5e7eb;
  background: #fff;
  color: #6b7280;
  font-size: 12px;
  font-weight: 500;
  cursor: pointer;
  margin-bottom: 8px;
  display: flex;
  align-items: center;
  gap: 6px;
  transition: all 0.15s ease;
}

.filter-tag:hover {
  border-color: #37B7C3;
  color: #37B7C3;
}

.filter-tag.active {
  background: #37B7C3;
  color: #fff;
  border-color: #37B7C3;
}

.sidebar__subtitle {
  font-size: 15px;
  font-weight: 600;
  color: #111827;
  margin-bottom: 12px;
}

.tag-item {
  padding: 6px 12px;
  background-color: #f3f4f6;
  border-radius: 20px;
  font-size: 12px;
  color: #4b5563;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.tag-item:hover {
  background-color: #e5e7eb;
}

/* Status styles */
.status-badge {
  padding: 5px 10px;
  border-radius: 15px;
  font-weight: 500;
  font-size: 0.875rem;
}

.status-badge.pending {
  background-color: #FEF3C7;
  color: #D97706;
}

.status-badge.approved {
  background-color: #D1FAE5;
  color: #059669;
}

.status-badge.rejected {
  background-color: #FEE2E2;
  color: #DC2626;
}

.status-badge.in-progress {
  background-color: #DBEAFE;
  color: #2563EB;
}

/* Source tag styles */
.source-tag {
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
}

.source-tag.doctor {
  background-color: #dbeafe;
  color: #2563eb;
}

.source-tag.patient {
  background-color: #e0f2fe;
  color: #0284c7;
}

/* Request detail section styles */
.request-detail {
  background-color: #fff;
  border-radius: 16px;
  padding: 24px;
}

.back-button {
  display: flex;
  align-items: center;
  background-color: transparent;
  border: none;
  cursor: pointer;
  color: #6B7280;
  transition: color 0.2s ease;
}

.back-button:hover {
  color: #111827;
}

/* Make DataTable responsive */
@media (max-width: 992px) {
  .content-wrapper {
    padding: 10px;
  }
  
  .patient-details-container {
    padding: 16px;
  }
}

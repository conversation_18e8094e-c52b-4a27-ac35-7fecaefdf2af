import { useEffect, useState } from "react";
import "./department-admins.css";
import { useCreateDepartmentAdminMutation, useDepartmentAdminsQuery, useResendActivationEmailMutation, useDeleteAdminMutation } from "@/hooks/user.query.ts";
import { Admin } from "@/types/types";
import { Trash2 } from "lucide-react";
import DeleteAdminModal from "@/components/modal/DeleteAdminModal";
import { useCurrentUserQuery } from "@/hooks/user.query";
import { getUserRole, can} from "@/services/permission-system";

interface DepartmentAdminsProps {
  uuid: string;
}

const SPECIALITY_CHOICES = [
    { value: "Registered Nurse", label: "Registered Nurse" },
    { value: "Senior Registered Nurse", label: "Senior Registered Nurse" },
    { value: "Doctor", label: "Doctor" },
    { value: "SpR Doctor", label: "SpR Doctor" },
    { value: "Consultant", label: "Consultant" },
    { value: "Professor", label: "Professor" },
    { value: "Pharmacist", label: "Pharmacist" },
    { value: "Senior Pharmacist", label: "Senior Pharmacist" },
    { value: "Clinical Research Practitioner", label: "Clinical Research Practitioner" },
    { value: "Advanced Clinical Practitioner", label: "Advanced Clinical Practitioner" },
    { value: "Clinical Research Associate", label: "Clinical Research Associate" },
    { value: "Primary Investigator", label: "Primary Investigator" },
    { value: "Sub-Investigator", label: "Sub-Investigator" },
    { value: "Lab Technician", label: "Lab Technician" },
    { value: "Receptionist", label: "Receptionist" },
    { value: "Data Manager", label: "Data Manager" },
    { value: "Matron", label: "Matron" },
    { value: "Senior Matron", label: "Senior Matron" },
    { value: "Lead Nurse", label: "Lead Nurse" },
    { value: "Ward Manager", label: "Ward Manager" },
    { value: "Charge Nurse", label: "Charge Nurse" },
    { value: "Clinical Practice Educator", label: "Clinical Practice Educator" },
    { value: "Trial Manager", label: "Trial Manager" },
    { value: "CRF Manager", label: "CRF Manager" },
    { value: "Study Coordinator", label: "Study Coordinator" },
    { value: "Portfolio Manager", label: "Portfolio Manager" },
    { value: "Other", label: "Other, please specify" },
];

const DepartmentAdmins: React.FC<DepartmentAdminsProps> = ({ uuid }) => {
  const [admins, setAdmins] = useState<Admin[]>([]);
  const [showAddSection, setShowAddSection] = useState(false);
  const [emailInput, setEmailInput] = useState("");
  const [speciality, setSpeciality] = useState("");

  const createAdminMutation = useCreateDepartmentAdminMutation();
  const resendActivationMutation = useResendActivationEmailMutation();
  const { data: adminData } = useDepartmentAdminsQuery(uuid);
  const { data: currentUser} = useCurrentUserQuery();
  const role = getUserRole(currentUser);
  useEffect(() => {
    if (adminData) {
      setAdmins(adminData);
      console.log("adminData", adminData);
    }
  }, [adminData]);

  const deleteAdminMutation = useDeleteAdminMutation();
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [deletableIdentifier, setDeletableIdentifier] = useState("");

  const handleSubmitModal = async () => {
    try {
      await deleteAdminMutation.mutateAsync({ identifier: deletableIdentifier });
      setIsModalOpen(false);
      setAdmins((prev: Admin[]) => prev.filter((admin) => admin.identifier !== deletableIdentifier));
    } catch (error) {
      console.error("Error deleting department:", error);
    }
  };

  const handleCloseModal = () => {
    setIsModalOpen(false);
  };

  const handleDeleteAdmin = (identifier: string) => {
    setDeletableIdentifier(identifier);
    setIsModalOpen(true);
  };

  const handleResendActivation = (identifier: string) => {
    resendActivationMutation.mutate({ identifier: identifier }, {
      onSuccess: () => {
        console.log(`Activation email resent to ${identifier}`);
      },
      onError: (error) => {
        console.error(`Failed to resend activation email to ${identifier}:`, error);
      },
    });
  };

  const handleAddNewAdmin = () => {
    if (emailInput && speciality) {
      const formData = new FormData();
      formData.append("email", emailInput);
      formData.append("department_uuid", uuid);
      formData.append("speciality", speciality);

      createAdminMutation.mutate(formData, {
        onSuccess: (newAdmin) => {
          const newAdminData: Admin = {
            identifier: newAdmin?.identifier || `${admins.length + 1}`,
            is_active: false,
            first_name: newAdmin?.first_name || "",
            last_name: newAdmin?.last_name || "",
            email: emailInput,
            phone_number: newAdmin?.phone_number || "",
            speciality: speciality,
          };
          setAdmins((prev) => [...prev, newAdminData]);
          setEmailInput("");
          setSpeciality("");
          setShowAddSection(false);
        },
        onError: (error) => {
          console.error("Failed to create admin:", error);
        },
      });
    }
  };

  const getInitials = (first_name: string, last_name: string) => {
    if (!first_name && !last_name) {
      return "NA";
    }
    return `${first_name.charAt(0)}${last_name.charAt(0)}`.toUpperCase();
  };

  const getStatus = (is_active: boolean) => {
    return is_active ? "Active" : "Invited";
  };

  return (
    <>
      <div className="header">
        <div>
          <p>Invite or manage your department's admins.</p>
        </div>
        {can(role, "DepartmentAdmin", "create") && (
        <button
          className={`add-member-btn ${showAddSection ? "active" : ""}`}
          onClick={() => {
            setShowAddSection(!showAddSection);
            if (showAddSection) {
              setEmailInput("");
              setSpeciality("");
            }
          }}
        >
          {showAddSection ? "Cancel" : "+ Add New Admin"}
        </button>
      )}
      </div>

      {showAddSection && (
        <div className="add-member-section">
          <div className="new-admin-form">
            <input
              type="email"
              value={emailInput}
              onChange={(e) => setEmailInput(e.target.value)}
              placeholder="Enter email address"
              className="enhanced-input"
            />
            <select
              value={speciality}
              onChange={(e) => setSpeciality(e.target.value)}
              className="enhanced-input"
            >
              <option value="" disabled>
                Select speciality
              </option>
              {SPECIALITY_CHOICES.map((option) => (
                <option key={option.value} value={option.value}>
                  {option.label}
                </option>
              ))}
            </select>
            <button
              onClick={handleAddNewAdmin}
              disabled={!emailInput || !speciality || createAdminMutation.isPending}
              className="action-btn primary"
            >
              {createAdminMutation.isPending ? "Inviting..." : "Invite"}
            </button>
          </div>
        </div>
      )}

      <div className="users-list">
        {admins.map((admin: Admin) => (
          <div key={admin.identifier} className="user-row">
            <div className="user-info">
              <div className="user-initials">
                {getInitials(admin.first_name ? admin.first_name : "", admin.last_name ? admin.last_name : "")}
              </div>
              <div>
                <p className="user-name">
                  {admin.first_name && admin.last_name
                    ? `${admin.first_name} ${admin.last_name}`
                    : "New Admin"}
                </p>
                <p className="user-email">{admin.email}</p>
              </div>
            </div>
            <div className="user-status">
              <span className={`status ${getStatus(admin.is_active || false).toLowerCase()}`}>
                {getStatus(admin.is_active || false)}
              </span>
            </div>
            <div className="user-actions">
              {!admin.is_active && (
                <button
                  className="resend-btn"
                  onClick={() => handleResendActivation(admin.identifier || '')}
                  disabled={resendActivationMutation.isPending || admin.is_active || resendActivationMutation.isSuccess}
                >
                  {resendActivationMutation.isPending ? "Resending..." : "Resend Activation"}
                </button>
              )}
              {can(role, "DepartmentAdmin", "delete") && (
              <div
                onClick={(e) => {
                  e.stopPropagation();
                  handleDeleteAdmin(admin.identifier || '');
                }}
                style={{ transition: 'transform 0.2s ease' }}
                onMouseEnter={(e) => {
                  e.currentTarget.style.transform = 'scale(1.2)';
                }}
                onMouseLeave={(e) => {
                  e.currentTarget.style.transform = 'scale(1)';
                }}
              >
                <Trash2 size={20} color="#E53935" style={{ cursor: 'pointer' }} />
              </div>
            )}
            </div>
          </div>
        ))}

        {isModalOpen && (
          <DeleteAdminModal
            isOpen={isModalOpen}
            onClose={handleCloseModal}
            onDelete={handleSubmitModal}
          />
        )}
      </div>
    </>
  );
};

export default DepartmentAdmins;
.outerCard {
  padding-left: 30px;
  padding-right: 10px;
  width: 100%;
  transform-origin: top;
  animation: expandCard 0.3s cubic-bezier(0.34, 1.56, 0.64, 1);
}

.innerCard {
  background: #FFFFFF;
  box-shadow: 0px 2px 8px rgba(0, 0, 0, 0.08);
  border-radius: 12px;
  padding: 16px;
  border: 1px solid #E5E7EB;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 16px;
}

@keyframes expandCard {
  from {
    transform: scaleY(0.6);
  }
  to {
    transform: scaleY(1);
  }
}

.innerCard:hover {
  box-shadow: 0px 4px 12px rgba(0, 0, 0, 0.12);
  transform: translateY(-2px);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.cardMainContent {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 12px;
  min-width: 0; /* Prevents flex child from overflowing */
}

.formTitleSection {
  display: flex;
  align-items: center;
  gap: 12px;
}

.formIcon {
  color: #37b7c3;
  flex-shrink: 0;
}

.formTitle {
  font-size: 16px;
  font-weight: 500;
  color: #140342;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.formMetadata {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
}

.metadataItem {
  display: flex;
  align-items: center;
  gap: 8px;
}

.metadataLabel {
  color: #6B7280;
  font-size: 14px;
}

.metadataValue {
  color: #140342;
  font-size: 14px;
  font-weight: 500;
}

.formButtons {
  display: flex;
  gap: 8px;
  flex-shrink: 0;
}

.iconButton {
  cursor: pointer;
  display: flex;
  justify-content: center;
  align-items: center;
  width: 32px;
  height: 32px;
  border-radius: 8px;
  border: none;
  transition: all 0.2s ease;
}

.copyIcon {
  background-color: #d7f1f3;
  color: #37b7c3;
}

.editIcon {
  background-color: #37b7c3;
  color: white;
}

.deleteIcon {
  background-color: #fee2e2;
  color: #ef4444;
}

.iconButton:hover {
  transform: translateY(-2px);
  filter: brightness(95%);
}

/* Responsive Styles */
@media (max-width: 768px) {
  .innerCard {
    flex-direction: column;
    align-items: flex-start;
    padding: 12px;
  }

  .formButtons {
    width: 100%;
    justify-content: flex-end;
    padding-top: 12px;
    border-top: 1px solid #E5E7EB;
    margin-top: 12px;
  }

  .formMetadata {
    flex-direction: column;
    gap: 8px;
  }
}

@media (max-width: 480px) {
  .formTitle {
    font-size: 14px;
  }

  .metadataLabel,
  .metadataValue {
    font-size: 13px;
  }

  .iconButton {
    width: 28px;
    height: 28px;
  }
}

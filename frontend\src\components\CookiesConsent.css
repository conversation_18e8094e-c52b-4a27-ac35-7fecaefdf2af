.cookies-consent-banner {
  position: fixed;
  right: 2vw;
  bottom: 2vw;
  z-index: 9999;
  display: flex;
  justify-content: flex-end;
  align-items: flex-end;
  background: transparent;
  pointer-events: none;
  width: auto;
  max-width: 100vw;
}

.cookies-consent-banner .card.cookies-consent-card {
  margin: 0;
  pointer-events: auto;
  font-family: var(--font-primary, "Poppins", sans-serif);
  background: #fff;
  border-radius: 16px;
  box-shadow: 0 6px 32px 0 rgba(55, 183, 195, 0.10), 0 1.5px 8px 0 rgba(55, 183, 195, 0.06);
  border: none;
  min-width: 320px;
  max-width: 380px;
  padding: 20px 28px 18px 28px;
  transition: box-shadow 0.2s;
}

.cookies-consent-banner .card.cookies-consent-card:hover {
  box-shadow: 0 12px 36px 0 rgba(55, 183, 195, 0.16), 0 2px 12px 0 rgba(55, 183, 195, 0.10);
}



.card.cookies-consent-card {
  width: 400px;
  height: auto;
  background: #f9f9f9;
  border-radius: 5px;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.1), 0 2px 3px rgba(0, 0, 0, 0.1);
  margin: 20px;
  padding: 0 10px;
}

.card-wrapper {
  display: inline-flex;
  flex-wrap: nowrap;
  align-items: center;
  width: 100%;
}

.card-icon {
  width: 20%;
}

.card-icon .icon-cart-box {
  background-color: #2196f3;
  width: 3em;
  height: 3em;
  border-radius: 50%;
  text-align: center;
  padding: 15px 0px;
  margin: 0 auto;
}

.card-content {
  width: 80%;
}

.card-title-wrapper {
  display: inline-flex;
  flex-wrap: nowrap;
  align-items: baseline;
  width: 100%;
}

.card-title {
  width: 100%;
  font-size: 1.08em;
  font-weight: 600;
  color: var(--color-dark-1, #140342);
  padding: 0 0 6px 0;
  letter-spacing: 0.1px;
}

.card-action {
  width: 5%;
  text-align: right;
  padding: 0 20px;
}

.card-action svg {
  cursor: pointer;
  fill: rgba(0, 0, 0, 0.2);
  transition: 0.3s ease-in-out;
}

.card-action svg:hover {
  fill: rgba(0, 0, 0, 0.6);
}

.card-text {
  font-size: 0.98em;
  color: var(--color-dark-3, #6A7A99);
  padding: 0 0 14px 0;
  line-height: 1.6;
}

.product-price {
  font-size: 0.9em;
  font-weight: 600;
  color: #333;
  padding: 0 0 10px 10px;
}

.btn-accept {
  font-size: 0.98em;
  font-weight: 600;
  padding: 7px 26px;
  margin: 0 0 0 0;
  border-radius: 10px;
  color: #fff;
  border: none;
  background: linear-gradient(90deg, #37B7C3 0%, #6440FB 100%);
  box-shadow: 0 2px 8px 0 rgba(55, 183, 195, 0.08);
  text-transform: none;
  cursor: pointer;
  transition: background 0.2s, color 0.2s, box-shadow 0.2s;
  font-family: var(--font-primary, "Poppins", sans-serif);
  letter-spacing: 0.1px;
}

.btn-accept:hover,
.btn-accept:active,
.btn-accept:focus {
  color: #fff;
  background: linear-gradient(90deg, #6440FB 0%, #37B7C3 100%);
  box-shadow: 0 4px 16px 0 rgba(55, 183, 195, 0.14);
  border: none;
}

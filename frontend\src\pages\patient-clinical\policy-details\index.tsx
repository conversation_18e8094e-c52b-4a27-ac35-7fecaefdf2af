import React from "react";
import { useParams, useNavigate } from "react-router-dom";
import { motion } from "framer-motion";
import { usePolicyQuery } from "@/hooks/policy.query";
import PdfViewer from "@/components/PdfViewer";
import "./policy-details.css";

const PatientPolicyDetails: React.FC = () => {
  const { uuid } = useParams<{ uuid: string }>();
  const navigate = useNavigate();
  const { data: policy, isLoading, error } = usePolicyQuery(uuid || "");

  if (isLoading) {
    return (
      <motion.div
        className="patclin-tab-content"
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ duration: 0.5 }}
      >
        <div className="policy-details-loading">
          <p>Loading policy details...</p>
        </div>
      </motion.div>
    );
  }

  if (error || !policy) {
    return (
      <motion.div
        className="patclin-tab-content"
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ duration: 0.5 }}
      >
        <div className="policy-details-error">
          <h2>Policy Not Found</h2>
          <p>The requested policy could not be found or you don't have permission to view it.</p>
          <button
            onClick={() => navigate("/patient/resources")}
            className="back-button"
          >
            Back to Resources
          </button>
        </div>
      </motion.div>
    );
  }

  return (
    <motion.div
      className="patclin-tab-content"
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      transition={{ duration: 0.5 }}
    >
      <div className="policy-details-header">
        <button
          onClick={() => navigate("/patient/resources")}
          className="back-button"
        >
          ← Back to Resources
        </button>
        <h1 className="policy-details-title">{policy.title}</h1>
      </div>

      <div className="policy-details-content">
        <div className="policy-info-section">
          <div className="policy-meta">
            <div className="policy-meta-item">
              <span className="policy-meta-label">Author:</span>
              <span className="policy-meta-value">{policy.author_name}</span>
            </div>
            <div className="policy-meta-item">
              <span className="policy-meta-label">Job Title:</span>
              <span className="policy-meta-value">{policy.job_title}</span>
            </div>
            {policy.date_c && (
              <div className="policy-meta-item">
                <span className="policy-meta-label">Date:</span>
                <span className="policy-meta-value">
                  {new Date(policy.date_c).toLocaleDateString()}
                </span>
              </div>
            )}
          </div>

          <div className="policy-description">
            <h3>Description</h3>
            <p>{policy.description}</p>
          </div>
        </div>

        {policy.attach_content && (
          <div className="policy-document-section">
            <h3>Policy Document</h3>
            <div className="policy-document-viewer">
              {(() => {
                const attachUrl = policy.attach_content ? `${policy.attach_content}` : null;
                const imageExtensions = [".png", ".jpg", ".jpeg", ".gif", ".bmp"];
                const isImage = attachUrl && imageExtensions.some(ext => attachUrl.toLowerCase().endsWith(ext));
                const isPdf = attachUrl && attachUrl.toLowerCase().endsWith(".pdf");

                if (isPdf) {
                  return <PdfViewer fileUrl={attachUrl} />;
                } else if (isImage) {
                  return (
                    <div className="pdf-viewer">
                      <img
                        src={attachUrl}
                        alt="Policy attachment"
                        style={{ maxWidth: "100%", height: "auto" }}
                      />
                    </div>
                  );
                } else {
                  return (
                    <div className="unsupported-format-message">
                      <p>Unsupported file format</p>
                    </div>
                  );
                }
              })()}
            </div>
          </div>
        )}

        {!policy.attach_content && (
          <div className="policy-no-document">
            <p>No document attached to this policy.</p>
          </div>
        )}
      </div>
    </motion.div>
  );
};

export default PatientPolicyDetails;

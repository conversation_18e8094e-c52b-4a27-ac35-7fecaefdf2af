/**
 * This utility overrides console methods to suppress logs in production mode.
 * Import this file once in your application's entry point (e.g., main.tsx).
 */

// Check if we're in production mode
const isProduction = import.meta.env.VITE_APP_MODE === 'production';

// Only apply in production mode
if (isProduction) {
  // Store original methods
  const originalConsole = { ...console };
  
  // Override non-error console methods to do nothing in production
  console.log = () => {};
  console.info = () => {};
  console.debug = () => {};
  console.warn = () => {};
  
  // Keep error logging for production issues
  console.error = originalConsole.error;
}

// No export needed, just import the file for the side effects

{"name": "nurtify-documentation", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc -b && vite build", "build:dev": "tsc -b && vite build --mode development", "lint": "eslint . --max-warnings 200", "preview": "vite preview", "prepare": "cd .. && husky install", "lint-staged": "lint-staged"}, "dependencies": {"@dnd-kit/core": "^6.3.1", "@dnd-kit/modifiers": "^9.0.0", "@dnd-kit/sortable": "^10.0.0", "@dnd-kit/utilities": "^3.2.2", "@emoji-mart/data": "^1.2.1", "@emoji-mart/react": "^1.1.1", "@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.0", "@fortawesome/fontawesome-svg-core": "^6.7.2", "@fortawesome/free-brands-svg-icons": "^6.7.2", "@fortawesome/free-regular-svg-icons": "^6.7.2", "@fortawesome/free-solid-svg-icons": "^6.7.2", "@fortawesome/react-fontawesome": "^0.2.2", "@fullcalendar/core": "^6.1.17", "@fullcalendar/daygrid": "^6.1.17", "@fullcalendar/interaction": "^6.1.17", "@fullcalendar/react": "^6.1.17", "@fullcalendar/timegrid": "^6.1.17", "@mui/material": "^6.4.0", "@popperjs/core": "^2.11.8", "@react-keycloak/web": "^3.4.0", "@react-pdf-viewer/core": "^3.12.0", "@react-pdf-viewer/default-layout": "^3.12.0", "@tanstack/react-query": "^5.66.0", "@tanstack/react-query-devtools": "^5.66.0", "@tanstack/react-table": "^8.21.3", "@types/react-datepicker": "^7.0.0", "@types/react-helmet-async": "^1.0.1", "@types/react-router-dom": "^5.3.3", "@types/react-signature-canvas": "^1.0.6", "axios": "^1.7.9", "bootstrap": "^5.3.3", "bpor-dev-resource": "^0.28.0", "date-fns": "^4.1.0", "emoji-mart": "^5.6.0", "framer-motion": "^11.18.1", "keycloak-js": "^26.2.0", "lodash": "^4.17.21", "lucide-react": "^0.473.0", "mynucui-bot": "^1.0.2", "pdfjs-dist": "^3.11.174", "react": "^18.3.1", "react-countdown-circle-timer": "^3.2.1", "react-country-state-city": "^1.1.12", "react-datepicker": "^7.6.0", "react-dom": "^18.3.1", "react-feather": "^2.0.10", "react-helmet-async": "^2.0.5", "react-hot-toast": "^2.5.2", "react-router-dom": "^7.1.2", "react-signature-canvas": "^1.0.7", "recharts": "^2.15.1", "sonner": "^2.0.3", "uuid": "^11.1.0", "zustand": "^5.0.3"}, "devDependencies": {"@eslint/js": "^9.17.0", "@types/lodash": "^4.17.15", "@types/node": "^22.10.7", "@types/react": "^18.3.18", "@types/react-dom": "^18.3.5", "@vitejs/plugin-react-swc": "^3.5.0", "eslint": "^9.17.0", "eslint-plugin-react-hooks": "^5.0.0", "eslint-plugin-react-refresh": "^0.4.16", "globals": "^15.14.0", "husky": "^8.0.0", "lint-staged": "^15.2.0", "terser": "^5.37.0", "typescript": "~5.6.2", "typescript-eslint": "^8.18.2", "vite": "^6.0.5"}, "lint-staged": {"src/**/*.{js,jsx,ts,tsx}": ["eslint --fix"]}}
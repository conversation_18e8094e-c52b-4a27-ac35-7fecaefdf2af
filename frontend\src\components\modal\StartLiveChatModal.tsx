import "./StartLiveChatModal.css";
import { useState, useEffect } from "react";
import { Send, X, MessageSquare, Building, User, Users } from "lucide-react";
import {
  useCreateLiveChatMutation,
  useDepartmentStudyTeamUsersQuery,
} from "@/hooks/livechat.query";
import { useStudyEnrollmentsByPatientQuery } from "@/hooks/study.query";
import type {
  LiveChatCreateData,
  StudyEnrollment,
} from "@/services/api/livechat.types";
import { useCurrentUserQuery } from "@/hooks/user.query";

interface StartLiveChatModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSuccess: (chatUuid: string) => void;
}

const StartLiveChatModal: React.FC<StartLiveChatModalProps> = ({
  isOpen,
  onClose,
  onSuccess,
}) => {
  const [subject, setSubject] = useState("");
  const [message, setMessage] = useState("");
  const [isLoading, setIsLoading] = useState(false);
  const [selectedDepartment, setSelectedDepartment] = useState("");
  const [selectedStudy, setSelectedStudy] = useState("");
  const [showFilteredUsers, setShowFilteredUsers] = useState(false);
  const { data: currentUser } = useCurrentUserQuery();
  const createLiveChatMutation = useCreateLiveChatMutation();

  // Get patient UUID from current user
  const patientUuid = currentUser?.patient_uuid;

  // Query filtered users when department is selected
  const { data: filteredUsersData, isLoading: isLoadingUsers } =
    useDepartmentStudyTeamUsersQuery(
      patientUuid || "",
      selectedDepartment,
      !!patientUuid && !!selectedDepartment && showFilteredUsers
    );

  const { data: studyEnrollmentsData } = useStudyEnrollmentsByPatientQuery(
    patientUuid || ""
  );
  console.log("Study Enrollments:", studyEnrollmentsData);

  useEffect(() => {
    if (!isOpen) {
      // Reset form when modal closes
      setSubject("");
      setMessage("");
      setSelectedDepartment("");
      setSelectedStudy("");
      setIsLoading(false);
    }
  }, [isOpen]);

  useEffect(() => {
    if (currentUser?.departments && currentUser.departments.length > 0) {
      setSelectedDepartment(currentUser.departments[0].uuid);
    }
  }, [currentUser]);

  if (!isOpen) return null;

  const handleSendMessage = async () => {
    if (!subject || !message) {
      return;
    }

    setIsLoading(true);

    try {
      const chatData: LiveChatCreateData = {
        department_id: selectedDepartment,
        subject: subject,
        initial_message: message,
        study_id: selectedStudy,
      };

      console.log("Sending chat data:", chatData);
      const newChat = await createLiveChatMutation.mutateAsync(chatData);
      console.log("Chat created successfully:", newChat);
      onSuccess(newChat.uuid);
      onClose();
    } catch (error) {
      console.error("Error creating live chat:", error);
      setIsLoading(false);
    }
  };

  const handleClose = () => {
    if (!isLoading) {
      onClose();
    }
  };

  return (
    <div className="modal-overlay" onClick={handleClose}>
      <div
        className="start-livechat-modal-content"
        onClick={(e) => e.stopPropagation()}
      >
        <button
          className="close-button"
          onClick={handleClose}
          disabled={isLoading}
        >
          <X size={24} />
        </button>
        <h2 className="modal-title">
          <MessageSquare size={28} />
          Start New Conversation
        </h2>

        <div className="form-container">
          <div className="form-group">
            <label htmlFor="department">
              <Building size={20} />
              Department
            </label>
            <select
              id="department"
              value={selectedDepartment}
              onChange={(e) => setSelectedDepartment(e.target.value)}
              disabled={isLoading}
            >
              {currentUser?.departments?.map((dept) => (
                <option key={dept.uuid} value={dept.uuid}>
                  {dept.hospital.name} - {dept.name}
                </option>
              ))}
            </select>
          </div>

          <div className="form-group">
            <label htmlFor="study">
              <Users size={20} />
              Study
            </label>
            <select
              id="study"
              value={selectedStudy || ""}
              onChange={(e) => setSelectedStudy(e.target.value)}
              disabled={isLoading || !selectedDepartment}
            >
              <option value="">Select a study</option>
              {studyEnrollmentsData?.map((enrollment: StudyEnrollment) => (
                <option key={enrollment.uuid} value={enrollment.study_uuid_read}>
                  {enrollment.study_name}
                </option>
              ))}
            </select>
          </div>

          <div className="form-group">
            <label htmlFor="subject">
              <MessageSquare size={20} />
              Subject
            </label>
            <input
              id="subject"
              type="text"
              placeholder="Enter subject"
              value={subject}
              onChange={(e) => setSubject(e.target.value)}
              disabled={isLoading}
            />
          </div>

          <div className="form-group">
            <label htmlFor="message">
              <User size={20} />
              Message
            </label>
            <textarea
              id="message"
              placeholder="Type your message here..."
              value={message}
              onChange={(e) => setMessage(e.target.value)}
              disabled={isLoading}
              rows={4}
            />
          </div>

          {/* Preview Recipients Button */}
          {patientUuid && selectedDepartment && (
            <div className="form-group">
              <button
                type="button"
                className="preview-recipients-btn"
                onClick={() => setShowFilteredUsers(!showFilteredUsers)}
                disabled={isLoading}
              >
                <Users size={20} />
                {showFilteredUsers ? "Hide Recipients" : "Preview Recipients"}
              </button>
            </div>
          )}

          {/* Show Filtered Users */}
          {showFilteredUsers && (
            <div className="filtered-users-section">
              <h4>Message will be sent to:</h4>
              {isLoadingUsers ? (
                <div className="loading-users">
                  <div className="loading-spinner-small"></div>
                  <span>Loading recipients...</span>
                </div>
              ) : filteredUsersData && filteredUsersData.users.length > 0 ? (
                <div className="users-list">
                  <div className="users-count">
                    {filteredUsersData.count} staff member
                    {filteredUsersData.count !== 1 ? "s" : ""} will receive this
                    message
                  </div>
                  <div className="users-grid">
                    {filteredUsersData.users.map((user) => (
                      <div key={user.identifier} className="user-card">
                        <div className="user-avatar">
                          {user.first_name.charAt(0)}
                          {user.last_name.charAt(0)}
                        </div>
                        <div className="user-info">
                          <div className="user-name">
                            {user.first_name} {user.last_name}
                          </div>
                          <div className="user-role">
                            {user.speciality || "Staff"}
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              ) : (
                <div className="no-users-message">
                  <p>
                    ⚠️ No staff members found for this department and your
                    studies.
                  </p>
                  <p>
                    Your message will still be sent, but may not reach anyone
                    immediately.
                  </p>
                </div>
              )}
            </div>
          )}
        </div>

        <div className="modal-actions">
          <button
            className="start-conversation-btn"
            onClick={handleSendMessage}
            disabled={
              !subject ||
              !message ||
              !selectedDepartment ||
              !selectedStudy ||
              isLoading
            }
          >
            {isLoading ? "Sending..." : "Send Message"}
            <Send size={20} />
          </button>
        </div>
      </div>
    </div>
  );
};

export default StartLiveChatModal;

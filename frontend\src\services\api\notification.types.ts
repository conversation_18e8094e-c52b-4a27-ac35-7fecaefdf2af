// General Notification System Types
export interface NotificationType {
  id: number;
  name: string;
  description: string;
  is_active: boolean;
  created_at: string;
  updated_at: string;
}

export interface SystemNotification {
  uuid: string;
  title: string;
  message: string;
  notification_type: NotificationType;
  notification_type_name: string;
  priority: 'low' | 'normal' | 'high' | 'urgent';
  status: 'pending' | 'sent' | 'failed' | 'cancelled';
  data: Record<string, any>;
  created_at: string;
  sent_at: string | null;
  read_at: string | null;
  send_push: boolean;
  send_email: boolean;
  send_sms: boolean;
  send_in_app: boolean;
}

export interface NotificationPreferences {
  id: number;
  push_enabled: boolean;
  email_enabled: boolean;
  sms_enabled: boolean;
  in_app_enabled: boolean;
  quiet_hours_start: string;
  quiet_hours_end: string;
  type_preferences: Record<string, {
    push: boolean;
    email: boolean;
    sms: boolean;
    in_app: boolean;
  }>;
  created_at: string;
  updated_at: string;
}

export interface PushToken {
  id: number;
  token: string;
  platform: 'web' | 'ios' | 'android';
  device_id: string;
  is_active: boolean;
  created_at: string;
  last_used: string;
}

export interface NotificationTemplate {
  id: number;
  name: string;
  notification_type: NotificationType;
  title_template: string;
  message_template: string;
  email_subject_template: string;
  email_body_template: string;
  sms_template: string;
  is_active: boolean;
  created_at: string;
  updated_at: string;
}

// Paginated response type
export interface NotificationPaginatedResponse<T> {
  count: number;
  next: string | null;
  previous: string | null;
  results: T[];
}

// WebSocket message types
export interface NotificationWebSocketMessage {
  type: 'notification' | 'notification_update' | 'unread_count_update' | 'unread_count';
  notification?: SystemNotification;
  count?: number;
}

// Request/Response types
export interface UnreadCountResponse {
  count: number;
}

export interface MarkAllReadResponse {
  updated_count: number;
}

export interface SendNotificationRequest {
  recipient_id: number;
  notification_type: string;
  title: string;
  message: string;
  data?: Record<string, any>;
  priority?: 'low' | 'normal' | 'high' | 'urgent';
  send_push?: boolean;
  send_email?: boolean;
  send_sms?: boolean;
  send_in_app?: boolean;
}

export interface SendBatchNotificationRequest {
  recipient_ids: number[];
  notification_type: string;
  title: string;
  message: string;
  data?: Record<string, any>;
  priority?: 'low' | 'normal' | 'high' | 'urgent';
  batch_name?: string;
  send_push?: boolean;
  send_email?: boolean;
  send_sms?: boolean;
  send_in_app?: boolean;
}

export interface RegisterDeviceRequest {
  token: string;
  platform: 'web' | 'ios' | 'android';
  device_id: string;
}

export interface UnregisterDeviceRequest {
  device_id?: string;
  token?: string;
}

export interface UpdatePreferencesRequest {
  push_enabled?: boolean;
  email_enabled?: boolean;
  sms_enabled?: boolean;
  in_app_enabled?: boolean;
  quiet_hours_start?: string;
  quiet_hours_end?: string;
  type_preferences?: Record<string, {
    push?: boolean;
    email?: boolean;
    sms?: boolean;
    in_app?: boolean;
  }>;
}

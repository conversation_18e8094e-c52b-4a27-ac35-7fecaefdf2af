/* User List Page Styles */
.users-container {
    background-color: #ffffff;
    border-radius: 0; /* Changed */
    box-shadow: none; /* Changed */
    padding: 15px 30px 30px 30px; /* Changed top padding */
    margin-bottom: 30px;
    width: 100%;
}

.users-header {
    display: flex;
    flex-direction: column;
    margin-bottom: 30px;
    padding-bottom: 15px;
    border-bottom: 1px solid #e5e7eb;
}

.users-title {
    margin-bottom: 10px;
}

.users-title h1 {
    font-size: 24px;
    font-weight: 600;
    color: #1a1a1a;
    display: flex;
    align-items: center;
    gap: 10px;
    margin: 0;
}

.users-subtitle {
    color: #4F547B;
    font-size: 16px;
    font-weight: 400;
    margin-top: 5px;
}

.users-subtitle h6 {
    margin: 0;
    font-weight: 400;
}

.users-controls {
    margin-bottom: 20px;
}

.users-search-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: 20px;
    margin-bottom: 20px;
}

.users-search-box {
    position: relative;
    flex: 1;
    max-width: 550px;
}

.users-search-box .search-icon {
    position: absolute;
    left: 12px;
    top: 50%;
    transform: translateY(-50%);
    color: #64748b;
    z-index: 1;
}

.users-add-button {
    min-width: 150px;
    height: 48px;
    background-color: #37B7C3;
    color: white !important;
    border: none;
    border-radius: 0; /* Changed */
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    text-decoration: none;
    padding: 0 20px;
    box-shadow: none; /* Changed */
}

.users-add-button:hover {
    background-color: #2A9A9F;
    transform: none; /* Changed */
    box-shadow: none; /* Changed */
}

.users-table-container {
    background-color: #ffffff;
    border-radius: 0; /* Changed */
    box-shadow: none; /* Changed */
    overflow: hidden;
    margin-bottom: 30px;
    border: 1px solid #e5e7eb;
}

.loading-message,
.error-message {
    padding: 30px;
    text-align: center;
    font-size: 16px;
    color: #4F547B;
    background-color: #f9fafb;
    border-radius: 0; /* Changed */
    margin: 20px 0;
}

.error-message {
    color: #ef4444;
    background-color: #fee2e2;
    border: 1px solid #fecaca;
}

.users-table {
    width: 100%;
    border-collapse: collapse;
}

.users-table thead {
    background-color: #f9fafb;
    border-bottom: 1px solid #e5e7eb;
}

.users-table th {
    padding: 16px;
    text-align: left;
    font-weight: 600;
    color: #4F547B;
    font-size: 14px;
}

.users-table td {
    padding: 16px;
    border-bottom: 1px solid #e5e7eb;
    color: #1a1a1a;
}

.users-table tr:last-child td {
    border-bottom: none;
}

.users-table tr:hover {
    background-color: #f9fafb;
}

.users-action-button {
    width: 36px;
    height: 36px;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: #37B7C3;
    color: white;
    border: none;
    border-radius: 0; /* Changed */
    cursor: pointer;
    transition: all 0.2s ease;
}

.users-action-button:hover {
    background-color: #2A9A9F;
    transform: none; /* Changed */
    box-shadow: none; /* Changed */
}

.users-actions {
    display: flex;
    gap: 8px;
}

/* Responsive Styles */
@media (max-width: 992px) {
    .users-container {
        padding: 25px;
    }
    
    .users-search-container {
        flex-direction: column;
        align-items: flex-start;
        gap: 15px;
    }
    
    .users-search-box {
        width: 100%;
        max-width: none;
    }
    
    .users-add-button {
        width: 100%;
        justify-content: center;
    }
}

@media (max-width: 768px) {
    .users-container {
        padding: 20px;
    }
    
    .users-title h1 {
        font-size: 20px;
    }
    
    .users-subtitle {
        font-size: 14px;
    }
    
    .users-table th, .users-table td {
        padding: 12px;
    }
}

@media (max-width: 576px) {
    .users-container {
        padding: 15px;
    }
    
    .users-table {
        display: block;
        overflow-x: auto;
    }
}

import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import {
  getUserDocuments,
  uploadDocument,
  getDocumentDetails,
  updateDocument,
  deleteDocument,
  downloadDocument,
  getDocumentTypes,
  getExpiredDocuments,
  getDocumentsExpiringSoon,
  getExpirySummary,
  type UserDocument,
  type DocumentUploadPayload,
  type DocumentUpdatePayload,
} from "@/services/api/document.service";

// Document query keys
export const DOCUMENT_KEYS = {
  GET_ALL: "document/getAll",
  GET_BY_UUID: "document/getById",
  GET_TYPES: "document/getTypes",
  GET_EXPIRED: "document/getExpired",
  GET_EXPIRING_SOON: "document/getExpiringSoon",
  GET_EXPIRY_SUMMARY: "document/getExpirySummary",
} as const;

// Get all user documents
export const useUserDocumentsQuery = () => {
  return useQuery<UserDocument[], Error>({
    queryKey: [DOCUMENT_KEYS.GET_ALL],
    queryFn: getUserDocuments,
    staleTime: 1000 * 60 * 5, // 5 minutes
    refetchOnWindowFocus: false,
  });
};

// Get document details
export const useDocumentDetailsQuery = (uuid: string) => {
  return useQuery<UserDocument, Error>({
    queryKey: [DOCUMENT_KEYS.GET_BY_UUID, uuid],
    queryFn: () => getDocumentDetails(uuid),
    enabled: !!uuid,
    staleTime: 1000 * 60 * 5, // 5 minutes
  });
};

// Get available document types
export const useDocumentTypesQuery = () => {
  return useQuery<string[], Error>({
    queryKey: [DOCUMENT_KEYS.GET_TYPES],
    queryFn: getDocumentTypes,
    staleTime: 1000 * 60 * 60, // 1 hour (document types don't change often)
    refetchOnWindowFocus: false,
  });
};

// Get expired documents
export const useExpiredDocumentsQuery = () => {
  return useQuery<UserDocument[], Error>({
    queryKey: [DOCUMENT_KEYS.GET_EXPIRED],
    queryFn: getExpiredDocuments,
    staleTime: 1000 * 60 * 5, // 5 minutes
    refetchOnWindowFocus: false,
  });
};

// Get documents expiring soon
export const useDocumentsExpiringSoonQuery = () => {
  return useQuery<UserDocument[], Error>({
    queryKey: [DOCUMENT_KEYS.GET_EXPIRING_SOON],
    queryFn: getDocumentsExpiringSoon,
    staleTime: 1000 * 60 * 5, // 5 minutes
    refetchOnWindowFocus: false,
  });
};

// Get expiry summary
export const useExpirySummaryQuery = () => {
  return useQuery<{
    total_documents: number;
    expired_documents: number;
    expiring_soon: number;
    no_expiry_date: number;
    valid_documents: number;
  }, Error>({
    queryKey: [DOCUMENT_KEYS.GET_EXPIRY_SUMMARY],
    queryFn: getExpirySummary,
    staleTime: 1000 * 60 * 5, // 5 minutes
    refetchOnWindowFocus: false,
  });
};

// Upload document mutation
export const useUploadDocumentMutation = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (payload: DocumentUploadPayload) => uploadDocument(payload),
    onSuccess: () => {
      // Invalidate and refetch documents list and expiry-related queries
      queryClient.invalidateQueries({ queryKey: [DOCUMENT_KEYS.GET_ALL] });
      queryClient.invalidateQueries({ queryKey: [DOCUMENT_KEYS.GET_EXPIRED] });
      queryClient.invalidateQueries({ queryKey: [DOCUMENT_KEYS.GET_EXPIRING_SOON] });
      queryClient.invalidateQueries({ queryKey: [DOCUMENT_KEYS.GET_EXPIRY_SUMMARY] });
    },
    onError: (error) => {
      console.error("Error uploading document:", error);
    },
  });
};

// Update document mutation
export const useUpdateDocumentMutation = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ uuid, payload }: { uuid: string; payload: DocumentUpdatePayload }) =>
      updateDocument(uuid, payload),
    onSuccess: (_, { uuid }) => {
      // Invalidate and refetch documents list and specific document
      queryClient.invalidateQueries({ queryKey: [DOCUMENT_KEYS.GET_ALL] });
      queryClient.invalidateQueries({ queryKey: [DOCUMENT_KEYS.GET_BY_UUID, uuid] });
      queryClient.invalidateQueries({ queryKey: [DOCUMENT_KEYS.GET_EXPIRED] });
      queryClient.invalidateQueries({ queryKey: [DOCUMENT_KEYS.GET_EXPIRING_SOON] });
      queryClient.invalidateQueries({ queryKey: [DOCUMENT_KEYS.GET_EXPIRY_SUMMARY] });
    },
    onError: (error) => {
      console.error("Error updating document:", error);
    },
  });
};

// Delete document mutation
export const useDeleteDocumentMutation = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (uuid: string) => deleteDocument(uuid),
    onSuccess: () => {
      // Invalidate and refetch documents list and expiry-related queries
      queryClient.invalidateQueries({ queryKey: [DOCUMENT_KEYS.GET_ALL] });
      queryClient.invalidateQueries({ queryKey: [DOCUMENT_KEYS.GET_EXPIRED] });
      queryClient.invalidateQueries({ queryKey: [DOCUMENT_KEYS.GET_EXPIRING_SOON] });
      queryClient.invalidateQueries({ queryKey: [DOCUMENT_KEYS.GET_EXPIRY_SUMMARY] });
    },
    onError: (error) => {
      console.error("Error deleting document:", error);
    },
  });
};

// Download document mutation
export const useDownloadDocumentMutation = () => {
  return useMutation({
    mutationFn: (uuid: string) => downloadDocument(uuid),
    onError: (error) => {
      console.error("Error downloading document:", error);
    },
  });
}; 
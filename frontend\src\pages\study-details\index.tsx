import React, { useState, useMemo } from "react";
import { use<PERSON><PERSON><PERSON>, useNavigate } from "react-router-dom";
import {
  FileText,
  Users,
  User,
  Eye,
  ArrowLeft,
  Download,
} from "lucide-react";
import DataTable from "@/components/common/DataTable";
import Preloader from "@/components/common/Preloader";
import Wrapper from "@/components/common/Wrapper";
import NurtifyFilter from "@/components/NurtifyFilter";
import { useStudyQuery } from "@/hooks/study.query";
import PdfDownloadButton from "@/pages/policy-list/PdfDownloadButton";
import { useActivePoliciesByStudyQuery } from "@/hooks/policy.query";
import { useStudyTeamMembersQuery } from "@/hooks/study.query";
import { useStudyPatientsByDepartmentQuery } from "@/hooks/patient.query";
import { Policy } from "@/services/api/types";
import "./study-details.css";

// Define policy categories to match backend model
const POLICY_CATEGORIES = [
  { label: "Clinical Trial Protocol", value: "CLINICAL_TRIAL_PROTOCOL" },
  { label: "Lab Manual", value: "LAB_MANUAL" },
  { label: "Pharmacy Manual", value: "PHARMACY_MANUAL" },
  { label: "Imaging Manual", value: "IMAGING_MANUAL" },
  { label: "ECG or Cardiac Monitoring Manual", value: "ECG_MANUAL" },
  { label: "Randomization and Unblinding Procedures", value: "RANDOMIZATION_PROCEDURES" },
  { label: "Patient Information Sheet", value: "PATIENT_INFO_SHEET" },
  { label: "Patient Education Brochures", value: "PATIENT_EDUCATION" },
  { label: "Safety Management Plan", value: "SAFETY_MANAGEMENT" },
  { label: "Site Visit Schedule", value: "SITE_VISIT_SCHEDULE" },
  { label: "Contact list of study personnel and site staff", value: "CONTACT_LIST_STUDY_PERSONNEL" },
  { label: "Contact details of external vendors", value: "CONTACT_DETAILS_EXTERNAL_VENDORS" },
  { label: "Current versions of the Investigator Brochure and Product Information Forms", value: "CURRENT_VERSIONS_INVESTIGATOR_BROCHURE" },
  { label: "Previous submitted versions", value: "PREVIOUS_SUBMITTED_VERSIONS" },
  { label: "Current approved version", value: "CURRENT_APPROVED_VERSION_1" },
  { label: "Previous approved versions and updates", value: "PREVIOUS_APPROVED_VERSIONS_UPDATES" },
  { label: "Signature pages", value: "SIGNATURE_PAGES" },
  { label: "Current approved versions", value: "CURRENT_APPROVED_VERSIONS_2" },
  { label: "Previous approved versions", value: "PREVIOUS_APPROVED_VERSIONS_2" },
  { label: "Signed Informed Consent forms", value: "SIGNED_INFORMED_CONSENT_FORMS" },
  { label: "Signed Informed Consent Tracking Log", value: "SIGNED_INFORMED_CONSENT_TRACKING_LOG" },
  { label: "Patient Card, Diary or Questionnaire", value: "PATIENT_CARD_DIARY_QUESTIONNAIRE" },
  { label: "Current approved versions", value: "CURRENT_APPROVED_VERSIONS_3" },
  { label: "Previous approved copies", value: "PREVIOUS_APPROVED_COPIES" },
  { label: "Translation certificates", value: "TRANSLATION_CERTIFICATES_1" },
  { label: "Current approved version", value: "CURRENT_APPROVED_VERSION_2" },
  { label: "Previous approved versions", value: "PREVIOUS_APPROVED_VERSIONS_3" },
  { label: "Translation certificates", value: "TRANSLATION_CERTIFICATES_2" },
  { label: "Current version (blank sample)", value: "CURRENT_VERSION_BLANK_SAMPLE" },
  { label: "Previous version (blank sample)", value: "PREVIOUS_VERSION_BLANK_SAMPLE" },
  { label: "Completion guidelines", value: "COMPLETION_GUIDELINES" },
  { label: "Signed, dated and completed CRFs", value: "SIGNED_DATED_COMPLETED_CRFS" },
  { label: "Documentation of CRF edits made", value: "DOCUMENTATION_CRF_EDITS" },
  { label: "Initial submission", value: "INITIAL_SUBMISSION" },
  { label: "Amendments", value: "AMENDMENTS" },
  { label: "Progress Reports", value: "PROGRESS_REPORTS" },
  { label: "Ethics Composition", value: "ETHICS_COMPOSITION" },
  { label: "Notification of Safety Reports", value: "NOTIFICATION_SAFETY_REPORTS" },
  { label: "Notification of Non-compliance and Protocol Deviations", value: "NOTIFICATION_NON_COMPLIANCE" },
  { label: "Correspondence", value: "CORRESPONDENCE_1" },
  { label: "Site Authorisation Letter", value: "SITE_AUTHORISATION_LETTER" },
  { label: "Post Authorisation Submission and Authorisation Letters", value: "POST_AUTHORISATION_SUBMISSION" },
  { label: "Clinical Trial Notification (CTN) or Clinical Trial Exemption (CTX) forms", value: "CLINICAL_TRIAL_NOTIFICATION_CTN_CTX" },
  { label: "Therapeutic Goods Administration (TGA) acknowledgement letter", value: "TGA_ACKNOWLEDGEMENT_LETTER" },
  { label: "Correspondence", value: "CORRESPONDENCE_2" },
  { label: "Delegation Log/Signature sheet", value: "DELEGATION_LOG_SIGNATURE_SHEET" },
  { label: "Curriculum Vitae (CV) (including GCP and Medical License, etc)", value: "CURRICULUM_VITAE_CV" },
  { label: "Training log and documentation", value: "TRAINING_LOG_DOCUMENTATION" },
  { label: "Signed Confidentiality Agreement", value: "SIGNED_CONFIDENTIALITY_AGREEMENT" },
  { label: "Signed Clinical Trial Agreement", value: "SIGNED_CLINICAL_TRIAL_AGREEMENT" },
  { label: "Other relevant agreements/contracts", value: "OTHER_RELEVANT_AGREEMENTS" },
  { label: "Insurance Certificate", value: "INSURANCE_CERTIFICATE" },
  { label: "Indemnity", value: "INDEMNITY" },
  { label: "Participant Screening Log", value: "PARTICIPANT_SCREENING_LOG" },
  { label: "Participant Enrolment Log", value: "PARTICIPANT_ENROLMENT_LOG" },
  { label: "Participant Identification Log", value: "PARTICIPANT_IDENTIFICATION_LOG" },
  { label: "Participant Visit Tracking Log", value: "PARTICIPANT_VISIT_TRACKING_LOG" },
  { label: "Instructions for handling the IP", value: "INSTRUCTIONS_HANDLING_IP" },
  { label: "Shipping and receipt records", value: "SHIPPING_RECEIPT_RECORDS" },
  { label: "Dispensing and Accountability logs", value: "DISPENSING_ACCOUNTABILITY_LOGS" },
  { label: "IP Destruction Logs", value: "IP_DESTRUCTION_LOGS" },
  { label: "IP Storage and Temperature Logs", value: "IP_STORAGE_TEMPERATURE_LOGS" },
  { label: "Decoding and Un-blinding Procedure", value: "DECODING_UNBLINDING_PROCEDURE" },
  { label: "Sample of labels attached to IP containers", value: "SAMPLE_LABELS_IP_CONTAINERS" },
  { label: "Instructions", value: "INSTRUCTIONS_3" },
  { label: "Un-blinding process", value: "UNBLINDING_PROCESS" },
  { label: "Normal values and ranges for medical procedures and tests included in protocol", value: "NORMAL_VALUES_RANGES" },
  { label: "Certification/Accreditation/Established Quality Control/Validation for medical procedures and tests", value: "CERTIFICATION_ACCREDITATION" },
  { label: "Sample Log", value: "SAMPLE_LOG" },
  { label: "Sample Handling Manual", value: "SAMPLE_HANDLING_MANUAL" },
  { label: "Sample Shipping Record", value: "SAMPLE_SHIPPING_RECORD" },
  { label: "Laboratory Manual and Certification", value: "LABORATORY_MANUAL_CERTIFICATION" },
  { label: "Shipping Materials", value: "SHIPPING_MATERIALS" },
  { label: "Samples Destruction/Return Records", value: "SAMPLES_DESTRUCTION_RETURN_RECORDS" },
  { label: "Serious Adverse Event (SAE) Tracking Log", value: "SAE_TRACKING_LOG" },
  { label: "SAE Reports which have been submitted to the Sponsor", value: "SAE_REPORTS_SUBMITTED_SPONSOR" },
  { label: "Safety Reports", value: "SAFETY_REPORTS" },
  { label: "Interim Report and Data Safety Monitoring Boards (DSMB) Reports", value: "INTERIM_REPORT_DSMB" },
  { label: "Final Clinical Study Report", value: "FINAL_CLINICAL_STUDY_REPORT" },
  { label: "Relevant Study Publications and References", value: "RELEVANT_STUDY_PUBLICATIONS" },
  { label: "Investigator Meeting (agenda and presentation)", value: "INVESTIGATOR_MEETING_AGENDA" },
  { label: "Site Initiation Visit (agenda and presentation)", value: "SITE_INITIATION_VISIT_AGENDA" },
  { label: "Other relevant meeting documentation", value: "OTHER_RELEVANT_MEETING_DOCS" },
  { label: "Correspondence with Trial Sponsor", value: "CORRESPONDENCE_TRIAL_SPONSOR" },
  { label: "Correspondence with Site Staff", value: "CORRESPONDENCE_SITE_STAFF" },
  { label: "Correspondence with Laboratory/Vendors", value: "CORRESPONDENCE_LABORATORY_VENDORS" },
  { label: "Other relevant correspondence", value: "OTHER_RELEVANT_CORRESPONDENCE" },
  { label: "Newsletters", value: "NEWSLETTERS" },
  { label: "Other", value: "OTHER" },
] as const;

interface StudyTeamMember {
  uuid?: string;
  identifier: string;
  first_name: string;
  last_name: string;
  email: string;
  phone_number: string;
  speciality: string;
  hospital: {
    uuid: string;
    name: string;
  };
  department: {
    uuid: string;
    name: string;
  };
}

interface StudyPatientByDepartment {
  uuid: string;
  first_name: string;
  last_name: string;
  nhs_number: string;
  date_of_birth: string;
  gender: string;
  email: string;
  phone_number: string;
  address: string;
  profile_picture_url: string | null;
  department: {
    uuid: string;
    name: string;
    hospital: {
      uuid: string;
      name: string;
    };
  };
  created_at: string;
}

interface StudyPatientsByDepartmentResponse {
  patients: StudyPatientByDepartment[];
  count: number;
  study_name: string;
  departments_with_access: number[];
  message: string;
}

interface SidebarItem {
  id: string;
  label: string;
  icon: JSX.Element;
  active?: boolean;
}

interface StudyDetailsSidebarProps {
  isMinimized: boolean;
  onToggleMinimize: () => void;
  onItemClick: (item: SidebarItem) => void;
  activeTab: string;
}

const StudyDetailsSidebar: React.FC<StudyDetailsSidebarProps> = ({
  isMinimized,
  onToggleMinimize,
  onItemClick,
  activeTab
}) => {
  const sidebarItems: SidebarItem[] = [
    { id: 'details', label: 'Study Details', icon: <FileText size={20} /> },
    { id: 'resources', label: 'Study Resources', icon: <FileText size={20} /> },
    { id: 'team', label: 'Study Team', icon: <Users size={20} /> },
    { id: 'patients', label: 'Study Patients', icon: <User size={20} /> }
  ];

  return (
    <div className={`studydetails-sidebar ${isMinimized ? 'studydetails-sidebar-minimized' : ''}`}>
      <div className="studydetails-sidebar-header">
        <button className="studydetails-minimize-btn" onClick={onToggleMinimize}>
          {isMinimized ? '→' : '←'} {!isMinimized && 'Minimize'}
        </button>
      </div>

      <ul className="studydetails-sidebar-menu">
        {sidebarItems.map((item) => {
          const isActive = item.id === activeTab;
          return (
            <li key={item.id} className="studydetails-sidebar-item">
              <button
                className={`studydetails-sidebar-link ${isActive ? 'studydetails-active' : ''}`}
                onClick={() => onItemClick(item)}
              >
                <span className="studydetails-sidebar-icon">{item.icon}</span>
                {!isMinimized && item.label}
              </button>
            </li>
          );
        })}
      </ul>
    </div>
  );
};

export default function StudyDetails(): JSX.Element {
  const { studyUuid } = useParams<{ studyUuid: string }>();
  const navigate = useNavigate();
  const [activeTab, setActiveTab] = useState<string>("details");
  const [selectedCategories, setSelectedCategories] = useState<string[]>([]);
  const [isMinimized, setIsMinimized] = useState(false);

  // Fetch study data
  const {
    data: study,
    isLoading: isLoadingStudy,
    isError: isErrorStudy
  } = useStudyQuery(studyUuid || "");

  // Fetch active study policies
  const {
    data: activePolicies,
    isLoading: isLoadingActivePolicies,
    isError: isErrorActivePolicies
  } = useActivePoliciesByStudyQuery(studyUuid || "");

  // Fetch study team members
  const {
    data: teamMembers,
    isLoading: isLoadingTeam,
    isError: isErrorTeam
  } = useStudyTeamMembersQuery(studyUuid || "");

  // Fetch study patients by department
  const {
    data: patientsData,
    isLoading: isLoadingPatients,
    isError: isErrorPatients
  } = useStudyPatientsByDepartmentQuery(studyUuid || "") as {
    data: StudyPatientsByDepartmentResponse | undefined;
    isLoading: boolean;
    isError: boolean;
  };

  // Handle back navigation
  const handleBack = () => {
    navigate(-1);
  };

  const handleToggleMinimize = () => {
    setIsMinimized(!isMinimized);
  };

  const handleSidebarItemClick = (item: SidebarItem) => {
    setActiveTab(item.id);
  };

  // Study Details Card
  const StudyDetailsCard = () => (
    <div
      className="study-details-card"
    >
      <div className="card-header">
        <h2 className="card-title">
          <FileText size={24} />
          Study Details
        </h2>
      </div>
      <div className="card-content" style={{width: "100%"}}>
        {study ? (
          <div className="study-info-grid">
            <div className="info-item">
              <label>Study Name</label>
              <span>{study.name}</span>
            </div>
            <div className="info-item">
              <label>IRAS Number</label>
              <span>{study.iras || "N/A"}</span>
            </div>
            <div className="info-item">
              <label>Description</label>
              <span>{study.description || "No description available"}</span>
            </div>
            <div className="info-item">
              <label>Sponsor</label>
              <span>
                {study.sponsor ?
                  `${study.sponsor.first_name} ${study.sponsor.last_name}` :
                  "N/A"
                }
              </span>
            </div>
            <div className="info-item">
              <label>Created Date</label>
              <span>
                {study.created_at ?
                  new Date(study.created_at).toLocaleDateString() :
                  "N/A"
                }
              </span>
            </div>
            <div className="info-item">
              <label>Status</label>
              <span className={`status-badge ${study.status || 'active'}`}>
                {study.status || 'Active'}
              </span>
            </div>
          </div>
        ) : (
          <div className="no-data">No study details available</div>
        )}
      </div>
    </div>
  );

  // Study Resources (Policies) Card
  const StudyResourcesCard = () => {
    // Filter policies based on selected categories
    const filteredPolicies = useMemo(() => {
      if (!activePolicies?.policies) return [];

      if (selectedCategories.length === 0) {
        return activePolicies.policies;
      }

      return activePolicies.policies.filter((policy: Policy) =>
        selectedCategories.includes(policy.category)
      );
    }, [activePolicies?.policies, selectedCategories]);

    // Get unique categories from policies for filter options
    const availableCategories = useMemo(() => {
      if (!activePolicies?.policies) return [];

      const uniqueCategories = [...new Set(activePolicies.policies.map((policy: Policy) => policy.category))];
      return uniqueCategories.map(category => {
        const categoryInfo = POLICY_CATEGORIES.find(cat => cat.value === category);
        return {
          label: (categoryInfo?.label || category) as string,
          value: category as string
        };
      });
    }, [activePolicies?.policies]);

    const handleCategoryChange = (value: string[] | string) => {
      if (Array.isArray(value)) {
        setSelectedCategories(value);
      } else {
        setSelectedCategories([value]);
      }
    };

    const filterConfig = [
      {
        label: "Category",
        type: "checkbox" as const,
        options: availableCategories,
        value: selectedCategories,
        onChange: handleCategoryChange,
      },
    ];

    const policyColumns = useMemo(() => [
      {
        key: "title" as keyof Policy,
        header: "Title",
        sortable: true,
        render: (_: unknown, row?: Policy): React.ReactNode => {
          return row?.title || "N/A";
        },
      },
      {
        key: "category" as keyof Policy,
        header: "Category",
        sortable: true,
        render: (_: unknown, row?: Policy): React.ReactNode => {
          return row?.category || "N/A";
        },
      },
      {
        key: "description" as keyof Policy,
        header: "Description",
        sortable: true,
        render: (_: unknown, row?: Policy): React.ReactNode => {
          return row?.description || "N/A";
        },
      },
      {
        key: "created_at" as keyof Policy,
        header: "Date",
        sortable: true,
        render: (_: unknown, row?: Policy): React.ReactNode => {
          return row?.created_at ? new Date(row.created_at).toLocaleDateString() : "N/A";
        },
      },
      {
        key: "policy_version" as keyof Policy,
        header: "Version",
        sortable: true,
        render: (_: unknown, row?: Policy): React.ReactNode => {
          return row?.policy_version || "N/A";
        },
      },
      {
        key: 'actions' as keyof Policy,
        header: 'Actions',
        icon: <Download size={16} />,
        render: (_value: unknown, row?: Policy) => {
            if (!row) return null;
            const attachUrl = row.attach_content || '';
            const fileName = attachUrl && typeof attachUrl === 'string'
                ? attachUrl.split('/').pop() || row.title
                : row.title;
            return (
                <div className="policy-actions">
                    {/* View Policy Details Button */}
                    <button
                        className="action-btn view-btn text-primary"
                        onClick={() => navigate(`/policy-details/${row.uuid}`)}
                        title="View Policy Details"
                    >
                        <Eye size={16} />
                    </button>

                    {/* Download Button */}
                    {attachUrl && typeof attachUrl === 'string' ? (
                        <PdfDownloadButton
                            fileUrl={attachUrl}
                            fileName={fileName}
                        />
                    ) : (
                        <span>No file</span>
                    )}
                </div>
            );
        },
    },
    ], [navigate]);

    return (
      <div
        className="study-resources-card"

      >
        <div className="card-header">
          <h2 className="card-title">
            <FileText size={24} />
            Study Resources (Policies)
          </h2>
        </div>
        <div className="card-content" style={{width: "100%"}}>
          {isLoadingActivePolicies ? (
            <div className="loading-state">Loading policies...</div>
          ) : isErrorActivePolicies ? (
            <div className="error-state">Error loading policies</div>
          ) : activePolicies?.policies && activePolicies.policies.length > 0 ? (
            <>
              {/* Filter Section */}
              <div className="filter-section">
                <NurtifyFilter
                  layout="horizontal"
                  filters={filterConfig}
                />
              </div>

              {/* Results count */}
              <div className="results-count">
                <span>
                  Showing {filteredPolicies.length} of {activePolicies.policies.length} policies
                </span>
                {selectedCategories.length > 0 && (
                  <button
                    onClick={() => setSelectedCategories([])}
                    className="clear-filters-btn"
                  >
                    Clear filters
                  </button>
                )}
              </div>

              <DataTable
                data={filteredPolicies}
                columns={policyColumns}
                noDataMessage="No policies match the selected filters"
                defaultItemsPerPage={10}
              />
            </>
          ) : (
            <div className="no-data">No policies available for this study</div>
          )}
        </div>
      </div>
    );
  };

  // Study Team Card
  const StudyTeamCard = () => {
         const teamColumns = useMemo(() => [
       {
         key: "name" as keyof StudyTeamMember,
         header: "Name",
         sortable: true,
         render: (_: unknown, row?: StudyTeamMember): React.ReactNode => {
           return row ? `${row.first_name} ${row.last_name}` : "N/A";
         },
       },
       {
         key: "email" as keyof StudyTeamMember,
         header: "Email",
         sortable: true,
         render: (_: unknown, row?: StudyTeamMember): React.ReactNode => {
           return row?.email || "N/A";
         },
       },
       {
         key: "speciality" as keyof StudyTeamMember,
         header: "Speciality",
         sortable: true,
         render: (value: unknown): React.ReactNode => {
           return value ? String(value) : "N/A";
         },
       },
       {
         key: "department" as keyof StudyTeamMember,
         header: "Department",
         sortable: true,
         render: (_: unknown, row?: StudyTeamMember): React.ReactNode => {
           return row?.department?.name || "N/A";
         },
       },
       {
         key: "hospital" as keyof StudyTeamMember,
         header: "Hospital",
         sortable: true,
         render: (_: unknown, row?: StudyTeamMember): React.ReactNode => {
           return row?.hospital?.name || "N/A";
         },
       },
     ], []);

    return (
      <div
        className="study-team-card"

      >
        <div className="card-header">
          <h2 className="card-title">
            <Users size={24} />
            Study Team
          </h2>
        </div>
        <div className="card-content" style={{width: "100%"}}>
          {isLoadingTeam ? (
            <div className="loading-state">Loading team members...</div>
          ) : isErrorTeam ? (
            <div className="error-state">
              Error loading team members. Please try refreshing the page.
            </div>
          ) : teamMembers && teamMembers.length > 0 ? (
            <DataTable
              data={teamMembers}
              columns={teamColumns}
              noDataMessage="No team members found"
              defaultItemsPerPage={10}
            />
          ) : (
            <div className="no-data">No team members found for this study</div>
          )}
        </div>
      </div>
    );
  };

  // Study Patient List Card
  const StudyPatientListCard = () => {
    const patientColumns = useMemo(() => [
      {
        key: "name" as keyof StudyPatientByDepartment,
        header: "Name",
        sortable: true,
                 render: (_: unknown, row?: StudyPatientByDepartment): React.ReactNode => {
           return row ? `${row.first_name} ${row.last_name}` : "N/A";
         },
      },
      {
        key: "nhs_number" as keyof StudyPatientByDepartment,
        header: "NHS Number",
        sortable: true,
        render: (value: unknown): React.ReactNode => {
          return value ? String(value) : "N/A";
        },
      },
      {
        key: "email" as keyof StudyPatientByDepartment,
        header: "Email",
        sortable: true,
        render: (value: unknown): React.ReactNode => {
          return value ? String(value) : "N/A";
        },
      },
      {
        key: "date_of_birth" as keyof StudyPatientByDepartment,
        header: "Date of Birth",
        sortable: true,
        render: (value: unknown): React.ReactNode => {
          return value ? new Date(String(value)).toLocaleDateString() : "N/A";
        },
      },
      {
        key: "gender" as keyof StudyPatientByDepartment,
        header: "Gender",
        sortable: true,
        render: (value: unknown): React.ReactNode => {
          return value ? String(value) : "N/A";
        },
      },
      {
        key: "department" as keyof StudyPatientByDepartment,
        header: "Department",
        sortable: true,
        render: (_: unknown, row?: StudyPatientByDepartment): React.ReactNode => {
          return row?.department?.name || "N/A";
        },
      },
      {
        key: "hospital" as keyof StudyPatientByDepartment,
        header: "Hospital",
        sortable: true,
        render: (_: unknown, row?: StudyPatientByDepartment): React.ReactNode => {
          return row?.department?.hospital?.name || "N/A";
        },
      },
      {
        key: "created_at" as keyof StudyPatientByDepartment,
        header: "Created Date",
        sortable: true,
        render: (value: unknown): React.ReactNode => {
          return value ? new Date(String(value)).toLocaleDateString() : "N/A";
        },
      },
    ], []);

    const patientActions = useMemo(() => [
      {
        icon: <Eye size={16} />,
        tooltipText: "View Patient Details",
        onClick: (patient: StudyPatientByDepartment) => {
          // Navigate to patient details page
          navigate(`/patient-details/${patient.uuid}`);
        },
      },
    ], [navigate]);

    return (
      <div

      >
        <div className="card-header">
          <h2 className="card-title">
            <User size={24} />
            Study Patient List
          </h2>
          {patientsData && (
            <div className="card-subtitle">
              <span className="patient-count">{patientsData.count} patients</span>
              <span className="study-name">{patientsData.study_name}</span>
            </div>
          )}
        </div>
        <div className="card-content" style={{width: "100%"}}>
          {isLoadingPatients ? (
            <div className="loading-state">Loading patients...</div>
          ) : isErrorPatients ? (
            <div className="error-state">
              Error loading patients. Please try refreshing the page.
            </div>
          ) : patientsData && patientsData.patients && patientsData.patients.length > 0 ? (
            <DataTable
              data={patientsData.patients}
              columns={patientColumns}
              actions={patientActions}
              noDataMessage="No patients enrolled in this study"
              defaultItemsPerPage={10}
            />
          ) : (
            <div className="no-data">No patients enrolled in this study</div>
          )}
        </div>
      </div>
    );
  };

  // Loading state
  if (isLoadingStudy) {
    return (
      <Wrapper>
        <Preloader />
        <div className="content-wrapper js-content-wrapper overflow-hidden" style={{paddingBottom: "88px"}}>
          <div className="dashboard__content bg-light-4">
            <div className="row y-gap-20">
              <div className="col-12">
                <div className="loading-message">Loading study details...</div>
              </div>
            </div>
          </div>
        </div>
      </Wrapper>
    );
  }

  // Error state
  if (isErrorStudy || !study) {
    return (
      <Wrapper>
        <Preloader />
        <div className="content-wrapper js-content-wrapper overflow-hidden" style={{paddingBottom: "88px"}}>
          <div className="dashboard__content bg-light-4">
            <div className="row y-gap-20">
              <div className="col-12">
                <div className="error-message">
                  <h3>Error Loading Study</h3>
                  <p>There was an error loading the study details. Please try again later.</p>
                  <button onClick={handleBack} className="back-btn">
                    <ArrowLeft size={16} />
                    Go Back
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </Wrapper>
    );
  }

  return (
    <Wrapper>
      <Preloader />
      <div className="studydetails-dashboard-container">
        <StudyDetailsSidebar
          isMinimized={isMinimized}
          onToggleMinimize={handleToggleMinimize}
          onItemClick={handleSidebarItemClick}
          activeTab={activeTab}
        />
        <div className={`studydetails-main-content ${isMinimized ? 'studydetails-main-content-expanded' : ''}`}>
          <div className="studydetails-header">
            <div className="header-content">
              <button onClick={handleBack} className="back-button">
                <ArrowLeft size={20} />
                Back
              </button>
              <div className="header-text">
                <h1 className="study-title">{study.name}</h1>
                <p className="study-subtitle">Study Details and Management</p>
              </div>
            </div>
          </div>

          <div className="studydetails-content-section">
            {activeTab === 'details' && <StudyDetailsCard />}
            {activeTab === 'resources' && <StudyResourcesCard />}
            {activeTab === 'team' && <StudyTeamCard />}
            {activeTab === 'patients' && <StudyPatientListCard />}
          </div>
        </div>
      </div>
    </Wrapper>
  );
}
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import {
  getAllUsers,
  getUserById,
  getAnalytics,
  activateAccount,
  resetPassword,
  requestResetPassword,
  loginUser,
  verifyOtp,
  resendOtp,
  createUserDepartment,
  deleteAdmin,
  createHospitalAdmin,
  getHospitalAdmin,
  getAnalyticsHospitalAdmin,
  getSponsorListByOrg,
  createSponsor,
  getAnalyticsSponsor,
  getPatientsForDepartmentDashboard,
  getPoliciesForDepartmentDashboard,
  getSponsorsForDepartmentDashboard,
  getStaffForDepartmentDashboard,
  getStudiesForDepartmentDashboard,
  getRequestsForDepartmentDashboard,
  getPatientsForHospitalDashboard,
  getPoliciesForHospitalDashboard,
  getSponsorsForHospitalDashboard,
  getStaffForHospitalDashboard,
  getStudiesForHospitalDashboard,
  getDepartmentsForHospitalDashboard,
  getRequestsForHospitalDashboard,
} from "@/services/api/user.service";
import { ANALYTICS_KEYS, USER_KEYS } from "./keys";
import { 
  Analytics,
  PatientsAnalytics,
  PoliciesAnalytics,
  SponsorsAnalytics,
  StaffAnalytics,
  StudiesAnalytics,
  RequestsAnalytics,
  departmentsAnalitics,
 } from "@/services/api/types";
import { User } from "@/types/types";
import { Admin } from "@/types/types";
import { useNavigate } from "react-router-dom";
import {
  getUsersByDepartment,
  createDepartmentAdmin,
  getCurrentUser,
  deleteUserByUuid,
  updateUser,
  getDepartmentAdmins,
  resendActivationEmail,
} from "@/services/api/user.service.ts";
import { keycloak } from "@/keycloack";

// Add the missing key to ANALYTICS_KEYS
const updatedAnalyticsKeys = {
  ...ANALYTICS_KEYS,
  GET_ANALYTICS_SPONSOR: 'GET_ANALYTICS_SPONSOR'
};

export const useUsersQuery = () => {
  return useQuery<User[], Error>({
    queryKey: [USER_KEYS.GET_ALL],
    queryFn: getAllUsers,
  });
};

export const useUserQuery = (uuid: string) => {
  return useQuery<User, Error>({
    queryKey: [USER_KEYS.GET_BY_ID, uuid],
    queryFn: () => getUserById(uuid),
    enabled: !!uuid,
  });
};

// Récupérer tous les utilisateurs d'un département
export const useUsersByDepartmentQuery = (departmentUuid: string) => {
  return useQuery<User[], Error>({
    queryKey: [USER_KEYS.GET_BY_DEPARTMENT, departmentUuid],
    queryFn: () => getUsersByDepartment(departmentUuid),
    enabled: !!departmentUuid,
    staleTime: 1000 * 60 * 5,
    refetchOnWindowFocus: false,
    refetchOnMount: false,
  });
};

// Récupérer l'utilisateur connecté
export const useCurrentUserQuery = () => {
  return useQuery<User, Error>({
    queryKey: [USER_KEYS.GET_USER],
    queryFn: getCurrentUser,
    enabled: !!keycloak.authenticated,
  });
};

// Créer un utilisateur
export const useCreateUserMutation = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: createUserDepartment,
    onSuccess: () => {
      // Invalidate and refetch User list
      queryClient.invalidateQueries({ queryKey: [USER_KEYS.GET_ALL] });
      queryClient.invalidateQueries({
        queryKey: [USER_KEYS.GET_BY_DEPARTMENT],
      });
    },
  });
};

export const useAnalyticsQuery = () => {
  return useQuery<Analytics, Error>({
    queryKey: [ANALYTICS_KEYS.GET_ANALYTICS],
    queryFn: getAnalytics,
    refetchOnWindowFocus: false, // Prevent refetch when switching tabs/windows
    refetchOnReconnect: false, // Prevent refetch when reconnecting to the internet
    staleTime: 1000 * 60 * 1, // Cache data for 5 minutes before marking it stale
  });
};

export const useAnalyticsHospitalAdminQuery = (uuid: string) => {
  return useQuery<Analytics, Error>({
    queryKey: [ANALYTICS_KEYS.GET_ANALYTICS_HOSPITAL_ADMIN],
    queryFn: () => getAnalyticsHospitalAdmin(uuid),
    refetchOnWindowFocus: false, // Prevent refetch when switching tabs/windows
    refetchOnReconnect: false, // Prevent refetch when reconnecting to the internet
    staleTime: 1000 * 60 * 1, // Cache data for 5 minutes before marking it stale
  });
};

// Hospital admin dashboard : Start

export const usePatientsForHospitalDashboardQuery = (uuid: string, period?: string) => {
  return useQuery<PatientsAnalytics, Error>({
    queryKey: [ANALYTICS_KEYS.GET_PATIENTS_ANALYTICS_HOSPITAL_ADMIN, period], // Include period in queryKey
    queryFn: () => getPatientsForHospitalDashboard(uuid, period), // Pass period to queryFn
    refetchOnWindowFocus: false,
    refetchOnReconnect: false,
    staleTime: 1000 * 60 * 1,
  });
};

export const usePoliciesForHospitalDashboardQuery = (uuid: string, period?: string) => {
  return useQuery<PoliciesAnalytics, Error>({
    queryKey: [ANALYTICS_KEYS.GET_POLICIES_ANALYTICS_HOSPITAL_ADMIN, period], // Include period in queryKey
    queryFn: () => getPoliciesForHospitalDashboard(uuid, period), // Pass period to queryFn
    refetchOnWindowFocus: false,
    refetchOnReconnect: false,
    staleTime: 1000 * 60 * 1,
  });
};

export const useSponsorsForHospitalDashboardQuery = (uuid: string, period?: string) => {
  return useQuery<SponsorsAnalytics, Error>({
    queryKey: [ANALYTICS_KEYS.GET_SPONSORS_ANALYTICS_HOSPITAL_ADMIN, period], // Include period in queryKey
    queryFn: () => getSponsorsForHospitalDashboard(uuid, period), // Pass period to queryFn
    refetchOnWindowFocus: false,
    refetchOnReconnect: false,
    staleTime: 1000 * 60 * 1,
  });
};

export const useStaffForHospitalDashboardQuery = (uuid: string, period?: string) => {
  return useQuery<StaffAnalytics, Error>({
    queryKey: [ANALYTICS_KEYS.GET_STAFF_ANALYTICS_HOSPITAL_ADMIN, period], // Include period in queryKey
    queryFn: () => getStaffForHospitalDashboard(uuid, period), // Pass period to queryFn
    refetchOnWindowFocus: false,
    refetchOnReconnect: false,
    staleTime: 1000 * 60 * 1,
  });
};


export const useStudiesForHospitalDashboardQuery = (uuid: string) => {
  return useQuery<StudiesAnalytics, Error>({
    queryKey: [ANALYTICS_KEYS.GET_STUDIES_ANALYTICS_HOSPITAL_ADMIN],
    queryFn: () => getStudiesForHospitalDashboard(uuid),
    refetchOnWindowFocus: false,
    refetchOnReconnect: false,
    staleTime: 1000 * 60 * 1,
  });
};

export const useDepartmentsFofHosHospitalDashboardQuery = (uuid: string) => {
  return useQuery<departmentsAnalitics, Error>({
    queryKey: [ANALYTICS_KEYS.GET_DEPARTMENTS_ANALYTICS_HOSPITAL_ADMIN],
    queryFn: () => getDepartmentsForHospitalDashboard(uuid),
    refetchOnWindowFocus: false,
    refetchOnReconnect: false,
    staleTime: 1000 * 60 * 1,
  });
};

export const useRequestsForHospitalDashboardQuery = (uuid: string) => {
  return useQuery<RequestsAnalytics, Error>({
    queryKey: [ANALYTICS_KEYS.GET_REQUESTS_ANALYTICS_HOSPITAL_ADMIN],
    queryFn: () => getRequestsForHospitalDashboard(uuid),
    refetchOnWindowFocus: false,
    refetchOnReconnect: false,
    staleTime: 1000 * 60 * 1,
  });
};

// Hospital admin dashboard : End

// Department admin dashboard : Start
export const usePatientsForDepartmentDashboardQuery = (uuid: string, period?: string) => {
  return useQuery<PatientsAnalytics, Error>({
    queryKey: [ANALYTICS_KEYS.GET_PATIENTS_ANALYTICS_DEPARTMENT_ADMIN, period], // Include period in queryKey
    queryFn: () => getPatientsForDepartmentDashboard(uuid, period), // Pass period to queryFn
    refetchOnWindowFocus: false,
    refetchOnReconnect: false,
    staleTime: 1000 * 60 * 1,
  });
};

export const usePoliciesForDepartmentDashboardQuery = (uuid: string, period?: string) => {
  return useQuery<PoliciesAnalytics, Error>({
    queryKey: [ANALYTICS_KEYS.GET_POLICIES_ANALYTICS_DEPARTMENT_ADMIN, period], // Include period in queryKey
    queryFn: () => getPoliciesForDepartmentDashboard(uuid, period), // Pass period to queryFn
    refetchOnWindowFocus: false,
    refetchOnReconnect: false,
    staleTime: 1000 * 60 * 1,
  });
};

export const useSponsorsForDepartmentDashboardQuery = (uuid: string, period?: string) => {
  return useQuery<SponsorsAnalytics, Error>({
    queryKey: [ANALYTICS_KEYS.GET_SPONSORS_ANALYTICS_DEPARTMENT_ADMIN, period], // Include period in queryKey
    queryFn: () => getSponsorsForDepartmentDashboard(uuid, period), // Pass period to queryFn
    refetchOnWindowFocus: false,
    refetchOnReconnect: false,
    staleTime: 1000 * 60 * 1,
  });
};

export const useStaffForDepartmentDashboardQuery = (uuid: string, period?: string) => {
  return useQuery<StaffAnalytics, Error>({
    queryKey: [ANALYTICS_KEYS.GET_STAFF_ANALYTICS_DEPARTMENT_ADMIN, period], // Include period in queryKey
    queryFn: () => getStaffForDepartmentDashboard(uuid, period), // Pass period to queryFn
    refetchOnWindowFocus: false,
    refetchOnReconnect: false,
    staleTime: 1000 * 60 * 1,
  });
};


export const useStudiesForDepartmentDashboardQuery = (uuid: string) => {
  return useQuery<StudiesAnalytics, Error>({
    queryKey: [ANALYTICS_KEYS.GET_STUDIES_ANALYTICS_DEPARTMENT_ADMIN],
    queryFn: () => getStudiesForDepartmentDashboard(uuid),
    refetchOnWindowFocus: false,
    refetchOnReconnect: false,
    staleTime: 1000 * 60 * 1,
  });
};

export const useRequestsForDepartmentDashboardQuery = (uuid: string) => {
  return useQuery<RequestsAnalytics, Error>({
    queryKey: [ANALYTICS_KEYS.GET_REQUESTS_ANALYTICS_DEPARTMENT_ADMIN],
    queryFn: () => getRequestsForDepartmentDashboard(uuid),
    refetchOnWindowFocus: false,
    refetchOnReconnect: false,
    staleTime: 1000 * 60 * 1,
  });
};
// Department admin dashboard : End

// Mettre à jour un utilisateur
export const useUpdateUserMutation = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ uuid, data }: { uuid: string; data: Partial<User> }) =>
      updateUser(uuid, data),
    onSuccess: (_, { uuid }) => {
      queryClient.invalidateQueries({
        queryKey: [USER_KEYS.GET_BY_DEPARTMENT],
      });
      queryClient.invalidateQueries({ queryKey: [USER_KEYS.GET_USER, uuid] });
    },
  });
};

// Supprimer un utilisateur
export const useDeleteUserMutation = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: deleteUserByUuid,
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: [USER_KEYS.GET_BY_DEPARTMENT],
      });
    },
  });
};

export const useActivateAccountMutation = () => {
  return useMutation({
    mutationFn: ({ uid, token, currentPassword, newPassword, confirmPassword }:
      { uid: string; token: string; currentPassword: string; newPassword: string; confirmPassword: string }) =>
      activateAccount(uid, token, currentPassword, newPassword, confirmPassword),
  });
};

// Réinitialiser le mot de passe
export const useRequestResetPasswordMutation = () => {
  return useMutation({
    mutationFn: ({ email }: { email: string }) => requestResetPassword(email),
  });
};

export const useResetPasswordMutation = () => {
  return useMutation({
    mutationFn: ({ uid, token, password, confirm_password }:
      { uid: string; token: string; password: string; confirm_password: string }) =>
      resetPassword(uid, token, password, confirm_password),
  });
};

export const useLoginMutation = () => {
  const navigate = useNavigate();

  return useMutation({
    mutationFn: ({ email, password }: { email: string; password: string }) =>
      loginUser(email, password),
    onSuccess: (data) => {
      if (data.requires_otp) {
        navigate("/otp");
      } else {
        navigate("/dashboard");
      }
    },
    onError: (error) => {
      console.error("Login failed:", error);
    },
  });
};

export const useVerifyOtp = () => {
  return useMutation({
    mutationKey: [USER_KEYS.VERIFY_OTP],
    mutationFn: ({ email, password,otp_code}: { email: string; password: string; otp_code: string }) => 
      verifyOtp(email, password,otp_code),
  });
};

export const useResendOtp = () => {
  return useMutation({
    mutationKey: [USER_KEYS.RESEND_OTP],
    mutationFn: ({ email, password }: { email: string; password: string }) =>
      resendOtp(email, password),
    onSuccess: () => {
      console.log('OTP resent successfully');
    },
    onError: (error) => {
      console.error('Failed to resend OTP:', error);
    }
  });
};

export const useCreateHospitalAdminMutation = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: createHospitalAdmin,
    onSuccess: () => {
      // Invalidate and refetch User list
      queryClient.invalidateQueries({ queryKey: [USER_KEYS.CREATE_ADMIN] });
    },
  });
};

export const useCreateDepartmentAdminMutation = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: createDepartmentAdmin,
    onSuccess: () => {
      // Invalidate and refetch User list
      queryClient.invalidateQueries({ queryKey: [USER_KEYS.CREATE_ADMIN] });
    },
  });
};

// Récupérer tous les administrateurs d'un département
export const useDepartmentAdminsQuery = (departmentUuid: string) => {
  return useQuery<Admin[], Error>({
    queryKey: [USER_KEYS.GET_BY_DEPARTMENT, departmentUuid],
    queryFn: () => getDepartmentAdmins(departmentUuid),
    enabled: !!departmentUuid,
    staleTime: 1000 * 60 * 5,
    refetchOnWindowFocus: false,
    refetchOnMount: false,
  });
};

export const useHospitalAdminQuery = (hospitalUuid: string) => {
  return useQuery<Admin[], Error>({
    queryKey: [USER_KEYS.GET_BY_DEPARTMENT, hospitalUuid],
    queryFn: () => getHospitalAdmin(hospitalUuid),
    enabled: !!hospitalUuid,
    staleTime: 1000 * 60 * 5,
    refetchOnWindowFocus: true,
    refetchOnMount: true,
  });
};

export const useResendActivationEmailMutation = () => {
  return useMutation({
    mutationKey: [USER_KEYS.RESEND_ACTIVATION],
    mutationFn: ({identifier}:{identifier:string}) => resendActivationEmail(identifier),
    onSuccess: () => {
      // Could add success notification/handling here
      console.log('Activation email resent successfully');
    },
    onError: (error) => {
      console.error('Failed to resend activation email:', error);
    }
  });
};

export const useDeleteAdminMutation = () => {
  const queryClient = useQueryClient();

  return useMutation({
      mutationFn: ({ identifier}: { identifier: string }) =>
        deleteAdmin(identifier),
      onSuccess: () => {
          // Invalidate hospital list and the updated hospital details
          queryClient.invalidateQueries({ queryKey: [USER_KEYS.DELETE_ADMIN] });

      },
  });
};

export const useOrganizationUsersQuery = () => {
  const { data: currentUser } = useCurrentUserQuery();
  const departmentUuid = currentUser?.department?.uuid;

  return useQuery({
    queryKey: [USER_KEYS.GET_BY_DEPARTMENT, departmentUuid],
    queryFn: () => getUsersByDepartment(departmentUuid || ''),
    enabled: !!departmentUuid,
  });
};

export const useSponsorListByOrgQuery = (uuid: string) => {
  return useQuery<Admin[], Error>({
    queryKey: [USER_KEYS.GET_ALL],
    queryFn: () => getSponsorListByOrg(uuid),
    enabled: !!uuid,
  });
};

export const useCreateSponsorMutation = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: createSponsor,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey:[USER_KEYS.GET_ALL] });
    },
  });
};

export const useAnalyticsSponsorQuery = (globalStudyId?: string) => {
  return useQuery<Analytics, Error>({
    queryKey: [updatedAnalyticsKeys.GET_ANALYTICS_SPONSOR, globalStudyId],
    queryFn: () => getAnalyticsSponsor(globalStudyId),
    enabled: !!globalStudyId,
    refetchOnWindowFocus: false,
    refetchOnReconnect: false,
    staleTime: 1000 * 60 * 1,
  });
};

interface FormCardProps {
  title: string;
  description: string;
  // Add other props as needed
}

export function FormCard({ title, description }: FormCardProps) {
  return (
    <div className="hover-shadow-2">
      <div className="shadow-1 rounded-8 border-light bg-white h-100 p-20">
        <div className="d-flex justify-between items-center">
          <h4 className="text-18 lh-1 fw-500">{title}</h4>
          <div className="d-flex items-center">
            {/* Add any card actions/icons here */}
          </div>
        </div>
        
        <div className="mt-15">
          <p className="text-14 lh-1">{description}</p>
        </div>

        <div className="d-flex items-center mt-20">
          {/* Add form metadata or additional info */}
        </div>
      </div>
    </div>
  );
} 
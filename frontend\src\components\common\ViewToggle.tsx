import React from 'react';
import { List, Calendar } from 'lucide-react';

export type ViewType = 'list' | 'calendar';

interface ViewToggleProps {
  currentView: ViewType;
  onViewChange: (view: ViewType) => void;
  className?: string;
}

const ViewToggle: React.FC<ViewToggleProps> = ({
  currentView,
  onViewChange,
  className = '',
}) => {
  return (
    <div className={`view-toggle ${className}`}>
      <button
        type="button"
        className={`view-toggle-btn ${currentView === 'list' ? 'active' : ''}`}
        onClick={() => onViewChange('list')}
        aria-label="List view"
      >
        <List size={16} />
        <span>List</span>
      </button>
      <button
        type="button"
        className={`view-toggle-btn ${currentView === 'calendar' ? 'active' : ''}`}
        onClick={() => onViewChange('calendar')}
        aria-label="Calendar view"
      >
        <Calendar size={16} />
        <span>Calendar</span>
      </button>
    </div>
  );
};

export default ViewToggle;

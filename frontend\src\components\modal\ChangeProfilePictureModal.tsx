import React, { useState, useRef } from 'react';
import { X, Upload, User } from 'lucide-react';
import { useUpdatePatientProfilePictureMutation } from '@/hooks/patient.query';
import useSelectedPatientStore from '@/store/SelectedPatientState';
import { getProfilePictureUrl } from '@/utils/imageUtils';
import './modal.css';

interface ChangeProfilePictureModalProps {
  isOpen: boolean;
  onClose: () => void;
  patientUuid: string;
}

const ChangeProfilePictureModal: React.FC<ChangeProfilePictureModalProps> = ({
  isOpen,
  onClose,
  patientUuid
}) => {
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [previewUrl, setPreviewUrl] = useState<string | null>(null);
  const [isUploading, setIsUploading] = useState(false);
  const [imageError, setImageError] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);
  
  const { selectedPatient, updateProfilePicture } = useSelectedPatientStore();
  const updateProfilePictureMutation = useUpdatePatientProfilePictureMutation(patientUuid);

  if (!isOpen) return null;

  const handleFileSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      // Validate file type
      if (!file.type.startsWith('image/')) {
        alert('Please select an image file (JPG, PNG, etc.)');
        return;
      }

      // Validate file size (5MB limit)
      const maxSize = 5 * 1024 * 1024; // 5MB
      if (file.size > maxSize) {
        alert('File size must be less than 5MB');
        return;
      }

      setSelectedFile(file);
      
      // Create preview URL
      const url = URL.createObjectURL(file);
      setPreviewUrl(url);
    }
  };

  const handleUpload = async () => {
    if (!selectedFile) {
      alert('Please select a file first');
      return;
    }

    setIsUploading(true);
    
    try {
      const result = await updateProfilePictureMutation.mutateAsync(selectedFile);
      
      // Update the store with new profile picture
      updateProfilePicture(result.profile_picture, result.profile_picture_url);
      
      // Clean up
      if (previewUrl) {
        URL.revokeObjectURL(previewUrl);
      }
      
      setSelectedFile(null);
      setPreviewUrl(null);
      onClose();
      
      alert('Profile picture updated successfully!');
    } catch (error: any) {
      console.error('Failed to update profile picture:', error);
      alert('Failed to update profile picture. Please try again.');
    } finally {
      setIsUploading(false);
    }
  };

  const handleClose = () => {
    // Clean up preview URL
    if (previewUrl) {
      URL.revokeObjectURL(previewUrl);
    }
    setSelectedFile(null);
    setPreviewUrl(null);
    onClose();
  };

  const triggerFileInput = () => {
    fileInputRef.current?.click();
  };

  return (
    <div className="modal-overlay" onClick={handleClose}>
      <div className="modal-content" onClick={(e) => e.stopPropagation()}>
        <div className="modal-header">
          <h2>Change Profile Picture</h2>
          <button className="close-button" onClick={handleClose}>
            <X size={20} />
          </button>
        </div>
        
        <div className="modal-body">
          <div className="profile-picture-upload-container">
            {/* Current Profile Picture */}
            <div className="current-profile-picture">
              <h3>Current Picture</h3>
              <div className="profile-picture-display">
                {selectedPatient?.profile_picture_url && !imageError ? (
                  <img 
                    src={getProfilePictureUrl(selectedPatient.profile_picture_url) || ''}
                    alt="Current profile picture"
                    onError={() => setImageError(true)}
                  />
                ) : (
                  <div className="profile-picture-placeholder">
                    <User size={48} />
                  </div>
                )}
              </div>
            </div>

            {/* New Profile Picture Upload */}
            <div className="new-profile-picture">
              <h3>New Picture</h3>
              <div className="upload-area" onClick={triggerFileInput}>
                {previewUrl ? (
                  <div className="preview-container">
                    <img src={previewUrl} alt="Preview" className="preview-image" />
                    <div className="preview-overlay">
                      <Upload size={24} />
                      <span>Click to change</span>
                    </div>
                  </div>
                ) : (
                  <div className="upload-placeholder">
                    <Upload size={48} />
                    <p>Click to select image</p>
                    <span className="upload-hint">JPG, PNG (Max 5MB)</span>
                  </div>
                )}
              </div>
              
              <input
                ref={fileInputRef}
                type="file"
                accept="image/*"
                onChange={handleFileSelect}
                style={{ display: 'none' }}
              />
            </div>
          </div>

          {/* Action Buttons */}
          <div className="modal-actions">
            <button 
              className="modal-button cancel" 
              onClick={handleClose}
              disabled={isUploading}
            >
              Cancel
            </button>
            <button 
              className="modal-button primary" 
              onClick={handleUpload}
              disabled={!selectedFile || isUploading}
            >
              {isUploading ? 'Uploading...' : 'Upload Picture'}
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ChangeProfilePictureModal; 
import React, { useState } from "react";
import "./VisitCalendar.css";
import { PatientVisitToday } from "@/services/api/types";
import { useVisits } from "@/hooks/patient.query";
import NurtifyDateInput from "@/components/NurtifyDateInput";
import { ChevronLeft, ChevronRight } from "lucide-react";
import VisitDetailsByDateModal from "@/components/modal/VisitDetailsByDateModal";

interface VisitCalendarProps {
  onViewVisitDetails: (patientUuid: string, visitUuid: string) => void;
  onEditVisit: (patientUuid: string, visitUuid: string, visitName: string, visitStatus: string) => void;
  hideDatePicker?: boolean; // Optional prop to hide the date picker
  externalSelectedDate?: string; // Optional prop to control the date from outside
  onDateChange?: (date: string) => void; // Optional callback when date changes
  onViewTypeChange?: (viewType: "D" | "W" | "M") => void; // Optional callback when view type changes
  initialViewType?: "D" | "W" | "M"; // Optional prop to set the initial view type
}

type CalendarViewType = "D" | "W" | "M";

const VisitCalendar: React.FC<VisitCalendarProps> = ({
  onViewVisitDetails,
  onEditVisit,
  hideDatePicker = false,
  externalSelectedDate,
  onDateChange,
  onViewTypeChange,
  initialViewType = "D" // Default to day view if not provided
}) => {
  const [selectedDate, setSelectedDate] = useState<string>(externalSelectedDate || new Date().toISOString().split('T')[0]);
  const [viewType, setViewType] = useState<CalendarViewType>(initialViewType); // Use initialViewType instead of hardcoded "D"
  const [isModalOpen, setIsModalOpen] = useState<boolean>(false);
  const [modalSelectedDate, setModalSelectedDate] = useState<Date | null>(null);

  // Update internal state when external date changes
  React.useEffect(() => {
    if (externalSelectedDate) {
      setSelectedDate(externalSelectedDate);
      // Don't reset the view type when the date changes
      console.log(`Date changed to ${externalSelectedDate}, maintaining view type: ${viewType}`);
    }
  }, [externalSelectedDate, viewType]);

  // Update internal viewType when initialViewType changes
  React.useEffect(() => {
    setViewType(initialViewType);
    console.log(`View type initialized/changed to: ${initialViewType}`);
  }, [initialViewType]);

  // Custom handler for view type changes
  const handleViewTypeChange = (newViewType: CalendarViewType) => {
    setViewType(newViewType);
    // Call the onViewTypeChange callback if provided
    if (onViewTypeChange) {
      onViewTypeChange(newViewType);
    }
  };

  // Use the unified visits hook with the selected view type
  const visitsQuery = useVisits(
    viewType, // This should match the calendar view (D, W, M)
    selectedDate,
    false // Don't use local date
  );

  const visits: PatientVisitToday[] = visitsQuery.data || [];
  const isLoading = visitsQuery.isLoading;
  const error = visitsQuery.error;

  // Debug logging
  console.log(`Calendar View: ${viewType}, Date: ${selectedDate}, Visits:`, visits);

  // Handle date change
  const handleDateChange = (event: { target: { name: string; value: string } }) => {
    const newDate = event.target.value;
    setSelectedDate(newDate);
    // Call the onDateChange callback if provided
    if (onDateChange) {
      onDateChange(newDate);
    }
  };

  // Navigate to previous period
  const goToPrevious = () => {
    const currentDate = new Date(selectedDate);

    switch (viewType) {
      case "D":
        // Previous day
        currentDate.setDate(currentDate.getDate() - 1);
        break;
      case "W":
        // Previous week
        currentDate.setDate(currentDate.getDate() - 7);
        break;
      case "M":
        // Previous month
        currentDate.setMonth(currentDate.getMonth() - 1);
        break;
    }

    const newDate = currentDate.toISOString().split('T')[0];
    setSelectedDate(newDate);
    // Call the onDateChange callback if provided
    if (onDateChange) {
      onDateChange(newDate);
    }
  };

  // Navigate to next period
  const goToNext = () => {
    const currentDate = new Date(selectedDate);

    switch (viewType) {
      case "D":
        // Next day
        currentDate.setDate(currentDate.getDate() + 1);
        break;
      case "W":
        // Next week
        currentDate.setDate(currentDate.getDate() + 7);
        break;
      case "M":
        // Next month
        currentDate.setMonth(currentDate.getMonth() + 1);
        break;
    }

    const newDate = currentDate.toISOString().split('T')[0];
    setSelectedDate(newDate);
    // Call the onDateChange callback if provided
    if (onDateChange) {
      onDateChange(newDate);
    }
  };

  // Handle day click to open modal
  const handleDayClick = (day: Date) => {
    setModalSelectedDate(day);
    setIsModalOpen(true);
  };

  // Handle modal close
  const handleModalClose = () => {
    setIsModalOpen(false);
    setModalSelectedDate(null);
  };

  // Get the title for the current view
  const getViewTitle = () => {
    const date = new Date(selectedDate);

    // Pre-calculate week dates outside the switch
    const startOfWeek = new Date(date);
    startOfWeek.setDate(date.getDate() - date.getDay() + (date.getDay() === 0 ? -6 : 1)); // Start on Monday
    const endOfWeek = new Date(startOfWeek);
    endOfWeek.setDate(startOfWeek.getDate() + 6); // End on Sunday

    switch (viewType) {
      case "D":
        return `Visits for ${date.toLocaleDateString()}`;
      case "W":
        return `Visits for week ${startOfWeek.toLocaleDateString()} - ${endOfWeek.toLocaleDateString()}`;
      case "M":
        return `Visits for ${date.toLocaleString('default', { month: 'long', year: 'numeric' })}`;
      default:
        return "Visits";
    }
  };

  // Render day view
  const renderDayView = () => {
    return (
      <div className="day-view">
        <h3>{new Date(selectedDate).toLocaleDateString()} Visits</h3>
        {visits.length === 0 ? (
          <div className="no-visits">No visits scheduled for this day</div>
        ) : (
          <div className="visits-list">
            {visits.map((visit) => (
              <div key={visit.uuid} className="visit-card">
                <div className="patient-name">
                  {visit.patient_details.first_name} {visit.patient_details.last_name}
                </div>
                <div className="visit-details">
                  {visit.visit_details.map((detail) => (
                    <div key={detail.uuid} className="detail-item">
                      <div>Study: {detail.study_name}</div>
                      <div>Status: {detail.registration_status}</div>
                      <div>Time: {detail.time}</div>
                      <div>Room: {detail.location || 'Not assigned'}</div>
                      <div className="actions">
                        <button
                          onClick={() => onViewVisitDetails(visit.uuid, detail.uuid)}
                          className="view-btn"
                        >
                          View Details
                        </button>
                        <button
                          onClick={() => onEditVisit(visit.uuid, detail.uuid, detail.name, detail.visit_status)}
                          className="edit-btn"
                        >
                          Edit
                        </button>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            ))}
          </div>
        )}
      </div>
    );
  };

  // Render week view
  const renderWeekView = () => {
    // Calculate the start of the week (Monday)
    const date = new Date(selectedDate);
    const startOfWeek = new Date(date);
    startOfWeek.setDate(date.getDate() - date.getDay() + (date.getDay() === 0 ? -6 : 1)); // Start on Monday

    // Create an array of dates for the week
    const weekDates = Array.from({ length: 7 }, (_, i) => {
      const day = new Date(startOfWeek);
      day.setDate(startOfWeek.getDate() + i);
      return day;
    });

    return (
      <div className="week-view">
        <h3>Week of {startOfWeek.toLocaleDateString()}</h3>
        <div className="week-grid">
          {weekDates.map((day) => {
            // Create date string in local timezone to avoid UTC conversion issues
            const dayStr = `${day.getFullYear()}-${String(day.getMonth() + 1).padStart(2, '0')}-${String(day.getDate()).padStart(2, '0')}`;
            const dayVisits = visits.filter(visit =>
              visit.visit_details.some(detail => {
                // Normalize date format by removing any time component
                const detailDate = detail.date ? detail.date.split('T')[0] : '';
                // Create a standardized date string for comparison
                const detailDateParts = detailDate.split('-');
                if (detailDateParts.length !== 3) return false;

                const year = parseInt(detailDateParts[0]);
                const month = parseInt(detailDateParts[1]);
                const dayNum = parseInt(detailDateParts[2]);

                // Compare with the current day's date components
                return year === day.getFullYear() &&
                      month === (day.getMonth() + 1) &&
                      dayNum === day.getDate();
              })
            );

            return (
              <div
                key={dayStr}
                className="day-cell clickable-day"
                onClick={() => handleDayClick(day)}
                title="Click to view visits for this day"
              >
                <div className="day-header">
                  <div className="day-name">{day.toLocaleDateString('en-US', { weekday: 'short' })}</div>
                  <div className="day-date">{day.getDate()}</div>
                </div>
                <div className="day-visits">
                  {dayVisits.length === 0 ? (
                    <div className="no-visits">No visits</div>
                  ) : (
                    dayVisits.map(visit => (
                      <div key={visit.uuid} className="visit-item">
                        <div className="patient-name">
                          {visit.patient_details.first_name} {visit.patient_details.last_name}
                        </div>
                        {visit.visit_details
                          .filter(detail => {
                            // Normalize date format by removing any time component
                            const detailDate = detail.date ? detail.date.split('T')[0] : '';
                            // Create a standardized date string for comparison
                            const detailDateParts = detailDate.split('-');
                            if (detailDateParts.length !== 3) return false;

                            const year = parseInt(detailDateParts[0]);
                            const month = parseInt(detailDateParts[1]);
                            const dayNum = parseInt(detailDateParts[2]);

                            // Compare with the current day's date components
                            return year === day.getFullYear() &&
                                  month === (day.getMonth() + 1) &&
                                  dayNum === day.getDate();
                          })
                          .map(detail => (
                            <div key={detail.uuid} className="visit-detail">
                              <div>{detail.time}</div>
                              <div>{detail.study_name}</div>
                              {/* <div className="actions">
                                <button
                                  onClick={() => onViewVisitDetails(visit.uuid, detail.uuid)}
                                  className="view-btn"
                                >
                                  View Detailss
                                </button>
                                <button
                                  onClick={() => onEditVisit(visit.uuid, detail.uuid, detail.name, detail.visit_status)}
                                  className="edit-btn"
                                >
                                  Edit
                                </button>
                              </div> */}
                            </div>
                          ))}
                      </div>
                    ))
                  )}
                </div>
              </div>
            );
          })}
        </div>
      </div>
    );
  };

  // Render month view
  const renderMonthView = () => {
    // Calculate the start and end of the month
    const date = new Date(selectedDate);
    const startOfMonth = new Date(date.getFullYear(), date.getMonth(), 1);
    const endOfMonth = new Date(date.getFullYear(), date.getMonth() + 1, 0);

    // Calculate the start of the calendar (might be in the previous month)
    const startOfCalendar = new Date(startOfMonth);
    startOfCalendar.setDate(startOfCalendar.getDate() - startOfCalendar.getDay() + (startOfCalendar.getDay() === 0 ? -6 : 1)); // Start on Monday

    // Calculate the end of the calendar (might be in the next month)
    const endOfCalendar = new Date(endOfMonth);
    const daysToAdd = 7 - endOfCalendar.getDay();
    endOfCalendar.setDate(endOfCalendar.getDate() + (daysToAdd === 7 ? 0 : daysToAdd));

    // Create an array of dates for the calendar
    const calendarDates = [];
    const currentDate = new Date(startOfCalendar);
    while (currentDate <= endOfCalendar) {
      calendarDates.push(new Date(currentDate));
      currentDate.setDate(currentDate.getDate() + 1);
    }

    // Group dates into weeks
    const weeks = [];
    for (let i = 0; i < calendarDates.length; i += 7) {
      weeks.push(calendarDates.slice(i, i + 7));
    }

    return (
      <div className="month-view">
        <h3>{date.toLocaleString('default', { month: 'long', year: 'numeric' })}</h3>
        <div className="month-grid">
          <div className="week-header">
            <div>Mon</div>
            <div>Tue</div>
            <div>Wed</div>
            <div>Thu</div>
            <div>Fri</div>
            <div>Sat</div>
            <div>Sun</div>
          </div>
          {weeks.map((week, weekIndex) => (
            <div key={weekIndex} className="week-row">
              {week.map(day => {
                // Create date string in local timezone to avoid UTC conversion issues
                const dayStr = `${day.getFullYear()}-${String(day.getMonth() + 1).padStart(2, '0')}-${String(day.getDate()).padStart(2, '0')}`;
                const isCurrentMonth = day.getMonth() === date.getMonth();
                const dayVisits = visits.filter(visit =>
                  visit.visit_details.some(detail => {
                    // Normalize date format by removing any time component
                    const detailDate = detail.date ? detail.date.split('T')[0] : '';
                    // Create a standardized date string for comparison
                    const detailDateParts = detailDate.split('-');
                    if (detailDateParts.length !== 3) return false;

                    const year = parseInt(detailDateParts[0]);
                    const month = parseInt(detailDateParts[1]);
                    const dayNum = parseInt(detailDateParts[2]);

                    // Compare with the current day's date components
                    return year === day.getFullYear() &&
                          month === (day.getMonth() + 1) &&
                          dayNum === day.getDate();
                  })
                );

                return (
                  <div
                    key={dayStr}
                    className={`day-cell ${isCurrentMonth ? 'current-month' : 'other-month'} clickable-day`}
                    onClick={() => handleDayClick(day)}
                    title="Click to view visits for this day"
                  >
                    <div className="day-number">{day.getDate()}</div>
                    <div className="day-visits">
                      {dayVisits.length > 0 && (
                        <div className="visit-count">
                          {dayVisits.reduce((count, visit) =>
                            count + visit.visit_details.filter(detail => {
                              // Normalize date format by removing any time component
                              const detailDate = detail.date ? detail.date.split('T')[0] : '';
                              // Create a standardized date string for comparison
                              const detailDateParts = detailDate.split('-');
                              if (detailDateParts.length !== 3) return false;

                              const year = parseInt(detailDateParts[0]);
                              const month = parseInt(detailDateParts[1]);
                              const dayNum = parseInt(detailDateParts[2]);

                              // Compare with the current day's date components
                              return year === day.getFullYear() &&
                                    month === (day.getMonth() + 1) &&
                                    dayNum === day.getDate();
                            }).length,
                            0
                          )} visits
                        </div>
                      )}
                    </div>
                  </div>
                );
              })}
            </div>
          ))}
        </div>
      </div>
    );
  };

  // Render the appropriate view based on the viewType
  const renderCalendarView = () => {
    switch (viewType) {
      case "D":
        return renderDayView();
      case "W":
        return renderWeekView();
      case "M":
        return renderMonthView();
      default:
        return null;
    }
  };

  // Handle loading and error states
  if (isLoading) {
    return <div className="loading">Loading visits...</div>;
  }

  if (error) {
    return <div className="error">Error loading visits: {(error as Error).message}</div>;
  }

  return (
    <div className="visit-calendar">
      <div className="calendar-header">
        <h2>{getViewTitle()}</h2>

        <div className="view-controls">
          <div className="view-selector gap-2">
            <button
              className={`view-btn ${viewType === "D" ? "active" : ""}`}
              onClick={() => handleViewTypeChange("D")}
            >
              Day
            </button>
            <button
              className={`view-btn ${viewType === "W" ? "active" : ""}`}
              onClick={() => handleViewTypeChange("W")}
            >
              Week
            </button>
            <button
              className={`view-btn ${viewType === "M" ? "active" : ""}`}
              onClick={() => handleViewTypeChange("M")}
            >
              Month
            </button>
          </div>

          {!hideDatePicker && (
            <div className="date-navigator">
              <button
                onClick={goToPrevious}
                className="nav-btn"
                title={`Previous ${viewType === "D" ? "day" : viewType === "W" ? "week" : "month"}`}
              >
                <ChevronLeft size={18} />
              </button>

              <div className="date-picker">
                <NurtifyDateInput
                  value={selectedDate}
                  onChange={handleDateChange}
                  label=""
                />
              </div>

              <button
                onClick={goToNext}
                className="nav-btn"
                title={`Next ${viewType === "D" ? "day" : viewType === "W" ? "week" : "month"}`}
              >
                <ChevronRight size={18} />
              </button>
            </div>
          )}
        </div>
      </div>

      <div className="calendar-content">
        {renderCalendarView()}
      </div>

      {/* Visit Details Modal */}
      <VisitDetailsByDateModal
        isOpen={isModalOpen}
        onClose={handleModalClose}
        selectedDate={modalSelectedDate}
      />
    </div>
  );
};

export default VisitCalendar;

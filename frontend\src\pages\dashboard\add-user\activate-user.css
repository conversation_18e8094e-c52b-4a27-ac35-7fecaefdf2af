.activate-card {
  border-radius: 0; /* Changed */
  box-shadow: none; /* Changed */
  transition: none; /* Changed */
}

.activate-card:hover {
  transform: none; /* Changed */
  box-shadow: none; /* Changed */
}

.card-body {
  padding: 1rem 2rem 2rem 2rem !important; /* Changed top padding */
}

.password-container {
  position: relative;
  margin-bottom: 20px;
}

.password-input {
  width: 100%;
  height: 50px;
  border-radius: 0; /* Changed */
  border: 2px solid #e4e7ea;
  background-color: #f7f8fb;
  font-size: 16px;
  padding: 0 15px;
  transition: all 0.3s ease;
  color: var(--color-dark-1);
  outline: none;
}

.password-input:focus {
  border-color: var(--color-purple-1);
  box-shadow: none; /* Changed */
  background-color: white;
}

label {
  color: #1B2559;
  font-size: 14px;
  font-weight: 500;
  margin-bottom: 8px;
  text-align: left;
  display: block;
}

.password-toggle {
  position: absolute;
  right: 14px;
  top: 55px;
  transform: translateY(-50%);
  background: none;
  border: none;
  color: #94a3b8;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
}

.error-container {
  background-color: rgba(237, 76, 92, 0.1);
  border-radius: 0; /* Changed */
  padding: 10px;
  margin-bottom: 20px;
}

.error-message {
  color: #ed4c5c;
  font-size: 14px;
  margin: 0;
}

.success-message {
  background-color: rgba(16, 185, 129, 0.1);
  color: #10b981;
  font-size: 14px;
  font-weight: 500;
  padding: 10px;
  border-radius: 0; /* Changed */
  margin-top: 10px;
  animation: fadeIn 0.3s ease-in-out;
}

.strength-meter {
  display: flex;
  gap: 4px;
  margin-bottom: 4px;
}

.strength-segment {
  height: 4px;
  flex: 1;
  border-radius: 0; /* Changed */
  transition: background-color 0.3s ease;
}

.strength-text {
  font-size: 12px;
  text-align: left;
}

.password-requirements {
  font-size: 12px;
  color: #64748b;
  margin-bottom: 12px;
  text-align: left;
}

.password-requirements ul {
  padding-left: 16px;
  margin-top: 4px;
}

.submit-button {
  width: 100%;
  height: 50px;
  border-radius: 0; /* Changed */
  border: none;
  background-color: var(--color-purple-1);
  color: white;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: background-color 0.3s ease;
  margin-top: 1.5rem;
}

.submit-button:hover {
  background-color: #2ea0ab;
}

.submit-button:disabled {
  background-color: #B0BEC5;
  cursor: not-allowed;
}

h3 {
  color: #1B2559;
  font-size: 24px;
  font-weight: 600;
  margin-bottom: 0.5rem;
}

p.text-muted {
  color: #64748b;
  margin-bottom: 1.5rem;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Mobile responsiveness */
@media (max-width: 576px) {
  .password-input {
    height: 45px;
    font-size: 14px;
  }
  
  .submit-button {
    height: 45px;
    font-size: 14px;
  }
}

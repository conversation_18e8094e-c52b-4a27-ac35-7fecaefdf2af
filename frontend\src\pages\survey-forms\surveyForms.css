.survey-form-wrapper {
  width: 100%;
  max-width: 900px;
  margin: auto;
  position: relative;
  padding-top: 50px;
  padding-bottom: 50px;
}

.survey-form-container {
  padding-left: 20px;
  padding-right: 20px;
  padding-top: 20px;
  border: 1px solid #000;
  border-radius: 8px;
  background-color: #ffffff;
  position: relative;
}

.survey-form-grid {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.survey-form-group {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.survey-form-group .nurtify-range {
  width: 70%; /* Adjust the width as needed */
  margin-bottom: 20px; /* Add spacing between the range and the inputs */
}

.nurtify-range {
  width: 50% !important; /* Further adjust the width */
  margin: 0 auto 20px auto; /* Center the component and add bottom margin */
}

.nurtify-switch-tiny .nurtify-switch {
    width: 63px !important;
    height: 25px !important;
}

.nurtify-switch-tiny .nurtify-switch-circle {
    
    width: 9px !important;
    height: 9px !important;
}


label {
  font-size: 14px;
  font-weight: bold;
  color: #333;
}

.survey-form-input {
  width: 100%;
  padding: 12px;
  border: 1px solid #000 !important;
  border-radius: 8px;
  background-color: #e0f7f9;
  font-size: 14px;
  color: #333;
}

.survey-form-input:focus {
  outline: none;
  border-color: #000 !important;
  box-shadow: 0 0 3px #000 !important;
}

.survey-section-input {
  width: 40%;
}

.survey-button-container {
  margin-top: 20px;
  display: flex;
  justify-content: flex-start;
}

.survey-center-button {
  justify-content: center;
}

.survey-new-section-button {
  display: flex;
  align-items: center;
  gap: 20px;
  background-color: #3dc6d6;
  color: #fff;
  padding: 10px 20px;
  border: none;
  border-radius: 8px;
  font-size: 14px;
  font-weight: bold;
  cursor: pointer;
  transition: background-color 0.3s ease;
  position: relative;
  top: 10px;
}

.survey-new-section-button:disabled {
  background-color: #96c7cf;
  cursor: not-allowed;
}

.survey-new-section-button:hover {
  background-color: #2ca4b5;
}

.survey-button-icon {
  display: inline-block;
  vertical-align: middle;
}

.survey-plus-icon {
  background-color: #fff;
  color: #3dc6d6;
  border-radius: 50%;
  padding: 2px;
}

.survey-right-icon {
  margin-left: 10px;
}

.survey-section-container {
  margin-top: 20px;
  padding: 20px;
  border: 1px solid #000;
  border-radius: 8px;
  background-color: #f9f9f9;
}

.survey-section-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
}

.survey-section-info {
  display: flex;
  flex-direction: column;
  gap: 8px;
  flex-grow: 1;
  margin-top: 20px;
}

.survey-section-icons {
  display: flex;
  gap: 20px;
  align-self: flex-start;
  right: 20px;
  top: 20px;
}

.survey-add-question-button {
  font-size: 23px;
  width: 30%;
  margin-top: 20px;
  background-color: #3dc6d6;
  color: #fff;
  padding: 10px 40px;
  border: none;
  border-radius: 8px;
  font-size: 14px;
  font-weight: bold;
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.survey-add-question-button:hover {
  background-color: #2ca4b5;
}

.survey-question-container {
  margin-top: 20px;
  padding: 15px;
  border: 2px solid #3dc6d6;
  border-radius: 8px;
  background-color: #f0fcfc;
  margin-bottom: 15px;
}
.survey-question-icons {
  position: absolute;
  top: 5px;
  right: 5px;
  display: flex;
  gap: 10px;
}
.survey-section-icons {
  position: absolute;
  top: 10px;
  right: 10px;
  display: flex;
  gap: 10px;
}
.survey-question-buttons {
  display: flex;
  gap: 20px;
  margin-bottom: 20px;
  flex-direction: row;
  flex-wrap: wrap;
  justify-content: center;
}

.survey-question-button {
  display: flex;
  align-items: center;
  gap: 10px;
  background-color: #3dc6d6;
  color: #fff;
  padding: 10px;
  border: none;
  border-radius: 8px;
  font-size: 14px;
  font-weight: bold;
  cursor: pointer;
  transition: background-color 0.3s ease;
  width: 250px;
  height: 50px;
}

.survey-question-button:hover {
  background-color: #2ca4b5;
}

.survey-toggle-button {
  background-color: #3dc6d6;
  color: #fff;
  padding: 10px 20px;
  border: none;
  border-radius: 8px;
  font-size: 14px;
  font-weight: bold;
  cursor: pointer;
  transition: background-color 0.3s ease;
  margin-right: 10px;
}

.survey-toggle-button:hover {
  background-color: #2ca4b5;
}

.survey-add-textbox-button {
  background-color: #3dc6d6;
  color: #fff;
  padding: 10px 20px;
  border: none;
  border-radius: 8px;
  font-size: 14px;
  font-weight: bold;
  cursor: pointer;
  transition: background-color 0.3s ease;
  margin-bottom: 10px;
  width: 180px;
  align-self: flex-start;
}

.survey-add-textbox-button:hover {
  background-color: #2ca4b5;
}

.survey-question-form {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.survey-question-name-input {
  border: none;
  border-bottom: 1px solid #000;
  padding: 8px;
  font-size: 16px;
  background-color: transparent;
}

.survey-answer-input-group {
  display: flex;
  align-items: center;
  gap: 10px;
}

.survey-answer-input-group .nurtify-radio {
  width: 10px; /* Adjust the width as needed */
  border: none;
}

.survey-answer-input-group .nurtify-input {
  width: 100%; /* Adjust the width as needed */
}

.survey-answer-input {
  border: none !important;
}

.survey-collapse-icon {
  text-align: center;
  cursor: pointer;
  background-color: #c7ebef;
  border-radius: 110px 110px 0 0;
  width: 75px;
  height: 35px;
  display: flex;
  justify-content: center;
  align-items: center;
  margin: 7px auto;
  padding-left: 30px;
  padding-right: 30px;
}

.survey-save-form-button {
  display: flex;
  align-items: center;
  gap: 20px;
  background-color: #3dc6d6;
  color: #fff;
  padding: 10px 20px;
  border: none;
  border-radius: 8px;
  font-size: 14px;
  font-weight: bold;
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.survey-save-form-button:hover {
  background-color: #2ca4b5;
}


@media (max-width: 1024px) {
  .survey-form-grid {
    grid-template-columns: 1fr; /* Switch to single column layout */
  }
}

@media (max-width: 768px) {


  .survey-form-wrapper {
    padding: 15px;
  }

  .survey-form-input {
    font-size: 14px;
    padding: 8px;
  }

  .survey-button-container {
    flex-direction: column;
    align-items: center;
  }

  .survey-add-question-button,
  .survey-new-section-button,
  .survey-save-form-button {
    width: 100%;
    text-align: center;
    font-size: 14px;
    padding: 10px;
    max-width: 300px;
    justify-content: center;
  }
}

@media (max-width: 480px) {
  .survey-form-wrapper {
    padding: 10px;
  }

  .survey-form-input {
    font-size: 13px;
    padding: 7px;
  }

  .survey-add-question-button,
  .survey-new-section-button,
  .survey-save-form-button {
    font-size: 13px;
    padding: 8px;
  }
}

.survey-main-content {
 padding-bottom: 70px;
}

@media (max-width: 480px) {
  .survey-main-content {
    padding-bottom: 250px;
  }
}

@media (min-width: 425px) and (max-width: 768px) {
  .survey-main-content {
    padding-bottom: 200px;
  }
}



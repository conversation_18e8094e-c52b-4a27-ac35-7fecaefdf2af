/* Study Patients Page Styles */
.study-patients-page {
  padding: 30px;
  min-height: calc(100vh - 80px);
  background-color: #f5f7fa;
  font-family: var(--font-primary);
}

/* Page Header */
.page-header {
  margin-bottom: 30px;
}

.page-header h1 {
  font-size: 28px;
  font-weight: 700;
  color: var(--color-dark-1);
  margin: 0 0 8px 0;
  display: flex;
  align-items: center;
  gap: 12px;
}

.page-header p {
  font-size: 16px;
  color: var(--color-light-1);
  margin: 0;
  font-weight: 400;
}

/* Filters Section */
.filters-section {
  background: #ffffff;
  border-radius: 16px;
  padding: 24px;
  margin-bottom: 30px;
  box-shadow: 0 8px 24px rgba(55, 183, 195, 0.08),
              0 2px 4px rgba(55, 183, 195, 0.04);
  border: 1px solid rgba(55, 183, 195, 0.1);
  transition: all 0.3s ease;
}

.filters-section:hover {
  box-shadow: 0 12px 32px rgba(55, 183, 195, 0.12),
              0 4px 8px rgba(55, 183, 195, 0.06);
  transform: translateY(-2px);
}

/* Search Filter */
.search-filter {
  margin-bottom: 20px;
}

.search-input-wrapper {
  position: relative;
  max-width: 500px;
  width: 100%;
}

.search-input {
  width: 100%;
  padding: 16px 20px;
  padding-left: 50px;
  padding-right: 50px;
  border: 1px solid rgba(55, 183, 195, 0.15);
  border-radius: 12px;
  font-size: 15px;
  color: var(--color-dark-1);
  background: rgba(55, 183, 195, 0.03);
  transition: all 0.3s ease;
  outline: none;
}

.search-input:hover {
  background: rgba(55, 183, 195, 0.05);
  border-color: rgba(55, 183, 195, 0.25);
  box-shadow: 0 4px 12px rgba(55, 183, 195, 0.08);
}

.search-input:focus {
  background: #ffffff;
  border-color: var(--color-purple-1);
  box-shadow: 0 0 0 4px rgba(55, 183, 195, 0.1);
  transform: translateY(-1px);
}

.search-input::placeholder {
  color: var(--color-light-1);
  font-style: italic;
}

.search-icon {
  position: absolute;
  left: 16px;
  top: 50%;
  transform: translateY(-50%);
  color: var(--color-purple-1);
  z-index: 2;
  transition: all 0.3s ease;
}

.clear-search {
  position: absolute;
  right: 16px;
  top: 50%;
  transform: translateY(-50%);
  background: none;
  border: none;
  color: var(--color-light-1);
  cursor: pointer;
  padding: 4px;
  border-radius: 50%;
  transition: all 0.3s ease;
  z-index: 2;
  display: flex;
  align-items: center;
  justify-content: center;
}

.clear-search:hover {
  background: rgba(55, 183, 195, 0.1);
  color: var(--color-purple-1);
  transform: translateY(-50%) scale(1.1);
}

/* Filter Controls */
.filter-controls {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 16px;
  margin-top: 20px;
}

.filter-group {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.filter-group label {
  font-size: 14px;
  font-weight: 600;
  color: var(--color-dark-1);
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.filter-select {
  padding: 12px 16px;
  border: 1px solid rgba(55, 183, 195, 0.15);
  border-radius: 10px;
  font-size: 14px;
  font-weight: 500;
  color: var(--color-dark-1);
  background: rgba(55, 183, 195, 0.02);
  cursor: pointer;
  transition: all 0.3s ease;
  appearance: none;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='16' height='16' viewBox='0 0 24 24' fill='none' stroke='%2337B7C3' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpath d='M6 9l6 6 6-6'/%3E%3C/svg%3E");
  background-repeat: no-repeat;
  background-position: right 12px center;
  padding-right: 40px;
}

.filter-select:hover {
  background: rgba(55, 183, 195, 0.05);
  border-color: rgba(55, 183, 195, 0.25);
  box-shadow: 0 4px 8px rgba(55, 183, 195, 0.06);
}

.filter-select:focus {
  outline: none;
  background: #ffffff;
  border-color: var(--color-purple-1);
  box-shadow: 0 0 0 3px rgba(55, 183, 195, 0.1);
}

.filter-select option {
  background: #ffffff;
  color: var(--color-dark-1);
  padding: 8px;
}

/* Results Summary */
.results-summary {
  margin-bottom: 20px;
  padding: 12px 0;
}

.results-summary span {
  font-size: 14px;
  font-weight: 600;
  color: var(--color-light-1);
  background: rgba(55, 183, 195, 0.08);
  padding: 8px 16px;
  border-radius: 20px;
  border: 1px solid rgba(55, 183, 195, 0.15);
}

/* Table Container */
.table-container {
  background: #ffffff;
  border-radius: 16px;
  box-shadow: 0 8px 24px rgba(55, 183, 195, 0.08),
              0 2px 4px rgba(55, 183, 195, 0.04);
  border: 1px solid rgba(55, 183, 195, 0.1);
  overflow: hidden;
  transition: all 0.3s ease;
}

.table-container:hover {
  box-shadow: 0 12px 32px rgba(55, 183, 195, 0.12),
              0 4px 8px rgba(55, 183, 195, 0.06);
  transform: translateY(-2px);
}

/* View Patient Button */
.view-patient-btn {
  display: inline-flex;
  align-items: center;
  gap: 6px;
  padding: 8px 16px;
  background: linear-gradient(135deg, var(--color-purple-1) 0%, #2d919a 100%);
  color: white;
  border: none;
  border-radius: 8px;
  font-size: 13px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 2px 8px rgba(55, 183, 195, 0.25);
  text-decoration: none;
}

.view-patient-btn:hover {
  background: linear-gradient(135deg, #2d919a 0%, #237a82 100%);
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(55, 183, 195, 0.4);
  color: white;
  text-decoration: none;
}

.view-patient-btn:active {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(55, 183, 195, 0.3);
}

.view-patient-btn:focus {
  outline: none;
  box-shadow: 0 0 0 3px rgba(55, 183, 195, 0.3), 0 6px 20px rgba(55, 183, 195, 0.4);
}

/* Loading State */
.loading {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
  background: #ffffff;
  border-radius: 16px;
  box-shadow: 0 4px 12px rgba(55, 183, 195, 0.05);
  border: 1px solid rgba(55, 183, 195, 0.1);
  color: var(--color-light-1);
  font-size: 16px;
  font-weight: 500;
}

.loading::before {
  content: "";
  width: 24px;
  height: 24px;
  border: 3px solid rgba(55, 183, 195, 0.2);
  border-top: 3px solid var(--color-purple-1);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-right: 12px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Enhanced DataTable Integration */
.study-patients-page .data-table-container {
  border-radius: 16px;
  box-shadow: 0 8px 24px rgba(55, 183, 195, 0.08),
              0 2px 4px rgba(55, 183, 195, 0.04);
  border: 1px solid rgba(55, 183, 195, 0.1);
  overflow: hidden;
  transition: all 0.3s ease;
}

.study-patients-page .data-table-container:hover {
  box-shadow: 0 12px 32px rgba(55, 183, 195, 0.12),
              0 4px 8px rgba(55, 183, 195, 0.06);
  transform: translateY(-2px);
}

/* Custom table header styling */
.study-patients-page .data-table thead th {
  background: linear-gradient(135deg, rgba(55, 183, 195, 0.08) 0%, rgba(55, 183, 195, 0.12) 100%);
  color: var(--color-dark-1);
  font-weight: 700;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  font-size: 12px;
  border-bottom: 2px solid rgba(55, 183, 195, 0.15);
}

.study-patients-page .data-table tbody tr:hover {
  background: rgba(55, 183, 195, 0.03);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(55, 183, 195, 0.08);
}

/* Pagination styling */
.study-patients-page .data-table-pagination {
  background: linear-gradient(135deg, rgba(55, 183, 195, 0.02) 0%, rgba(55, 183, 195, 0.05) 100%);
  border-top: 1px solid rgba(55, 183, 195, 0.15);
}

.study-patients-page .pagination-page.active {
  background: var(--color-purple-1);
  border-color: var(--color-purple-1);
  color: white;
  box-shadow: 0 4px 12px rgba(55, 183, 195, 0.3);
}

.study-patients-page .pagination-button:hover:not(:disabled),
.study-patients-page .pagination-page:hover:not(.active) {
  background: rgba(55, 183, 195, 0.1);
  border-color: rgba(55, 183, 195, 0.2);
  color: var(--color-purple-1);
}

/* Responsive Design */
@media (max-width: 1200px) {
  .filter-controls {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (max-width: 991px) {
  .study-patients-page {
    padding: 20px;
  }
  
  .page-header h1 {
    font-size: 24px;
    margin-bottom: 20px;
  }
  
  .filters-section {
    padding: 20px;
    margin-bottom: 20px;
  }
  
  .filter-controls {
    grid-template-columns: 1fr;
    gap: 12px;
  }
  
  .search-input-wrapper {
    max-width: 100%;
  }
}

@media (max-width: 767px) {
  .study-patients-page {
    padding: 15px;
  }
  
  .page-header h1 {
    font-size: 22px;
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }
  
  .filters-section {
    padding: 16px;
  }
  
  .search-filter {
    margin-bottom: 16px;
  }
  
  .search-input {
    padding: 14px 18px;
    padding-left: 45px;
    padding-right: 45px;
    font-size: 14px;
  }
  
  .filter-group label {
    font-size: 13px;
  }
  
  .filter-select {
    padding: 10px 14px;
    font-size: 13px;
  }
  
  .view-patient-btn {
    padding: 6px 12px;
    font-size: 12px;
  }
  
  .results-summary span {
    font-size: 13px;
    padding: 6px 12px;
  }
}

@media (max-width: 480px) {
  .search-input-wrapper {
    border-radius: 10px;
  }
  
  .filter-select {
    border-radius: 8px;
  }
  
  .view-patient-btn {
    width: 100%;
    justify-content: center;
  }
}

/* Animation for smooth transitions */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.study-patients-page > * {
  animation: fadeInUp 0.3s ease-out forwards;
}

.study-patients-page > *:nth-child(2) {
  animation-delay: 0.1s;
}

.study-patients-page > *:nth-child(3) {
  animation-delay: 0.2s;
}

.study-patients-page > *:nth-child(4) {
  animation-delay: 0.3s;
}

/* Focus states for accessibility */
.search-input:focus,
.filter-select:focus,
.view-patient-btn:focus {
  outline: 2px solid var(--color-purple-1);
  outline-offset: 2px;
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .filters-section {
    border: 2px solid var(--color-dark-1);
  }
  
  .search-input {
    border: 2px solid var(--color-dark-1);
  }
  
  .filter-select {
    border: 2px solid var(--color-dark-1);
  }
  
  .view-patient-btn {
    border: 2px solid var(--color-dark-1);
  }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}

/* Print styles */
@media print {
  .study-patients-page {
    background: white;
    padding: 0;
  }
  
  .filters-section {
    display: none;
  }
  
  .view-patient-btn {
    display: none;
  }
  
  .data-table-pagination {
    display: none;
  }
}

.pdf-viewer {
    width: 100%;
    height: 70vh;
    max-height: 800px;
    margin: 0 auto;
    padding: 0;
    box-sizing: border-box;
    border-radius: 12px;
    overflow: hidden;
    box-shadow:
        0 10px 25px rgba(0, 0, 0, 0.1),
        0 4px 10px rgba(0, 0, 0, 0.05);
    background: #ffffff;
    border: 1px solid #e5e7eb;
    position: relative;
    transition: all 0.3s ease;
}

.pdf-viewer:hover {
    box-shadow:
        0 20px 40px rgba(0, 0, 0, 0.15),
        0 8px 20px rgba(0, 0, 0, 0.08);
    transform: translateY(-2px);
}

.pdf-viewer::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, #00B3A6, #0891b2);
    z-index: 10;
}

/* Enhanced toolbar styling */
.pdf-viewer .rpv-default-layout__toolbar {
    background: #f8fafc !important;
    border-bottom: 1px solid #e5e7eb !important;
    padding: 8px 16px !important;
    border-radius: 0 !important;
    min-height: 48px !important;
    display: flex !important;
    align-items: center !important;
    gap: 8px !important;
}

.pdf-viewer .rpv-toolbar__item {
    margin: 0 2px !important;
}

.pdf-viewer .rpv-toolbar__item button,
.pdf-viewer .rpv-core__button {
    background: #ffffff !important;
    border: 1px solid #d1d5db !important;
    border-radius: 4px !important;
    padding: 6px 10px !important;
    color: #374151 !important;
    font-weight: 500 !important;
    font-size: 13px !important;
    transition: all 0.2s ease !important;
    min-height: 32px !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
}

.pdf-viewer .rpv-toolbar__item button:hover,
.pdf-viewer .rpv-core__button:hover {
    background: #00B3A6 !important;
    color: white !important;
    border-color: #00B3A6 !important;
    transform: translateY(-1px);
}

.pdf-viewer .rpv-toolbar__item button:active,
.pdf-viewer .rpv-core__button:active {
    transform: translateY(0);
}

/* Search input styling */
.pdf-viewer .rpv-search__input {
    border: 1px solid #d1d5db !important;
    border-radius: 4px !important;
    padding: 6px 10px !important;
    font-size: 13px !important;
    min-width: 150px !important;
}

.pdf-viewer .rpv-search__input:focus {
    border-color: #00B3A6 !important;
    outline: none !important;
    box-shadow: 0 0 0 2px rgba(0, 179, 166, 0.1) !important;
}

/* Page navigation styling */
.pdf-viewer .rpv-page-navigation__current-page-input {
    border: 1px solid #d1d5db !important;
    border-radius: 4px !important;
    padding: 4px 8px !important;
    font-size: 13px !important;
    width: 60px !important;
    text-align: center !important;
}

.pdf-viewer .rpv-page-navigation__current-page-input:focus {
    border-color: #00B3A6 !important;
    outline: none !important;
    box-shadow: 0 0 0 2px rgba(0, 179, 166, 0.1) !important;
}

/* Zoom dropdown styling */
.pdf-viewer .rpv-zoom__popover-target button {
    background: #ffffff !important;
    border: 1px solid #d1d5db !important;
    border-radius: 4px !important;
    padding: 6px 12px !important;
    color: #374151 !important;
    font-weight: 500 !important;
    min-width: 100px !important;
    font-size: 13px !important;
}

.pdf-viewer .rpv-zoom__popover-target button:hover {
    background: #00B3A6 !important;
    color: white !important;
    border-color: #00B3A6 !important;
}

/* Toolbar separator */
.pdf-viewer .rpv-toolbar__separator {
    width: 1px !important;
    height: 24px !important;
    background: #d1d5db !important;
    margin: 0 8px !important;
}

/* Page container styling */
.pdf-viewer .rpv-core__inner-pages {
    background: #ffffff !important;
    padding: 20px !important;
}

.pdf-viewer .rpv-core__inner-page {
    margin: 0 auto 20px auto !important;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1) !important;
    border-radius: 8px !important;
    overflow: hidden !important;
    background: white !important;
}

/* Scrollbar styling */
.pdf-viewer .rpv-core__inner-pages::-webkit-scrollbar {
    width: 8px;
}

.pdf-viewer .rpv-core__inner-pages::-webkit-scrollbar-track {
    background: #f1f5f9;
    border-radius: 4px;
}

.pdf-viewer .rpv-core__inner-pages::-webkit-scrollbar-thumb {
    background: #cbd5e1;
    border-radius: 4px;
    transition: background 0.2s ease;
}

.pdf-viewer .rpv-core__inner-pages::-webkit-scrollbar-thumb:hover {
    background: #94a3b8;
}

/* Loading state styling */
.pdf-viewer .rpv-core__spinner {
    border-color: #00B3A6 !important;
}

/* Zoom controls enhancement */
.pdf-viewer .rpv-zoom__popover-target button {
    background: #ffffff !important;
    border: 1px solid #d1d5db !important;
    border-radius: 6px !important;
    padding: 8px 16px !important;
    color: #374151 !important;
    font-weight: 500 !important;
    min-width: 80px !important;
}

.pdf-viewer .rpv-zoom__popover-target button:hover {
    background: #00B3A6 !important;
    color: white !important;
    border-color: #00B3A6 !important;
}

@media (max-width: 1024px) {
    .pdf-viewer {
        width: 100%;
        height: 85vh;
        margin: 0 auto;
        border-radius: 8px;
    }

    .pdf-viewer .rpv-core__toolbar {
        padding: 8px 12px !important;
    }

    .pdf-viewer .rpv-core__inner-pages {
        padding: 15px !important;
    }
}

@media (max-width: 768px) {
    .pdf-viewer {
        width: 100%;
        height: 80vh;
        margin: 0 auto;
        border-radius: 6px;
        box-shadow:
            0 4px 15px rgba(0, 0, 0, 0.1),
            0 2px 6px rgba(0, 0, 0, 0.05);
    }

    .pdf-viewer:hover {
        transform: none;
    }

    .pdf-viewer .rpv-core__toolbar {
        padding: 6px 8px !important;
        flex-wrap: wrap !important;
    }

    .pdf-viewer .rpv-core__toolbar-item button {
        padding: 6px 8px !important;
        font-size: 12px !important;
    }

    .pdf-viewer .rpv-core__inner-pages {
        padding: 10px !important;
    }

    .pdf-viewer .rpv-core__inner-page {
        margin-bottom: 15px !important;
    }
}

/* Disable dark mode - force light theme always */
.pdf-viewer {
    color-scheme: light !important;
}

/* Force white background for PDF pages in all modes */
.pdf-viewer .rpv-core__page-layer,
.pdf-viewer .rpv-core__canvas-layer,
.pdf-viewer .rpv-core__text-layer {
    background: #ffffff !important;
}

/* Ensure PDF viewer container always has white background for document area */
.pdf-viewer .rpv-core__viewer {
    background: #ffffff !important;
}

/* Force white background for all PDF viewer elements */
.pdf-viewer .rpv-core__doc,
.pdf-viewer .rpv-core__pages-container,
.pdf-viewer .rpv-core__inner-container,
.pdf-viewer .rpv-default-layout__main,
.pdf-viewer .rpv-default-layout__body {
    background: #ffffff !important;
}

/* Ensure PDF content is visible */
.pdf-viewer .rpv-default-layout__main {
    background: #f8fafc !important;
}

.pdf-viewer .rpv-default-layout__body {
    background: #f8fafc !important;
}

.pdf-viewer .rpv-core__inner-pages {
    background: #f8fafc !important;
    padding: 20px !important;
}

.pdf-viewer .rpv-core__viewer {
    background: #f8fafc !important;
}

/* Ensure PDF pages are visible */
.pdf-viewer .rpv-core__page {
    background: #ffffff !important;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1) !important;
    border-radius: 8px !important;
    margin-bottom: 20px !important;
}

.pdf-viewer .rpv-core__page-layer {
    background: #ffffff !important;
}

.pdf-viewer .rpv-core__canvas-layer {
    background: #ffffff !important;
}

.pdf-viewer .rpv-core__text-layer {
    background: transparent !important;
}

.pdf-viewer .rpv-core__annotation-layer {
    background: transparent !important;
}

/* Force canvas visibility */
.pdf-viewer canvas {
    background: #ffffff !important;
    display: block !important;
    visibility: visible !important;
    opacity: 1 !important;
}

/* Ensure container visibility */
.pdf-viewer .rpv-default-layout__container {
    background: #ffffff !important;
    height: 100% !important;
    display: flex !important;
    flex-direction: column !important;
}

/* Enable manual scrolling for all pages */
.pdf-viewer .rpv-default-layout__main {
    height: calc(100% - 48px) !important;
    overflow-y: auto !important;
    overflow-x: hidden !important;
}

.pdf-viewer .rpv-default-layout__body {
    height: 100% !important;
    overflow-y: auto !important;
    overflow-x: hidden !important;
}

.pdf-viewer .rpv-core__viewer {
    height: 100% !important;
    overflow-y: auto !important;
    overflow-x: hidden !important;
}

/* Container for all pages - enable scrolling */
.pdf-viewer .rpv-core__inner-pages {
    height: auto !important;
    min-height: 100% !important;
    overflow: visible !important;
    display: block !important;
    padding: 0 !important;
}

.pdf-viewer .rpv-core__pages-container {
    height: auto !important;
    overflow: visible !important;
}

.pdf-viewer .rpv-core__doc {
    height: auto !important;
    overflow: visible !important;
}

.pdf-viewer .rpv-core__inner-container {
    height: auto !important;
    overflow: visible !important;
}

/* All pages should be visible in sequence */
.pdf-viewer .rpv-core__inner-page {
    width: 100% !important;
    margin: 0 auto 0 auto !important;
    display: block !important;
    position: relative !important;
}

/* Page styling for continuous scroll */
.pdf-viewer .rpv-core__page {
    width: 100% !important;
    height: auto !important;
    max-width: 100% !important;
    margin: -250 !important;
    padding: -250 !important;
    display: block !important;
    box-shadow: none !important;
    border-radius: 0 !important;
    background: #ffffff !important;
    border: none !important;
}

/* Enhanced scrollbar for better UX */
.pdf-viewer .rpv-default-layout__main::-webkit-scrollbar {
    width: 12px;
}

.pdf-viewer .rpv-default-layout__main::-webkit-scrollbar-track {
    background: #f1f5f9;
    border-radius: 6px;
}

.pdf-viewer .rpv-default-layout__main::-webkit-scrollbar-thumb {
    background: #cbd5e1;
    border-radius: 6px;
    transition: background 0.2s ease;
}

.pdf-viewer .rpv-default-layout__main::-webkit-scrollbar-thumb:hover {
    background: #94a3b8;
}

/* Fullscreen mode support */
.pdf-viewer .rpv-fullscreen {
    background: #ffffff !important;
}

.pdf-viewer .rpv-fullscreen .rpv-core__viewer {
    background: #ffffff !important;
}

.pdf-viewer .rpv-fullscreen .rpv-core__page {
    background: #ffffff !important;
}

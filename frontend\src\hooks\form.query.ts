import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { FORM_KEYS, SUBMISSION_KEYS, TAG_KEYS, HOLISTIC_FORM_KEYS } from './keys';
import * as formService from '../services/api/form.service';
import { HolisticFormData } from '../types/types';
import {
  FormPayload,
  SubmissionPayload,
  TagPayload,
  FormSubmissionQueryCreate,
  FormSubmissionQueryUpdate,
  FormSubmissionQueryResolve,
  QueryResponseCreate,
  QueryResponseUpdate
} from '@/services/api/types';

// Add to the keys object at the top of the file
export const QUERY_KEYS = {
  LIST: 'form-submission-queries',
  READ: 'form-submission-queries/{uuid}',
  RESPONSES: 'form-submission-queries-responses',
  FORM_SUBMISSION_QUERIES: 'form-submission-queries-by-patient'
};

// Form Query Hooks
export const useGetForms = () => {
  return useQuery({
    queryKey: [FORM_KEYS.LIST],
    queryFn: formService.getForms,
  });
};

export const useGetFormByUuid = (uuid: string, options?: { enabled?: boolean }) => {
  return useQuery({
    queryKey: [FORM_KEYS.READ.replace('{uuid}', uuid), uuid],
    queryFn: () => formService.getFormByUuid(uuid),
    enabled: options?.enabled !== undefined ? options.enabled && !!uuid : !!uuid,
  });
};

export const useCreateForm = () => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: (formData: FormPayload) => formService.createForm(formData),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: [FORM_KEYS.LIST] });
    },
  });
};

export const useUpdateForm = (uuid: string) => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: (formData: FormPayload) => formService.updateForm(uuid, formData),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: [FORM_KEYS.LIST] });
      queryClient.invalidateQueries({ queryKey: [FORM_KEYS.READ.replace('{uuid}', uuid), uuid] });
    },
  });
};

export const useDeleteForm = () => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: (uuid: string) => formService.deleteForm(uuid),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: [FORM_KEYS.LIST] });
    },
  });
};

export const useGetFormsByUser = (identifier: string) => {
  return useQuery({
    queryKey: [FORM_KEYS.BY_USER.replace('{identifier}', identifier), identifier],
    queryFn: () => formService.getFormsByUser(identifier),
    enabled: !!identifier,
  });
};

export const useGetFormsByPatient = (patientUuid: string) => {
  return useQuery({
    queryKey: [FORM_KEYS.BY_PATIENT.replace('{patientUuid}', patientUuid), patientUuid],
    queryFn: () => formService.getFormsByPatient(patientUuid),
    enabled: !!patientUuid,
  });
};

export const useGetFormsBySponsor = (sponsorIdentifier: string) => {
  return useQuery({
    queryKey: [FORM_KEYS.BY_SPONSOR, sponsorIdentifier],
    queryFn: () => formService.getFormsBySponsor(sponsorIdentifier),
    enabled: !!sponsorIdentifier,
  });
};

export const useGetPendingForms = () => {
  return useQuery({
    queryKey: [FORM_KEYS.PENDING],
    queryFn: formService.getPendingForms,
  });
};

export const useGetFormVersions = (uuid: string) => {
  return useQuery({
    queryKey: [FORM_KEYS.VERSIONS.replace('{uuid}', uuid), uuid],
    queryFn: () => formService.getFormVersions(uuid),
    enabled: !!uuid,
  });
};

export const useAcceptForm = () => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: (uuid: string) => formService.acceptForm(uuid),
    onSuccess: (_, uuid) => {
      queryClient.invalidateQueries({ queryKey: [FORM_KEYS.LIST] });
      queryClient.invalidateQueries({ queryKey: [FORM_KEYS.PENDING] });
      queryClient.invalidateQueries({ queryKey: [FORM_KEYS.READ.replace('{uuid}', uuid), uuid] });
    },
  });
};

export const useRejectForm = () => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: (uuid: string) => formService.rejectForm(uuid),
    onSuccess: (_, uuid) => {
      queryClient.invalidateQueries({ queryKey: [FORM_KEYS.LIST] });
      queryClient.invalidateQueries({ queryKey: [FORM_KEYS.PENDING] });
      queryClient.invalidateQueries({ queryKey: [FORM_KEYS.READ.replace('{uuid}', uuid), uuid] });
    },
  });
};

// Custom type for form submission with both form_uuid and form for compatibility
interface FormSubmissionPayload extends SubmissionPayload {
  form_uuid: string;
  existing_attachments?: string[]; // Add support for existing attachments
  original_submission?: Record<string, unknown>; // Add support for original submission data
  attachments?: {
    image_attachments?: File[];
    video_attachments?: File[];
    document_attachments?: File[];
  };
  final_submission?: boolean;
}

// Form Submission Query Hooks
export const useGetSubmissions = () => {
  return useQuery({
    queryKey: [SUBMISSION_KEYS.LIST],
    queryFn: formService.getSubmissions,
  });
};

export const useGetSubmissionByUuid = (uuid: string) => {
  return useQuery({
    queryKey: [SUBMISSION_KEYS.READ.replace('{uuid}', uuid), uuid],
    queryFn: () => formService.getSubmissionByUuid(uuid),
    enabled: !!uuid,
  });
};

export const useCreateSubmission = () => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: async (submissionData: FormSubmissionPayload) => {
      // TRACKING: Log the existing_attachments field in the submission data
      console.log("TRACKING: existing_attachments in useCreateSubmission:", {
        exists: 'existing_attachments' in submissionData,
        isArray: Array.isArray(submissionData.existing_attachments),
        length: submissionData.existing_attachments ? submissionData.existing_attachments.length : 0,
        value: submissionData.existing_attachments
      });

      // CRITICAL FIX: If we're updating an existing submission, fetch it first to get the attachment UUIDs
      // Check for form_uuid which is always present
      if (submissionData.form_uuid) {
        try {
          // Try to find existing submissions for this form
          const existingSubmissions = await formService.getSubmissionsByForm(submissionData.form_uuid);
          console.log("TRACKING: Retrieved existing submissions for form:", submissionData.form_uuid);

          // Find a submission that matches the patient_uuid if available
          let existingSubmission = null;
          if (submissionData.patient_uuid && Array.isArray(existingSubmissions)) {
            existingSubmission = existingSubmissions.find(sub => sub.patient_uuid === submissionData.patient_uuid);
          } else if (Array.isArray(existingSubmissions) && existingSubmissions.length > 0) {
            // Just take the first one if we can't match by patient
            existingSubmission = existingSubmissions[0];
          }

          if (existingSubmission &&
              typeof existingSubmission === 'object' &&
              'attachments' in existingSubmission &&
              Array.isArray(existingSubmission.attachments)) {

            console.log("TRACKING: Found existing submission with attachments:", existingSubmission.uuid);

            // Initialize existing_attachments if it doesn't exist
            if (!submissionData.existing_attachments) {
              submissionData.existing_attachments = [];
            }

            // Extract UUIDs from the attachments array
            existingSubmission.attachments.forEach(attachment => {
              if (attachment && typeof attachment === 'object' && 'uuid' in attachment) {
                const uuid = String(attachment.uuid);
                if (uuid && Array.isArray(submissionData.existing_attachments) &&
                    !submissionData.existing_attachments.includes(uuid)) {
                  submissionData.existing_attachments.push(uuid);
                  console.log(`TRACKING: Added UUID ${uuid} from existing submission attachments`);
                }
              }
            });
          }
        } catch (error) {
          console.error("Error fetching existing submissions:", error);
        }
      }

      // CRITICAL FIX: Make sure existing_attachments is an array
      if (!submissionData.existing_attachments) {
        submissionData.existing_attachments = [];
        console.log("TRACKING: Initialized empty existing_attachments array");
      }

      // CRITICAL FIX: Log the final existing_attachments before submission
      console.log("TRACKING: Final existing_attachments before submission:", {
        isArray: Array.isArray(submissionData.existing_attachments),
        length: Array.isArray(submissionData.existing_attachments) ? submissionData.existing_attachments.length : 0,
        value: submissionData.existing_attachments
      });

      return formService.createSubmission(submissionData);
    },
    onSuccess: (_, variables) => {
      queryClient.invalidateQueries({ queryKey: [SUBMISSION_KEYS.LIST] });
      queryClient.invalidateQueries({
        queryKey: [SUBMISSION_KEYS.BY_FORM.replace('{uuid}', variables.form_uuid), variables.form_uuid]
      });
      if (variables.patient_uuid) {
        queryClient.invalidateQueries({
          queryKey: [SUBMISSION_KEYS.BY_PATIENT.replace('{patient_uuid}', variables.patient_uuid), variables.patient_uuid]
        });
        queryClient.invalidateQueries({
          queryKey: [SUBMISSION_KEYS.PATIENT_ASSIGNED_FORMS.replace('{patient_uuid}', variables.patient_uuid), variables.patient_uuid]
        });
      }
    },
  });
};

export const useUpdateSubmission = (uuid: string) => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: async (submissionData: FormSubmissionPayload) => {
      // CRITICAL FIX: Always check for attachments in the original submission
      // This ensures we don't lose files when updating a form
      let extractedUuids: string[] = [];

      // First check if we already have existing_attachments
      if (submissionData.existing_attachments && submissionData.existing_attachments.length > 0) {
        console.log(`TRACKING: Updating submission with ${submissionData.existing_attachments.length} existing attachments:`,
          submissionData.existing_attachments);

        // Ensure all UUIDs are strings
        extractedUuids = submissionData.existing_attachments.map(uuid => String(uuid));
      }

      // CRITICAL FIX: Log the submission data to verify existing_attachments is included
      console.log("TRACKING: existing_attachments in submission data:", {
        included: 'existing_attachments' in submissionData,
        type: Array.isArray(submissionData.existing_attachments) ? 'array' : typeof submissionData.existing_attachments,
        length: Array.isArray(submissionData.existing_attachments) ? submissionData.existing_attachments.length : 0,
        value: submissionData.existing_attachments
      });

      // Then check if we have an original submission with attachments
      if (submissionData.original_submission &&
          typeof submissionData.original_submission === 'object' &&
          'attachments' in submissionData.original_submission &&
          Array.isArray(submissionData.original_submission.attachments)) {

        // Extract UUIDs from the original submission's attachments
        const uuidsFromOriginal = submissionData.original_submission.attachments
          .filter(attachment => attachment && typeof attachment === 'object' && 'uuid' in attachment)
          .map(attachment => String(attachment.uuid));

        if (uuidsFromOriginal.length > 0) {
          console.log(`TRACKING: Extracted ${uuidsFromOriginal.length} attachment UUIDs from original submission:`,
            uuidsFromOriginal);

          // Add these UUIDs to our list
          extractedUuids = [...extractedUuids, ...uuidsFromOriginal];
        }
      }

      // CRITICAL FIX: Check if we have a direct attachments array in the submission data
      // This is needed for the case where we're updating an existing submission
      try {
        const getSubmissionResponse = await formService.getSubmissionByUuid(uuid);
        console.log("TRACKING: Retrieved submission data for UUID:", uuid);

        if (getSubmissionResponse &&
            typeof getSubmissionResponse === 'object' &&
            'attachments' in getSubmissionResponse &&
            Array.isArray(getSubmissionResponse.attachments)) {

          console.log("TRACKING: Found attachments in submission response:", getSubmissionResponse.attachments);

          // Extract UUIDs from the attachments array
          const uuidsFromResponse = getSubmissionResponse.attachments
            .filter(attachment => attachment && typeof attachment === 'object' && 'uuid' in attachment)
            .map(attachment => String(attachment.uuid));

          if (uuidsFromResponse.length > 0) {
            console.log(`TRACKING: Extracted ${uuidsFromResponse.length} attachment UUIDs from submission response:`,
              uuidsFromResponse);

            // Add these UUIDs to our list
            extractedUuids = [...extractedUuids, ...uuidsFromResponse];
          }
        }
      } catch (error) {
        console.error("Error fetching existing submission:", error);
      }

      // Remove any duplicates and update the submission data
      if (extractedUuids.length > 0) {
        // Initialize existing_attachments if it doesn't exist
        if (!submissionData.existing_attachments) {
          submissionData.existing_attachments = [];
        }

        // Add any UUIDs we extracted that aren't already in the list
        extractedUuids.forEach(uuid => {
          if (Array.isArray(submissionData.existing_attachments) &&
              !submissionData.existing_attachments.includes(uuid)) {
            submissionData.existing_attachments.push(uuid);
          }
        });

        console.log("TRACKING: Final existing_attachments after extraction:", submissionData.existing_attachments);
      }

      return formService.updateSubmission(uuid, submissionData);
    },
    onSuccess: (_, variables) => {
      queryClient.invalidateQueries({ queryKey: [SUBMISSION_KEYS.LIST] });
      queryClient.invalidateQueries({
        queryKey: [SUBMISSION_KEYS.READ.replace('{uuid}', uuid), uuid]
      });
      if (variables.form) {
        queryClient.invalidateQueries({
          queryKey: [SUBMISSION_KEYS.BY_FORM.replace('{uuid}', variables.form), variables.form]
        });
      }
      if (variables.patient_uuid) {
        queryClient.invalidateQueries({
          queryKey: [SUBMISSION_KEYS.BY_PATIENT.replace('{patient_uuid}', variables.patient_uuid), variables.patient_uuid]
        });
        queryClient.invalidateQueries({
          queryKey: [SUBMISSION_KEYS.PATIENT_ASSIGNED_FORMS.replace('{patient_uuid}', variables.patient_uuid), variables.patient_uuid]
        });
      }
    },
  });
};

export const useDeleteSubmission = () => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: (uuid: string) => formService.deleteSubmission(uuid),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: [SUBMISSION_KEYS.LIST] });
    },
  });
};

export const useGetSubmissionsByForm = (formUuid: string) => {
  return useQuery({
    queryKey: [SUBMISSION_KEYS.BY_FORM.replace('{uuid}', formUuid), formUuid],
    queryFn: () => formService.getSubmissionsByForm(formUuid),
    enabled: !!formUuid,
  });
};

export const useGetSubmissionsByPatient = (patientUuid: string) => {
  return useQuery({
    queryKey: [SUBMISSION_KEYS.BY_PATIENT.replace('{patient_uuid}', patientUuid), patientUuid],
    queryFn: () => formService.getSubmissionsByPatient(patientUuid),
    enabled: !!patientUuid,
  });
};

export const useGetPatientAssignedForms = (patientUuid: string) => {
  return useQuery({
    queryKey: [SUBMISSION_KEYS.PATIENT_ASSIGNED_FORMS.replace('{patient_uuid}', patientUuid), patientUuid],
    queryFn: () => formService.getPatientAssignedForms(patientUuid),
    enabled: !!patientUuid,
  });
};

export const useUpdateFormSubmissionByFormAndPatient = (formUuid: string, patientUuid: string) => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: (submissionData: { submission?: Record<string, unknown>; is_completed?: boolean }) =>
      formService.updateFormSubmissionByFormAndPatient(formUuid, patientUuid, submissionData),
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: [SUBMISSION_KEYS.BY_PATIENT.replace('{patient_uuid}', patientUuid), patientUuid]
      });
      queryClient.invalidateQueries({
        queryKey: [SUBMISSION_KEYS.PATIENT_ASSIGNED_FORMS.replace('{patient_uuid}', patientUuid), patientUuid]
      });
      queryClient.invalidateQueries({
        queryKey: [SUBMISSION_KEYS.BY_FORM.replace('{uuid}', formUuid), formUuid]
      });
    },
  });
};

export const useDeleteFormSubmissionByFormAndPatient = (formUuid: string, patientUuid: string) => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: () => formService.deleteFormSubmissionByFormAndPatient(formUuid, patientUuid),
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: [SUBMISSION_KEYS.BY_PATIENT.replace('{patient_uuid}', patientUuid), patientUuid]
      });
      queryClient.invalidateQueries({
        queryKey: [SUBMISSION_KEYS.PATIENT_ASSIGNED_FORMS.replace('{patient_uuid}', patientUuid), patientUuid]
      });
      queryClient.invalidateQueries({
        queryKey: [SUBMISSION_KEYS.BY_FORM.replace('{uuid}', formUuid), formUuid]
      });
      queryClient.invalidateQueries({ queryKey: [SUBMISSION_KEYS.LIST] });
    },
  });
};

// Tags Query Hooks
export const useGetTags = () => {
  return useQuery({
    queryKey: [TAG_KEYS.LIST],
    queryFn: formService.getTags,
  });
};

export const useGetTagByUuid = (uuid: string) => {
  return useQuery({
    queryKey: [TAG_KEYS.READ.replace('{uuid}', uuid), uuid],
    queryFn: () => formService.getTagByUuid(uuid),
    enabled: !!uuid,
  });
};

export const useCreateTag = () => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: (tagData: TagPayload) => formService.createTag(tagData),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: [TAG_KEYS.LIST] });
    },
  });
};

export const useUpdateTag = (uuid: string) => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: (tagData: TagPayload) => formService.updateTag(uuid, tagData),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: [TAG_KEYS.LIST] });
      queryClient.invalidateQueries({ queryKey: [TAG_KEYS.READ.replace('{uuid}', uuid), uuid] });
    },
  });
};

export const useDeleteTag = () => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: (uuid: string) => formService.deleteTag(uuid),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: [TAG_KEYS.LIST] });
    },
  });
};

// Specialized hooks for filtering submissions by completion status
export const useGetCompletedSubmissionsByPatient = (patientUuid: string) => {
  const { data, isLoading, error } = useGetSubmissionsByPatient(patientUuid);

  return {
    data: data?.filter(submission => submission.is_completed === true && !submission.final_submission) || [],
    isLoading,
    error
  };
};

export const useGetFinalizedSubmissionsByPatient = (patientUuid: string) => {
  const { data, isLoading, error } = useGetSubmissionsByPatient(patientUuid);

  return {
    data: data?.filter(submission => submission.final_submission === true) || [],
    isLoading,
    error
  };
};

export const useGetIncompleteSubmissionsByPatient = (patientUuid: string) => {
  const { data, isLoading, error } = useGetSubmissionsByPatient(patientUuid);

  return {
    data: data?.filter(submission => submission.is_completed === false) || [],
    isLoading,
    error
  };
};

export const useGetUnsubmittedFormsByPatient = (patientUuid: string) => {
  const { data: allForms, isLoading: isLoadingForms } = useGetFormsByPatient(patientUuid);
  const { isLoading: isLoadingSubmissions } = useGetSubmissionsByPatient(patientUuid);

  const isLoading = isLoadingForms || isLoadingSubmissions;
  const error = null;

  // Return all forms without filtering out submitted ones
  let forms: Array<{uuid: string, [key: string]: unknown}> = [];

  if (!isLoading && allForms) {
    // Define a type for the paginated response
    type PaginatedFormsResponse = {
      results: Array<{uuid: string, [key: string]: unknown}>;
      [key: string]: unknown;
    };

    // Return all forms without filtering
    forms = Array.isArray(allForms)
      ? allForms.map(form => ({...form}))
      : ((allForms as PaginatedFormsResponse)?.results || []);
  }

  return {
    data: forms,
    isLoading,
    error
  };
};

// Hook for getting a specific submission by form and patient UUIDs for edit mode
export const useGetSubmissionByFormAndPatient = (formUuid: string, patientUuid: string) => {
  const { data: patientSubmissions, isLoading: isLoadingSubmissions, error: submissionsError } =
    useGetSubmissionsByPatient(patientUuid);

  const submission = patientSubmissions?.find(sub => {
    // Handle both string form uuid and object form with uuid property
    const subFormUuid = typeof sub.form === 'string' ? sub.form : sub.form?.uuid;
    // Only find incomplete submissions
    return subFormUuid === formUuid && !sub.is_completed;
  });

  return {
    data: submission,
    isLoading: isLoadingSubmissions,
    error: submissionsError,
    exists: !!submission
  };
};

export const useFinalizeSubmissions = () => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: (data: {
      submission_uuids?: string[];
      submission_uuid?: string
    }) => formService.finalizeSubmissions(data),
    onSuccess: (response, variables) => {
      // Invalidate all potentially affected queries
      queryClient.invalidateQueries({ queryKey: [SUBMISSION_KEYS.LIST] });

      // Invalidate individual submission queries for each finalized UUID
      if (response.finalized && response.finalized.length > 0) {
        response.finalized.forEach(uuid => {
          queryClient.invalidateQueries({
            queryKey: [SUBMISSION_KEYS.READ.replace('{uuid}', uuid), uuid]
          });
        });
      }

      // If we have a single submission UUID, invalidate related queries
      if (variables.submission_uuid) {
        const uuid = variables.submission_uuid;
        queryClient.invalidateQueries({
          queryKey: [SUBMISSION_KEYS.READ.replace('{uuid}', uuid), uuid]
        });
      }

      // If we have multiple submission UUIDs, invalidate all of them
      if (variables.submission_uuids) {
        variables.submission_uuids.forEach(uuid => {
          queryClient.invalidateQueries({
            queryKey: [SUBMISSION_KEYS.READ.replace('{uuid}', uuid), uuid]
          });
        });
      }

      // Explicitly invalidate all patient-related submission queries
      // This is crucial for updating the "submitted" tab data
      queryClient.invalidateQueries({
        queryKey: [SUBMISSION_KEYS.BY_PATIENT]
      });

      // Force a refetch of all submission-related queries
      setTimeout(() => {
        queryClient.refetchQueries({
          queryKey: [SUBMISSION_KEYS.BY_PATIENT],
          exact: false
        });
      }, 100);
    },
  });
};


export const useCreateHolisticFormSubmission = () => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: (formData: HolisticFormData) =>
      formService.createHolisticFormSubmission(formData),
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: [HOLISTIC_FORM_KEYS.LIST]
      });
    },
    onError: (error) => {
      console.error('Error creating holistic form:', error);
    },
  });
};

// Form Submission Query Hooks
export const useGetFormSubmissionQueries = (params?: {
  form_submission_uuid?: string;
  is_resolved?: boolean;
  priority?: 'low' | 'medium' | 'high';
}) => {
  return useQuery({
    queryKey: [QUERY_KEYS.LIST, params],
    queryFn: () => formService.getFormSubmissionQueries(params),
  });
};

export const useGetFormSubmissionQueryByUuid = (uuid: string) => {
  return useQuery({
    queryKey: [QUERY_KEYS.READ.replace('{uuid}', uuid), uuid],
    queryFn: () => formService.getFormSubmissionQueryByUuid(uuid),
    enabled: !!uuid,
  });
};

export const useCreateFormSubmissionQuery = () => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: (queryData: FormSubmissionQueryCreate) =>
      formService.createFormSubmissionQuery(queryData),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: [QUERY_KEYS.LIST] });
    },
  });
};

export const useUpdateFormSubmissionQuery = (uuid: string) => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: (queryData: FormSubmissionQueryUpdate) =>
      formService.updateFormSubmissionQuery(uuid, queryData),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: [QUERY_KEYS.LIST] });
      queryClient.invalidateQueries({
        queryKey: [QUERY_KEYS.READ.replace('{uuid}', uuid), uuid]
      });
    },
  });
};

export const useResolveFormSubmissionQuery = (uuid: string) => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: (resolutionData: FormSubmissionQueryResolve) =>
      formService.resolveFormSubmissionQuery(uuid, resolutionData),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: [QUERY_KEYS.LIST] });
      queryClient.invalidateQueries({
        queryKey: [QUERY_KEYS.READ.replace('{uuid}', uuid), uuid]
      });
    },
  });
};

export const useGetQueryResponses = (params?: { query_uuid?: string }) => {
  return useQuery({
    queryKey: [QUERY_KEYS.RESPONSES, params],
    queryFn: () => formService.getQueryResponses(params),
    enabled: !!params?.query_uuid,
  });
};

export const useCreateQueryResponse = () => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: (responseData: QueryResponseCreate) =>
      formService.createQueryResponse(responseData),
    onSuccess: (_, variables) => {
      queryClient.invalidateQueries({
        queryKey: [QUERY_KEYS.RESPONSES, { query_uuid: variables.query }]
      });
    },
  });
};

export const useUpdateQueryResponse = (uuid: string, queryUuid: string) => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: (responseData: QueryResponseUpdate) =>
      formService.updateQueryResponse(uuid, responseData),
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: [QUERY_KEYS.RESPONSES, { query_uuid: queryUuid }]
      });
    },
  });
};

export const useGetFormSubmissionQueriesByPatient = (patientUuid: string, params?: {
  is_resolved?: boolean;
  priority?: 'low' | 'medium' | 'high';
  assigned_to?: string;
}) => {
  return useQuery({
    queryKey: [QUERY_KEYS.FORM_SUBMISSION_QUERIES, 'patient', patientUuid, params],
    queryFn: () => formService.getFormSubmissionQueriesByPatient(patientUuid, params),
    enabled: !!patientUuid
  });
};

// Hook to get all open queries for all patients
export const useGetAllOpenQueriesQuery = () => {
  return useQuery({
    queryKey: [QUERY_KEYS.LIST, 'all-open'],
    queryFn: () => formService.getFormSubmissionQueries({ is_resolved: false }),
  });
};


export const useGenerateHolisticFormPDF = () => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: (uuid: string) => formService.generateHolisticFormPDF(uuid),
    onSuccess: (_data, uuid) => {
      queryClient.invalidateQueries({
        queryKey: [HOLISTIC_FORM_KEYS.LIST]
      });
      queryClient.invalidateQueries({
        queryKey: HOLISTIC_FORM_KEYS.DETAIL(uuid)
      });
    },
    onError: (error) => {
      console.error('Error generating PDF:', error);
    },
  });
};

export const useGenerateDynamicFormPDF = () => {
  const queryClient = useQueryClient();

  return useMutation({
    // Accept both uuid and isSubmission
    mutationFn: ({ uuid, isSubmission }: { uuid: string; isSubmission: boolean }) =>
      formService.generateDynamicFormPDF(uuid, isSubmission),

    onSuccess: (_data, { uuid }) => {
      queryClient.invalidateQueries({
        queryKey: [HOLISTIC_FORM_KEYS.LIST],
      });
      queryClient.invalidateQueries({
        queryKey: HOLISTIC_FORM_KEYS.DETAIL(uuid),
      });
    },

    onError: (error) => {
      console.error('Error generating PDF:', error);
    },
  });
};

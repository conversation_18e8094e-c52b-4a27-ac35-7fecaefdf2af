import React, { useState } from 'react';
import './CommentModal.css';

interface CommentModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSubmit: (comment: string) => void;
  action: 'complete' | 'reject';
}

const CommentModal: React.FC<CommentModalProps> = ({ isOpen, onClose, onSubmit, action }) => {
  const [comments, setComment] = useState('');

  if (!isOpen) return null;

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    onSubmit(comments);
    setComment('');
  };

  return (
    <div className="comment-modal-overlay">
      <div className="comment-modal-content">
        <h2 className="comment-modal-title">
          {action === 'complete' ? 'Complete Conversation' : 'Reject Conversation'}
        </h2>
        <form onSubmit={handleSubmit}>
          <div className="comment-modal-body">
            <label htmlFor="comment">Describe the actions taken to close this conversation</label>
            <textarea
              id="comments"
              value={comments}
              onChange={(e) => setComment(e.target.value)}
              required
              placeholder="Enter your comment..."
              rows={4}
            />
          </div>
          <div className="comment-modal-actions">
            <button type="button" onClick={onClose} className="comment-modal-button cancel">
              Cancel
            </button>
            <button type="submit" className="comment-modal-button submit">
              {action === 'complete' ? 'Complete' : 'Reject'}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default CommentModal;
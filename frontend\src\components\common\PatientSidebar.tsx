import React, { useState, useEffect, useCallback } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  ChevronLeft,
  ChevronRight,
  MessageSquare,
  BookOpen,
  Calendar,
  HelpCircle,
  LogOut,
  Signature,
  Search,
  User,
  FileText,
  LayoutDashboard,
  RefreshCw,
  Pill,
  Activity,
  UserCircle
} from 'lucide-react';
import './PatientSidebar.css';
import { useCurrentUserQuery } from "@/hooks/user.query";
import { useKeycloak } from "@react-keycloak/web";
import { useQueryClient } from "@tanstack/react-query";

type PatientSidebarProps = {
  onTabChange?: (tab: string) => void;
  currentTab?: string;
};

const PatientSidebar: React.FC<PatientSidebarProps> = ({
  onTabChange,
  currentTab = 'report'
}) => {
  const { keycloak } = useKeycloak();
  const navigate = useNavigate();
  const queryClient = useQueryClient();


  // State for collapsed sidebar
  const { data: currentUser } = useCurrentUserQuery();
  const [isCollapsed, setIsCollapsed] = useState(() => {
    const stored = localStorage.getItem('patientSidebarCollapsed') === 'true';
    return stored || window.innerWidth <= 991;
  });

  const [activeTab, setActiveTab] = useState(currentTab);
  const [isMobile, setIsMobile] = useState(window.innerWidth <= 991);

  const handleResize = useCallback(() => {
    setIsMobile(window.innerWidth <= 991);
  }, []);

  useEffect(() => {
    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, [handleResize]);

  useEffect(() => {
    localStorage.setItem('patientSidebarCollapsed', isCollapsed.toString());
  }, [isCollapsed]);

  useEffect(() => {
    if (currentTab) {
      setActiveTab(currentTab);
    }
  }, [currentTab]);

  const toggleSidebar = () => {
    setIsCollapsed(!isCollapsed);
  };

  const handleTabChange = (tab: string) => {
    setActiveTab(tab);
    if (onTabChange) {
      onTabChange(tab);
    }
  };


  const handleLogout = () => {
    const redirectUri = `${window.location.origin}/`;
    queryClient.clear();
    keycloak.logout({ redirectUri: redirectUri });
  };

  return (
    <div
      className={`patient-sidebar ${isCollapsed ? 'collapsed' : ''} ${isMobile ? 'mobile' : ''}`}
      role="navigation"
      aria-label="Patient navigation"
    >
      <div className="patient-minimize-button" onClick={toggleSidebar}>
        {isCollapsed ? <ChevronRight size={20} /> : <ChevronLeft size={20} />}
      </div>

      {isCollapsed ? (
        // Collapsed view - only icons
        <div className="patient-icons-container">
          <div className="user-avatar-collapsed">
            <User size={24} />
          </div>
          <div
            className={`patient-icon ${activeTab === 'dashboard' ? 'active' : ''}`}
            onClick={() => handleTabChange('dashboard')}
          >
            <LayoutDashboard size={20} />
          </div>
          <div
            className={`patient-icon ${activeTab === 'refund-request' ? 'active' : ''}`}
            onClick={() => navigate('/patient/conversation?type=refund')}
          >
            <RefreshCw size={20} />
          </div>
          <div
            className={`patient-icon ${activeTab === 'messages' ? 'active' : ''}`}
            onClick={() => navigate('/patient/live-chat')}
          >
            <MessageSquare size={20} />
          </div>
          <div
            className={`patient-icon ${activeTab === 'my-forms' ? 'active' : ''}`}
            onClick={() => handleTabChange('my-forms')}
          >
            <FileText size={20} />
          </div>
          <div
            className={`patient-icon ${activeTab === 'studies' ? 'active' : ''}`}
            onClick={() => handleTabChange('studies')}
          >
            <BookOpen size={20} />
          </div>
          <div
            className={`patient-icon ${activeTab === 'medication' ? 'active' : ''}`}
            onClick={() => handleTabChange('medication')}
          >
            <Pill size={20} />
          </div>
          <div
            className={`patient-icon ${activeTab === 'symptoms' ? 'active' : ''}`}
            onClick={() => handleTabChange('symptoms')}
          >
            <Activity size={20} />
          </div>
          <div
            className={`patient-icon ${activeTab === 'appointments' ? 'active' : ''}`}
            onClick={() => handleTabChange('appointments')}
          >
            <Calendar size={20} />
          </div>
          <div
            className={`patient-icon ${activeTab === 'resources' ? 'active' : ''}`}
            onClick={() => handleTabChange('resources')}
          >
            <HelpCircle size={20} />
          </div>
          <div
            className={`patient-icon ${activeTab === 'consent' ? 'active' : ''}`}
            onClick={() => handleTabChange('consent')}
          >
            <Signature size={20} />
          </div>
          <div
            className={`patient-icon ${activeTab === 'show-interest' ? 'active' : ''}`}
            onClick={() => handleTabChange('show-interest')}
          >
            <Search size={20} />
          </div>
          <div
            className={`patient-icon ${activeTab === 'my-profile' ? 'active' : ''}`}
            onClick={() => handleTabChange('my-profile')}
          >
            <UserCircle size={20} />
          </div>
          <div className="patient-icon logout" onClick={handleLogout}>
            <LogOut size={20} />
          </div>
        </div>
      ) : (
        // Expanded view - full content
        <div className="patient-content-container">
          <div className="user-avatar-expanded">
            <div className="avatar-circle">
              <User size={24} />
            </div>
            <div className="user-info">
              <div className="user-name">{currentUser?.first_name} {currentUser?.last_name}</div>
            </div>
          </div>
          <div
            className={`patient-menu-item ${activeTab === 'dashboard' ? 'active' : ''}`}
            onClick={() => handleTabChange('dashboard')}
          >
            <LayoutDashboard size={18} />
            <span>Dashboard</span>
          </div>

          <div
            className={`patient-menu-item ${activeTab === 'refund-request' ? 'active' : ''}`}
            onClick={() => navigate('/patient/conversation?type=refund')}
          >
            <RefreshCw size={18} />
            <span>Refund Request</span>
          </div>

          <div
            className={`patient-menu-item ${activeTab === 'messages' ? 'active' : ''}`}
            onClick={() => navigate('/patient/live-chat')}
          >
            <MessageSquare size={18} />
            <span>Messages</span>
          </div>

          <div
            className={`patient-menu-item ${activeTab === 'my-forms' ? 'active' : ''}`}
            onClick={() => handleTabChange('my-forms')}
          >
            <FileText size={18} />
            <span>My Forms</span>
          </div>

          <div
            className={`patient-menu-item ${activeTab === 'studies' ? 'active' : ''}`}
            onClick={() => handleTabChange('studies')}
          >
            <BookOpen size={18} />
            <span>My Studies</span>
          </div>

          <div
            className={`patient-menu-item ${activeTab === 'medication' ? 'active' : ''}`}
            onClick={() => handleTabChange('medication')}
          >
            <Pill size={18} />
            <span>My Medication</span>
          </div>

          <div
            className={`patient-menu-item ${activeTab === 'symptoms' ? 'active' : ''}`}
            onClick={() => handleTabChange('symptoms')}
          >
            <Activity size={18} />
            <span>My Symptoms</span>
          </div>

          <div
            className={`patient-menu-item ${activeTab === 'appointments' ? 'active' : ''}`}
            onClick={() => handleTabChange('appointments')}
          >
            <Calendar size={18} />
            <span>My Appointments</span>
          </div>

          <div
            className={`patient-menu-item ${activeTab === 'resources' ? 'active' : ''}`}
            onClick={() => handleTabChange('resources')}
          >
            <HelpCircle size={18} />
            <span>Resources</span>
          </div>

          <div
            className={`patient-menu-item ${activeTab === 'consent' ? 'active' : ''}`}
            onClick={() => handleTabChange('consent')}
          >
            <Signature size={18} />
            <span>Consent</span>
          </div>

          <div
            className={`patient-menu-item ${activeTab === 'show-interest' ? 'active' : ''}`}
            onClick={() => handleTabChange('show-interest')}
          >
            <Search size={18} />
            <span>Show interest in study</span>
          </div>

          <div
            className={`patient-menu-item ${activeTab === 'my-profile' ? 'active' : ''}`}
            onClick={() => handleTabChange('my-profile')}
          >
            <UserCircle size={18} />
            <span>My Profile</span>
          </div>

          <div className="patient-menu-item logout" onClick={handleLogout}>
            <LogOut size={18} />
            <span>Log Out</span>
          </div>
        </div>
      )}
    </div>
  );
};

export default PatientSidebar;

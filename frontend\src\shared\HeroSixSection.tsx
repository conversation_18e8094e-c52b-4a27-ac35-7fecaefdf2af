import { useEffect, useRef, useState } from "react";
import { motion, useInView } from "framer-motion";
import { Link } from "react-router-dom";
import "./hero-section.css";

// Helper function to create URL-friendly slugs
const createBlogUrl = (article: { id: number, title: string }) => {
  const slug = article.title.toLowerCase()
    .replace(/[^\w\s-]/g, '') // Remove special chars
    .replace(/\s+/g, '-')     // Replace spaces with hyphens
    .trim();
  return `${article.id}-${slug}`;
};

const HeroSixSection: React.FC = () => {
  const ref = useRef<HTMLDivElement>(null);
  const isInView = useInView(ref, { once: true });
  const [, setIsMobile] = useState<boolean>(window.innerWidth < 768);

  const handleResize = () => {
    setIsMobile(window.innerWidth < 768);
  };

  useEffect(() => {
    window.addEventListener("resize", handleResize);
    return () => {
      window.removeEventListener("resize", handleResize);
    };
  }, []);

  const articles = [
    {
      id: 1,
      image: "/assets/img/home-1/hero/trial.jpeg",
      author: "Mohamed Amine Haouem",
      date: "31 Mar 2025",
      title: "Clinical Trials",
      excerpt: "A comprehensive guide to understanding the complexities of clinical trials and care."
    },
    {
      id: 2,
      image: "/assets/img/home-1/hero/insulin.jpeg",
      author: 'Dr. James Wilson',
      date: '15 Feb 2024',
      title: "Advanced Diabetes: A Deep Dive into DKA and HHS",
      excerpt: "Understanding the pathophysiology, diagnosis, and management of diabetic ketoacidosis and hyperosmolar hyperglycemic state."
    },
    {
      id: 3,
      image: "/assets/img/home-1/hero/sepsisBlog.jpeg",
      author: "Sarah Johnson, RN",
      date: "31 Mar 2025",
      title: "Understanding Sepsis: Early Detection and Management",
      excerpt: "A comprehensive guide to recognizing the signs of sepsis and implementing evidence-based interventions to improve outcomes."
    },
    {
      id: 4,
      image: "/assets/img/home-1/hero/criticalBlog.jpeg",
      author: "Mohamed Amine Haouem",
      date: "31 Mar 2025",
      title: "Advances in Critical Care Nursing",
      excerpt: "Exploring the latest research and innovations in critical care nursing to improve patient outcomes and quality of care."
    }
  ];

  return (
    <div className="news-container" ref={ref}>
      <div className="container">
        <motion.p
          className="news-subtitle"
          initial={{ opacity: 0, y: -20 }}
          animate={isInView ? { opacity: 1, y: 0 } : {}}
          transition={{ duration: 0.5 }}
        >
          Latest Articles
        </motion.p>

        <motion.h2
          className="news-title"
          initial={{ opacity: 0, y: -20 }}
          animate={isInView ? { opacity: 1, y: 0 } : {}}
          transition={{ duration: 0.5 }}
        >
          Knowledge <span className="text-highlight">Hub</span>
        </motion.h2>

        <div className="news-grid">
          {/* Featured Article */}
          <motion.div
            className="featured-article"
            initial={{ opacity: 0, scale: 0.95, y: -20 }}
            animate={isInView ? { opacity: 1, scale: 1, y: 0 } : {}}
            transition={{ duration: 0.5 }}
          >
            <div className="featured-article-image">
              <img src={articles[0].image} alt={articles[0].title} />
            </div>
            <div className="article-meta">
              <span className="article-author">{articles[0].author}</span>
              <span className="article-date">{articles[0].date}</span>
            </div>
            <h3 className="article-title">{articles[0].title}</h3>
            <p className="article-excerpt">{articles[0].excerpt}</p>
            <Link to={`/blog/${createBlogUrl(articles[0])}`}>
              <motion.button
                className="article-button"
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
              >
                Read Article
              </motion.button>
            </Link>
          </motion.div>

          {/* Secondary Articles */}
          <div className="secondary-articles">
            {articles.map((article, index) => (
              <motion.div
                key={article.id}
                className="secondary-article"
                initial={{ opacity: 0, y: 20 }}
                animate={isInView ? { opacity: 1, y: 0 } : {}}
                transition={{ duration: 0.5, delay: index * 0.2 }}
              >
                <div className="secondary-article-image">
                  <img src={article.image} alt={article.title} />
                </div>
                <div className="secondary-article-content">
                  <div className="article-meta">
                    <span className="article-author">{article.author}</span>
                    <span className="article-date">{article.date}</span>
                  </div>
                  <h3 className="article-title">{article.title}</h3>
                  <p className="article-excerpt">{article.excerpt}</p>
                </div>
              </motion.div>
            ))}
          </div>
        </div>

        <motion.div
          className="view-all-container"
          initial={{ opacity: 0, y: 20 }}
          animate={isInView ? { opacity: 1, y: 0 } : {}}
          transition={{ duration: 0.5, delay: 0.4 }}
        >
          <Link to="/blog">
            <button className="view-all-button">
              View All Articles
            </button>
          </Link>
        </motion.div>
      </div>
    </div>
  );
};

export default HeroSixSection;

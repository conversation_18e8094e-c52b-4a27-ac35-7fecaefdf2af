/* Patient Sidebar - Base Styles */
.patient-sidebar {
  position: fixed;
  top: 0;
  left: 0;
  height: 100vh;
  z-index: 1000;
  transition: all 0.3s ease;
  display: flex;
  flex-direction: column;
  background-color: #ffffff;
}

/* Collapsed State */
.patient-sidebar.collapsed {
  width: 60px;
  background-color: #ffffff;
  border-radius: 0 8px 8px 0;
  border-right: 1px solid #e0e0e0;
  box-shadow: 2px 0 10px rgba(0, 0, 0, 0.1);
  overflow: visible;
}

/* Expanded State */
.patient-sidebar:not(.collapsed) {
  width: 240px;
  background-color: #ffffff;
  color: #333333;
  border-right: 1px solid #e0e0e0;
  box-shadow: 2px 0 10px rgba(0, 0, 0, 0.1);
}

/* Minimize Button */
.patient-minimize-button {
  width: 30px;
  height: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  color: #666666;
  background-color: #ffffff;
  border-radius: 4px;
  position: absolute;
  border: 1px solid #e0e0e0;
  top: 10px;
  right: -5px;
  margin-right: 10px;
  z-index: 1001;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.patient-sidebar:not(.collapsed) .patient-minimize-button {
  color: #666666;
  background-color: #ffffff;
  border: 1px solid #e0e0e0;
}

.patient-sidebar.collapsed .patient-minimize-button {
  color: #666666;
  background-color: #ffffff;
  border: 1px solid #e0e0e0;
}

/* User Avatar Styles */
.user-avatar-collapsed {
  width: 40px;
  height: 40px;
  background-color: #508C9B;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 20px auto;
  color: #EEEEEE;
  flex-shrink: 0;
}

.user-avatar-expanded {
  display: flex;
  gap: 10px;
  align-items: center;
  padding: 15px;
  margin-bottom: 20px;
  border-bottom: 1px solid #e0e0e0;
}

.avatar-circle {
  width: 40px;
  height: 40px;
  background-color: #508C9B;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #EEEEEE;
}

.user-info {
  display: flex;
  flex-direction: column;
}

.user-name {
  font-weight: 600;
  color: #333333;
  font-size: 14px;
}

/* Icons Container - Collapsed State */
.patient-icons-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 50px 5px 20px 5px;
  height: 100%;
  overflow-y: auto;
  gap: 8px;
  visibility: visible;
  opacity: 1;
}

.patient-sidebar.collapsed .patient-icons-container {
  visibility: visible;
  opacity: 1;
  display: flex;
}

.patient-icon {
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #666666;
  cursor: pointer;
  position: relative;
  border-radius: 6px;
  transition: all 0.2s ease;
  flex-shrink: 0;
  visibility: visible;
  opacity: 1;
}

.patient-sidebar.collapsed .patient-icon {
  visibility: visible;
  opacity: 1;
  display: flex;
}

.patient-icon.active {
  background-color: #4ECDC4;
  color: #ffffff;
}

.patient-icon:hover {
  background-color: #f5f5f5;
  color: #333333;
}

.patient-icon.logout {
  margin-top: auto;
  margin-bottom: 10px;
  color: #666666;
  background-color: transparent;
}

.patient-icon.logout:hover {
  background-color: #f5f5f5;
  color: #333333;
}

/* Content Container - Expanded State */
.patient-content-container {
  padding: 50px 15px 15px;
  height: 100%;
  display: flex;
  flex-direction: column;
  overflow-y: auto;
}

.patient-sidebar.collapsed .patient-content-container {
  display: none;
}

/* Menu Items */
.patient-menu-item {
  display: flex;
  align-items: center;
  gap: 10px;
  padding: 12px 15px;
  cursor: pointer;
  color: #666666;
  font-size: 14px;
  margin-bottom: 5px;
  border-radius: 6px;
  transition: all 0.2s ease;
}

.patient-menu-item:hover {
  background-color: #f5f5f5;
  color: #333333;
}

.patient-menu-item.active {
  background-color: #4ECDC4;
  color: #ffffff;
  font-weight: 600;
}

.patient-menu-item.logout {
  margin-top: auto;
  color: #666666;
  background-color: transparent;
}

.patient-menu-item.logout:hover {
  background-color: #f5f5f5;
  color: #333333;
}

/* Notification Badge */
.notification-badge {
  position: absolute;
  top: -5px;
  right: -5px;
  background-color: #ff6b6b;
  color: white;
  border-radius: 50%;
  width: 18px;
  height: 18px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 10px;
  font-weight: bold;
}

.menu-icon-wrapper {
  position: relative;
}

/* Mobile Adjustments */
@media (max-width: 991px) {
  .patient-sidebar {
    top: 0;
    height: 100vh;
    transform: translateX(-100%);
    transition: transform 0.3s ease;
  }

  .patient-sidebar.show {
    transform: translateX(0);
  }

  .patient-sidebar.collapsed {
    width: 60px;
    transform: translateX(-100%);
  }

  .patient-sidebar.collapsed.show {
    transform: translateX(0);
  }

  .patient-sidebar:not(.collapsed) {
    width: 240px;
  }

  .patient-sidebar.mobile.collapsed {
    transform: translateX(-100%);
  }

  .patient-sidebar.mobile.collapsed.show {
    transform: translateX(0);
  }

  .patient-sidebar.mobile:not(.collapsed) {
    transform: translateX(-100%);
  }

  .patient-sidebar.mobile:not(.collapsed).show {
    transform: translateX(0);
  }
}

/* Tablet Adjustments */
@media (max-width: 768px) {
  .patient-sidebar {
    width: 280px;
    transform: translateX(-100%);
  }

  .patient-sidebar.show {
    transform: translateX(0);
  }

  .patient-sidebar.collapsed {
    width: 60px;
  }
}

/* Custom Scroll Bar Styling */
.patient-content-container::-webkit-scrollbar,
.patient-icons-container::-webkit-scrollbar {
  width: 4px;
}

.patient-content-container::-webkit-scrollbar-track,
.patient-icons-container::-webkit-scrollbar-track {
  background: transparent;
}

.patient-content-container::-webkit-scrollbar-thumb,
.patient-icons-container::-webkit-scrollbar-thumb {
  background-color: #37B7C3;
  border-radius: 2px;
}

.patient-content-container::-webkit-scrollbar-thumb:hover,
.patient-icons-container::-webkit-scrollbar-thumb:hover {
  background-color: #2a9aa5;
}

/* Mobile Overlay */
.sidebar-overlay {
  display: none;
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  z-index: 999;
  transition: opacity 0.3s ease;
}

.sidebar-overlay.show {
  display: block;
}

/* Mobile Toggle Button */
.sidebar-toggle {
  display: none;
  position: fixed;
  top: 20px;
  left: 20px;
  z-index: 1001;
  background: #4ECDC4;
  color: white;
  border: none;
  border-radius: 8px;
  padding: 8px;
  cursor: pointer;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  transition: background-color 0.2s ease;
}

.sidebar-toggle:hover {
  background: #3bb3ac;
}

@media (max-width: 991px) {
  .sidebar-toggle {
    display: block;
  }
}

/* Small mobile screens */
@media (max-width: 576px) {
  .patient-sidebar {
    width: 100%;
    max-width: 320px;
  }

  .patient-minimize-button {
    width: 24px;
    height: 24px;
    right: -12px;
  }

  .patient-icon {
    width: 34px;
    height: 34px;
    margin-bottom: 15px;
  }

  .patient-menu-item {
    padding: 10px 12px;
    font-size: 13px;
  }

  .sidebar-toggle {
    top: 15px;
    left: 15px;
    padding: 6px;
  }
}

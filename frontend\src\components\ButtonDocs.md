# NurtifyButton Usage Guide

The `NurtifyButton` component is a reusable, customizable button for your application. It supports multiple variants, sizes, outline styles, disabled state, and can be used with icons.

---

## 1. Importing

```tsx
import NurtifyButton from "@/components/NurtifyButton";
```

---

## 2. Basic Usage

```tsx
<NurtifyButton variant="primary" size="medium" onClick={() => alert("Clicked!")}>
  Primary Button
</NurtifyButton>
```

---

## 3. Props

| Prop      | Type     | Description                                                      |
|-----------|----------|------------------------------------------------------------------|
| variant   | string   | One of: "primary", "success", "danger", "warning", "info", "light", "dark", "link" |
| size      | string   | One of: "small", "regular", "medium"                              |
| outline   | boolean  | If true, renders an outline style                                 |
| disabled  | boolean  | If true, disables the button                                      |
| onClick   | function | Click handler                                                     |
| ...rest   | any      | Any other standard button props (type, style, etc.)               |

---

## 4. Variants

```tsx
<NurtifyButton variant="primary">Primary</NurtifyButton>
<NurtifyButton variant="success">Success</NurtifyButton>
<NurtifyButton variant="danger">Danger</NurtifyButton>
<NurtifyButton variant="warning">Warning</NurtifyButton>
<NurtifyButton variant="info">Info</NurtifyButton>
<NurtifyButton variant="light">Light</NurtifyButton>
<NurtifyButton variant="dark">Dark</NurtifyButton>
<NurtifyButton variant="link">Link</NurtifyButton>
```

---

## 5. Sizes

```tsx
<NurtifyButton size="small">Small</NurtifyButton>
<NurtifyButton size="regular">Regular</NurtifyButton>
<NurtifyButton size="medium">Medium</NurtifyButton>
```

---

## 6. Outline Style

```tsx
<NurtifyButton variant="primary" outline>Outline Primary</NurtifyButton>
<NurtifyButton variant="success" outline>Outline Success</NurtifyButton>
```

---

## 7. Disabled State

```tsx
<NurtifyButton variant="danger" disabled>Disabled Danger</NurtifyButton>
```

---

## 8. With Icons

```tsx
import { Eye } from "lucide-react";

<NurtifyButton variant="primary" size="medium">
  <Eye size={16} style={{ marginRight: 6 }} />
  View Details
</NurtifyButton>
```

---

## 9. Custom Styles

You can pass a `style` prop or use a custom className for further customization:

```tsx
<NurtifyButton variant="primary" style={{ background: '#088395' }}>
  Custom Color
</NurtifyButton>
```

---

## 10. Example: All Features

```tsx
<NurtifyButton variant="success" size="small" outline onClick={() => alert('Success!')}>
  <Eye size={14} style={{ marginRight: 4 }} />
  Success Small Outline
</NurtifyButton>
```

---

## 11. Notes
- You can use NurtifyButton anywhere in your project after importing it.
- It supports all standard button props.
- For more examples, see the SDK demo page.
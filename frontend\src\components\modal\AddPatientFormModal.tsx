import React, { useEffect, useState, useCallback, useMemo, useRef } from 'react';
import './addpatientformmodal.css';
import { usePatientStore } from '@/store/patientState';
import { useAllergyStore } from '@/store/AllergyState';
import useSelectedPatientStore from '@/store/SelectedPatientState';
import { faTrash, faUser, faNotesMedical, faExclamationCircle, faCheckCircle, faArrowRight, faTimes } from "@fortawesome/free-solid-svg-icons";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import axios from 'axios';
import { useNavigate } from 'react-router-dom';

import { useCreatePatientMutation, useAddPatientAccessLogMutation } from '@/hooks/patient.query';
import { useCurrentUserQuery } from '@/hooks/user.query';
import { useDepartmentsByHospitalQuery } from '@/hooks/department.query';
import NurtifyToggle from '../NurtifyToggle';
import NurtifyInput from '../NurtifyInput';
import MEDICATIONLIST from "@/pages/forms/holistic-form/constants/MEDICATIONLIST.json";
import NurtifySelect from '../NurtifySelect';
import NurtifyText from '../NurtifyText';
import { motion, AnimatePresence } from "framer-motion";
import { Search, Loader2, Info } from "lucide-react";

interface Allergy {
  name: string;
}

// Define the props for the AddPatientFormModal component
interface AddPatientFormModalProps {
  isOpen: boolean;
  setIsModalOpen: (value: boolean) => void;
  setSwitchModal: (value: boolean) => void;
  switchModal: boolean;
  isEditing?: boolean; // Add isEditing prop
}

const AddPatientFormModal: React.FC<AddPatientFormModalProps> = ({
  isOpen,
  setIsModalOpen,
  isEditing = false,
}) => {
  // Use the patient store
  const patientState = usePatientStore();
  const navigate = useNavigate();
  const { data: currentUser } = useCurrentUserQuery();
  const departmentname = currentUser?.department?.name || "";
  const hospitalname = currentUser?.hospital?.name || "";
  const hospital_uuid = currentUser?.hospital?.uuid || "";
  const department_uuid = currentUser?.department?.uuid || "";
  const { data: Departments } = useDepartmentsByHospitalQuery(hospital_uuid)
  const { setSelectedPatient } = useSelectedPatientStore();
  const addPatientAccessLogMutation = useAddPatientAccessLogMutation();
  const {allergies, addAllergy, removeAllergy, clearAllergies} = useAllergyStore();
  const [allergicPatient, setAllergicPatient] = useState("false");
  const [medicationSearchInput, setMedicationSearchInput] = useState<string>("");
  const [medicationsSelectedOptions, setMedicationsSelectedOptions] = useState<Allergy[]>(allergies || []);
  const [attemptedSubmit, setAttemptedSubmit] = useState(false);
  const [activeTab, setActiveTab] = useState('personal');
  const [matchedMedicationsOptions, setMatchedMedicationsOptions] = useState<string[]>([]);
  const [nhsNumberConfirm, setNhsNumberConfirm] = useState('');
  const [mrnConfirm, setMrnConfirm] = useState('');
  const [selectedDepartmentUuid, setSelectedDepartmentUuid] = useState('');

  const [autocompleteSuggestions, setAutocompleteSuggestions] = useState<string[]>([]);
  const [showPostcodeAutocomplete, setShowPostcodeAutocomplete] = useState(false);
  const [isPostcodeApiLoading, setIsPostcodeApiLoading] = useState(false);
  const [postcodeApiError, setPostcodeApiError] = useState<string | null>(null);
  const autocompletePostcodeRef = useRef<HTMLUListElement>(null); // Ref for the autocomplete list


  const postcodeDebounceTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  const API_BASE_URL = 'https://api.postcodes.io';


  // Create patient mutation and error state
  const createPatientMutation = useCreatePatientMutation();
  const [apiError, setApiError] = useState<string | null>(null);

  // Validate email format
  const isValidEmail = (email: string) => {
    if (!email) return false;
    // Basic validation: contains @ and at least one dot after @
    const hasAtSymbol = email.includes('@');
    if (!hasAtSymbol) return false;

    const [, domain] = email.split('@');
    return domain && domain.includes('.');
  };

  // Validate phone number format (basic validation)
  const isValidPhoneNumber = (phone: string) => {
    if (!phone?.trim()) return false;
    // Accept only digits, plus signs, parentheses, spaces, and hyphens
    return /^[0-9+() -]+$/.test(phone.trim());
  };

  // Validate NHS number (must be exactly 10 digits)
  const isValidNHSNumber = (nhs: string) => {
    if (!nhs?.trim()) return false;
    return /^\d{10}$/.test(nhs.trim());
  };

  // Check missing fields by tab
  const missingFields = useMemo(() => {
    return {
      personal: {
        first_name: !patientState.first_name?.trim(),
        last_name: !patientState.last_name?.trim(),
        gender: !patientState.gender,
        date_of_birth: !patientState.date_of_birth,
        email: !patientState.email?.trim() || !isValidEmail(patientState.email),
        phone_number: !patientState.phone_number?.trim() || !isValidPhoneNumber(patientState.phone_number),
        address: !patientState.address?.trim(), // Any non-empty address is valid
        postcode: !patientState.postcode?.trim() // Postcode is required
      },
      health: {
        nhs_number: !patientState.nhs_number?.trim() || !isValidNHSNumber(patientState.nhs_number),
        medical_record_number: !patientState.medical_record_number?.trim(),
        department: !department_uuid && !selectedDepartmentUuid // Department is required if user doesn't have one
      },
      recheck: {
        nhs_number_confirmed: !nhsNumberConfirm?.trim() || !isValidNHSNumber(nhsNumberConfirm) || nhsNumberConfirm !== patientState.nhs_number,
        medical_record_number_confirmed: !mrnConfirm?.trim() || mrnConfirm !== patientState.medical_record_number
      }
    };
  }, [
    patientState.first_name,
    patientState.last_name,
    patientState.gender,
    patientState.date_of_birth,
    patientState.email,
    patientState.phone_number,
    patientState.address,
    patientState.nhs_number,
    patientState.medical_record_number,
    patientState.postcode,
    nhsNumberConfirm,
    mrnConfirm,
    department_uuid,
    selectedDepartmentUuid
  ]);

  // Check if tabs have all required fields filled
  const tabCompletionStatus = useMemo(() => {
    const personalComplete = !Object.values(missingFields.personal).some(value => value);
    const healthComplete = !Object.values(missingFields.health).some(value => value);
    const recheckComplete = !Object.values(missingFields.recheck).some(value => value);

    return {
      personal: personalComplete,
      health: healthComplete,
      recheck: recheckComplete
    };
  }, [missingFields]);

  // Check if all required fields are filled
  const isFormValid = useMemo(() => {
    return tabCompletionStatus.personal && tabCompletionStatus.health && tabCompletionStatus.recheck;
  }, [tabCompletionStatus]);

  // Find the first tab with missing fields
  const findIncompleteTab = useCallback(() => {
    if (!tabCompletionStatus.personal) return 'personal';
    if (!tabCompletionStatus.health) return 'health';
    if (!tabCompletionStatus.recheck) return 'recheck';
    return null;
  }, [tabCompletionStatus]);

  useEffect(() => {
    if (allergies) {
      setMedicationsSelectedOptions(allergies);
    }
  }, [allergies]);

  const genderOptions = [
    { value: "Select a gender", label: "" },
    { value: "Male", label: "Male" },
    { value: "Female", label: "Female" },
    { value: "Prefer_not_to_disclose", label: "Prefer not to disclose" },
  ];

  
  const fetchPostcodeAutocompleteSuggestions = async (query: string) => {

  setIsPostcodeApiLoading(true);

  setPostcodeApiError(null);

  try {

   const response = await axios.get(`${API_BASE_URL}/postcodes/${query}/autocomplete`);

   if (response.status === 200) {

    setAutocompleteSuggestions(response.data.result || []);

    setShowPostcodeAutocomplete(true);

   } else {

    setAutocompleteSuggestions([]);

    if (response.status !== 404) {

     setPostcodeApiError(response.data.error);

    }

   }

  } catch (error) {
    console.error("Failed to fetch postcode suggestions:", error);

   setPostcodeApiError('Failed to fetch postcode suggestions. Please try again.');

   setAutocompleteSuggestions([]);

  } finally {

   setIsPostcodeApiLoading(false);

  }

 };



 const fetchFullAddressDetails = async (pc: string) => {

  setIsPostcodeApiLoading(true);

  setPostcodeApiError(null);

  try {

   const response = await axios.get(`${API_BASE_URL}/postcodes/${pc}`);

   if (response.status === 200 && response.data.result) {

    const result = response.data.result;

    patientState.setField('address', `${result.line_1 || ''}, ${result.postcode || ''}`);
    patientState.setField('postcode', result.postcode);

    setShowPostcodeAutocomplete(false);

   } else {

    setPostcodeApiError(response.data.error || 'Postcode not found or invalid.');

    patientState.setField('address', '');
    patientState.setField('postcode', '');

   }

  } catch (error) {
    console.error("Failed to lookup postcode details:", error);
   setPostcodeApiError('Failed to lookup postcode details. Please try again.');

   patientState.setField('address', '');
   patientState.setField('postcode', '');

  } finally {
  

   setIsPostcodeApiLoading(false);

  }

 };



 // Handle postcode input change (specific handler)

 const handlePostcodeInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {

  const { value } = e.target;

  patientState.setField('postcode', value);

 };



 // Handle selecting a suggestion

 const handlePostcodeSuggestionSelect = (suggestion: string) => {

  patientState.setField('postcode', suggestion);

  setShowPostcodeAutocomplete(false);

  fetchFullAddressDetails(suggestion);

 };



 // Effect for postcode autocomplete debouncing

 useEffect(() => {

  if (postcodeDebounceTimeoutRef.current) {

   clearTimeout(postcodeDebounceTimeoutRef.current);

  }



  if (patientState.postcode.length > 1) {

   postcodeDebounceTimeoutRef.current = setTimeout(() => {

    fetchPostcodeAutocompleteSuggestions(patientState.postcode);

   }, 300);

  } else {

   setAutocompleteSuggestions([]);

   setShowPostcodeAutocomplete(false);

  }



  return () => {

   if (postcodeDebounceTimeoutRef.current) {

    clearTimeout(postcodeDebounceTimeoutRef.current);

   }

  };

 }, [patientState.postcode]);



 // Handle click outside for postcode autocomplete

 useEffect(() => {

  const handleClickOutside = (event: MouseEvent) => {

   if (autocompletePostcodeRef.current && !autocompletePostcodeRef.current.contains(event.target as Node)) {

    setShowPostcodeAutocomplete(false);

   }

  };

  document.addEventListener('mousedown', handleClickOutside);

  return () => {

   document.removeEventListener('mousedown', handleClickOutside);

  };

 }, []);


  const handleMedicationsSearchInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setMedicationSearchInput(value);

    // Filter the options based on the search input
    const matchedOptions = MEDICATIONLIST.filter((option) =>
      option.toLowerCase().includes(value.toLowerCase())
    );
    setMatchedMedicationsOptions(matchedOptions.slice(0, 10)); // Limit to 10 results for performance
  };

  // Handle medication search key press (Enter)
  const handleMedicationSearchKeyPress = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === "Enter") {
      const inputOption = medicationSearchInput.trim();
      if (inputOption && !medicationsSelectedOptions.some(option => option.name === inputOption)) {
        // Add the input option to the selectedOptions array
        addAllergy(inputOption);
        // Clear the search input and matched options
        setMedicationSearchInput("");
        setMatchedMedicationsOptions([]);
      }
    }
  };

  const handleMedicationOptionClick = (option: string) => {
    if (!medicationsSelectedOptions.some((item) => item.name === option)) {
      // Add the selected option as an Allergy object
      addAllergy(option);
      // Clear the search input and matched options
      setMedicationSearchInput("");
      setMatchedMedicationsOptions([]);
    }
  };

  // Handle medication remove option
  const handleMedicationsRemoveOption = (index: number) => {
    // Remove the option from the selectedOptions array
    removeAllergy(index);
  };

  const onClose = () => {
    setIsModalOpen(false);
    clearAllergies();
    patientState.resetForm();
    setAttemptedSubmit(false);
    setApiError(null);
    setSelectedDepartmentUuid('');
  };

  const handleNext = () => {
    setAttemptedSubmit(true);

    // If current tab is complete, move to next tab
    if (activeTab === 'personal' && tabCompletionStatus.personal) {
      setActiveTab('health');
    } else if (activeTab === 'health' && tabCompletionStatus.health) {
      setActiveTab('recheck');
    } else if (activeTab === 'recheck' && tabCompletionStatus.recheck) {
      // All tabs are complete, submit the form
      patientState.setField('allergies', allergies);

      // Call the API to create the patient
      createPatientMutation.mutate(
        {
          first_name: patientState.first_name,
          last_name: patientState.last_name,
          gender: patientState.gender,
          date_of_birth: patientState.date_of_birth,
          email: patientState.email,
          phone_number: patientState.phone_number,
          address: patientState.address,
          nhs_number: patientState.nhs_number,
          medical_record_number: patientState.medical_record_number,
          ethnic_background: patientState.ethnic_background,
          allergies: allergies,
          department_uuid: department_uuid || selectedDepartmentUuid,
          postcode: patientState.postcode,
        },
        {
          onSuccess: (response) => {
            // Set the selected patient in the store
            setSelectedPatient(response);

            // Add access log for creating the patient
            addPatientAccessLogMutation.mutate({
              patient_uuid: response.uuid,
              access_type: 'create'
            });

            // Close the modal and navigate to patient board
            onClose();
            navigate('/org/dashboard/patient-board');
          },
          onError: (error) => {
            console.error("Error creating patient:", error);

            // Extract specific error message from API response
            let errorMessage = "Failed to create patient. Please try again.";

            // Check if it's an Axios error with a response
            if (axios.isAxiosError(error) && error.response && error.response.data) {
              const responseData = error.response.data;

              // Check if the response data contains field-specific errors
              if (typeof responseData === 'object' && responseData !== null) {
                // Get the first error message from the response data
                const firstErrorField = Object.keys(responseData)[0];
                if (firstErrorField && responseData[firstErrorField]) {
                  errorMessage = `${firstErrorField}: ${responseData[firstErrorField]}`;
                }
              } else if (typeof responseData === 'string') {
                // If the response data is a string, use it directly
                errorMessage = responseData;
              }
            }

            // Set the API error message
            setApiError(errorMessage);
          },
        }
      );
    } else {
      // Current tab is incomplete, stay on it
      const incompleteTab = findIncompleteTab();
      if (incompleteTab) {
        setActiveTab(incompleteTab);
      }
    }
  };

  const isLastTab = activeTab === 'recheck';

// Determine if a field is invalid (missing and attempted submit)
const isFieldInvalid = (field: string, tab: 'personal' | 'health' | 'recheck') => {
  // Only show validation errors if the user has attempted to submit AND we're on the current tab
  if (!attemptedSubmit || activeTab !== tab) return false;
  return missingFields[tab][field as keyof typeof missingFields[typeof tab]];
};
  // Reset state when the modal opens
useEffect(() => {
  if (isOpen) {
    setAttemptedSubmit(false);
    setActiveTab('personal'); // Reset to the first tab when the modal opens
    setNhsNumberConfirm(''); // Reset NHS number confirmation
    setMrnConfirm(''); // Reset MRN confirmation
    setSelectedDepartmentUuid(''); // Reset selected department
  }
}, [isOpen]);
  if (!isOpen) return null;

  return (
    <div className="add-patient-form-modal" style={{ backgroundColor: 'rgba(0, 0, 0, 0.5)' }}>
      <div className="add-patient-form" style={{ backgroundColor: 'white', boxShadow: '0 4px 8px rgba(0, 0, 0, 0.1)' }}>
        <h2 className="add-new-patient-form-title">Patient details</h2>

        {apiError && (
          <div className="api-error-message" style={{
            backgroundColor: '#f8d7da',
            color: '#721c24',
            padding: '10px 15px',
            marginBottom: '15px',
            borderRadius: '4px',
            display: 'flex',
            justifyContent: 'space-between',
            alignItems: 'center'
          }}>
            <span>{apiError}</span>
            <button
              onClick={() => setApiError(null)}
              style={{
                background: 'none',
                border: 'none',
                cursor: 'pointer',
                color: '#721c24'
              }}
              aria-label="Close error message"
            >
              <FontAwesomeIcon icon={faTimes} />
            </button>
          </div>
        )}

        <div className="tabs-container">
          <div
            className={`tab ${activeTab === 'personal' ? 'active' : ''} ${!tabCompletionStatus.personal && attemptedSubmit ? 'incomplete' : ''} ${tabCompletionStatus.personal ? 'complete' : ''}`}
            onClick={() => setActiveTab('personal')}
            style={{ cursor: 'pointer' }}
          >
            <FontAwesomeIcon icon={faUser} className="tab-icon" />
            <span>1. Personal Details</span>
            {!tabCompletionStatus.personal && attemptedSubmit && (
              <FontAwesomeIcon icon={faExclamationCircle} className="tab-warning-icon" />
            )}
            {tabCompletionStatus.personal && (
              <FontAwesomeIcon icon={faCheckCircle} className="tab-complete-icon" />
            )}
          </div>
          <div
            className={`tab ${activeTab === 'health' ? 'active' : ''} ${!tabCompletionStatus.health && attemptedSubmit ? 'incomplete' : ''} ${tabCompletionStatus.health ? 'complete' : ''}`}
            onClick={() => {
              // Only allow navigation to health tab if personal tab is complete or we're already past it
              if (tabCompletionStatus.personal || activeTab === 'health' || activeTab === 'recheck') {
                setActiveTab('health');
              }
            }}
            style={{ cursor: tabCompletionStatus.personal || activeTab === 'health' || activeTab === 'recheck' ? 'pointer' : 'not-allowed' }}
          >
            <FontAwesomeIcon icon={faNotesMedical} className="tab-icon" />
            <span>2. Health Records</span>
            {!tabCompletionStatus.health && attemptedSubmit && (
              <FontAwesomeIcon icon={faExclamationCircle} className="tab-warning-icon" />
            )}
            {tabCompletionStatus.health && (
              <FontAwesomeIcon icon={faCheckCircle} className="tab-complete-icon" />
            )}
          </div>
          <div
            className={`tab ${activeTab === 'recheck' ? 'active' : ''} ${!tabCompletionStatus.recheck && attemptedSubmit ? 'incomplete' : ''} ${tabCompletionStatus.recheck ? 'complete' : ''}`}
            onClick={() => {
              // Only allow navigation to recheck tab if health tab is complete or we're already on it
              if ((tabCompletionStatus.personal && tabCompletionStatus.health) || activeTab === 'recheck') {
                setActiveTab('recheck');
              }
            }}
            style={{ cursor: (tabCompletionStatus.personal && tabCompletionStatus.health) || activeTab === 'recheck' ? 'pointer' : 'not-allowed' }}
          >
            <FontAwesomeIcon icon={faCheckCircle} className="tab-icon" />
            <span>3. Recheck MRN & NHS</span>
            {!tabCompletionStatus.recheck && attemptedSubmit && (
              <FontAwesomeIcon icon={faExclamationCircle} className="tab-warning-icon" />
            )}
            {tabCompletionStatus.recheck && (
              <FontAwesomeIcon icon={faCheckCircle} className="tab-complete-icon" />
            )}
          </div>
        </div>

        <div className="tab-content">
          {activeTab === 'personal' && (
            <form className="form-grid">
              <div className="form-column">
                <div className={`form-group design-1 ${isFieldInvalid('first_name', 'personal') ? 'invalid' : ''}`}>
                  <label htmlFor="firstName">First Name</label>
                  <input
                    type="text"
                    id="firstName"
                    name="firstName"
                    placeholder="Enter first name"
                    value={patientState.first_name}
                    onChange={(e) => patientState.setField('first_name', e.target.value)}
                    required
                  />
                  {isFieldInvalid('first_name', 'personal') && (
                    <div className="field-error">First name is required</div>
                  )}
                </div>
                <div className={`form-group design-1 ${isFieldInvalid('last_name', 'personal') ? 'invalid' : ''}`}>
                  <label htmlFor="lastName">Last Name</label>
                  <input
                    type="text"
                    id="lastName"
                    name="lastName"
                    placeholder="Enter last name"
                    value={patientState.last_name}
                    onChange={(e) => patientState.setField('last_name', e.target.value)}
                    required
                  />
                  {isFieldInvalid('last_name', 'personal') && (
                    <div className="field-error">Last name is required</div>
                  )}
                </div>
                <div className={`form-group ${isFieldInvalid('date_of_birth', 'personal') ? 'invalid' : ''}`}>
                  <label htmlFor="dateOfBirth">Date of Birth</label>
                  <input
                    type="date"
                    id="dateOfBirth"
                    name="dateOfBirth"
                    value={patientState.date_of_birth}
                    onChange={(e) => patientState.setField('date_of_birth', e.target.value)}
                    required
                  />
                  {isFieldInvalid('date_of_birth', 'personal') && (
                    <div className="field-error">Date of birth is required</div>
                  )}
                </div>
                <div className={` ${isFieldInvalid('gender', 'personal') ? 'invalid' : ''}`}>
                <NurtifyText label="Gender*" />
                    <NurtifySelect
                      options={genderOptions}
                      value={patientState.gender}
                      onChange={(e) => patientState.setField('gender', e.target.value)}
                    />
                  {isFieldInvalid('gender', 'personal') && (
                    <div className="field-error">Gender is required</div>
                  )}
                </div>
              </div>
              <div className="form-column">
                {/* here */}
                <div className={`form-group ${isFieldInvalid('email', 'personal') ? 'invalid' : ''}`}>
                  <label htmlFor="email">Email</label>
                  <input
                    type="email"
                    id="email"
                    name="email"
                    placeholder="Enter email"
                    value={patientState.email}
                    onChange={(e) => patientState.setField('email', e.target.value)}
                    required
                  />
                  {isFieldInvalid('email', 'personal') && (
                    <div className="field-error">
                      {!patientState.email?.trim() ? "Email is required" : "Please enter a valid email address"}
                    </div>
                  )}
                </div>
                <div className={`form-group ${isFieldInvalid('phone_number', 'personal') ? 'invalid' : ''}`}>
                  <label htmlFor="phone">Phone Number</label>
                  <input
                    type="tel"
                    id="phone"
                    name="phone"
                    placeholder="Enter phone number"
                    value={patientState.phone_number}
                    onChange={(e) => patientState.setField('phone_number', e.target.value)}
                    required
                  />
                  {isFieldInvalid('phone_number', 'personal') && (
                    <div className="field-error">
                      {!patientState.phone_number?.trim()
                        ? "Phone number is required"
                        : "Phone number can only contain digits, +, (), spaces, and hyphens"}
                    </div>
                  )}
                </div>
                <div className={`form-group relative ${isFieldInvalid('postcode', 'personal') ? 'invalid' : ''}`}>
         <label htmlFor="postcode">Postcode</label>
         <input
          type="text"
          id="postcode"
          name="postcode"
          placeholder="Enter Postcode here"
          value={patientState.postcode}
          onChange={handlePostcodeInputChange}
          onFocus={() => patientState.postcode.length > 1 && setShowPostcodeAutocomplete(true)}
          required
         />
         {isFieldInvalid('postcode', 'personal') && (
          <div className="field-error">Postcode is required</div>
         )}

         {isPostcodeApiLoading && (
          <div className="absolute z-20 w-full mt-1 p-2 bg-white rounded-xl shadow-lg flex items-center justify-center text-blue-600 text-sm">
           <Loader2 className="animate-spin mr-2" size={16} />
           <span>Searching...</span>
          </div>
         )}

         <AnimatePresence>
          {showPostcodeAutocomplete && autocompleteSuggestions.length > 0 && (
           <motion.ul
            ref={autocompletePostcodeRef}
            initial={{ opacity: 0, y: -10 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -10 }}
            transition={{ duration: 0.2 }}
            style={{ listStyleType: 'none', paddingLeft: 0, margin: 0 }}
            className="absolute z-10 w-full mt-1 bg-white border border-gray-200 rounded-xl shadow-lg max-h-40 overflow-y-auto"
           >
            {autocompleteSuggestions.map((suggestion, index) => (
             <motion.li
              key={index}
              whileHover={{ backgroundColor: '#f0f4f8' }}
              onClick={() => handlePostcodeSuggestionSelect(suggestion)}
              className="cursor-pointer p-2 border-b border-gray-100 last:border-b-0 text-gray-800 text-sm"
             >
              <Search className="inline-block mr-2 text-gray-400" size={14} /> {String(suggestion)}
             </motion.li>
            ))}
           </motion.ul>
          )}
         </AnimatePresence>

         {postcodeApiError && (
          <motion.div
           initial={{ opacity: 0, y: -10 }}
           animate={{ opacity: 1, y: 0 }}
           className="mt-2 flex items-center p-2 bg-red-100 text-red-700 rounded-xl text-xs"
          >
           <Info className="mr-1" size={14} />
           <span>{postcodeApiError}</span>
          </motion.div>
         )}
        </div>
                <div className={`form-group ${isFieldInvalid('address', 'personal') ? 'invalid' : ''}`}>
                  <label htmlFor="address">Address</label>
                  <input
                    type="text"
                    id="address"
                    name="address"
                    placeholder="Enter address"
                    value={patientState.address}
                    onChange={(e) => patientState.setField('address', e.target.value)}
                    required
                  />
                  {isFieldInvalid('address', 'personal') && (
                    <div className="field-error">Address is required</div>
                  )}
                </div>
              </div>
            </form>
          )}

          {activeTab === 'health' && (
            <>
              <form className="form-grid">
                <div className="form-column">
                  <div className={`form-group design-1 ${isFieldInvalid('nhs_number', 'health') ? 'invalid' : ''}`}>
                    <label htmlFor="nhsNumber">NHS number</label>
                    <input
                      type="text"
                      id="nhsNumber"
                      name="nhsNumber"
                      placeholder="Enter NHS number"
                      value={patientState.nhs_number}
                      onChange={(e) => patientState.setField('nhs_number', e.target.value)}
                      required
                    />
                    {isFieldInvalid('nhs_number', 'health') && (
                      <div className="field-error">
                        {!patientState.nhs_number?.trim()
                          ? "NHS number is required"
                          : "NHS number must be exactly 10 digits"}
                      </div>
                    )}
                  </div>
                  <div className={`form-group ${isFieldInvalid('medical_record_number', 'health') ? 'invalid' : ''}`}>
                    <label htmlFor="medicalRecordNumber">Medical Record Number</label>
                    <input
                      type="text"
                      id="medicalRecordNumber"
                      name="medicalRecordNumber"
                      placeholder="Enter MRN"
                      value={patientState.medical_record_number}
                      onChange={(e) => patientState.setField('medical_record_number', e.target.value)}
                      required
                    />
                    {isFieldInvalid('medical_record_number', 'health') && (
                      <div className="field-error">Medical record number is required</div>
                    )}
                  </div>
                </div>
                <div className="form-column">
                  <div className={`form-group ${isFieldInvalid('hospital', 'health') ? 'invalid' : ''}`}>
                    <label htmlFor="hospitalName">Hospital Name</label>
                    <input
                      type="text"
                      id="hospitalName"
                      name="hospitalName"
                      value={hospitalname}
                      disabled
                      required
                    />
                    {isFieldInvalid('hospital', 'health') && (
                      <div className="field-error">Hospital name is required</div>
                    )}
                  </div>
                  <div className={`form-group ${isFieldInvalid('department', 'health') ? 'invalid' : ''}`}>
                    <label htmlFor="department">Department</label>
                    {department_uuid ? (
                      // User has a department, show disabled input
                      <input
                        type="text"
                        id="department"
                        name="department"
                        value={departmentname}
                        disabled
                        required
                      />
                    ) : (
                      // User doesn't have department but has hospital, show dropdown
                      <NurtifySelect
                        name="department"
                        value={selectedDepartmentUuid}
                        onChange={(e) => setSelectedDepartmentUuid(e.target.value)}
                        options={[
                          { value: "", label: "Select a department" },
                          ...(Departments?.map(dept => ({
                            value: dept.uuid || "",
                            label: dept.name || ""
                          })) || [])
                        ]}
                      />
                    )}
                    {isFieldInvalid('department', 'health') && (
                      <div className="field-error">Department is required</div>
                    )}
                  </div>
                  <div className="mb-4">
                    <NurtifyText label="Ethnic Background" />
                    <NurtifySelect
                      name="ethnicBackground"
                      value={patientState.ethnic_background || ""}
                      onChange={(e) => patientState.setField('ethnic_background', e.target.value)}
                      options={[
                        { value: "", label: "Select ethnic background" },
                        { value: "White_British", label: "White (English/Welsh/Scottish/Northern Irish/British)" },
                        { value: "White_Irish", label: "White (Irish)" },
                        { value: "White_Gypsy", label: "White (Gypsy or Irish Traveller)" },
                        { value: "White_Other", label: "White (Other)" },
                        { value: "Asian_Indian", label: "Asian/Asian British (Indian)" },
                        { value: "Asian_Pakistani", label: "Asian/Asian British (Pakistani)" },
                        { value: "Asian_Bangladeshi", label: "Asian/Asian British (Bangladeshi)" },
                        { value: "Asian_Chinese", label: "Asian/Asian British (Chinese)" },
                        { value: "Asian_Other", label: "Asian/Asian British (Other)" },
                        { value: "Black_African", label: "Black/Black British (African)" },
                        { value: "Black_Caribbean", label: "Black/Black British (Caribbean)" },
                        { value: "Black_Other", label: "Black/Black British (Other)" },
                        { value: "Mixed", label: "Mixed/Multiple Ethnic Groups" },
                        { value: "Other", label: "Other Ethnic Group" },
                        { value: "Prefer_Not_to_Say", label: "Prefer Not to Say" }
                      ]}
                    />
                  </div>
                </div>
              </form>

              <form className="form-grid">
                <div className="form-column wide-column">
                  <div className="form-group">
                    <label htmlFor="allergyCheck">Does the patient have a history of allergies?</label>
                    <NurtifyToggle
                      name="allergyCheck"
                      value={allergicPatient}
                      onChange={(value) => setAllergicPatient(value)}
                      labels={['No', 'Yes']}
                    />
                  </div>
                </div>
              </form>

              {allergicPatient === "true" && (
                <div id="division-18-a" className="mt-4">
                  <div className="row">
                    <div className="col-md-8">
                      <h4 className="headinqQuestion mb-3">
                        Please Specify what allergy the patient has:
                      </h4>

                      <NurtifyInput
                        placeholder="Type Allegy Name (Select or Press Enter)"
                        type="text"
                        value={medicationSearchInput}
                        onChange={handleMedicationsSearchInputChange}
                        onKeyDown={handleMedicationSearchKeyPress}
                        className="mb-3"
                      />

                      {medicationsSelectedOptions.length > 0 && (
                        <div className="d-flex flex-wrap gap-2 mb-3">
                          {medicationsSelectedOptions.map((opt, index) => (
                            <div
                              key={index}
                              className="badge bg-info text-dark d-flex align-items-center p-2"
                            >
                              {opt.name}
                              <button
                                className="btn btn-sm ms-2 p-0"
                                onClick={() => handleMedicationsRemoveOption(index)}
                                aria-label="Remove allergy"
                              >
                                <FontAwesomeIcon icon={faTrash} size="xs" />
                              </button>
                            </div>
                          ))}
                        </div>
                      )}

                      {matchedMedicationsOptions.length > 0 && (
                        <div className="list-group mb-3">
                          {matchedMedicationsOptions.map((opt, index) => (
                            <button
                              key={index}
                              type="button"
                              className="list-group-item list-group-item-action"
                              onClick={() => handleMedicationOptionClick(opt)}
                            >
                              {opt}
                            </button>
                          ))}
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              )}
            </>
          )}

          {activeTab === 'recheck' && (
            <form className="form-grid">
              <div className="form-column wide-column">
                <h3 className="recheck-title">Please re-enter the following information to verify:</h3>

                <div className={`form-group ${isFieldInvalid('nhs_number_confirmed', 'recheck') ? 'invalid' : ''}`}>
                  <label htmlFor="nhsNumberConfirm">NHS Number</label>
                  <input
                    type="text"
                    id="nhsNumberConfirm"
                    name="nhsNumberConfirm"
                    placeholder="Re-enter NHS number"
                    value={nhsNumberConfirm}
                    onChange={(e) => setNhsNumberConfirm(e.target.value)}
                    required
                  />
                  {isFieldInvalid('nhs_number_confirmed', 'recheck') && (
                    <div className="field-error">
                      {!nhsNumberConfirm?.trim()
                        ? "NHS number is required"
                        : !isValidNHSNumber(nhsNumberConfirm)
                        ? "NHS number must be exactly 10 digits"
                        : "NHS number does not match the one entered previously"}
                    </div>
                  )}
                </div>

                <div className={`form-group ${isFieldInvalid('medical_record_number_confirmed', 'recheck') ? 'invalid' : ''}`}>
                  <label htmlFor="mrnConfirm">Medical Record Number</label>
                  <input
                    type="text"
                    id="mrnConfirm"
                    name="mrnConfirm"
                    placeholder="Re-enter Medical Record Number"
                    value={mrnConfirm}
                    onChange={(e) => setMrnConfirm(e.target.value)}
                    required
                  />
                  {isFieldInvalid('medical_record_number_confirmed', 'recheck') && (
                    <div className="field-error">
                      {!mrnConfirm?.trim()
                        ? "Medical Record Number is required"
                        : "Medical Record Number does not match the one entered previously"}
                    </div>
                  )}
                </div>

                <div className="confirmation-message">
                  <p>Please verify that the NHS Number and Medical Record Number match what you entered on the previous page.</p>
                </div>
              </div>
            </form>
          )}
        </div>

        {attemptedSubmit && !isFormValid && (
          <div className="validation-error-message">
            Please fill in all required fields before proceeding
          </div>
        )}

        <div className="button-container">
          <button
            type="button"
            className="cancel-button"
            onClick={onClose}
          >
            Cancel
          </button>
          <button
            type="button"
            className="submit-button"
            onClick={handleNext}
          >
            {isLastTab ? (isEditing ? 'Update' : 'Submit') : (
              <>
                Next <FontAwesomeIcon icon={faArrowRight} />
              </>
            )}
          </button>
        </div>
      </div>
    </div>
  );
};

export default AddPatientFormModal;

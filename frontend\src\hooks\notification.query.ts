import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import type {
  SystemNotification,
  NotificationType,
  NotificationPreferences,
  PushToken,
  NotificationTemplate,
  NotificationPaginatedResponse,
  UnreadCountResponse,
  MarkAllReadResponse,
  SendNotificationRequest,
  SendBatchNotificationRequest,
  RegisterDeviceRequest,
  UnregisterDeviceRequest,
  UpdatePreferencesRequest
} from "@/services/api/notification.types";
import {
  getUserNotifications,
  getRecentNotifications,
  getUnreadCount,
  getNotificationsByType,
  markNotificationAsRead,
  markAllNotificationsAsRead,
  getNotificationTypes,
  getUserPreferences,
  updateUserPreferences,
  updateSpecificPreferences,
  getUserPushTokens,
  registerDevice,
  unregisterDevice,
  getNotificationTemplates,
  sendNotificationToUser,
  sendBatchNotification
} from "@/services/api/notification.service";

// Query Keys
export const NOTIFICATION_KEYS = {
  GET_ALL: "notification/getAll",
  GET_RECENT: "notification/getRecent",
  GET_UNREAD_COUNT: "notification/getUnreadCount",
  GET_BY_TYPE: "notification/getByType",
  GET_TYPES: "notification/getTypes",
  GET_PREFERENCES: "notification/getPreferences",
  GET_PUSH_TOKENS: "notification/getPushTokens",
  GET_TEMPLATES: "notification/getTemplates",
} as const;

// Notification Queries
export const useNotificationsQuery = (page?: number) => {
  return useQuery<NotificationPaginatedResponse<SystemNotification>, Error>({
    queryKey: [NOTIFICATION_KEYS.GET_ALL, page],
    queryFn: () => getUserNotifications(page),
    refetchInterval: 30000, // Refetch every 30 seconds
  });
};

export const useRecentNotificationsQuery = () => {
  return useQuery<SystemNotification[], Error>({
    queryKey: [NOTIFICATION_KEYS.GET_RECENT],
    queryFn: getRecentNotifications,
    refetchInterval: 30000, // Refetch every 30 seconds
  });
};

export const useUnreadCountQuery = () => {
  return useQuery<UnreadCountResponse, Error>({
    queryKey: [NOTIFICATION_KEYS.GET_UNREAD_COUNT],
    queryFn: getUnreadCount,
    refetchInterval: 30000, // Refetch every 30 seconds
  });
};

export const useNotificationsByTypeQuery = (type: string) => {
  return useQuery<NotificationPaginatedResponse<SystemNotification>, Error>({
    queryKey: [NOTIFICATION_KEYS.GET_BY_TYPE, type],
    queryFn: () => getNotificationsByType(type),
    enabled: !!type,
  });
};

export const useNotificationTypesQuery = () => {
  return useQuery<NotificationType[], Error>({
    queryKey: [NOTIFICATION_KEYS.GET_TYPES],
    queryFn: getNotificationTypes,
  });
};

export const useUserPreferencesQuery = () => {
  return useQuery<NotificationPreferences, Error>({
    queryKey: [NOTIFICATION_KEYS.GET_PREFERENCES],
    queryFn: getUserPreferences,
  });
};

export const useUserPushTokensQuery = () => {
  return useQuery<PushToken[], Error>({
    queryKey: [NOTIFICATION_KEYS.GET_PUSH_TOKENS],
    queryFn: getUserPushTokens,
  });
};

export const useNotificationTemplatesQuery = () => {
  return useQuery<NotificationTemplate[], Error>({
    queryKey: [NOTIFICATION_KEYS.GET_TEMPLATES],
    queryFn: getNotificationTemplates,
  });
};

// Notification Mutations
export const useMarkNotificationAsReadMutation = () => {
  const queryClient = useQueryClient();
  return useMutation<{ status: string }, Error, string>({
    mutationFn: markNotificationAsRead,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: [NOTIFICATION_KEYS.GET_ALL] });
      queryClient.invalidateQueries({ queryKey: [NOTIFICATION_KEYS.GET_RECENT] });
      queryClient.invalidateQueries({ queryKey: [NOTIFICATION_KEYS.GET_UNREAD_COUNT] });
    },
  });
};

export const useMarkAllNotificationsAsReadMutation = () => {
  const queryClient = useQueryClient();
  return useMutation<MarkAllReadResponse, Error, void>({
    mutationFn: markAllNotificationsAsRead,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: [NOTIFICATION_KEYS.GET_ALL] });
      queryClient.invalidateQueries({ queryKey: [NOTIFICATION_KEYS.GET_RECENT] });
      queryClient.invalidateQueries({ queryKey: [NOTIFICATION_KEYS.GET_UNREAD_COUNT] });
    },
  });
};

export const useUpdateUserPreferencesMutation = () => {
  const queryClient = useQueryClient();
  return useMutation<NotificationPreferences, Error, NotificationPreferences>({
    mutationFn: updateUserPreferences,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: [NOTIFICATION_KEYS.GET_PREFERENCES] });
    },
  });
};

export const useUpdateSpecificPreferencesMutation = () => {
  const queryClient = useQueryClient();
  return useMutation<NotificationPreferences, Error, UpdatePreferencesRequest>({
    mutationFn: updateSpecificPreferences,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: [NOTIFICATION_KEYS.GET_PREFERENCES] });
    },
  });
};

export const useRegisterDeviceMutation = () => {
  const queryClient = useQueryClient();
  return useMutation<PushToken, Error, RegisterDeviceRequest>({
    mutationFn: registerDevice,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: [NOTIFICATION_KEYS.GET_PUSH_TOKENS] });
    },
  });
};

export const useUnregisterDeviceMutation = () => {
  const queryClient = useQueryClient();
  return useMutation<{ status: string }, Error, UnregisterDeviceRequest>({
    mutationFn: unregisterDevice,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: [NOTIFICATION_KEYS.GET_PUSH_TOKENS] });
    },
  });
};

// Admin Mutations
export const useSendNotificationMutation = () => {
  return useMutation<SystemNotification, Error, SendNotificationRequest>({
    mutationFn: sendNotificationToUser,
  });
};

export const useSendBatchNotificationMutation = () => {
  return useMutation<{ status: string }, Error, SendBatchNotificationRequest>({
    mutationFn: sendBatchNotification,
  });
};

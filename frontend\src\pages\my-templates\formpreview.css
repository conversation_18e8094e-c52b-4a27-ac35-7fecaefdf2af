.form-preview-container {
  max-width: 900px;
  margin: 30px auto;
  background-color: #fff;
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  padding: 30px;
}

.form-preview-header {
  margin-bottom: 30px;
  border-bottom: 1px solid #e0e0e0;
  padding-bottom: 20px;
}

.form-preview-back-btn {
  display: inline-flex;
  align-items: center;
  color: #071952;
  font-weight: 500;
  text-decoration: none;
  margin-bottom: 15px;
  transition: color 0.2s;
}

.form-preview-back-btn:hover {
  color: #37B7C3;
}

.form-preview-back-btn svg {
  margin-right: 8px;
}

.form-preview-title {
  font-size: 28px;
  font-weight: 600;
  color: #071952;
  margin-bottom: 15px;
}

.form-preview-description {
  font-size: 16px;
  color: #555;
  margin-bottom: 20px;
  line-height: 1.5;
}

.form-preview-details {
  display: flex;
  flex-wrap: wrap;
  gap: 15px;
  margin-bottom: 10px;
}

.form-preview-detail {
  display: flex;
  align-items: center;
  font-size: 14px;
  color: #666;
}

.form-preview-detail svg {
  margin-right: 8px;
  color: #37B7C3;
}

.form-preview-tags {
  display: flex;
  align-items: flex-start;
  width: 100%;
}

.form-preview-tags svg {
  margin-right: 8px;
  color: #37B7C3;
  margin-top: 6px;
}

.tags-list {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.form-preview-tag {
  background: rgba(55, 183, 195, 0.12);
  color: #37B7C3;
  border-radius: 20px;
  padding: 4px 10px;
  font-size: 12px;
  font-weight: 500;
}

.form-preview-content {
  margin-bottom: 30px;
}

.form-preview-sections-title {
  font-size: 20px;
  font-weight: 600;
  color: #071952;
  margin-bottom: 20px;
  padding-bottom: 10px;
  border-bottom: 2px solid #37B7C3;
}

.form-preview-section {
  background-color: #f9f9f9;
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 25px;
  border-left: 3px solid #37B7C3;
  animation: fadeIn 0.5s ease-in-out;
}

.section-title {
  font-size: 18px;
  font-weight: 600;
  color: #37B7C3;
  margin-bottom: 8px;
}

.section-description {
  font-size: 14px;
  color: #666;
  margin-bottom: 15px;
}

.form-preview-questions {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.form-preview-question {
  background-color: #fff;
  border-radius: 8px;
  padding: 15px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  transition: transform 0.2s, box-shadow 0.2s;
  animation: slideIn 0.3s ease-in-out;
}

.form-preview-question:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.question-header {
  display: flex;
  align-items: flex-start;
  margin-bottom: 15px;
}

.question-icon {
  background-color: #e0f7f9;
  color: #37B7C3;
  width: 36px;
  height: 36px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 15px;
  flex-shrink: 0;
}

.question-content {
  flex: 1;
}

.question-name {
  font-size: 16px;
  font-weight: 500;
  color: #333;
  margin-bottom: 5px;
  display: flex;
  align-items: center;
  gap: 5px;
}

.question-required {
  color: #E53935;
  margin-left: 4px;
}

.question-type {
  display: flex;
  gap: 8px;
}

.question-type .badge {
  background-color: #f0f0f0;
  color: #666;
  border-radius: 4px;
  padding: 3px 8px;
  font-size: 12px;
  font-weight: normal;
}

.badge-non-clinical {
  background-color: #e1f5fe !important;
  color: #0288d1 !important;
}

.question-preview {
  border-top: 1px solid #eee;
  padding-top: 15px;
}

.question-preview-content {
  max-width: 600px;
  margin: 0 auto;
}

.question-preview-option {
  margin-bottom: 8px;
}

.question-preview-toggle {
  display: flex;
  justify-content: flex-start;
  pointer-events: none;
  opacity: 0.8;
}

.question-preview-no-options {
  padding: 10px;
  text-align: center;
  color: #888;
  font-style: italic;
  background-color: #f9f9f9;
  border-radius: 6px;
}

.preview-multi-select {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  background-color: #f9f9f9;
  border-radius: 8px;
  padding: 12px;
}

.preview-selected-option {
  background: rgba(55, 183, 195, 0.1);
  color: #37B7C3;
  border-radius: 16px;
  padding: 5px 12px;
  font-size: 14px;
}

.range-preview-container {
  padding: 10px;
}

.range-values {
  display: flex;
  justify-content: space-between;
  margin-top: 10px;
  font-size: 14px;
  color: #666;
}

.table-preview {
  width: 100%;
  overflow-x: auto;
}

.table-preview table {
  width: 100%;
  border-collapse: collapse;
  border: 1px solid #eee;
}

.table-preview th, .table-preview td {
  padding: 10px;
  text-align: left;
  border: 1px solid #eee;
}

.table-preview th {
  background-color: #f5f5f5;
  font-weight: 500;
}

.file-attachment-preview {
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
  gap: 10px;
  padding: 20px;
  border: 2px dashed #ccc;
  border-radius: 8px;
  background-color: #f9f9f9;
  color: #888;
}

.file-attachment-preview svg {
  color: #37B7C3;
}

.signature-preview {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.signature-placeholder {
  border: 2px dashed #ccc;
  border-radius: 8px;
  height: 120px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: #888;
  background-color: #f9f9f9;
}

.signature-fields {
  display: flex;
  gap: 10px;
}

.signature-fields > * {
  flex: 1;
}

.multiple-text-boxes-preview {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.text-box-item {
  display: flex;
  flex-direction: column;
  gap: 5px;
}

.text-box-item label {
  font-size: 14px;
  color: #333;
  font-weight: 500;
}

.expression-preview {
  background-color: #f5f5f5;
  padding: 12px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  gap: 10px;
  color: #666;
  font-family: 'Courier New', monospace;
}

.unknown-question-type {
  color: #E53935;
  font-style: italic;
}

.no-questions, .no-sections {
  text-align: center;
  padding: 20px;
  color: #888;
  font-style: italic;
}

.form-preview-actions {
  display: flex;
  gap: 15px;
  justify-content: flex-end;
  margin-top: 20px;
  padding-top: 20px;
  border-top: 1px solid #e0e0e0;
}

.btn-nurtify,
.btn-nurtify-lighter {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 10px 20px;
  border-radius: 8px;
  font-weight: 500;
  transition: all 0.2s;
  text-decoration: none;
  cursor: pointer;
  border: none;
}

.btn-nurtify {
  background-color: #37B7C3;
  color: white;
}

.btn-nurtify:hover {
  background-color: #2aa2ae;
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(55, 183, 195, 0.3);
}

.btn-nurtify-lighter {
  background-color: #e0f7f9;
  color: #37B7C3;
}

.btn-nurtify-lighter:hover {
  background-color: #c7ebef;
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(55, 183, 195, 0.2);
}

.form-preview-loading,
.form-preview-error {
  padding: 60px;
  text-align: center;
}

.form-preview-error {
  color: #E53935;
}

/* Quick Actions Toolbar */
.quick-actions-toolbar {
  display: flex;
  gap: 10px;
  margin-top: 15px;
  padding-top: 10px;
  border-top: 1px dashed #e0e0e0;
}

.quick-action-button {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 36px;
  height: 36px;
  border-radius: 50%;
  background-color: #f5f5f5;
  border: none;
  cursor: pointer;
  color: #37B7C3;
  transition: all 0.2s ease;
}

.quick-action-button:hover {
  background-color: #e0f7f9;
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(55, 183, 195, 0.2);
}

.quick-action-button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* Tooltip */
.tooltip-container {
  position: relative;
}

.tooltip {
  position: absolute;
  bottom: -30px;
  left: 50%;
  transform: translateX(-50%);
  background-color: #333;
  color: white;
  padding: 5px 8px;
  border-radius: 4px;
  font-size: 12px;
  white-space: nowrap;
  opacity: 0;
  visibility: hidden;
  transition: opacity 0.2s, visibility 0.2s;
  z-index: 100;
}

.tooltip-container:hover .tooltip {
  opacity: 1;
  visibility: visible;
}

/* Loading Spinner */
.loading-spinner {
  display: inline-block;
  width: 18px;
  height: 18px;
  border: 2px solid rgba(55, 183, 195, 0.3);
  border-radius: 50%;
  border-top-color: #37B7C3;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  to { transform: rotate(360deg); }
}

/* Share Modal */
.share-modal-backdrop {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  opacity: 0;
  visibility: hidden;
  transition: opacity 0.3s, visibility 0.3s;
}

.share-modal-backdrop.active {
  opacity: 1;
  visibility: visible;
}

.share-modal {
  background-color: white;
  border-radius: 8px;
  padding: 25px;
  width: 90%;
  max-width: 500px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
  transform: translateY(20px);
  transition: transform 0.3s;
}

.share-modal-backdrop.active .share-modal {
  transform: translateY(0);
}

.share-modal h3 {
  margin-top: 0;
  margin-bottom: 20px;
  color: #071952;
}

.share-link-container {
  display: flex;
  margin-bottom: 20px;
}

.share-link-container input {
  flex: 1;
  padding: 10px 12px;
  border: 1px solid #ddd;
  border-radius: 6px 0 0 6px;
  font-size: 14px;
}

.copy-link-btn {
  background-color: #37B7C3;
  color: white;
  border: none;
  padding: 10px 15px;
  border-radius: 0 6px 6px 0;
  cursor: pointer;
  transition: background-color 0.2s;
}

.copy-link-btn.success {
  background-color: #4CAF50;
}

.close-modal-btn {
  width: 100%;
  padding: 10px;
  background-color: #f5f5f5;
  border: none;
  border-radius: 6px;
  color: #333;
  cursor: pointer;
  transition: background-color 0.2s;
}

.close-modal-btn:hover {
  background-color: #e0e0e0;
}

/* Improved animations for form elements */
.form-preview-section {
  animation: fadeIn 0.5s ease-in-out;
}

.form-preview-question {
  animation: slideIn 0.3s ease-in-out;
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes slideIn {
  from { 
    opacity: 0;
    transform: translateY(10px);
  }
  to { 
    opacity: 1;
    transform: translateY(0);
  }
}

/* Error message styling */
.question-preview-error {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 15px;
  background-color: #ffebee;
  border-radius: 6px;
  color: #E53935;
}

.question-preview-error svg {
  margin-bottom: 8px;
}

.error-actions {
  display: flex;
  justify-content: center;
  gap: 15px;
}

/* Accessibility improvements */
:focus {
  outline: 2px solid #37B7C3;
  outline-offset: 2px;
}

/* High contrast mode */
@media (forced-colors: active) {
  .form-preview-tag,
  .badge,
  .form-preview-section {
    forced-color-adjust: none;
    border: 1px solid ButtonText;
  }
}

/* Print styles */
@media print {
  header, footer, .form-preview-back-btn, .form-preview-actions, .quick-actions-toolbar {
    display: none !important;
  }
  
  body, .main-content, .content-wrapper, .dashboard__content {
    background-color: white !important;
  }
  
  .form-preview-container {
    box-shadow: none !important;
    margin: 0 !important;
    padding: 0 !important;
    max-width: 100% !important;
  }
  
  .question-preview-content * {
    page-break-inside: avoid;
  }
  
  .form-preview-section {
    break-inside: avoid;
  }
}

/* Enhance existing responsive styles */
@media (max-width: 768px) {
  .form-preview-container {
    margin: 15px;
    padding: 20px;
  }
  
  .form-preview-details {
    flex-direction: column;
    gap: 10px;
  }
  
  .form-preview-actions {
    flex-direction: column;
  }
  
  .btn-nurtify,
  .btn-nurtify-lighter {
    width: 100%;
  }
  
  .signature-fields {
    flex-direction: column;
  }
  
  .quick-actions-toolbar {
    justify-content: center;
    margin-top: 20px;
  }
  
  .form-preview-section {
    padding: 15px;
  }
  
  .question-icon {
    width: 30px;
    height: 30px;
  }
}

/* Accept/Reject Button Styles */
.action-buttons {
  display: flex;
  gap: 15px;
  align-items: center;
}

.btn-accept,
.btn-reject {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  padding: 12px 24px;
  border-radius: 8px;
  font-weight: 500;
  font-size: 14px;
  border: none;
  cursor: pointer;
  transition: all 0.2s ease;
  min-width: 140px;
}

.btn-accept {
  background-color: #4CAF50;
  color: white;
}

.btn-accept:hover:not(:disabled) {
  background-color: #45a049;
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(76, 175, 80, 0.3);
}

.btn-accept:disabled {
  background-color: #a5d6a7;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

.btn-reject {
  background-color: #f44336;
  color: white;
}

.btn-reject:hover:not(:disabled) {
  background-color: #da190b;
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(244, 67, 54, 0.3);
}

.btn-reject:disabled {
  background-color: #ef9a9a;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

/* Confirmation Dialog Styles */
.confirmation-dialog-backdrop {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  animation: fadeIn 0.3s ease-out;
}

.confirmation-dialog {
  background-color: white;
  border-radius: 12px;
  padding: 30px;
  max-width: 400px;
  width: 90%;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.15);
  animation: slideIn 0.3s ease-out;
}

.confirmation-dialog h3 {
  margin: 0 0 15px 0;
  color: #071952;
  font-size: 20px;
  font-weight: 600;
}

.confirmation-dialog p {
  margin: 0 0 25px 0;
  color: #666;
  line-height: 1.5;
  font-size: 14px;
}

.confirmation-dialog-actions {
  display: flex;
  gap: 12px;
  justify-content: flex-end;
}

.btn-cancel {
  padding: 10px 20px;
  border: 1px solid #ddd;
  background-color: #f5f5f5;
  color: #333;
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  transition: all 0.2s ease;
}

.btn-cancel:hover {
  background-color: #e0e0e0;
  border-color: #ccc;
}

.btn-confirm {
  padding: 10px 20px;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  transition: all 0.2s ease;
  color: white;
}

.btn-confirm-accept {
  background-color: #4CAF50;
}

.btn-confirm-accept:hover {
  background-color: #45a049;
}

.btn-confirm-reject {
  background-color: #f44336;
}

.btn-confirm-reject:hover {
  background-color: #da190b;
}

/* Responsive adjustments for action buttons */
@media (max-width: 768px) {
  .form-preview-actions {
    flex-direction: column;
    gap: 15px;
  }
  
  .action-buttons {
    flex-direction: column;
    width: 100%;
  }
  
  .btn-accept,
  .btn-reject {
    width: 100%;
    justify-content: center;
  }
  
  .confirmation-dialog {
    margin: 20px;
    padding: 25px;
  }
  
  .confirmation-dialog-actions {
    flex-direction: column;
  }
  
  .btn-cancel,
  .btn-confirm {
    width: 100%;
    text-align: center;
  }
}
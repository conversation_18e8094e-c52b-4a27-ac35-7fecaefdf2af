import { useState, useEffect } from "react";
import poctImage from "./static/images/added/poct.png";
import useHolisticFormStore from "@/store/holisticFormState";
import NurtifyRadio from "@/components/NurtifyRadio";
import NurtifyText from "@/components/NurtifyText";
import NurtifyMultiInput from "@/components/NurtifyMultiInput";
import { ChangeEvent } from "react";

interface BloodGazValues {
  bloodVenousOrArterial: string;
  ph: string;
  paco2: string;
  pao2: string;
  hco3: string;
  baseExcess: string;
  hb: string;
  bloodGlucose: string;
  lactateLevel: string;
}

interface UrineAnalysisValues {
  ph: string;
  blood: string;
  protien: string;
  glucose: string;
  ketone: string;
  specificGravity: string;
  nitrite: string;
  leukocytes: string;
}

interface POCTState {
  venousBloodGasDone: boolean;
  bloodGaz: BloodGazValues;
  urineTestDone: boolean;
  urineAnalysis: UrineAnalysisValues;
}

const PointOfcareTesting = () => {
  const { assessment, setAssessment } = useHolisticFormStore();
  const [POCT, setPOCT] = useState<POCTState>(
    assessment?.POCT || {
      venousBloodGasDone: false,
      bloodGaz: {},
      urineTestDone: false,
      urineAnalysis: {},
    }
  );

  useEffect(() => {
    setAssessment({ ...assessment, POCT });
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [POCT]);

  const handlePOCTChange = (field: keyof POCTState, value: boolean) => {
    setPOCT((prev) => ({
      ...prev,
      [field]: value,
    }));
  };

  const bloodGazInputs = [
    { name: "bloodVenousOrArterial", label: "Blood (Venous / Arterial)" },
    { name: "ph", label: "PH" },
    { name: "paco2", label: "PaCO2" },
    { name: "pao2", label: "PaO2" },
    { name: "hco3", label: "HCO3" },
    { name: "baseExcess", label: "Base Excess" },
    { name: "hb", label: "Hb" },
    { name: "bloodGlucose", label: "Blood Glucose" },
    { name: "lactateLevel", label: "Lactate Level" },
  ];

  const urineAnalysisInputs = [
    { name: "ph", label: "PH" },
    { name: "blood", label: "Blood" },
    { name: "protien", label: "Protein" },
    { name: "glucose", label: "Glucose" },
    { name: "ketone", label: "Ketone" },
    { name: "specificGravity", label: "Specific Gravity" },
    { name: "nitrite", label: "Nitrite" },
    { name: "leukocytes", label: "Leukocytes" },
  ];

  const handleBloodGazChange = (_index: number, event: ChangeEvent<HTMLInputElement>) => {
    const { name, value } = event.target;
    setPOCT((prev) => ({
      ...prev,
      bloodGaz: {
        ...prev.bloodGaz,
        [name]: value,
      },
    }));
  };

  const handleUrineAnalysisChange = (_index: number, event: ChangeEvent<HTMLInputElement>) => {
    const { name, value } = event.target;
    setPOCT((prev) => ({
      ...prev,
      urineAnalysis: {
        ...prev.urineAnalysis,
        [name]: value,
      },
    }));
  };

  return (
    <form className="block align-items-start flex-column flex-md-row p-4">
      <div className="mb-3">
        <div className="mb-4 headinqQuestion">
          <img
            src={poctImage}
            className="imageEtiquette"
            alt="Point of care testing section"
          />
          <span className="fs-2 text-start etiquetteHeadingForms">
            Point of Care Testing
          </span>
        </div>

        <div className="list-group me-4">
          <span className="headinqQuestion">Is venous Blood Gas Done?</span>
          {["Yes", "No"].map((option, index) => (
            <label key={index} className="cursor-pointer">
              <NurtifyRadio
                checked={POCT.venousBloodGasDone === (option === "Yes")}
                id={`venous-gas-${option.toLowerCase()}`}
                name="venousBloodGasDone"
                value={option.toLowerCase()}
                onChange={() =>
                  handlePOCTChange("venousBloodGasDone", option === "Yes")
                }
                label={option}
              />
            </label>
          ))}
        </div>
      </div>

      {POCT.venousBloodGasDone && (
        <div className="mb-3 list-group">
          <NurtifyText label="Blood Gas" className="subheading headinqQuestion" />
          <NurtifyMultiInput
            inputs={bloodGazInputs}
            values={Object.values(POCT.bloodGaz)}
            onChange={handleBloodGazChange}
          />
        </div>
      )}

      <div className="list-group me-4">
        <NurtifyText label="Is Urine test done?" className="headinqQuestion" />
        {["Yes", "No"].map((option, index) => (
          <label key={index} className="cursor-pointer">
            <NurtifyRadio
              checked={POCT.urineTestDone === (option === "Yes")}
              onChange={() =>
                handlePOCTChange("urineTestDone", option === "Yes")
              }
              id={`urine-test-${option.toLowerCase()}`}
              label={option}
              name="urineTestDone"
              value={option.toLowerCase()}
            />
          </label>
        ))}
      </div>

      {POCT.urineTestDone && (
        <div className="mb-3 list-group">
          <NurtifyText label="Urine Analysis" className="subheading headinqQuestion" />
          <NurtifyMultiInput
            inputs={urineAnalysisInputs}
            values={Object.values(POCT.urineAnalysis)}
            onChange={handleUrineAnalysisChange}
          />
        </div>
      )}
    </form>
  );
};

export default PointOfcareTesting;

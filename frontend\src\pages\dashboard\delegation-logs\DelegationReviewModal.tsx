import { useState } from "react";
import { X, CheckCircle, XCircle } from "lucide-react";
import "./DelegationReviewModal.css";

interface DelegationLogListItem {
  uuid: string;
  study_name: string;
  department_name: string;
  team_member_name: string;
  pi_name: string;
  study_task: string;
  status: string;
  pi_notes: string | null;
  sponsor_notes: string | null;
  created_at: string;
  updated_at: string;
}

interface DelegationReviewModalProps {
  isOpen: boolean;
  delegation: DelegationLogListItem | null;
  action: 'approve' | 'reject' | null;
  onClose: () => void;
  onConfirm: (notes: string) => void;
  isLoading: boolean;
}

export default function DelegationReviewModal({
  isOpen,
  delegation,
  action,
  onClose,
  onConfirm,
  isLoading
}: DelegationReviewModalProps) {
  const [notes, setNotes] = useState("");

  if (!isOpen || !delegation || !action) return null;

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (notes.trim()) {
      onConfirm(notes.trim());
    }
  };

  const handleClose = () => {
    setNotes("");
    onClose();
  };

  const isApprove = action === 'approve';
  const actionIcon = isApprove ? <CheckCircle size={20} /> : <XCircle size={20} />;
  const actionTitle = isApprove ? "Approve Application" : "Reject Application";
  const actionButtonText = isApprove ? "Approve" : "Reject";

  return (
    <div className="delegation-review-modal-overlay">
      <div className="delegation-review-modal-container">
        <div className={`delegation-review-modal-header ${isApprove ? 'approve' : 'reject'}`}>
          <div className="delegation-review-modal-header-content">
            <div className="delegation-review-modal-header-icon">
              {actionIcon}
            </div>
            <h2>{actionTitle}</h2>
          </div>
          <button 
            className="delegation-review-modal-close-button" 
            onClick={handleClose} 
            disabled={isLoading}
          >
            <X size={20} />
          </button>
        </div>
        
        <form onSubmit={handleSubmit}>
          <div className="delegation-review-modal-content">
            <p className="delegation-review-modal-description">
              {isApprove 
                ? "Please review the application details below and provide approval notes."
                : "Please review the application details below and provide rejection notes."
              }
            </p>
            
            <div className="delegation-review-modal-details">
              <div className="delegation-review-modal-detail-row">
                <span className="delegation-review-modal-detail-label">Study:</span>
                <span className="delegation-review-modal-detail-value">{delegation.study_name}</span>
              </div>
              <div className="delegation-review-modal-detail-row">
                <span className="delegation-review-modal-detail-label">Team Member:</span>
                <span className="delegation-review-modal-detail-value">{delegation.team_member_name}</span>
              </div>
              <div className="delegation-review-modal-detail-row">
                <span className="delegation-review-modal-detail-label">Department:</span>
                <span className="delegation-review-modal-detail-value">{delegation.department_name}</span>
              </div>
              <div className="delegation-review-modal-detail-row">
                <span className="delegation-review-modal-detail-label">Study Task:</span>
                <span className="delegation-review-modal-detail-value">{delegation.study_task}</span>
              </div>
              <div className="delegation-review-modal-detail-row">
                <span className="delegation-review-modal-detail-label">Requested Date:</span>
                <span className="delegation-review-modal-detail-value">
                  {new Date(delegation.created_at).toLocaleDateString()}
                </span>
              </div>
            </div>
            
            <div className="delegation-review-modal-notes-section">
              <label htmlFor="pi-notes" className="delegation-review-modal-notes-label">
                {isApprove ? "Approval Notes:" : "Rejection Notes:"}
              </label>
              <textarea
                id="pi-notes"
                className="delegation-review-modal-notes-textarea"
                value={notes}
                onChange={(e) => setNotes(e.target.value)}
                placeholder={isApprove 
                  ? "Provide approval notes (e.g., 'Approved - Excellent experience with patient recruitment')"
                  : "Provide rejection notes (e.g., 'Rejected - Insufficient experience with this type of study')"
                }
                rows={4}
                required
                disabled={isLoading}
              />
              <p className="delegation-review-modal-notes-help">
                {isApprove 
                  ? "These notes will be shared with the sponsor and team member."
                  : "These notes will be shared with the team member."
                }
              </p>
            </div>
          </div>
          
          <div className="delegation-review-modal-footer">
            <div className="delegation-review-modal-footer-buttons">
              <button 
                type="button"
                className="delegation-review-modal-btn delegation-review-modal-btn-cancel"
                onClick={handleClose}
                disabled={isLoading}
              >
                Cancel
              </button>
              <button 
                type="submit"
                className={`delegation-review-modal-btn ${isApprove ? 'delegation-review-modal-btn-approve' : 'delegation-review-modal-btn-reject'}`}
                disabled={isLoading || !notes.trim()}
              >
                {isLoading ? (
                  <div className="delegation-review-modal-loading">
                    <div className="delegation-review-modal-spinner"></div>
                    Processing...
                  </div>
                ) : (
                  <>
                    {actionIcon}
                    {actionButtonText}
                  </>
                )}
              </button>
            </div>
          </div>
        </form>
      </div>
    </div>
  );
}

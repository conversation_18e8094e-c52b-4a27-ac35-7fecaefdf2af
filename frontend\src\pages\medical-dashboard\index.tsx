import React, { useState, useEffect } from 'react';
import { useNavigate, Outlet, useLocation } from 'react-router-dom';
import useSelectedPatientStore from '@/store/SelectedPatientState';
import Preloader from '@/components/common/Preloader';
import { formatDate } from '@/utils/date';
import {
  usePatientAllergiesQuery,
  usePatientConcomitantMedicationsQuery,
  usePatientMedicalHistoryQuery,
  usePatientAccessLogsByUuidQuery,
  useCheckFavoritePatientQuery,
  useToggleFavoritePatientMutation
} from '@/hooks/patient.query';

// Calculate age from date of birth
const calculateAge = (dateOfBirth: string | undefined): number => {
  if (!dateOfBirth) return 0;
  try {
    const birthDate = new Date(dateOfBirth);
    const today = new Date();
    let age = today.getFullYear() - birthDate.getFullYear();
    const monthDiff = today.getMonth() - birthDate.getMonth();

    if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birthDate.getDate())) {
      age--;
    }

    return age;
  } catch {
    return 0;
  }
};
import { usePatientWarnings } from '@/hooks/usePatientWarnings';
import { usePatientVitalSigns } from '@/hooks/vital-signs.query';
import { useStudyEnrollmentsByPatientQuery } from '@/hooks/study.query';
import { usePrescriptionsByPatientQuery } from '@/hooks/prescription.query';
import { useSymptomsQuery } from '@/hooks/symptom.query';
import {
  useGetUnsubmittedFormsByPatient,
  useGetIncompleteSubmissionsByPatient,
  useGetCompletedSubmissionsByPatient,
  useGetFinalizedSubmissionsByPatient,
  useGetFormSubmissionQueriesByPatient
} from '@/hooks/form.query';
import {
  FileText,
  MessagesSquare,
  ClipboardList,
  Pill,
  ListCollapse,
  History,
  NotebookText,
  PillBottle,
  Activity,
  BarChart3,
  Bell,
  AlertTriangle,
  Zap,
  Edit,
  ClipboardPlus,
  Star,
  X
} from 'lucide-react';
import './MedicalDashboard.css';
import ChangeProfilePictureModal from '@/components/modal/ChangeProfilePictureModal';
import api from '@/services/api';

interface SidebarItem {
  id: string;
  label: string;
  icon: JSX.Element;
  active?: boolean;
  path?: string;
}

const sidebarItems: SidebarItem[] = [
  { id: 'dashboard', label: 'Dashboard', icon: <BarChart3 size={20} />, path: '/org/dashboard/patient-board' },
  { id: 'patient-profile', label: 'Patient Details', icon: <ListCollapse size={20} />, path: '/org/dashboard/patient-board/edit-patient' },
  { id: 'patient-medications', label: 'Patient Medications', icon: <PillBottle size={20} />, path: '/org/dashboard/patient-board/patient-medications' },
  { id: 'vital-signs', label: 'Vital Signs', icon: <Activity size={20} />, path: '/org/dashboard/patient-board/vital-signs' },
  { id: 'forms', label: 'Forms', icon: <FileText size={20} />, path: '/org/dashboard/patient-board/patient-forms' },
  { id: 'patient-documentation', label: 'Documentation', icon: <FileText size={20} />, path: '/org/dashboard/patient-board/patient-documentation' },
  { id: 'forms-queries', label: 'Forms Queries', icon: <MessagesSquare size={20} />, path: '/org/dashboard/patient-board/forms-queries' },
  { id: 'studies', label: 'Studies', icon: <ClipboardList size={20} />, path: '/org/dashboard/patient-board/patient-studies' },
  { id: 'patient-diary', label: 'Patient Diary', icon: <NotebookText size={20} />, path: '/org/dashboard/patient-board/patient-diary' },
  { id: 'prescriptions', label: 'Prescriptions', icon: <Pill size={20} />, path: '/org/dashboard/patient-board/prescription' },
  { id: 'patient-logs', label: 'Patient Logs', icon: <History size={20} />, path: '/org/dashboard/patient-board/patient-logs' },
  { id: 'patient-reports', label: 'Patient Reports', icon: <ClipboardPlus size={20} />, path: '/org/dashboard/patient-board/patient-report' }
];

interface SidebarProps {
  isMinimized: boolean;
  onToggleMinimize: () => void;
  onItemClick: (item: SidebarItem) => void;
  selectedPatient: any;
  currentPath: string;
  onProfilePictureClick: () => void;
}

const getProfilePictureUrl = (url: string | undefined) => {
  const placeholder = "data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNjAiIGhlaWdodD0iNjAiIHZpZXdCb3g9IjAgMCA2MCA2MCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPGNpcmNsZSBjeD0iMzAiIGN5PSIzMCIgcj0iMzAiIGZpbGw9IiNEQ0RDREMiLz4KPGNpcmNsZSBjeD0iMzAiIGN5PSIyMyIgcj0iMTAiIGZpbGw9IiM5OTk5OTkiLz4KPHBhdGggZD0iTTEwIDUwQzEwIDQwIDIwIDM1IDMwIDM1QzQwIDM1IDUwIDQwIDUwIDUwIiBmaWxsPSIjOTk5OTk5Ii8+Cjwvc3ZnPgo=";
  if (!url || url.trim() === '') return placeholder;
  if (url.startsWith('http://') || url.startsWith('https://')) return url;
  // Use API base URL for relative paths
  return `${api.defaults.baseURL?.replace(/\/api$/, '')}${url}`;
};

const Sidebar: React.FC<SidebarProps> = ({ isMinimized, onToggleMinimize, onItemClick, selectedPatient, currentPath, onProfilePictureClick }) => {
  // Use the favorite patient hooks
  const { data: isStarred } = useCheckFavoritePatientQuery(selectedPatient?.uuid || '');
  const toggleFavoriteMutation = useToggleFavoritePatientMutation();

  const handleStarClick = (e: React.MouseEvent) => {
    e.stopPropagation();
    if (selectedPatient?.uuid) {
      toggleFavoriteMutation.mutate(selectedPatient.uuid);
    }
  };

  const placeholder = "data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNjAiIGhlaWdodD0iNjAiIHZpZXdCb3g9IjAgMCA2MCA2MCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPGNpcmNsZSBjeD0iMzAiIGN5PSIzMCIgcj0iMzAiIGZpbGw9IiNEQ0RDREMiLz4KPGNpcmNsZSBjeD0iMzAiIGN5PSIyMyIgcj0iMTAiIGZpbGw9IiM5OTk5OTkiLz4KPHBhdGggZD0iTTEwIDUwQzEwIDQwIDIwIDM1IDMwIDM1QzQwIDM1IDUwIDQwIDUwIDUwIiBmaWxsPSIjOTk5OTk5Ii8+Cjwvc3ZnPgo=";
  return (
    <div className={`med-sidebar ${isMinimized ? 'med-sidebar-minimized' : ''}`}>
      <div className="med-sidebar-header">
        <button className="med-minimize-btn" onClick={onToggleMinimize}>
          {isMinimized ? '→' : '←'} {!isMinimized && 'Minimize'}
        </button>
      </div>
      {!isMinimized && (
        <div className="med-sidebar-patient">
          <div className="med-sidebar-patient-info">
            <div className="med-sidebar-patient-header">
              <div className="med-sidebar-patient-avatar" onClick={onProfilePictureClick} style={{ cursor: 'pointer' }}>
                <img
                  src={getProfilePictureUrl(selectedPatient?.profile_picture_url)}
                  alt="Patient Avatar"
                  style={{ width: '100%', height: '100%', objectFit: 'cover' }}
                  onError={e => { e.currentTarget.src = placeholder; }}
                />
              </div>
              <div className="med-sidebar-patient-basic-info">
            
                    <div className="d-flex gap-5">
                      <h3 className="med-sidebar-patient-name">{selectedPatient?.first_name} {selectedPatient?.last_name}</h3>
                      <span
                        style={{ cursor: 'pointer', display: 'flex', alignItems: 'center' }}
                        onClick={handleStarClick}
                        title={isStarred ? "Remove from Favorites" : "Add to Favorites"}
                      >
                        <Star
                          size={25}
                          fill={isStarred ? '#FECE23' : '#EBF4F6'}
                          color={isStarred ? '#FECE23' : '#088395'}
                        />
                      </span>
                    </div>

                <p className="med-sidebar-patient-meta">{selectedPatient?.gender || 'Male'} | {calculateAge(selectedPatient?.date_of_birth)} Years</p>
              </div>
            </div>
            <div className="med-sidebar-patient-details">
              <div className="med-sidebar-patient-table">
                <div className="med-sidebar-patient-row">
                  <span className="med-sidebar-patient-label">NHS</span>
                  <span className="med-sidebar-patient-value">{selectedPatient?.nhs_number || '123456'}</span>
                </div>
                <div className="med-sidebar-patient-row">
                  <span className="med-sidebar-patient-label">DOB</span>
                  <span className="med-sidebar-patient-value">{selectedPatient?.date_of_birth ? formatDate(selectedPatient.date_of_birth) : '12-12-2025'}</span>
                </div>
                <div className="med-sidebar-patient-row">
                  <span className="med-sidebar-patient-label">MRN</span>
                  <span className="med-sidebar-patient-value">{selectedPatient?.medical_record_number || '1234567'}</span>
                </div>
                <div className="med-sidebar-patient-row">
                  <span className="med-sidebar-patient-label">Ethnicity</span>
                  <span className="med-sidebar-patient-value">{selectedPatient?.ethnic_background || 'YYY'}</span>
                </div>
                <div className="med-sidebar-patient-row">
                  <span className="med-sidebar-patient-label">Email</span>
                  <span className="med-sidebar-patient-value">{selectedPatient?.email || '<EMAIL>'}</span>
                </div>
                <div className="med-sidebar-patient-row">
                  <span className="med-sidebar-patient-label">Phone</span>
                  <span className="med-sidebar-patient-value">{selectedPatient?.phone_number || '**********'}</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}

      <ul className="med-sidebar-menu">
        {sidebarItems.map((item) => {
          const isActive = item.path === currentPath ||
            (item.path === '/org/dashboard/patient-board' && currentPath === '/org/dashboard/patient-board');
          return (
            <li key={item.id} className="med-sidebar-item">
              <button
                className={`med-sidebar-link ${isActive ? 'med-active' : ''}`}
                onClick={() => onItemClick(item)}
              >
                <span className="med-sidebar-icon">{item.icon}</span>
                {!isMinimized && item.label}
              </button>
            </li>
          );
        })}
      </ul>
      
      {/* Close Patient Portal Button */}
      <div className="med-sidebar-close-portal">
        <button
          className="med-sidebar-close-btn"
          onClick={() => window.location.href = '/clinical'}
        >
          <span className="med-sidebar-icon">
            <X size={20} />
          </span>
          {!isMinimized && 'Close Patient Portal'}
        </button>
      </div>
    </div>
  );
};

const AlertsSection: React.FC<{ patient: any }> = ({ patient }) => {
  // Patient Allergies functionality
  const { data: patientAllergies, isLoading: isAllergiesLoading } = usePatientAllergiesQuery(patient?.nhs_number || "");

  // Patient Warnings functionality
  const {
    warnings,
    isLoading: isWarningsLoading
  } = usePatientWarnings(patient?.uuid || "");

  // Get active warnings for summary
  const activeWarnings = warnings.filter((warning: any) => warning.is_active);

  const navigate = useNavigate();

  return (
    <div className="med-alerts-section">
      <div className="med-alert-card med-allergies">
        <div className="med-alert-header">
          <h3>
            <Bell size={16} className="med-alert-icon" />
            Allergies
          </h3>
          <button className="med-edit-btn" onClick={() => navigate('/org/dashboard/patient-board/edit-patient')}>
            <Edit size={14} />
          </button>
        </div>
        <ul className="med-alert-list">
          {isAllergiesLoading ? (
            <li>Loading...</li>
          ) : patientAllergies && patientAllergies.length > 0 ? (
            patientAllergies.map((allergy: any, index: number) => (
              <li key={allergy.uuid || index}>• {allergy.name}</li>
            ))
          ) : (
            <li>No allergies recorded</li>
          )}
        </ul>
      </div>

      <div className="med-alert-card med-warnings">
        <div className="med-alert-header">
          <h3>
            <AlertTriangle size={16} className="med-alert-icon" />
            Active Warnings
          </h3>
          <button className="med-edit-btn" onClick={() => navigate('/org/dashboard/patient-board/edit-patient')}>
            <Edit size={14} />
          </button>
        </div>
        <ul className="med-alert-list">
          {isWarningsLoading ? (
            <li>Loading...</li>
          ) : activeWarnings.length > 0 ? (
            activeWarnings.map((warning: any) => (
              <li key={warning.uuid}>• {warning.name}</li>
            ))
          ) : (
            <li>No active warnings</li>
          )}
        </ul>
      </div>

      <div className="med-alert-card med-attention">
        <div className="med-alert-header">
          <h3>
            <Zap size={16} className="med-alert-icon" />
            Needs Attention
          </h3>
        </div>
        <ul className="med-alert-list">
          <li>• Violent patient</li>
          <li>• Sterilized Chemo</li>
        </ul>
      </div>
    </div>
  );
};

const VitalsSection: React.FC<{ patientUuid: string }> = ({ patientUuid }) => {
  const { summary, vitalSigns, isLoading } = usePatientVitalSigns(patientUuid);

  // Get the latest vital signs from the summary or the first vital sign record
  const latestVitalSign = vitalSigns[0];

  const formatVitalSign = (value: number | undefined, unit: string, fallback: string) => {
    if (value !== undefined && value !== null) {
      return `${value} ${unit}`;
    }
    return fallback;
  };

  if (isLoading) {
    return (
      <div className="med-vitals-section">
        <h3 className="med-section-title">Vitals</h3>
        <div className="med-vitals-grid">
          <div className="med-vital-item">
            <span className="med-vital-label">Loading...</span>
            <span className="med-vital-value">-</span>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="med-vitals-section">
      <h3 className="med-section-title">Vitals</h3>
      <div className="med-vitals-grid">
        <div className="med-vital-item">
          <span className="med-vital-label">Temperature</span>
          <span className="med-vital-value">
            {formatVitalSign(
              summary?.latest_temperature || latestVitalSign?.temperature,
              summary?.latest_temp_unit || latestVitalSign?.temp_unit || '°C',
              '-'
            )}
          </span>
        </div>
        <div className="med-vital-item">
          <span className="med-vital-label">Heart Rate</span>
          <span className="med-vital-value">
            {formatVitalSign(
              summary?.latest_heart_rate || latestVitalSign?.heart_rate,
              'bpm',
              '-'
            )}
          </span>
        </div>
        <div className="med-vital-item">
          <span className="med-vital-label">Blood Pressure</span>
          <span className="med-vital-value">
            {summary?.latest_systolic_bp || latestVitalSign?.systolic_bp
              ? `${summary?.latest_systolic_bp || latestVitalSign?.systolic_bp}/${summary?.latest_diastolic_bp || latestVitalSign?.diastolic_bp || '-'} mmHg`
              : '-'
            }
          </span>
        </div>
        <div className="med-vital-item">
          <span className="med-vital-label">SpO2</span>
          <span className="med-vital-value">
            {formatVitalSign(
              summary?.latest_oxygen_saturation || latestVitalSign?.oxygen_saturation,
              '%',
              '-'
            )}
          </span>
        </div>
        <div className="med-vital-item">
          <span className="med-vital-label">Respiratory Rate</span>
          <span className="med-vital-value">
            {formatVitalSign(
              summary?.latest_respiratory_rate || latestVitalSign?.respiratory_rate,
              'br/min',
              '-'
            )}
          </span>
        </div>
        <div className="med-vital-item">
          <span className="med-vital-label">Pain</span>
          <span className="med-vital-value">-</span>
        </div>
        <div className="med-vital-item">
          <span className="med-vital-label">Height</span>
          <span className="med-vital-value">
            {formatVitalSign(
              summary?.latest_height || latestVitalSign?.height,
              summary?.latest_height_unit || latestVitalSign?.height_unit || 'cm',
              '-'
            )}
          </span>
        </div>
        <div className="med-vital-item">
          <span className="med-vital-label">Weight</span>
          <span className="med-vital-value">
            {formatVitalSign(
              summary?.latest_weight || latestVitalSign?.weight,
              summary?.latest_weight_unit || latestVitalSign?.weight_unit || 'kg',
              '-'
            )}
          </span>
        </div>
      </div>
    </div>
  );
};

const PatientDiary: React.FC<{ patientUuid: string }> = ({ patientUuid }) => {
  const { data: symptoms, isLoading } = useSymptomsQuery(patientUuid);
  const navigate = useNavigate();

  if (isLoading) {
    return (
      <div className="med-section-card">
        <div className="med-section-header">
          <h3>Patient Diary</h3>
          <button className="med-add-btn" onClick={() => navigate('/org/dashboard/patient-board/patient-diary')}>Add Symptom</button>
        </div>
        <div className="med-table-container">
          <table className="med-data-table">
            <thead>
              <tr>
                <th>Description</th>
                <th>Start Date</th>
                <th>Time</th>
                <th>Hospitalized</th>
              </tr>
            </thead>
            <tbody>
              <tr>
                <td colSpan={4}>Loading...</td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>
    );
  }

  const symptomsData = symptoms || [];

  return (
    <div className="med-section-card">
      <div className="med-section-header">
        <h3>Patient Diary</h3>
        <button className="med-add-btn" onClick={() => navigate('/org/dashboard/patient-board/patient-diary')}>Add Symptom</button>
      </div>
      <div className="med-table-container">
        <table className="med-data-table">
          <thead>
            <tr>
              <th>Description</th>
              <th>Start Date</th>
              <th>Time</th>
              <th>Hospitalized</th>
            </tr>
          </thead>
          <tbody>
            {symptomsData.length > 0 ? (
              symptomsData.slice(0, 3).map((symptom: any) => (
                <tr key={symptom.uuid}>
                  <td>{symptom.description}</td>
                  <td>{formatDate(symptom.start_date)}</td>
                  <td>{symptom.start_time}</td>
                  <td>{symptom.hospitalization_required ? 'Yes' : 'No'}</td>
                </tr>
              ))
            ) : (
              <tr>
                <td colSpan={4}>No symptoms recorded</td>
              </tr>
            )}
          </tbody>
        </table>
      </div>
    </div>
  );
};

const PatientStudies: React.FC<{ patientUuid: string }> = ({ patientUuid }) => {
  const { data: enrollments, isLoading } = useStudyEnrollmentsByPatientQuery(patientUuid);
  const navigate = useNavigate();

  const enrollmentCount = enrollments?.length || 0;
  const pendingCount = enrollments?.filter((e: any) => e.study_status === 'Pending')?.length || 0;
  const acceptedCount = enrollments?.filter((e: any) => e.study_status === 'Accepted')?.length || 0;
  const rejectedCount = enrollments?.filter((e: any) => e.study_status === 'Rejected')?.length || 0;

  if (isLoading) {
    return (
      <div className="med-section-card">
        <div className="med-section-header">
          <h3>Patient Studies</h3>
          <button className="med-add-btn" onClick={() => navigate('/org/dashboard/patient-board/patient-studies')}>Enrol in Study</button>
        </div>
        <div className="med-studies-grid">
          <div className="med-studies-row">
            <div className="med-study-category">
              <div className="med-study-category-header">
                <span className="med-study-category-title">Loading...</span>
                <span className="med-study-count">-</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="med-section-card">
      <div className="med-section-header">
        <h3>Patient Studies</h3>
        <button className="med-add-btn" onClick={() => navigate('/org/dashboard/patient-board/patient-studies')}>Enrol in Study</button>
      </div>
      <div className="med-studies-grid">
        <div className="med-studies-row">
          <div className="med-study-category">
            <div className="med-study-category-header">
              <span className="med-study-category-title">Enrollments</span>
              <span className="med-study-count">{enrollmentCount}</span>
            </div>
            <div className="med-study-items">
              {enrollments && enrollments.length > 0 ? (
                enrollments.slice(0, 2).map((enrollment: any, index: number) => (
                  <div key={enrollment.uuid} className="med-study-item">
                    {index + 1}. {enrollment.study_name}
                  </div>
                ))
              ) : (
                <div className="med-study-item">No enrollments</div>
              )}
            </div>
          </div>

          <div className="med-study-category">
            <div className="med-study-category-header">
              <span className="med-study-category-title">Pending Requests</span>
              <span className="med-study-count">{pendingCount}</span>
            </div>
            <div className="med-study-items">
              {enrollments && enrollments.filter((e: any) => e.study_status === 'Pending').length > 0 ? (
                enrollments
                  .filter((e: any) => e.study_status === 'Pending')
                  .slice(0, 2)
                  .map((enrollment: any, index: number) => (
                    <div key={enrollment.uuid} className="med-study-item">
                      {index + 1}. {enrollment.study_name}
                    </div>
                  ))
              ) : (
                <div className="med-study-item">No pending requests</div>
              )}
            </div>
          </div>
        </div>

        <div className="med-studies-row">
          <div className="med-study-category">
            <div className="med-study-category-header">
              <span className="med-study-category-title">Accepted Requests</span>
              <span className="med-study-count">{acceptedCount}</span>
            </div>
            <div className="med-study-items">
              {enrollments && enrollments.filter((e: any) => e.study_status === 'Accepted').length > 0 ? (
                enrollments
                  .filter((e: any) => e.study_status === 'Accepted')
                  .slice(0, 2)
                  .map((enrollment: any, index: number) => (
                    <div key={enrollment.uuid} className="med-study-item">
                      {index + 1}. {enrollment.study_name}
                    </div>
                  ))
              ) : (
                <div className="med-study-item">No accepted requests</div>
              )}
            </div>
          </div>

          <div className="med-study-category">
            <div className="med-study-category-header">
              <span className="med-study-category-title">Rejected Requests</span>
              <span className="med-study-count">{rejectedCount}</span>
            </div>
            <div className="med-study-items">
              {enrollments && enrollments.filter((e: any) => e.study_status === 'Rejected').length > 0 ? (
                enrollments
                  .filter((e: any) => e.study_status === 'Rejected')
                  .slice(0, 2)
                  .map((enrollment: any, index: number) => (
                    <div key={enrollment.uuid} className="med-study-item">
                      {index + 1}. {enrollment.study_name}
                    </div>
                  ))
              ) : (
                <div className="med-study-item">No rejected requests</div>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

const ReportsSection: React.FC = () => {
  return (
    <div className="med-section-card">
      <h3>Reports</h3>
      <div className="med-table-container">
        <table className="med-data-table">
          <thead>
            <tr>
              <th>Name</th>
              <th>Date</th>
              <th>Type</th>
              <th>Status</th>
            </tr>
          </thead>
          <tbody>
            <tr>
              <td>Name</td>
              <td>2025-06-01</td>
              <td>abc</td>
              <td className="med-status-yyy">YYY</td>
            </tr>
            <tr>
              <td>Name</td>
              <td>2025-06-01</td>
              <td>abc</td>
              <td className="med-status-yyy">YYY</td>
            </tr>
            <tr>
              <td>Name</td>
              <td>2025-06-01</td>
              <td>abc</td>
              <td className="med-status-yyy">YYY</td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>
  );
};

const MedicalHistory: React.FC<{ patientUuid: string }> = ({ patientUuid }) => {
  const { data: medicalHistory, isLoading } = usePatientMedicalHistoryQuery(patientUuid);

  if (isLoading) {
    return (
      <div className="med-section-card">
        <h3>Medical History</h3>
        <div className="med-table-container">
          <table className="med-data-table">
            <thead>
              <tr>
                <th>Name</th>
                <th>Date</th>
                <th>Status</th>
              </tr>
            </thead>
            <tbody>
              <tr>
                <td colSpan={3}>Loading...</td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>
    );
  }

  const historyData = medicalHistory?.results || [];

  return (
    <div className="med-section-card">
      <h3>Medical History</h3>
      <div className="med-table-container">
        <table className="med-data-table">
          <thead>
            <tr>
              <th>Name</th>
              <th>Date</th>
              <th>Status</th>
            </tr>
          </thead>
          <tbody>
            {historyData.length > 0 ? (
              historyData.slice(0, 3).map((history: any) => (
                <tr key={history.uuid}>
                  <td>{history.condition_name}</td>
                  <td>{formatDate(history.diagnosis_date)}</td>
                  <td className="med-status-yyy">{history.status || 'Active'}</td>
                </tr>
              ))
            ) : (
              <tr>
                <td colSpan={3}>No medical history recorded</td>
              </tr>
            )}
          </tbody>
        </table>
      </div>
    </div>
  );
};

const Documentation: React.FC<{ patientUuid: string }> = ({ patientUuid }) => {
  const { data: emptyForms, isLoading: isLoadingEmptyForms } = useGetUnsubmittedFormsByPatient(patientUuid);
  const { data: draftSubmissions, isLoading: isLoadingDrafts } = useGetIncompleteSubmissionsByPatient(patientUuid);
  const { data: completedSubmissions, isLoading: isLoadingCompleted } = useGetCompletedSubmissionsByPatient(patientUuid);
  const { data: submittedForms, isLoading: isLoadingSubmitted } = useGetFinalizedSubmissionsByPatient(patientUuid);
  const navigate = useNavigate();

  const isLoading = isLoadingEmptyForms || isLoadingDrafts || isLoadingCompleted || isLoadingSubmitted;

  if (isLoading) {
    return (
      <div className="med-section-card">
        <div className="med-section-header">
          <h3>Documentation</h3>
          <button className="med-add-btn" onClick={() => navigate('/org/dashboard/patient-board/patient-forms')}>Add New Form</button>
        </div>
        <div className="med-documentation-grid">
          <div className="med-doc-item">
            <span className="med-doc-label">Empty Forms</span>
            <span className="med-doc-count">Loading...</span>
          </div>
          <div className="med-doc-item">
            <span className="med-doc-label">Drafts</span>
            <span className="med-doc-count">Loading...</span>
          </div>
          <div className="med-doc-item">
            <span className="med-doc-label">Completed</span>
            <span className="med-doc-count">Loading...</span>
          </div>
          <div className="med-doc-item">
            <span className="med-doc-label">Submitted</span>
            <span className="med-doc-count">Loading...</span>
          </div>
        </div>
      </div>
    );
  }

  const emptyFormsCount = Array.isArray(emptyForms) ? emptyForms.length : 0;
  const draftsCount = Array.isArray(draftSubmissions) ? draftSubmissions.length : 0;
  const completedCount = Array.isArray(completedSubmissions) ? completedSubmissions.length : 0;
  const submittedCount = Array.isArray(submittedForms) ? submittedForms.length : 0;

  return (
    <div className="med-section-card">
      <div className="med-section-header">
        <h3>Documentation</h3>
        <button className="med-add-btn" onClick={() => navigate('/org/dashboard/patient-board/patient-forms')}>Add New Form</button>
      </div>
      <div className="med-documentation-grid">
        <div className="med-doc-item">
          <span className="med-doc-label">Empty Forms</span>
          <span className="med-doc-count">{emptyFormsCount.toString().padStart(2, '0')}</span>
        </div>
        <div className="med-doc-item">
          <span className="med-doc-label">Drafts</span>
          <span className="med-doc-count">{draftsCount.toString().padStart(2, '0')}</span>
        </div>
        <div className="med-doc-item">
          <span className="med-doc-label">Completed</span>
          <span className="med-doc-count">{completedCount.toString().padStart(2, '0')}</span>
        </div>
        <div className="med-doc-item">
          <span className="med-doc-label">Submitted</span>
          <span className="med-doc-count">{submittedCount.toString().padStart(2, '0')}</span>
        </div>
      </div>
    </div>
  );
};

const FormsQueries: React.FC<{ patientUuid: string }> = ({ patientUuid }) => {
  // Get active queries
  const {
    data: activeQueriesData,
    isLoading: isLoadingActiveQueries
  } = useGetFormSubmissionQueriesByPatient(patientUuid, {
    is_resolved: false
  });

  // Get resolved queries
  const {
    data: resolvedQueriesData,
    isLoading: isLoadingResolvedQueries
  } = useGetFormSubmissionQueriesByPatient(patientUuid, {
    is_resolved: true
  });

  const navigate = useNavigate();
  const isLoading = isLoadingActiveQueries || isLoadingResolvedQueries;

  // Extract queries from paginated response
  const activeQueries = ((activeQueriesData as any)?.results || []);
  const resolvedQueries = ((resolvedQueriesData as any)?.results || []);

  const activeCount = activeQueries.length;
  const resolvedCount = resolvedQueries.length;

  if (isLoading) {
    return (
      <div className="med-section-card">
        <div className="med-section-header">
          <h3>Forms Queries</h3>
          <span className="med-resolved-count">Loading...</span>
        </div>
        <div className="med-section-header">
          <span>Loading...</span>
        </div>
        <div className="med-table-container">
          <table className="med-data-table">
            <thead>
              <tr>
                <th>Question</th>
                <th>Form Name</th>
                <th>Priority</th>
                <th>Action</th>
              </tr>
            </thead>
            <tbody>
              <tr>
                <td colSpan={4}>Loading...</td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>
    );
  }

  return (
    <div className="med-section-card">
      <div className="med-section-header">
        <h3>Forms Queries</h3>
        <span className="med-resolved-count">Resolved: {resolvedCount.toString().padStart(2, '0')}</span>
      </div>
      <div className="med-section-header">
        <span>Active: {activeCount.toString().padStart(2, '0')}</span>
      </div>
      <div className="med-table-container">
        <table className="med-data-table">
          <thead>
            <tr>
              <th>Question</th>
              <th>Form Name</th>
              <th>Priority</th>
              <th>Action</th>
            </tr>
          </thead>
          <tbody>
            {activeQueries.length > 0 ? (
              activeQueries.slice(0, 3).map((query: any) => (
                <tr key={query.uuid}>
                  <td>{query.question_number || 'N/A'}</td>
                  <td>{query.form_submission?.form_name || 'N/A'}</td>
                  <td className={`med-priority-${query.priority?.toLowerCase() || 'medium'}`}>
                    {query.priority || 'Medium'}
                  </td>
                  <td><button className="med-action-btn" onClick={() => navigate('/org/dashboard/patient-board/forms-queries')}>Details</button></td>
                </tr>
              ))
            ) : (
              <tr>
                <td colSpan={4}>No active queries</td>
              </tr>
            )}
          </tbody>
        </table>
      </div>
    </div>
  );
};

const CurrentMedications: React.FC<{ patientUuid: string }> = ({ patientUuid }) => {
  const { data: medications, isLoading } = usePatientConcomitantMedicationsQuery(patientUuid);
  const navigate = useNavigate();

  if (isLoading) {
    return (
      <div className="med-section-card">
        <div className="med-section-header">
          <h3>Current Medications</h3>
          <button className="med-add-btn" onClick={() => navigate('/org/dashboard/patient-board/patient-medications')}>Add Medication</button>
        </div>
        <div className="med-table-container">
          <table className="med-data-table">
            <thead>
              <tr>
                <th>Medication</th>
                <th>Indication</th>
                <th>Dose</th>
                <th>Schedule</th>
              </tr>
            </thead>
            <tbody>
              <tr>
                <td colSpan={4}>Loading...</td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>
    );
  }

  const medicationsData = medications?.results || [];

  return (
    <div className="med-section-card">
      <div className="med-section-header">
        <h3>Current Medications</h3>
        <button className="med-add-btn" onClick={() => navigate('/org/dashboard/patient-board/patient-medications')}>Add Medication</button>
      </div>
      <div className="med-table-container">
        <table className="med-data-table">
          <thead>
            <tr>
              <th>Medication</th>
              <th>Indication</th>
              <th>Dose</th>
              <th>Schedule</th>
            </tr>
          </thead>
          <tbody>
            {medicationsData.length > 0 ? (
              medicationsData.slice(0, 3).map((medication: any) => (
                <tr key={medication.uuid}>
                  <td>{medication.medication}</td>
                  <td>{medication.indication}</td>
                  <td>{medication.dose} {medication.dose_units_display}</td>
                  <td>{medication.schedule_display}</td>
                </tr>
              ))
            ) : (
              <tr>
                <td colSpan={4}>No medications recorded</td>
              </tr>
            )}
          </tbody>
        </table>
      </div>
    </div>
  );
};

const Prescription: React.FC<{ patientUuid: string }> = ({ patientUuid }) => {
  const { data: prescriptions, isLoading } = usePrescriptionsByPatientQuery(patientUuid);
  const navigate = useNavigate();

  if (isLoading) {
    return (
      <div className="med-section-card">
        <div className="med-section-header">
          <h3>Prescription</h3>
          <button className="med-add-btn" onClick={() => navigate('/org/dashboard/patient-board/prescription')}>Add Prescription</button>
        </div>
        <div className="med-table-container">
          <table className="med-data-table">
            <thead>
              <tr>
                <th>Drug</th>
                <th>Frequency</th>
                <th>Dose</th>
                <th>Unit</th>
              </tr>
            </thead>
            <tbody>
              <tr>
                <td colSpan={4}>Loading...</td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>
    );
  }

  const prescriptionsData = prescriptions || [];

  return (
    <div className="med-section-card">
      <div className="med-section-header">
        <h3>Prescription</h3>
        <button className="med-add-btn" onClick={() => navigate('/org/dashboard/patient-board/prescription')}>Add Prescription</button>
      </div>
      <div className="med-table-container">
        <table className="med-data-table">
          <thead>
            <tr>
              <th>Drug</th>
              <th>Frequency</th>
              <th>Dose</th>
              <th>Unit</th>
            </tr>
          </thead>
          <tbody>
            {prescriptionsData.length > 0 ? (
              prescriptionsData.slice(0, 3).map((prescription: any) => (
                <tr key={prescription.uuid}>
                  <td>{prescription.drug_name}</td>
                  <td>{prescription.frequency}</td>
                  <td>{prescription.dose}</td>
                  <td>{prescription.unit}</td>
                </tr>
              ))
            ) : (
              <tr>
                <td colSpan={4}>No prescriptions recorded</td>
              </tr>
            )}
          </tbody>
        </table>
      </div>
    </div>
  );
};

const PatientLogs: React.FC<{ patientUuid: string }> = ({ patientUuid }) => {
  const { data: logs, isLoading, error } = usePatientAccessLogsByUuidQuery(patientUuid);

  const formatDateTime = (dateString: string) => {
    try {
      return new Date(dateString).toLocaleString('en-US', {
        year: 'numeric',
        month: 'long',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit'
      });
    } catch {
      return dateString;
    }
  };

  if (isLoading) {
    return (
      <div className="med-section-card">
        <h3>Patient Logs</h3>
        <div className="med-table-container">
          <table className="med-data-table">
            <thead>
              <tr>
                <th>Accessed By</th>
                <th>Access Type</th>
                <th>Date & Time</th>
                <th>IP Address</th>
              </tr>
            </thead>
            <tbody>
              <tr>
                <td colSpan={4}>Loading...</td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="med-section-card">
        <h3>Patient Logs</h3>
        <div className="med-table-container">
          <table className="med-data-table">
            <thead>
              <tr>
                <th>Accessed By</th>
                <th>Access Type</th>
                <th>Date & Time</th>
                <th>IP Address</th>
              </tr>
            </thead>
            <tbody>
              <tr>
                <td colSpan={4}>Error loading logs</td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>
    );
  }

  const logsData = Array.isArray(logs) ? logs : [];

  return (
    <div className="med-section-card">
      <h3>Patient Logs</h3>
      <div className="med-table-container">
        <table className="med-data-table">
          <thead>
            <tr>
              <th>Accessed By</th>
              <th>Access Type</th>
              <th>Date & Time</th>
              <th>IP Address</th>
            </tr>
          </thead>
          <tbody>
            {logsData.length > 0 ? (
              logsData.slice(0, 3).map((log: any) => (
                <tr key={log.uuid}>
                  <td>{log.accessed_by_name || 'Unknown'}</td>
                  <td>{log.access_type ? log.access_type.charAt(0).toUpperCase() + log.access_type.slice(1) : 'N/A'}</td>
                  <td>{log.created_at ? formatDateTime(log.created_at) : 'N/A'}</td>
                  <td>{log.ip_address || 'N/A'}</td>
                </tr>
              ))
            ) : (
              <tr>
                <td colSpan={4}>No access logs available</td>
              </tr>
            )}
          </tbody>
        </table>
      </div>
    </div>
  );
};

const MedicalDashboard: React.FC = () => {
  const [isMinimized, setIsMinimized] = useState(false);
  const [isChangeProfilePictureModalOpen, setChangeProfilePictureModalOpen] = useState(false);
  const navigate = useNavigate();
  const location = useLocation();

  // Get the selected patient from the store
  const { selectedPatient, isLoading } = useSelectedPatientStore();

  // Redirect to patients list if no patient is selected
  useEffect(() => {
    if (!selectedPatient && !isLoading) {
      navigate('/patients');
    }
  }, [selectedPatient, isLoading, navigate]);

  const handleToggleMinimize = () => {
    setIsMinimized(!isMinimized);
  };

  const handleSidebarItemClick = (item: SidebarItem) => {
    if (item.path) {
      navigate(item.path);
    }
  };

  // Show loading state while checking for selected patient
  if (isLoading) return <Preloader />;
  if (!selectedPatient) return <Preloader />;

  // Check if we're on a sub-route (not the main dashboard)
  const isSubRoute = location.pathname !== '/org/dashboard/patient-board';

  return (
    <div className="med-dashboard-container">
      <Sidebar
        isMinimized={isMinimized}
        onToggleMinimize={handleToggleMinimize}
        onItemClick={handleSidebarItemClick}
        selectedPatient={selectedPatient}
        currentPath={location.pathname}
        onProfilePictureClick={() => setChangeProfilePictureModalOpen(true)}
      />
      <div className={`med-main-content ${isMinimized ? 'med-main-content-expanded' : ''}`}>
        {isSubRoute ? (
          <Outlet />
        ) : (
          <>
            <AlertsSection patient={selectedPatient} />
            <VitalsSection patientUuid={selectedPatient?.uuid || ''} />
            <div className="med-content-2col-grid">
              <div className="med-content-col">
                <PatientDiary patientUuid={selectedPatient?.uuid || ''} />
                <ReportsSection />
                <Documentation patientUuid={selectedPatient?.uuid || ''} />
                <CurrentMedications patientUuid={selectedPatient?.uuid || ''} />
              </div>
              <div className="med-content-col">
                <PatientStudies patientUuid={selectedPatient?.uuid || ''} />
                <MedicalHistory patientUuid={selectedPatient?.uuid || ''} />
                <FormsQueries patientUuid={selectedPatient?.uuid || ''} />
                <Prescription patientUuid={selectedPatient?.uuid || ''} />
              </div>
            </div>
            <PatientLogs patientUuid={selectedPatient?.uuid || ''} />
          </>
        )}
      </div>
      <ChangeProfilePictureModal
        isOpen={isChangeProfilePictureModalOpen}
        onClose={() => setChangeProfilePictureModalOpen(false)}
        patientUuid={selectedPatient?.uuid}
      />
    </div>
  );
};

export default MedicalDashboard;

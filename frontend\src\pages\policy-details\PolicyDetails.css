.content-wrapper-pdf {
    padding: 20px;
    display: flex;
    flex-direction: column;
    gap: 20px;
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
    min-height: 100vh;
}

.details-policy {
    display: flex;
    flex-direction: column;
    gap: 30px;
    max-width: 1400px;
    margin: 0 auto;
    width: 100%;
}

.policy-grid {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: 30px;
    align-items: start;
    margin-top: 20px;
}

.policy-informations {
    justify-self: start;
    position: sticky;
    top: 20px;
}

.title-wrapper {
    text-align: center;
    margin-bottom: 40px;
    padding: 40px 20px;
    background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
    border-radius: 16px;
    box-shadow:
        0 4px 20px rgba(0, 0, 0, 0.08),
        0 1px 4px rgba(0, 0, 0, 0.04);
    border: 1px solid #e5e7eb;
    position: relative;
    overflow: hidden;
}

.title-wrapper::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, #00B3A6, #0891b2);
}

.details-subtitle {
    font-size: 14px;
    font-weight: 600;
    color: #00B3A6;
    margin-bottom: 12px;
    text-transform: uppercase;
    letter-spacing: 1px;
    position: relative;
}

.details-subtitle::after {
    content: '';
    position: absolute;
    bottom: -6px;
    left: 50%;
    transform: translateX(-50%);
    width: 40px;
    height: 2px;
    background: #00B3A6;
    border-radius: 1px;
}

.details-title {
    color: #140342;
    font-size: 42px;
    font-weight: 700;
    margin-bottom: 12px;
    line-height: 1.2;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
}

.details-date {
    font-size: 16px;
    color: #64748b;
    margin-bottom: 0;
    font-weight: 500;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
}

.details-date::before {
    content: '📅';
    font-size: 14px;
}

/* Enhanced PDF viewer container */
.pdf-viewer-container {
    position: relative;
    background: white;
    border-radius: 16px;
    overflow: hidden;
    box-shadow:
        0 10px 30px rgba(0, 0, 0, 0.1),
        0 4px 12px rgba(0, 0, 0, 0.05);
    border: 1px solid #e5e7eb;
    transition: all 0.3s ease;
}

.pdf-viewer-container:hover {
    box-shadow:
        0 20px 50px rgba(0, 0, 0, 0.15),
        0 8px 20px rgba(0, 0, 0, 0.08);
    transform: translateY(-2px);
}

.pdf-viewer-container::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, #00B3A6, #0891b2);
    z-index: 10;
}

/* Image styling for non-PDF attachments */
.pdf-viewer img {
    display: block;
    margin: 0 auto;
    max-height: 800px;
    max-width: 100%;
    border-radius: 12px;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s ease;
}

.pdf-viewer img:hover {
    transform: scale(1.02);
}

/* No attachment message styling */
.no-attachment-message {
    text-align: center;
    padding: 60px 20px;
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
    border-radius: 16px;
    border: 2px dashed #cbd5e1;
    color: #64748b;
    font-size: 18px;
    font-weight: 500;
}

.no-attachment-message::before {
    content: '📄';
    display: block;
    font-size: 48px;
    margin-bottom: 16px;
    opacity: 0.5;
}

/* Unsupported format message styling */
.unsupported-format-message {
    text-align: center;
    padding: 60px 20px;
    background: linear-gradient(135deg, #fef3c7 0%, #fde68a 100%);
    border-radius: 16px;
    border: 2px dashed #f59e0b;
    color: #92400e;
    font-size: 18px;
    font-weight: 500;
}

.unsupported-format-message::before {
    content: '⚠️';
    display: block;
    font-size: 48px;
    margin-bottom: 16px;
}

/* Responsive design */
@media (max-width: 1200px) {
    .details-policy {
        max-width: 100%;
        padding: 0 20px;
    }

    .policy-grid {
        gap: 25px;
    }
}

@media (max-width: 768px) {
    .content-wrapper-pdf {
        padding: 15px;
    }

    .details-policy {
        gap: 20px;
        padding: 0 10px;
    }

    .policy-grid {
        grid-template-columns: 1fr;
        gap: 20px;
    }

    .policy-informations {
        justify-self: center;
        margin-top: 0;
        position: static;
    }

    .title-wrapper {
        padding: 30px 15px;
        margin-bottom: 30px;
    }

    .details-title {
        font-size: 32px;
    }

    .details-subtitle {
        font-size: 13px;
    }

    .details-date {
        font-size: 14px;
    }

    .pdf-viewer-container:hover {
        transform: none;
    }
}

@media (max-width: 480px) {
    .title-wrapper {
        padding: 25px 10px;
    }

    .details-title {
        font-size: 28px;
    }

    .no-attachment-message,
    .unsupported-format-message {
        padding: 40px 15px;
        font-size: 16px;
    }

    .no-attachment-message::before,
    .unsupported-format-message::before {
        font-size: 36px;
        margin-bottom: 12px;
    }
}

/* Loading animation */
@keyframes pulse {
    0%, 100% {
        opacity: 1;
    }
    50% {
        opacity: 0.5;
    }
}

.loading-pulse {
    animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

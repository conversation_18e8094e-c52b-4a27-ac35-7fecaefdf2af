/* Form Card Component */
.form-card {
  position: relative;
  display: flex;
  flex-direction: column;
  background-color: white;
  border-radius: 12px;
  box-shadow: 0 4px 16px rgba(55, 183, 195, 0.08);
  overflow: hidden;
  transition: all 0.3s ease;
  height: 100%;
  border: 1px solid rgba(55, 183, 195, 0.1);
  cursor: pointer;
  outline: none;
}

.form-card:hover, .form-card:focus {
  transform: translateY(-5px);
  box-shadow: 0 8px 24px rgba(55, 183, 195, 0.15);
  border-color: var(--color-purple-1);
}

.form-card--selected {
  border-color: var(--color-purple-1);
  box-shadow: 0 0 0 2px rgba(55, 183, 195, 0.2);
}

.form-card--hovered {
  transform: translateY(-5px);
  box-shadow: 0 8px 24px rgba(55, 183, 195, 0.15);
}

.form-card__selected-indicator {
  position: absolute;
  top: 0;
  left: 0;
  width: 4px;
  height: 100%;
  background-color: var(--color-purple-1);
}

/* Checkbox */
.form-card__select {
  position: absolute;
  top: 10px;
  right: 10px;
  z-index: 10;
}

.form-card__checkbox {
  width: 24px;
  height: 24px;
  border-radius: 50%;
  background-color: white;
  border: 2px solid #cccccc;
  display: flex;
  justify-content: center;
  align-items: center;
  cursor: pointer;
  transition: all 0.2s ease;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.form-card__checkbox:hover {
  transform: scale(1.1);
  box-shadow: 0 3px 6px rgba(0, 0, 0, 0.15);
}

.form-card--selected .form-card__checkbox {
  background-color: var(--color-purple-1);
  border-color: var(--color-purple-1);
  animation: pulse 0.5s ease-in-out;
}

@keyframes pulse {
  0% { transform: scale(1); }
  50% { transform: scale(1.2); }
  100% { transform: scale(1); }
}

/* Header */
.form-card__header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  background-color: var(--color-purple-1);
  color: white;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  position: relative;
  overflow: hidden;
}

.form-card__header::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(to right, rgba(255, 255, 255, 0.1), transparent);
  transform: translateX(-100%);
  transition: transform 0.5s ease;
}

.form-card:hover .form-card__header::after {
  transform: translateX(100%);
}

.form-card__title {
  display: flex;
  align-items: center;
  gap: 10px;
  font-weight: 600;
  font-size: 15px;
  letter-spacing: 0.3px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 80%;
}

.form-card__version {
  font-size: 12px;
  background-color: rgba(255, 255, 255, 0.2);
  padding: 4px 8px;
  border-radius: 4px;
  font-weight: 500;
  transition: all 0.2s ease;
}

.form-card:hover .form-card__version {
  background-color: rgba(255, 255, 255, 0.3);
}

/* Content */
.form-card__content {
  flex: 1;
  padding: 20px;
  display: flex;
  flex-direction: column;
}

.form-card__preview {
  flex: 1;
  margin-bottom: 20px;
  display: flex;
  flex-direction: column;
  gap: 15px;
  position: relative;
  border-radius: 8px;
  overflow: hidden;
}

.form-card__line {
  height: 8px;
  background-color: rgba(55, 183, 195, 0.1);
  border-radius: 4px;
  transition: all 0.3s ease;
}

.form-card:hover .form-card__line {
  background-color: rgba(55, 183, 195, 0.15);
}

.form-card__line--title {
  width: 100%;
  height: 10px;
  background-color: rgba(55, 183, 195, 0.2);
}

.form-card:hover .form-card__line--title {
  background-color: rgba(55, 183, 195, 0.25);
}

.form-card__line--question {
  width: 80%;
  margin-bottom: 8px;
}

.form-card__line--option {
  width: 70%;
  height: 6px;
}

.form-card__question {
  margin-bottom: 10px;
}

.form-card__options {
  display: flex;
  flex-direction: column;
  gap: 8px;
  margin-left: 5px;
}

.form-card__option {
  display: flex;
  align-items: center;
  gap: 8px;
}

.form-card__radio {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  border: 2px solid rgba(55, 183, 195, 0.3);
  transition: all 0.3s ease;
}

.form-card:hover .form-card__radio {
  border-color: rgba(55, 183, 195, 0.5);
}

.form-card__input {
  width: 100%;
  height: 24px;
  background-color: rgba(55, 183, 195, 0.05);
  border-radius: 6px;
  border: 1px solid rgba(55, 183, 195, 0.2);
  transition: all 0.3s ease;
}

.form-card:hover .form-card__input {
  border-color: rgba(55, 183, 195, 0.3);
  background-color: rgba(55, 183, 195, 0.08);
}

.form-card__overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(55, 183, 195, 0.7);
  display: flex;
  justify-content: center;
  align-items: center;
  opacity: 0;
  animation: fadeIn 0.3s ease forwards;
  border-radius: 8px;
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

.form-card__overlay-button {
  background-color: white;
  border: none;
  border-radius: 8px;
  padding: 10px 20px;
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 600;
  color: var(--color-purple-1);
  cursor: pointer;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  transition: all 0.2s ease;
  transform: translateY(10px);
  animation: slideUp 0.3s ease forwards;
}

@keyframes slideUp {
  from { transform: translateY(10px); opacity: 0; }
  to { transform: translateY(0); opacity: 1; }
}

.form-card__overlay-button:hover {
  transform: scale(1.05);
  box-shadow: 0 6px 16px rgba(0, 0, 0, 0.2);
}

/* Meta Information */
.form-card__meta {
  display: flex;
  justify-content: space-between;
  font-size: 13px;
  color: var(--color-light-1);
  border-top: 1px dashed rgba(55, 183, 195, 0.15);
  padding-top: 12px;
}

.form-card__creator, 
.form-card__date {
  display: flex;
  align-items: center;
  gap: 5px;
  transition: all 0.3s ease;
}

.form-card__date {
  margin-left: 10px;
}

.calendar-icon {
  margin-right: 2px;
}

.form-card:hover .form-card__creator,
.form-card:hover .form-card__date {
  color: var(--color-dark-1);
}

.form-card__creator span {
  font-weight: 500;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 100px;
}

/* Footer */
.form-card__footer {
  display: flex;
  justify-content: flex-end;
  padding: 12px 20px;
  background-color: rgba(55, 183, 195, 0.03);
  border-top: 1px solid rgba(55, 183, 195, 0.1);
  transition: all 0.3s ease;
}

.form-card:hover .form-card__footer {
  background-color: rgba(55, 183, 195, 0.06);
}

.form-card__actions {
  display: flex;
  gap: 8px;
}

.form-card__action {
  display: flex;
  align-items: center;
  gap: 6px;
  background-color: white;
  border: 1px solid rgba(95, 106, 196, 0.2);
  border-radius: 8px;
  padding: 8px 12px;
  font-size: 13px;
  font-weight: 500;
  color: var(--color-purple-1);
  cursor: pointer;
  transition: all 0.2s ease;
}

.form-card__action:hover {
  background-color: rgba(95, 106, 196, 0.1);
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(95, 106, 196, 0.15);
}

.form-card__action--secondary {
  width: 34px;
  height: 34px;
  padding: 0;
  display: flex;
  align-items: center;
  justify-content: center;
}

.form-card__action--secondary:hover {
  background-color: rgba(95, 106, 196, 0.1);
  transform: translateY(-2px) rotate(15deg);
}

/* Responsive Adjustments */
@media (max-width: 768px) {
  .form-card__title {
    font-size: 14px;
  }
  
  .form-card__meta {
    flex-direction: column;
    gap: 8px;
  }
}

import { create } from "zustand";

export interface Recommendations {
  nursingImpressions: string[];
  doctorSpeciality: string[];
  UpcomingPlans: string[];
  seenByDoctorStatus: string;
  medicalInterventions: string[];
}

export interface PatientDetails {
  Name: string;
  medicalRecordNumber: string;
  dateofBirth: string;
  currentDate: string;
  hospitalName: string;
  departmentName: string;
}

export interface Situation {
  age: string;
  sex: string;
  modeOfArrival: string;
  situationSentence: string;
  transferFromHospital?: string;
  referredFromHospital?: string;
  referingDepartment?: string;
  mobilityOnArrival?: string;
}

interface Background {
  pregnencyStatus?: string;
  lastMenstruationPeriod?: string;
  prevPregnancies?: number;
  numberOfMisCarriages?: number;
  pregnancySentence?: string;
  timeOfOnset?: string;
  dateFromOnset?: string;
  SymptomsOnsetSentence?: string;
  preferedMethod?: string;
  pastMedicalHistorySelectedOptions?: string[];
  checkedProblems?: string[];
  reasonOfCurrentVisit?: string;
  pastMedicalHistory?: string;
  otherCheckedProblems?: OtherCheckedProblems;
}

interface OtherCheckedProblem {
  checked: boolean;
  text: string;
}

interface OtherCheckedProblems {
  [key: string]: OtherCheckedProblem;
}

export interface SoreData {
  name: string;
  size: string;
  grade: string;
  description: string;
}

export interface Exposure {
  temperature: number;
  skinCondition: string;
  sores: SoreData[];
  hasPresureSore: boolean;
  pressureSoreWasReported: boolean;
}

export interface Airway {
  assessment: string;
  interventions?: string[];
}

export interface Breathing {
  saturationOxygen: number;
  respiratoryRate: number;
  o2SupportType: string;
  litrePerMinute: number;
  fio2: number;
  respiratoryDistress: string[];
  percussions: string[];
  respiratoryNoise: string[];
  cough: string[];
  palpations: string[];
  airEntries: string[];
}

export interface Circulation {
  heartRate: number;
  bloodPressureSystolic?: number;
  bloodPressureDiastolic?: number;
  bloodPressure: string;
  capillaryRefill: string;
  CRT?: number;
  abdomen?: string[];
  bleeding?: string[];
  urineOutput?: string[];
}

export interface Disability {
  GCS: {
    eyeOpening: string;
    verbalResponse: string;
    motorResponse: string;
  };
  AVPU: string;
  bloodGlucose: number;
  rightPupil: {
    shape: string;
    responsivenessToLight: string;
    size: number;
  };
  leftPupil: {
    shape: string;
    responsivenessToLight: string;
    size: number;
  };
  rightArmMovement: string;
  leftArmMovement: string;
  rightLegMovement: string;
  leftLegMovement: string;
  limbMovement?: {
    rightArm?: string;
    leftArm?: string;
    rightLeg?: string;
    leftLeg?: string;
  };
}

export interface Injury {
  patientHasTrauma: boolean;
  injuryLocation: string;
  anyReducedMovement: string;
  decreasedSensationDesc: string[];
  anyDecreasedSensation: string;
  paresthesia: string;
  injuryLowerLimb: string;
  bearWeightOnLimb: string;
  pedalPulse: string;
  ableToMoveToes: string;
}

export interface Line {
  d1: string;
  d2: string;
  d3: string;
  d4: string;
  d5: string;
}

export interface Medication {
  d1: string;
  d2: string;
  d3: string;
  d4: string;
}

export interface Overview {
  summary: string;
  patientAwake: string;
  patientAppearance: string;
  clothingAndPharnalia: string[];
  behaviouralState: string[];
  faciesAndExpressions: string[];
  postureAndDecubitus: string[];
  odorsOfBreathAndBody: string[];
  patientHasAllergy: string;
  medications: string[];
}

export interface Pain {
  d1: string;
  d2: string;
  d3: string;
  d4: string;
  d5: string;
}

export interface MentalMedication {
  d1: string;
  d2: string;
  d3: string;
  d4: string;
}

export interface Properties {
  valuableProperties: string;
}

export interface Psychology {
  mentalStatus: string;
  mood: string;
  hasMentalHealthConcern: boolean;
  distinctiveFeatures?: string;
  clothing?: string;
  postureGait?: string;
  grooming?: string;
  facialExpression?: string;
  bodyLanguage?: string;
  abilityToFollowRequest?: string;
  rapport?: string;
  moodState?: string;
  psychomotorActivity?: string;
  levelOfArousal?: string;
  obviousSelfHarm?: string;
  suicidalPlanInPlace?: string;
  eyeContact?: string;
  alcoholAssociated?: string;
  drugsAssociated?: string;
  overdoseAssociated?: string;
  
  mentalMedications?: MentalMedication[];
  rate?: string;
  quantity?: string;
  tone?: string;
  volume?: string;
  fluencyAndRhythm?: string;
  suicidalThoughts?: string;
  suicidalPlanDesc?: string;
  PreviousSuidicalAttempt?: string;
  HarmOthers?: string;
  HomicidalThoughts?: string;
  Halucination?: string;
  HalucinationDesc?: string;
  Orientation?: string;
  AskingForHelp?: string;
  otherMentalHealthDetails?: string;
}

export interface SocialHx {
  accomodation: "";
  drinkingHistory: "";
  elimination: "";
  livesWith: "";
  mobility: "";
  packageOfCare: "";
  smokingHistory: "";
}

export interface Wound {
  anyWound: boolean;
  locationOfWound: string;
  ageOfWound: string;
  causeOfWound: string;
  typeOfWound: string;
  lengthOfWound: string;
  widthOfWound: string;
  depthOfWound: string;
  colorOfWound: string;
  surroundingSkinOfWound: string;
  exudatesOfWound: string;
  descriptionOfExudateOfWound: string;
  odorOfWound: string;
  painOfWound: string;
  healingOrClosure: string;
  conditionOfWound: string;
  otherTakenActionsOfWound: string;
}

export interface StaticAssessment {
  info: string;
}

export interface POCT {
  venousBloodGasDone: boolean;
  bloodGaz: {
    bloodVenousOrArterial: string;
    ph: string;
    paco2: string;
    pao2: string;
    hco3: string;
    baseExcess: string;
    hb: string;
    bloodGlucose: string;
    lactateLevel: string;
  };
  urineTestDone: boolean;
  urineAnalysis: {
    ph: string;
    blood: string;
    protien: string;
    glucose: string;
    ketone: string;
    specificGravity: string;
    nitrite: string;
    leukocytes: string;
  };
}

export interface Assessment {
   
  injuryTrauma: any;
  airway: Airway;
  breathing: Breathing;
  circulation: Circulation;
  disability: Disability;
  event: string[];
  exposure: Exposure;
  frailty: number;
  injury: Injury;
  lines: Line[];
  medication: Medication[];
  overview: Overview;
  pains: { pain: Pain[], hasPain: boolean };
  properties: Properties;
  psychology: Psychology;
  social_hx: SocialHx;
  wound: Wound;
  static: StaticAssessment;
  POCT: POCT;
}

export interface HolisticFormState {
  patientDetails: PatientDetails;
  situation: Situation;
  recommendations: Recommendations;
  background: Background;
  assessment: Assessment;
  whenSymptomsStarted: string;
  matchedPastMedicalHistoryOptions: string[];
}

export interface HolisticFormActions {
  setPatientDetails: (patientDetails: PatientDetails) => void;
  setSituation: (situation: Situation) => void;
  setRecommendations: (recommendations: Recommendations) => void;
  setBackground: (background: Background) => void;
  setWhenSymptomsStarted: (time: string) => void;
  setAssessment: (assessment: Assessment) => void;
  addSore: (soreData: SoreData) => void;
}

const getCurrentDate = () => {
  const today = new Date();
  const year = today.getFullYear();
  const month = (1 + today.getMonth()).toString().padStart(2, '0');
  const day = today.getDate().toString().padStart(2, '0');
  return `${year}-${month}-${day}`;
};


const useHolisticFormStore = create<HolisticFormState & HolisticFormActions>(
  (set) => ({
    patientDetails: {
      Name: "",
      medicalRecordNumber: "",
      dateofBirth: "",
      currentDate: getCurrentDate(),
      hospitalName: "",
      departmentName: "",
    },
    situation: {
      age: "14",
      sex: "",
      modeOfArrival: "",
      situationSentence: "",
      transferFromHospital: "",
      referingDepartment: "",
      mobilityOnArrival: "",
      referredFromHospital: "",
    },
    recommendations: {
      nursingImpressions: [],
      doctorSpeciality: [],
      UpcomingPlans: [],
      seenByDoctorStatus: "",
      medicalInterventions: [],
    },
    background: {
      pregnencyStatus: "",
      lastMenstruationPeriod: "",
      prevPregnancies: 0,
      numberOfMisCarriages: 0,
      pregnancySentence: "",
      timeOfOnset: "",
      pastMedicalHistorySelectedOptions: [],
      otherCheckedProblems: {
        "Other Breathing Problem": { checked: false, text: "" },
        "Other Circulation Problem": { checked: false, text: "" },
        "Other Abdominal Problem": { checked: false, text: "" },
        "Other Urination Problem": { checked: false, text: "" },
        "Other Nueral Problem": { checked: false, text: "" },
        "Other Skin Problem": { checked: false, text: "" },
        "Other Ear Problem": { checked: false, text: "" },
        "Other Eye Problem": { checked: false, text: "" },
        "Other Ob/Gyn Problem": { checked: false, text: "" },
        "Other Mental Health Problem": { checked: false, text: "" },
        "Other Injuries": { checked: false, text: "" },
        "Other": { checked: false, text: "" },
      },
    },  
    assessment: {
      injuryTrauma: {
        anyReducedMovement: "",
        decreasedSensationDesc: [],
        anyDecreasedSensation: "",
        paresthesia: "",
        injuryLowerLimb: "",
        bearWeightOnLimb: "",
        pedalPulse: "",
        ableToMoveToes: "",
        patientHasTrauma: false,
        injuryLocation: "",
      },
      airway: { assessment: "" },
      breathing: {
        saturationOxygen: 0,
        respiratoryRate: 0,
        o2SupportType: "",
        litrePerMinute: 0,
        fio2: 0,
        respiratoryDistress: [],
        percussions: [],
        respiratoryNoise: [],
        cough: [],
        palpations: [],
        airEntries: [],
      },
      circulation: { heartRate: 0, bloodPressure: "", capillaryRefill: "" },
      disability: {
        GCS: { eyeOpening: "", verbalResponse: "", motorResponse: "" },
        AVPU: "",
        bloodGlucose: 0,
        rightPupil: { shape: "", responsivenessToLight: "", size: 0 },
        leftPupil: { shape: "", responsivenessToLight: "", size: 0 },
        rightArmMovement: "",
        leftArmMovement: "",
        rightLegMovement: "",
        leftLegMovement: "",
      },
      event:[],
      exposure: {
        temperature: 0,
        skinCondition: "",
        sores: [],
        hasPresureSore: false,
        pressureSoreWasReported: false,
      },
      frailty: 0,
      injury: {
        patientHasTrauma: false,
        injuryLocation: "",
        anyReducedMovement: "",
        decreasedSensationDesc: [],
        anyDecreasedSensation: "",
        paresthesia: "",
        injuryLowerLimb: "",
        bearWeightOnLimb: "",
        pedalPulse: "",
        ableToMoveToes: "",
      },
      lines: [],
      medication: [],
      overview: {
        summary: "",
        patientAwake: "",
        patientAppearance: "",
        clothingAndPharnalia: [],
        behaviouralState: [],
        faciesAndExpressions: [],
        postureAndDecubitus: [],
        odorsOfBreathAndBody: [],
        patientHasAllergy: "",
        medications: [],
      },
      pains: { pain: [], hasPain: false },
      properties: { valuableProperties: "" },
      psychology: { mentalStatus: "", mood: "", hasMentalHealthConcern: false },
      social_hx: {
        accomodation: "",
        drinkingHistory: "",
        elimination: "",
        livesWith: "",
        mobility: "",
        packageOfCare: "",
        smokingHistory: "",
      },
      wound: {
        anyWound: false,
        locationOfWound: "",
        ageOfWound: "",
        causeOfWound: "",
        typeOfWound: "",
        lengthOfWound: "",
        widthOfWound: "",
        depthOfWound: "",
        colorOfWound: "",
        surroundingSkinOfWound: "",
        exudatesOfWound: "",
        descriptionOfExudateOfWound: "",
        odorOfWound: "",
        painOfWound: "",
        healingOrClosure: "",
        conditionOfWound: "",
        otherTakenActionsOfWound: ""
      },
      static: { info: "" },
      POCT: {
        venousBloodGasDone: false,
        bloodGaz: {
          bloodVenousOrArterial: "",
          ph: "",
          paco2: "",
          pao2: "",
          hco3: "",
          baseExcess: "",
          hb: "",
          bloodGlucose: "",
          lactateLevel: "",
        },
        urineTestDone: false,
        urineAnalysis: {
          ph: "",
          blood: "",
          protien: "",
          glucose: "",
          ketone: "",
          specificGravity: "",
          nitrite: "",
          leukocytes: "",
        },
      },
    },
    whenSymptomsStarted: "",
    matchedPastMedicalHistoryOptions: [],
    setPatientDetails: (patientDetails) => set({ patientDetails }),
    setSituation: (situation) => set({ situation }),
    setRecommendations: (recommendations) => set({ recommendations }),
    setBackground: (background) => set({ background }),
    setWhenSymptomsStarted: (time: string) => set({ whenSymptomsStarted: time }),
    setAssessment: (assessment: Assessment) => set({ assessment }),
    addSore: (soreData: SoreData) =>
      set((state) => ({
        assessment: {
          ...state.assessment,
          exposure: {
            ...state.assessment.exposure,
            sores: [...state.assessment.exposure.sores, soreData],
          },
        },
      })),
  })
);

export default useHolisticFormStore;

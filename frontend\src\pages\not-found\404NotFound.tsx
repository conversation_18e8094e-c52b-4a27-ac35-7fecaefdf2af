import { useNavigate } from "react-router-dom";
import "./404NotFound.css";

export default function NotFoundPage() {
  const navigate = useNavigate();
  return (
    <div className="not-found-container">
      <div className="not-found-background">
        <div className="not-found-content">
          <div className="not-found-left">
            <div className="error-text">
              <h1 className="error-404">Error 404</h1>
              <h2 className="page-not-found">Page not found!</h2>
              <button className="go-back-button" onClick={() => navigate('/home')}>Go Back</button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
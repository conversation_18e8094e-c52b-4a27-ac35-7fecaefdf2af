import React from "react";
import { motion } from "framer-motion";

interface CardItemV2Props {
  title: string;
  image: string;
  style?: React.CSSProperties;
}

const CardItemV2: React.FC<CardItemV2Props> = ({ title, image, style }) => {
  return (
    <motion.div
      className="card-item d-flex card-item--style-1"
      style={{
        gap: "10px",
        border: "1px solid #E4E4E7",
        padding: "24px 15px",
        ...style,
      }}
      initial={{ opacity: 0, y: 20 }}
      whileInView={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.6 }}
      viewport={{ once: true, amount: 0.5 }}
    >
      <motion.div
        className="card-item__image"
        initial={{ scale: 0.8 }}
        whileInView={{ scale: 1 }}
        transition={{ duration: 0.4, delay: 0.2 }}
      >
        <img src={image} alt={title} />
      </motion.div>
      <div className="card-item__content">
        <div className="d-flex items-center justify-start">
          <div className="text-14 lh-1 text-yellow-1 mr-10">4.6</div>
          <div className="d-flex x-gap-5 items-center">
            <div className="icon-star text-9 text-yellow-1"></div>
            <div className="icon-star text-9 text-yellow-1"></div>
            <div className="icon-star text-9 text-yellow-1"></div>
            <div className="icon-star text-9 text-yellow-1"></div>
            <div className="icon-star text-9 text-yellow-1"></div>
          </div>
          <div className="text-13 lh-1 ml-10">(3,456)</div>
        </div>
        <motion.h3
          className="card-item__title"
          style={{
            color: "#18181B",
            fontSize: "16px",
            fontStyle: "normal",
            fontWeight: 600,
            lineHeight: "26px",
          }}
          initial={{ opacity: 0, y: 10 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.4, delay: 0.3 }}
        >
          {title}
        </motion.h3>
        <div className="d-flex x-gap-10 items-center justify-start pt-10">
          <div className="d-flex items-center">
            <div className="mr-8">
              <img src="assets/img/coursesCards/icons/1.svg" alt="icon" />
            </div>
            <div className="text-14 lh-1">18 lessons</div>
          </div>

          <div className="d-flex items-center">
            <div className="mr-8">
              <img src="assets/img/coursesCards/icons/3.svg" alt="icon" />
            </div>
            <div className="text-14 lh-1">Intermediate</div>
          </div>
        </div>

        <div
          className="d-flex justify-content-around align-items-start pt-4"
          style={{ gap: "15px" }}
        >
          <div className="d-flex items-center">
            <div className="mr-8">
              <img src="/assets/img/home-1/hero/ali.png" alt="icon" />
            </div>
            <div className="text-14 lh-1">Ali tufan</div>
          </div>

          <div className="d-flex items-center">
            <div className="mr-8">
              <img src="assets/img/coursesCards/icons/3.svg" alt="icon" />
            </div>
            <div className="text-14 lh-1">179$</div>
          </div>
        </div>
      </div>
    </motion.div>
  );
};

export default CardItemV2;

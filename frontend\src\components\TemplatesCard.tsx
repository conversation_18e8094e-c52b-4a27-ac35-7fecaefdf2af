import React from 'react';
import { Edit, Link2, User, Calendar, Eye, Check, FileText, FilePlus } from 'lucide-react';
import { useNavigate } from 'react-router-dom';

interface FormCardProps {
  label: string;
  onClick?: () => void;
  link?: string;
  createdBy?: string;
  createdAt?: string;
  formVersion?: string;
  showActions?: boolean;
  formUuid?: string;
  selectable?: boolean;
  isSelected?: boolean;
  onSelect?: (formUuid: string) => void;
}

const TemplatesCard: React.FC<FormCardProps> = ({ 
  label,  
  link,
  createdBy,
  createdAt,
  formVersion,
  showActions = false,
  formUuid = '',
  selectable = false,
  isSelected = false,
  onSelect
}) => {
  const navigate = useNavigate();
  
  const handleClick = () => {
    if (formUuid) {
      navigate(`/form/preview/${formUuid}`);
    } else if (link) {
      navigate(link);
    }
  };
  
  const handlePreviewClick = (e: React.MouseEvent) => {
    e.stopPropagation();
    navigate(`/form/preview/${formUuid}`);
  };

  const handleEditClick = (e: React.MouseEvent) => {
    e.stopPropagation();
    navigate(`/survey-forms/edit/${formUuid}`);
  };

  const handleNewVersionClick = (e: React.MouseEvent) => {
    e.stopPropagation();
    navigate(`/survey-forms/new?name=${encodeURIComponent(label)}&baseForm=${formUuid}`);
  };
  
  const handleShareClick = (e: React.MouseEvent) => {
    e.stopPropagation();
    const url = `${window.location.origin}/form/preview/${formUuid}`;
    navigator.clipboard.writeText(url)
      .then(() => {
      })
      .catch(err => {
        console.error('Error copying form link: ', err);
      });
  };
  
  
  const handleSelectClick = (e: React.MouseEvent) => {
    e.stopPropagation();
    if (onSelect && formUuid) {
      onSelect(formUuid);
    }
  };
  
  return (
    <div
      className="card-container"
      onClick={handleClick}
      style={{
        display: 'flex',
        flexDirection: 'column',
        padding: '0',
        backgroundColor: 'white',
        borderRadius: '16px',
        boxShadow: '0 8px 20px rgba(55, 183, 195, 0.12)',
        cursor: 'pointer',
        transition: 'all 0.3s ease',
        height: '100%',
        position: 'relative',
        border: '1px solid rgba(55, 183, 195, 0.15)',
        overflow: 'hidden',
      }}
      onMouseEnter={(e) => {
        e.currentTarget.style.transform = 'translateY(-5px)';
        e.currentTarget.style.boxShadow = '0 15px 30px rgba(55, 183, 195, 0.2)';
        e.currentTarget.style.borderColor = 'var(--color-purple-1)';
      }}
      onMouseLeave={(e) => {
        e.currentTarget.style.transform = 'translateY(0)';
        e.currentTarget.style.boxShadow = '0 8px 20px rgba(55, 183, 195, 0.12)';
        e.currentTarget.style.borderColor = 'rgba(55, 183, 195, 0.15)';
      }}
    >
      {selectable && (
        <div 
          onClick={handleSelectClick}
          style={{ 
            position: 'absolute', 
            top: '10px', 
            right: '10px',
            width: '24px',
            height: '24px',
            borderRadius: '50%',
            backgroundColor: isSelected ? '#4CAF50' : '#ffffff',
            border: isSelected ? 'none' : '2px solid #cccccc',
            display: 'flex',
            justifyContent: 'center',
            alignItems: 'center',
            cursor: 'pointer',
            zIndex: 10
          }}
        >
          {isSelected && <Check size={16} color="#ffffff" />}
        </div>
      )}
      
      {/* Card Header - Form Title Bar */}
      <div style={{ 
        display: 'flex',
        justifyContent: 'space-between',
        alignItems: 'center',
        padding: '16px 20px',
        backgroundColor: 'var(--color-purple-1)',
        color: 'white',
        borderBottom: '1px solid rgba(255, 255, 255, 0.1)',
        position: 'relative'
      }}>
        <div style={{
          display: 'flex',
          alignItems: 'center',
          gap: '10px'
        }}>
          <FileText size={18} />
          <span style={{ 
            fontWeight: '600', 
            fontSize: '16px',
            letterSpacing: '0.3px'
          }}>{label}</span>
        </div>
        <div style={{
          fontSize: '12px',
          backgroundColor: 'rgba(255, 255, 255, 0.2)',
          padding: '4px 8px',
          borderRadius: '4px',
          fontWeight: '500'
        }}>
          V{formVersion || "1.0"}
        </div>
      </div>
      
      {/* Card Content - Form Preview */}
      <div style={{ padding: '20px' }}>
        {/* Form Elements Preview */}
        <div style={{ 
          marginBottom: '20px',
          display: 'flex',
          flexDirection: 'column',
          gap: '10px'
        }}>
          {/* Question 1 Preview */}
          <div style={{
            width: '100%',
            height: '8px',
            backgroundColor: 'rgba(55, 183, 195, 0.2)',
            borderRadius: '4px'
          }}></div>
          
          {/* Multiple Choice Preview */}
          <div style={{
            display: 'flex',
            flexDirection: 'column',
            gap: '6px'
          }}>
            <div style={{
              width: '70%',
              height: '6px',
              backgroundColor: 'rgba(55, 183, 195, 0.15)',
              borderRadius: '3px'
            }}></div>
            <div style={{
              display: 'flex',
              gap: '8px'
            }}>
              <div style={{
                width: '12px',
                height: '12px',
                borderRadius: '50%',
                border: '2px solid rgba(55, 183, 195, 0.3)'
              }}></div>
              <div style={{
                width: '40%',
                height: '6px',
                backgroundColor: 'rgba(55, 183, 195, 0.1)',
                borderRadius: '3px'
              }}></div>
            </div>
            <div style={{
              display: 'flex',
              gap: '8px'
            }}>
              <div style={{
                width: '12px',
                height: '12px',
                borderRadius: '50%',
                border: '2px solid rgba(55, 183, 195, 0.3)'
              }}></div>
              <div style={{
                width: '60%',
                height: '6px',
                backgroundColor: 'rgba(55, 183, 195, 0.1)',
                borderRadius: '3px'
              }}></div>
            </div>
          </div>
          
          {/* Question 2 Preview */}
          <div style={{
            width: '90%',
            height: '8px',
            backgroundColor: 'rgba(55, 183, 195, 0.2)',
            borderRadius: '4px',
            marginTop: '6px'
          }}></div>
          
          {/* Text Input Preview */}
          <div style={{
            width: '100%',
            height: '24px',
            backgroundColor: 'rgba(55, 183, 195, 0.05)',
            borderRadius: '6px',
            border: '1px solid rgba(55, 183, 195, 0.2)'
          }}></div>
        </div>
        
        {/* Metadata */}
        <div style={{ 
          fontSize: '13px',
          color: 'var(--color-light-1)',
          display: 'flex',
          justifyContent: 'space-between',
          borderTop: '1px dashed rgba(55, 183, 195, 0.15)',
          paddingTop: '12px'
        }}>
          <div style={{ display: 'flex', alignItems: 'center' }}>
            <User size={14} style={{ marginRight: '5px', opacity: 0.7 }} />
            <span style={{ fontWeight: '500' }}>{createdBy}</span>
          </div>
          <div style={{ display: 'flex', alignItems: 'center' }}>
            <Calendar size={14} style={{ marginRight: '5px', opacity: 0.7 }} />
            <span>{createdAt}</span>
          </div>
        </div>
      </div>

      {/* Card Footer - Actions */}
      <div style={{ 
        display: 'flex', 
        gap: '10px', 
        padding: '12px 20px',
        justifyContent: 'flex-end',
        backgroundColor: 'rgba(55, 183, 195, 0.03)',
        borderTop: '1px solid rgba(55, 183, 195, 0.1)'
      }}>
        <div 
          onClick={handlePreviewClick} 
          style={{ 
            transition: 'all 0.2s ease',
            backgroundColor: 'white',
            borderRadius: '8px',
            width: '34px',
            height: '34px',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            boxShadow: '0 2px 6px rgba(95, 106, 196, 0.15)',
            border: '1px solid rgba(95, 106, 196, 0.2)'
          }}
          onMouseEnter={(e) => {
            e.currentTarget.style.transform = 'scale(1.1)';
            e.currentTarget.style.backgroundColor = 'rgba(95, 106, 196, 0.2)';
          }}
          onMouseLeave={(e) => {
            e.currentTarget.style.transform = 'scale(1)';
            e.currentTarget.style.backgroundColor = 'rgba(95, 106, 196, 0.1)';
          }}
        >
          <Eye size={20} color="#5F6AC4" style={{ cursor: 'pointer' }} />
        </div>
        
        <div 
          onClick={handleShareClick} 
          style={{ 
            transition: 'all 0.2s ease',
            backgroundColor: 'white',
            borderRadius: '8px',
            width: '34px',
            height: '34px',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            boxShadow: '0 2px 6px rgba(109, 74, 254, 0.15)',
            border: '1px solid rgba(109, 74, 254, 0.2)'
          }}
          onMouseEnter={(e) => {
            e.currentTarget.style.transform = 'scale(1.1)';
            e.currentTarget.style.backgroundColor = 'rgba(109, 74, 254, 0.2)';
          }}
          onMouseLeave={(e) => {
            e.currentTarget.style.transform = 'scale(1)';
            e.currentTarget.style.backgroundColor = 'rgba(109, 74, 254, 0.1)';
          }}
        >
          <Link2 size={20} color="#6D4AFE" style={{ cursor: 'pointer' }} />
        </div>

        {showActions && (
          <>
            <div 
              onClick={handleEditClick} 
              style={{ 
                transition: 'all 0.2s ease',
                backgroundColor: 'white',
                borderRadius: '8px',
                width: '34px',
                height: '34px',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                boxShadow: '0 2px 6px rgba(0, 150, 136, 0.15)',
                border: '1px solid rgba(0, 150, 136, 0.2)'
              }}
              onMouseEnter={(e) => {
                e.currentTarget.style.transform = 'scale(1.1)';
                e.currentTarget.style.backgroundColor = 'rgba(0, 150, 136, 0.2)';
              }}
              onMouseLeave={(e) => {
                e.currentTarget.style.transform = 'scale(1)';
                e.currentTarget.style.backgroundColor = 'rgba(0, 150, 136, 0.1)';
              }}
            >
              <Edit size={20} color="#009688" style={{ cursor: 'pointer' }} />
            </div>
            <div 
              onClick={handleNewVersionClick} 
              style={{ 
                transition: 'all 0.2s ease',
                backgroundColor: 'white',
                borderRadius: '8px',
                width: '34px',
                height: '34px',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                boxShadow: '0 2px 6px rgba(255, 152, 0, 0.15)',
                border: '1px solid rgba(255, 152, 0, 0.2)'
              }}
              onMouseEnter={(e) => {
                e.currentTarget.style.transform = 'scale(1.1)';
                e.currentTarget.style.backgroundColor = 'rgba(255, 152, 0, 0.2)';
              }}
              onMouseLeave={(e) => {
                e.currentTarget.style.transform = 'scale(1)';
                e.currentTarget.style.backgroundColor = 'rgba(255, 152, 0, 0.1)';
              }}
            >
              <FilePlus size={20} color="#FF9800" style={{ cursor: 'pointer' }} />
            </div>
          </>
        )}
      </div>
    </div>
  );
};

export default TemplatesCard;

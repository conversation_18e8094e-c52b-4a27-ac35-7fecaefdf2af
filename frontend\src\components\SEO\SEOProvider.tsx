import React, { createContext, useContext, useEffect, useState } from 'react';
import { useLocation } from 'react-router-dom';

interface SEOContextType {
  updateSEO: (seoData: SEOData) => void;
  currentSEO: SEOData | null;
}

interface SEOData {
  title?: string;
  description?: string;
  keywords?: string;
  image?: string;
  canonical?: string;
  noIndex?: boolean;
  noFollow?: boolean;
  structuredData?: object | object[];
}

const SEOContext = createContext<SEOContextType | undefined>(undefined);

export const useSEO = () => {
  const context = useContext(SEOContext);
  if (!context) {
    throw new Error('useSEO must be used within SEOProvider');
  }
  return context;
};

interface SEOProviderProps {
  children: React.ReactNode;
}

export const SEOProvider: React.FC<SEOProviderProps> = ({ children }) => {
  const [currentSEO, setCurrentSEO] = useState<SEOData | null>(null);
  const location = useLocation();

  const updateSEO = (seoData: SEOData) => {
    setCurrentSEO(seoData);
  };

  // Reset SEO data when route changes
  useEffect(() => {
    setCurrentSEO(null);
  }, [location.pathname]);

  return (
    <SEOContext.Provider value={{ updateSEO, currentSEO }}>
      {children}
    </SEOContext.Provider>
  );
};

export default SEOProvider;

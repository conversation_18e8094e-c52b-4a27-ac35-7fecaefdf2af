/* Delegation Review Modal Styles - Prefixed with .delegation-review-modal- */

/* Modal Overlay */
.delegation-review-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.6);
  backdrop-filter: blur(4px);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: 20px;
  animation: delegation-review-modal-fadeIn 0.3s ease-out;
}

@keyframes delegation-review-modal-fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

/* Main Modal Container */
.delegation-review-modal-container {
  background: var(--color-white);
  border-radius: 16px;
  box-shadow: 0 25px 80px rgba(0, 0, 0, 0.2);
  width: 100%;
  max-width: 700px;
  max-height: 90vh;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  animation: delegation-review-modal-slideUp 0.4s cubic-bezier(0.34, 1.56, 0.64, 1);
  border: 1px solid var(--color-light-2);
}

@keyframes delegation-review-modal-slideUp {
  from {
    opacity: 0;
    transform: translateY(30px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

/* Modal Header */
.delegation-review-modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 28px 36px;
  border-bottom: 1px solid var(--color-light-2);
  position: relative;
}

.delegation-review-modal-header.approve {
  background: linear-gradient(135deg, var(--color-green-4) 0%, var(--color-green-5) 100%);
  color: white;
}

.delegation-review-modal-header.reject {
  background: linear-gradient(135deg, var(--color-red-1) 0%, var(--color-orange-1) 100%);
  color: white;
}

.delegation-review-modal-header::before {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
}

.delegation-review-modal-header-content {
  display: flex;
  align-items: center;
  gap: 12px;
}

.delegation-review-modal-header-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.2);
}

.delegation-review-modal-header h2 {
  margin: 0;
  font-size: var(--text-24);
  font-weight: 700;
  font-family: var(--font-primary);
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.delegation-review-modal-close-button {
  background: none;
  border: none;
  color: white;
  cursor: pointer;
  padding: 10px;
  border-radius: 8px;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  display: flex;
  align-items: center;
  justify-content: center;
}

.delegation-review-modal-close-button:hover {
  background-color: rgba(255, 255, 255, 0.15);
  transform: scale(1.05);
}

.delegation-review-modal-close-button:active {
  transform: scale(0.95);
}

.delegation-review-modal-close-button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  transform: none !important;
}

/* Modal Content */
.delegation-review-modal-content {
  flex: 1;
  overflow-y: auto;
  padding: 36px;
  background: var(--color-white);
}

.delegation-review-modal-content::-webkit-scrollbar {
  width: 6px;
}

.delegation-review-modal-content::-webkit-scrollbar-track {
  background: var(--color-light-3);
  border-radius: 3px;
}

.delegation-review-modal-content::-webkit-scrollbar-thumb {
  background: var(--color-light-1);
  border-radius: 3px;
}

.delegation-review-modal-content::-webkit-scrollbar-thumb:hover {
  background: var(--color-purple-1);
}

.delegation-review-modal-description {
  color: var(--color-light-1);
  margin-bottom: 28px;
  line-height: 1.6;
  font-size: var(--text-15);
  padding: 16px 20px;
  background: var(--color-light-6);
  border-radius: 10px;
  border-left: 4px solid var(--color-purple-1);
}

/* Application Details Section */
.delegation-review-modal-details {
  background: linear-gradient(135deg, var(--color-light-6) 0%, var(--color-white) 100%);
  padding: 24px;
  border-radius: 12px;
  margin-bottom: 28px;
  border: 2px solid var(--color-light-2);
  position: relative;
  overflow: hidden;
}

.delegation-review-modal-details::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, var(--color-purple-1), var(--color-blue-1));
}

.delegation-review-modal-detail-row {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  padding: 12px 0;
  border-bottom: 1px solid var(--color-light-2);
  gap: 16px;
}

.delegation-review-modal-detail-row:last-child {
  border-bottom: none;
  margin-bottom: 0;
}

.delegation-review-modal-detail-label {
  font-weight: 600;
  color: var(--color-dark-1);
  font-size: var(--text-14);
  min-width: 120px;
  flex-shrink: 0;
}

.delegation-review-modal-detail-value {
  color: var(--color-dark-1);
  font-size: var(--text-14);
  line-height: 1.5;
  flex: 1;
  text-align: right;
  word-break: break-word;
}

/* Notes Section */
.delegation-review-modal-notes-section {
  margin-bottom: 28px;
}

.delegation-review-modal-notes-label {
  display: block;
  margin-bottom: 12px;
  font-weight: 600;
  color: var(--color-dark-1);
  font-size: var(--text-16);
  font-family: var(--font-primary);
}

.delegation-review-modal-notes-textarea {
  width: 100%;
  min-height: 120px;
  padding: 16px 20px;
  border: 2px solid var(--color-light-2);
  border-radius: 12px;
  font-size: var(--text-14);
  font-family: var(--font-primary);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  background: var(--color-white);
  color: var(--color-dark-1);
  resize: vertical;
  line-height: 1.5;
}

.delegation-review-modal-notes-textarea:focus {
  outline: none;
  border-color: var(--color-purple-1);
  box-shadow: 0 0 0 4px rgba(55, 183, 195, 0.1);
  transform: translateY(-1px);
}

.delegation-review-modal-notes-textarea::placeholder {
  color: var(--color-light-1);
  font-style: italic;
}

.delegation-review-modal-notes-textarea:disabled {
  background: var(--color-light-3);
  cursor: not-allowed;
  opacity: 0.7;
}

.delegation-review-modal-notes-help {
  margin-top: 8px;
  font-size: var(--text-13);
  color: var(--color-light-1);
  line-height: 1.4;
}

/* Modal Footer */
.delegation-review-modal-footer {
  padding: 28px 36px;
  border-top: 1px solid var(--color-light-2);
  background: linear-gradient(135deg, var(--color-light-6) 0%, var(--color-white) 100%);
}

.delegation-review-modal-footer-buttons {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  gap: 16px;
}

/* Button Styles */
.delegation-review-modal-btn {
  padding: 14px 28px;
  border-radius: 10px;
  font-weight: 600;
  cursor: pointer;
  border: none;
  display: flex;
  align-items: center;
  gap: 10px;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  font-size: var(--text-14);
  font-family: var(--font-primary);
  position: relative;
  overflow: hidden;
  min-width: 120px;
  justify-content: center;
}

.delegation-review-modal-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s;
}

.delegation-review-modal-btn:hover::before {
  left: 100%;
}

.delegation-review-modal-btn-cancel {
  background: linear-gradient(135deg, var(--color-light-1) 0%, var(--color-dark-3) 100%);
  color: white;
  box-shadow: 0 4px 16px rgba(111, 122, 153, 0.3);
}

.delegation-review-modal-btn-cancel:hover:not(:disabled) {
  background: linear-gradient(135deg, var(--color-dark-3) 0%, var(--color-dark-4) 100%);
  transform: translateY(-2px);
  box-shadow: 0 8px 24px rgba(111, 122, 153, 0.4);
}

.delegation-review-modal-btn-approve {
  background: linear-gradient(135deg, var(--color-green-4) 0%, var(--color-green-5) 100%);
  color: white;
  box-shadow: 0 4px 16px rgba(4, 214, 151, 0.3);
}

.delegation-review-modal-btn-approve:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 8px 24px rgba(4, 214, 151, 0.4);
}

.delegation-review-modal-btn-reject {
  background: linear-gradient(135deg, var(--color-red-1) 0%, var(--color-orange-1) 100%);
  color: white;
  box-shadow: 0 4px 16px rgba(240, 30, 0, 0.3);
}

.delegation-review-modal-btn-reject:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 8px 24px rgba(240, 30, 0, 0.4);
}

.delegation-review-modal-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none !important;
  box-shadow: none !important;
}

.delegation-review-modal-btn:active:not(:disabled) {
  transform: translateY(0);
}

/* Loading State */
.delegation-review-modal-loading {
  display: inline-flex;
  align-items: center;
  gap: 8px;
}

.delegation-review-modal-spinner {
  width: 16px;
  height: 16px;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-top: 2px solid white;
  border-radius: 50%;
  animation: delegation-review-modal-spin 1s linear infinite;
}

@keyframes delegation-review-modal-spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Error State */
.delegation-review-modal-error {
  display: flex;
  align-items: center;
  gap: 10px;
  padding: 16px 20px;
  background: linear-gradient(135deg, var(--color-error-1) 0%, var(--color-red-2) 100%);
  border: 2px solid var(--color-error-2);
  border-radius: 10px;
  color: var(--color-error-2);
  margin-top: 20px;
  font-weight: 500;
  font-size: var(--text-14);
}

/* Responsive Design */
@media (max-width: 1024px) {
  .delegation-review-modal-container {
    max-width: 95vw;
    margin: 10px;
  }
}

@media (max-width: 768px) {
  .delegation-review-modal-container {
    max-height: 95vh;
    border-radius: 12px;
  }

  .delegation-review-modal-header,
  .delegation-review-modal-content,
  .delegation-review-modal-footer {
    padding: 20px 24px;
  }

  .delegation-review-modal-header h2 {
    font-size: var(--text-20);
  }

  .delegation-review-modal-detail-row {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }

  .delegation-review-modal-detail-label {
    min-width: auto;
  }

  .delegation-review-modal-detail-value {
    text-align: left;
  }

  .delegation-review-modal-footer-buttons {
    flex-direction: column;
    gap: 12px;
  }

  .delegation-review-modal-btn {
    width: 100%;
    justify-content: center;
  }
}

@media (max-width: 480px) {
  .delegation-review-modal-content {
    padding: 16px 20px;
  }

  .delegation-review-modal-details {
    padding: 20px;
  }

  .delegation-review-modal-btn {
    padding: 12px 20px;
    font-size: var(--text-13);
  }

  .delegation-review-modal-header-icon {
    width: 36px;
    height: 36px;
  }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .delegation-review-modal-container {
    border: 3px solid var(--color-dark-1);
  }

  .delegation-review-modal-notes-textarea:focus {
    border-width: 3px;
  }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  .delegation-review-modal-container,
  .delegation-review-modal-overlay,
  .delegation-review-modal-btn,
  .delegation-review-modal-spinner {
    animation: none;
    transition: none;
  }
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
  .delegation-review-modal-container {
    background: var(--color-dark-4);
    border-color: var(--color-dark-3);
  }
  
  .delegation-review-modal-content {
    background: var(--color-dark-4);
  }
  
  .delegation-review-modal-details {
    background: linear-gradient(135deg, var(--color-dark-3) 0%, var(--color-dark-4) 100%);
    border-color: var(--color-dark-3);
  }
  
  .delegation-review-modal-description {
    background: var(--color-dark-3);
  }
  
  .delegation-review-modal-notes-textarea {
    background: var(--color-dark-4);
    border-color: var(--color-dark-3);
    color: var(--color-white);
  }
  
  .delegation-review-modal-footer {
    background: linear-gradient(135deg, var(--color-dark-3) 0%, var(--color-dark-4) 100%);
  }
}

/* Focus styles for accessibility */
.delegation-review-modal-close-button:focus,
.delegation-review-modal-notes-textarea:focus,
.delegation-review-modal-btn:focus {
  outline: 2px solid var(--color-purple-1);
  outline-offset: 2px;
}

/* Print styles */
@media print {
  .delegation-review-modal-overlay {
    position: static;
    background: none;
    backdrop-filter: none;
  }
  
  .delegation-review-modal-container {
    box-shadow: none;
    border: 1px solid #ccc;
    max-width: none;
    max-height: none;
  }
  
  .delegation-review-modal-close-button,
  .delegation-review-modal-footer {
    display: none;
  }
}

/* Documentation Sidebar - Base Styles */
.documentation-sidebar {
  position: fixed;
  top: 50%;
  right: 0;
  transform: translateY(-50%);
  z-index: 1000;
  transition: all 0.3s ease;
}

/* Collapsed State */
.documentation-sidebar.collapsed {
  width: 50px;
  background-color: #4a4a7c; /* Purple/blue background */
  border-radius: 8px 0 0 8px;
  box-shadow: -2px 0 10px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

/* Expanded State */
.documentation-sidebar:not(.collapsed) {
  width: 240px;
  background-color: #ffffff;
  border: 1px solid #4a4a7c;
  border-radius: 8px 0 0 8px;
  box-shadow: -2px 0 10px rgba(0, 0, 0, 0.1);
}

/* Toggle Button */
.doc-toggle-button {
  width: 30px;
  height: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  color: #ffffff;
  background-color: #4a4a7c;
  border-radius: 4px;
  position: absolute;
}

/* Toggle Button - Collapsed State */
.documentation-sidebar.collapsed .doc-toggle-button {
  top: 10px;
  left: 10px;
}

/* Toggle Button - Expanded State */
.documentation-sidebar:not(.collapsed) .doc-toggle-button {
  top: 10px;
  left: 10px;
}

/* Icons Container - Collapsed State */
.doc-icons-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding-top: 50px;
  height: 100%;
}

.doc-icon {
  width: 30px;
  height: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 20px;
  color: #ffffff;
  cursor: pointer;
}

.doc-icon:hover {
  color: #e0e0e0;
}

/* Content Container - Expanded State */
.doc-content-container {
  padding: 50px 15px 15px;
}

/* Tabs */
.doc-tabs {
  display: flex;
  margin-bottom: 20px;
  border-bottom: 1px solid #e0e0e0;
}

.doc-tab {
  padding: 8px 12px;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
  color: #555;
}

.doc-tab.active {
  color: #4a4a7c;
  border-bottom: 2px solid #4a4a7c;
}

/* Toggle Row */
.doc-toggle-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 0;
  margin-bottom: 15px;
  border-bottom: 1px solid #e0e0e0;
}

.doc-toggle-label {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
  color: #333;
}

.doc-toggle {
  position: relative;
  width: 50px;
  height: 24px;
  background-color: #e0e0e0;
  border-radius: 12px;
  cursor: pointer;
  display: flex;
  align-items: center;
  padding: 0 4px;
  justify-content: flex-end;
}

.doc-toggle.active {
  background-color: #4a4a7c;
  justify-content: flex-start;
}

.doc-toggle-slider {
  width: 18px;
  height: 18px;
  background-color: #fff;
  border-radius: 50%;
}

.doc-toggle span {
  position: absolute;
  right: -30px;
  font-size: 12px;
  color: #555;
}

/* Menu Items */
.doc-menu-item {
  display: flex;
  align-items: center;
  gap: 10px;
  padding: 10px 0;
  cursor: pointer;
  color: #333;
  font-size: 14px;
  border-bottom: 1px solid #e0e0e0;
}

.doc-menu-item:hover {
  color: #4a4a7c;
}

/* Mobile Adjustments */
@media (max-width: 991px) {
  .documentation-sidebar {
    top: auto;
    bottom: 70px;
    transform: none;
  }
  
  .documentation-sidebar.collapsed {
    width: 40px;
  }
  
  .documentation-sidebar:not(.collapsed) {
    width: 220px;
  }
}

/* SDK Page Styles */
.content-wrapper-sdk {
  padding-top: 80px;
  min-height: calc(100vh - 80px);
  display: flex;
  flex-direction: column;
}

/* SDK Header */
.sdk-header {
  background: linear-gradient(135deg, #37b7c3 0%, #2a9a9f 100%);
  padding: 60px 0 40px;
  color: white;
  margin-bottom: 40px;
}

.sdk-title {
  font-size: 36px;
  font-weight: 700;
  margin-bottom: 16px;
}

.sdk-description {
  font-size: 18px;
  max-width: 700px;
  margin-bottom: 30px;
  opacity: 0.9;
}

.sdk-search-container {
  max-width: 600px;
  margin-bottom: 20px;
}

.sdk-search {
  width: 100%;
  padding: 14px 20px;
  border-radius: 8px;
  border: none;
  font-size: 16px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.sdk-search:focus {
  outline: none;
  box-shadow: 0 6px 16px rgba(0, 0, 0, 0.15);
}

/* SDK Navigation */
.sdk-navigation {
  display: flex;
  flex-wrap: wrap;
  gap: 12px;
  margin-bottom: 30px;
}

.sdk-nav-item {
  padding: 10px 20px;
  border-radius: 30px;
  background-color: #f8f9fa;
  border: 1px solid #e9ecef;
  color: #6c757d;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.sdk-nav-item:hover {
  background-color: #f1f3f5;
  border-color: #dee2e6;
}

.sdk-nav-item.active {
  background-color: #37b7c3;
  border-color: #37b7c3;
  color: white;
}

/* Component Cards */
.component-card {
  background-color: white;
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
  padding: 24px;
  margin-bottom: 30px;
  transition: all 0.3s ease;
}

.component-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.1);
}

.component-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 16px;
  border-bottom: 1px solid #e9ecef;
}

.component-title {
  font-size: 18px;
  font-weight: 600;
  color: #343a40;
}

.component-category {
  font-size: 12px;
  font-weight: 500;
  color: white;
  background-color: #37b7c3;
  padding: 4px 10px;
  border-radius: 20px;
}

.component-content {
  margin-bottom: 20px;
}

.component-description {
  font-size: 14px;
  color: #6c757d;
  margin-bottom: 16px;
}

.component-code {
  background-color: #f8f9fa;
  border-radius: 8px;
  padding: 16px;
  font-family: monospace;
  font-size: 14px;
  color: #343a40;
  overflow-x: auto;
}

/* Component Grid */
.component-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 30px;
  margin-bottom: 40px;
}

/* No Results */
.no-results {
  text-align: center;
  padding: 60px 0;
  color: #6c757d;
}

.no-results-icon {
  font-size: 48px;
  margin-bottom: 20px;
  color: #dee2e6;
}

.no-results-text {
  font-size: 18px;
  margin-bottom: 10px;
}

.no-results-subtext {
  font-size: 14px;
  color: #adb5bd;
}

/* Button Styles for SDK */
.nurtify-button {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  border: none;
  cursor: pointer;
  font-family: inherit;
  font-weight: 500;
  text-decoration: none;
  transition: all 200ms ease-out;
  border-radius: 35px;
  gap: 10px;
  opacity: 1;
  outline: none;
  position: relative;
  margin: 4px;
}

.nurtify-button:focus {
  outline: 2px solid rgba(55, 183, 195, 0.3);
  outline-offset: 2px;
}

/* Size variants */
.nurtify-button--small {
  height: 48px;
  padding: 12px 32px;
  font-size: 14px;
  min-width: 120px;
}

.nurtify-button--regular {
  height: 48px;
  padding: 12px 40px;
  font-size: 14px;
  min-width: 140px;
}

.nurtify-button--medium {
  height: 48px;
  padding: 12px 48px;
  font-size: 16px;
  min-width: 159px;
}

/* Primary variant */
.nurtify-button--primary {
  background: #37B7C3;
  color: white;
}

.nurtify-button--primary:hover:not(.nurtify-button--disabled) {
  background: #088395;
  transform: translateY(-1px);
}

/* Success variant */
.nurtify-button--success {
  background: #34C759;
  color: white;
}

.nurtify-button--success:hover:not(.nurtify-button--disabled) {
  background: #28a745;
  transform: translateY(-1px);
}

/* Danger variant */
.nurtify-button--danger {
  background: #FF2D55;
  color: white;
}

.nurtify-button--danger:hover:not(.nurtify-button--disabled) {
  background: #dc3545;
  transform: translateY(-1px);
}

/* Warning variant */
.nurtify-button--warning {
  background: #FF9500;
  color: white;
}

.nurtify-button--warning:hover:not(.nurtify-button--disabled) {
  background: #e68900;
  transform: translateY(-1px);
}

/* Info variant */
.nurtify-button--info {
  background: #007AFF;
  color: white;
}

.nurtify-button--info:hover:not(.nurtify-button--disabled) {
  background: #0056cc;
  transform: translateY(-1px);
}

/* Light variant */
.nurtify-button--light {
  background: #EBF4F6;
  color: #333;
}

.nurtify-button--light:hover:not(.nurtify-button--disabled) {
  background: #d4e6ea;
  transform: translateY(-1px);
}

/* Dark variant */
.nurtify-button--dark {
  background: #090914;
  color: white;
}

.nurtify-button--dark:hover:not(.nurtify-button--disabled) {
  background: #1a1a2e;
  transform: translateY(-1px);
}

/* Link variant */
.nurtify-button--link {
  background: transparent;
  color: #37B7C3;
  padding: 12px 8px;
  min-width: auto;
}

.nurtify-button--link:hover:not(.nurtify-button--disabled) {
  color: #088395;
  text-decoration: underline;
}

/* Outline variants */
.nurtify-button--outline.nurtify-button--primary {
  background: transparent;
  color: #37B7C3;
  border: 2px solid #37B7C3;
}

.nurtify-button--outline.nurtify-button--primary:hover:not(.nurtify-button--disabled) {
  background: #37B7C3;
  color: white;
  transform: translateY(-1px);
}

.nurtify-button--outline.nurtify-button--success {
  background: transparent;
  color: #34C759;
  border: 2px solid #34C759;
}

.nurtify-button--outline.nurtify-button--success:hover:not(.nurtify-button--disabled) {
  background: #34C759;
  color: white;
  transform: translateY(-1px);
}

.nurtify-button--outline.nurtify-button--danger {
  background: transparent;
  color: #FF2D55;
  border: 2px solid #FF2D55;
}

.nurtify-button--outline.nurtify-button--danger:hover:not(.nurtify-button--disabled) {
  background: #FF2D55;
  color: white;
  transform: translateY(-1px);
}

.nurtify-button--outline.nurtify-button--warning {
  background: transparent;
  color: #FF9500;
  border: 2px solid #FF9500;
}

.nurtify-button--outline.nurtify-button--warning:hover:not(.nurtify-button--disabled) {
  background: #FF9500;
  color: white;
  transform: translateY(-1px);
}

.nurtify-button--outline.nurtify-button--info {
  background: transparent;
  color: #007AFF;
  border: 2px solid #007AFF;
}

.nurtify-button--outline.nurtify-button--info:hover:not(.nurtify-button--disabled) {
  background: #007AFF;
  color: white;
  transform: translateY(-1px);
}

.nurtify-button--outline.nurtify-button--light {
  background: transparent;
  color: #666;
  border: 2px solid #EBF4F6;
}

.nurtify-button--outline.nurtify-button--light:hover:not(.nurtify-button--disabled) {
  background: #EBF4F6;
  color: #333;
  transform: translateY(-1px);
}

.nurtify-button--outline.nurtify-button--dark {
  background: transparent;
  color: #090914;
  border: 2px solid #090914;
}

.nurtify-button--outline.nurtify-button--dark:hover:not(.nurtify-button--disabled) {
  background: #090914;
  color: white;
  transform: translateY(-1px);
}

/* Disabled state */
.nurtify-button--disabled,
.nurtify-button:disabled {
  background: #AFAFAF !important;
  color: #666 !important;
  cursor: not-allowed !important;
  transform: none !important;
  border-color: #AFAFAF !important;
}

.nurtify-button--outline.nurtify-button--disabled,
.nurtify-button--outline:disabled {
  background: transparent !important;
  color: #AFAFAF !important;
  border-color: #AFAFAF !important;
}

.nurtify-button--link.nurtify-button--disabled,
.nurtify-button--link:disabled {
  background: transparent !important;
  color: #AFAFAF !important;
  text-decoration: none !important;
}

/* Button demo containers */
.button-demo-section {
  margin-bottom: 30px;
}

.button-demo-title {
  font-size: 12px;
  text-align: center;
  font-weight: 600;
  margin-bottom: 15px;
  color: #343a40;
}

.button-demo-row {
  display: flex;
  flex-wrap: wrap;
  gap: 12px;
  margin-bottom: 20px;
}

.button-size-group {
  display: flex;
  flex-direction: column;
  gap: 8px;
  padding: 10px;
  border: 1px dashed #dee2e6;
  border-radius: 8px;
  min-width: 100px;
}

.button-size-label {
  font-size: 12px;
  font-weight: 500;
  color: #6c757d;
  text-transform: uppercase;
  margin-bottom: 8px;
}

/* Responsive Styles */
@media (max-width: 768px) {
  .sdk-header {
    padding: 40px 0 30px;
  }

  .sdk-title {
    font-size: 28px;
  }

  .sdk-description {
    font-size: 16px;
  }

  .component-grid {
    grid-template-columns: 1fr;
  }

  .button-demo-row {
    flex-direction: column;
  }

  .button-size-group {
    min-width: auto;
  }
}

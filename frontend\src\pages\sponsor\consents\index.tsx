import React, { useState } from "react";
import { Plus, Trash2, <PERSON><PERSON>, <PERSON>, FileText, CheckCircle, XCircle, Search, Filter, Download, Settings } from "lucide-react";
import Preloader from "@/components/common/Preloader";
import Wrapper from "@/components/common/Wrapper";
import DataTable from "@/components/common/DataTable";
import NurtifySelect from "@/components/NurtifySelect";
import { useCurrentUserQuery } from "@/hooks/user.query";
import { useStudiesBySponsorQuery } from "@/hooks/study.query";
import {
  useConsentFormsQuery,
  useCreateConsentFormMutation,
  useDeleteConsentFormMutation
} from "@/hooks/consent.query";
import { ConsentForm } from "@/types/types";
import CreateConsentModal from "@/components/modal/CreateConsentModal";
import ViewConsentModal from "@/components/modal/ViewConsentModal";
import DuplicateConsentModal from "@/components/modal/DuplicateConsentModal";
import "./consents.css";

// Enhanced consent form interface for table display
interface EnhancedConsentForm extends ConsentForm {
  study_name: string;
  questions_count: number;
  status: 'active' | 'inactive';
}

const SponsorConsents: React.FC = () => {
  const { data: currentUser } = useCurrentUserQuery();
  const { data: studies } = useStudiesBySponsorQuery(currentUser?.identifier || "");
  const [selectedStudy, setSelectedStudy] = useState<string>("");
  const [searchTerm, setSearchTerm] = useState<string>("");
  const [statusFilter, setStatusFilter] = useState<string>("");
  const [isCreateModalOpen, setIsCreateModalOpen] = useState(false);
  const [isViewModalOpen, setIsViewModalOpen] = useState(false);
  const [isDuplicateModalOpen, setIsDuplicateModalOpen] = useState(false);
  const [selectedConsentForView, setSelectedConsentForView] = useState<ConsentForm | null>(null);
  const [selectedConsentForDuplicate, setSelectedConsentForDuplicate] = useState<ConsentForm | null>(null);
  const [errorMessage, setErrorMessage] = useState<string | null>(null);
  const [successMessage, setSuccessMessage] = useState<string | null>(null);

  const createConsentMutation = useCreateConsentFormMutation();
  const deleteConsentMutation = useDeleteConsentFormMutation();

  // Fetch consent forms with filters
  const { data: consentForms } = useConsentFormsQuery({
    sponsor_id: currentUser?.identifier,
    study_id: selectedStudy || undefined,
    is_active: true,
  });

  // Enhance consent forms with computed properties and apply filters
  const enhancedConsentForms: EnhancedConsentForm[] = consentForms?.results?.map((consent: ConsentForm): EnhancedConsentForm => ({
    ...consent,
    study_name: studies?.find((s: any) => s.uuid === consent.study_id)?.name || "Not assigned",
    questions_count: consent.questions?.length || 0,
    status: consent.is_active ? 'active' : 'inactive'
  })).filter((consent: EnhancedConsentForm) => {
    // Apply search filter
    const matchesSearch = !searchTerm || 
      consent.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      consent.study_name.toLowerCase().includes(searchTerm.toLowerCase());
    
    // Apply status filter
    const matchesStatus = !statusFilter || consent.status === statusFilter;
    
    return matchesSearch && matchesStatus;
  }) || [];

  // Clear messages after timeout
  const clearMessages = () => {
    setTimeout(() => {
      setErrorMessage(null);
      setSuccessMessage(null);
    }, 5000);
  };

  // Handle delete consent
  const handleDeleteConsent = (consent: EnhancedConsentForm) => {
    if (window.confirm("Are you sure you want to delete this consent form? This action cannot be undone.")) {
      deleteConsentMutation.mutate(consent.uuid, {
        onSuccess: () => {
          setSuccessMessage("Consent form deleted successfully!");
          clearMessages();
        },
        onError: (error: any) => {
          setErrorMessage(error?.response?.data?.message || "Failed to delete consent form. Please try again.");
          clearMessages();
        },
      });
    }
  };

  // Handle duplicate consent - open modal instead of direct duplication
  const handleDuplicateConsent = (consent: EnhancedConsentForm) => {
    setSelectedConsentForDuplicate(consent);
    setIsDuplicateModalOpen(true);
  };

  // Handle view consent
  const handleViewConsent = (consent: EnhancedConsentForm) => {
    setSelectedConsentForView(consent);
    setIsViewModalOpen(true);
  };

  // Handle duplicate success
  const handleDuplicateSuccess = () => {
    setSuccessMessage("Consent form duplicated successfully!");
    clearMessages();
  };

  // Define table columns
  const columns = [
    {
      key: "name" as keyof EnhancedConsentForm,
      header: "Consent Form Name",
      render: (value: any) => (
        <div>
          <FileText size={16} />
          <span>{value}</span>
        </div>
      ),
    },
    {
      key: "study_name" as keyof EnhancedConsentForm,
      header: "Associated Study",
    },
    {
      key: "version" as keyof EnhancedConsentForm,
      header: "Version",
      render: (value: any) => (
        <span>v{value}</span>
      ),
    },
    {
      key: "questions_count" as keyof EnhancedConsentForm,
      header: "Questions",
      render: (value: any) => (
        <span>{value} questions</span>
      ),
    },
    {
      key: "status" as keyof EnhancedConsentForm,
      header: "Status",
      render: (value: any) => (
        <div>
          {value === 'active' ? (
            <>
              <CheckCircle size={14} />
              Active
            </>
          ) : (
            <>
              <XCircle size={14} />
              Inactive
            </>
          )}
        </div>
      ),
    },
    {
      key: "created_at" as keyof EnhancedConsentForm,
      header: "Created",
      render: (value: any) => (
        <span>
          {new Date(value).toLocaleDateString()}
        </span>
      ),
    },
  ];

  // Define actions for the DataTable
  const actions = [
    {
      icon: <Eye size={16} />,
      tooltipText: "View Consent Form",
      onClick: (row: EnhancedConsentForm) => handleViewConsent(row),
    },
    {
      icon: <Copy size={16} />,
      tooltipText: "Duplicate Consent Form",
      onClick: (row: EnhancedConsentForm) => handleDuplicateConsent(row),
    },
    {
      icon: <Trash2 size={16} />,
      tooltipText: "Delete Consent Form",
      onClick: (row: EnhancedConsentForm) => handleDeleteConsent(row),
    },
  ];

  return (
    <Wrapper>
      <Preloader />
      <div className="sponsor-consents-container" style={{ paddingBottom: "88px" }}>
        <div>
          <div>
            <div>
              <div>
                {/* Enhanced Header with Stats */}
                <div className="sponsor-consents-header">
                  <div className="sponsor-consents-header-content">
                    <div className="sponsor-consents-header-left">
                      <div className="sponsor-consents-title-section">
                        <div className="sponsor-consents-icon-wrapper">
                          <FileText size={28} />
                        </div>
                        <div className="sponsor-consents-title-content">
                          <h1>Consent Forms Management</h1>
                          <p>Create, manage, and track digital consent forms for your clinical studies</p>
                        </div>
                      </div>
                      <div className="sponsor-consents-stats">
                        <div className="sponsor-consents-stat-card">
                          <div className="sponsor-consents-stat-icon total">
                            <FileText size={20} />
                          </div>
                          <div className="sponsor-consents-stat-content">
                            <div className="sponsor-consents-stat-number">{enhancedConsentForms.length}</div>
                            <div className="sponsor-consents-stat-label">Total Forms</div>
                          </div>
                        </div>
                        <div className="sponsor-consents-stat-card">
                          <div className="sponsor-consents-stat-icon active">
                            <CheckCircle size={20} />
                          </div>
                          <div className="sponsor-consents-stat-content">
                            <div className="sponsor-consents-stat-number">
                              {enhancedConsentForms.filter(f => f.status === 'active').length}
                            </div>
                            <div className="sponsor-consents-stat-label">Active</div>
                          </div>
                        </div>
                        <div className="sponsor-consents-stat-card">
                          <div className="sponsor-consents-stat-icon inactive">
                            <XCircle size={20} />
                          </div>
                          <div className="sponsor-consents-stat-content">
                            <div className="sponsor-consents-stat-number">
                              {enhancedConsentForms.filter(f => f.status === 'inactive').length}
                            </div>
                            <div className="sponsor-consents-stat-label">Inactive</div>
                          </div>
                        </div>
                      </div>
                    </div>
                    <div className="sponsor-consents-actions">
                      <button
                        className="sponsor-consents-btn sponsor-consents-btn-secondary"
                        onClick={() => {/* Add export functionality */}}
                      >
                        <Download size={16} />
                        Export
                      </button>
                      <button
                        className="sponsor-consents-btn sponsor-consents-btn-primary"
                        onClick={() => setIsCreateModalOpen(true)}
                        disabled={createConsentMutation.isPending}
                      >
                        <Plus size={16} />
                        Create New Form
                      </button>
                    </div>
                  </div>
                </div>

                {/* Messages */}
                {errorMessage && (
                  <div className="sponsor-consents-alert sponsor-consents-alert-error" role="alert">
                    <XCircle size={16} />
                    <span>{errorMessage}</span>
                  </div>
                )}
                {successMessage && (
                  <div className="sponsor-consents-alert sponsor-consents-alert-success" role="alert">
                    <CheckCircle size={16} />
                    <span>{successMessage}</span>
                  </div>
                )}

                {/* Enhanced Filters */}
                <div className="sponsor-consents-filters">
                  <div className="sponsor-consents-filters-header">
                    <div className="sponsor-consents-filters-title">
                      <Filter size={18} />
                      <span>Filter & Search</span>
                    </div>
                    <button 
                      className="sponsor-consents-clear-btn"
                      onClick={() => {
                        setSearchTerm("");
                        setSelectedStudy("");
                        setStatusFilter("");
                      }}
                    >
                      Clear All
                    </button>
                  </div>
                  <div className="sponsor-consents-filters-content">
                    <div className="sponsor-consents-search-wrapper">
                      <div className="sponsor-consents-search-icon">
                        <Search size={18} />
                      </div>
                      <input
                        className="sponsor-consents-search-input"
                        type="text"
                        placeholder="Search consent forms..."
                        value={searchTerm}
                        onChange={(e) => setSearchTerm(e.target.value)}
                      />
                    </div>
                    <div>
                      <NurtifySelect
                        value={selectedStudy}
                        onChange={(e) => setSelectedStudy(e.target.value)}
                        options={[
                          { value: "", label: "All Studies" },
                          ...(studies?.map((study: any) => ({
                            value: study.uuid,
                            label: study.name,
                          })) || []),
                        ]}
                      />
                    </div>
                    <div>
                      <NurtifySelect
                        value={statusFilter}
                        onChange={(e) => setStatusFilter(e.target.value)}
                        options={[
                          { value: "", label: "All Status" },
                          { value: "active", label: "Active" },
                          { value: "inactive", label: "Inactive" },
                        ]}
                      />
                    </div>
                  </div>
                </div>

                {/* Enhanced Table Container */}
                <div className="sponsor-consents-table-section">
                  <div className="sponsor-consents-table-header">
                    <div className="sponsor-consents-table-title">
                      <h3>Consent Forms ({enhancedConsentForms.length})</h3>
                      <p>Manage and track all your consent forms</p>
                    </div>
                    <div className="sponsor-consents-table-actions">
                      <button className="sponsor-consents-btn sponsor-consents-btn-secondary">
                        <Settings size={16} />
                        Table Settings
                      </button>
                    </div>
                  </div>
                  <div className="sponsor-consents-table-content">
                    <DataTable
                      data={enhancedConsentForms}
                      columns={columns}
                      actions={actions}
                      noDataMessage="No consent forms found. Create your first consent form to get started."
                    />
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Create Consent Modal */}
      {isCreateModalOpen && (
        <CreateConsentModal
          isOpen={isCreateModalOpen}
          onClose={() => setIsCreateModalOpen(false)}
          studies={studies || []}
          currentUserIdentifier={currentUser?.identifier || ""}
          onSuccess={() => {
            setIsCreateModalOpen(false);
            setSuccessMessage("Consent form created successfully!");
            clearMessages();
          }}
        />
      )}

      {/* View Consent Modal */}
      {isViewModalOpen && selectedConsentForView && (
        <ViewConsentModal
          isOpen={isViewModalOpen}
          onClose={() => setIsViewModalOpen(false)}
          consent={selectedConsentForView}
        />
      )}

      {/* Duplicate Consent Modal */}
      {isDuplicateModalOpen && selectedConsentForDuplicate && (
        <DuplicateConsentModal
          isOpen={isDuplicateModalOpen}
          onClose={() => setIsDuplicateModalOpen(false)}
          consent={selectedConsentForDuplicate}
          studies={studies || []}
          onSuccess={handleDuplicateSuccess}
        />
      )}
    </Wrapper>
  );
};

export default SponsorConsents;

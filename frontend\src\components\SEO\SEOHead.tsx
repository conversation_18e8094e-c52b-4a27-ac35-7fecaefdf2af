import React from 'react';
import { Helmet } from 'react-helmet-async';
import { useLocation } from 'react-router-dom';
import { getSEOConfig } from '@/config/seoConfig';

export interface SEOHeadProps {
  title?: string;
  description?: string;
  keywords?: string;
  image?: string;
  noIndex?: boolean;
  noFollow?: boolean;
  canonical?: string;
  structuredData?: object | object[];
  alternateLanguages?: Array<{ hreflang: string; url: string }>;
  openGraph?: {
    type?: string;
    siteName?: string;
    locale?: string;
  };
  twitter?: {
    card?: string;
    site?: string;
    creator?: string;
  };
}

const SEOHead: React.FC<SEOHeadProps> = ({
  title,
  description,
  keywords,
  image,
  noIndex = false,
  noFollow = false,
  canonical,
  structuredData,
  alternateLanguages,
  openGraph,
  twitter,
}) => {
  const location = useLocation();
  const defaultConfig = getSEOConfig(location.pathname);
  
  // Merge props with default config
  const seoData = {
    title: title || defaultConfig.title,
    description: description || defaultConfig.description,
    keywords: keywords || defaultConfig.keywords,
    image: image || defaultConfig.image || 'https://nurtify.co.uk/assets/nurtify-social-preview.png',
    canonical: canonical || `https://nurtify.co.uk${location.pathname}`,
    structuredData: structuredData || defaultConfig.structuredData,
  };

  // Create robots meta content
  const robotsContent = [];
  if (noIndex) robotsContent.push('noindex');
  else robotsContent.push('index');
  
  if (noFollow) robotsContent.push('nofollow');
  else robotsContent.push('follow');
  
  const robots = robotsContent.join(', ');

  return (
    <Helmet>
      {/* Basic Meta Tags */}
      <title>{seoData.title}</title>
      <meta name="description" content={seoData.description} />
      <meta name="keywords" content={seoData.keywords} />
      <meta name="robots" content={robots} />
      <link rel="canonical" href={seoData.canonical} />
      
      {/* Open Graph Meta Tags */}
      <meta property="og:title" content={seoData.title} />
      <meta property="og:description" content={seoData.description} />
      <meta property="og:image" content={seoData.image} />
      <meta property="og:url" content={seoData.canonical} />
      <meta property="og:type" content={openGraph?.type || defaultConfig.type || 'website'} />
      <meta property="og:site_name" content={openGraph?.siteName || 'Nurtify'} />
      <meta property="og:locale" content={openGraph?.locale || 'en_GB'} />
      
      {/* Twitter Meta Tags */}
      <meta name="twitter:card" content={twitter?.card || 'summary_large_image'} />
      <meta name="twitter:site" content={twitter?.site || '@nurtify'} />
      <meta name="twitter:creator" content={twitter?.creator || '@nurtify'} />
      <meta name="twitter:title" content={seoData.title} />
      <meta name="twitter:description" content={seoData.description} />
      <meta name="twitter:image" content={seoData.image} />
      
      {/* Additional Meta Tags */}
      <meta name="author" content="Nurtify" />
      <meta name="viewport" content="width=device-width, initial-scale=1.0" />
      <meta httpEquiv="Content-Language" content="en" />
      <meta name="theme-color" content="#2563eb" />
      
      {/* Alternate Language Links */}
      {alternateLanguages?.map((lang) => (
        <link
          key={lang.hreflang}
          rel="alternate"
          hrefLang={lang.hreflang}
          href={lang.url}
        />
      ))}
      
      {/* Structured Data */}
      {seoData.structuredData && (
        <script type="application/ld+json">
          {typeof seoData.structuredData === 'string'
            ? seoData.structuredData
            : JSON.stringify(seoData.structuredData)}
        </script>
      )}
    </Helmet>
  );
};

export default SEOHead;

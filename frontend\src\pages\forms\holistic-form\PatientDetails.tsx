import NurtifyDateInput from "@/components/NurtifyDateInput";
import NurtifyInput from "@/components/NurtifyInput";
import useHolisticFormStore from "@/store/holisticFormState";
import useHolisticFormTabStore from "@/store/holisticFormTabState";

interface PatientDetailsData {
  Name: string;
  medicalRecordNumber: string;
  dateofBirth: string;
  currentDate: string;
  hospitalName: string;
  departmentName: string;
}

export default function PatientDetails() {
  const { patientDetails, setPatientDetails } = useHolisticFormStore();
  const { goToNext } = useHolisticFormTabStore();

  const handleInputChange = (field: keyof PatientDetailsData, value: string) => {
    setPatientDetails({ ...patientDetails, [field]: value });
  };
/*
  const getCurrentDate = () => {
    const today = new Date();
    const year = today.getFullYear();
    const month = (1 + today.getMonth()).toString().padStart(2, '0');
    const day = today.getDate().toString().padStart(2, '0');
  
    return `${year}-${month}-${day}`;
  };

  const handleNext = () => {
    console.log(patientDetails);
  };
  */
  
  return (
    <div className={`tabs__pane -tab-item-1`}>
      <div id="patientDetailsSection" style={{ padding: "30px 0px" }}>
        <div className="container-fluid py-6 px-6">
          <div className="row g-5">
            {/* Left Column */}
            <div className="col-md-6">
              <div className="row g-4">
                {/* Name */}
                <div className="col-12">
                  <div className="form-group">
                    <span className="headingQuestion d-block mb-3 headinqQuestion">Name</span>
                    <NurtifyInput
                      type="text"
                      name="Name"
                      onChange={(e) => handleInputChange('Name', e.target.value)}
                      placeholder="Enter patient name"
                      className="w-100"
                    />
                  </div>
                </div>

                {/* Date of Birth */}
                <div className="col-12">
                  <div className="form-group">
                    <span className="d-block mb-3 headinqQuestion">Date of Birth</span>
                    <NurtifyDateInput
                      name="dateofBirth"
                      onChange={(e) => handleInputChange('dateofBirth', e.target.value)}
                      className="w-100"
                    />
                  </div>
                </div>

                {/* Hospital Name */}
                <div className="col-12">
                  <div className="form-group">
                    <span className="d-block mb-3 headinqQuestion">Hospital Name</span>
                    <NurtifyInput
                      type="text"
                      name="hospitalName"
                      onChange={(e) => handleInputChange('hospitalName', e.target.value)}
                      placeholder="Enter hospital name"
                      className="w-100"
                    />
                  </div>
                </div>
              </div>
            </div>

            {/* Right Column */}
            <div className="col-md-6">
              <div className="row g-4">
                {/* Medical Record Number */}
                <div className="col-12">
                  <div className="form-group">
                    <span className="d-block mb-3 headinqQuestion">Medical Record Number</span>
                    <NurtifyInput
                      type="text"
                      name="medicalRecordNumber"
                      onChange={(e) => handleInputChange('medicalRecordNumber', e.target.value)}
                      placeholder="Enter MRN"
                      className="w-100"
                    />
                  </div>
                </div>

                {/* Current Date */}
                <div className="col-12">
                  <div className="form-group">
                    <span className="d-block mb-3 headinqQuestion">Current Date</span>
                    <NurtifyDateInput
                      name="currentDate"
                      value={patientDetails.currentDate}
                      onChange={(e) => handleInputChange('currentDate', e.target.value)}
                      className="w-100"
                    />
                  </div>
                </div>

                {/* Department Name */}
                <div className="col-12">
                  <div className="form-group">
                    <span className="d-block mb-3 headinqQuestion">Department Name</span>
                    <NurtifyInput
                      type="text"
                      name="departmentName"
                      onChange={(e) => handleInputChange('departmentName', e.target.value)}
                      placeholder="Enter department name"
                      className="w-100"
                    />
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Navigation Buttons */}
      <div className="container-fluid px-6 py-4 mt-4">
        <div className="row">
          <div className="col-12 d-flex justify-content-between">
            <button className="button -md btn-nurtify-lighter">
              Prev
            </button>
            <button className="button -md btn-nurtify text-white" onClick={goToNext}>
              Next
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}

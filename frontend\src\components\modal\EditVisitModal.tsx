import React, { useState, useEffect } from "react";
import { X, Save, Calendar } from "lucide-react";
import "./EditVisitModal.css";

interface EditVisitModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSave: (updatedVisit: { visit_status: string; date: string; time: string }) => void; // Updated to include time
  visitData: {
    name: string;
    visit_status: string;
    date: string;
    time: string;
  };
}

const EditVisitModal: React.FC<EditVisitModalProps> = ({
  isOpen,
  onClose,
  onSave,
  visitData,
}) => {
  const [status, setStatus] = useState(visitData.visit_status);
  const [date, setDate] = useState(visitData.date);
  const [time, setTime] = useState(visitData.time);

  // Update state when the modal opens with new data
  useEffect(() => {
    if (isOpen) {
      setStatus(visitData.visit_status);
      setDate(visitData.date);
      setTime(visitData.time); // Update time state
    }
  }, [isOpen, visitData]);

  // Format date for input field (YYYY-MM-DD)
  const formatDateForInput = (dateString: string) => {
    const date = new Date(dateString);
    return date.toISOString().split("T")[0];
  };

  // Format time for input field (HH:mm)
  const formatTimeForInput = (timeString: string) => {
    if (!timeString) return "";
    // If timeString is in "HH:mm:ss" format, strip the seconds
    const [hours, minutes] = timeString.split(":");
    return `${hours}:${minutes}`; // Return "HH:mm"
  };

  // Handle form submission
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    onSave({
      visit_status: status,
      date: date,
      time: time, // Include time in the saved data
    });
    onClose();
  };

  if (!isOpen) return null;

  return (
    <div className="edit-visit-modal-overlay">
      <div className="edit-visit-modal">
        <div className="edit-visit-modal-header">
          <h2 className="edit-visit-modal-title">
            <Calendar size={20} className="modal-icon" /> Edit Visit:{" "}
            {visitData.name}
          </h2>
          <button
            type="button"
            className="edit-visit-modal-close"
            onClick={onClose}
            aria-label="Close"
          >
            <X size={20} />
          </button>
        </div>

        <form onSubmit={handleSubmit}>
          <div className="edit-visit-modal-body">
            <div className="form-group">
              <label htmlFor="visit-status">Status</label>
              <select
                id="visit-status"
                className="form-select"
                value={status}
                onChange={(e) => setStatus(e.target.value)}
              >
                <option value="Pending">Pending</option>
                <option value="Completed">Completed</option>
                <option value="Delayed">Delayed</option>
                <option value="Canceled">Canceled</option>
              </select>
            </div>

            <div className="form-group">
              <label htmlFor="visit-date">Date</label>
              <input
                type="date"
                id="visit-date"
                className="form-control"
                value={formatDateForInput(date)}
                onChange={(e) => setDate(e.target.value)}
              />
            </div>
            <div className="form-group">
              <label htmlFor="visit-time">Time</label>
              <input
                type="time"
                id="visit-time"
                value={formatTimeForInput(time)} // Bind formatted time
                onChange={(e) => setTime(e.target.value)} // Update time state
                className="form-control"
              />
            </div>
          </div>

          <div className="edit-visit-modal-footer">
            <button type="button" className="cancel-btn" onClick={onClose}>
              <X size={16} /> Cancel
            </button>
            <button type="submit" className="save-btn">
              <Save size={16} /> Save Changes
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default EditVisitModal;
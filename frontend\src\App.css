#root {
  max-width: 100%;
  margin: 0 auto;
  overflow:hidden;
}
:root {
  --font-primary: "Poppin<PERSON>", sans-serif;
  --text-6: 6px;
  --text-9: 9px;
  --text-11: 11px;
  --text-13: 13px;
  --text-14: 14px;
  --text-15: 15px;
  --text-16: 16px;
  --text-17: 17px;
  --text-18: 18px;
  --text-20: 20px;
  --text-24: 24px;
  --text-30: 30px;
  --text-35: 35px;
  --text-40: 40px;
  --text-45: 45px;
  --text-50: 50px;
  --text-55: 55px;
  --text-60: 60px;
  --text-64: 64px;
}

:root {
  --color-white: #FFFFFF;
  --color-black: #000000;
  --color-dark-1: #140342;
  --color-dark-2: #1A064F;
  --color-dark-3: #6A7A99;
  --color-dark-4: #242239;
  --color-dark-5: #282664;
  --color-dark-6: #311F61;
  --color-dark-7: #EAE9EF;
  --color-dark-8: #202124;
  --color-light-1: #4F547B;
  --color-light-2: #E4E7EA;
  --color-light-3: #EEF2F6;
  --color-light-4: #F7F8FB;
  --color-light-5: #EDEDED;
  --color-light-6: #F5F7FE;
  --color-light-7: #E5F0FD;
  --color-light-8: #DDDDDD;
  --color-light-9: #EEF2F6;
  --color-light-10: #F4F3F6;
  --color-light-11: #EBEAFE;
  --color-light-12: #CCE0F8;
  --color-purple-1: #37B7C3;
  --color-purple-2: #E3EDFD;
  --color-purple-3: #F4F1FE;
  --color-purple-4: #7545F0;
  --color-purple-5: #EAE3FD;
  --color-green-1: #00FF84;
  --color-green-2: #DEF5F0;
}

/* System Notification Dropdown Styles */
.notification-dropdown {
  position: absolute;
  top: 100%;
  right: 0;
  background: white;
  border-radius: 8px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
  width: 350px;
  max-width: 90vw;
  z-index: 1000;
  border: 1px solid #e0e0e0;
  animation: fadeInDropdown 0.2s ease-out;
}

@keyframes fadeInDropdown {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.notification-dropdown-header {
  font-weight: 600;
  fontSize: 16px;
  padding: 12px 16px;
  border-bottom: 1px solid #f0f0f0;
  background: #fff;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.notification-list {
  max-height: 400px;
  overflow-y: auto;
}

.notification-item {
  padding: 12px 16px;
  border-bottom: 1px solid #f0f0f0;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.notification-item:hover {
  background-color: #f8f9fa;
}

.notification-item.unread {
  background-color: #f8f9fa;
  border-left: 3px solid #37B7C3;
}

.notification-item:last-child {
  border-bottom: none;
}

.no-notifications {
  padding: 20px;
  text-align: center;
  color: #666;
  font-size: 14px;
}

.mark-all-read {
  background: none;
  border: none;
  color: #37B7C3;
  font-size: 12px;
  cursor: pointer;
  font-weight: 500;
}

.mark-all-read:hover {
  color: #088395;
}
  --color-green-3: #EEF8F5;
  --color-green-4: #04D697;
  --color-green-5: #34A853;
  --color-green-6: #DFF1DD;
  --color-green-7: #D9FFED;
  --color-orange-1: #E8543E;
  --color-orange-2: #F7E9E7;
  --color-orange-3: #FDF2EB;
  --color-orange-4: #E97D38;
  --color-orange-5: #FDEEEC;
  --color-orange-6: #E78E34;
  --color-orange-7: #FBEEE1;
  --color-red-1: #F01E00;
  --color-red-2: #FCE5E2;
  --color-red-3: #D93025;
  --color-beige-1: #FEFBF4;
  --color-blue-1: #65C2F9;
  --color-blue-2: #EFF9FE;
  --color-blue-3: #1967D2;
  --color-blue-4: #508EF0;
  --color-blue-5: #445B95;
  --color-blue-6: #E5EEFD;
  --color-blue-7: #E3E6EF;
  --color-yellow-1: #E59819;
  --color-yellow-2: #FDF8EB;
  --color-yellow-3: #ECB53E;
  --color-yellow-4: #F9AB00;
  --color-yellow-5: #FCEDCD;
  --color-info-1: #CDE9F6;
  --color-info-2: #4780AA;
  --color-warning-1: #F7F3D7;
  --color-warning-2: #927238;
  --color-error-1: #ECC8C5;
  --color-error-2: #AB3331;
  --color-success-1: #DEF2D7;
  --color-success-2: #5B7052;
  --white:#fff
}

.btn-outline-facebook {
  color: #1967D2; /* Text color */
  border-radius: 60px;
  border: 2px solid #1967D2; /* Border color */
}

.btn-outline-facebook:hover {
  background-color: #1967D2; /* Background color on hover */
  color: white; /* Text color on hover */
}

.btn-outline-facebook:hover .icon-path {
  fill: white; /* Change the SVG path fill to white on hover */
}

.btn-outline-google {
  color: #D93025; /* Text color */
  border-radius: 60px;
  border: 2px solid #D93025; /* Border color */
}

.btn-outline-google:hover {
  background-color: #D93025; /* Background color on hover */
  color: white; /* Text color on hover */
}

.btn-outline-google:hover .google-icon-path {
  fill: white; /* Change the SVG path fill to white on hover */
}
.btn-primary-nurtify {
  background-color: rgba(55, 183, 195, 1); /* Set yes button color */
  color: #fff;
  border: none;
  border-radius: 4px;
  cursor: pointer;
}
.btn-primary-nurtify:hover {
  background-color: rgba(55, 183, 195, 0.8); /* Set yes button color */
}
.mobileMenu{
  padding: 16px 20px;
  width: 100%;
  display: flex;
  flex-direction: column;
  /* box-sizing: border-box; */
  overflow-y: scroll;
  height: 100%;
  row-gap: 15px;
  margin: 40px 0px;
  @media (min-width:1200px) {
      display: none;
  }
  @media (max-width: 768px) {
    .mobileMenu .submenuOne {
      padding: 0px 10px;
    }
    .mobileMenu .submenuOne:hover {
      background-color: #37B7C3;
      border-radius: 8px;
    }
    .mobileMenu .submenuOne:hover span {
      color: white;
    }
    .mobileMenu .submenuOne .title{
      padding: 0px 10px;
    }
    .mobileMenu .submenuOne .title:hover{
      background-color: #37B7C3;
      border-radius: 8px;
    }
  }
  .link{
      padding: 6px 7px;
      font-size: 17px;
      transition: 0.3s;
      margin: 4px 0px;
      &:hover{
          background-color: #fcfcfc;
      }

  }
  i{
      transition: 0.4;
      &.active{
          transform: rotate(90deg);
      }

  }

  .mobileMenu::-webkit-scrollbar {
    display: none;
  }

  .mobileMenu::-webkit-scrollbar-thumb {
    display: none;
  }

  .submenuOne{
      display: flex;
      flex-direction: column;
      width: 100%;
      .title{
          width: 100%;
          display: flex;
          align-items: center;
          justify-content: space-between;
          padding: 4px 7px;
          
          cursor: pointer;
          
          span{
              font-size: 19px;
              font-weight: 500;
              

          }
         
      }
      .toggle{
          display: flex;
          flex-direction: column;
          padding: 0px 7px;
          row-gap: 2px;
          height: 0px;
          overflow: hidden;
          opacity: 0;
          transition: 0.3s;
         
          &.active{
              height: fit-content;
              opacity: 1;
              
          }
          
          .submenuTwo{
              display: flex;
              flex-direction: column;
              padding: 1px 7px;
              .toggle{
                  display: flex;
                  flex-direction: column;
                  padding: 6px 7px;
                  .link{
                      padding: 6px 7px;
      
                  }

      
              }
              

          }

      }

  }

}


.linkCustom{
  text-decoration: none !important;
  color:inherit !important;
  &:hover{
      color: var(--color-purple-1) !important;
  }

}

.linkCustomTwo{
  text-decoration: none;
}


button {
  padding: 0;
  margin: 0;
  border: 0;
  border-radius: 35px;
  background-color: transparent;
  text-decoration: none;
}

button:focus {
  outline: 0;
}

.button {
  display: flex;
  align-items: center;
  justify-content: center;
  vertical-align: middle;
  text-align: center;
  border-radius: 35px;
  line-height: 1;
  font-weight: 400;
  transition: all 0.3s cubic-bezier(0.165, 0.84, 0.44, 1);
}

.button.-sm {
  padding: 6px 34px;
  font-size: 15px;
  height: 40px;
}

.button.-md {
  padding: 12px 55px;
  font-size: 16px;
  line-height: 18px;
  font-weight: 500;
  height: 60px;
}

.button.-icon {
  min-width: 120px;
  padding: 14px 30px;
  font-size: 15px;
  height: 50px;
}

.button.-single-icon {
  display: flex;
  justify-content: center;
  align-items: center;
}

.button.-narrow {
  min-width: unset;
  padding-left: 25px;
  padding-right: 25px;
  height: 50px;
}

.button.-rounded {
  border-radius: 100px;
}

.button.-underline:hover {
  text-decoration: underline;
}

.button.-white {
  background-color: var(--color-white);
  border: 2px solid var(--color-white);
}

.button.-white:hover {
  background-color: transparent !important;
  color: var(--color-white) !important;
}

.button.-outline-white {
  border: 2px solid var(--color-white);
}

.button.-outline-white:hover {
  background-color: var(--color-white);
  border-color: transparent;
  color: white !important;
}

.button.-black {
  background-color: var(--color-black);
  border: 2px solid var(--color-black);
}

.button.-black:hover {
  background-color: transparent !important;
  color: var(--color-black) !important;
}

.button.-outline-black {
  border: 2px solid var(--color-black);
}

.button.-outline-black:hover {
  background-color: var(--color-black);
  border-color: transparent;
  color: white !important;
}

.button.-dark-1 {
  background-color: var(--color-dark-1);
  border: 2px solid var(--color-dark-1);
}

.button.-dark-1:hover {
  background-color: transparent !important;
  color: var(--color-dark-1) !important;
}

.button.-outline-dark-1 {
  border: 2px solid var(--color-dark-1);
}

.button.-outline-dark-1:hover {
  background-color: var(--color-dark-1);
  border-color: transparent;
  color: white !important;
}

.button.-dark-2 {
  background-color: var(--color-dark-2);
  border: 2px solid var(--color-dark-2);
}

.button.-dark-2:hover {
  background-color: transparent !important;
  color: var(--color-dark-2) !important;
}

.button.-outline-dark-2 {
  border: 2px solid var(--color-dark-2);
}

.button.-outline-dark-2:hover {
  background-color: var(--color-dark-2);
  border-color: transparent;
  color: white !important;
}

.button.-dark-3 {
  background-color: var(--color-dark-3);
  border: 2px solid var(--color-dark-3);
}

.button.-dark-3:hover {
  background-color: transparent !important;
  color: var(--color-dark-3) !important;
}

.button.-outline-dark-3 {
  border: 2px solid var(--color-dark-3);
}

.button.-outline-dark-3:hover {
  background-color: var(--color-dark-3);
  border-color: transparent;
  color: white !important;
}

.button.-dark-4 {
  background-color: var(--color-dark-4);
  border: 2px solid var(--color-dark-4);
}

.button.-dark-4:hover {
  background-color: transparent !important;
  color: var(--color-dark-4) !important;
}

.button.-outline-dark-4 {
  border: 2px solid var(--color-dark-4);
}

.button.-outline-dark-4:hover {
  background-color: var(--color-dark-4);
  border-color: transparent;
  color: white !important;
}

.button.-dark-5 {
  background-color: var(--color-dark-5);
  border: 2px solid var(--color-dark-5);
}

.button.-dark-5:hover {
  background-color: transparent !important;
  color: var(--color-dark-5) !important;
}

.button.-outline-dark-5 {
  border: 2px solid var(--color-dark-5);
}

.button.-outline-dark-5:hover {
  background-color: var(--color-dark-5);
  border-color: transparent;
  color: white !important;
}

.button.-dark-6 {
  background-color: var(--color-dark-6);
  border: 2px solid var(--color-dark-6);
}

.button.-dark-6:hover {
  background-color: transparent !important;
  color: var(--color-dark-6) !important;
}

.button.-outline-dark-6 {
  border: 2px solid var(--color-dark-6);
}

.button.-outline-dark-6:hover {
  background-color: var(--color-dark-6);
  border-color: transparent;
  color: white !important;
}

.button.-dark-7 {
  background-color: var(--color-dark-7);
  border: 2px solid var(--color-dark-7);
}

.button.-dark-7:hover {
  background-color: transparent !important;
  color: var(--color-dark-7) !important;
}

.button.-outline-dark-7 {
  border: 2px solid var(--color-dark-7);
}

.button.-outline-dark-7:hover {
  background-color: var(--color-dark-7);
  border-color: transparent;
  color: white !important;
}

.button.-dark-8 {
  background-color: var(--color-dark-8);
  border: 2px solid var(--color-dark-8);
}

.button.-dark-8:hover {
  background-color: transparent !important;
  color: var(--color-dark-8) !important;
}

.button.-outline-dark-8 {
  border: 2px solid var(--color-dark-8);
}

.button.-outline-dark-8:hover {
  background-color: var(--color-dark-8);
  border-color: transparent;
  color: white !important;
}

.button.-light-1 {
  background-color: var(--color-light-1);
  border: 2px solid var(--color-light-1);
}

.button.-light-1:hover {
  background-color: transparent !important;
  color: var(--color-light-1) !important;
}

.button.-outline-light-1 {
  border: 2px solid var(--color-light-1);
}

.button.-outline-light-1:hover {
  background-color: var(--color-light-1);
  border-color: transparent;
  color: white !important;
}

.button.-light-2 {
  background-color: var(--color-light-2);
  border: 2px solid var(--color-light-2);
}

.button.-light-2:hover {
  background-color: transparent !important;
  color: var(--color-light-2) !important;
}

.button.-outline-light-2 {
  border: 2px solid var(--color-light-2);
}

.button.-outline-light-2:hover {
  background-color: var(--color-light-2);
  border-color: transparent;
  color: white !important;
}

.button.-light-3 {
  background-color: var(--color-light-3);
  border: 2px solid var(--color-light-3);
}

.button.-light-3:hover {
  background-color: transparent !important;
  color: var(--color-light-3) !important;
}

.button.-outline-light-3 {
  border: 2px solid var(--color-light-3);
}

.button.-outline-light-3:hover {
  background-color: var(--color-light-3);
  border-color: transparent;
  color: white !important;
}

.button.-light-4 {
  background-color: var(--color-light-4);
  border: 2px solid var(--color-light-4);
}

.button.-light-4:hover {
  background-color: transparent !important;
  color: var(--color-light-4) !important;
}

.button.-outline-light-4 {
  border: 2px solid var(--color-light-4);
}

.button.-outline-light-4:hover {
  background-color: var(--color-light-4);
  border-color: transparent;
  color: white !important;
}

.button.-light-5 {
  background-color: var(--color-light-5);
  border: 2px solid var(--color-light-5);
}

.button.-light-5:hover {
  background-color: transparent !important;
  color: var(--color-light-5) !important;
}

.button.-outline-light-5 {
  border: 2px solid var(--color-light-5);
}

.button.-outline-light-5:hover {
  background-color: var(--color-light-5);
  border-color: transparent;
  color: white !important;
}

.button.-light-6 {
  background-color: var(--color-light-6);
  border: 2px solid var(--color-light-6);
}

.button.-light-6:hover {
  background-color: transparent !important;
  color: var(--color-light-6) !important;
}

.button.-outline-light-6 {
  border: 2px solid var(--color-light-6);
}

.button.-outline-light-6:hover {
  background-color: var(--color-light-6);
  border-color: transparent;
  color: white !important;
}

.button.-light-7 {
  background-color: var(--color-light-7);
  border: 2px solid var(--color-light-7);
}

.button.-light-7:hover {
  background-color: transparent !important;
  color: var(--color-light-7) !important;
}

.button.-outline-light-7 {
  border: 2px solid var(--color-light-7);
}

.button.-outline-light-7:hover {
  background-color: var(--color-light-7);
  border-color: transparent;
  color: white !important;
}

.button.-light-8 {
  background-color: var(--color-light-8);
  border: 2px solid var(--color-light-8);
}

.button.-light-8:hover {
  background-color: transparent !important;
  color: var(--color-light-8) !important;
}

.button.-outline-light-8 {
  border: 2px solid var(--color-light-8);
}

.button.-outline-light-8:hover {
  background-color: var(--color-light-8);
  border-color: transparent;
  color: white !important;
}

.button.-light-9 {
  background-color: var(--color-light-9);
  border: 2px solid var(--color-light-9);
}

.button.-light-9:hover {
  background-color: transparent !important;
  color: var(--color-light-9) !important;
}

.button.-outline-light-9 {
  border: 2px solid var(--color-light-9);
}

.button.-outline-light-9:hover {
  background-color: var(--color-light-9);
  border-color: transparent;
  color: white !important;
}

.button.-light-10 {
  background-color: var(--color-light-10);
  border: 2px solid var(--color-light-10);
}

.button.-light-10:hover {
  background-color: transparent !important;
  color: var(--color-light-10) !important;
}

.button.-outline-light-10 {
  border: 2px solid var(--color-light-10);
}

.button.-outline-light-10:hover {
  background-color: var(--color-light-10);
  border-color: transparent;
  color: white !important;
}

.button.-light-11 {
  background-color: var(--color-light-11);
  border: 2px solid var(--color-light-11);
}

.button.-light-11:hover {
  background-color: transparent !important;
  color: var(--color-light-11) !important;
}

.button.-outline-light-11 {
  border: 2px solid var(--color-light-11);
}

.button.-outline-light-11:hover {
  background-color: var(--color-light-11);
  border-color: transparent;
  color: white !important;
}

.button.-light-12 {
  background-color: var(--color-light-12);
  border: 2px solid var(--color-light-12);
}

.button.-light-12:hover {
  background-color: transparent !important;
  color: var(--color-light-12) !important;
}

.button.-outline-light-12 {
  border: 2px solid var(--color-light-12);
}

.button.-outline-light-12:hover {
  background-color: var(--color-light-12);
  border-color: transparent;
  color: white !important;
}

.button.-purple-1 {
  background-color: var(--color-purple-1);
  border: 2px solid var(--color-purple-1);
}

.button.-purple-1:hover {
  background-color: transparent !important;
  color: var(--color-purple-1) !important;
}

.button.-outline-purple-1 {
  border: 2px solid var(--color-purple-1);
}

.button.-outline-purple-1:hover {
  background-color: var(--color-purple-1);
  border-color: transparent;
  color: white !important;
}

.button.-purple-2 {
  background-color: var(--color-purple-2);
  border: 2px solid var(--color-purple-2);
}

.button.-purple-2:hover {
  background-color: transparent !important;
  color: var(--color-purple-2) !important;
}

.button.-outline-purple-2 {
  border: 2px solid var(--color-purple-2);
}

.button.-outline-purple-2:hover {
  background-color: var(--color-purple-2);
  border-color: transparent;
  color: white !important;
}

.button.-purple-3 {
  background-color: var(--color-purple-3);
  border: 2px solid var(--color-purple-3);
}

.button.-purple-3:hover {
  background-color: transparent !important;
  color: var(--color-purple-3) !important;
}

.button.-outline-purple-3 {
  border: 2px solid var(--color-purple-3);
}

.button.-outline-purple-3:hover {
  background-color: var(--color-purple-3);
  border-color: transparent;
  color: white !important;
}

.button.-purple-4 {
  background-color: var(--color-purple-4);
  border: 2px solid var(--color-purple-4);
}

.button.-purple-4:hover {
  background-color: transparent !important;
  color: var(--color-purple-4) !important;
}

.button.-outline-purple-4 {
  border: 2px solid var(--color-purple-4);
}

.button.-outline-purple-4:hover {
  background-color: var(--color-purple-4);
  border-color: transparent;
  color: white !important;
}

.button.-purple-5 {
  background-color: var(--color-purple-5);
  border: 2px solid var(--color-purple-5);
}

.button.-purple-5:hover {
  background-color: transparent !important;
  color: var(--color-purple-5) !important;
}

.button.-outline-purple-5 {
  border: 2px solid var(--color-purple-5);
}

.button.-outline-purple-5:hover {
  background-color: var(--color-purple-5);
  border-color: transparent;
  color: white !important;
}

.button.-green-1 {
  background-color: var(--color-green-1);
  border: 2px solid var(--color-green-1);
}

.button.-green-1:hover {
  background-color: transparent !important;
  color: var(--color-green-1) !important;
}

.button.-outline-green-1 {
  border: 2px solid var(--color-green-1);
}

.button.-outline-green-1:hover {
  background-color: var(--color-green-1);
  border-color: transparent;
  color: white !important;
}

.button.-green-2 {
  background-color: var(--color-green-2);
  border: 2px solid var(--color-green-2);
}

.button.-green-2:hover {
  background-color: transparent !important;
  color: var(--color-green-2) !important;
}

.button.-outline-green-2 {
  border: 2px solid var(--color-green-2);
}

.button.-outline-green-2:hover {
  background-color: var(--color-green-2);
  border-color: transparent;
  color: white !important;
}

.button.-green-3 {
  background-color: var(--color-green-3);
  border: 2px solid var(--color-green-3);
}

.button.-green-3:hover {
  background-color: transparent !important;
  color: var(--color-green-3) !important;
}

.button.-outline-green-3 {
  border: 2px solid var(--color-green-3);
}

.button.-outline-green-3:hover {
  background-color: var(--color-green-3);
  border-color: transparent;
  color: white !important;
}

.button.-green-4 {
  background-color: var(--color-green-4);
  border: 2px solid var(--color-green-4);
}

.button.-green-4:hover {
  background-color: transparent !important;
  color: var(--color-green-4) !important;
}

.button.-outline-green-4 {
  border: 2px solid var(--color-green-4);
}

.button.-outline-green-4:hover {
  background-color: var(--color-green-4);
  border-color: transparent;
  color: white !important;
}

.button.-green-5 {
  background-color: var(--color-green-5);
  border: 2px solid var(--color-green-5);
}

.button.-green-5:hover {
  background-color: transparent !important;
  color: var(--color-green-5) !important;
}

.button.-outline-green-5 {
  border: 2px solid var(--color-green-5);
}

.button.-outline-green-5:hover {
  background-color: var(--color-green-5);
  border-color: transparent;
  color: white !important;
}

.button.-green-6 {
  background-color: var(--color-green-6);
  border: 2px solid var(--color-green-6);
}

.button.-green-6:hover {
  background-color: transparent !important;
  color: var(--color-green-6) !important;
}

.button.-outline-green-6 {
  border: 2px solid var(--color-green-6);
}

.button.-outline-green-6:hover {
  background-color: var(--color-green-6);
  border-color: transparent;
  color: white !important;
}

.button.-green-7 {
  background-color: var(--color-green-7);
  border: 2px solid var(--color-green-7);
}

.button.-green-7:hover {
  background-color: transparent !important;
  color: var(--color-green-7) !important;
}

.button.-outline-green-7 {
  border: 2px solid var(--color-green-7);
}

.button.-outline-green-7:hover {
  background-color: var(--color-green-7);
  border-color: transparent;
  color: white !important;
}

.button.-orange-1 {
  background-color: var(--color-orange-1);
  border: 2px solid var(--color-orange-1);
}

.button.-orange-1:hover {
  background-color: transparent !important;
  color: var(--color-orange-1) !important;
}

.button.-outline-orange-1 {
  border: 2px solid var(--color-orange-1);
}

.button.-outline-orange-1:hover {
  background-color: var(--color-orange-1);
  border-color: transparent;
  color: white !important;
}

.button.-orange-2 {
  background-color: var(--color-orange-2);
  border: 2px solid var(--color-orange-2);
}

.button.-orange-2:hover {
  background-color: transparent !important;
  color: var(--color-orange-2) !important;
}

.button.-outline-orange-2 {
  border: 2px solid var(--color-orange-2);
}

.button.-outline-orange-2:hover {
  background-color: var(--color-orange-2);
  border-color: transparent;
  color: white !important;
}

.button.-orange-3 {
  background-color: var(--color-orange-3);
  border: 2px solid var(--color-orange-3);
}

.button.-orange-3:hover {
  background-color: transparent !important;
  color: var(--color-orange-3) !important;
}

.button.-outline-orange-3 {
  border: 2px solid var(--color-orange-3);
}

.button.-outline-orange-3:hover {
  background-color: var(--color-orange-3);
  border-color: transparent;
  color: white !important;
}

.button.-orange-4 {
  background-color: var(--color-orange-4);
  border: 2px solid var(--color-orange-4);
}

.button.-orange-4:hover {
  background-color: transparent !important;
  color: var(--color-orange-4) !important;
}

.button.-outline-orange-4 {
  border: 2px solid var(--color-orange-4);
}

.button.-outline-orange-4:hover {
  background-color: var(--color-orange-4);
  border-color: transparent;
  color: white !important;
}

.button.-orange-5 {
  background-color: var(--color-orange-5);
  border: 2px solid var(--color-orange-5);
}

.button.-orange-5:hover {
  background-color: transparent !important;
  color: var(--color-orange-5) !important;
}

.button.-outline-orange-5 {
  border: 2px solid var(--color-orange-5);
}

.button.-outline-orange-5:hover {
  background-color: var(--color-orange-5);
  border-color: transparent;
  color: white !important;
}

.button.-orange-6 {
  background-color: var(--color-orange-6);
  border: 2px solid var(--color-orange-6);
}

.button.-orange-6:hover {
  background-color: transparent !important;
  color: var(--color-orange-6) !important;
}

.button.-outline-orange-6 {
  border: 2px solid var(--color-orange-6);
}

.button.-outline-orange-6:hover {
  background-color: var(--color-orange-6);
  border-color: transparent;
  color: white !important;
}

.button.-orange-7 {
  background-color: var(--color-orange-7);
  border: 2px solid var(--color-orange-7);
}

.button.-orange-7:hover {
  background-color: transparent !important;
  color: var(--color-orange-7) !important;
}

.button.-outline-orange-7 {
  border: 2px solid var(--color-orange-7);
}

.button.-outline-orange-7:hover {
  background-color: var(--color-orange-7);
  border-color: transparent;
  color: white !important;
}

.button.-red-1 {
  background-color: var(--color-red-1);
  border: 2px solid var(--color-red-1);
}

.button.-red-1:hover {
  background-color: transparent !important;
  color: var(--color-red-1) !important;
}

.button.-outline-red-1 {
  border: 2px solid var(--color-red-1);
}

.button.-outline-red-1:hover {
  background-color: var(--color-red-1);
  border-color: transparent;
  color: white !important;
}

.button.-red-2 {
  background-color: var(--color-red-2);
  border: 2px solid var(--color-red-2);
}

.button.-red-2:hover {
  background-color: transparent !important;
  color: var(--color-red-2) !important;
}

.button.-outline-red-2 {
  border: 2px solid var(--color-red-2);
}

.button.-outline-red-2:hover {
  background-color: var(--color-red-2);
  border-color: transparent;
  color: white !important;
}

.button.-red-3 {
  background-color: var(--color-red-3);
  border: 2px solid var(--color-red-3);
}

.button.-red-3:hover {
  background-color: transparent !important;
  color: var(--color-red-3) !important;
}

.button.-outline-red-3 {
  border: 2px solid var(--color-red-3);
}

.button.-outline-red-3:hover {
  background-color: var(--color-red-3);
  border-color: transparent;
  color: white !important;
}

.button.-beige-1 {
  background-color: var(--color-beige-1);
  border: 2px solid var(--color-beige-1);
}

.button.-beige-1:hover {
  background-color: transparent !important;
  color: var(--color-beige-1) !important;
}

.button.-outline-beige-1 {
  border: 2px solid var(--color-beige-1);
}

.button.-outline-beige-1:hover {
  background-color: var(--color-beige-1);
  border-color: transparent;
  color: white !important;
}

.button.-blue-1 {
  background-color: var(--color-blue-1);
  border: 2px solid var(--color-blue-1);
}

.button.-blue-1:hover {
  background-color: transparent !important;
  color: var(--color-blue-1) !important;
}

.button.-outline-blue-1 {
  border: 2px solid var(--color-blue-1);
}

.button.-outline-blue-1:hover {
  background-color: var(--color-blue-1);
  border-color: transparent;
  color: white !important;
}

.button.-blue-2 {
  background-color: var(--color-blue-2);
  border: 2px solid var(--color-blue-2);
}

.button.-blue-2:hover {
  background-color: transparent !important;
  color: var(--color-blue-2) !important;
}

.button.-outline-blue-2 {
  border: 2px solid var(--color-blue-2);
}

.button.-outline-blue-2:hover {
  background-color: var(--color-blue-2);
  border-color: transparent;
  color: white !important;
}

.button.-blue-3 {
  background-color: var(--color-blue-3);
  border: 2px solid var(--color-blue-3);
}

.button.-blue-3:hover {
  background-color: transparent !important;
  color: var(--color-blue-3) !important;
}

.button.-outline-blue-3 {
  border: 2px solid var(--color-blue-3);
}

.button.-outline-blue-3:hover {
  background-color: var(--color-blue-3);
  border-color: transparent;
  color: white !important;
}

.button.-blue-4 {
  background-color: var(--color-blue-4);
  border: 2px solid var(--color-blue-4);
}

.button.-blue-4:hover {
  background-color: transparent !important;
  color: var(--color-blue-4) !important;
}

.button.-outline-blue-4 {
  border: 2px solid var(--color-blue-4);
}

.button.-outline-blue-4:hover {
  background-color: var(--color-blue-4);
  border-color: transparent;
  color: white !important;
}

.button.-blue-5 {
  background-color: var(--color-blue-5);
  border: 2px solid var(--color-blue-5);
}

.button.-blue-5:hover {
  background-color: transparent !important;
  color: var(--color-blue-5) !important;
}

.button.-outline-blue-5 {
  border: 2px solid var(--color-blue-5);
}

.button.-outline-blue-5:hover {
  background-color: var(--color-blue-5);
  border-color: transparent;
  color: white !important;
}

.button.-blue-6 {
  background-color: var(--color-blue-6);
  border: 2px solid var(--color-blue-6);
}

.button.-blue-6:hover {
  background-color: transparent !important;
  color: var(--color-blue-6) !important;
}

.button.-outline-blue-6 {
  border: 2px solid var(--color-blue-6);
}

.button.-outline-blue-6:hover {
  background-color: var(--color-blue-6);
  border-color: transparent;
  color: white !important;
}

.button.-blue-7 {
  background-color: var(--color-blue-7);
  border: 2px solid var(--color-blue-7);
}

.button.-blue-7:hover {
  background-color: transparent !important;
  color: var(--color-blue-7) !important;
}

.button.-outline-blue-7 {
  border: 2px solid var(--color-blue-7);
}

.button.-outline-blue-7:hover {
  background-color: var(--color-blue-7);
  border-color: transparent;
  color: white !important;
}

.button.-yellow-1 {
  background-color: var(--color-yellow-1);
  border: 2px solid var(--color-yellow-1);
}

.button.-yellow-1:hover {
  background-color: transparent !important;
  color: var(--color-yellow-1) !important;
}

.button.-outline-yellow-1 {
  border: 2px solid var(--color-yellow-1);
}

.button.-outline-yellow-1:hover {
  background-color: var(--color-yellow-1);
  border-color: transparent;
  color: white !important;
}

.button.-yellow-2 {
  background-color: var(--color-yellow-2);
  border: 2px solid var(--color-yellow-2);
}

.button.-yellow-2:hover {
  background-color: transparent !important;
  color: var(--color-yellow-2) !important;
}

.button.-outline-yellow-2 {
  border: 2px solid var(--color-yellow-2);
}

.button.-outline-yellow-2:hover {
  background-color: var(--color-yellow-2);
  border-color: transparent;
  color: white !important;
}

.button.-yellow-3 {
  background-color: var(--color-yellow-3);
  border: 2px solid var(--color-yellow-3);
}

.button.-yellow-3:hover {
  background-color: transparent !important;
  color: var(--color-yellow-3) !important;
}

.button.-outline-yellow-3 {
  border: 2px solid var(--color-yellow-3);
}

.button.-outline-yellow-3:hover {
  background-color: var(--color-yellow-3);
  border-color: transparent;
  color: white !important;
}

.button.-yellow-4 {
  background-color: var(--color-yellow-4);
  border: 2px solid var(--color-yellow-4);
}

.button.-yellow-4:hover {
  background-color: transparent !important;
  color: var(--color-yellow-4) !important;
}

.button.-outline-yellow-4 {
  border: 2px solid var(--color-yellow-4);
}

.button.-outline-yellow-4:hover {
  background-color: var(--color-yellow-4);
  border-color: transparent;
  color: white !important;
}

.button.-yellow-5 {
  background-color: var(--color-yellow-5);
  border: 2px solid var(--color-yellow-5);
}

.button.-yellow-5:hover {
  background-color: transparent !important;
  color: var(--color-yellow-5) !important;
}

.button.-outline-yellow-5 {
  border: 2px solid var(--color-yellow-5);
}

.button.-outline-yellow-5:hover {
  background-color: var(--color-yellow-5);
  border-color: transparent;
  color: white !important;
}

.button.-info-1 {
  background-color: var(--color-info-1);
  border: 2px solid var(--color-info-1);
}

.button.-info-1:hover {
  background-color: transparent !important;
  color: var(--color-info-1) !important;
}

.button.-outline-info-1 {
  border: 2px solid var(--color-info-1);
}

.button.-outline-info-1:hover {
  background-color: var(--color-info-1);
  border-color: transparent;
  color: white !important;
}

.button.-info-2 {
  background-color: var(--color-info-2);
  border: 2px solid var(--color-info-2);
}

.button.-info-2:hover {
  background-color: transparent !important;
  color: var(--color-info-2) !important;
}

.button.-outline-info-2 {
  border: 2px solid var(--color-info-2);
}

.button.-outline-info-2:hover {
  background-color: var(--color-info-2);
  border-color: transparent;
  color: white !important;
}

.button.-warning-1 {
  background-color: var(--color-warning-1);
  border: 2px solid var(--color-warning-1);
}

.button.-warning-1:hover {
  background-color: transparent !important;
  color: var(--color-warning-1) !important;
}

.button.-outline-warning-1 {
  border: 2px solid var(--color-warning-1);
}

.button.-outline-warning-1:hover {
  background-color: var(--color-warning-1);
  border-color: transparent;
  color: white !important;
}

.button.-warning-2 {
  background-color: var(--color-warning-2);
  border: 2px solid var(--color-warning-2);
}

.button.-warning-2:hover {
  background-color: transparent !important;
  color: var(--color-warning-2) !important;
}

.button.-outline-warning-2 {
  border: 2px solid var(--color-warning-2);
}

.button.-outline-warning-2:hover {
  background-color: var(--color-warning-2);
  border-color: transparent;
  color: white !important;
}

.button.-error-1 {
  background-color: var(--color-error-1);
  border: 2px solid var(--color-error-1);
}

.button.-error-1:hover {
  background-color: transparent !important;
  color: var(--color-error-1) !important;
}

.button.-outline-error-1 {
  border: 2px solid var(--color-error-1);
}

.button.-outline-error-1:hover {
  background-color: var(--color-error-1);
  border-color: transparent;
  color: white !important;
}

.button.-error-2 {
  background-color: var(--color-error-2);
  border: 2px solid var(--color-error-2);
}

.button.-error-2:hover {
  background-color: transparent !important;
  color: var(--color-error-2) !important;
}

.button.-outline-error-2 {
  border: 2px solid var(--color-error-2);
}

.button.-outline-error-2:hover {
  background-color: var(--color-error-2);
  border-color: transparent;
  color: white !important;
}

.button.-success-1 {
  background-color: var(--color-success-1);
  border: 2px solid var(--color-success-1);
}

.button.-success-1:hover {
  background-color: transparent !important;
  color: var(--color-success-1) !important;
}

.button.-outline-success-1 {
  border: 2px solid var(--color-success-1);
}

.button.-outline-success-1:hover {
  background-color: var(--color-success-1);
  border-color: transparent;
  color: white !important;
}

.button.-success-2 {
  background-color: var(--color-success-2);
  border: 2px solid var(--color-success-2);
}

.button.-success-2:hover {
  background-color: transparent !important;
  color: var(--color-success-2) !important;
}

.button.-outline-success-2 {
  border: 2px solid var(--color-success-2);
}

.button.-outline-success-2:hover {
  background-color: var(--color-success-2);
  border-color: transparent;
  color: white !important;
}

.button.-white-20 {
  background-color: rgba(255, 255, 255, 0.2);
}

.button.-white-20:hover {
  background-color: white;
  color: var(--color-dark-1) !important;
}

.button.-gradient-1 {
  background: linear-gradient(90deg, #6440FB 0%, #E8543E 108.34%);
}

.button.-gradient-1:hover {
  color: var(--color-dark-1) !important;
}

.button.-purple-3:hover {
  color: white !important;
  background-color: var(--color-purple-1) !important;
}

.button.-dark-6:hover {
  color: var(--color-dark-1) !important;
  background-color: white !important;
}

.button.-light-7:hover {
  color: white !important;
  background-color: var(--color-purple-1) !important;
  border-color: var(--color-purple-1) !important;
}

.button.-outline-green-1:hover {
  color: var(--color-dark-1) !important;
}

.button.-outline-white:hover {
  color: var(--color-dark-1) !important;
}

.button.-outline-light-5:hover {
  color: var(--color-dark-1) !important;
}


/* styles.css */
.btn-nurtify {
  background-color: #37B7C3; /* Use the correct CSS property for background color */
  width: 180px;
  font-weight: 700; /* Use font-weight instead of fontWeight */
  border-radius: 60px;
  height: 45px;
  border: none; /* Optional: Remove border */
  color: white; /* Optional: Set text color */
  cursor: pointer; /* Optional: Change cursor on hover */
  transition: background-color 0.3s; /* Optional: Smooth transition */
  text-decoration: none;
}

.btn-nurtify-white{
  background-color: white; /* Use the correct CSS property for background color */
  width: 228px;
  text-decoration: none;
  font-weight: 700; /* Use font-weight instead of fontWeight */
  border-radius: 35px;
  border: none; /* Optional: Remove border */
  color: black; /* Optional: Set text color */
  cursor: pointer; /* Optional: Change cursor on hover */
  padding: 21px 55px;
  transition: background-color 0.3s; /* Optional: Smooth transition */
}

.btn-nurtifywithBorder {
  background-color: white; /* Use the correct CSS property for background color */
  width: 180px;
  border:1px solid #37B7C3;
  font-weight: 700; /* Use font-weight instead of fontWeight */
  border-radius: 60px;
  height: 45px;
  color: #37B7C3; /* Optional: Set text color */
  cursor: pointer; /* Optional: Change cursor on hover */
  transition: background-color 0.3s; /* Optional: Smooth transition */
  text-decoration: none;
}

.bg-nurtify{
  background-color: #37B7C3; /* Use the correct CSS property for background color */
}

.bg-nurtify-forms{
  cursor: pointer;
  background-color: #37B7C3; /* Use the correct CSS property for background color */
}

.bg-nurtify-forms:hover{
  background-color:#0d848fe2; /* Use the correct CSS property for background color */
}

.btn-nurtify-light {
  color: #37B7C3; /* Use the correct CSS property for background color */
  width: 180px;
  font-weight: 700; /* Use font-weight instead of fontWeight */
  border-radius: 60px;
  height: 45px;
  border: none; /* Optional: Remove border */
  cursor: pointer; /* Optional: Change cursor on hover */
  transition: background-color 0.3s; /* Optional: Smooth transition */
  text-decoration: none;
}
.btn-nurtify-darker {
  color: white; /* Use the correct CSS property for background color */
  width: 312px;
  font-weight: 500; /* Use font-weight instead of fontWeight */
  border-radius: 60px;
  background-color: #03053A;
  height: 45px;
  text-decoration: none;
  font-size: 16px;
  border: none; /* Optional: Remove border */
  cursor: pointer; /* Optional: Change cursor on hover */
  transition: background-color 0.3s; /* Optional: Smooth transition */
}

.btn-nurtify-lighter{
  background-color: #37B7C333; /* Use the correct CSS property for background color */
  width: auto;
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 20px;
  font-weight: 500; /* Use font-weight instead of fontWeight */
  border-radius: 60px;
  height: 45px;
  text-decoration: none;
  border: none; /* Optional: Remove border */
  color: #37B7C3 !important; /* Optional: Set text color */
  cursor: pointer; /* Optional: Change cursor on hover */
  transition: background-color 0.3s; /* Optional: Smooth transition */
}
.btn-nurtify-gray{
  background-color: #EEF2F6; /* Use the correct CSS property for background color */
  width: auto;
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 20px;
  font-weight: 500; /* Use font-weight instead of fontWeight */
  border-radius: 60px;
  height: 45px;
  border: 1px solid #DDDDDD; /* Optional: Remove border */
  color: #4F547B !important; /* Optional: Set text color */
  cursor: pointer; /* Optional: Change cursor on hover */
  transition: background-color 0.3s; /* Optional: Smooth transition */
}

.btn-nurtify-grayer{
  background-color: #EEF2F6; /* Use the correct CSS property for background color */
  width: auto;
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 20px;
  font-weight: 500; /* Use font-weight instead of fontWeight */
  border-radius: 8px;
  height: 45px;
  color: #37B7C3 !important; /* Optional: Set text color */
  cursor: pointer; /* Optional: Change cursor on hover */
  transition: background-color 0.3s; /* Optional: Smooth transition */
  text-decoration: none;

}

.btn-nurtify:hover {
  background-color: #2a9a9f; /* Optional: Change color on hover */
  text-decoration: none;
  color: white !important;
}

@media (min-width: 1200px) {
  .header .header-menu {
    position: absolute;
    left: 0;
    z-index: 5;
  }
  .header .header-menu .header-menu__content {
    display: flex;
    align-items: center;
  }
  .header .header-menu-close {
    display: none;
  }
  .header .header-menu-bg {
    display: none;
  }
  .header .header-menu .menu {
    position: absolute;
    left: 50%;
    transform: translateX(-50%);
  }
  .header .header-menu .menu__nav {
    display: flex;
    gap: 40px;
    font-weight: 500;
  }
  .header .header-menu .menu__nav a {
    display: flex;
    align-items: center;
    padding: 7px 16px;
    border-radius: 8px;
    transition: all 0.2s cubic-bezier(0.215, 0.61, 0.355, 1);
  }
}

@media (min-width: 1200px) and (max-width: 1399px) {
  .header .header-menu .menu__nav a {
    padding: 6px 6px;
  }
}

@media (min-width: 1200px) {
  .header .header-menu .menu__nav li.menu-item-has-children a {
    display: flex;
    align-items: center;
  }
  .header .header-menu .menu__nav > li.menu-item-has-children > a .icon-chevron-right {
    transform: rotate(90deg);
  }
  .header .header-menu .menu__nav > li {
    padding: 17px 0;
  }
  .header .header-menu .menu__nav > li:hover > a {
    color: var(--color-green-1);
    background-color: rgba(255, 255, 255, 0.15);
  }
  .header .header-menu .menu__nav > li > .subnav::before {
    content: '';
    position: absolute;
    top: -5px;
    left: 20px;
    width: 10px;
    height: 10px;
    background-color: white;
    transform: rotate(45deg);
  }
  .header .header-menu .menu li.-has-mega-menu:hover > .mega {
    opacity: 1 !important;
    pointer-events: auto !important;
  }
  .header .header-menu .menu li:hover > .subnav {
    opacity: 1;
    pointer-events: auto;
  }
  .header .header-menu .menu .subnav {
    position: absolute;
    top: 100%;
    background-color: white;
    border-radius: 8px;
    color: black !important;
    min-width: 230px;
    padding-top: 20px;
    padding-bottom: 20px;
    box-shadow: 0px 25px 70px 0px #01213A12;
    opacity: 0;
    pointer-events: none;
    transition: all 0.2s cubic-bezier(0.215, 0.61, 0.355, 1);
  }
  .header .header-menu .menu .subnav .subnav {
    top: 0;
    left: 97%;
  }
  .header .header-menu .menu .subnav > li {
    position: relative;
  }
  .header .header-menu .menu .subnav > li > a {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 10px 30px;
  }
  .header .header-menu .menu .subnav > li > a:hover {
    color: var(--color-purple-1);
  }
  .header .header-menu .menu .subnav .menu__backButton {
    display: none;
  }
  .header .header-menu .menu > .subnav {
    top: 100%;
    left: 0;
  }
  .header .header-menu .mobile-bg {
    display: none;
  }
  .header .header-menu .mobile-back-button {
    display: none;
  }
  .header .header-menu .mobile-footer {
    display: none;
  }
}

@media (max-width: 1199px) {
  .header .header-menu {
    position: fixed !important;
    top: 0;
    left: 0;
    z-index: 10;
    max-width: calc(100vw - 80px);
    width: 60vw;
    height: 100vh;
    padding: 0 !important;
    transition: all 0.3s cubic-bezier(0.215, 0.61, 0.355, 1);
    opacity: 0;
    pointer-events: none;
  }
  .header .header-menu .header-menu__content {
    display: flex;
    flex-direction: column;
    height: 100%;
    transition: all 0.3s cubic-bezier(0.215, 0.61, 0.355, 1);
    transform: translateX(-20%);
  }
  .header .header-menu ul {
    overflow-y: hidden;
    overflow-x: hidden;
  }
  .header .header-menu.-is-el-visible {
    opacity: 1;
    pointer-events: auto;
  }
  .header .header-menu.-is-el-visible .-is-active {
    pointer-events: auto !important;
    overflow-y: scroll;
    overflow-x: hidden;
  }
  .header .header-menu.-is-el-visible .header-menu__content {
    transform: none;
  }
}

@media (max-width: 1199px) and (max-width: 767px) {
  .header .header-menu {
    width: 80vw;
  }
}

@media (max-width: 1199px) and (max-width: 575px) {
  .header .header-menu {
    width: calc(100vw - 60px);
    max-width: 100%;
  }
}

@media (max-width: 1199px) {
  .header .header-menu-close {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: -1;
  }
}

@media (max-width: 1199px) and (max-width: 575px) {
  .header .header-menu-close {
    top: 10px;
    right: 10px;
  }
}

@media (max-width: 1199px) {
  .header .header-menu-bg {
    position: fixed;
    top: 0;
    bottom: 0;
    left: 0;
    right: 0;
    background-color: rgba(24, 24, 26, 0.7);
    z-index: -2;
  }
  .header .header-menu .menu {
    overflow-y: hidden;
    overflow-x: hidden;
    position: relative;
    padding: 20px;
    height: 100%;
  }
  .header .header-menu .menu ul {
    pointer-events: none;
  }
  .header .header-menu .menu__nav {
    display: flex;
    flex-direction: column;
  }
  .header .header-menu .menu__nav a {
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 60px;
    padding: 0 20px;
    border-radius: 8px;
    color: var(--color-dark-1) !important;
    transition: all 0.2s cubic-bezier(0.215, 0.61, 0.355, 1);
  }
  .header .header-menu .menu__nav a:hover {
    color: var(--color-purple-1) !important;
    background-color: #F4F2FF;
  }
  .header .header-menu .menu__nav > li > a {
    font-weight: 500;
    font-size: 16px;
  }
  .header .header-menu .menu__nav li {
    overflow: hidden;
  }
  .header .header-menu .menu .subnav {
    position: absolute;
    top: 0;
    left: 0;
    color: black !important;
    padding: 20px;
    width: 100%;
    height: 100%;
  }
  .header .header-menu .menu .subnav a {
    transform: translateY(100%);
  }
  .header .header-menu .menu .subnav > li > a {
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 45px;
    padding: 0 20px;
    color: var(--color-dark-1) !important;
    transition: color 0.2s cubic-bezier(0.215, 0.61, 0.355, 1);
  }
  .header .header-menu .menu .subnav > li > a:hover {
    color: var(--color-purple-1) !important;
  }
  .header .header-menu .menu .subnav .menu__backButton a {
    justify-content: flex-start !important;
    height: 60px !important;
    font-weight: 600 !important;
    color: var(--color-purple-1) !important;
    font-size: 16px;
    background-color: var(--color-purple-3);
  }
  .header .header-menu .mobile-bg {
    position: fixed;
    top: 0;
    left: 0;
    max-width: calc(100vw - 80px);
    width: 60vw;
    height: 100vh;
    z-index: -1;
    background-color: white;
  }
}

@media (max-width: 1199px) and (max-width: 767px) {
  .header .header-menu .mobile-bg {
    width: 80vw;
  }
}

@media (max-width: 1199px) and (max-width: 575px) {
  .header .header-menu .mobile-bg {
    width: calc(100vw - 60px);
    max-width: 100%;
  }
}

@media (max-width: 1199px) {
  .header .header-menu .mobile-footer .mobile-socials {
    display: flex;
    margin-left: -15px;
  }
  .header .header-menu .mobile-footer .mobile-socials a {
    transition: all 0.2s cubic-bezier(0.215, 0.61, 0.355, 1);
  }
  .header .header-menu .mobile-footer .mobile-socials a:hover {
    background-color: var(--color-light-4);
    color: var(--color-purple-1) !important;
  }
}

.header.-base {
  z-index: 100;
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
}

.header.-base .header__container {
  position: relative;
  padding: 0 60px;
}

@media (max-width: 991px) {
  .header.-base .header__container {
    padding: 0 40px;
  }
}

@media (max-width: 767px) {
  .header.-base .header__container {
    padding: 0 15px;
  }
}

.header.-base .header-menu {
  position: relative;
}

.header.-base .header-menu .menu {
  position: relative;
  left: unset;
  transform: none;
}

.header.-type-3 {
  position: fixed;
  top: 0;
  left: 0;
  z-index: 100;
  background-color: white;
  width: 100%;
  padding: 0 60px;
}

@media (max-width: 991px) {
  .header.-type-3 {
    padding: 0 40px;
  }
}

@media (max-width: 767px) {
  .header.-type-3 {
    padding: 0 15px;
  }
}

.header.-type-3 .header__container {
  position: relative;
  width: 100%;
  border-bottom: 1px solid rgba(0, 0, 0, 0.15);
}

.header.-type-3 .header-menu {
  position: relative;
}

.header.-type-3 .header-menu .menu {
  position: relative;
  left: unset;
  transform: none;
}

@media (max-width: 1670px) {
  .header.-type-3 .header-search-field {
    display: none;
  }
}

.header.-type-3 .header-search-field__group {
  position: relative;
  max-width: 340px;
  min-width: 260px;
  box-shadow: none;
}

.header.-type-3 .header-search-field input {
  width: 100%;
  font-size: 13px;
  line-height: 1;
  background-color: var(--color-light-3);
  border-radius: 8px;
  padding: 18px 28px;
  box-shadow: none;
  border: 1px solid #21858E40;
}

.header.-type-3 .header-search-field button {
  position: absolute;
  top: 0;
  right: 0;
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
  width: 40px;
}

.header.-type-3 .header-search-field button .icon {
  font-size: 20px;
  color: var(--color-dark-1);
}

.header.-type-4 {
  position: fixed;
  top: 0;
  left: 0;
  z-index: 100;
  width: 100%;
  padding: 0 60px;
}

@media (max-width: 1199px) {
  .header.-type-4 {
    padding: 0 45px;
  }
}

@media (max-width: 991px) {
  .header.-type-4 {
    padding: 0 15px;
  }
}

.header.-type-4.-shadow {
  box-shadow: 0px 6px 15px 0px #404F680D;
}

.header.-type-4 *.-before-border {
  position: relative;
}

.header.-type-4 *.-before-border::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 1px;
  height: 100%;
  background-color: #EDEDED;
}

.header.-type-4 *.-after-border {
  position: relative;
}

.header.-type-4 *.-after-border::after {
  content: "";
  position: absolute;
  top: 0;
  right: 0;
  width: 1px;
  height: 100%;
  background-color: #EDEDED;
}

.header.-type-4 .header__container {
  position: relative;
}

.header.-type-4 .header__container .row {
  flex-wrap: nowrap;
}

.header.-type-4 .header-menu {
  position: relative;
}

.header.-type-4 .header-menu .menu {
  position: relative;
  left: unset;
  transform: none;
}

.header.-type-4 .header-search-field {
  position: relative;
}

@media (max-width: 1670px) {
  .header.-type-4 .header-search-field {
    display: none;
  }
}

.header.-type-4 .header-search-field__group {
  position: relative;
  max-width: 300px;
  min-width: 250px;
}

.header.-type-4 .header-search-field input {
  width: 100%;
  font-size: 13px;
  line-height: 1;
  background-color: white;
  border: 1px solid #EDEDED;
  border-radius: 100px;
  padding: 17px 28px;
}

.header.-type-4 .header-search-field button {
  position: absolute;
  top: 0;
  right: 0;
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
  width: 60px;
}

.header.-type-4 .header-search-field button .icon {
  font-size: 20px;
  color: var(--color-dark-1);
}

.header.-type-5 {
  z-index: 100;
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
}

.header.-type-5 .header__container {
  width: 100%;
  padding: 0 60px;
}

@media (max-width: 1199px) {
  .header.-type-5 .header__container {
    padding: 0 45px;
  }
}

@media (max-width: 991px) {
  .header.-type-5 .header__container {
    padding: 0 15px;
  }
}

.header.-type-5 .header__container .row {
  flex-wrap: nowrap;
}

.header.-type-5 .header-menu {
  position: relative;
}

.header.-type-5 .header-menu .menu {
  position: relative;
  left: unset;
  transform: none;
}

.header.-base-sidebar {
  z-index: 100;
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  padding: 0 20px;
}

.header.-base-sidebar .row {
  flex-wrap: nowrap;
}

@media (max-width: 767px) {
  .header.-base-sidebar .row {
    margin: 0;
  }
  .header.-base-sidebar .col-auto {
    padding: 0;
  }
}

@media (max-width: 575px) {
  .header.-base-sidebar .header__logo img {
    width: 80%;
  }
}

@media (max-width: 991px) {
  .dashboard.-home-9 {
    margin-top: 30px;
  }
}

@media (max-width: 767px) {
  .dashboard.-home-9 {
    margin-top: 60px;
  }
}

.dashboard.-home-9 .dashboard__sidebar {
  will-change: transform;
  transition: all 0.5s cubic-bezier(0.215, 0.61, 0.355, 1);
}

@media (max-width: 991px) {
  .dashboard.-home-9 .dashboard__sidebar {
    z-index: 110;
    position: fixed;
    top: 0;
    bottom: 0;
    width: 300px;
    margin-top: 90px;
  }
}

@media (max-width: 767px) {
  .dashboard.-home-9 .dashboard__sidebar {
    width: 80%;
  }
}

.dashboard.-home-9 .dashboard__main {
  will-change: padding-left;
  transition: all 0.5s cubic-bezier(0.215, 0.61, 0.355, 1);
}

@media (max-width: 991px) {
  .dashboard.-home-9 .dashboard__main::after {
    content: "";
    position: fixed;
    z-index: 50;
    top: 0;
    bottom: 0;
    left: 0;
    right: 0;
    background-color: rgba(0, 0, 0, 0.5);
    pointer-events: auto;
    opacity: 1;
    transition: all 0.5s cubic-bezier(0.215, 0.61, 0.355, 1);
  }
}

.dashboard.-home-9.-is-sidebar-hidden .dashboard__sidebar {
  transform: translateX(-100%);
}

.dashboard.-home-9.-is-sidebar-hidden .dashboard__main {
  padding-left: 0;
}

.dashboard.-home-9.-is-sidebar-hidden .dashboard__main::after {
  pointer-events: none;
  opacity: 0;
}

.sidebar.-base-sidebar .sidebar__inner {
  padding-top: 40px;
}

.sidebar.-base-sidebar .sidebar__item {
  display: flex;
  align-items: center;
  border-radius: 16px;
  padding: 0 20px;
  height: 55px;
}

@media (max-width: 575px) {
  .sidebar.-base-sidebar .sidebar__item {
    border-radius: 0;
    height: 45px;
  }
}

.sidebar.-base-sidebar .sidebar__item.-is-active {
  /* background-color: var(--color-purple-3); */
  color: var(--color-purple-1) !important;
}

.sidebar.-base-sidebar .sidebar__links > div > a {
  display: flex;
  align-items: center;
}

.sidebar.-base-sidebar .sidebar__links > div > a::before {
  content: "";
  display: inline-block;
  margin-right: 10px;
  background-color: currentColor;
  border-radius: 100%;
  width: 5px;
  height: 5px;
}

.sidebar.-base-sidebar .sidebar__links > div > a:hover {
  color: var(--color-dark-1);
}

.sidebar.-base-sidebar .sidebar__links > div > a:hover::before {
  background-color: var(--color-dark-1);
}

.header {
  transition: all 0.2s cubic-bezier(0.215, 0.61, 0.355, 1);
  max-width: 100% !important;
  
}

.header__explore > a {
  position: relative;
  z-index: 1;
}

.header__explore > a::before {
  content: "";
  position: absolute;
  top: -6px;
  bottom: -6px;
  left: -14px;
  right: -14px;
  z-index: -1;
  background-color: rgba(255, 255, 255, 0.15);
  border-radius: 8px;
  transition: all 0.2s cubic-bezier(0.165, 0.84, 0.44, 1);
  opacity: 0;
}

.header__explore > a:hover {
  color: var(--color-green-1) !important;
}

.header__explore > a:hover::before {
  opacity: 1;
}

.header.-type-1 {
  z-index: 100;
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  background-color: white;
  padding: 0 20px;
}

@media (max-width: 991px) {
  .header.-type-1 {
    padding: 0;
  }
}

.header.-type-1 .header__container {
  position: relative;
  max-width: calc(1500px);
  padding: 20px 0;
  border-bottom: 1px solid #E5E9EB;
  margin: 0 auto;
}

@media (max-width: 991px) {
  .header.-type-1 .header__container {
    padding: 20px;
  }
}

.header.-type-1 .header-left {
  display: flex;
  align-items: center;
}

@media (min-width: 1200px) {
  .header .header-menu {
    position: absolute;
    left: 0;
    z-index: 5;
  }
  .header .header-menu .header-menu__content {
    display: flex;
    align-items: center;
  }
  .header .header-menu-close {
    display: none;
  }
  .header .header-menu-bg {
    display: none;
  }
  .header .header-menu .menu {
    position: absolute;
    left: 50%;
    transform: translateX(-50%);
  }
  .header .header-menu .menu__nav {
    display: flex;
    gap: 40px;
    font-weight: 500;
  }
  .header .header-menu .menu__nav a {
    display: flex;
    align-items: center;
    padding: 7px 16px;
    border-radius: 8px;
    transition: all 0.2s cubic-bezier(0.215, 0.61, 0.355, 1);
  }
}

@media (min-width: 1200px) and (max-width: 1399px) {
  .header .header-menu .menu__nav a {
    padding: 6px 6px;
  }
}

@media (min-width: 1200px) {
  .header .header-menu .menu__nav li.menu-item-has-children a {
    display: flex;
    align-items: center;
  }
  .header .header-menu .menu__nav > li.menu-item-has-children > a .icon-chevron-right {
    transform: rotate(90deg);
  }
  .header .header-menu .menu__nav > li {
    padding: 17px 0;
  }
  .header .header-menu .menu__nav > li:hover > a {
    color: var(--color-green-1);
    background-color: rgba(255, 255, 255, 0.15);
  }
  .header .header-menu .menu__nav > li > .subnav::before {
    content: '';
    position: absolute;
    top: -5px;
    left: 20px;
    width: 10px;
    height: 10px;
    background-color: white;
    transform: rotate(45deg);
  }
  .header .header-menu .menu li.-has-mega-menu:hover > .mega {
    opacity: 1 !important;
    pointer-events: auto !important;
  }
  .header .header-menu .menu li:hover > .subnav {
    opacity: 1;
    pointer-events: auto;
  }
  .header .header-menu .menu .subnav {
    position: absolute;
    top: 100%;
    background-color: white;
    border-radius: 8px;
    color: black !important;
    min-width: 230px;
    padding-top: 20px;
    padding-bottom: 20px;
    box-shadow: 0px 25px 70px 0px #01213A12;
    opacity: 0;
    pointer-events: none;
    transition: all 0.2s cubic-bezier(0.215, 0.61, 0.355, 1);
  }
  .header .header-menu .menu .subnav .subnav {
    top: 0;
    left: 97%;
  }
  .header .header-menu .menu .subnav > li {
    position: relative;
  }
  .header .header-menu .menu .subnav > li > a {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 10px 30px;
  }
  .header .header-menu .menu .subnav > li > a:hover {
    color: var(--color-purple-1);
  }
  .header .header-menu .menu .subnav .menu__backButton {
    display: none;
  }
  .header .header-menu .menu > .subnav {
    top: 100%;
    left: 0;
  }
  .header .header-menu .mobile-bg {
    display: none;
  }
  .header .header-menu .mobile-back-button {
    display: none;
  }
  .header .header-menu .mobile-footer {
    display: none;
  }
}

@media (max-width: 1199px) {
  .header .header-menu {
    position: fixed !important;
    top: 0;
    left: 0;
    z-index: 10;
    max-width: calc(100vw - 80px);
    width: 60vw;
    height: 100vh;
    padding: 0 !important;
    transition: all 0.3s cubic-bezier(0.215, 0.61, 0.355, 1);
    opacity: 0;
    pointer-events: none;
  }
  .header .header-menu .header-menu__content {
    display: flex;
    flex-direction: column;
    height: 100%;
    transition: all 0.3s cubic-bezier(0.215, 0.61, 0.355, 1);
    transform: translateX(-20%);
  }
  .header .header-menu ul {
    overflow-y: hidden;
    overflow-x: hidden;
  }
  .header .header-menu.-is-el-visible {
    opacity: 1;
    pointer-events: auto;
  }
  .header .header-menu.-is-el-visible .-is-active {
    pointer-events: auto !important;
    overflow-y: scroll;
    overflow-x: hidden;
  }
  .header .header-menu.-is-el-visible .header-menu__content {
    transform: none;
  }
}

@media (max-width: 1199px) and (max-width: 767px) {
  .header .header-menu {
    width: 80vw;
  }
}

@media (max-width: 1199px) and (max-width: 575px) {
  .header .header-menu {
    width: calc(100vw - 60px);
    max-width: 100%;
  }
}

@media (max-width: 1199px) {
  .header .header-menu-close {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: -1;
  }
}

@media (max-width: 1199px) and (max-width: 575px) {
  .header .header-menu-close {
    top: 10px;
    right: 10px;
  }
}

@media (max-width: 1199px) {
  .header .header-menu-bg {
    position: fixed;
    top: 0;
    bottom: 0;
    left: 0;
    right: 0;
    background-color: rgba(24, 24, 26, 0.7);
    z-index: -2;
  }
  .header .header-menu .menu {
    overflow-y: hidden;
    overflow-x: hidden;
    position: relative;
    padding: 20px;
    height: 100%;
  }
  .header .header-menu .menu ul {
    pointer-events: none;
  }
  .header .header-menu .menu__nav {
    display: flex;
    flex-direction: column;
  }
  .header .header-menu .menu__nav a {
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 60px;
    padding: 0 20px;
    border-radius: 8px;
    color: var(--color-dark-1) !important;
    transition: all 0.2s cubic-bezier(0.215, 0.61, 0.355, 1);
  }
  .header .header-menu .menu__nav a:hover {
    color: var(--color-purple-1) !important;
    background-color: #F4F2FF;
  }
  .header .header-menu .menu__nav > li > a {
    font-weight: 500;
    font-size: 16px;
  }
  .header .header-menu .menu__nav li {
    overflow: hidden;
  }
  .header .header-menu .menu .subnav {
    position: absolute;
    top: 0;
    left: 0;
    color: black !important;
    padding: 20px;
    width: 100%;
    height: 100%;
  }
  .header .header-menu .menu .subnav a {
    transform: translateY(100%);
  }
  .header .header-menu .menu .subnav > li > a {
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 45px;
    padding: 0 20px;
    color: var(--color-dark-1) !important;
    transition: color 0.2s cubic-bezier(0.215, 0.61, 0.355, 1);
  }
  .header .header-menu .menu .subnav > li > a:hover {
    color: var(--color-purple-1) !important;
  }
  .header .header-menu .menu .subnav .menu__backButton a {
    justify-content: flex-start !important;
    height: 60px !important;
    font-weight: 600 !important;
    color: var(--color-purple-1) !important;
    font-size: 16px;
    background-color: var(--color-purple-3);
  }
  .header .header-menu .mobile-bg {
    position: fixed;
    top: 0;
    left: 0;
    max-width: calc(100vw - 80px);
    width: 60vw;
    height: 100vh;
    z-index: -1;
    background-color: white;
  }
}

@media (max-width: 1199px) and (max-width: 767px) {
  .header .header-menu .mobile-bg {
    width: 80vw;
  }
}

@media (max-width: 1199px) and (max-width: 575px) {
  .header .header-menu .mobile-bg {
    width: calc(100vw - 60px);
    max-width: 100%;
  }
}

@media (max-width: 1199px) {
  .header .header-menu .mobile-footer .mobile-socials {
    display: flex;
    margin-left: -15px;
  }
  .header .header-menu .mobile-footer .mobile-socials a {
    transition: all 0.2s cubic-bezier(0.215, 0.61, 0.355, 1);
  }
  .header .header-menu .mobile-footer .mobile-socials a:hover {
    background-color: var(--color-light-4);
    color: var(--color-purple-1) !important;
  }
}

.header.-base {
  z-index: 100;
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
}

.header.-base .header__container {
  position: relative;
  padding: 0 60px;
}

@media (max-width: 991px) {
  .header.-base .header__container {
    padding: 0 40px;
  }
}

@media (max-width: 767px) {
  .header.-base .header__container {
    padding: 0 15px;
  }
}

.header.-base .header-menu {
  position: relative;
}

.header.-base .header-menu .menu {
  position: relative;
  left: unset;
  transform: none;
}

.header.-type-3 {
  position: fixed;
  top: 0;
  left: 0;
  z-index: 100;
  background-color: white;
  width: 100%;
  padding: 0 60px;
}

@media (max-width: 991px) {
  .header.-type-3 {
    padding: 0 40px;
  }
}

@media (max-width: 767px) {
  .header.-type-3 {
    padding: 0 15px;
  }
}

.header.-type-3 .header__container {
  position: relative;
  width: 100%;
  border-bottom: 1px solid rgba(0, 0, 0, 0.15);
}

.header.-type-3 .header-menu {
  position: relative;
}

.header.-type-3 .header-menu .menu {
  position: relative;
  left: unset;
  transform: none;
}

@media (max-width: 1670px) {
  .header.-type-3 .header-search-field {
    display: none;
  }
}

.header.-type-3 .header-search-field__group {
  position: relative;
  max-width: 340px;
  min-width: 260px;
}

.header.-type-3 .header-search-field input {
  width: 100%;
  font-size: 13px;
  line-height: 1;
  background-color: var(--color-light-3);
  border-radius: 8px;
  padding: 18px 28px;
}

.header.-type-3 .header-search-field button {
  position: absolute;
  top: 0;
  right: 0;
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
  width: 40px;
}

.header.-type-3 .header-search-field button .icon {
  font-size: 20px;
  color: var(--color-dark-1);
}

.header.-type-4 {
  position: fixed;
  top: 0;
  left: 0;
  z-index: 100;
  width: 100%;
  padding: 0 60px;
}

@media (max-width: 1199px) {
  .header.-type-4 {
    padding: 0 45px;
  }
}

@media (max-width: 991px) {
  .header.-type-4 {
    padding: 0 15px;
  }
}

.header.-type-4.-shadow {
  box-shadow: 0px 6px 15px 0px #404F680D;
}

.header.-type-4 *.-before-border {
  position: relative;
}

.header.-type-4 *.-before-border::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 1px;
  height: 100%;
  background-color: #EDEDED;
}

.header.-type-4 *.-after-border {
  position: relative;
}

.header.-type-4 *.-after-border::after {
  content: "";
  position: absolute;
  top: 0;
  right: 0;
  width: 1px;
  height: 100%;
  background-color: #EDEDED;
}

.header.-type-4 .header__container {
  position: relative;
}

.header.-type-4 .header__container .row {
  flex-wrap: nowrap;
}

.header.-type-4 .header-menu {
  position: relative;
}

.header.-type-4 .header-menu .menu {
  position: relative;
  left: unset;
  transform: none;
}

.header.-type-4 .header-search-field {
  position: relative;
}

@media (max-width: 1670px) {
  .header.-type-4 .header-search-field {
    display: none;
  }
}

.header.-type-4 .header-search-field__group {
  position: relative;
  max-width: 300px;
  min-width: 250px;
}

.header.-type-4 .header-search-field input {
  width: 100%;
  font-size: 13px;
  line-height: 1;
  background-color: white;
  border: 1px solid #EDEDED;
  border-radius: 100px;
  padding: 17px 28px;
}

.header.-type-4 .header-search-field button {
  position: absolute;
  top: 0;
  right: 0;
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
  width: 60px;
}

.header.-type-4 .header-search-field button .icon {
  font-size: 20px;
  color: var(--color-dark-1);
}

.header.-type-5 {
  z-index: 100;
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
}

.header.-type-5 .header__container {
  width: 100%;
  padding: 0 60px;
}

@media (max-width: 1199px) {
  .header.-type-5 .header__container {
    padding: 0 45px;
  }
}

@media (max-width: 991px) {
  .header.-type-5 .header__container {
    padding: 0 15px;
  }
}

.header.-type-5 .header__container .row {
  flex-wrap: nowrap;
}

.header.-type-5 .header-menu {
  position: relative;
}

.header.-type-5 .header-menu .menu {
  position: relative;
  left: unset;
  transform: none;
}

.header.-base-sidebar {
  z-index: 100;
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  padding: 0 20px;
}

.header.-base-sidebar .row {
  flex-wrap: nowrap;
}

@media (max-width: 767px) {
  .header.-base-sidebar .row {
    margin: 0;
  }
  .header.-base-sidebar .col-auto {
    padding: 0;
  }
}

@media (max-width: 575px) {
  .header.-base-sidebar .header__logo img {
    width: 80%;
  }
}

@media (max-width: 991px) {
  .dashboard.-home-9 {
    margin-top: 30px;
  }
}

@media (max-width: 767px) {
  .dashboard.-home-9 {
    margin-top: 60px;
  }
}

.dashboard.-home-9 .dashboard__sidebar {
  will-change: transform;
  transition: all 0.5s cubic-bezier(0.215, 0.61, 0.355, 1);
}

@media (max-width: 991px) {
  .dashboard.-home-9 .dashboard__sidebar {
    z-index: 110;
    position: fixed;
    top: 0;
    bottom: 0;
    width: 300px;
    margin-top: 90px;
  }
}

@media (max-width: 767px) {
  .dashboard.-home-9 .dashboard__sidebar {
    width: 80%;
  }
}

.dashboard.-home-9 .dashboard__main {
  will-change: padding-left;
  transition: all 0.5s cubic-bezier(0.215, 0.61, 0.355, 1);
}

@media (max-width: 991px) {
  .dashboard.-home-9 .dashboard__main::after {
    content: "";
    position: fixed;
    z-index: 50;
    top: 0;
    bottom: 0;
    left: 0;
    right: 0;
    background-color: rgba(0, 0, 0, 0.5);
    pointer-events: auto;
    opacity: 1;
    transition: all 0.5s cubic-bezier(0.215, 0.61, 0.355, 1);
  }
}

.dashboard.-home-9.-is-sidebar-hidden .dashboard__sidebar {
  transform: translateX(-100%);
}

.dashboard.-home-9.-is-sidebar-hidden .dashboard__main {
  padding-left: 0;
}

.dashboard.-home-9.-is-sidebar-hidden .dashboard__main::after {
  pointer-events: none;
  opacity: 0;
}

.sidebar.-base-sidebar .sidebar__inner {
  padding-top: 40px;
}

.sidebar.-base-sidebar .sidebar__item {
  display: flex;
  align-items: center;
  border-radius: 16px;
  padding: 0 20px;
  height: 55px;
}

@media (max-width: 575px) {
  .sidebar.-base-sidebar .sidebar__item {
    border-radius: 0;
    height: 45px;
  }
}

.sidebar.-base-sidebar .sidebar__item.-is-active {
  /* background-color: var(--color-purple-3); */
  color: var(--color-purple-1) !important;
}

.sidebar.-base-sidebar .sidebar__links > div > a {
  display: flex;
  align-items: center;
}

.sidebar.-base-sidebar .sidebar__links > div > a::before {
  content: "";
  display: inline-block;
  margin-right: 10px;
  background-color: currentColor;
  border-radius: 100%;
  width: 5px;
  height: 5px;
}

.sidebar.-base-sidebar .sidebar__links > div > a:hover {
  color: var(--color-dark-1);
}

.sidebar.-base-sidebar .sidebar__links > div > a:hover::before {
  background-color: var(--color-dark-1);
}

.header .header-search {
  position: fixed;
  top: 0;
  left: 0;
  z-index: 10;
  width: 100%;
  height: 590px;
  box-shadow: none;
}

.header .header-search__field {
  position: relative;
}

.header .header-search__field input {
  padding: 18px 36px;
  border-bottom: 1px solid #EDEDED;
}

.header .header-search__field input:focus {
  outline: none;
  border-bottom: 1px solid var(--color-dark-1);
}

.header .header-search__field input::-moz-placeholder {
  color: var(--color-dark-1);
}

.header .header-search__field input:-ms-input-placeholder {
  color: var(--color-dark-1);
}

.header .header-search__field input::placeholder {
  color: var(--color-dark-1);
}

.header .header-search__field .icon {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  left: 0;
  font-size: 20px;
}

.header .header-search__field button {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  right: 0;
}

.header .header-search__bg {
  position: fixed;
  top: 0;
  left: 0;
  z-index: 8;
  width: 100%;
  height: 100%;
  background-color: rgba(24, 24, 26, 0.7);
}

.header .header-cart {
  position: absolute;
  top: calc(100% + 24px);
  right: -16px;
  z-index: 10;
  min-width: 410px;
  box-shadow: 0px 0px 70px 0px #01213A12;
}

@media (max-width: 575px) {
  .header .header-cart {
    position: fixed;
    top: 80px;
    right: 0;
    left: 0;
    width: 100vw;
    min-width: 100vw;
  }
}

.header .explore-content {
  position: absolute;
  top: 100%;
  box-shadow: inset 0px 0px 0px 1px #DDDDDD;
  min-width: 300px;
}

.header .explore-content::before {
  content: '';
  position: absolute;
  z-index: -1;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  box-shadow: 0px 25px 70px 0px #01213A12;
}

.header .explore-content a {
  display: flex;
  padding: 5px 0;
}

.header .explore-content .explore__subnav {
  position: absolute;
  top: 0;
  bottom: 0;
  left: 95%;
  min-width: 300px;
  padding: 22px 40px;
  box-shadow: 0px 25px 70px 0px #01213A12;
  background-color: white;
  z-index: -1;
  transition: all 0.2s cubic-bezier(0.215, 0.61, 0.355, 1);
  opacity: 0;
  pointer-events: none;
}

.header .explore-content .explore__item {
  z-index: -1;
  padding: 0 30px;
}

.header .explore-content .explore__item:hover .explore__subnav {
  opacity: 1;
  pointer-events: auto;
}

.header .header-menu .mega {
  position: fixed;
  left: 50%;
  background-color: white;
  padding: 30px;
  width: 1500px;
  margin-top: 10px;
  min-height: 430px;
  transform: translate(-50%);
  border-radius: 8px;
  box-shadow: 0px 25px 70px 0px #01213A12;
  opacity: 0;
  pointer-events: none;
  transition: all 0.2s cubic-bezier(0.215, 0.61, 0.355, 1);
}

@media screen and (min-width: 1800px) {
  .header .header-menu .mega {
    width: 85vw;
  }
}

@media screen and (max-width: 1580px) {
  .header .header-menu .mega {
    width: 100vw;
  }
}

.header .header-menu .mega__menu {
  display: flex;
  height: 100%;
}

.header .header-menu .mega__menu .row {
  width: 100%;
}

.header .header-menu .mega__menu a {
  padding: 6px 0;
  color: var(--color-dark-1);
}

.header .header-menu .mega__menu a:hover {
  color: var(--color-purple-1) !important;
}

.header .header-menu .mega .mega-banner {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  margin-left: auto;
  height: 100%;
  max-width: 310px;
  padding: 30px;
  border-radius: 8px;
}



.form-page {
  position: relative;
  display: flex;
  align-items: center;
  height: 100vh;
}

@media (max-width: 991px) {
  .form-page {
    display: block;
    height: auto;
  }
}

.form-page__img {
  height: 100%;
}

@media (max-width: 991px) {
  .form-page__img {
    width: 100%;
    position: relative;
  }
}

.form-page__content {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 60vw;
  height: 100%;
}

@media (max-width: 991px) {
  .form-page__content {
    width: 100%;
  }
}

.form-page-composition {
  position: relative;
}

.form-page-composition .-bg {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 20;
}

.form-page-composition .-bg img {
  -o-object-fit: cover;
     object-fit: cover;
  width: 100%;
  height: 100%;
}

.form-page-composition .-el-1 {
  width: 100%;
}

.form-page-composition .-el-2 {
  position: absolute;
  bottom: 33px;
  right: 90px;
}

.form-page-composition .-el-3 {
  position: absolute;
  bottom: 290px;
  left: 20px;
}

.form-page-composition .-el-4 {
  position: absolute;
  bottom: 300px;
  right: 52px;
}




.masthead.-type-1 {
  z-index: 1;
  position: relative;
  /* padding: 20px 40px 160px 0; */
}

@media (max-width: 991px) {
  .masthead.-type-1 {
    /* padding-top: 140px; */
    padding-bottom: 80px;
  }
}

@media (max-width: 767px) {
  .masthead.-type-1 {
    padding-bottom: 80px;
    padding-bottom: 160px;
  }
}

.masthead.-type-1 .container {
  max-width: 1530px;
  padding: 0 15px;
}

.masthead.-type-1 .masthead__bg {
  z-index: -1;
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: var(--color-dark-1);
}

.masthead.-type-1 .masthead__bg img {
  -o-object-fit: cover;
     object-fit: cover;
  width: 100%;
  height: 100%;
}

.masthead.-type-1 .masthead__title {
  font-size: 55px;
  line-height: 80px;
  font-weight: 700;
  color: white;
}

@media (max-width: 991px) {
  .masthead.-type-1 .masthead__title {
    font-size: 50px;
    line-height: 1.3;
  }
  .masthead.-type-1 .masthead__title br {
    display: none;
  }
}

@media (max-width: 767px) {
  .masthead.-type-1 .masthead__title {
    font-size: 40px;
  }
}

@media (max-width: 575px) {
  .masthead.-type-1 .masthead__title {
    font-size: 30px;
  }
}

.masthead.-type-1 .masthead__text {
  margin-top: 13px;
  font-size: 17px;
  line-height: 36px;
  color: white;
}

@media (max-width: 991px) {
  .masthead.-type-1 .masthead__text {
    font-size: 16px;
    line-height: 1.6;
  }
  .masthead.-type-1 .masthead__text br {
    display: none;
  }
}

@media (max-width: 575px) {
  .masthead.-type-1 .masthead__text {
    margin-top: 10px;
    font-size: 15px;
  }
}

.masthead.-type-1 .masthead__buttons {
  padding-top: 27px;
}

@media (max-width: 767px) {
  .masthead.-type-1 .masthead__buttons {
    padding-top: 15px;
  }
  .masthead.-type-1 .masthead__buttons a {
    width: 100%;
  }
}

.masthead.-type-1 .masthead-image {
  display: grid;
  gap: 66px;
  grid-template-columns: 0.6fr 0.4fr;
  padding-left: 80px;
}

@media (max-width: 991px) {
  .masthead.-type-1 .masthead-image {
    padding-left: 0;
    grid-template-columns: 1fr 1fr;
    gap: 40px 24px;
    width: 85%;
  }
}

@media (max-width: 767px) {
  .masthead.-type-1 .masthead-image {
    width: 100%;
  }
}

.masthead.-type-1 .masthead-image > * {
  position: relative;
}

.masthead.-type-1 .masthead-image__el1 {
  grid-row: span 2;
  padding-top: 48px;
}

.masthead.-type-1 .masthead-image__el1 > div {
  position: relative;
  top: -54px;
  left: -132px;
}

.masthead.-type-1 .masthead-image__el2 {
  display: flex;
  justify-content: flex-end;
}

.masthead.-type-1 .masthead-image__el2 > div {
  position: absolute;
  bottom: -52px;
  right: 112px;
}

.masthead.-type-1 .masthead-image__el3 {
  display: flex;
  justify-content: flex-end;
}

.masthead.-type-1 .masthead-image__el3 > div {
  position: absolute;
  bottom: -50px;
  right: 15px;
}

.masthead.-type-1 .masthead-info {
  padding-top: 85px;
  display: flex;
  flex-wrap: wrap;
}

@media (max-width: 991px) {
  .masthead.-type-1 .masthead-info {
    padding-top: 30px;
  }
}

.masthead.-type-1 .masthead-info__item {
  width: auto;
}

.hero-background {
  background-image: url('/assets/img/home-1/hero/image790.jpeg');
  background-size: auto; /* Ensures the image covers the entire area */
  background-repeat: no-repeat;
  background-position: top center;
  height: 90vh; /* Sets height to full viewport height */
  width: 100%;
  padding: 40px; 
}

.hero-background-home {
  background-image: url('/assets/img/home/<USER>');
  background-size: auto; /* Ensures the image covers the entire area */
  background-repeat: no-repeat;
  background-position: top center;
  height: 64vh; /* Sets height to full viewport height */
  width: 100%;
  padding: 20px; 
}

.hero-content {
  padding: 105px 20px 60px;
  max-width: 1200px;
  margin: 0 auto;
}

@media (min-width: 768px) {
  .hero-content {
    padding: 45px 40px 60px;
  }

  .hero-title {
    font-size: 60px;
  }

  .stat-number {
    font-size: 42px;
  }

  .stat-text {
    font-size: 16px;
  }

  .supporting-text {
    font-size: 32px;
  }
}

.hero-title .highlight {
  color: white;
}

.stats-container {
  display: flex;
  gap: 20px;
  margin-bottom: 40px;
}

.hero-content {
  padding: 105px 20px 60px;
  max-width: 1200px;
  margin: 0 auto;
}

@media (min-width: 768px) {
  .hero-content {
    padding: 45px 40px 60px;
  }

  .hero-title {
    font-size: 60px;
  }

  .stat-number {
    font-size: 42px;
  }

  .stat-text {
    font-size: 16px;
  }

  .supporting-text {
    font-size: 32px;
  }
}

.card-item--style-1{
  width: "309.319px"
}
@media (max-width: 768px) {
  .card-item--style-1{
    width: 100%;
  }
  .cards-nursing {
    word-wrap: break-word;
    flex-direction: column; /* Stack items vertically */
    width: 100%; 
    gap: "24px";
    padding: "0 16px";
    align-items: center;
  }
  .cards-nursing > * {
    word-wrap: break-word;
    width: 100%; /* Make each CardItem full width */
  }
}
/* Contact Us Header Section */
.contact-header-section {
  position: relative;
  width: 100%;
  min-height: 55vh;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
  background: #0a3d4d; /* fallback for image loading */
}

.contact-header-section img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  min-height: 320px;
  max-height: 100vh;
  display: block;
}

.contact-header-content {
  position: absolute;
  top: 50%;
  left: 6.5%;
  transform: translateY(-50%);
  max-width: 90%;
  z-index: 1;
  color: white;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
}

.contact-header-content .responsive-heading {
  font-family: 'Poppins', sans-serif;
  font-size: clamp(2.2rem, 7vw, 4rem);
  font-weight: 700;
  line-height: 1.15;
  color: white;
  margin: 0;
  text-shadow: 0 2px 8px rgba(0,0,0,0.18);
}

.contact-header-content .responsive-desc {
  font-size: clamp(1rem, 3vw, 1.5rem);
  font-weight: 400;
  line-height: 1.5;
  color: white;
  margin-top: 18px;
  text-shadow: 0 1px 6px rgba(0,0,0,0.12);
}

@media (max-width: 900px) {
  .contact-header-section {
    min-height: 38vh;
  }
  .contact-header-content {
    left: 4%;
    max-width: 96%;
  }
  .contact-header-content .responsive-heading {
    font-size: clamp(1.5rem, 8vw, 2.5rem);
  }
}

@media (max-width: 600px) {
  .contact-header-section {
    min-height: 28vh;
  }
  .contact-header-content {
    left: 2%;
    max-width: 98%;
    top: 55%;
    transform: translateY(-55%);
  }
  .contact-header-content .responsive-heading {
    font-size: clamp(1.2rem, 9vw, 2rem);
  }
  .contact-header-content .responsive-desc {
    font-size: clamp(0.95rem, 4vw, 1.1rem);
  }
}

img {
  max-width: 100%;
  height: auto;
}

.lh-1 {
  line-height: 1 !important;
}

.footer.-type-1 {
  color: white;
}

.footer.-type-1.-dark {
  color: var(--color-dark-1);
}

.footer.-type-1.-dark a {
  color: var(--color-light-1);
}

.footer.-type-1 .footer-header {
  padding: 60px 0;
  border-bottom: 1px solid rgba(255, 255, 255, 0.15);
}

@media (max-width: 767px) {
  .footer.-type-1 .footer-header {
    padding-bottom: 0;
  }
}

.footer.-type-1 .footer-header .footer-header-socials {
  display: flex;
  align-items: center;
}

.footer.-type-1 .footer-header .footer-header-socials__title {
  font-size: 17px;
  line-height: 26px;
  margin-right: 20px;
}

.footer.-type-1 .footer-header .footer-header-socials__list {
  display: flex;
}

.footer.-type-1 .footer-header .footer-header-socials__list > * + * {
  margin-left: 4px;
}

.footer.-type-1 .footer-header .footer-header-socials__list a {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  border-radius: 100%;
  transition: all 0.2s cubic-bezier(0.215, 0.61, 0.355, 1);
  font-size: 14px;
}

.footer.-type-1 .footer-header .footer-header-socials__list a:hover {
  color: white !important;
  background-color: #2B1C55 !important;
}

.footer.-type-1 .footer-columns {
  padding-top: 60px;
  padding-bottom: 127px;
}

@media (max-width: 767px) {
  .footer.-type-1 .footer-columns {
    padding-top: 60px;
    padding-bottom: 60px;
  }
}

.footer.-type-1 .footer-columns__title {
  margin-bottom: 26px;
  font-size: 17px;
  line-height: 26px;
  text-transform: uppercase;
  font-weight: 500;
}

.footer.-type-1 .footer-columns__links {
  display: flex;
  flex-direction: column;
}

.footer.-type-1 .footer-columns__links a {
  display: block;
  font-size: 15px;
  line-height: 35px;
  font-weight: 400;
}

.footer.-type-1 .footer-columns__links a:hover {
  color: var(--color-purple-1);
}

.footer.-type-1 .footer-columns .footer-columns-form > div {
  font-size: 15px;
  line-height: 30px;
  font-weight: 400;
  margin-bottom: 10px;
}

.footer.-type-1 .footer-columns .footer-columns-form .form-group {
  position: relative;
}

.footer.-type-1 .footer-columns .footer-columns-form .form-group input {
  width: 100%;
  border-radius: 100px;
  background-color: white;
  padding: 22px 30px;
  font-size: 14px;
  line-height: 16px;
  font-weight: 400;
  color: var(--color-light-1);
}

.footer.-type-1 .footer-columns .footer-columns-form .form-group input::-moz-placeholder {
  color: var(--color-light-1);
}

.footer.-type-1 .footer-columns .footer-columns-form .form-group input:-ms-input-placeholder {
  color: var(--color-light-1);
}

.footer.-type-1 .footer-columns .footer-columns-form .form-group input::placeholder {
  color: var(--color-light-1);
}

.footer.-type-1 .footer-columns .footer-columns-form .form-group button {
  position: absolute;
  top: 10px;
  right: 10px;
  font-size: 14px;
  font-weight: 400;
  line-height: 16px;
  color: white;
  padding: 12px 24px;
  background-color: #6440FB;
  border-radius: 100px;
}

.footer.-type-1 .footer-footer {
  padding: 30px 0;
  border-top: 1px solid rgba(255, 255, 255, 0.15);
}

.footer.-type-1 .footer-footer__copyright {
  font-size: 13px;
  line-height: 15px;
  font-weight: 400;
}

.footer.-type-1 .footer-footer__right {
  display: flex;
  align-items: center;
}

@media (max-width: 575px) {
  .footer.-type-1 .footer-footer__right {
    flex-direction: column;
    align-items: flex-start;
  }
}

.footer.-type-1 .footer-footer__list {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
}

.footer.-type-1 .footer-footer__list > * {
  margin: 0 5px;
}

.footer.-type-1 .footer-footer__list a {
  font-size: 13px;
  line-height: 15px;
  font-weight: 400;
}

.footer.-type-1 .footer-footer__button {
  margin-left: 30px;
}

@media (max-width: 575px) {
  .footer.-type-1 .footer-footer__button {
    margin-left: 0;
    margin-top: 24px;
  }
}

.footer.-type-1 .footer-footer__button a {
  display: inline-block;
  background-color: rgba(255, 255, 255, 0.1);
  padding: 15px 30px;
  font-size: 13px;
  line-height: 15px;
  font-weight: 400;
  border-radius: 100px;
  color: white;
}

.footer.-type-1.-green-links .footer-columns a:hover {
  text-decoration: underline;
  color: var(--color-green-1) !important;
}

.footer.-type-4 .footer-header-socials__list {
  margin-left: -15px;
}

.footer.-type-4 .footer-header-socials__list a {
  position: relative;
  z-index: 1;
}

.footer.-type-4 .footer-header-socials__list a::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  z-index: -1;
  width: 100%;
  height: 100%;
  border-radius: 100%;
  background-color: var(--color-dark-6);
  opacity: 0;
  transition: all 0.25s cubic-bezier(0.215, 0.61, 0.355, 1);
}

.footer.-type-4 .footer-header-socials__list a:hover::after {
  opacity: 1;
}

.footer.-type-5 .footer-header-socials__list {
  margin-left: -15px;
}

.footer.-type-5 .footer-header-socials__list a {
  position: relative;
  z-index: 1;
}

.footer.-type-5 .footer-header-socials__list a::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  z-index: -1;
  width: 100%;
  height: 100%;
  border-radius: 100%;
  background-color: var(--color-dark-6);
  opacity: 0;
  transition: all 0.25s cubic-bezier(0.215, 0.61, 0.355, 1);
}

.footer.-type-5 .footer-header-socials__list a:hover::after {
  opacity: 1;
}

.header {
  transition: all 0.2s cubic-bezier(0.215, 0.61, 0.355, 1);
  max-width: 100% !important;
  
}

.header__explore > a {
  position: relative;
  z-index: 1;
}

.header__explore > a::before {
  content: "";
  position: absolute;
  top: -6px;
  bottom: -6px;
  left: -14px;
  right: -14px;
  z-index: -1;
  background-color: rgba(255, 255, 255, 0.15);
  border-radius: 8px;
  transition: all 0.2s cubic-bezier(0.165, 0.84, 0.44, 1);
  opacity: 0;
}

.header__explore > a:hover {
  color: var(--color-green-1) !important;
}

.header__explore > a:hover::before {
  opacity: 1;
}

.header.-type-1 {
  z-index: 100;
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  background-color: white;
  padding: 0 20px;
}

@media (max-width: 991px) {
  .header.-type-1 {
    padding: 0;
  }
}

.header.-type-1 .header__container {
  position: relative;
  max-width: calc(1500px);
  padding: 20px 0;
  border-bottom: 1px solid #E5E9EB;
  margin: 0 auto;
}

@media (max-width: 991px) {
  .header.-type-1 .header__container {
    padding: 20px;
  }
}

.header.-type-1 .header-left {
  display: flex;
  align-items: center;
}

@media (min-width: 1200px) {
  .header .header-menu {
    position: absolute;
    left: 0;
    z-index: 5;
  }
  .header .header-menu .header-menu__content {
    display: flex;
    align-items: center;
  }
  .header .header-menu-close {
    display: none;
  }
  .header .header-menu-bg {
    display: none;
  }
  .header .header-menu .menu {
    position: absolute;
    left: 50%;
    transform: translateX(-50%);
  }
  .header .header-menu .menu__nav {
    display: flex;
    gap: 40px;
    font-weight: 500;
  }
  .header .header-menu .menu__nav a {
    display: flex;
    align-items: center;
    padding: 7px 16px;
    border-radius: 8px;
    transition: all 0.2s cubic-bezier(0.215, 0.61, 0.355, 1);
  }
}

@media (min-width: 1200px) and (max-width: 1399px) {
  .header .header-menu .menu__nav a {
    padding: 6px 6px;
  }
}

@media (min-width: 1200px) {
  .header .header-menu .menu__nav li.menu-item-has-children a {
    display: flex;
    align-items: center;
  }
  .header .header-menu .menu__nav > li.menu-item-has-children > a .icon-chevron-right {
    transform: rotate(90deg);
  }
  .header .header-menu .menu__nav > li {
    padding: 17px 0;
  }
  .header .header-menu .menu__nav > li:hover > a {
    color: var(--color-green-1);
    background-color: rgba(255, 255, 255, 0.15);
  }
  .header .header-menu .menu__nav > li > .subnav::before {
    content: '';
    position: absolute;
    top: -5px;
    left: 20px;
    width: 10px;
    height: 10px;
    background-color: white;
    transform: rotate(45deg);
  }
  .header .header-menu .menu li.-has-mega-menu:hover > .mega {
    opacity: 1 !important;
    pointer-events: auto !important;
  }
  .header .header-menu .menu li:hover > .subnav {
    opacity: 1;
    pointer-events: auto;
  }
  .header .header-menu .menu .subnav {
    position: absolute;
    top: 100%;
    background-color: white;
    border-radius: 8px;
    color: black !important;
    min-width: 230px;
    padding-top: 20px;
    padding-bottom: 20px;
    box-shadow: 0px 25px 70px 0px #01213A12;
    opacity: 0;
    pointer-events: none;
    transition: all 0.2s cubic-bezier(0.215, 0.61, 0.355, 1);
  }
  .header .header-menu .menu .subnav .subnav {
    top: 0;
    left: 97%;
  }
  .header .header-menu .menu .subnav > li {
    position: relative;
  }
  .header .header-menu .menu .subnav > li > a {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 10px 30px;
  }
  .header .header-menu .menu .subnav > li > a:hover {
    color: var(--color-purple-1);
  }
  .header .header-menu .menu .subnav .menu__backButton {
    display: none;
  }
  .header .header-menu .menu > .subnav {
    top: 100%;
    left: 0;
  }
  .header .header-menu .mobile-bg {
    display: none;
  }
  .header .header-menu .mobile-back-button {
    display: none;
  }
  .header .header-menu .mobile-footer {
    display: none;
  }
}

@media (max-width: 1199px) {
  .header .header-menu {
    position: fixed !important;
    top: 0;
    left: 0;
    z-index: 10;
    max-width: calc(100vw - 80px);
    width: 60vw;
    height: 100vh;
    padding: 0 !important;
    transition: all 0.3s cubic-bezier(0.215, 0.61, 0.355, 1);
    opacity: 0;
    pointer-events: none;
  }
  .header .header-menu .header-menu__content {
    display: flex;
    flex-direction: column;
    height: 100%;
    transition: all 0.3s cubic-bezier(0.215, 0.61, 0.355, 1);
    transform: translateX(-20%);
  }
  .header .header-menu ul {
    overflow-y: hidden;
    overflow-x: hidden;
  }
  .header .header-menu.-is-el-visible {
    opacity: 1;
    pointer-events: auto;
  }
  .header .header-menu.-is-el-visible .-is-active {
    pointer-events: auto !important;
    overflow-y: scroll;
    overflow-x: hidden;
  }
  .header .header-menu.-is-el-visible .header-menu__content {
    transform: none;
  }
}

@media (max-width: 1199px) and (max-width: 767px) {
  .header .header-menu {
    width: 80vw;
  }
}

@media (max-width: 1199px) and (max-width: 575px) {
  .header .header-menu {
    width: calc(100vw - 60px);
    max-width: 100%;
  }
}

@media (max-width: 1199px) {
  .header .header-menu-close {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: -1;
  }
}

@media (max-width: 1199px) and (max-width: 575px) {
  .header .header-menu-close {
    top: 10px;
    right: 10px;
  }
}

@media (max-width: 1199px) {
  .header .header-menu-bg {
    position: fixed;
    top: 0;
    bottom: 0;
    left: 0;
    right: 0;
    background-color: rgba(24, 24, 26, 0.7);
    z-index: -2;
  }
  .header .header-menu .menu {
    overflow-y: hidden;
    overflow-x: hidden;
    position: relative;
    padding: 20px;
    height: 100%;
  }
  .header .header-menu .menu ul {
    pointer-events: none;
  }
  .header .header-menu .menu__nav {
    display: flex;
    flex-direction: column;
  }
  .header .header-menu .menu__nav a {
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 60px;
    padding: 0 20px;
    border-radius: 8px;
    color: var(--color-dark-1) !important;
    transition: all 0.2s cubic-bezier(0.215, 0.61, 0.355, 1);
  }
  .header .header-menu .menu__nav a:hover {
    color: var(--color-purple-1) !important;
    background-color: #F4F2FF;
  }
  .header .header-menu .menu__nav > li > a {
    font-weight: 500;
    font-size: 16px;
  }
  .header .header-menu .menu__nav li {
    overflow: hidden;
  }
  .header .header-menu .menu .subnav {
    position: absolute;
    top: 0;
    left: 0;
    color: black !important;
    padding: 20px;
    width: 100%;
    height: 100%;
  }
  .header .header-menu .menu .subnav a {
    transform: translateY(100%);
  }
  .header .header-menu .menu .subnav > li > a {
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 45px;
    padding: 0 20px;
    color: var(--color-dark-1) !important;
    transition: color 0.2s cubic-bezier(0.215, 0.61, 0.355, 1);
  }
  .header .header-menu .menu .subnav > li > a:hover {
    color: var(--color-purple-1) !important;
  }
  .header .header-menu .menu .subnav .menu__backButton a {
    justify-content: flex-start !important;
    height: 60px !important;
    font-weight: 600 !important;
    color: var(--color-purple-1) !important;
    font-size: 16px;
    background-color: var(--color-purple-3);
  }
  .header .header-menu .mobile-bg {
    position: fixed;
    top: 0;
    left: 0;
    max-width: calc(100vw - 80px);
    width: 60vw;
    height: 100vh;
    z-index: -1;
    background-color: white;
  }
}

@media (max-width: 1199px) and (max-width: 767px) {
  .header .header-menu .mobile-bg {
    width: 80vw;
  }
}

@media (max-width: 1199px) and (max-width: 575px) {
  .header .header-menu .mobile-bg {
    width: calc(100vw - 60px);
    max-width: 100%;
  }
}

@media (max-width: 1199px) {
  .header .header-menu .mobile-footer .mobile-socials {
    display: flex;
    margin-left: -15px;
  }
  .header .header-menu .mobile-footer .mobile-socials a {
    transition: all 0.2s cubic-bezier(0.215, 0.61, 0.355, 1);
  }
  .header .header-menu .mobile-footer .mobile-socials a:hover {
    background-color: var(--color-light-4);
    color: var(--color-purple-1) !important;
  }
}

.header.-base {
  z-index: 100;
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
}

.header.-base .header__container {
  position: relative;
  padding: 0 60px;
}

@media (max-width: 991px) {
  .header.-base .header__container {
    padding: 0 40px;
  }
}

@media (max-width: 767px) {
  .header.-base .header__container {
    padding: 0 15px;
  }
}

.header.-base .header-menu {
  position: relative;
}

.header.-base .header-menu .menu {
  position: relative;
  left: unset;
  transform: none;
}

.header.-type-3 {
  position: fixed;
  top: 0;
  left: 0;
  z-index: 100;
  background-color: white;
  width: 100%;
  padding: 0 60px;
}

@media (max-width: 991px) {
  .header.-type-3 {
    padding: 0 40px;
  }
}

@media (max-width: 767px) {
  .header.-type-3 {
    padding: 0 15px;
  }
}

.header.-type-3 .header__container {
  position: relative;
  width: 100%;
  border-bottom: 1px solid rgba(0, 0, 0, 0.15);
}

.header.-type-3 .header-menu {
  position: relative;
}

.header.-type-3 .header-menu .menu {
  position: relative;
  left: unset;
  transform: none;
}

@media (max-width: 1670px) {
  .header.-type-3 .header-search-field {
    display: none;
  }
}

.header.-type-3 .header-search-field__group {
  position: relative;
  max-width: 340px;
  min-width: 260px;
}

.header.-type-3 .header-search-field input {
  width: 100%;
  font-size: 13px;
  line-height: 1;
  background-color: var(--color-light-3);
  border-radius: 8px;
  padding: 18px 28px;
}

.header.-type-3 .header-search-field button {
  position: absolute;
  top: 0;
  right: 0;
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
  width: 40px;
}

.header.-type-3 .header-search-field button .icon {
  font-size: 20px;
  color: var(--color-dark-1);
}

.header.-type-4 {
  position: fixed;
  top: 0;
  left: 0;
  z-index: 100;
  width: 100%;
  padding: 0 60px;
}

@media (max-width: 1199px) {
  .header.-type-4 {
    padding: 0 45px;
  }
}

@media (max-width: 991px) {
  .header.-type-4 {
    padding: 0 15px;
  }
}

.header.-type-4.-shadow {
  box-shadow: 0px 6px 15px 0px #404F680D;
}

.header.-type-4 *.-before-border {
  position: relative;
}

.header.-type-4 *.-before-border::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 1px;
  height: 100%;
  background-color: #EDEDED;
}

.header.-type-4 *.-after-border {
  position: relative;
}

.header.-type-4 *.-after-border::after {
  content: "";
  position: absolute;
  top: 0;
  right: 0;
  width: 1px;
  height: 100%;
  background-color: #EDEDED;
}

.header.-type-4 .header__container {
  position: relative;
}

.header.-type-4 .header__container .row {
  flex-wrap: nowrap;
}

.header.-type-4 .header-menu {
  position: relative;
}

.header.-type-4 .header-menu .menu {
  position: relative;
  left: unset;
  transform: none;
}

.header.-type-4 .header-search-field {
  position: relative;
}

@media (max-width: 1670px) {
  .header.-type-4 .header-search-field {
    display: none;
  }
}

.header.-type-4 .header-search-field__group {
  position: relative;
  max-width: 300px;
  min-width: 250px;
}

.header.-type-4 .header-search-field input {
  width: 100%;
  font-size: 13px;
  line-height: 1;
  background-color: white;
  border: 1px solid #EDEDED;
  border-radius: 100px;
  padding: 17px 28px;
}

.header.-type-4 .header-search-field button {
  position: absolute;
  top: 0;
  right: 0;
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
  width: 60px;
}

.header.-type-4 .header-search-field button .icon {
  font-size: 20px;
  color: var(--color-dark-1);
}

.header.-type-5 {
  z-index: 100;
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
}

.header.-type-5 .header__container {
  width: 100%;
  padding: 0 60px;
}

@media (max-width: 1199px) {
  .header.-type-5 .header__container {
    padding: 0 45px;
  }
}

@media (max-width: 991px) {
  .header.-type-5 .header__container {
    padding: 0 15px;
  }
}

.header.-type-5 .header__container .row {
  flex-wrap: nowrap;
}

.header.-type-5 .header-menu {
  position: relative;
}

.header.-type-5 .header-menu .menu {
  position: relative;
  left: unset;
  transform: none;
}

.header.-base-sidebar {
  z-index: 100;
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  padding: 0 20px;
}

.header.-base-sidebar .row {
  flex-wrap: nowrap;
}

@media (max-width: 767px) {
  .header.-base-sidebar .row {
    margin: 0;
  }
  .header.-base-sidebar .col-auto {
    padding: 0;
  }
}

@media (max-width: 575px) {
  .header.-base-sidebar .header__logo img {
    width: 80%;
  }
}

@media (max-width: 991px) {
  .dashboard.-home-9 {
    margin-top: 30px;
  }
}

@media (max-width: 767px) {
  .dashboard.-home-9 {
    margin-top: 60px;
  }
}

.dashboard.-home-9 .dashboard__sidebar {
  will-change: transform;
  transition: all 0.5s cubic-bezier(0.215, 0.61, 0.355, 1);
}

@media (max-width: 991px) {
  .dashboard.-home-9 .dashboard__sidebar {
    z-index: 110;
    position: fixed;
    top: 0;
    bottom: 0;
    width: 300px;
    margin-top: 90px;
  }
}

@media (max-width: 767px) {
  .dashboard.-home-9 .dashboard__sidebar {
    width: 80%;
  }
}

.dashboard.-home-9 .dashboard__main {
  will-change: padding-left;
  transition: all 0.5s cubic-bezier(0.215, 0.61, 0.355, 1);
}


@media (max-width: 991px) {
  .dashboard.-home-9 .dashboard__main::after {
    content: "";
    position: fixed;
    z-index: 50;
    top: 0;
    bottom: 0;
    left: 0;
    right: 0;
    background-color: rgba(0, 0, 0, 0.5);
    pointer-events: auto;
    opacity: 1;
    transition: all 0.5s cubic-bezier(0.215, 0.61, 0.355, 1);
  }
}

.dashboard.-home-9.-is-sidebar-hidden .dashboard__sidebar {
  transform: translateX(-100%);
}

.dashboard.-home-9.-is-sidebar-hidden .dashboard__main {
  padding-left: 0;
}

.dashboard.-home-9.-is-sidebar-hidden .dashboard__main::after {
  pointer-events: none;
  opacity: 0;
}

.sidebar.-base-sidebar .sidebar__inner {
  padding-top: 40px;
}

.sidebar.-base-sidebar .sidebar__item {
  display: flex;
  align-items: center;
  border-radius: 16px;
  padding: 0 20px;
  height: 55px;
}

@media (max-width: 575px) {
  .sidebar.-base-sidebar .sidebar__item {
    border-radius: 0;
    height: 45px;
  }
}

.sidebar.-base-sidebar .sidebar__item.-is-active {
  /* background-color: var(--color-purple-3); */
  color: var(--color-purple-1) !important;
}

.sidebar.-base-sidebar .sidebar__links > div > a {
  display: flex;
  align-items: center;
}

.sidebar.-base-sidebar .sidebar__links > div > a::before {
  content: "";
  display: inline-block;
  margin-right: 10px;
  background-color: currentColor;
  border-radius: 100%;
  width: 5px;
  height: 5px;
}

.sidebar.-base-sidebar .sidebar__links > div > a:hover {
  color: var(--color-dark-1);
}

.sidebar.-base-sidebar .sidebar__links > div > a:hover::before {
  background-color: var(--color-dark-1);
}

.header .header-search {
  position: fixed;
  top: 0;
  left: 0;
  z-index: 10;
  width: 100%;
  height: 590px;
}

.header .header-search__field {
  position: relative;
}

.header .header-search__field input {
  padding: 18px 36px;
  border-bottom: 1px solid #EDEDED;
}

.header .header-search__field input:focus {
  outline: none;
  border-bottom: 1px solid var(--color-dark-1);
}

.header .header-search__field input::-moz-placeholder {
  color: var(--color-dark-1);
}

.header .header-search__field input:-ms-input-placeholder {
  color: var(--color-dark-1);
}

.header .header-search__field input::placeholder {
  color: var(--color-dark-1);
}

.header .header-search__field .icon {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  left: 0;
  font-size: 20px;
}

.header .header-search__field button {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  right: 0;
}

.header .header-search__bg {
  position: fixed;
  top: 0;
  left: 0;
  z-index: 8;
  width: 100%;
  height: 100%;
  background-color: rgba(24, 24, 26, 0.7);
}

.header .header-cart {
  position: absolute;
  top: calc(100% + 24px);
  right: -16px;
  z-index: 10;
  min-width: 410px;
  box-shadow: 0px 0px 70px 0px #01213A12;
}

@media (max-width: 575px) {
  .header .header-cart {
    position: fixed;
    top: 80px;
    right: 0;
    left: 0;
    width: 100vw;
    min-width: 100vw;
  }
}

.header .explore-content {
  position: absolute;
  top: 100%;
  box-shadow: inset 0px 0px 0px 1px #DDDDDD;
  min-width: 300px;
}

.header .explore-content::before {
  content: '';
  position: absolute;
  z-index: -1;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  box-shadow: 0px 25px 70px 0px #01213A12;
}

.header .explore-content a {
  display: flex;
  padding: 5px 0;
}

.header .explore-content .explore__subnav {
  position: absolute;
  top: 0;
  bottom: 0;
  left: 95%;
  min-width: 300px;
  padding: 22px 40px;
  box-shadow: 0px 25px 70px 0px #01213A12;
  background-color: white;
  z-index: -1;
  transition: all 0.2s cubic-bezier(0.215, 0.61, 0.355, 1);
  opacity: 0;
  pointer-events: none;
}

.header .explore-content .explore__item {
  z-index: -1;
  padding: 0 30px;
}

.header .explore-content .explore__item:hover .explore__subnav {
  opacity: 1;
  pointer-events: auto;
}

.header .header-menu .mega {
  position: fixed;
  left: 50%;
  background-color: white;
  padding: 30px;
  width: 1500px;
  margin-top: 10px;
  min-height: 430px;
  transform: translate(-50%);
  border-radius: 8px;
  box-shadow: 0px 25px 70px 0px #01213A12;
  opacity: 0;
  pointer-events: none;
  transition: all 0.2s cubic-bezier(0.215, 0.61, 0.355, 1);
}

@media screen and (min-width: 1800px) {
  .header .header-menu .mega {
    width: 85vw;
  }
}

@media screen and (max-width: 1580px) {
  .header .header-menu .mega {
    width: 100vw;
  }
}

.header .header-menu .mega__menu {
  display: flex;
  height: 100%;
}

.header .header-menu .mega__menu .row {
  width: 100%;
}

.header .header-menu .mega__menu a {
  padding: 6px 0;
  color: var(--color-dark-1);
}

.header .header-menu .mega__menu a:hover {
  color: var(--color-purple-1) !important;
}

.header .header-menu .mega .mega-banner {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  margin-left: auto;
  height: 100%;
  max-width: 310px;
  padding: 30px;
  border-radius: 8px;
}



.form-page {
  position: relative;
  display: flex;
  align-items: center;
  height: 100vh;
}

@media (max-width: 991px) {
  .form-page {
    display: block;
    height: auto;
  }
}

.form-page__img {
  height: 100%;
}

@media (max-width: 991px) {
  .form-page__img {
    width: 100%;
    position: relative;
  }
}

.form-page__content {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 60vw;
  height: 100%;
}

@media (max-width: 991px) {
  .form-page__content {
    width: 100%;
  }
}

.form-page-composition {
  position: relative;
}

.form-page-composition .-bg {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 20;
}

.form-page-composition .-bg img {
  -o-object-fit: cover;
     object-fit: cover;
  width: 100%;
  height: 100%;
}

.form-page-composition .-el-1 {
  width: 100%;
}

.form-page-composition .-el-2 {
  position: absolute;
  bottom: 33px;
  right: 90px;
}

.form-page-composition .-el-3 {
  position: absolute;
  bottom: 290px;
  left: 20px;
}

.form-page-composition .-el-4 {
  position: absolute;
  bottom: 300px;
  right: 52px;
}




.masthead.-type-1 {
  z-index: 1;
  position: relative;
  /* padding: 20px 40px 160px 0; */
}

@media (max-width: 991px) {
  .masthead.-type-1 {
    /* padding-top: 140px; */
    padding-bottom: 80px;
  }
}

@media (max-width: 767px) {
  .masthead.-type-1 {
    padding-bottom: 80px;
    padding-bottom: 160px;
  }
}

.masthead.-type-1 .container {
  max-width: 1530px;
  padding: 0 15px;
}

.masthead.-type-1 .masthead__bg {
  z-index: -1;
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: var(--color-dark-1);
}

.masthead.-type-1 .masthead__bg img {
  -o-object-fit: cover;
     object-fit: cover;
  width: 100%;
  height: 100%;
}

.masthead.-type-1 .masthead__title {
  font-size: 55px;
  line-height: 80px;
  font-weight: 700;
  color: white;
}

@media (max-width: 991px) {
  .masthead.-type-1 .masthead__title {
    font-size: 50px;
    line-height: 1.3;
  }
  .masthead.-type-1 .masthead__title br {
    display: none;
  }
}

@media (max-width: 767px) {
  .masthead.-type-1 .masthead__title {
    font-size: 40px;
  }
}

@media (max-width: 575px) {
  .masthead.-type-1 .masthead__title {
    font-size: 30px;
  }
}

.masthead.-type-1 .masthead__text {
  margin-top: 13px;
  font-size: 17px;
  line-height: 36px;
  color: white;
}

@media (max-width: 991px) {
  .masthead.-type-1 .masthead__text {
    font-size: 16px;
    line-height: 1.6;
  }
  .masthead.-type-1 .masthead__text br {
    display: none;
  }
}

@media (max-width: 575px) {
  .masthead.-type-1 .masthead__text {
    margin-top: 10px;
    font-size: 15px;
  }
}

.masthead.-type-1 .masthead__buttons {
  padding-top: 27px;
}

@media (max-width: 767px) {
  .masthead.-type-1 .masthead__buttons {
    padding-top: 15px;
  }
  .masthead.-type-1 .masthead__buttons a {
    width: 100%;
  }
}

.masthead.-type-1 .masthead-image {
  display: grid;
  gap: 66px;
  grid-template-columns: 0.6fr 0.4fr;
  padding-left: 80px;
}

@media (max-width: 991px) {
  .masthead.-type-1 .masthead-image {
    padding-left: 0;
    grid-template-columns: 1fr 1fr;
    gap: 40px 24px;
    width: 85%;
  }
}

@media (max-width: 767px) {
  .masthead.-type-1 .masthead-image {
    width: 100%;
  }
}

.masthead.-type-1 .masthead-image > * {
  position: relative;
}

.masthead.-type-1 .masthead-image__el1 {
  grid-row: span 2;
  padding-top: 48px;
}

.masthead.-type-1 .masthead-image__el1 > div {
  position: relative;
  top: -54px;
  left: -132px;
}

.masthead.-type-1 .masthead-image__el2 {
  display: flex;
  justify-content: flex-end;
}

.masthead.-type-1 .masthead-image__el2 > div {
  position: absolute;
  bottom: -52px;
  right: 112px;
}

.masthead.-type-1 .masthead-image__el3 {
  display: flex;
  justify-content: flex-end;
}

.masthead.-type-1 .masthead-image__el3 > div {
  position: absolute;
  bottom: -50px;
  right: 15px;
}

.masthead.-type-1 .masthead-info {
  padding-top: 85px;
  display: flex;
  flex-wrap: wrap;
}

@media (max-width: 991px) {
  .masthead.-type-1 .masthead-info {
    padding-top: 30px;
  }
}

.masthead.-type-1 .masthead-info__item {
  width: auto;
}


.hero-background-home {
  background-image: url('/assets/img/home/<USER>');
  background-size: auto; /* Ensures the image covers the entire area */
  background-repeat: no-repeat;
  background-position: top center;
  height: 64vh; /* Sets height to full viewport height */
  width: 100%;
  padding: 20px; 
}

.hero-content {
  padding: 105px 20px 60px;
  max-width: 1200px;
  margin: 0 auto;
}

@media (min-width: 768px) {
  .hero-content {
    padding: 45px 40px 60px;
  }

  .hero-title {
    font-size: 60px;
  }

  .stat-number {
    font-size: 42px;
  }

  .stat-text {
    font-size: 16px;
  }

  .supporting-text {
    font-size: 32px;
  }
}

.hero-title .highlight {
  color: white;
}

.stats-container {
  display: flex;
  gap: 20px;
  margin-bottom: 40px;
}

.hero-content {
  padding: 105px 20px 60px;
  max-width: 1200px;
  margin: 0 auto;
}

@media (min-width: 768px) {
  .hero-content {
    padding: 45px 40px 60px;
  }

  .hero-title {
    font-size: 60px;
  }

  .stat-number {
    font-size: 42px;
  }

  .stat-text {
    font-size: 16px;
  }

  .supporting-text {
    font-size: 32px;
  }
}

.card-item--style-1{
  width: "309.319px"
}
@media (max-width: 768px) {
  .card-item--style-1{
    width: 100%;
  }
  .cards-nursing {
    word-wrap: break-word;
    flex-direction: column; /* Stack items vertically */
    width: 100%; 
    gap: "24px";
    padding: "0 16px";
    align-items: center;
  }
  .cards-nursing > * {
    word-wrap: break-word;
    width: 100%; /* Make each CardItem full width */
  }
}

img {
  max-width: 100%;
  height: auto;
}

.lh-1 {
  line-height: 1 !important;
}

.footer.-type-1 {
  color: white;
}

.footer.-type-1.-dark {
  color: var(--color-dark-1);
}

.footer.-type-1.-dark a {
  color: var(--color-light-1);
}

.footer.-type-1 .footer-header {
  padding: 60px 0;
  border-bottom: 1px solid rgba(255, 255, 255, 0.15);
}

@media (max-width: 767px) {
  .footer.-type-1 .footer-header {
    padding-bottom: 0;
  }
}

.footer.-type-1 .footer-header .footer-header-socials {
  display: flex;
  align-items: center;
}

.footer.-type-1 .footer-header .footer-header-socials__title {
  font-size: 17px;
  line-height: 26px;
  margin-right: 20px;
}

.footer.-type-1 .footer-header .footer-header-socials__list {
  display: flex;
}

.footer.-type-1 .footer-header .footer-header-socials__list > * + * {
  margin-left: 4px;
}

.footer.-type-1 .footer-header .footer-header-socials__list a {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  border-radius: 100%;
  transition: all 0.2s cubic-bezier(0.215, 0.61, 0.355, 1);
  font-size: 14px;
}

.footer.-type-1 .footer-header .footer-header-socials__list a:hover {
  color: white !important;
  background-color: #2B1C55 !important;
}

.footer.-type-1 .footer-columns {
  padding-top: 60px;
  padding-bottom: 127px;
}

@media (max-width: 767px) {
  .footer.-type-1 .footer-columns {
    padding-top: 60px;
    padding-bottom: 60px;
  }
}

.footer.-type-1 .footer-columns__title {
  margin-bottom: 26px;
  font-size: 17px;
  line-height: 26px;
  text-transform: uppercase;
  font-weight: 500;
}

.footer.-type-1 .footer-columns__links {
  display: flex;
  flex-direction: column;
}

.footer.-type-1 .footer-columns__links a {
  display: block;
  font-size: 15px;
  line-height: 35px;
  font-weight: 400;
}

.footer.-type-1 .footer-columns__links a:hover {
  color: var(--color-purple-1);
}

.footer.-type-1 .footer-columns .footer-columns-form > div {
  font-size: 15px;
  line-height: 30px;
  font-weight: 400;
  margin-bottom: 10px;
}

.footer.-type-1 .footer-columns .footer-columns-form .form-group {
  position: relative;
}

.footer.-type-1 .footer-columns .footer-columns-form .form-group input {
  width: 100%;
  border-radius: 100px;
  background-color: white;
  padding: 22px 30px;
  font-size: 14px;
  line-height: 16px;
  font-weight: 400;
  color: var(--color-light-1);
}

.footer.-type-1 .footer-columns .footer-columns-form .form-group input::-moz-placeholder {
  color: var(--color-light-1);
}

.footer.-type-1 .footer-columns .footer-columns-form .form-group input:-ms-input-placeholder {
  color: var(--color-light-1);
}

.footer.-type-1 .footer-columns .footer-columns-form .form-group input::placeholder {
  color: var(--color-light-1);
}

.footer.-type-1 .footer-columns .footer-columns-form .form-group button {
  position: absolute;
  top: 10px;
  right: 10px;
  font-size: 14px;
  font-weight: 400;
  line-height: 16px;
  color: white;
  padding: 12px 24px;
  background-color: #6440FB;
  border-radius: 100px;
}

.footer.-type-1 .footer-footer {
  padding: 30px 0;
  border-top: 1px solid rgba(255, 255, 255, 0.15);
}

.footer.-type-1 .footer-footer__copyright {
  font-size: 13px;
  line-height: 15px;
  font-weight: 400;
}

.footer.-type-1 .footer-footer__right {
  display: flex;
  align-items: center;
}

@media (max-width: 575px) {
  .footer.-type-1 .footer-footer__right {
    flex-direction: column;
    align-items: flex-start;
  }
}

.footer.-type-1 .footer-footer__list {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
}

.footer.-type-1 .footer-footer__list > * {
  margin: 0 5px;
}

.footer.-type-1 .footer-footer__list a {
  font-size: 13px;
  line-height: 15px;
  font-weight: 400;
}

.footer.-type-1 .footer-footer__button {
  margin-left: 30px;
}

@media (max-width: 575px) {
  .footer.-type-1 .footer-footer__button {
    margin-left: 0;
    margin-top: 24px;
  }
}

.footer.-type-1 .footer-footer__button a {
  display: inline-block;
  background-color: rgba(255, 255, 255, 0.1);
  padding: 15px 30px;
  font-size: 13px;
  line-height: 15px;
  font-weight: 400;
  border-radius: 100px;
  color: white;
}

.footer.-type-1.-green-links .footer-columns a:hover {
  text-decoration: underline;
  color: var(--color-green-1) !important;
}

.footer.-type-4 .footer-header-socials__list {
  margin-left: -15px;
}

.footer.-type-4 .footer-header-socials__list a {
  position: relative;
  z-index: 1;
}

.footer.-type-4 .footer-header-socials__list a::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  z-index: -1;
  width: 100%;
  height: 100%;
  border-radius: 100%;
  background-color: var(--color-dark-6);
  opacity: 0;
  transition: all 0.25s cubic-bezier(0.215, 0.61, 0.355, 1);
}

.footer.-type-4 .footer-header-socials__list a:hover::after {
  opacity: 1;
}

.footer.-type-5 .footer-header-socials__list {
  margin-left: -15px;
}

.footer.-type-5 .footer-header-socials__list a {
  position: relative;
  z-index: 1;
}

.footer.-type-5 .footer-header-socials__list a::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  z-index: -1;
  width: 100%;
  height: 100%;
  border-radius: 100%;
  background-color: var(--color-dark-6);
  opacity: 0;
  transition: all 0.25s cubic-bezier(0.215, 0.61, 0.355, 1);
}

.footer.-type-5 .footer-header-socials__list a:hover::after {
  opacity: 1;
}

.header {
  transition: all 0.2s cubic-bezier(0.215, 0.61, 0.355, 1);
  max-width: 100% !important;
  
}

.header__explore > a {
  position: relative;
  z-index: 1;
}

.header__explore > a::before {
  content: "";
  position: absolute;
  top: -6px;
  bottom: -6px;
  left: -14px;
  right: -14px;
  z-index: -1;
  background-color: rgba(255, 255, 255, 0.15);
  border-radius: 8px;
  transition: all 0.2s cubic-bezier(0.165, 0.84, 0.44, 1);
  opacity: 0;
}

.header__explore > a:hover {
  color: var(--color-green-1) !important;
}

.header__explore > a:hover::before {
  opacity: 1;
}

.header.-type-1 {
  z-index: 100;
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  background-color: white;
  padding: 0 20px;
}

@media (max-width: 991px) {
  .header.-type-1 {
    padding: 0;
  }
}

.header.-type-1 .header__container {
  position: relative;
  max-width: calc(1500px);
  padding: 20px 0;
  border-bottom: 1px solid #E5E9EB;
  margin: 0 auto;
}

@media (max-width: 991px) {
  .header.-type-1 .header__container {
    padding: 20px;
  }
}

.header.-type-1 .header-left {
  display: flex;
  align-items: center;
}

@media (min-width: 1200px) {
  .header .header-menu {
    position: absolute;
    left: 0;
    z-index: 5;
  }
  .header .header-menu .header-menu__content {
    display: flex;
    align-items: center;
  }
  .header .header-menu-close {
    display: none;
  }
  .header .header-menu-bg {
    display: none;
  }
  .header .header-menu .menu {
    position: absolute;
    left: 50%;
    transform: translateX(-50%);
  }
  .header .header-menu .menu__nav {
    display: flex;
    gap: 40px;
    font-weight: 500;
  }
  .header .header-menu .menu__nav a {
    display: flex;
    align-items: center;
    padding: 7px 16px;
    border-radius: 8px;
    transition: all 0.2s cubic-bezier(0.215, 0.61, 0.355, 1);
  }
}

@media (min-width: 1200px) and (max-width: 1399px) {
  .header .header-menu .menu__nav a {
    padding: 6px 6px;
  }
}

@media (min-width: 1200px) {
  .header .header-menu .menu__nav li.menu-item-has-children a {
    display: flex;
    align-items: center;
  }
  .header .header-menu .menu__nav > li.menu-item-has-children > a .icon-chevron-right {
    transform: rotate(90deg);
  }
  .header .header-menu .menu__nav > li {
    padding: 17px 0;
  }
  .header .header-menu .menu__nav > li:hover > a {
    color: var(--color-green-1);
    background-color: rgba(255, 255, 255, 0.15);
  }
  .header .header-menu .menu__nav > li > .subnav::before {
    content: '';
    position: absolute;
    top: -5px;
    left: 20px;
    width: 10px;
    height: 10px;
    background-color: white;
    transform: rotate(45deg);
  }
  .header .header-menu .menu li.-has-mega-menu:hover > .mega {
    opacity: 1 !important;
    pointer-events: auto !important;
  }
  .header .header-menu .menu li:hover > .subnav {
    opacity: 1;
    pointer-events: auto;
  }
  .header .header-menu .menu .subnav {
    position: absolute;
    top: 100%;
    background-color: white;
    border-radius: 8px;
    color: black !important;
    min-width: 230px;
    padding-top: 20px;
    padding-bottom: 20px;
    box-shadow: 0px 25px 70px 0px #01213A12;
    opacity: 0;
    pointer-events: none;
    transition: all 0.2s cubic-bezier(0.215, 0.61, 0.355, 1);
  }
  .header .header-menu .menu .subnav .subnav {
    top: 0;
    left: 97%;
  }
  .header .header-menu .menu .subnav > li {
    position: relative;
  }
  .header .header-menu .menu .subnav > li > a {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 10px 30px;
  }
  .header .header-menu .menu .subnav > li > a:hover {
    color: var(--color-purple-1);
  }
  .header .header-menu .menu .subnav .menu__backButton {
    display: none;
  }
  .header .header-menu .menu > .subnav {
    top: 100%;
    left: 0;
  }
  .header .header-menu .mobile-bg {
    display: none;
  }
  .header .header-menu .mobile-back-button {
    display: none;
  }
  .header .header-menu .mobile-footer {
    display: none;
  }
}

@media (max-width: 1199px) {
  .header .header-menu {
    position: fixed !important;
    top: 0;
    left: 0;
    z-index: 10;
    max-width: calc(100vw - 80px);
    width: 60vw;
    height: 100vh;
    padding: 0 !important;
    transition: all 0.3s cubic-bezier(0.215, 0.61, 0.355, 1);
    opacity: 0;
    pointer-events: none;
  }
  .header .header-menu .header-menu__content {
    display: flex;
    flex-direction: column;
    height: 100%;
    transition: all 0.3s cubic-bezier(0.215, 0.61, 0.355, 1);
    transform: translateX(-20%);
  }
  .header .header-menu ul {
    overflow-y: hidden;
    overflow-x: hidden;
  }
  .header .header-menu.-is-el-visible {
    opacity: 1;
    pointer-events: auto;
  }
  .header .header-menu.-is-el-visible .-is-active {
    pointer-events: auto !important;
    overflow-y: scroll;
    overflow-x: hidden;
  }
  .header .header-menu.-is-el-visible .header-menu__content {
    transform: none;
  }
}

@media (max-width: 1199px) and (max-width: 767px) {
  .header .header-menu {
    width: 80vw;
  }
}

@media (max-width: 1199px) and (max-width: 575px) {
  .header .header-menu {
    width: calc(100vw - 60px);
    max-width: 100%;
  }
}

@media (max-width: 1199px) {
  .header .header-menu-close {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: -1;
  }
}

@media (max-width: 1199px) and (max-width: 575px) {
  .header .header-menu-close {
    top: 10px;
    right: 10px;
  }
}

@media (max-width: 1199px) {
  .header .header-menu-bg {
    position: fixed;
    top: 0;
    bottom: 0;
    left: 0;
    right: 0;
    background-color: rgba(24, 24, 26, 0.7);
    z-index: -2;
  }
  .header .header-menu .menu {
    overflow-y: hidden;
    overflow-x: hidden;
    position: relative;
    padding: 20px;
    height: 100%;
  }
  .header .header-menu .menu ul {
    pointer-events: none;
  }
  .header .header-menu .menu__nav {
    display: flex;
    flex-direction: column;
  }
  .header .header-menu .menu__nav a {
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 60px;
    padding: 0 20px;
    border-radius: 8px;
    color: var(--color-dark-1) !important;
    transition: all 0.2s cubic-bezier(0.215, 0.61, 0.355, 1);
  }
  .header .header-menu .menu__nav a:hover {
    color: var(--color-purple-1) !important;
    background-color: #F4F2FF;
  }
  .header .header-menu .menu__nav > li > a {
    font-weight: 500;
    font-size: 16px;
  }
  .header .header-menu .menu__nav li {
    overflow: hidden;
  }
  .header .header-menu .menu .subnav {
    position: absolute;
    top: 0;
    left: 0;
    color: black !important;
    padding: 20px;
    width: 100%;
    height: 100%;
  }
  .header .header-menu .menu .subnav a {
    transform: translateY(100%);
  }
  .header .header-menu .menu .subnav > li > a {
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 45px;
    padding: 0 20px;
    color: var(--color-dark-1) !important;
    transition: color 0.2s cubic-bezier(0.215, 0.61, 0.355, 1);
  }
  .header .header-menu .menu .subnav > li > a:hover {
    color: var(--color-purple-1) !important;
  }
  .header .header-menu .menu .subnav .menu__backButton a {
    justify-content: flex-start !important;
    height: 60px !important;
    font-weight: 600 !important;
    color: var(--color-purple-1) !important;
    font-size: 16px;
    background-color: var(--color-purple-3);
  }
  .header .header-menu .mobile-bg {
    position: fixed;
    top: 0;
    left: 0;
    max-width: calc(100vw - 80px);
    width: 60vw;
    height: 100vh;
    z-index: -1;
    background-color: white;
  }
}

@media (max-width: 1199px) and (max-width: 767px) {
  .header .header-menu .mobile-bg {
    width: 80vw;
  }
}

@media (max-width: 1199px) and (max-width: 575px) {
  .header .header-menu .mobile-bg {
    width: calc(100vw - 60px);
    max-width: 100%;
  }
}

@media (max-width: 1199px) {
  .header .header-menu .mobile-footer .mobile-socials {
    display: flex;
    margin-left: -15px;
  }
  .header .header-menu .mobile-footer .mobile-socials a {
    transition: all 0.2s cubic-bezier(0.215, 0.61, 0.355, 1);
  }
  .header .header-menu .mobile-footer .mobile-socials a:hover {
    background-color: var(--color-light-4);
    color: var(--color-purple-1) !important;
  }
}

.header.-base {
  z-index: 100;
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
}

.header.-base .header__container {
  position: relative;
  padding: 0 60px;
}

@media (max-width: 991px) {
  .header.-base .header__container {
    padding: 0 40px;
  }
}

@media (max-width: 767px) {
  .header.-base .header__container {
    padding: 0 15px;
  }
}

.header.-base .header-menu {
  position: relative;
}

.header.-base .header-menu .menu {
  position: relative;
  left: unset;
  transform: none;
}

.header.-type-3 {
  position: fixed;
  top: 0;
  left: 0;
  z-index: 100;
  background-color: white;
  width: 100%;
  padding: 0 60px;
}

@media (max-width: 991px) {
  .header.-type-3 {
    padding: 0 40px;
  }
}

@media (max-width: 767px) {
  .header.-type-3 {
    padding: 0 15px;
  }
}

.header.-type-3 .header__container {
  position: relative;
  width: 100%;
  border-bottom: 1px solid rgba(0, 0, 0, 0.15);
}

.header.-type-3 .header-menu {
  position: relative;
}

.header.-type-3 .header-menu .menu {
  position: relative;
  left: unset;
  transform: none;
}

@media (max-width: 1670px) {
  .header.-type-3 .header-search-field {
    display: none;
  }
}

.header.-type-3 .header-search-field__group {
  position: relative;
  max-width: 340px;
  min-width: 260px;
}

.header.-type-3 .header-search-field input {
  width: 100%;
  font-size: 13px;
  line-height: 1;
  background-color: var(--color-light-3);
  border-radius: 8px;
  padding: 18px 28px;
}

.header.-type-3 .header-search-field button {
  position: absolute;
  top: 0;
  right: 0;
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
  width: 40px;
}

.header.-type-3 .header-search-field button .icon {
  font-size: 20px;
  color: var(--color-dark-1);
}

.header.-type-4 {
  position: fixed;
  top: 0;
  left: 0;
  z-index: 100;
  width: 100%;
  padding: 0 60px;
}

@media (max-width: 1199px) {
  .header.-type-4 {
    padding: 0 45px;
  }
}

@media (max-width: 991px) {
  .header.-type-4 {
    padding: 0 15px;
  }
}

.header.-type-4.-shadow {
  box-shadow: 0px 6px 15px 0px #404F680D;
}

.header.-type-4 *.-before-border {
  position: relative;
}

.header.-type-4 *.-before-border::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 1px;
  height: 100%;
  background-color: #EDEDED;
}

.header.-type-4 *.-after-border {
  position: relative;
}

.header.-type-4 *.-after-border::after {
  content: "";
  position: absolute;
  top: 0;
  right: 0;
  width: 1px;
  height: 100%;
  background-color: #EDEDED;
}

.header.-type-4 .header__container {
  position: relative;
}

.header.-type-4 .header__container .row {
  flex-wrap: nowrap;
}

.header.-type-4 .header-menu {
  position: relative;
}

.header.-type-4 .header-menu .menu {
  position: relative;
  left: unset;
  transform: none;
}

.header.-type-4 .header-search-field {
  position: relative;
}

@media (max-width: 1670px) {
  .header.-type-4 .header-search-field {
    display: none;
  }
}

.header.-type-4 .header-search-field__group {
  position: relative;
  max-width: 300px;
  min-width: 250px;
}

.header.-type-4 .header-search-field input {
  width: 100%;
  font-size: 13px;
  line-height: 1;
  background-color: white;
  border: 1px solid #EDEDED;
  border-radius: 100px;
  padding: 17px 28px;
}

.header.-type-4 .header-search-field button {
  position: absolute;
  top: 0;
  right: 0;
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
  width: 60px;
}

.header.-type-4 .header-search-field button .icon {
  font-size: 20px;
  color: var(--color-dark-1);
}

.header.-type-5 {
  z-index: 100;
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
}

.header.-type-5 .header__container {
  width: 100%;
  padding: 0 60px;
}

@media (max-width: 1199px) {
  .header.-type-5 .header__container {
    padding: 0 45px;
  }
}

@media (max-width: 991px) {
  .header.-type-5 .header__container {
    padding: 0 15px;
  }
}

.header.-type-5 .header__container .row {
  flex-wrap: nowrap;
}

.header.-type-5 .header-menu {
  position: relative;
}

.header.-type-5 .header-menu .menu {
  position: relative;
  left: unset;
  transform: none;
}

.header.-base-sidebar {
  z-index: 100;
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  padding: 0 20px;
}

.header.-base-sidebar .row {
  flex-wrap: nowrap;
}

@media (max-width: 767px) {
  .header.-base-sidebar .row {
    margin: 0;
  }
  .header.-base-sidebar .col-auto {
    padding: 0;
  }
}

@media (max-width: 575px) {
  .header.-base-sidebar .header__logo img {
    width: 80%;
  }
}

a {
  color: inherit !important;
  font-size: 14px;
  line-height: inherit;
  font-weight: 500;
  transition: color 0.2s ease-in-out;
  text-decoration: none;
} 

a:hover {
  color: var(--color-purple-1) !important;
}

.footer-light {
  background-color: var(--color-white);
  border-top: 1px solid var(--color-light-2);
  padding: 20px 0;
}

.footer-light__content {
  width: 100%;
}

.footer-light__row {
  display: flex;
  align-items: center;
  justify-content: space-between;
  flex-wrap: wrap;
  gap: 20px;
}

.footer-light__copyright {
  font-size: var(--text-13);
  color: var(--color-light-1);
}

.footer-light__right {
  display: flex;
  align-items: center;
  gap: 30px;
}

.footer-light__links {
  display: flex;
  align-items: center;
  gap: 20px;
}

.footer-light__language {
  display: flex;
  align-items: center;
  padding: 10px 20px;
  border-radius: 30px;
  background-color: var(--color-light-4);
  color: var(--color-light-1);
  font-size: var(--text-14);
  transition: all 0.3s ease;
}

.footer-light__language:hover {
  background-color: var(--color-light-3);
}

@media (max-width: 767px) {
  .footer-light__row {
    justify-content: center;
    text-align: center;
  }
  
  .footer-light__right {
    flex-direction: column;
    gap: 15px;
  }
}
.content-wrapper-sdk {
  position: relative;
  height: 100%;
  width: 100%;
  overflow-y: auto;
  overflow-x: hidden;
  padding-bottom: 100px;
  padding-top: 50px;
}


.no-page {
  margin-top: 40px;
}

.no-page__main {
  font-size: 200px;
  line-height: 1.2;
}

@media (max-width: 991px) {
  .no-page__main {
    font-size: 160px;
  }
}

@media (max-width: 767px) {
  .no-page__main {
    font-size: 140px;
  }
}

/* Responsive Footer Styles */
@media (max-width: 768px) {
  .footer-header-socials {
    flex-direction: column;
    align-items: center;
  }

  .footer-header-socials__list {
    margin-top: 15px;
  }

  .footer-links-wrapper {
    margin-bottom: 20px;
  }

  .language-selector {
    width: 100%;
    display: flex;
    justify-content: center;
  }

  .footer.-type-1 .footer-columns {
    padding: 40px 0;
  }

  .footer.-type-1 .footer-header {
    padding: 40px 0;
  }
}

/* General Footer Improvements */
.footer-header-socials__list {
  display: flex;
  gap: 15px;
  flex-wrap: wrap;
}

.footer-links-wrapper {
  display: flex;
  justify-content: center;
}

.language-selector .button {
  min-width: 160px;
}

@media (min-width: 769px) {
  .w-md-auto {
    width: auto !important;
  }

  .text-md-left {
    text-align: left !important;
  }

  .justify-md-start {
    justify-content: flex-start !important;
  }

  .justify-md-end {
    justify-content: flex-end !important;
  }

  .mb-md-0 {
    margin-bottom: 0 !important;
  }
}

.footer-dark {
  background-color: #071952;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.footer-dark__content {
  width: 100%;
}

.footer-dark__row {
  display: flex;
  align-items: center;
  justify-content: space-between;
  flex-wrap: wrap;
  gap: 20px;
}

.footer-dark__copyright {
  font-size: var(--text-13);
  color: var(--color-white);
}

.footer-dark__right {
  display: flex;
  align-items: center;
  gap: 30px;
}

.footer-dark__links {
  display: flex;
  align-items: center;
  gap: 20px;
}

.footer-dark__links a {
  color: var(--color-white) !important;
}

.footer-dark__language {
  display: flex;
  align-items: center;
  padding: 10px 20px;
  border-radius: 30px;
  background-color: rgba(255, 255, 255, 0.1);
  color: var(--color-white);
  font-size: var(--text-14);
  transition: all 0.3s ease;
  gap: 10px;
}

.footer-dark__language:hover {
  background-color: rgba(255, 255, 255, 0.2);
}

@media (max-width: 767px) {
  .footer-dark__row {
    justify-content: center;
    text-align: center;
  }
  
  .footer-dark__right {
    flex-direction: column;
    gap: 15px;
  }
}


form.contact-form select,
form.contact-form input[type="text"],
form.contact-form input[type="password"],
form.contact-form input[type="search"],
form.contact-form input[type="number"],
form.contact-form input[type="date"],
form.contact-form textarea,
form.contact-form input[type="email"] {
  border: 0;
  outline: none;
  width: 100%;
  background-color: transparent;
  border-radius: 8px;
  border: 1px solid #DDDDDD;
  font-size: 15px;
  line-height: 1.5;
  padding: 15px 22px;
  transition: all 0.15s cubic-bezier(0.165, 0.84, 0.44, 1);
}

#tags-outlined {
  border: none;
}

form.contact-form select:focus,
form.contact-form input[type="text"]:focus,
form.contact-form input[type="password"]:focus,
form.contact-form input[type="search"]:focus,
form.contact-form input[type="date"]:focus,
form.contact-form input[type="number"]:focus,
form.contact-form textarea:focus,
form.contact-form input[type="email"]:focus {
  outline: none;
}

form.contact-form select.-border-dark,
form.contact-form input[type="text"].-border-dark,
form.contact-form input[type="password"].-border-dark,
form.contact-form input[type="search"].-border-dark,
form.contact-form input[type="date"].-border-dark,
form.contact-form input[type="number"].-border-dark,
form.contact-form textarea.-border-dark,
form.contact-form input[type="email"].-border-dark {
  border: 1px solid rgba(0, 0, 0, 0.15) !important;
}

form.contact-form ::-moz-placeholder {
  font-weight: 400;
}

form.contact-form :-ms-input-placeholder {
  font-weight: 400;
}

form.contact-form ::placeholder {
  font-weight: 400;
}

form.contact-form.-light label,
form.contact-form.-light p {
  color: white;
}

form.contact-form.-light select,
form.contact-form.-light input[type="text"],
form.contact-form.-light input[type="search"],
form.contact-form.-light input[type="number"],
form.contact-form.-light input[type="date"],
form.contact-form.-light textarea,
form.contact-form.-light input[type="email"] {
  color: white;
  border-bottom: 1px solid rgba(255, 255, 255, 0.6);
}

form.contact-form.-light ::-moz-placeholder {
  color: rgba(255, 255, 255, 0.7);
}

form.contact-form.-light :-ms-input-placeholder {
  color: rgba(255, 255, 255, 0.7);
}

form.contact-form.-light ::placeholder {
  color: rgba(255, 255, 255, 0.7);
}


.sectionTitle__title {
  font-size: 30px;
  line-height: 34px;
  font-weight: 700;
  color: var(--color-dark-1);
}

.sectionTitle__text {
  margin-top: 10px;
  color: var(--color-light-1);
}

.sectionTitle.-light .sectionTitle__title {
  color: white;
}

.sectionTitle.-light .sectionTitle__text {
  color: white;
}


.accordion__icon {
  position: relative;
}

.accordion__icon .icon {
  transition: all 0.2s ease-out;
}

.accordion__icon .icon:nth-child(1) {
  position: absolute;
  top: 0;
  left: 0;
}

.accordion__icon .icon:nth-child(2) {
  position: relative;
  transform: rotate(-90deg);
  opacity: 1;
}

.accordion__item.is-active .accordion__icon > *:nth-child(1) {
  transform: rotate(90deg);
  opacity: 1;
}

.accordion__item.is-active .accordion__icon > *:nth-child(2) {
  transform: none;
  opacity: 1;
}

.accordion__content {
  overflow: hidden;
  max-height: 0;
  transition: max-height 0.2s ease-out;
}

.accordion__button {
  cursor: pointer;
  border: none;
  outline: none;
  width: 100%;
  display: flex;
  justify-content: space-between;
  transition: 0.4s;
}

.accordion__button button {
  color: #454545;
}

.accordion__button:active {
  color: #454545;
}

.accordion.-simple .accordion__item {
  padding: 0;
}

.accordion.-simple .accordion__content {
  overflow: hidden;
  max-height: 0;
  transition: max-height 0.2s ease-out;
}

.accordion.-simple .accordion__content__inner {
  padding-bottom: 1.33333rem;
}

.accordion.-simple .accordion__button {
  cursor: pointer;
  color: var(--text-dark-1);
  padding: 1.26667rem 0;
  width: 100%;
  display: flex;
  justify-content: flex-start;
  align-items: center;
  border: none;
  outline: none;
  transition: 0.4s;
}

.accordion.-simple .accordion__button button {
  color: var(--text-dark-1);
}

.accordion.-simple .accordion__icon {
  position: relative;
  margin-right: 1rem;
}

.accordion.-simple .accordion__icon .icon {
  width: 1.6rem;
  height: 1.6rem;
  stroke-width: 2;
  transition: all 0.2s ease-out;
}

.accordion.-simple .accordion__icon .icon:nth-child(1) {
  position: absolute;
  top: 0;
  left: 0;
}

.accordion.-simple .accordion__icon .icon:nth-child(2) {
  position: relative;
  transform: rotate(-90deg);
  opacity: 0;
}

.accordion.-simple .accordion__item.is-active .accordion__icon > *:nth-child(1) {
  transform: rotate(90deg);
  opacity: 0;
}

.accordion.-simple .accordion__item.is-active .accordion__icon > *:nth-child(2) {
  transform: none;
  opacity: 1;
}

.accordion.-simple.-light .accordion__item + .accordion__item {
  border-top: 1px solid rgba(255, 255, 255, 0.2);
}

.accordion.-block .accordion__item {
  box-shadow: 0px 1px 3px 1px #21858E40;
  border-radius: 16px;
  padding: 0;
}

.accordion.-block .accordion__item + .accordion__item {
  margin-top: 20px;
}

.accordion.-block .accordion__content {
  overflow: hidden;
  max-height: 0;
  transition: max-height 0.2s ease-out;
}

.accordion.-block .accordion__content__inner {
  padding: 0 85px 35px 82px;
}

@media (max-width: 575px) {
  .accordion.-block .accordion__content__inner {
    padding: 0 25px 35px 82px;
  }
}

.accordion.-block .accordion__button {
  border: none;
  outline: none;
  cursor: pointer;
  display: flex;
  justify-content: flex-start;
  align-items: center;
  padding: 20px;
  width: 100%;
  transition: all 0.4s cubic-bezier(0.165, 0.84, 0.44, 1);
}

.accordion.-block .accordion__icon {
  position: relative;
  display: flex;
  justify-content: center;
  align-items: center;
  flex-shrink: 0;
  width: 35px;
  height: 35px;
  color: white;
  border-radius: 100%;
  margin-right: 22px;
  transition: all 0.2s ease-out;
}

.accordion.-block .accordion__icon .icon {
  position: absolute;
  width: 34px;
  height: 34px;
  stroke-width: 2;
  color: var(--color-purple-1);
  transition: all 0.2s ease-out;
}

.accordion.-block .accordion__icon .icon:nth-child(1) {
  top: unset;
  left: unset;
}

.accordion.-block .accordion__icon .icon:nth-child(2) {
  transform: rotate(-90deg);
  opacity: 0;
}


.accordion.-block .accordion__item.is-active .accordion__icon > *:nth-child(1) {
  transform: rotate(360deg);
  opacity: 1;
}

.accordion.-block .accordion__item.is-active .accordion__icon > *:nth-child(2) {
  transform: none;
  opacity: 1;
}

.-dark-mode .accordion.-block-2 .accordion__item {
  border: 1px solid rgba(255, 255, 255, 0.1) !important;
}

.accordion.-block-2 .accordion__item {
  background-color: #FFFFFF;
  border-radius: 16px;
  padding: 0;
  border: 1px solid #EDEDED;
}

.accordion.-block-2 .accordion__item + .accordion__item {
  margin-top: 1.33333rem;
}

.accordion.-block-2 .accordion__content {
  overflow: hidden;
  max-height: 0;
  transition: max-height 0.2s ease-out;
}

.accordion.-block-2 .accordion__button {
  border-radius: 16px;
  border: none;
  outline: none;
  cursor: pointer;
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  transition: all 0.4s cubic-bezier(0.165, 0.84, 0.44, 1);
}

.accordion.-block-2 .accordion__icon {
  position: relative;
  flex-shrink: 0;
  width: 24px;
  height: 24px;
  margin-right: 14px;
  transition: all 0.2s ease-out;
}

.accordion.-block-2 .accordion__icon .icon {
  position: absolute;
  width: 24px;
  height: 24px;
  stroke-width: 2;
  transition: all 0.2s ease-out;
}

.accordion.-block-2 .accordion__icon .icon:nth-child(2) {
  transform: rotate(-90deg);
  opacity: 0;
}

.accordion.-block-2 .accordion__item.is-active .accordion__icon > *:nth-child(1) {
  transform: rotate(90deg);
  opacity: 0;
}

.accordion.-block-2 .accordion__item.is-active .accordion__icon > *:nth-child(2) {
  transform: none;
  opacity: 1;
}

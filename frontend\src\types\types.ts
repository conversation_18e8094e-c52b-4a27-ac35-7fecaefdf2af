export interface Policy {
    id: number;
    attach_content: File | string | null;
    uuid: string;
    title: string;
    category: string;
    author_name: string;
    job_title: string;
    description: string;
    created_at: string;
    updated_at: string;
    study: {
        uuid: string;
        name: string;
        iras: string;
        description: string;
    };
    date_c?: string;
    policy_state: 'active' | 'superseded';
    access_level: 'patientAndStaff' | 'staff';
    policy_version: number;
    attachment_url?: string;
}

export interface User {
    id: number;
    uuid: string;
    identifier: string;
    first_name: string;
    last_name: string;
    email: string;
    phone_number: string;
    gender: string;
    speciality?: string;
    image?: string | File | null; // Support both URL strings and File objects
    profile_picture?: string | null; // Actual field returned by API
    registration_body?: string;
    created_at: string;
    updated_at: string;
    department?: {
        uuid: string;
        id: number;
        name: string;
    };
    hospital?: {
        uuid: string;
        id: number;
        name: string;
    };
    departments?: {
        uuid: string;
        name: string;
        hospital: {
            uuid: string;
            name: string;
        };
    }[];
    is_superuser?:boolean;
    is_hospital_admin?:boolean;
    is_admin?:boolean;
    is_staff?:boolean;
    user_type?: string;
    organization_name?: string;
    organization_type?: string;
    is_active?:boolean;
    patient_uuid?: string;
    patient_forms?: any[];
}
export interface Admin {
    identifier: string;
    is_active:boolean;
    first_name: string;
    last_name: string;
    email: string;
    phone_number: string;
    speciality?: string;
}

export type UserCreateData = FormData;

export interface ApiError {
    message: string | undefined;
    response?: {
        data?: {
            detail?: string;
            message?: string;
        };
    };
}

export interface Tag {
    uuid: string;
    name: string;
    created_at: string;
}

export interface FormVersion {
    uuid: string;
    version: number;
    description: string;
    categories: string[];
    study: string;
    study_uuid: string;
    password?: string;
    structure: string;
    structure_data?: Record<string, unknown>;
    status: 'pending' | 'accepted' | 'rejected' | 'archived';
    created_at: string;
    is_active: boolean;
    form_structure: Record<string, unknown>;
    privacy: string;
}

export interface Form {
    password: string;
    study: string | null;
    study_uuid?: string;
    description: string;
    uuid: string;
    name: string;
    privacy: string;
    user: {
        identifier: string;
        first_name: string;
        last_name: string;
        email: string;
    };
    created_at: string;
    active_version?: FormVersion;
    status: "accepted" | "rejected" | "pending" | "archived";
    form_structure: Record<string, unknown> | undefined;
    updated_at: string;
    categories: string | undefined;
}

export interface FormCreate {
    name: string;
    description: string;
    categories: string[];
    study: string | null;
    password?: string;
    form_structure: Record<string, unknown>;
}

export interface FormSubmissionAttachment {
    uuid: string;
    file: string;
    file_type: 'image' | 'video' | 'document';
    uploaded_at: string;
    uploaded_by: {
        identifier: string;
        first_name: string;
        last_name: string;
        email: string;
    };
}


export interface Visit {
    id: number;
    uuid: string;
    name: string;
    description: string;
    created_at: string;
    updated_at: string;
}

export interface Study {
    id: number;
    uuid: string;
    name: string;
    description: string;
    visits: Visit[];
    created_at: string;
    updated_at: string;
}

export interface HolisticFormData {

    patientDetails: Record<string, any>;

    situation: Record<string, any>;

    recommendations: Record<string, any>;

    background: Record<string, any>;

    assessment: Record<string, any>;

    matched_past_medical_history_options: Record<string, any>;
    when_symptoms_started: string;
  }

  export interface HolisticFormDataResponse extends HolisticFormData {
      uuid:string;
  }
  export interface PDFResponse {
    data: Blob;
  }

export interface FormSubmission {
    updated_by: { identifier: string; first_name: string; last_name: string; email: string; } | undefined;
    updated_at: string;
    uuid: string;
    form: string | {
        uuid: string;
        name: string;
    };
    user: number | {
        id: number;
        identifier: string;
        first_name: string;
        last_name: string;
        email: string;
    };
    patient_uuid?: string;
    is_completed: boolean;
    submission: Record<string, unknown>;
    created_at: string;
    final_submission: boolean;
    last_updated_by?: {
        identifier: string;
        first_name: string;
        last_name: string;
        email: string;
    };
    last_updated_at: string;
    attachments?: FormSubmissionAttachment[];
}

export interface FormSubmissionQuery {
    uuid: string;
    form_submission_uuid: string;
    question_number: string;
    description: string;
    priority: 'low' | 'medium' | 'high';
    created_by: {
        identifier: string;
        first_name: string;
        last_name: string;
        email: string;
    };
    created_at: string;
    resolved_at: string | null;
    is_resolved: boolean;
    resolution_notes: string | null;
}

export interface FormSubmissionQueryCreate {
    form_submission_uuid: string;
    question_number: string;
    description: string;
    priority: 'low' | 'medium' | 'high';
}

// Blog interfaces
export interface BlogSection {
    title: string;
    content: string;
}

export interface BlogReference {
    text: string;
    url?: string;
}

export interface BlogArticle {
    id: string;
    image: string;
    title: string;
    author: string;
    date: string;
    excerpt: string;
    sections: BlogSection[];
    references: BlogReference[];
}

// Consent Module Types
export interface ConsentForm {
    uuid: string;
    sponsor_id: string;
    study_id: string;
    version: string;
    created_at: string;
    updated_at: string;
    is_active: boolean;
    source_consent_id?: string; // For duplicates
    name: string;
    description?: string;
    questions: ConsentQuestion[];
    // Add computed properties for table display
    study_name?: string; // Computed from study_id
    questions_count?: number; // Computed from questions.length
    status?: 'active' | 'inactive'; // Computed from is_active
}

export interface ConsentQuestion {
    uuid: string;
    consent_form_id: string;
    question_text: string;
    is_required: boolean;
    sequence: number;
    created_at: string;
}

export interface PatientConsent {
    uuid: string;
    patient_id: string;
    consent_form_id: string;
    consent_status: 'Consented' | 'Not Consented' | 'Withdrawn';
    signed_at: string;
    created_at: string;
    updated_at: string;
}

export interface PatientConsentAnswer {
    uuid: string;
    patient_consent_id: string;
    question_id: string;
    answer: boolean; // True (Agreed), False (Declined)
    signed_at: string;
    created_at: string;
}

export interface ConsentFormCreate {
    sponsor_id: string;
    study_id: string;
    name: string;
    description?: string;
    questions: ConsentQuestionCreate[];
    source_consent_id?: string; // For duplicates
}

export interface ConsentQuestionCreate {
    question_text: string;
    is_required: boolean;
    sequence: number;
}

export interface ConsentFormUpdate {
    name?: string;
    description?: string;
    is_active?: boolean;
}

export interface PatientConsentCreate {
    patient_id: string;
    consent_form_id: string;
    answers: PatientConsentAnswerCreate[];
}

export interface PatientConsentAnswerCreate {
    question_id: string;
    answer: boolean;
}

export interface ConsentFormResponse {
    count: number;
    next: string | null;
    previous: string | null;
    results: ConsentForm[];
}

export interface PatientConsentResponse {
    count: number;
    next: string | null;
    previous: string | null;
    results: PatientConsent[];
}

import React from "react";
import {Link, } from "react-router-dom";
import { Plus } from "lucide-react";

interface NoResultProps {
  title?: string;
  description?: string;
  addPath?: string;
}

const NoResult: React.FC<NoResultProps> = ({
  title ,
  description = "It looks like there are no items available at the moment. Try adding a new item to get started!",
  addPath,
}) => {
  return (
    <div className="no-result-container text-center">
      <img src="/assets/img/404NotFounds.svg"  height={150}
          width={200} alt="image" />
      <h2 className="mt-3">{title ? `No ${title}s Found` : 'No Results Found'}</h2>
      <p className="text-muted">{description}</p>
      {addPath && <div className="mt-2 mb-4 d-flex justify-content-center">
        <Link to={addPath} className="no-result-icon   ">
                                        Add {title && <>{title}</>}  <Plus size={18}/>
                                    </Link>
        </div>}
    </div>
  );
};

export default NoResult;
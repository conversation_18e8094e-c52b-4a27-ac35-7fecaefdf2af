/* Pitch Page Styles */
.pitch-page .particles {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: -1;
  background: radial-gradient(circle at 20% 20%, rgba(55, 183, 195, 0.1) 0%, transparent 50%),
              radial-gradient(circle at 80% 80%, rgba(8, 131, 149, 0.1) 0%, transparent 50%);
}

.pitch-page {
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  background: linear-gradient(135deg, #071952 0%, #088395 100%);
  color: #EBF4F6;
  line-height: 1.6;
  overflow-x: hidden;
}

.pitch-page .container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

.pitch-page .hero {
  height: 100vh;
  display: flex;
  align-items: center;
  position: relative;
  background: linear-gradient(135deg, #071952 0%, #088395 50%, #37B7C3 100%);
}

.pitch-page .hero-content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 4rem;
  align-items: center;
  width: 100%;
}

.pitch-page .hero-text h1 {
  font-size: 3.5rem;
  font-weight: 700;
  margin-bottom: 1.5rem;
  background: linear-gradient(45deg, #EBF4F6, #37B7C3);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  line-height: 1.2;
}

.pitch-page .hero-text p {
  font-size: 1.3rem;
  margin-bottom: 2rem;
  color: #EBF4F6;
  opacity: 0.9;
}

.pitch-page .video-container {
  position: relative;
  border-radius: 20px;
  overflow: hidden;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
  background: rgba(7, 25, 82, 0.3);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(55, 183, 195, 0.3);
}

.pitch-page .video-placeholder {
  width: 100%;
  height: 400px;
  background: linear-gradient(45deg, #071952, #088395);
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  cursor: pointer;
  overflow: hidden;
}

.pitch-page .video-placeholder::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="rgba(55,183,195,0.1)" stroke-width="1"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
  opacity: 0.3;
}

.pitch-page .play-button {
  width: 80px;
  height: 80px;
  background: rgba(55, 183, 195, 0.9);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
  z-index: 2;
}

.pitch-page .play-button:hover {
  transform: scale(1.1);
  background: #37B7C3;
}

.pitch-page .play-button::after {
  content: '';
  width: 0;
  height: 0;
  border-left: 20px solid #071952;
  border-top: 12px solid transparent;
  border-bottom: 12px solid transparent;
  margin-left: 4px;
}

.pitch-page .video-overlay {
  position: absolute;
  top: 20px;
  left: 20px;
  background: rgba(7, 25, 82, 0.8);
  padding: 0.5rem 1rem;
  border-radius: 25px;
  font-size: 0.9rem;
  color: #37B7C3;
  backdrop-filter: blur(5px);
}

/* Responsive Design */
@media (max-width: 768px) {
  .pitch-page .hero-content {
    grid-template-columns: 1fr;
    text-align: center;
    gap: 2rem;
  }

  .pitch-page .hero-text h1 {
    font-size: 2.5rem;
  }

  .pitch-page .video-placeholder {
    height: 250px;
  }
}

/* Animations */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.pitch-page .hero-text,
.pitch-page .video-container {
  animation: fadeInUp 0.8s ease-out;
}

/* Sponsor Consents Page Styles - Prefixed with .sponsor-consents- */

/* Main Container */
.sponsor-consents-container {
  font-family: var(--font-primary);
  color: var(--color-dark-1);
  background-color: var(--color-light-4);
  min-height: 100vh;
}

/* Header Section */
.sponsor-consents-header {
  background: linear-gradient(135deg, var(--color-white) 0%, var(--color-light-6) 100%);
  border-radius: 16px;
  padding: 32px;
  margin-bottom: 32px;
  box-shadow: 0px 4px 20px rgba(0, 0, 0, 0.08);
  border: 1px solid var(--color-light-2);
}

.sponsor-consents-header-content {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  gap: 24px;
}

.sponsor-consents-header-left {
  flex: 1;
}

.sponsor-consents-title-section {
  display: flex;
  align-items: center;
  gap: 16px;
  margin-bottom: 24px;
}

.sponsor-consents-icon-wrapper {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 56px;
  height: 56px;
  background: linear-gradient(135deg, var(--color-purple-1) 0%, var(--color-blue-1) 100%);
  border-radius: 16px;
  color: white;
  box-shadow: 0px 8px 24px rgba(55, 183, 195, 0.3);
}

.sponsor-consents-title-content h1 {
  font-size: var(--text-30);
  font-weight: 700;
  color: var(--color-dark-1);
  margin: 0 0 8px 0;
  line-height: 1.2;
}

.sponsor-consents-title-content p {
  font-size: var(--text-16);
  color: var(--color-light-1);
  margin: 0;
  line-height: 1.5;
}

/* Stats Section */
.sponsor-consents-stats {
  display: flex;
  gap: 24px;
  flex-wrap: wrap;
}

.sponsor-consents-stat-card {
  display: flex;
  align-items: center;
  gap: 12px;
  background: var(--color-white);
  padding: 16px 20px;
  border-radius: 12px;
  border: 1px solid var(--color-light-2);
  box-shadow: 0px 2px 8px rgba(0, 0, 0, 0.04);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.sponsor-consents-stat-card:hover {
  transform: translateY(-2px);
  box-shadow: 0px 8px 24px rgba(0, 0, 0, 0.12);
}

.sponsor-consents-stat-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  border-radius: 10px;
  color: white;
}

.sponsor-consents-stat-icon.total {
  background: linear-gradient(135deg, var(--color-blue-3) 0%, var(--color-blue-4) 100%);
}

.sponsor-consents-stat-icon.active {
  background: linear-gradient(135deg, var(--color-green-4) 0%, var(--color-green-5) 100%);
}

.sponsor-consents-stat-icon.inactive {
  background: linear-gradient(135deg, var(--color-orange-1) 0%, var(--color-orange-4) 100%);
}

.sponsor-consents-stat-content {
  display: flex;
  flex-direction: column;
}

.sponsor-consents-stat-number {
  font-size: var(--text-20);
  font-weight: 700;
  color: var(--color-dark-1);
  line-height: 1;
}

.sponsor-consents-stat-label {
  font-size: var(--text-13);
  color: var(--color-light-1);
  margin-top: 2px;
}

/* Action Buttons */
.sponsor-consents-actions {
  display: flex;
  gap: 12px;
  flex-shrink: 0;
}

.sponsor-consents-btn {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 20px;
  border-radius: 10px;
  font-size: var(--text-14);
  font-weight: 500;
  border: none;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  text-decoration: none;
}

.sponsor-consents-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.sponsor-consents-btn-secondary {
  background: var(--color-white);
  color: var(--color-light-1);
  border: 1px solid var(--color-light-2);
}

.sponsor-consents-btn-secondary:hover:not(:disabled) {
  background: var(--color-light-3);
  transform: translateY(-1px);
  box-shadow: 0px 4px 12px rgba(0, 0, 0, 0.1);
}

.sponsor-consents-btn-primary {
  background: linear-gradient(135deg, var(--color-purple-1) 0%, var(--color-blue-1) 100%);
  color: white;
  border: none;
  box-shadow: 0px 4px 16px rgba(55, 183, 195, 0.3);
}

.sponsor-consents-btn-primary:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0px 8px 24px rgba(55, 183, 195, 0.4);
}

/* Alert Messages */
.sponsor-consents-alert {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 16px 20px;
  border-radius: 12px;
  margin-bottom: 24px;
  font-size: var(--text-14);
  font-weight: 500;
}

.sponsor-consents-alert-error {
  background: var(--color-error-1);
  color: var(--color-error-2);
  border: 1px solid var(--color-red-2);
}

.sponsor-consents-alert-success {
  background: var(--color-success-1);
  color: var(--color-success-2);
  border: 1px solid var(--color-green-6);
}

/* Filter Section */
.sponsor-consents-filters {
  background: var(--color-white);
  border-radius: 16px;
  padding: 24px;
  margin-bottom: 24px;
  box-shadow: 0px 2px 12px rgba(0, 0, 0, 0.06);
  border: 1px solid var(--color-light-2);
}

.sponsor-consents-filters-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.sponsor-consents-filters-title {
  display: flex;
  align-items: center;
  gap: 12px;
  font-size: var(--text-16);
  font-weight: 600;
  color: var(--color-dark-1);
}

.sponsor-consents-clear-btn {
  background: none;
  border: none;
  color: var(--color-purple-1);
  font-size: var(--text-14);
  font-weight: 500;
  cursor: pointer;
  padding: 6px 12px;
  border-radius: 6px;
  transition: all 0.2s ease;
}

.sponsor-consents-clear-btn:hover {
  background: var(--color-purple-2);
}

.sponsor-consents-filters-content {
  display: grid;
  grid-template-columns: 2fr 1fr 1fr;
  gap: 20px;
  align-items: end;
}

.sponsor-consents-search-wrapper {
  position: relative;
}

.sponsor-consents-search-input {
  width: 100%;
  padding: 12px 16px 12px 44px;
  border: 1px solid var(--color-light-2);
  border-radius: 10px;
  font-size: var(--text-14);
  background: var(--color-white);
  transition: all 0.3s ease;
}

.sponsor-consents-search-input:focus {
  outline: none;
  border-color: var(--color-purple-1);
  box-shadow: 0px 0px 0px 3px rgba(55, 183, 195, 0.1);
}

.sponsor-consents-search-icon {
  position: absolute;
  left: 16px;
  top: 50%;
  transform: translateY(-50%);
  color: var(--color-light-1);
}

/* Table Section */
.sponsor-consents-table-section {
  background: var(--color-white);
  border-radius: 16px;
  overflow: hidden;
  box-shadow: 0px 2px 12px rgba(0, 0, 0, 0.06);
  border: 1px solid var(--color-light-2);
}

.sponsor-consents-table-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24px;
  border-bottom: 1px solid var(--color-light-2);
  background: var(--color-light-6);
}

.sponsor-consents-table-title {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.sponsor-consents-table-title h3 {
  font-size: var(--text-18);
  font-weight: 600;
  color: var(--color-dark-1);
  margin: 0;
}

.sponsor-consents-table-title p {
  font-size: var(--text-14);
  color: var(--color-light-1);
  margin: 0;
}

.sponsor-consents-table-actions {
  display: flex;
  gap: 12px;
}

.sponsor-consents-table-content {
  padding: 0;
}

/* Custom Table Styles */
.sponsor-consents-table {
  width: 100%;
  border-collapse: collapse;
}

.sponsor-consents-table th {
  background: var(--color-light-6);
  padding: 16px 20px;
  text-align: left;
  font-size: var(--text-13);
  font-weight: 600;
  color: var(--color-dark-1);
  text-transform: uppercase;
  letter-spacing: 0.5px;
  border-bottom: 1px solid var(--color-light-2);
}

.sponsor-consents-table td {
  padding: 16px 20px;
  border-bottom: 1px solid var(--color-light-2);
  font-size: var(--text-14);
  color: var(--color-dark-1);
}

.sponsor-consents-table tbody tr {
  transition: all 0.2s ease;
}

.sponsor-consents-table tbody tr:hover {
  background: var(--color-light-6);
}

/* Table Cell Content */
.sponsor-consents-cell-with-icon {
  display: flex;
  align-items: center;
  gap: 8px;
}

.sponsor-consents-version-badge {
  background: var(--color-blue-6);
  color: var(--color-blue-3);
  padding: 4px 8px;
  border-radius: 6px;
  font-size: var(--text-11);
  font-weight: 600;
}

.sponsor-consents-questions-count {
  color: var(--color-light-1);
  font-size: var(--text-13);
}

.sponsor-consents-status-badge {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 6px 12px;
  border-radius: 20px;
  font-size: var(--text-11);
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.sponsor-consents-status-active {
  background: var(--color-green-6);
  color: var(--color-green-5);
}

.sponsor-consents-status-inactive {
  background: var(--color-orange-2);
  color: var(--color-orange-1);
}

.sponsor-consents-date {
  color: var(--color-light-1);
  font-size: var(--text-13);
}

/* Action Buttons in Table */
.sponsor-consents-table-actions-cell {
  display: flex;
  gap: 8px;
  justify-content: flex-end;
}

.sponsor-consents-action-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  border-radius: 8px;
  border: none;
  cursor: pointer;
  transition: all 0.2s ease;
  position: relative;
}

.sponsor-consents-action-btn:hover::after {
  content: attr(data-tooltip);
  position: absolute;
  bottom: 100%;
  left: 50%;
  transform: translateX(-50%);
  background: var(--color-dark-8);
  color: white;
  padding: 6px 8px;
  border-radius: 4px;
  font-size: var(--text-11);
  white-space: nowrap;
  z-index: 1000;
  margin-bottom: 4px;
}

.sponsor-consents-action-view {
  background: var(--color-blue-6);
  color: var(--color-blue-3);
}

.sponsor-consents-action-view:hover {
  background: var(--color-blue-3);
  color: white;
  transform: translateY(-1px);
}

.sponsor-consents-action-duplicate {
  background: var(--color-purple-2);
  color: var(--color-purple-1);
}

.sponsor-consents-action-duplicate:hover {
  background: var(--color-purple-1);
  color: white;
  transform: translateY(-1px);
}

.sponsor-consents-action-delete {
  background: var(--color-red-2);
  color: var(--color-red-1);
}

.sponsor-consents-action-delete:hover {
  background: var(--color-red-1);
  color: white;
  transform: translateY(-1px);
}

/* Empty State */
.sponsor-consents-empty-state {
  text-align: center;
  padding: 60px 20px;
  color: var(--color-light-1);
}

.sponsor-consents-empty-state svg {
  margin-bottom: 16px;
  opacity: 0.5;
}

.sponsor-consents-empty-state h3 {
  font-size: var(--text-18);
  font-weight: 600;
  color: var(--color-dark-1);
  margin: 0 0 8px 0;
}

.sponsor-consents-empty-state p {
  font-size: var(--text-14);
  margin: 0;
}

/* Loading States */
.sponsor-consents-loading {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 40px;
  color: var(--color-light-1);
}

.sponsor-consents-spinner {
  width: 24px;
  height: 24px;
  border: 2px solid var(--color-light-2);
  border-top: 2px solid var(--color-purple-1);
  border-radius: 50%;
  animation: sponsor-consents-spin 1s linear infinite;
}

@keyframes sponsor-consents-spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Responsive Design */
@media (max-width: 1200px) {
  .sponsor-consents-filters-content {
    grid-template-columns: 1fr;
    gap: 16px;
  }
  
  .sponsor-consents-stats {
    justify-content: center;
  }
}

@media (max-width: 768px) {
  .sponsor-consents-header {
    padding: 24px;
  }
  
  .sponsor-consents-header-content {
    flex-direction: column;
    align-items: stretch;
  }
  
  .sponsor-consents-title-section {
    flex-direction: column;
    align-items: center;
    text-align: center;
  }
  
  .sponsor-consents-stats {
    flex-direction: column;
  }
  
  .sponsor-consents-stat-card {
    justify-content: center;
  }
  
  .sponsor-consents-actions {
    flex-direction: column;
  }
  
  .sponsor-consents-filters {
    padding: 20px;
  }
  
  .sponsor-consents-filters-header {
    flex-direction: column;
    gap: 12px;
    align-items: stretch;
  }
  
  .sponsor-consents-table-header {
    flex-direction: column;
    gap: 16px;
    align-items: stretch;
  }
  
  .sponsor-consents-table-actions {
    justify-content: center;
  }
  
  /* Make table horizontally scrollable on mobile */
  .sponsor-consents-table-content {
    overflow-x: auto;
  }
  
  .sponsor-consents-table {
    min-width: 600px;
  }
}

@media (max-width: 480px) {
  .sponsor-consents-header {
    padding: 20px;
  }
  
  .sponsor-consents-title-content h1 {
    font-size: var(--text-24);
  }
  
  .sponsor-consents-icon-wrapper {
    width: 48px;
    height: 48px;
  }
  
  .sponsor-consents-filters {
    padding: 16px;
  }
  
  .sponsor-consents-table-header {
    padding: 20px;
  }
  
  .sponsor-consents-btn {
    padding: 10px 16px;
    font-size: var(--text-13);
  }
}

/* Dark mode support (if needed) */
@media (prefers-color-scheme: dark) {
  .sponsor-consents-container {
    background-color: var(--color-dark-8);
    color: var(--color-white);
  }
  
  .sponsor-consents-header,
  .sponsor-consents-filters,
  .sponsor-consents-table-section {
    background: var(--color-dark-4);
    border-color: var(--color-dark-3);
  }
  
  .sponsor-consents-search-input {
    background: var(--color-dark-4);
    border-color: var(--color-dark-3);
    color: var(--color-white);
  }
  
  .sponsor-consents-table th {
    background: var(--color-dark-3);
  }
  
  .sponsor-consents-table tbody tr:hover {
    background: var(--color-dark-3);
  }
}

/* Animation for smooth transitions */
.sponsor-consents-fade-in {
  animation: sponsor-consents-fadeIn 0.3s ease-in-out;
}

@keyframes sponsor-consents-fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Focus styles for accessibility */
.sponsor-consents-btn:focus,
.sponsor-consents-search-input:focus,
.sponsor-consents-action-btn:focus {
  outline: 2px solid var(--color-purple-1);
  outline-offset: 2px;
}

/* Print styles */
@media print {
  .sponsor-consents-actions,
  .sponsor-consents-table-actions-cell,
  .sponsor-consents-filters {
    display: none;
  }
  
  .sponsor-consents-container {
    background: white;
  }
  
  .sponsor-consents-header,
  .sponsor-consents-table-section {
    box-shadow: none;
    border: 1px solid #ccc;
  }
}

import React, { ChangeEvent, useState, useEffect, useCallback } from "react";
import NurtifyInput from "@/components/NurtifyInput";
import NurtifyTextArea from "@/components/NurtifyTextArea";
import NurtifyRadio from "@/components/NurtifyRadio";
import NurtifyCheckBox from "@/components/NurtifyCheckBox";
import NurtifyMultiInput from "@/components/NurtifyMultiInput";
import NurtifySelect from "@/components/NurtifySelect";
import NurtifyAttachFileBox from "@/components/NurtifyAttachFileBox";
import NurtifyRange from "@/components/NurtifyRange";
import NurtifyComboBox from "@/components/NurtifyComboBox";
import NurtifyDateInput from "@/components/NurtifyDateInput";
import { Trash, AlertCircle, CheckCircle, Loader, Edit3, RefreshCw } from "lucide-react";
import "./surveyformrenderer.css";
import { useCurrentUserQuery } from "@/hooks/user.query";
import { useCreateSubmission } from "@/hooks/form.query";
import useSelectedPatientStore from "@/store/SelectedPatientState";
import { GeneratePdfButton } from "../patient-form/GeneratePdfButton";

export type QuestionType =
  | "single-choice"
  | "multiple-choice"
  | "boolean"
  | "short-text"
  | "long-text"
  | "multiple-text-boxes"
  | "dropdown"
  | "multi-select-dropdown"
  | "attach-file"
  | "signature"
  | "range"
  | "table"
  | "Expression";

export interface SavedQuestionData {
  id: number;
  type: QuestionType | null;
  questionName?: string;
  answers?: string[]; // initial answers (if any)
  options?: string[];
  signature?: Record<string, unknown>;
  attachment?: Record<string, unknown>;
  table?: Record<string, unknown>;
  inputType?: string;
  expression?: string;
  required?: boolean;
  nonClinical?: boolean;
  range?: { min: string; max: string; currentValue: string };
  fileTypes?: string;
  multiple?: boolean;
}

export interface SavedSectionData {
  id: number;
  name: string;
  description: string;
  questions: SavedQuestionData[];
}

export interface SavedFormData {
  formDetails: {
    name: string;
    description: string;
    categories: string;
    study: string | null;
    password: string;
    privacy: string; // Added privacy field
  };
  sections: SavedSectionData[];
  existingSubmissionData?: Record<string, unknown>; // Add this field for existing submission data
}

type FormAnswers = { [sectionId: number]: { [questionId: number]: unknown } };
type FormComments = { [sectionId: number]: { [questionId: number]: string } };

interface RebuildFormProps {
  formData: SavedFormData;
  formUuid: string;
  activeFilter?: string;
  patientsMode?: boolean;
  onSubmit: (data: Record<string, unknown>, isCompleted?: boolean) => void;
  onSaveDraft?: (data: Record<string, unknown>) => void;
  isEditMode?: boolean;
  trackedChanges?: Record<string, unknown>;
}

// Move these type definitions to the top-level scope, after imports:

export interface CommentHistoryItem {
  comment: string;
  "commented-by"?: {
    identifier?: string;
    first_name?: string;
    last_name?: string;
    email?: string;
    user_type?: string;
    [key: string]: unknown;
  } | string;
  "commented-at"?: string;
  [key: string]: unknown;
}

export interface ExistingSubmissionQuestion {
  id: number;
  comment?: string;
  "commented-by"?: CommentHistoryItem["commented-by"];
  "commented-at"?: string;
  comment_history?: CommentHistoryItem[];
  [key: string]: unknown;
}

export interface ExistingSubmissionSection {
  id: number;
  questions: ExistingSubmissionQuestion[];
  [key: string]: unknown;
}

function RebuildForm({
  formData,
  formUuid,
  activeFilter,
  patientsMode,
  onSubmit,
  onSaveDraft,
  isEditMode,
  trackedChanges
}: RebuildFormProps) {
  const [activeSectionIndex, setActiveSectionIndex] = useState<number>(0);
  const [formAnswers, setFormAnswers] = useState<FormAnswers>(() => {
    const initialAnswers: FormAnswers = {};
    formData.sections.forEach((section) => {
      initialAnswers[section.id] = {};
      section.questions.forEach((question) => {
        if (question.type === "multiple-choice") {
          initialAnswers[section.id][question.id] = [];
        } else if (question.type === "table") {
          initialAnswers[section.id][question.id] = { rows: 1, data: [[]] };
        } else {
          initialAnswers[section.id][question.id] = "";
        }
      });
    });
    return initialAnswers;
  });

  // Add comment state
  const [formComments, setFormComments] = useState<FormComments>(() => {
    const initialComments: FormComments = {};
    formData.sections.forEach((section) => {
      initialComments[section.id] = {};
      section.questions.forEach((question) => {
        initialComments[section.id][question.id] = "";
      });
    });
    return initialComments;
  });

  // Add state for tracking which questions have comments expanded
  const [expandedComments, setExpandedComments] = useState<Set<string>>(() => {
    // On mount, expand all questions that already have a comment
    const initial = new Set<string>();
    formData.sections.forEach(section => {
      section.questions.forEach(question => {
        if (
          formComments[section.id]?.[question.id] &&
          formComments[section.id][question.id].trim() !== ""
        ) {
          initial.add(`${section.id}-${question.id}`);
        }
      });
    });
    return initial;
  });

  const [submissionStatus, setSubmissionStatus] = useState<'idle' | 'loading' | 'success' | 'error'>('idle');
  const [errorMessage, setErrorMessage] = useState<string | null>(null);
  const [invalidRequiredFields, setInvalidRequiredFields] = useState<Set<string>>(new Set());

  // Track which questions have been answered - used for state management and tracking form completion
  // This state is necessary for proper form validation and submission logic
  const [answeredQuestions, setAnsweredQuestions] = useState<Set<string>>(new Set());

  // This effect ensures the answeredQuestions state is used (to avoid TypeScript warnings)
  // while also providing a debugging mechanism to track form completion status
  useEffect(() => {
    // Using the answeredQuestions variable to satisfy TypeScript "never read" warning
    // This is important for tracking form state even if not directly displayed
    console.debug(`Form completion status: ${answeredQuestions.size} questions answered`);
  }, [answeredQuestions]);

  // Get current user data with proper loading state
  const { data: currentUser, isLoading: isLoadingUser, error: userError } = useCurrentUserQuery();
  const createSubmissionMutation = useCreateSubmission();
  // Get the selected patient data from the store
  const { selectedPatient } = useSelectedPatientStore();
  const patientUuid = selectedPatient?.uuid;
  // Add a state to track previously answered questions
  const [previouslyAnsweredQuestions, setPreviouslyAnsweredQuestions] = useState<Set<string>>(new Set());

  // Check if the current user is a patient or organization
  const isPatientUser = currentUser?.user_type === 'patient';


  // Log user data for debugging
  useEffect(() => {
    console.log("Current user data:", currentUser);
    console.log("User type:", currentUser?.user_type);
    console.log("Is patient user:", isPatientUser);
    if (userError) {
      console.error("Error fetching user data:", userError);
    }
  }, [currentUser, userError, isPatientUser]);

  const activeSection = formData.sections[activeSectionIndex];

  // Store the original answered-by information for each question
  const [originalAnsweredBy, setOriginalAnsweredBy] = useState<Record<string, Record<string, unknown>>>({});

  // Define types for the API response format
  interface AttachmentInfo {
    uuid: string;
    file: string;
    file_type: string;
    [key: string]: unknown;
  }

  interface FormSubmissionResponse {
    id?: number | string;
    attachments?: AttachmentInfo[];
    [key: string]: unknown;
  }

  // Define a type for file attachment answers
  interface FileAttachmentAnswer {
    newFiles: FileList | null;
    existingFiles: Array<{id: string; url: string; name: string; type: string}>;
  }

  // Define a type for existing file info
  interface ExistingFileInfo {
    id: string;
    url: string;
    name: string;
    type: string;
  }

  // Enhanced helper function to extract attachment UUIDs from form submission response
  // in the specific format shown in your example

  // Helper function to extract file UUIDs from a form submission response
  const extractFileUuidsFromResponse = (response: unknown): string[] => {
    const uuids: string[] = [];

    // Type guard for FormSubmissionResponse
    const isFormSubmissionResponse = (obj: unknown): obj is FormSubmissionResponse =>
      typeof obj === 'object' && obj !== null;

    // Type guard for AttachmentInfo
    const isAttachmentInfo = (obj: unknown): obj is AttachmentInfo =>
      typeof obj === 'object' && obj !== null && 'uuid' in obj &&
      typeof (obj as Record<string, unknown>).uuid === 'string';

    if (!isFormSubmissionResponse(response)) {
      return uuids;
    }

    // Check if we have a direct attachments array
    if (response.attachments && Array.isArray(response.attachments)) {
      response.attachments.forEach(attachment => {
        if (isAttachmentInfo(attachment)) {
          uuids.push(attachment.uuid);
        }
      });
    }

    // Also check for nested attachments in the format shown in the example
    // This handles the case where response is an array with the first item containing attachments
    if (Array.isArray(response) && response.length > 0 && isFormSubmissionResponse(response[0])) {
      const firstItem = response[0];
      if (firstItem.attachments && Array.isArray(firstItem.attachments)) {
        firstItem.attachments.forEach(attachment => {
          if (isAttachmentInfo(attachment)) {
            uuids.push(attachment.uuid);
          }
        });
      }
    }

    // CRITICAL FIX: Extract UUIDs from user-answer-metadata in the form submission data
    if (typeof response === 'object' && response !== null && 'sections' in response) {
      const sections = response.sections as unknown;
      if (Array.isArray(sections)) {
        sections.forEach(section => {
          if (section && typeof section === 'object' && 'questions' in section) {
            const questions = section.questions as unknown;
            if (Array.isArray(questions)) {
              questions.forEach(question => {
                if (question && typeof question === 'object' &&
                    'type' in question && question.type === 'attach-file' &&
                    'user-answer-metadata' in question) {

                  const metadata = question['user-answer-metadata'] as unknown;
                  if (Array.isArray(metadata)) {
                    metadata.forEach(meta => {
                      if (meta && typeof meta === 'object' && 'file_uuid' in meta) {
                        const uuid = String(meta.file_uuid);
                        if (uuid && !uuids.includes(uuid)) {
                          uuids.push(uuid);
                          console.log(`Extracted UUID ${uuid} from user-answer-metadata file_uuid`);
                        }
                      }
                    });
                  }
                }
              });
            }
          }
        });
      }
    }

    return uuids;
  };

  // Load existing submission data if available
  useEffect(() => {
    if (!formData.existingSubmissionData || Object.keys(formData.existingSubmissionData).length === 0) {
      return; // Exit early if no submission data
    }

    console.log("Loading existing submission data:", formData.existingSubmissionData);

    try {
      // Check for sections in the existing submission data
      if (!formData.existingSubmissionData.sections ||
          !Array.isArray(formData.existingSubmissionData.sections)) {
        return;
      }

      const existingAnswers = {...formAnswers};
      const previouslyAnswered = new Set<string>();
      const answeredByInfo: Record<string, Record<string, unknown>> = {};
      const existingComments = { ...formComments };

      // Define types for the submission data structure
      interface ExistingSubmissionQuestion {
        id: number;
        'user-answer': unknown;
        'answered-by'?: Record<string, unknown>;
        type?: string;
        'user-answer-metadata'?: Array<{
          name: string;
          size: number;
          type: string;
          question_id?: number;
        }>;
        comment?: string;
        [key: string]: unknown;
      }

      interface ExistingSubmissionSection {
        id: number;
        questions: ExistingSubmissionQuestion[];
        [key: string]: unknown;
      }

      // NEW: Process top-level attachments first if they exist
      interface AttachmentInfo {
        uuid: string;
        file: string;
        file_type: string;
        questionId?: number;
        [key: string]: unknown;
      }

      // Map to store attachments by question ID
      const attachmentsByQuestionId: Record<number, ExistingFileInfo[]> = {};

      // Check if we have top-level attachments in the response
      if (formData.existingSubmissionData.attachments &&
          Array.isArray(formData.existingSubmissionData.attachments) &&
          formData.existingSubmissionData.attachments.length > 0) {

        console.log("Found top-level attachments:", formData.existingSubmissionData.attachments);

        // Find user-answer-metadata to determine which attachments belong to which questions
        // Include question_id in the metadata type definition
        const questionMetadata: Record<number, {name: string, type: string, question_id?: number}[]> = {};

        // First pass: collect metadata
        formData.existingSubmissionData.sections.forEach((section: ExistingSubmissionSection) => {
          if (section.id && Array.isArray(section.questions)) {
            section.questions.forEach((question: ExistingSubmissionQuestion) => {
              if (question.id !== undefined && question.type === 'attach-file' &&
                  'user-answer-metadata' in question && Array.isArray(question['user-answer-metadata'])) {
                // Store metadata with a direct link to the question ID
                questionMetadata[question.id] = question['user-answer-metadata'].map(meta => {
                  // Add question_id attribute to each metadata entry if it doesn't exist
                  if (typeof meta === 'object' && meta !== null && !('question_id' in meta)) {
                    return { ...meta, question_id: question.id };
                  }
                  return meta;
                });

                // IMPORTANT: For each attach-file question, directly create file info objects from the metadata and API
                // This ensures we don't lose files during form edits
                if (!attachmentsByQuestionId[question.id]) {
                  attachmentsByQuestionId[question.id] = [];
                }

                // If the API response includes attachments, match them with this question's metadata
                if (formData.existingSubmissionData?.attachments &&
                    Array.isArray(formData.existingSubmissionData.attachments)) {

                  // For each piece of metadata in this question
                  question['user-answer-metadata'].forEach(meta => {
                    if (meta && typeof meta === 'object') {
                      // Try to find a matching attachment in the API response
                      if (formData.existingSubmissionData?.attachments && Array.isArray(formData.existingSubmissionData.attachments)) {
                        formData.existingSubmissionData.attachments.forEach((attachment: unknown) => {
                        if (attachment && typeof attachment === 'object' && 'uuid' in attachment && 'file' in attachment) {
                          // Safely access properties of unknown type
                          const fileValue = attachment.file as unknown;
                          const fileName = typeof fileValue === 'string' ? fileValue.split('/').pop() || '' :
                                          fileValue?.toString().split('/').pop() || '';
                          const metaName = meta.name?.toString() || '';

                          // Check if this attachment matches the metadata by name or question_id
                          const matchesByName = fileName === metaName ||
                                              fileName.includes(metaName) ||
                                              metaName.includes(fileName);
                          const matchesByQuestionId = meta.question_id !== undefined &&
                                                    meta.question_id === question.id;

                          if (matchesByName || matchesByQuestionId) {
                            // Create a file info object for this attachment
                            const fileInfo = {
                              id: (attachment.uuid as unknown)?.toString() || '',
                              url: (attachment.file as unknown)?.toString() || '',
                              name: fileName,
                              type: ((attachment as Record<string, unknown>).file_type ?
                                    ((attachment as Record<string, unknown>).file_type as unknown)?.toString() || '' :
                                    (fileName.match(/\.(jpg|jpeg|png|gif)$/i) ? 'image/' + fileName.split('.').pop() :
                                    fileName.match(/\.(pdf)$/i) ? 'application/pdf' :
                                    fileName.match(/\.(doc|docx)$/i) ? 'application/msword' :
                                    'application/octet-stream'))
                            };

                            // Add this file to the question's attachments if not already there
                            const isAlreadyAdded = attachmentsByQuestionId[question.id].some(f => f.id === fileInfo.id);
                            if (!isAlreadyAdded) {
                              attachmentsByQuestionId[question.id].push(fileInfo);
                              console.log(`Directly matched attachment ${fileInfo.name} to question ${question.id} from API data`);
                            }
                          }
                        }
                      });
                      }
                    }
                  });
                }
              }
            });
          }
        });

        // Process all attachments
      // Create a direct lookup map from API attachments to question IDs
      const attachmentMap: Record<string, {
        uuid: string;
        file: string;
        file_type: string;
        questionId?: number;
      }> = {};

      // First catalog all attachments by their UUID for easy lookup
      if (formData.existingSubmissionData.attachments &&
          Array.isArray(formData.existingSubmissionData.attachments)) {
        formData.existingSubmissionData.attachments.forEach((attachment: AttachmentInfo) => {
          if (attachment.uuid) {
            attachmentMap[attachment.uuid] = attachment;
          }
        });
      }

      // Directly process attachments from the API response sections and questions
      formData.existingSubmissionData.sections.forEach((section: ExistingSubmissionSection) => {
        if (section.id && Array.isArray(section.questions)) {
          section.questions.forEach((question: ExistingSubmissionQuestion) => {
            if (question.id !== undefined && question.type === 'attach-file') {
              // Initialize the question's attachments array if needed
              if (!attachmentsByQuestionId[question.id]) {
                attachmentsByQuestionId[question.id] = [];
              }

              // If the question has metadata, use it to find matching attachments
              if ('user-answer-metadata' in question && Array.isArray(question['user-answer-metadata'])) {
                question['user-answer-metadata'].forEach(meta => {
                  // Look through all attachments to find a matching one
                  if (formData.existingSubmissionData?.attachments && Array.isArray(formData.existingSubmissionData.attachments)) {
                    formData.existingSubmissionData.attachments.forEach((attachment: AttachmentInfo) => {
                    const fileInfo = {
                      id: attachment.uuid,
                      url: attachment.file,
                      name: attachment.file.split('/').pop() || 'file',
                      type: attachment.file_type ||
                          (attachment.file.match(/\.(jpg|jpeg|png|gif)$/i) ? 'image/' + attachment.file.split('.').pop() :
                            attachment.file.match(/\.(pdf)$/i) ? 'application/pdf' :
                            attachment.file.match(/\.(doc|docx)$/i) ? 'application/msword' :
                            'application/octet-stream')
                    };

                    // Check if this file name matches the metadata
                    const fileName = fileInfo.name;
                    const metaName = meta.name;

                    // Try different matching approaches (exact match, partial match, etc.)
                    if (fileName === metaName ||
                        fileName.includes(metaName) ||
                        metaName.includes(fileName) ||
                        // If metadata has question_id, use that as the definitive matcher
                        (meta.question_id !== undefined && meta.question_id === question.id)) {

                      // Add this file to the question's attachments if not already there
                      const isAlreadyAdded = attachmentsByQuestionId[question.id].some(f => f.id === fileInfo.id);
                      if (!isAlreadyAdded) {
                        attachmentsByQuestionId[question.id].push(fileInfo);
                        console.log(`Matched attachment ${fileInfo.name} to question ${question.id} using metadata`);
                      }
                    }
                  });
                  }
                });
              }
            }
          });
        }
      });

        console.log("Processed attachments by question ID:", attachmentsByQuestionId);
      }

      // Extract answers from the submission data
      formData.existingSubmissionData.sections.forEach((section: ExistingSubmissionSection) => {
        if (section.id && Array.isArray(section.questions)) {
          // Initialize the section if it doesn't exist
          if (!existingAnswers[section.id]) {
            existingAnswers[section.id] = {};
          }

          if (!answeredByInfo[section.id]) {
            answeredByInfo[section.id] = {};
          }
          if (!existingComments[section.id]) {
            existingComments[section.id] = {};
          }

          // Process each question's user answer
          section.questions.forEach((question: ExistingSubmissionQuestion) => {
            if (question.id !== undefined && 'user-answer' in question) {
              const answer = question['user-answer'];
              const questionKey = `${section.id}-${question.id}`;
              const questionType = question.type;

              console.log(`Processing question ${question.id} of type ${questionType} with answer:`, answer);

              // Process the answer based on question type
              let processedAnswer = answer;

              // NEW: Special handling for file attachments
              if (questionType === 'attach-file') {
                // If there are existing attachments for this question from top-level attachments array
                if (attachmentsByQuestionId[question.id] && attachmentsByQuestionId[question.id].length > 0) {
                  // Ensure we use the attachment references from our processing
                  processedAnswer = {
                    newFiles: null,
                    existingFiles: attachmentsByQuestionId[question.id]
                  };
                  console.log(`Assigned ${attachmentsByQuestionId[question.id].length} attachments to question ${question.id}:`, attachmentsByQuestionId[question.id]);
                }
                // If the answer already has existingFiles, make sure we preserve them
                else if (answer && typeof answer === 'object' && 'existingFiles' in answer) {
                  // Cast to FileAttachmentAnswer to access properties safely
                  const fileAnswer = answer as FileAttachmentAnswer;
                  processedAnswer = {
                    newFiles: fileAnswer.newFiles || null,
                    existingFiles: fileAnswer.existingFiles || []
                  };
                }
                // Check if there are metadata entries for this question but no attachments were found
                else if (question['user-answer-metadata'] && Array.isArray(question['user-answer-metadata']) &&
                        formData.existingSubmissionData?.attachments && Array.isArray(formData.existingSubmissionData.attachments)) {

                  // Search for matching attachments in the top-level attachments array using metadata
                  const foundAttachments: ExistingFileInfo[] = [];

                  question['user-answer-metadata'].forEach(meta => {
                    if (meta && typeof meta === 'object') {
                      const metaName = meta.name?.toString() || '';
                      const metaQuestionId = meta.question_id;

                      // Check if this metadata entry corresponds to this question
                      if (metaQuestionId === question.id || !metaQuestionId) {
                        // Look for a matching attachment in the API response
                        if (formData.existingSubmissionData?.attachments && Array.isArray(formData.existingSubmissionData.attachments)) {
                          formData.existingSubmissionData.attachments.forEach((attachment: unknown) => {
                          if (attachment && typeof attachment === 'object' && 'uuid' in attachment && 'file' in attachment) {
                            // Safely access properties of unknown type
                            const fileValue = attachment.file as unknown;
                            const fileName = typeof fileValue === 'string' ? fileValue.split('/').pop() || '' :
                                            fileValue?.toString().split('/').pop() || '';

                            // Match by name or question_id
                            if (fileName === metaName || fileName.includes(metaName) || metaName.includes(fileName)) {
                              foundAttachments.push({
                                id: (attachment.uuid as unknown)?.toString() || '',
                                url: (attachment.file as unknown)?.toString() || '',
                                name: fileName,
                                type: ((attachment as Record<string, unknown>).file_type ?
                                      ((attachment as Record<string, unknown>).file_type as unknown)?.toString() || '' : '') ||
                                      (fileName.match(/\.(jpg|jpeg|png|gif)$/i) ? 'image/' + fileName.split('.').pop() :
                                       fileName.match(/\.(pdf)$/i) ? 'application/pdf' :
                                       fileName.match(/\.(doc|docx)$/i) ? 'application/msword' :
                                       'application/octet-stream')
                              });
                              console.log(`Direct metadata match: Found attachment ${fileName} for question ${question.id} using metadata`);
                            }
                          }
                        });
                        }
                      }
                    }
                  });

                  if (foundAttachments.length > 0) {
                    processedAnswer = {
                      newFiles: null,
                      existingFiles: foundAttachments
                    };
                    console.log(`Directly assigned ${foundAttachments.length} attachments to question ${question.id} from metadata match`);
                  } else {
                    processedAnswer = {
                      newFiles: null,
                      existingFiles: []
                    };
                  }
                }
                // Otherwise, create an empty file attachment structure
                else {
                  processedAnswer = {
                    newFiles: null,
                    existingFiles: []
                  };
                }
              }
              // For multiple-choice questions, ensure we have an array of strings
              else if (questionType === 'multiple-choice') {
                if (Array.isArray(answer)) {
                  // Convert all items to strings for consistent comparison
                  processedAnswer = answer.map(item => String(item));
                } else if (typeof answer === 'string' && answer) {
                  processedAnswer = [answer];
                } else if (typeof answer === 'number') {
                  processedAnswer = [String(answer)];
                }
                console.log(`Processed multiple-choice answer:`, processedAnswer);
              }
              // For multiple-text-boxes, ensure we have an array of strings
              else if (questionType === 'multiple-text-boxes') {
                if (Array.isArray(answer)) {
                  // Convert all items to strings for consistent comparison
                  processedAnswer = answer.map(item =>
                    item === null || item === undefined ? '' : String(item)
                  );
                } else if (typeof answer === 'string' && answer) {
                  // If it's a single string, put it in the first position
                  const optionsLength = Array.isArray(question.options) ? question.options.length : 1;
                  const emptyArray = Array(optionsLength).fill('');
                  emptyArray[0] = answer;
                  processedAnswer = emptyArray;
                }
                console.log(`Processed multiple-text-boxes answer:`, processedAnswer);
              }
              // For text fields, ensure we have a string
              else if (questionType === 'short-text' || questionType === 'long-text') {
                if (typeof answer === 'string') {
                  processedAnswer = answer;
                } else if (answer !== null && answer !== undefined) {
                  processedAnswer = String(answer);
                }
                console.log(`Processed text answer:`, processedAnswer);
              }

              // Store the processed answer
              existingAnswers[section.id][question.id] = processedAnswer;

              // Store the original answered-by information if available
              if (question['answered-by']) {
                answeredByInfo[section.id][question.id] = question['answered-by'];
              }

              // Store the comment if it exists
              if (typeof question.comment === 'string') {
                existingComments[section.id][question.id] = question.comment;
              } else {
                existingComments[section.id][question.id] = "";
              }

              // Mark as previously answered if it has a non-empty value
              if (answer !== "" && answer !== null && answer !== undefined) {
                if (Array.isArray(answer)) {
                  if (answer.length > 0 && answer.some((ans: string | number | unknown) => ans !== "")) {
                    previouslyAnswered.add(questionKey);
                  }
                } else if (typeof answer === 'object' && answer !== null) {
                  // Handle object types like signature, table, etc.
                  const hasValue = Object.values(answer).some(val =>
                    val !== "" && val !== null && val !== undefined &&
                    (Array.isArray(val) ? val.length > 0 : true)
                  );
                  if (hasValue) {
                    previouslyAnswered.add(questionKey);
                  }
                } else {
                  previouslyAnswered.add(questionKey);
                }
              }
            }
          });
        }
      });

      console.log("Parsed existing answers:", existingAnswers);
      console.log("Previously answered questions:", previouslyAnswered);
      console.log("Original answered-by info:", answeredByInfo);
      console.log("Loaded existing comments:", existingComments);

      setFormAnswers(existingAnswers);
      setPreviouslyAnsweredQuestions(previouslyAnswered);
      setOriginalAnsweredBy(answeredByInfo);
      setFormComments(existingComments);

      // Also update the answered questions to include previously answered ones
      setAnsweredQuestions(prev => {
        const newSet = new Set(prev);
        previouslyAnswered.forEach(id => newSet.add(id));
        return newSet;
      });

      // Expand comment input for all questions that have a comment
      setExpandedComments(() => {
        const initial = new Set<string>();
        Object.entries(existingComments).forEach(([sectionId, questions]) => {
          Object.entries(questions).forEach(([questionId, comment]) => {
            if (comment && comment.trim() !== "") {
              initial.add(`${sectionId}-${questionId}`);
            }
          });
        });
        return initial;
      });
    } catch (error) {
      console.error("Error parsing existing submission data:", error);
    }
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [formData.existingSubmissionData]); // formAnswers is intentionally excluded to avoid infinite loops

  const goToNext = () => {
    if (activeSectionIndex < formData.sections.length - 1) {
      setActiveSectionIndex(activeSectionIndex + 1);
    }
  };
  const goToPrevious = () => {
    if (activeSectionIndex > 0) {
      setActiveSectionIndex(activeSectionIndex - 1);
    }
  };
  // Track which questions have been modified by the current user
  const [modifiedQuestions, setModifiedQuestions] = useState<Set<string>>(new Set());

  // Enhanced handleAnswerChange function to track modifications and update previously answered questions
  const handleAnswerChange = (
    sectionId: number,
    questionId: number,
    value: unknown
  ) => {
    console.log(`Changing answer for question ${questionId} in section ${sectionId}:`, value);

    const questionKey = `${sectionId}-${questionId}`;

    // Mark this question as modified by the current user
    setModifiedQuestions(prev => {
      const newSet = new Set(prev);
      newSet.add(questionKey);
      return newSet;
    });

    setFormAnswers((prev) => {
      const sectionAnswers = prev[sectionId] || {};

      // Handle file attachments
      if (value instanceof FileList) {
        const question = formData.sections
          .find(s => s.id === sectionId)
          ?.questions.find(q => q.id === questionId);

        if (question?.type === 'attach-file') {
          const files = Array.from(value);

          // Categorize files based on type
          const imageFiles = files.filter(file => file.type.startsWith('image/'))
            .map(file => ({ file, questionId }));
          const videoFiles = files.filter(file => file.type.startsWith('video/'))
            .map(file => ({ file, questionId }));
          const documentFiles = files.filter(file =>
            file.type === 'application/pdf' ||
            file.type === 'application/msword' ||
            file.type === 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
          ).map(file => ({ file, questionId }));

          // Update file attachments state - preserve existing files while adding new ones
          setFileAttachments(prev => {
            // First get the existing files for this question
            const existingImageAttachments = prev.image_attachments.filter(item => item.questionId === questionId);
            const existingVideoAttachments = prev.video_attachments.filter(item => item.questionId === questionId);
            const existingDocAttachments = prev.document_attachments.filter(item => item.questionId === questionId);

            // For single file questions, replace existing files
            // For multiple file questions, append new files to existing ones
            const finalImageAttachments = question.multiple
              ? [...existingImageAttachments, ...imageFiles]
              : imageFiles;

            const finalVideoAttachments = question.multiple
              ? [...existingVideoAttachments, ...videoFiles]
              : videoFiles;

            const finalDocAttachments = question.multiple
              ? [...existingDocAttachments, ...documentFiles]
              : documentFiles;

            console.log(`Question ${questionId} - Updated file attachments:`, {
              images: finalImageAttachments.length,
              videos: finalVideoAttachments.length,
              docs: finalDocAttachments.length
            });

            return {
              image_attachments: [
                ...prev.image_attachments.filter(item => item.questionId !== questionId),
                ...finalImageAttachments
              ],
              video_attachments: [
                ...prev.video_attachments.filter(item => item.questionId !== questionId),
                ...finalVideoAttachments
              ],
              document_attachments: [
                ...prev.document_attachments.filter(item => item.questionId !== questionId),
                ...finalDocAttachments
              ]
            };
          });
        }
      }

      // Special handling for file attachments to preserve existing files
      if (value && typeof value === 'object' &&
          !('length' in value) && // Not an array
          'newFiles' in value && 'existingFiles' in value) {
        const fileValue = value as { newFiles: FileList | null; existingFiles: Array<{id: string; url: string; name: string; type: string}> };

        // Always preserve existing files from the current answer if they exist
        const currentAnswer = sectionAnswers[questionId];
        const currentExistingFiles =
          currentAnswer && typeof currentAnswer === 'object' && 'existingFiles' in currentAnswer
            ? (currentAnswer as { existingFiles: Array<{id: string; url: string; name: string; type: string}> }).existingFiles
            : [];

        // Merge existing files from current answer with new value
        // IMPORTANT: This is critical for preserving files in edit mode
        const mergedExistingFiles = [
          ...currentExistingFiles,
          ...(fileValue.existingFiles || [])
        ].filter((file, index, self) =>
          // Ensure we don't have duplicate files by ID
          index === self.findIndex(f => f.id === file.id)
        );

        console.log(`Question ${questionId} - Merging existing files:`, {
          currentFiles: currentExistingFiles.length,
          newFiles: fileValue.existingFiles?.length || 0,
          merged: mergedExistingFiles.length
        });

        // Update answered questions set based on value
        const hasValue = mergedExistingFiles.length > 0 || (fileValue.newFiles && fileValue.newFiles.length > 0);

        if (hasValue) {
          // Add to answered questions
          setAnsweredQuestions(prev => {
            const newSet = new Set(prev);
            newSet.add(questionKey);
            return newSet;
          });

          // If in the previouslyAnsweredQuestions set, remove the lock
          if (previouslyAnsweredQuestions.has(questionKey)) {
            setPreviouslyAnsweredQuestions(prev => {
              const updated = new Set(prev);
              updated.delete(questionKey);
              return updated;
            });
          }
        } else {
          // If removing all value, also remove from answered questions
          setAnsweredQuestions(prev => {
            const newSet = new Set(prev);
            newSet.delete(questionKey);
            return newSet;
          });
        }

        return {
          ...prev,
          [sectionId]: {
            ...sectionAnswers,
            [questionId]: {
              newFiles: fileValue.newFiles,
              existingFiles: mergedExistingFiles
            }
          }
        };
      }

      // Update answered questions set based on value
      const hasValue = (() => {
        if (value === undefined || value === null || value === "") return false;
        if (Array.isArray(value)) return value.length > 0 && value.some(v => v !== "");
        if (typeof value === 'object') {
          // For tables
          if (value && 'data' in value) {
            const dataArray = value.data as Array<Array<string>>;
            return dataArray.some(row => row.some(cell => cell !== ""));
          }
          // For signatures and other objects
          return Object.values(value).some(val =>
            val !== "" && val !== null && val !== undefined &&
            (Array.isArray(val) ? val.length > 0 : true)
          );
        }
        return true;
      })();

      if (hasValue) {
        // Add to answered questions
        setAnsweredQuestions(prev => {
          const newSet = new Set(prev);
          newSet.add(questionKey);
          return newSet;
        });

        // If in the previouslyAnsweredQuestions set, remove the lock
        if (previouslyAnsweredQuestions.has(questionKey)) {
          setPreviouslyAnsweredQuestions(prev => {
            const updated = new Set(prev);
            updated.delete(questionKey);
            return updated;
          });
        }
      } else {
        // If removing all value, also remove from answered questions
        setAnsweredQuestions(prev => {
          const newSet = new Set(prev);
          newSet.delete(questionKey);
          return newSet;
        });
      }

      // Return updated form answers
      return {
        ...prev,
        [sectionId]: {
          ...sectionAnswers,
          [questionId]: value,
        },
      };
    });
  };

  const [fileAttachments, setFileAttachments] = useState<{
    image_attachments: {file: File, questionId: number}[];
    video_attachments: {file: File, questionId: number}[];
    document_attachments: {file: File, questionId: number}[];
  }>({
    image_attachments: [],
    video_attachments: [],
    document_attachments: []
  });

  // Add comment handling functions
  const handleCommentChange = (sectionId: number, questionId: number, comment: string) => {
    setFormComments(prev => ({
      ...prev,
      [sectionId]: {
        ...prev[sectionId],
        [questionId]: comment
      }
    }));
  };

  const toggleCommentExpanded = (sectionId: number, questionId: number) => {
    const commentKey = `${sectionId}-${questionId}`;
    setExpandedComments(prev => {
      const newSet = new Set(prev);
      if (newSet.has(commentKey)) {
        newSet.delete(commentKey);
      } else {
        newSet.add(commentKey);
      }
      return newSet;
    });
  };

  // Add this state to RebuildForm component:
  // Track which comment histories are expanded
  const [expandedCommentHistories, setExpandedCommentHistories] = useState<{ [key: string]: boolean }>({});

  // Add this function to toggle history
  const toggleCommentHistory = (sectionId: number, questionId: number) => {
    const key = `${sectionId}-${questionId}`;
    setExpandedCommentHistories(prev => ({
      ...prev,
      [key]: !prev[key]
    }));
  };

  const handleSubmit = () => {
    console.log("Submit button clicked");
    // Validate required fields
    const allRequiredFieldsValid = validateRequiredFields();

    // Check if the form UUID is available
    if (!formUuid) {
      console.error("Form UUID is missing");
      setErrorMessage("Missing form UUID. Please ensure you're viewing a specific form.");
      setSubmissionStatus('error');
      return;
    }

    // Handle loading state for user data
    if (isLoadingUser) {
      console.log("User data is still loading, setting status to loading");
      setSubmissionStatus('loading');
      setErrorMessage("Waiting for user information...");
      return;
    }

    // Check if we have user information
    if (!currentUser) {
      console.error("User information is missing");
      console.log("Current user data:", currentUser);
      setErrorMessage("Unable to submit form: User information not available. Please ensure you're logged in.");
      setSubmissionStatus('error');
      return;
    }

    // Everything looks good, proceed with submission
    setSubmissionStatus('loading');
    setErrorMessage(null);

    // Log the current state before submission
    console.log("Form submission details:");
    console.log("- Form UUID:", formUuid);
    console.log("- Current user:", currentUser);
    console.log("- Patient UUID:", patientUuid);
    console.log("- All required fields valid:", allRequiredFieldsValid);

    const formattedAnswers: {
      [sectionId: number]: {
        [questionId: number]: {
          questionId: number;
          questionName?: string;
          questionType: QuestionType | null;
          answer: unknown;
          comment?: string;
        }
      }
    } = {};

    // Format the form answers data with comments
    formData.sections.forEach((section) => {
      formattedAnswers[section.id] = {};
      section.questions.forEach((question) => {
        const answer = formAnswers[section.id]?.[question.id];
        const comment = formComments[section.id]?.[question.id];
        formattedAnswers[section.id][question.id] = {
          questionId: question.id,
          questionName: question.questionName,
          questionType: question.type,
          answer: answer,
          comment: comment || undefined
        };
      });
    });

    const submissionData = {
      formDetails: formData.formDetails,
      sections: formData.sections.map((section) => ({
        id: section.id,
        name: section.name,
        description: section.description,
        questions: section.questions.map((question) => {
          const questionKey = `${section.id}-${question.id}`;
          const wasModified = modifiedQuestions.has(questionKey);
          const originalAnsweredByInfo = originalAnsweredBy[section.id]?.[question.id];

          // Get the current answer for this question
          const currentAnswer = formAnswers[section.id]?.[question.id] || "";

          // CRITICAL FIX: For attach-file questions, we need to include user-answer-metadata
          // This is essential for preserving file attachments when updating other questions
          let userAnswerMetadata: Array<{name: string; size: number; type: string; question_id?: number}> | undefined = undefined;

          // Define the type for file objects in existingFiles
          interface ExistingFileInfo {
            id: string;
            name: string;
            url: string;
            type: string;
          }

          if (question.type === 'attach-file' &&
              currentAnswer &&
              typeof currentAnswer === 'object' &&
              'existingFiles' in currentAnswer) {

            // Extract metadata from existing files
            const existingFiles = currentAnswer.existingFiles as ExistingFileInfo[];

            if (Array.isArray(existingFiles) && existingFiles.length > 0) {
              // Create metadata entries for each file
              userAnswerMetadata = existingFiles.map((file: ExistingFileInfo) => ({
                name: file.name,
                size: 0, // We don't have the size, but it's not critical
                type: file.type || 'application/octet-stream',
                question_id: question.id, // Add question_id to help with matching
                file_uuid: file.id // IMPORTANT: Include the file UUID to ensure proper matching
              }));

              console.log(`Created user-answer-metadata for question ${question.id} with ${userAnswerMetadata.length} files:`,
                userAnswerMetadata.map(m => m.name));
            }
          }

          // Also preserve existing metadata if it exists in the original submission
          let originalMetadata: Array<{name: string; size: number; type: string; question_id?: number}> | undefined = undefined;

          // Safely access the existing submission data
          if (formData.existingSubmissionData &&
              typeof formData.existingSubmissionData === 'object' &&
              'sections' in formData.existingSubmissionData &&
              Array.isArray(formData.existingSubmissionData.sections)) {

            // Find the matching section
            const matchingSection = formData.existingSubmissionData.sections.find(
              (s: {id: number; questions?: unknown[]}) => s.id === section.id
            );

            // Find the matching question
            if (matchingSection &&
                'questions' in matchingSection &&
                Array.isArray(matchingSection.questions)) {

              const matchingQuestion = matchingSection.questions.find(
                (q: {id: number; type?: string; 'user-answer-metadata'?: unknown[]}) => q.id === question.id
              );

              // Get the metadata if it exists
              if (matchingQuestion &&
                  'user-answer-metadata' in matchingQuestion &&
                  Array.isArray(matchingQuestion['user-answer-metadata'])) {

                originalMetadata = matchingQuestion['user-answer-metadata'] as Array<{name: string; size: number; type: string; question_id?: number}>;
              }
            }
          }

          if (originalMetadata && Array.isArray(originalMetadata) &&
              (!userAnswerMetadata || userAnswerMetadata.length === 0)) {
            userAnswerMetadata = originalMetadata as Array<{name: string; size: number; type: string; question_id?: number}>;
            console.log(`Preserved original user-answer-metadata for question ${question.id}:`,
              originalMetadata.map((m: {name: string}) => m.name));
          }

          // IMPORTANT: For attach-file questions, we need to ensure we have metadata
          // If we don't have metadata from the current form state or the original submission,
          // but we do have existing files, create metadata from the file information
          if (question.type === 'attach-file' &&
              (!userAnswerMetadata || userAnswerMetadata.length === 0) &&
              currentAnswer &&
              typeof currentAnswer === 'object' &&
              'existingFiles' in currentAnswer) {

            const existingFiles = currentAnswer.existingFiles as ExistingFileInfo[];

            if (Array.isArray(existingFiles) && existingFiles.length > 0) {
              // Create basic metadata from file names
              userAnswerMetadata = existingFiles.map((file: ExistingFileInfo) => ({
                name: file.name,
                size: 0,
                type: file.type || 'application/octet-stream',
                question_id: question.id,
                file_uuid: file.id // IMPORTANT: Include the file UUID to ensure proper matching
              }));

              console.log(`Created fallback metadata for question ${question.id} with ${existingFiles.length} files`);
            }
          }

          // CRITICAL FIX: For file attachment questions that haven't been modified, preserve the original question data
          if (question.type === 'attach-file' && !wasModified && formData.existingSubmissionData) {
            // Try to find the original question data in the existing submission
            let originalQuestionData = null;

            // Find the section and question in the original submission data
            if (formData.existingSubmissionData.sections &&
                Array.isArray(formData.existingSubmissionData.sections)) {
              const sections = formData.existingSubmissionData.sections as Array<{
                id: number;
                questions?: Array<Record<string, unknown>>;
              }>;

              // Look through all sections for this question
              for (const s of sections) {
                if (s.id === section.id && s.questions && Array.isArray(s.questions)) {
                  const q = s.questions.find(q => q.id === question.id);
                  if (q) {
                    originalQuestionData = q;
                    console.log(`Found original question data for file attachment question ${question.id}:`, originalQuestionData);
                    break;
                  }
                }
              }
            }

            // If we found the original question data, use it
            if (originalQuestionData) {
              console.log(`Using original question data for unmodified file attachment question ${question.id}`);
              return {
                ...question,
                ...originalQuestionData,
                // Make sure to keep the original ID
                id: question.id
              };
            }
          }

          // For all other questions or modified file attachment questions, create a new question object
          // --- COMMENT HISTORY LOGIC ---
          let commentHistory: Array<{comment: string, "commented-by"?: CommentHistoryItem["commented-by"], "commented-at"?: string}> = [];
          let previousComment = undefined;
          let previousCommentedBy = undefined;
          let previousCommentedAt = undefined;

          // Try to get previous comment info from existingSubmissionData
          if (formData.existingSubmissionData && Array.isArray(formData.existingSubmissionData.sections)) {
            const sectionData = formData.existingSubmissionData.sections.find((s: ExistingSubmissionSection) => s.id === section.id);
            if (sectionData && Array.isArray(sectionData.questions)) {
              const questionData = sectionData.questions.find((q: ExistingSubmissionQuestion) => q.id === question.id);
              if (questionData) {
                previousComment = questionData.comment;
                previousCommentedBy = questionData["commented-by"] || questionData.commentedBy;
                previousCommentedAt = questionData["commented-at"] || questionData.commentedAt;
                // If there is already a comment_history, preserve it
                if (Array.isArray(questionData.comment_history)) {
                  commentHistory = [...questionData.comment_history];
                }
              }
            }
          }

          // If the comment changed, push the old comment into history
          const currentComment = formComments[section.id]?.[question.id] || "";
          if (
            previousComment &&
            previousComment.trim() !== "" &&
            previousComment !== currentComment
          ) {
            commentHistory.push({
              comment: previousComment,
              "commented-by": previousCommentedBy,
              "commented-at": previousCommentedAt
            });
          }

          return {
            ...question,
            "user-answer": currentAnswer,
            ...(question.type === 'attach-file' ? {
              "user-answer-metadata": userAnswerMetadata || []
            } : {}),
            "answered-by": wasModified || !originalAnsweredByInfo
              ? {
                  identifier: currentUser.identifier || String(currentUser.id || ''),
                  first_name: currentUser.first_name || '',
                  last_name: currentUser.last_name || '',
                  email: currentUser.email || '',
                  user_type: currentUser.user_type || ''
                }
              : originalAnsweredByInfo,
            "comment": currentComment,
            ...(currentComment.trim()
              ? {
                  "commented-by": {
                    identifier: currentUser.identifier || String(currentUser.id || ''),
                    first_name: currentUser.first_name || '',
                    last_name: currentUser.last_name || '',
                    email: currentUser.email || '',
                    user_type: currentUser.user_type || ''
                  },
                  "commented-at": new Date().toISOString(),
                }
              : {}),
            ...(commentHistory.length > 0 ? { comment_history: commentHistory } : {}),
          };
        })
      }))
    };

    // Set is_completed based on whether all required fields are answered
    const isCompleted = allRequiredFieldsValid;

    // Process file attachments - extract actual files from the fileAttachments structure
    // AND preserve any existing attachments from the existing submission data
    const processedFileAttachments = {
      image_attachments: fileAttachments.image_attachments.map(item => item.file),
      video_attachments: fileAttachments.video_attachments.map(item => item.file),
      document_attachments: fileAttachments.document_attachments.map(item => item.file)
    };

    // IMPORTANT: Log all file attachments for debugging
    console.log("File attachments being sent to backend:", {
      image_count: processedFileAttachments.image_attachments.length,
      video_count: processedFileAttachments.video_attachments.length,
      document_count: processedFileAttachments.document_attachments.length
    });

    // Check if we're in edit mode
    const isEditMode = !!formData.existingSubmissionData?.id;
    console.log("Form is in edit mode:", isEditMode);

    if (isEditMode && formData.existingSubmissionData?.id) {
      console.log("Including submission ID to preserve attachments:", formData.existingSubmissionData.id);
    }

    // Enhanced collection of existing attachment UUIDs from questions with file attachments
    const existingAttachmentUuids: string[] = [];

    // First, collect all UUIDs from all attach-file questions in the current form state
    // This is critical for preserving files across submissions
    formData.sections.forEach(section => {
      section.questions.forEach(question => {
        if (question.type === 'attach-file') {
          // Get the answer for this question
          const answer = formAnswers[section.id]?.[question.id];

          // Check if this is an answered attach-file question
          const isAnswered = isQuestionAnswered(section.id, question.id);

          // Log whether this question is answered
          console.log(`Checking attach-file question ${question.id} in section ${section.id}: ${isAnswered ? 'ANSWERED' : 'not answered'}`);

          // Extract UUIDs from existingFiles in the answer
          if (answer && typeof answer === 'object' && 'existingFiles' in answer) {
            const existingFiles = answer.existingFiles;
            if (Array.isArray(existingFiles)) {
              existingFiles.forEach(file => {
                if (file && typeof file === 'object' && 'id' in file && file.id) {
                  // Make sure we don't have duplicates and the ID is valid
                  if (!existingAttachmentUuids.includes(file.id) && file.id !== 'undefined' && file.id !== 'null') {
                    existingAttachmentUuids.push(file.id);
                    console.log(`Added UUID ${file.id} from question ${question.id} in section ${section.id}`);
                  }
                }
              });
            }
          }

          // If there's original metadata with file_uuid, extract those UUIDs as well
          if (formData.existingSubmissionData &&
              formData.existingSubmissionData.sections &&
              Array.isArray(formData.existingSubmissionData.sections)) {

            // Find the matching section and question in existing submission data
            const originalSection = formData.existingSubmissionData.sections.find(
              (s: {id: number}) => s.id === section.id
            );

            if (originalSection && originalSection.questions && Array.isArray(originalSection.questions)) {
              const originalQuestion = originalSection.questions.find(
                (q: {id: number}) => q.id === question.id
              );

              if (originalQuestion &&
                  originalQuestion['user-answer-metadata'] &&
                  Array.isArray(originalQuestion['user-answer-metadata'])) {

                // Extract file_uuid from metadata if it exists
                interface MetadataItem {
                  name?: string;
                  size?: number;
                  type?: string;
                  question_id?: number;
                  file_uuid?: string;
                  [key: string]: unknown;
                }

                originalQuestion['user-answer-metadata'].forEach((meta: MetadataItem) => {
                  if (meta && meta.file_uuid && typeof meta.file_uuid === 'string') {
                    if (!existingAttachmentUuids.includes(meta.file_uuid)) {
                      existingAttachmentUuids.push(meta.file_uuid);
                      console.log(`Added UUID ${meta.file_uuid} from original metadata for question ${question.id}`);
                    }
                  }
                });
              }
            }
          }
        }
      });
    });

    // Then collect ALL attachment UUIDs from the original API response
    if (formData.existingSubmissionData &&
        typeof formData.existingSubmissionData === 'object' &&
        'attachments' in formData.existingSubmissionData &&
        Array.isArray(formData.existingSubmissionData.attachments)) {

      // Always include ALL existing attachment UUIDs from the API response
      formData.existingSubmissionData.attachments.forEach(attachment => {
        if (attachment && typeof attachment === 'object' && 'uuid' in attachment) {
          const uuid = String(attachment.uuid);
          if (uuid && !existingAttachmentUuids.includes(uuid)) {
            existingAttachmentUuids.push(uuid);
            console.log(`Added attachment UUID ${uuid} directly from API response`);
          }
        }
      });
    }

    // CRITICAL FIX: Extract UUIDs from user-answer-metadata in the form submission data
    if (formData.existingSubmissionData &&
        typeof formData.existingSubmissionData === 'object' &&
        'sections' in formData.existingSubmissionData &&
        Array.isArray(formData.existingSubmissionData.sections)) {

      // Define types for the section and question objects
      interface MetadataItem {
        name?: string;
        size?: number;
        type?: string;
        question_id?: number;
        file_uuid?: string;
        [key: string]: unknown;
      }

      interface ExistingQuestion {
        id?: number;
        type?: string;
        'user-answer-metadata'?: MetadataItem[];
        [key: string]: unknown;
      }

      interface ExistingSection {
        id?: number;
        questions?: ExistingQuestion[];
        [key: string]: unknown;
      }

      // Extract UUIDs from user-answer-metadata in each question
      formData.existingSubmissionData.sections.forEach((section: ExistingSection) => {
        if (section.questions && Array.isArray(section.questions)) {
          section.questions.forEach((question: ExistingQuestion) => {
            if (question.type === 'attach-file' &&
                question['user-answer-metadata'] &&
                Array.isArray(question['user-answer-metadata'])) {

              question['user-answer-metadata'].forEach((meta: MetadataItem) => {
                if (meta && typeof meta === 'object' && meta.file_uuid) {
                  const uuid = String(meta.file_uuid);
                  if (uuid && !existingAttachmentUuids.includes(uuid)) {
                    existingAttachmentUuids.push(uuid);
                    console.log(`Added UUID ${uuid} from user-answer-metadata file_uuid`);
                  }
                }
              });
            }
          });
        }
      });

      // Log all attachments for debugging
      if (formData.existingSubmissionData.attachments &&
          Array.isArray(formData.existingSubmissionData.attachments)) {
        console.log("All attachments from API response:",
          formData.existingSubmissionData.attachments.map((att: Record<string, unknown>) => ({
            uuid: att.uuid,
            file: typeof att.file === 'string' ? att.file.split('/').pop() : 'unknown',
            type: att.file_type
          }))
        );
      }
    }

    // For the specific format in your example with detailed attachments including uploaded_at and uploaded_by
    if (formData.existingSubmissionData &&
        typeof formData.existingSubmissionData === 'object') {
      // Check for the exact format of attachments array with uuid, file, file_type, uploaded_at, uploaded_by
      if ('attachments' in formData.existingSubmissionData &&
          Array.isArray(formData.existingSubmissionData.attachments)) {

        formData.existingSubmissionData.attachments.forEach((attachment: Record<string, unknown>) => {
          if ('uuid' in attachment &&
              'file' in attachment &&
              'file_type' in attachment &&
              'uploaded_at' in attachment &&
              'uploaded_by' in attachment) {

            const uuid = String(attachment.uuid);
            if (!existingAttachmentUuids.includes(uuid)) {
              existingAttachmentUuids.push(uuid);
              console.log(`Added UUID ${uuid} from full attachment metadata format`);
            }
          }
        });
      }
    }

    // Log the final list of attachment UUIDs
    console.log(`FINAL: Found ${existingAttachmentUuids.length} attachment UUIDs to preserve:`, existingAttachmentUuids);

    // Always extract file UUIDs from the top-level attachments array in the response
    // This ensures we preserve attachments even if the attach file question wasn't modified
    if (formData.existingSubmissionData) {
      // Direct extraction from attachments array if it exists
      if (formData.existingSubmissionData.attachments &&
          Array.isArray(formData.existingSubmissionData.attachments)) {
        formData.existingSubmissionData.attachments.forEach(attachment => {
          if (attachment && typeof attachment === 'object' && 'uuid' in attachment) {
            const uuid = attachment.uuid;
            if (typeof uuid === 'string' && !existingAttachmentUuids.includes(uuid)) {
              existingAttachmentUuids.push(uuid);
              console.log(`Added attachment UUID from top-level array: ${uuid}`);
            }
          }
        });
      }

      // Special case for the format in the example
      // Handle the case where attachments is an array with a single object containing uuid
      if (Array.isArray(formData.existingSubmissionData) && formData.existingSubmissionData.length > 0) {
        const firstItem = formData.existingSubmissionData[0];
        if (firstItem && typeof firstItem === 'object' && 'attachments' in firstItem &&
            Array.isArray(firstItem.attachments)) {
          firstItem.attachments.forEach((attachment: unknown) => {
            if (attachment && typeof attachment === 'object' && 'uuid' in attachment) {
              const uuid = attachment.uuid;
              if (typeof uuid === 'string' && !existingAttachmentUuids.includes(uuid)) {
                existingAttachmentUuids.push(uuid);
                console.log(`Added attachment UUID from nested array: ${uuid}`);
              }
            }
          });
        }
      }

      // Handle the exact format from the example
      if (typeof formData.existingSubmissionData === 'object' && formData.existingSubmissionData !== null) {
        // Check if it has the format from the example with a direct uuid property
        if ('uuid' in formData.existingSubmissionData && typeof formData.existingSubmissionData.uuid === 'string') {
          // This is the case where we have a direct uuid property at the top level
          const uuid = formData.existingSubmissionData.uuid;
          if (!existingAttachmentUuids.includes(uuid)) {
            existingAttachmentUuids.push(uuid);
            console.log(`Added UUID from top level: ${uuid}`);
          }
        }

        // Handle the specific format from the example with attachments array containing objects with uuid
        if ('attachments' in formData.existingSubmissionData &&
            Array.isArray(formData.existingSubmissionData.attachments)) {

          // This is the exact format from the example
          formData.existingSubmissionData.attachments.forEach((attachment: unknown) => {
            if (attachment && typeof attachment === 'object' && 'uuid' in attachment) {
              const uuid = (attachment as {uuid: string}).uuid;
              if (typeof uuid === 'string' && !existingAttachmentUuids.includes(uuid)) {
                existingAttachmentUuids.push(uuid);
                console.log(`Added UUID from exact format: ${uuid}`);
              }
            }
          });
        }

        // Handle the case where we have sections with questions that have user-answer-metadata
        if ('sections' in formData.existingSubmissionData &&
            Array.isArray(formData.existingSubmissionData.sections)) {

          // Define types for the section and question objects
          interface FormSection {
            id: number;
            questions: FormQuestion[];
            [key: string]: unknown;
          }

          interface FormQuestion {
            id: number;
            type: string;
            'user-answer-metadata'?: Array<{name: string; size: number; type: string}>;
            [key: string]: unknown;
          }

          // Process each section
          formData.existingSubmissionData.sections.forEach((section: unknown) => {
            // Type guard for section
            const isFormSection = (obj: unknown): obj is FormSection => {
              if (typeof obj !== 'object' || obj === null || !('questions' in obj)) {
                return false;
              }
              // Safe to access questions property now
              const questions = (obj as {questions: unknown}).questions;
              return Array.isArray(questions);
            };

            if (isFormSection(section)) {
              // Process each question
              section.questions.forEach((question: unknown) => {
                // Type guard for question
                const isAttachFileQuestion = (obj: unknown): obj is FormQuestion => {
                  if (typeof obj !== 'object' || obj === null || !('type' in obj) || !('user-answer-metadata' in obj)) {
                    return false;
                  }
                  // Safe to access type property now
                  const type = (obj as {type: unknown}).type;
                  return typeof type === 'string' && type === 'attach-file';
                };

                if (isAttachFileQuestion(question)) {
                  // Check if this is an attach-file question with user-answer-metadata
                  if (question['user-answer-metadata'] && Array.isArray(question['user-answer-metadata'])) {

                    console.log(`Found attach-file question with user-answer-metadata:`, question);

                    // If we have attachments at the top level, try to match them with this question
                    if (formData.existingSubmissionData && 'attachments' in formData.existingSubmissionData &&
                        Array.isArray(formData.existingSubmissionData.attachments)) {

                      // Get the metadata for this question
                      const metadata = question['user-answer-metadata'];

                      // Try to match attachments with metadata by name
                      formData.existingSubmissionData.attachments.forEach((attachment: unknown) => {
                        if (attachment && typeof attachment === 'object' && 'uuid' in attachment && 'file' in attachment) {
                          const uuid = (attachment as {uuid: string, file: string}).uuid;
                          const file = (attachment as {uuid: string, file: string}).file;
                          const fileName = file.split('/').pop() || '';

                          // Check if this attachment matches any metadata by name
                          metadata.forEach((meta: unknown) => {
                            if (meta && typeof meta === 'object' && 'name' in meta) {
                              const metaName = (meta as {name: string}).name;

                              // Try different ways to match the file name
                              // 1. Direct match
                              const directMatch = fileName === metaName;

                              // 2. Match without file extension
                              const fileNameWithoutExt = fileName.split('.')[0];
                              const metaNameWithoutExt = metaName.split('.')[0];
                              const matchWithoutExt = fileNameWithoutExt === metaNameWithoutExt;

                              // 3. Check if one contains the other
                              const containsMatch = fileName.includes(metaName) || metaName.includes(fileName);

                              // 4. Check if the file name contains the metadata name without special characters
                              const cleanMetaName = metaName.replace(/[^a-zA-Z0-9]/g, '');
                              const cleanFileName = fileName.replace(/[^a-zA-Z0-9]/g, '');
                              const cleanMatch = cleanFileName.includes(cleanMetaName) || cleanMetaName.includes(cleanFileName);

                              if (directMatch || matchWithoutExt || containsMatch || cleanMatch) {
                                // We found a match! Add the UUID if not already included
                                if (!existingAttachmentUuids.includes(uuid)) {
                                  existingAttachmentUuids.push(uuid);
                                  console.log(`Added UUID ${uuid} by matching file name ${fileName} with metadata name ${metaName}`);
                                }
                              }
                            }
                          });

                          // If we couldn't match by name, just add the UUID anyway
                          // This ensures we preserve all attachments even if we can't match them
                          if (!existingAttachmentUuids.includes(uuid)) {
                            existingAttachmentUuids.push(uuid);
                            console.log(`Added UUID ${uuid} without matching (fallback)`);
                          }
                        }
                      });
                    }
                  }
                }
              });
            }
          });
        }
      }

      // Also use our helper function to extract UUIDs from other possible locations in the response
      const responseUuids = extractFileUuidsFromResponse(formData.existingSubmissionData);

      // Add any UUIDs that aren't already in our list
      responseUuids.forEach(uuid => {
        if (!existingAttachmentUuids.includes(uuid)) {
          existingAttachmentUuids.push(uuid);
          console.log(`Added attachment UUID from helper function: ${uuid}`);
        }
      });
    }

    // Final fallback: If we have attachments in the response but couldn't extract any UUIDs,
    // add all attachment UUIDs from the top-level attachments array
    if (existingAttachmentUuids.length === 0 &&
        formData.existingSubmissionData &&
        'attachments' in formData.existingSubmissionData &&
        Array.isArray(formData.existingSubmissionData.attachments)) {

      formData.existingSubmissionData.attachments.forEach((attachment: unknown) => {
        if (attachment && typeof attachment === 'object' && 'uuid' in attachment) {
          const uuid = (attachment as {uuid: string}).uuid;
          if (typeof uuid === 'string') {
            existingAttachmentUuids.push(uuid);
            console.log(`Added UUID ${uuid} as fallback because no UUIDs were extracted`);
          }
        }
      });
    }

    // Special case for nested attachments that might be in a different format
    if (formData.existingSubmissionData &&
        typeof formData.existingSubmissionData === 'object') {

      // Handle case where data might be in an array format with the first item containing the attachments
      if (Array.isArray(formData.existingSubmissionData) && formData.existingSubmissionData.length > 0) {
        const firstItem = formData.existingSubmissionData[0];

        if (firstItem && typeof firstItem === 'object') {
          // Check if first item has attachments array
          if ('attachments' in firstItem && Array.isArray(firstItem.attachments)) {
            console.log("Found attachments in nested array format");

            firstItem.attachments.forEach((attachment: unknown) => {
              if (attachment && typeof attachment === 'object' && 'uuid' in attachment) {
                const uuid = String((attachment as {uuid: string}).uuid);
                if (uuid && !existingAttachmentUuids.includes(uuid)) {
                  existingAttachmentUuids.push(uuid);
                  console.log(`Added UUID ${uuid} from nested array format`);
                }
              }
            });
          }

          // Check if the first item itself has a uuid field (might be the case in some API responses)
          if ('uuid' in firstItem && typeof firstItem.uuid === 'string') {
            const uuid = firstItem.uuid;
            if (!existingAttachmentUuids.includes(uuid)) {
              existingAttachmentUuids.push(uuid);
              console.log(`Added UUID ${uuid} from first item in array`);
            }
          }
        }
      }
    }

    console.log("Existing attachment UUIDs to preserve:", existingAttachmentUuids);

    // CRITICAL FIX: Extract UUIDs directly from the attachments array in the response
    // This is the most reliable way to get the attachment UUIDs
    if (formData.existingSubmissionData &&
        typeof formData.existingSubmissionData === 'object' &&
        'attachments' in formData.existingSubmissionData &&
        Array.isArray(formData.existingSubmissionData.attachments)) {

      console.log("TRACKING: Extracting UUIDs directly from attachments array");
      console.log("TRACKING: Attachments array:", formData.existingSubmissionData.attachments);

      formData.existingSubmissionData.attachments.forEach(attachment => {
        if (attachment && typeof attachment === 'object' && 'uuid' in attachment) {
          const uuid = String(attachment.uuid);
          if (uuid && !existingAttachmentUuids.includes(uuid)) {
            existingAttachmentUuids.push(uuid);
            console.log(`TRACKING: Added UUID ${uuid} directly from attachments array`);
          }
        }
      });
    }

    // CRITICAL FIX: Ensure we have attachment UUIDs even if extraction failed
    // This is a fallback to make sure we always include attachment UUIDs in the payload
    if (existingAttachmentUuids.length === 0 && formData.existingSubmissionData) {
      // Direct extraction from user-answer-metadata in questions
      if (formData.existingSubmissionData.sections &&
          Array.isArray(formData.existingSubmissionData.sections)) {

        // Define types for the section and question objects
        interface MetadataItem {
          name?: string;
          size?: number;
          type?: string;
          question_id?: number;
          file_uuid?: string;
          [key: string]: unknown;
        }

        interface ExistingQuestion {
          id?: number;
          type?: string;
          'user-answer-metadata'?: MetadataItem[];
          [key: string]: unknown;
        }

        interface ExistingSection {
          id?: number;
          questions?: ExistingQuestion[];
          [key: string]: unknown;
        }

        formData.existingSubmissionData.sections.forEach((section: ExistingSection) => {
          if (section.questions && Array.isArray(section.questions)) {
            section.questions.forEach((question: ExistingQuestion) => {
              if (question.type === 'attach-file' &&
                  question['user-answer-metadata'] &&
                  Array.isArray(question['user-answer-metadata'])) {

                question['user-answer-metadata'].forEach((meta: MetadataItem) => {
                  if (meta && typeof meta === 'object' && meta.file_uuid) {
                    const uuid = String(meta.file_uuid);
                    if (uuid && !existingAttachmentUuids.includes(uuid)) {
                      existingAttachmentUuids.push(uuid);
                      console.log(`FALLBACK: Added UUID ${uuid} from user-answer-metadata file_uuid`);
                    }
                  }
                });
              }
            });
          }
        });
      }
    }

    // TRACKING: Log the final list of attachment UUIDs before creating payload
    console.log("TRACKING: Final list of attachment UUIDs before creating payload:", {
      count: existingAttachmentUuids.length,
      uuids: existingAttachmentUuids
    });

    // Create the payload
    const payload = {
      form_uuid: formUuid || "",
      form: formUuid || "", // Add the required 'form' property
      ...(isEditMode && formData.existingSubmissionData?.id ? { id: formData.existingSubmissionData.id } : {}),

      user: {
        identifier: currentUser.identifier || String(currentUser.id || ''),
        first_name: currentUser.first_name || '',
        last_name: currentUser.last_name || '',
        email: currentUser.email || ''
      },
      submission: submissionData,
      is_completed: isCompleted,
      final_submission: isCompleted, // Set final_submission based on completion status
      patient_uuid: patientUuid,
      existing_attachments: existingAttachmentUuids, // Use the UUIDs we collected
      attachments: processedFileAttachments // Use processed file attachments
    };

    // TRACKING: Verify existing_attachments is in the payload
    console.log("TRACKING: Verifying existing_attachments in payload:", {
      exists: 'existing_attachments' in payload,
      isArray: Array.isArray(payload.existing_attachments),
      length: payload.existing_attachments ? payload.existing_attachments.length : 0,
      value: payload.existing_attachments
    });

    // Add a stringified log of the actual payload to verify existing_attachments is included
    console.log("Final payload being sent to backend:", JSON.stringify({
      ...payload,
      // Show the actual existing_attachments field in the log
      existing_attachments: payload.existing_attachments,
      attachments: {
        image_count: processedFileAttachments.image_attachments.length,
        video_count: processedFileAttachments.video_attachments.length,
        document_count: processedFileAttachments.document_attachments.length
      }
    }));

    // For debug visibility, log the exact shape of existing_attachments
    console.log("existing_attachments field:", {
      type: Array.isArray(existingAttachmentUuids) ? "array" : typeof existingAttachmentUuids,
      length: Array.isArray(existingAttachmentUuids) ? existingAttachmentUuids.length : 0,
      value: existingAttachmentUuids
    });

    // Check if we should use external submit handler (from parent component)
    if (onSubmit) {
      onSubmit(submissionData, isCompleted);
      return;
    }

    if (!isCompleted && onSaveDraft) {
      // If not all required fields are filled and we have a draft handler
      onSaveDraft(submissionData);
      return;
    }

    // CRITICAL FIX: Add a direct log of the existing_attachments field to verify it's included
    console.log("CRITICAL CHECK - existing_attachments in payload:", {
      included: 'existing_attachments' in payload,
      type: Array.isArray(payload.existing_attachments) ? 'array' : typeof payload.existing_attachments,
      length: Array.isArray(payload.existing_attachments) ? payload.existing_attachments.length : 0,
      value: payload.existing_attachments
    });

    // Ensure existing_attachments is an array
    if (!Array.isArray(payload.existing_attachments)) {
      console.warn("existing_attachments is not an array, fixing...");
      payload.existing_attachments = [];
    }

    // CRITICAL FIX: If we have user-answer-metadata with file_uuid in the submission data,
    // make sure those UUIDs are included in existing_attachments
    if (formData.existingSubmissionData &&
        typeof formData.existingSubmissionData === 'object' &&
        'sections' in formData.existingSubmissionData &&
        Array.isArray(formData.existingSubmissionData.sections)) {

      // Define types for the section and question objects
      interface MetadataItem {
        name?: string;
        size?: number;
        type?: string;
        question_id?: number;
        file_uuid?: string;
        [key: string]: unknown;
      }

      interface ExistingQuestion {
        id?: number;
        type?: string;
        'user-answer-metadata'?: MetadataItem[];
        [key: string]: unknown;
      }

      interface ExistingSection {
        id?: number;
        questions?: ExistingQuestion[];
        [key: string]: unknown;
      }

      formData.existingSubmissionData.sections.forEach((section: ExistingSection) => {
        if (section && typeof section === 'object' && 'questions' in section && Array.isArray(section.questions)) {
          section.questions.forEach((question: ExistingQuestion) => {
            if (question && typeof question === 'object' &&
                question.type === 'attach-file' &&
                'user-answer-metadata' in question &&
                Array.isArray(question['user-answer-metadata'])) {

              question['user-answer-metadata'].forEach((meta: MetadataItem) => {
                if (meta && typeof meta === 'object' && 'file_uuid' in meta) {
                  const uuid = String(meta.file_uuid);
                  if (uuid && !payload.existing_attachments.includes(uuid)) {
                    payload.existing_attachments.push(uuid);
                    console.log(`CRITICAL FIX: Added UUID ${uuid} from user-answer-metadata to payload`);
                  }
                }
              });
            }
          });
        }
      });
    }

    // Final check of the payload before submission
    console.log("FINAL PAYLOAD CHECK - existing_attachments:", {
      included: 'existing_attachments' in payload,
      type: Array.isArray(payload.existing_attachments) ? 'array' : typeof payload.existing_attachments,
      length: Array.isArray(payload.existing_attachments) ? payload.existing_attachments.length : 0,
      value: payload.existing_attachments
    });

    createSubmissionMutation.mutate(payload, {
      onSuccess: (response: unknown) => {
        console.log("Form submission successful!", response);
        // Debug logging to check if existing_attachments was included in the response
        if (typeof response === 'object' && response !== null) {
          console.log("Response includes existing_attachments?", 'existing_attachments' in response);
        }
        setSubmissionStatus('success');
        // Show appropriate message based on if it was saved as draft or completed
        if (isCompleted) {
          setErrorMessage(null);
        } else {
          setErrorMessage("Some required fields were not answered. Form saved as draft.");
        }
      },
      onError: (error: {
        message?: string;
        response?: {
          data?: {
            detail?: string;
            message?: string;
            [key: string]: unknown;
          };
          status?: number;
        };
      }) => {
        console.error("Error submitting form:", error);
        // Log more details about the error response
        if (error.response) {
          console.error("Error response data:", error.response.data);
          console.error("Error response status:", error.response.status);
          // Extract a more detailed error message if available
          const errorDetail = error.response.data?.detail ||
                            error.response.data?.message ||
                            (typeof error.response.data === 'string' ? error.response.data : null) ||
                            JSON.stringify(error.response.data);
          setErrorMessage(`Error: ${errorDetail}`);
        } else {
          setErrorMessage(error.message || "Unknown error occurred");
        }
        setSubmissionStatus('error');
      }
    });
  };
  // Check if a question has been answered
  const isQuestionAnswered = useCallback((sectionId: number, questionId: number): boolean => {
    const answer = formAnswers[sectionId]?.[questionId];
    if (answer === undefined || answer === null) return false;
    if (Array.isArray(answer)) {
      return answer.length > 0 && answer.some(item => item !== "");
    }
    if (typeof answer === 'object') {
      if ('rows' in answer && 'data' in answer) {
        // Table type answer
        return Array.isArray(answer.data) && answer.data.some((row: string[]) => Array.isArray(row) && row.some((cell: string) => cell !== ""));
      }
      if ('fullName' in answer || 'date' in answer) {
        // Signature type answer
        return (
          (typeof answer === "object" && answer !== null && "fullName" in answer && !!answer.fullName) ||
          (typeof answer === "object" && answer !== null && "date" in answer && !!answer.date)
        );
      }
      // Generic check for any non-empty property in the object
      return Object.values(answer).some(val =>
        val !== "" &&
        val !== null &&
        val !== undefined &&
        (Array.isArray(val) ? val.length > 0 : true)
      );
    }
    return answer !== "";
  }, [formAnswers]);
  // NEW FUNCTION: Check if all required fields are answered
  const validateRequiredFields = useCallback((): boolean => {
    const newInvalidFields = new Set<string>();
    let allRequiredFieldsAnswered = true;
    formData.sections.forEach(section => {
      section.questions.forEach(question => {
        if (question.required && !isQuestionAnswered(section.id, question.id)) {
          newInvalidFields.add(`${section.id}-${question.id}`);
          allRequiredFieldsAnswered = false;
        }
      });
    });

    setInvalidRequiredFields(newInvalidFields);
    return allRequiredFieldsAnswered;
  }, [formData.sections, isQuestionAnswered]);
  // Run validation on initial load and whenever form answers change
  useEffect(() => {
    // This will automatically validate all required fields and update invalidRequiredFields set
    validateRequiredFields();
  }, [formAnswers, validateRequiredFields]);

  // Filter questions based on activeFilter and patientsMode
  const getFilteredQuestions = (section: SavedSectionData) => {
    return section.questions.filter(question => {
      if (question.type === "Expression") {
        return true;
      }
      if (patientsMode && question.nonClinical === false) {
        return false;
      }
      if (activeFilter === 'unanswered') {
        const isAnswered = isQuestionAnswered(section.id, question.id);
        return !isAnswered;
      }
      // Show all questions for 'showAll' filter
      return true;
    });
  };
  const filteredQuestions = getFilteredQuestions(activeSection);
  console.log("Original questions count:", activeSection.questions.length);
  console.log("Filtered questions count:", filteredQuestions.length);
  const renderQuestion = (
    question: SavedQuestionData,
    section: SavedSectionData,
    displayNumber: number | null = null,
    showHistory: boolean = false,
    onToggleHistory?: () => void
  ) => {
    const isRequired = question.required;
    const isInvalid = isRequired && invalidRequiredFields.has(`${section.id}-${question.id}`);
    const wasPreviouslyAnswered = previouslyAnsweredQuestions.has(`${section.id}-${question.id}`);
    const questionKey = `${section.id}-${question.id}`;
    const hasComment = formComments[section.id]?.[question.id]?.trim() !== "";
    const isCommentExpanded = expandedComments.has(questionKey);

    // --- COMMENT HISTORY LOGIC FOR DISPLAY ---
    let commentHistory: CommentHistoryItem[] = [];
    if (
      formData.existingSubmissionData &&
      Array.isArray(formData.existingSubmissionData.sections)
    ) {
      const sectionData = (formData.existingSubmissionData.sections as ExistingSubmissionSection[]).find((s) => s.id === section.id);
      if (sectionData && Array.isArray(sectionData.questions)) {
        const questionData = sectionData.questions.find((q: ExistingSubmissionQuestion) => q.id === question.id);
        if (questionData && Array.isArray(questionData.comment_history)) {
          commentHistory = questionData.comment_history;
        }
      }
    }

    const isModified = trackedChanges && Object.keys(trackedChanges).some(key => key.startsWith(questionKey));

    return (
      <div 
        key={question.id} 
        className={`question-container ${isModified ? 'changes-tracked' : ''}`}
      >
        {displayNumber !== null && (
          <span className="question-number">
            Question n° {displayNumber}
            {!isQuestionAnswered(section.id, question.id) && (
              <span className="unanswered-indicator"> (unanswered)</span>
            )}
            {isInvalid && (
              <span className="required-indicator"> (required)</span>
            )}
            {wasPreviouslyAnswered && (
              <span className="answered-indicator"> (previously answered)</span>
            )}
          </span>
        )}
        {question.type !== "Expression" && question.questionName && (
          <label className={`question-label ${isInvalid ? 'invalid-label' : ''}`}>
            {question.questionName} {question.required ? "*" : ""}
            {question.nonClinical && <span className="non-clinical-badge">Non-Clinical</span>}
          </label>
        )}
        <div className="question-content">
          <QuestionRenderer
            sectionId={section.id}
            question={question}
            currentAnswer={formAnswers[section.id]?.[question.id] || question.answers || ""}
            onAnswerChange={(qId, value) => handleAnswerChange(section.id, qId, value)}
            isInvalid={isInvalid}
            isEditMode={isEditMode}
            formData={formData}
          />

          {/* Add comment button and textarea */}
          <div className="question-comment">
            <button
              className={`comment-button ${hasComment ? 'has-comment' : ''}`}
              onClick={() => toggleCommentExpanded(section.id, question.id)}
            >
              {hasComment ? '💬 Edit Comment' : '💬 Add Comment'}
            </button>

            {isCommentExpanded && (
              <div className="comment-textarea-container">
                <label className="comment-label">Additional comment</label>
                <NurtifyTextArea
                  value={formComments[section.id]?.[question.id] || ""}
                  onChange={(e: ChangeEvent<HTMLTextAreaElement>) =>
                    handleCommentChange(section.id, question.id, e.target.value)
                  }
                  placeholder="Enter your comment here..."
                />
                {/* Show comment details if available */}
                {(() => {
                  // Try to get comment details from the question object in the loaded submission data
                  let commentBy = null;
                  let commentAt = null;
                  if (
                    formData.existingSubmissionData &&
                    Array.isArray(formData.existingSubmissionData.sections)
                  ) {
                    const sectionData = formData.existingSubmissionData.sections.find((s: ExistingSubmissionSection) => s.id === section.id);
                    if (sectionData && Array.isArray(sectionData.questions)) {
                      const questionData = sectionData.questions.find((q: ExistingSubmissionQuestion) => q.id === question.id);
                      if (questionData) {
                        // Support both snake_case and camelCase
                        commentBy = questionData.comment_by || questionData.commentBy || null;
                        commentAt = questionData.comment_at || questionData.commentAt || null;
                      }
                    }
                  }
                  if (commentBy || commentAt) {
                    return (
                      <div className="comment-details">
                        {commentBy && (
                          <span className="comment-by">Commented by: {typeof commentBy === 'object' ? (commentBy.first_name || '') + ' ' + (commentBy.last_name || '') : commentBy}</span>
                        )}
                        {commentAt && (
                          <span className="comment-at">on {new Date(commentAt).toLocaleString()}</span>
                        )}
                      </div>
                    );
                  }
                  return null;
                })()}
                {commentHistory.length > 0 && onToggleHistory && (
                  <div className="comment-history-block">
                    <button
                      type="button"
                      className="comment-history-toggle"
                      onClick={onToggleHistory}
                    >
                      {showHistory ? 'Hide comment history' : 'Show comment history'}
                    </button>
                    {showHistory && (
                      <div className="comment-history-list">
                        {commentHistory.map((item: CommentHistoryItem, idx: number) => (
                          <div className="comment-history-item" key={idx}>
                            <div className="comment-history-text">{item.comment}</div>
                            <div className="comment-history-meta">
                              {item["commented-by"] && (
                                <span className="comment-history-by">
                                  {typeof item["commented-by"] === 'object'
                                    ? `${item["commented-by"].first_name || ''} ${item["commented-by"].last_name || ''}`.trim()
                                    : item["commented-by"]}
                                </span>
                              )}
                              {item["commented-at"] && (
                                <span className="comment-history-at">
                                  {new Date(item["commented-at"]).toLocaleString()}
                                </span>
                              )}
                            </div>
                          </div>
                        ))}
                      </div>
                    )}
                  </div>
                )}
              </div>
            )}
          </div>
        </div>

        {isInvalid && (
          <div className="error-message">This field is required</div>
        )}

        {/* If question was previously answered, show option to clear and re-answer */}
        {wasPreviouslyAnswered && (
          <div className="clear-answer-option">
            <button
              className="clear-answer-btn"
              onClick={() => {
                console.log(`Clearing answer for ${questionKey}`);

                // Mark this question as modified by the current user
                setModifiedQuestions(prev => {
                  const newSet = new Set(prev);
                  newSet.add(questionKey);
                  return newSet;
                });

                // Clear this specific answer and tracking states
                setFormAnswers(prev => {
                  const updated = {...prev};

                  // Reset to default empty value based on question type
                  if (question.type === "multiple-choice") {
                    updated[section.id][question.id] = [];
                  } else if (question.type === "table") {
                    updated[section.id][question.id] = { rows: 1, data: [[]] };
                  } else {
                    updated[section.id][question.id] = "";
                  }

                  return updated;
                });

                // Remove from previously answered set
                setPreviouslyAnsweredQuestions(prev => {
                  const updated = new Set(prev);
                  updated.delete(questionKey);
                  return updated;
                });

                // Also remove from answered questions set
                setAnsweredQuestions(prev => {
                  const updated = new Set(prev);
                  updated.delete(questionKey);
                  return updated;
                });

                // Clear any file attachments for this question
                setFileAttachments(prev => ({
                  image_attachments: prev.image_attachments.filter(item => item.questionId !== question.id),
                  video_attachments: prev.video_attachments.filter(item => item.questionId !== question.id),
                  document_attachments: prev.document_attachments.filter(item => item.questionId !== question.id)
                }));
              }}
            >
              <RefreshCw size={14} /> Clear answer
            </button>
          </div>
        )}
      </div>
    );
  };
  return (
    <div className="rebuild-form-container">
      <div className="rebuild-form">
        <header>
          <h1>{formData.formDetails.name}</h1>
          <p>{formData.formDetails.description}</p>

          {isEditMode && (
            <div className="edit-mode-banner">
              <Edit3 size={20} />
              <span>You are editing a previously submitted form. Your previous answers have been loaded.</span>
            </div>
          )}

          {/* Display filtering information */}
          {activeFilter === 'unanswered' && (
            <div className="filter-info">
              Showing only unanswered questions
            </div>
          )}

          {patientsMode && (
            <div className="filter-info patients-mode">
              Patients Mode: Showing only non-clinical questions
            </div>
          )}

          {invalidRequiredFields.size > 0 && (
            <div className="required-fields-warning">
              <AlertCircle size={20} />
              <span>There are {invalidRequiredFields.size} required fields that need to be filled.</span>
            </div>
          )}

          {isLoadingUser && (
            <div className="submission-loading">
              <Loader size={20} className="animate-spin" />
              <span>Loading user information...</span>
            </div>
          )}

          {submissionStatus === 'error' && errorMessage && (
            <div className="submission-error">
              <AlertCircle size={20} />
              Error: {errorMessage}
            </div>
          )}

          {submissionStatus === 'success' && !errorMessage && (
            <div className="submission-success">
              <CheckCircle size={20} />
              Form submitted successfully!
            </div>
          )}

          {submissionStatus === 'success' && errorMessage && (
            <div className="submission-draft-saved">
              <CheckCircle size={20} />
              {errorMessage}
            </div>
          )}
        </header>
        <div className="tab-bar">
          {formData.sections.map((section, index) => (
            <div
              key={section.id}
              className={`tab ${index === activeSectionIndex ? "active" : ""}`}
              onClick={() => setActiveSectionIndex(index)}
            >
              {section.name}
            </div>
          ))}
        </div>
        <main className="form-content-wrapper">
          <div className="form-section">
            <div className="form-actions">
              {activeFilter === "submitted" ? (
                <GeneratePdfButton uuid={formUuid} isSubmission />
              ) : (
                <GeneratePdfButton uuid={formUuid} />
              )}
            </div>
            <h2>{activeSection.name}</h2>
            <p>{activeSection.description}</p>
            {filteredQuestions.length > 0 ? (
              (() => {
                let counter = 0;
                return filteredQuestions.map((question) => {
                  let displayNumber: number | null = null;
                  if (question.type !== "Expression") {
                    counter++;
                    displayNumber = counter;
                  }
                  return renderQuestion(question, activeSection, displayNumber, expandedCommentHistories[`${activeSection.id}-${question.id}`], () => toggleCommentHistory(activeSection.id, question.id));
                });
              })()
            ) : (
              <div className="no-questions-message">
                No questions match the current filter settings.
                {activeFilter === 'unanswered' && (
                  <p>All questions in this section have been answered.</p>
                )}
                {patientsMode && (
                  <p>No non-clinical questions are available in this section.</p>
                )}
              </div>
            )}

            <div className="navigation-buttons">
              {activeSectionIndex > 0 && (
                <button onClick={goToPrevious}>Previous</button>
              )}
              {activeSectionIndex < formData.sections.length - 1 && (
                <button onClick={goToNext}>Next</button>
              )}
              {activeSectionIndex === formData.sections.length - 1 && (
                <button
                  onClick={handleSubmit}
                  disabled={
                    submissionStatus === 'loading' ||
                    isLoadingUser ||
                    (isPatientUser && invalidRequiredFields.size > 0) // Only require all fields for patient users
                  }
                  className={`${isLoadingUser ? 'loading-user-data' : ''} ${isPatientUser && invalidRequiredFields.size > 0 ? 'disabled-submit' : ''}`}
                  title={isPatientUser && invalidRequiredFields.size > 0 ? 'Please answer all required questions before submitting' : ''}
                >
                  {submissionStatus === 'loading' ? (
                    <>
                      <Loader size={16} className="animate-spin" style={{marginRight: "8px"}} />
                      Submitting...
                    </>
                  ) : isLoadingUser ? (
                    <>
                      <Loader size={16} className="animate-spin" style={{marginRight: "8px"}} />
                      Loading User Data...
                    </>
                  ) : (
                    'Submit Form'
                  )}
                </button>
              )}
            </div>
          </div>
        </main>
      </div>
    </div>
  );
}

interface QuestionRendererProps {
  sectionId: number;
  question: SavedQuestionData;
  currentAnswer: string | string[] | FileList | { fullName?: string; date?: string } | { rows: number; data: string[][] } | { newFiles: FileList | null; existingFiles: Array<{id: string; url: string; name: string; type: string}> } | number | null;
  onAnswerChange: (qId: number, value: string | string[] | FileList | { fullName?: string; date?: string } | { rows: number; data: string[][] } | { newFiles: FileList | null; existingFiles: Array<{id: string; url: string; name: string; type: string}> } | number | null) => void;
  isInvalid?: boolean;
  isEditMode?: boolean;
  formData?: SavedFormData; // Add formData prop
}

const QuestionRenderer: React.FC<QuestionRendererProps> = ({
  question,
  currentAnswer,
  onAnswerChange,
  isInvalid = false,
  isEditMode = false,
  formData
}) => {
  // For different types, render a controlled component that calls onAnswerChange
  switch (question.type) {
    case "short-text": {
      // Normalize the short-text answer
      const normalizedShortTextValue = (() => {
        console.log(`Short text question ${question.id} current answer:`, currentAnswer);

        // If it's already a string or number, use it directly
        if (typeof currentAnswer === "string" || typeof currentAnswer === "number") {
          return currentAnswer;
        }

        // If it's an object with user-answer property
        if (currentAnswer && typeof currentAnswer === 'object') {
          interface UserAnswerContainer {
            'user-answer': unknown;
          }

          const hasUserAnswer = (obj: object): obj is UserAnswerContainer =>
            'user-answer' in obj;

          if (hasUserAnswer(currentAnswer)) {
            const userAnswer = currentAnswer['user-answer'];
            console.log('Found user-answer for short-text:', userAnswer);

            if (typeof userAnswer === 'string' || typeof userAnswer === 'number') {
              return userAnswer;
            } else if (userAnswer !== null && userAnswer !== undefined) {
              // Try to convert to string if possible
              return String(userAnswer);
            }
          }
        }

        return "";
      })();

      console.log('Final normalized value for short-text:', normalizedShortTextValue);

      return (
        <div className="short-text-question">
          <NurtifyInput
            type="text"
            value={normalizedShortTextValue}
            onChange={(e: ChangeEvent<HTMLInputElement>) =>
              onAnswerChange(question.id, e.target.value)
            }
            className={isInvalid ? "invalid-input" : ""}
          />
        </div>
      );
    }
    case "long-text": {
      // Normalize the long-text answer
      const normalizedLongTextValue = (() => {
        console.log(`Long text question ${question.id} current answer:`, currentAnswer);

        // If it's already a string, use it directly
        if (typeof currentAnswer === "string") {
          return currentAnswer;
        }

        // If it's an array with one string item, use that
        if (Array.isArray(currentAnswer) && currentAnswer.length > 0) {
          const firstItem = currentAnswer[0];
          if (typeof firstItem === 'string') {
            return firstItem;
          } else if (firstItem !== null && firstItem !== undefined) {
            return String(firstItem);
          }
        }

        // If it's an object with user-answer property
        if (currentAnswer && typeof currentAnswer === 'object') {
          interface UserAnswerContainer {
            'user-answer': unknown;
          }

          const hasUserAnswer = (obj: object): obj is UserAnswerContainer =>
            'user-answer' in obj;

          if (hasUserAnswer(currentAnswer)) {
            const userAnswer = currentAnswer['user-answer'];
            console.log('Found user-answer for long-text:', userAnswer);

            if (typeof userAnswer === 'string') {
              return userAnswer;
            } else if (Array.isArray(userAnswer) && userAnswer.length > 0) {
              return typeof userAnswer[0] === 'string' ? userAnswer[0] : String(userAnswer[0] || '');
            } else if (userAnswer !== null && userAnswer !== undefined) {
              // Try to convert to string if possible
              return String(userAnswer);
            }
          }
        }

        return "";
      })();

      console.log('Final normalized value for long-text:', normalizedLongTextValue);

      return (
        <div className="long-text-question">
          <NurtifyTextArea
            value={normalizedLongTextValue}
            required={question.required}
            onChange={(e: ChangeEvent<HTMLTextAreaElement>) =>
              onAnswerChange(question.id, e.target.value)
            }
          />
        </div>
      );
    }
    case "single-choice": {
      // Ensure we handle the currentAnswer correctly for single-choice questions
      const normalizedAnswer = (() => {
        console.log(`Single choice question ${question.id} current answer:`, currentAnswer);

        // If it's already a string, use it directly
        if (typeof currentAnswer === 'string') {
          return currentAnswer;
        }

        // If it's an array with one item, use that item
        if (Array.isArray(currentAnswer) && currentAnswer.length === 1) {
          return currentAnswer[0];
        }

        // If it's an object with user-answer property
        if (currentAnswer && typeof currentAnswer === 'object') {
          interface UserAnswerContainer {
            'user-answer': unknown;
          }

          const hasUserAnswer = (obj: object): obj is UserAnswerContainer =>
            'user-answer' in obj;

          if (hasUserAnswer(currentAnswer)) {
            const userAnswer = currentAnswer['user-answer'];
            if (typeof userAnswer === 'string') {
              return userAnswer;
            }
            if (Array.isArray(userAnswer) && userAnswer.length === 1) {
              return userAnswer[0];
            }
          }
        }

        return currentAnswer;
      })();

      return (
        <div className="single-choice-question">
          {question.answers?.map((ans, idx) => {
            const isChecked = normalizedAnswer === ans;
            console.log(`Option ${ans} checked:`, isChecked);

            return (
              <div key={idx}>
                <NurtifyRadio
                  name={`question-${question.id}`}
                  value={ans}
                  required={question.required}
                  label={ans}
                  checked={isChecked}
                  onChange={() => onAnswerChange(question.id, ans)}
                />
              </div>
            );
          })}
        </div>
      );
    }
    case "multiple-choice": {
      // Ensure currentAnswer is an array for multiple-choice questions
      // Using IIFE with block scope to avoid lexical declaration in case block error
      const ensuredArray = (() => {
        // Log the current answer for debugging
        console.log(`Multiple choice question ${question.id} current answer:`, currentAnswer);

        if (Array.isArray(currentAnswer)) {
          console.log('Current answer is array with values:', currentAnswer);
          return currentAnswer.map(item => String(item)); // Convert all items to strings
        }

        // If it's a string (single value), convert to array
        if (typeof currentAnswer === 'string' && currentAnswer) {
          console.log('Current answer is string:', currentAnswer);
          return [currentAnswer];
        }

        // If it's another type of non-empty value, try to convert
        if (currentAnswer && typeof currentAnswer === 'object') {
          console.log('Current answer is object:', JSON.stringify(currentAnswer));

          // For backward compatibility with different formats
          // Define a type for the expected structure
          interface UserAnswerContainer {
            'user-answer': unknown;
          }

          // Type guard to check if object has user-answer property
          const hasUserAnswer = (obj: object): obj is UserAnswerContainer =>
            'user-answer' in obj;

          if (hasUserAnswer(currentAnswer)) {
            const userAnswer = currentAnswer['user-answer'];
            console.log('Found user-answer:', userAnswer);

            if (Array.isArray(userAnswer)) {
              // Convert all items to strings for consistent comparison
              return userAnswer.map(item => String(item));
            } else if (typeof userAnswer === 'string' && userAnswer) {
              return [userAnswer];
            } else if (userAnswer && typeof userAnswer === 'number') {
              return [String(userAnswer)];
            }
          }
        }

        return [];
      })();

      console.log('Final ensured array for multiple choice:', ensuredArray);

      return (
        <div className="multiple-choice-question">
          {question.answers?.map((ans, idx) => {
            // Convert both to strings for comparison to avoid type mismatches
            const stringAns = String(ans);
            // Improved comparison using includes or some for more flexible matching
            const isChecked = ensuredArray.some(item =>
              String(item).toLowerCase() === stringAns.toLowerCase()
            );
            console.log(`Option ${ans} (${typeof ans}) checked:`, isChecked, 'Array contains:', ensuredArray);

            return (
              <div key={idx}>
                <NurtifyCheckBox
                  name={`question-${question.id}`}
                  value={ans}
                  required={question.required}
                  label={ans}
                  checked={isChecked}
                  onChange={(e: ChangeEvent<HTMLInputElement>) => {
                    let updated = [...ensuredArray];
                    if (e.target.checked) {
                      updated.push(stringAns);
                    } else {
                      updated = updated.filter((a) => String(a).toLowerCase() !== stringAns.toLowerCase());
                    }
                    onAnswerChange(question.id, updated);
                  }}
                />
              </div>
            );
          })}
        </div>
      );
    }
    case "boolean": {
      // Similar to single-choice, ensure we handle the currentAnswer correctly
      const normalizedAnswer = (() => {
        console.log(`Boolean question ${question.id} current answer:`, currentAnswer);

        // If it's already a string, use it directly
        if (typeof currentAnswer === 'string') {
          return currentAnswer;
        }

        // If it's an array with one item, use that item
        if (Array.isArray(currentAnswer) && currentAnswer.length === 1) {
          return currentAnswer[0];
        }

        // If it's an object with user-answer property
        if (currentAnswer && typeof currentAnswer === 'object') {
          interface UserAnswerContainer {
            'user-answer': unknown;
          }

          const hasUserAnswer = (obj: object): obj is UserAnswerContainer =>
            'user-answer' in obj;

          if (hasUserAnswer(currentAnswer)) {
            const userAnswer = currentAnswer['user-answer'];
            if (typeof userAnswer === 'string') {
              return userAnswer;
            }
            if (Array.isArray(userAnswer) && userAnswer.length === 1) {
              return userAnswer[0];
            }
          }
        }

        return currentAnswer;
      })();

      return (
        <div className="boolean-question">
          {question.answers?.map((ans, idx) => {
            const isChecked = normalizedAnswer === ans;
            console.log(`Option ${ans} checked:`, isChecked);

            return (
              <div key={idx}>
                <NurtifyRadio
                  name={`question-${question.id}`}
                  value={ans}
                  required={question.required}
                  label={ans}
                  checked={isChecked}
                  onChange={() => onAnswerChange(question.id, ans)}
                />
              </div>
            );
          })}
        </div>
      );
    }
    case "multiple-text-boxes": {
      // Ensure currentAnswer is properly formatted for multiple-text-boxes
      const ensuredMultiTextValues = (() => {
        console.log(`Multiple text boxes question ${question.id} current answer:`, currentAnswer);

        // Direct array handling - most common case
        if (Array.isArray(currentAnswer)) {
          console.log('Current answer is array with values:', currentAnswer);
          return currentAnswer.map(item => item === null || item === undefined ? '' : String(item));
        }

        // Handle case where the answer is a single string - put it in the first box
        if (typeof currentAnswer === 'string' && currentAnswer.trim() !== '') {
          console.log('Current answer is string:', currentAnswer);
          const result = Array(question.options?.length || 1).fill('');
          result[0] = currentAnswer;
          return result;
        }

        // If it's an object
        if (currentAnswer && typeof currentAnswer === 'object') {
          console.log('Current answer is object:', JSON.stringify(currentAnswer));

          // Case 1: User-answer property
          interface UserAnswerContainer {
            'user-answer': unknown;
          }

          const hasUserAnswer = (obj: object): obj is UserAnswerContainer =>
            'user-answer' in obj;

          if (hasUserAnswer(currentAnswer)) {
            const userAnswer = currentAnswer['user-answer'];
            console.log('Found user-answer:', userAnswer);

            if (Array.isArray(userAnswer)) {
              return userAnswer.map(item => item === null || item === undefined ? '' : String(item));
            } else if (typeof userAnswer === 'string' && userAnswer.trim() !== '') {
              // If user-answer is a string, put it in the first box
              const result = Array(question.options?.length || 1).fill('');
              result[0] = userAnswer;
              return result;
            }
          }

          // Case 2: Object with string properties matching options
          const optionsLength = question.options?.length || 0;
          if (optionsLength > 0) {
            const result = Array(optionsLength).fill('');
            let foundAny = false;

            // Try to match object properties to option names
            question.options?.forEach((option, index) => {
              const key = option.toLowerCase().replace(/\s+/g, '_');
              if (typeof currentAnswer === 'object' && currentAnswer !== null && key in currentAnswer) {
                // Use type assertion to access the property safely
                result[index] = String((currentAnswer as Record<string, unknown>)[key] || '');
                foundAny = true;
              }
            });

            if (foundAny) {
              return result;
            }
          }
        }

        // If we have options but no values, create an empty array with the right length
        const optionsLength = question.options?.length || 0;
        return Array(optionsLength).fill('');
      })();

      console.log('Final ensured values for multiple-text-boxes:', ensuredMultiTextValues);

      return (
        <div className="multiple-text-boxes-question">
          <NurtifyMultiInput
            inputs={
              question.options?.map((option) => ({ name: option, label: option })) ||
              []
            }
            values={ensuredMultiTextValues}
            onChange={(index, e) => {
              const newValues = [...ensuredMultiTextValues];
              newValues[index] = e.target.value;
              onAnswerChange(question.id, newValues);
            }}
          />
        </div>
      );
    }
    case "dropdown": {
      // Normalize the dropdown answer
      const normalizedDropdownValue = (() => {
        console.log(`Dropdown question ${question.id} current answer:`, currentAnswer);

        // If it's already a string, use it directly
        if (typeof currentAnswer === "string") {
          return currentAnswer;
        }

        // If it's an array with one item, use that item
        if (Array.isArray(currentAnswer) && currentAnswer.length === 1) {
          return typeof currentAnswer[0] === "string" ? currentAnswer[0] : "";
        }

        // If it's an object with user-answer property
        if (currentAnswer && typeof currentAnswer === 'object') {
          interface UserAnswerContainer {
            'user-answer': unknown;
          }

          const hasUserAnswer = (obj: object): obj is UserAnswerContainer =>
            'user-answer' in obj;

          if (hasUserAnswer(currentAnswer)) {
            const userAnswer = currentAnswer['user-answer'];
            if (typeof userAnswer === 'string') {
              return userAnswer;
            }
            if (Array.isArray(userAnswer) && userAnswer.length === 1 && typeof userAnswer[0] === 'string') {
              return userAnswer[0];
            }
          }
        }

        return "";
      })();

      return (
        <div className="dropdown-question">
          <NurtifySelect
            name={`question-${question.id}`}
            value={normalizedDropdownValue}
            required={question.required}
            options={[
              { value: "", label: "Select an option" },
              ...(question.options?.map((opt) => ({ value: opt, label: opt })) || [])
            ]}
            onChange={(e: ChangeEvent<HTMLSelectElement>) =>
              onAnswerChange(question.id, e.target.value)
            }
          />
        </div>
      );
    }
    case "multi-select-dropdown": {
      // Ensure currentAnswer is an array for multi-select-dropdown questions
      const ensuredMultiSelectArray = (() => {
        console.log(`Multi-select dropdown question ${question.id} current answer:`, currentAnswer);

        if (Array.isArray(currentAnswer)) {
          return currentAnswer;
        }

        // If it's a string (single value), convert to array
        if (typeof currentAnswer === 'string' && currentAnswer) {
          return [currentAnswer];
        }

        // If it's another type of non-empty value, try to convert
        if (currentAnswer && typeof currentAnswer === 'object') {
          // For backward compatibility with different formats
          interface UserAnswerContainer {
            'user-answer': unknown;
          }

          const hasUserAnswer = (obj: object): obj is UserAnswerContainer =>
            'user-answer' in obj;

          if (hasUserAnswer(currentAnswer)) {
            const userAnswer = currentAnswer['user-answer'];
            return Array.isArray(userAnswer) ? userAnswer :
                   (typeof userAnswer === 'string' && userAnswer ? [userAnswer] : []);
          }
        }

        return [];
      })();

      return (
        <div className="multi-select-dropdown-question">
          <NurtifyComboBox
            options={question.options?.map((opt) => ({ value: opt, label: opt })) || []}
            selectedValues={ensuredMultiSelectArray}
            onChange={(newValues) => onAnswerChange(question.id, newValues)}
          />
        </div>
      );
    }
    case "attach-file": {
      // Extract existing file attachments directly from the currentAnswer
      const getExistingFileAttachments = () => {
        // If we have existing files in currentAnswer, use those
        if (currentAnswer && typeof currentAnswer === 'object' && 'existingFiles' in currentAnswer) {
          // Log all existing files with full details for debugging
          const existingFiles = currentAnswer.existingFiles || [];
          console.log(`Question ${question.id} - Found ${existingFiles.length} existing files in currentAnswer:`,
            existingFiles.map(f => ({id: f.id, name: f.name, url: f.url ? f.url.substring(0, 30) + '...' : 'none'}))
          );
          return existingFiles;
        }

        // If we're in edit mode, try to find attachments from the original submission data
        if (isEditMode && formData && formData.existingSubmissionData) {
          // Look for attachments in the API response
          const attachments = formData.existingSubmissionData.attachments;
          if (attachments && Array.isArray(attachments) && attachments.length > 0) {
            // Try to find metadata for this question in the original submission
            let questionMetadata: Array<{name: string; size?: number; type?: string; question_id?: number}> = [];

            // Find the section and question in the original submission data
            if (formData.existingSubmissionData.sections &&
                Array.isArray(formData.existingSubmissionData.sections)) {
              // Try to find the section that contains this question
              const allSections = formData.existingSubmissionData.sections as Array<{
                id: number;
                questions?: Array<{
                  id: number;
                  type?: string;
                  'user-answer-metadata'?: Array<{name: string; size?: number; type?: string; question_id?: number}>;
                }>;
              }>;

              // Look through all sections for this question
              for (const section of allSections) {
                if (section.questions && Array.isArray(section.questions)) {
                  const q = section.questions.find(q => q.id === question.id);
                  if (q && q['user-answer-metadata'] && Array.isArray(q['user-answer-metadata'])) {
                    questionMetadata = q['user-answer-metadata'];
                    break;
                  }
                }
              }
            }

            // Match attachments to this question using metadata
            const matchedAttachments: Array<{id: string; url: string; name: string; type: string}> = [];

            // For each piece of metadata, find the matching attachment
            questionMetadata.forEach(meta => {
              const metaName = meta.name;

              // Find a matching attachment by name
              attachments.forEach((attachment: {
                uuid?: string;
                file?: string;
                file_type?: string;
              }) => {
                if (attachment && attachment.uuid && attachment.file) {
                  const fileName = attachment.file.split('/').pop() || '';

                  // Check if this attachment matches the metadata
                  if (fileName === metaName || fileName.includes(metaName) || metaName.includes(fileName)) {
                    matchedAttachments.push({
                      id: attachment.uuid,
                      url: attachment.file,
                      name: fileName,
                      type: attachment.file_type ||
                           (fileName.match(/\.(jpg|jpeg|png|gif)$/i) ? 'image/' + fileName.split('.').pop() :
                            fileName.match(/\.(pdf)$/i) ? 'application/pdf' :
                            fileName.match(/\.(doc|docx)$/i) ? 'application/msword' :
                            'application/octet-stream')
                    });
                  }
                }
              });
            });

            if (matchedAttachments.length > 0) {
              console.log(`Question ${question.id} - Found ${matchedAttachments.length} attachments from API data:`,
                matchedAttachments.map(f => ({id: f.id, name: f.name, url: f.url ? f.url.substring(0, 30) + '...' : 'none'}))
              );
              return matchedAttachments;
            }
          }
        }

        // Otherwise return empty array
        console.log(`Question ${question.id} - No existing files found in currentAnswer or API data`);
        return [];
      };

      const existingFileAttachments = getExistingFileAttachments();

      // Log detailed information for debugging the attachments issue
      console.log(`Question ${question.id} file component props:`, {
        questionId: question.id,
        existingAttachmentsCount: existingFileAttachments.length,
        multiple: question.multiple,
        required: question.required,
        fileTypes: question.fileTypes,
        hasNewFiles: currentAnswer && typeof currentAnswer === 'object' && 'newFiles' in currentAnswer
      });

      return (
        <div className="question-answer-file-attachment">
          <NurtifyAttachFileBox
            required={question.required}
            onChange={(files, existingFiles) => {
              // Always preserve existingFiles even if files is null
              console.log(`Question ${question.id} - File change:`, {
                newFiles: files ? `${files.length} files` : 'no files',
                existingFiles: existingFiles ? `${existingFiles.length} files` : 'no files'
              });

              // Create a file attachment answer object with the correct structure
              // IMPORTANT: Merge any new existingFiles with the ones already in currentAnswer
              // This ensures we don't lose existing files when adding new ones
              const currentExistingFiles =
                (currentAnswer && typeof currentAnswer === 'object' && 'existingFiles' in currentAnswer)
                ? currentAnswer.existingFiles || []
                : [];

              // If we're getting new existingFiles from the component, use those
              // Otherwise, keep the current ones
              const mergedExistingFiles = existingFiles || currentExistingFiles;

              console.log(`Question ${question.id} - Merged existing files:`, {
                currentExistingFiles: currentExistingFiles.length,
                newExistingFiles: existingFiles ? existingFiles.length : 0,
                mergedExistingFiles: mergedExistingFiles.length
              });

              const fileAttachmentAnswer = {
                newFiles: files,
                existingFiles: mergedExistingFiles
              };
              onAnswerChange(question.id, fileAttachmentAnswer);
            }}
            multiple={question.multiple}
            value={currentAnswer && typeof currentAnswer === 'object' && 'newFiles' in currentAnswer
              ? currentAnswer.newFiles as FileList
              : null}
            existingFiles={existingFileAttachments}
            accept={question.fileTypes}
            questionId={question.id}
          />
        </div>
      );
    }
    case "signature":
      return (
        <div className="signature-question">
          <div className="signature-container">
            {/* Signature box removed in form builder */}
            <div className="signature-inputs-left">
              <label>Full Name</label>
              <NurtifyInput
                type="text"
                placeholder="Full Name"
                value={currentAnswer && typeof currentAnswer === "object" && "fullName" in currentAnswer ? currentAnswer.fullName || "" : ""}
                onChange={(e: ChangeEvent<HTMLInputElement>) =>
                  onAnswerChange(question.id, {
                    ...(typeof currentAnswer === 'object' && currentAnswer !== null ? currentAnswer : {}),
                    fullName: e.target.value,
                  })
                }
              />
              <label>Date</label>
              <NurtifyDateInput
                placeholder="Date"
                value={typeof currentAnswer === "object" && currentAnswer !== null && "date" in currentAnswer ? currentAnswer.date || "" : ""}
                onChange={(event: { target: { name: string; value: string } }) =>
                  onAnswerChange(question.id, {
                    ...(typeof currentAnswer === 'object' && currentAnswer !== null ? currentAnswer : {}),
                    date: event.target.value,
                  })
                }
              />
            </div>
          </div>
        </div>
      );
    case "table": {
      const handleAddRow = () => {
        if (currentAnswer && typeof currentAnswer === 'object' && 'rows' in currentAnswer && 'data' in currentAnswer) {
          const newRows = currentAnswer.rows + 1;
          const newData = [...currentAnswer.data, Array(Array.isArray(question.table?.columns) ? question.table.columns.length : 0).fill("")];
          onAnswerChange(question.id, { rows: newRows, data: newData });
        }
      };
      const handleDeleteRow = (rowIdx: number) => {
        if (currentAnswer && typeof currentAnswer === 'object' && 'rows' in currentAnswer && currentAnswer.rows > 1) {
          const newRows = currentAnswer.rows - 1;
          const newData = currentAnswer.data.filter((_, idx) => idx !== rowIdx);
          onAnswerChange(question.id, { rows: newRows, data: newData });
        }
      };
      const handleCellChange = (rowIdx: number, colIdx: number, value: string) => {
        if (currentAnswer && typeof currentAnswer === 'object' && 'data' in currentAnswer) {
          const newData = currentAnswer.data.map((row: string[], rIdx: number) =>
            rIdx === rowIdx ? row.map((cell, cIdx) => (cIdx === colIdx ? value : cell)) : row
          );
          onAnswerChange(question.id, { ...currentAnswer, data: newData });
        }
      };
      return (
        <div className="table-question" style={{ width: "95%" }}>
          <table className="form-table" style={{ width: "95%" }}>
            <thead>
              <tr>
                {Array.isArray(question.table?.columns) && question.table?.columns.map((col: string, idx: number) => (
                  <th key={idx} style={{ width: `${90 / ((question.table?.columns as string[]).length)}%` }}>{col}</th>
                ))}
                <th style={{ width: "10%" }}>Action</th>
              </tr>
            </thead>
            <tbody>
              {currentAnswer && typeof currentAnswer === 'object' && 'data' in currentAnswer && Array.isArray(currentAnswer.data) && currentAnswer.data.map((row: string[], rowIdx: number) => (
                <tr key={rowIdx}>
                  {row.map((cell, colIdx) => (
                    <td key={colIdx}>
                      <NurtifyInput
                        type="text"
                        value={cell}
                        onChange={(e: ChangeEvent<HTMLInputElement>) =>
                          handleCellChange(rowIdx, colIdx, e.target.value)
                        }
                      />
                    </td>
                  ))}
                  <td>
                    {rowIdx !== 0 && (
                      <button className="delete-row-icon" onClick={() => handleDeleteRow(rowIdx)}>
                        <Trash />
                      </button>
                    )}
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
          <button className="add-row-button" onClick={handleAddRow}>Add Row</button>
        </div>
      );
    }
    case "Expression":
      return (
        <div
          className="expression-question"
          style={{ pointerEvents: "none", opacity: 0.6 }}
        >
          <NurtifyTextArea
            value={question.expression || ""}
            required={question.required}
          />
        </div>
      );
    case "range":
      return (
        <div className="range-question">
          <div className="range-input">
            <NurtifyRange
              min={Number(question.range?.min) || 0}
              max={Number(question.range?.max) || 100}
              value={typeof currentAnswer === "number" ? currentAnswer : 0}
              onChange={(e: ChangeEvent<HTMLInputElement>) =>
                onAnswerChange(question.id, Number(e.target.value))
              }
              step={1}
              required={question.required}
            />
            <div className="range-labels">
              <span style={{ fontWeight: "bold", marginRight: "15px" }}>
                Min: {question.range?.min}
              </span>
              <span style={{ fontWeight: "bold" }}>
                Max: {question.range?.max}
              </span>
            </div>
            <div style={{ fontWeight: "bold", marginTop: "10px" }}>
              Current Value: {typeof currentAnswer === "number" ? currentAnswer : 0}
            </div>
          </div>
        </div>
      );
    default:
      return null;
  }
};

export default RebuildForm;
import { useState } from 'react';
import { useNavigate, Outlet } from 'react-router-dom';
import { useQuery } from '@tanstack/react-query';
import api from '@/services/api';
import './LiveChat.css';
import { MessageSquareText } from 'lucide-react';
import { useMarkChatMessagesAsReadMutation } from '@/hooks/livechat.query'; // Importez la mutation

interface Message {
  uuid: string;
  content: string;
  sender: {
    identifier: string;
    name: string;
    role: string;
  };
  created_at: string;
  is_system_message: boolean;
}

interface Chat {
  uuid: string;
  subject: string;
  status: string;
  patient: {
    identifier: string;
    first_name: string;
    last_name: string;
  };
  department: {
    name: string;
  };
  created_at: string;
  last_message?: Message;
}

const LiveChat = () => {
  const navigate = useNavigate();
  const [search, setSearch] = useState('');
  const markChatMessagesAsReadMutation = useMarkChatMessagesAsReadMutation(); // Ajoutez la mutation

  const { data: chatsData = {} } = useQuery({
    queryKey: ['org-chats'],
    queryFn: async () => {
      const response = await api.get('/live-chat/chats/');
      return response.data;
    },
  });
  const chats = Array.isArray(chatsData) ? chatsData : chatsData.results || [];

  const formatTime = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
  };

  const filteredChats = chats.filter(
    (c: Chat) =>
      c.patient.first_name.toLowerCase().includes(search.toLowerCase()) ||
      c.patient.last_name.toLowerCase().includes(search.toLowerCase()) ||
      c.subject.toLowerCase().includes(search.toLowerCase())
  );

  const handleChatClick = (chatUuid: string) => {
    // Marquer tous les messages comme lus avant de naviguer
    markChatMessagesAsReadMutation.mutate(chatUuid, {
      onSuccess: () => {
        navigate(`/org/dashboard/live-chat/${chatUuid}`);
      },
      onError: (error) => {
        console.error('Erreur lors du marquage des messages comme lus:', error);
        // Optionnel : Naviguer même en cas d'erreur pour ne pas bloquer l'utilisateur
        navigate(`/org/dashboard/live-chat/${chatUuid}`);
      },
    });
  };

  return (
    <div className="nurtify-org-livechat-main-layout">
      {/* Left: Chat List */}
      <div className="org-livechat-chat-list-panel">
        <div className="org-livechat-chat-list-header">
          <input
            type="text"
            className="org-livechat-chat-search-input"
            placeholder="search for conversation"
            value={search}
            onChange={(e) => setSearch(e.target.value)}
          />
        </div>
        <div className="org-livechat-chat-list-body">
          {filteredChats.map((c: Chat) => (
            <div
              key={c.uuid}
              className={`org-livechat-chat-list-item`}
              onClick={() => handleChatClick(c.uuid)} // Utilisez la fonction handleChatClick
            >
              <div className="org-livechat-chat-list-item-avatar">
                {/* Placeholder avatar */}
                <img
                  src={`https://ui-avatars.com/api/?name=${c.patient.first_name}+${c.patient.last_name}`}
                  alt="avatar"
                />
              </div>
              <div className="org-livechat-chat-list-item-info">
                <div className="org-livechat-chat-list-item-name">{c.patient.first_name} {c.patient.last_name}</div>
                <div className="org-livechat-chat-list-item-lastmsg">{c.last_message ? c.last_message.content : ''}</div>
                <div className="org-livechat-chat-list-item-meta">
                  <span className="org-livechat-chat-list-item-time">
                    {c.last_message ? formatTime(c.last_message.created_at) : ''}
                  </span>
                  <span className="org-livechat-chat-list-item-dept">Patient</span>
                </div>
              </div>
            </div>
          ))}
          {filteredChats.length === 0 && (
            <div className="select-chat-placeholder-container2">
              <MessageSquareText size={64} className="placeholder-icon" />
              <h2 className="placeholder-title">No conversation yet</h2>
            </div>
          )}
        </div>
      </div>

      {/* Right: Outlet for rendering the Chat Detail component */}
      <div className="org-livechat-outlet-container">
        <Outlet />
      </div>
    </div>
  );
};

export default LiveChat;
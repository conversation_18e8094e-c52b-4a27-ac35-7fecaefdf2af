import api from "@/services/api";
import type { Policy, PolicyCreateData } from "./types";

export const getAllPolicies = async (searchTerm?: string, studyUuid?: string) => {
  const params: Record<string, string> = {};
  if (searchTerm) params.search = searchTerm;
  if (studyUuid) params.study_uuid = studyUuid;

  const response = await api.get("policy/policies/", { params });
  return response.data;
};

export const getPolicyByUuid = async (uuid: string) => {
  const { data } = await api.get(`policy/policies/${uuid}/`);
  return data;
};

export const createPolicy = async (policyData: PolicyCreateData) => {
  const { data } = await api.post("policy/policies/", policyData, {
    headers: {
      "Content-Type": "multipart/form-data",
    },
  });
  return data;
};

export const updatePolicy = async (uuid: string, data: Partial<Policy>) => {
  const response = await api.patch(`policy/policies/${uuid}/`, data, {
    headers: {
      "Content-Type": "multipart/form-data",
    },
  });
  return response.data;
};

export const deletePolicyByUuid = async (uuid: string) => {
  const { data } = await api.delete(`policy/policies/${uuid}/`);
  return data;
};

export const getPoliciesByStudy = async (studyUuid: string) => {
  const { data } = await api.get(`policy/policies/by-study/${studyUuid}/`);
  return data;
};

export const getActivePoliciesByStudy = async (studyUuid: string) => {
  const { data } = await api.get(`policy/policies/by-study-active/${studyUuid}/`);
  return data;
};

export const getActivePolicies = async () => {
  const { data } = await api.get("policy/policies/active-only/");
  return data;
};

export const getSupersededPolicies = async () => {
  const { data } = await api.get("policy/policies/superseded-only/");
  return data;
};

export const getPatientAndStaffPolicies = async () => {
  const { data } = await api.get("policy/policies/patient-and-staff/");
  return data;
};

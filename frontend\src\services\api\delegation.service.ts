import api from "@/services/api";

export interface DelegationLogCreateData {
  study: string;
  department: string;
  team_member: string;
  study_task: string;
  selected_task_uuids?: string[];
}

export interface DelegationLogReapplyData {
  study_task: string;
  selected_task_uuids?: string[];
}

export interface DelegationLog {
  uuid: string;
  study: string;
  study_name: string;
  department: string;
  department_name: string;
  team_member: string;
  team_member_name: string;
  team_member_email: string;
  team_member_speciality: string;
  team_member_identifier: string;
  principal_investigator: string;
  pi_name: string;
  name: string;
  job_title: string;
  study_task: string;
  start_date: string;
  status: string;
  pi_notes: string | null;
  sponsor_notes: string | null;
  pi_responded_at: string | null;
  sponsor_responded_at: string | null;
  created_at: string;
  updated_at: string;
}

export interface SelectedTask {
  uuid: string;
  name: string;
  description: string;
  created_at: string;
  updated_at: string;
}

export interface DelegationLogListItem {
  uuid: string;
  study_name: string;
  department_name: string;
  team_member_name: string;
  pi_name: string;
  study_task: string;
  selected_tasks?: SelectedTask[];
  status: string;
  pi_notes: string | null;
  sponsor_notes: string | null;
  created_at: string;
  updated_at: string;
}

export const DelegationService = {
  createDelegationLog: async (data: DelegationLogCreateData): Promise<DelegationLog> => {
    const response = await api.post('/study/delegation-logs/', data);
    return response.data;
  },
  
  getDelegationLogs: async (): Promise<{ count: number; next: string | null; previous: string | null; results: DelegationLogListItem[] }> => {
    const response = await api.get('/study/delegation-logs/');
    return response.data;
  },

  getDelegationLogByUuid: async (delegationUuid: string): Promise<DelegationLog> => {
    const response = await api.get(`/study/delegation-logs/${delegationUuid}/`);
    return response.data;
  },

  piApproveDelegationLog: async (delegationUuid: string, piNotes: string): Promise<{ success: string; delegation_log: DelegationLog }> => {
    const response = await api.post(`/study/delegation-logs/${delegationUuid}/pi-approve/`, {
      pi_notes: piNotes
    });
    return response.data;
  },

  piRejectDelegationLog: async (delegationUuid: string, piNotes: string): Promise<{ success: string; delegation_log: DelegationLog }> => {
    const response = await api.post(`/study/delegation-logs/${delegationUuid}/pi-reject/`, {
      pi_notes: piNotes
    });
    return response.data;
  },

  sponsorApproveDelegationLog: async (delegationUuid: string, sponsorNotes: string): Promise<{ success: string; delegation_log: DelegationLog }> => {
    const response = await api.post(`/study/delegation-logs/${delegationUuid}/sponsor-approve/`, {
      sponsor_notes: sponsorNotes
    });
    return response.data;
  },

  sponsorRejectDelegationLog: async (delegationUuid: string, sponsorNotes: string): Promise<{ success: string; delegation_log: DelegationLog }> => {
    const response = await api.post(`/study/delegation-logs/${delegationUuid}/sponsor-reject/`, {
      sponsor_notes: sponsorNotes
    });
    return response.data;
  },

  reapplyDelegationLog: async (delegationUuid: string, data: DelegationLogReapplyData): Promise<{ success: string; delegation_log: DelegationLog }> => {
    const response = await api.post(`/study/delegation-logs/${delegationUuid}/reapply/`, data);
    return response.data;
  },
}; 
import React from "react";
import { useSymptomLogsQuery } from "@/hooks/symptom.query";
import { format } from "date-fns";
import "@/components/patient-clinical/AddSymptomModal.css";

interface SymptomLogsModalProps {
  isOpen: boolean;
  onClose: () => void;
  symptomUuid: string;
}

const SymptomLogsModal: React.FC<SymptomLogsModalProps> = ({
  isOpen,
  onClose,
  symptomUuid,
}) => {
  const { data: logs = [], isLoading } = useSymptomLogsQuery(symptomUuid);

  if (!isOpen) return null;

  return (
    <div className="modal-overlay">
      <div className="modal-content">
        <div className="modal-header">
          <h3>Symptom History</h3>
          <button className="close-button" onClick={onClose}>×</button>
        </div>
        <div className="modal-body">
          {isLoading ? (
            <div className="text-center">Loading history...</div>
          ) : logs.length > 0 ? (
            <div className="symptom-logs">
              {logs.map((log) => (
                <div key={log.uuid} className="log-entry mb-3 p-3 border rounded">
                  <div className="d-flex justify-content-between align-items-start">
                    <div>
                      <strong>Status Change:</strong> {log.old_status_display} → {log.new_status_display}
                    </div>
                    <small className="text-muted">
                      {format(new Date(log.changed_at), "MMM d, yyyy h:mm a")}
                    </small>
                  </div>
                  <div className="mt-2">
                    <strong>Changed by:</strong> {log.changed_by.first_name} {log.changed_by.last_name}
                  </div>
                  {log.resolved_date && (
                    <div className="mt-1">
                      <strong>Resolved on:</strong> {format(new Date(log.resolved_date), "MMM d, yyyy")}
                    </div>
                  )}
                  {log.notes && (
                    <div className="mt-2">
                      <strong>Notes:</strong>
                      <p className="mb-0">{log.notes}</p>
                    </div>
                  )}
                </div>
              ))}
            </div>
          ) : (
            <div className="text-center">No history available for this symptom.</div>
          )}
        </div>
        <div className="modal-footer">
          <button className="button -md btn-nurtify-lighter" onClick={onClose}>
            Close
          </button>
        </div>
      </div>
    </div>
  );
};

export default SymptomLogsModal; 
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import {
  getVitalSignsSummary,
  getVitalSigns,
  createVitalSign,
  updateVitalSign,
  deleteVitalSign,
  getVitalSignsChartData,
  getVitalSignsAlerts,
  getVitalSignsStatistics,
  VitalSignsFilters,
  UpdateVitalSignData,
  VitalSignsSummary
} from '../services/api/vital-signs.service';

// Query keys
export const vitalSignsKeys = {
  all: ['vitalSigns'] as const,
  summary: (patientId: string) => [...vitalSignsKeys.all, 'summary', patientId] as const,
  list: (filters: VitalSignsFilters) => [...vitalSignsKeys.all, 'list', filters] as const,
  chartData: (patientId: string, days: number) => [...vitalSignsKeys.all, 'chartData', patientId, days] as const,
  alerts: (threshold: number, limit: number) => [...vitalSignsKeys.all, 'alerts', threshold, limit] as const,
  statistics: (patientId?: string, days?: number) => [...vitalSignsKeys.all, 'statistics', patientId, days] as const,
};

// Hooks
export const useVitalSignsSummary = (patientId: string) => {
  console.log('🎯 useVitalSignsSummary called with patientId:', patientId);
  return useQuery({
    queryKey: vitalSignsKeys.summary(patientId),
    queryFn: () => getVitalSignsSummary(patientId),
    enabled: !!patientId,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
};

export const useVitalSigns = (filters: VitalSignsFilters = {}) => {
  console.log('🎯 useVitalSigns called with filters:', filters);
  return useQuery({
    queryKey: vitalSignsKeys.list(filters),
    queryFn: () => getVitalSigns(filters),
    staleTime: 2 * 60 * 1000, // 2 minutes
  });
};

export const useVitalSignsChartData = (patientId: string, days: number = 7) => {
  console.log('🎯 useVitalSignsChartData called with patientId:', patientId, 'days:', days);
  return useQuery({
    queryKey: vitalSignsKeys.chartData(patientId, days),
    queryFn: () => getVitalSignsChartData(patientId, days),
    enabled: !!patientId,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
};

export const useVitalSignsAlerts = (threshold: number = 7, limit: number = 50) => {
  return useQuery({
    queryKey: vitalSignsKeys.alerts(threshold, limit),
    queryFn: () => getVitalSignsAlerts(threshold, limit),
    staleTime: 1 * 60 * 1000, // 1 minute
    refetchInterval: 2 * 60 * 1000, // Refetch every 2 minutes
  });
};

export const useVitalSignsStatistics = (patientId?: string, days: number = 30) => {
  return useQuery({
    queryKey: vitalSignsKeys.statistics(patientId, days),
    queryFn: () => getVitalSignsStatistics(patientId, days),
    staleTime: 10 * 60 * 1000, // 10 minutes
  });
};

// Mutations
export const useCreateVitalSign = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: createVitalSign,
    onSuccess: (data) => {
      // Invalidate and refetch vital signs data
      queryClient.invalidateQueries({ queryKey: vitalSignsKeys.all });
      
      // Update summary cache if available
      const summaryKey = vitalSignsKeys.summary(data.patient.uuid);
      queryClient.setQueryData(summaryKey, (oldData: VitalSignsSummary | undefined) => {
        if (oldData) {
          return {
            ...oldData,
            total_records: oldData.total_records + 1,
            latest_recorded_at: data.recorded_at,
            latest_temperature: data.temperature,
            latest_heart_rate: data.heart_rate,
            latest_systolic_bp: data.systolic_bp,
            latest_diastolic_bp: data.diastolic_bp,
            latest_respiratory_rate: data.respiratory_rate,
            latest_oxygen_saturation: data.oxygen_saturation,
            latest_consciousness_level: data.consciousness_level,
            latest_weight: data.weight,
            latest_news_score: data.news_score,
            latest_news_severity: data.news_severity,
          };
        }
        return oldData;
      });
    },
  });
};

export const useUpdateVitalSign = () => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: ({ uuid, data }: { uuid: string; data: UpdateVitalSignData }) => 
      updateVitalSign(uuid, data),
    onSuccess: (updatedVitalSign) => {
      // Invalidate and refetch vital signs data
      queryClient.invalidateQueries({ queryKey: vitalSignsKeys.all });
      
      // Update the specific vital sign in cache if it exists
      queryClient.setQueryData(
        vitalSignsKeys.list({ patient_id: updatedVitalSign.patient.uuid }),
        (oldData: any) => {
          if (!oldData) return oldData;
          return {
            ...oldData,
            results: oldData.results.map((vitalSign: any) =>
              vitalSign.uuid === updatedVitalSign.uuid ? updatedVitalSign : vitalSign
            )
          };
        }
      );
    },
  });
};

export const useDeleteVitalSign = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: deleteVitalSign,
    onSuccess: () => {
      // Invalidate and refetch vital signs data
      queryClient.invalidateQueries({ queryKey: vitalSignsKeys.all });
    },
  });
};

// Utility hook for patient vital signs
export const usePatientVitalSigns = (patientId: string) => {
  const summaryQuery = useVitalSignsSummary(patientId);
  const vitalSignsQuery = useVitalSigns({ 
    patient_id: patientId, 
    limit: 10, 
    ordering: '-recorded_at' 
  });
  const chartDataQuery = useVitalSignsChartData(patientId, 7);

  console.log('🎯 usePatientVitalSigns debug:', {
    patientId,
    summaryLoading: summaryQuery.isLoading,
    vitalSignsLoading: vitalSignsQuery.isLoading,
    chartDataLoading: chartDataQuery.isLoading,
    chartData: chartDataQuery.data,
    chartError: chartDataQuery.error
  });

  return {
    summary: summaryQuery.data,
    vitalSigns: vitalSignsQuery.data?.results || [],
    chartData: chartDataQuery.data,
    isLoading: summaryQuery.isLoading || vitalSignsQuery.isLoading || chartDataQuery.isLoading,
    isError: summaryQuery.isError || vitalSignsQuery.isError || chartDataQuery.isError,
    error: summaryQuery.error || vitalSignsQuery.error || chartDataQuery.error,
    refetch: () => {
      summaryQuery.refetch();
      vitalSignsQuery.refetch();
      chartDataQuery.refetch();
    },
  };
}; 
import { Link } from "react-router-dom";

interface FooterLink {
  href: string;
  label: string;
}

interface FooterSection {
  title: string;
  links: FooterLink[];
}

interface FooterLinksProps {
  allClasses?: string;
}

export default function FooterLinks({ allClasses }: FooterLinksProps) {
  const footerLinks: FooterSection[] = [
    {
      title: "For Patient",
      links: [
        { href: "/contact-us", label: "Join Trial" },
        { href: "https://bepartofresearch.nihr.ac.uk/results/search-results", label: "Search Clinical Trial" },
        { href: "/blog", label: "Resources"},
        { href: "/contact-us", label: "Contact Us" },
      ],
    },
    {
      title: "For Researchers",
      links: [
        { href: "/contact-us", label: "Contact us" },
        { href: "/contact-us", label: "Get Envolved" },
        { href: "/blog", label: "Blog" },
      ],
    },
    {
      title: "Company",
      links: [
        { href: "/about-us", label: "About Us" },
        { href: "/about-us#career", label: "Careers" },
        { href: "/blog", label: "Blog" },
        { href:"https://meetings.hubspot.com/nurtify?embed=true",label:"Book Demo"}
      ],
    },
    {
      title: "Legal",
      links: [
        { href: "/privacy", label: "Privacy Policy" },
        { href: "/contact-us", label: "FAQs" },
        { href: "/terms-and-conditions", label: "Terms of Service" },
        { href: "/cookies", label: "Cookies Policy" },
      ],
    },
  ];
  
  return (
    <>
      {footerLinks.map((elm, i) => (
        <div key={i} className="col-xl-2 col-lg-4 col-md-6">
          <div className={`${allClasses ? allClasses : ""}`}>{elm.title}</div>
          <div className="d-flex y-gap-10 flex-column">
            {elm.links.map((itm, index) => (
              <Link key={index} to={itm.href}>
                {itm.label}
              </Link>
            ))}
          </div>
        </div>
      ))}
    </>
  );
}

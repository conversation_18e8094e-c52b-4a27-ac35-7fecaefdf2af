import React, { useEffect, useState } from "react";
import "./CookiesConsent.css";

const COOKIES_CONSENT_KEY = "cookiesConsentAccepted";

const CookiesConsent: React.FC = () => {
  const [visible, setVisible] = useState(false);

  useEffect(() => {
    const consent = localStorage.getItem(COOKIES_CONSENT_KEY);
    if (!consent) {
      const timer = setTimeout(() => {
        setVisible(true);
      }, 3000);
      return () => clearTimeout(timer);
    }
  }, []);

  const handleAccept = () => {
    localStorage.setItem(COOKIES_CONSENT_KEY, "true");
    setVisible(false);
  };

  if (!visible) return null;

  return (
    <div className="cookies-consent-banner">
      <div className="card cookies-consent-card">
        <div className="card-wrapper">
          <div className="card-content" style={{ width: "100%" }}>
            <div className="card-text">
              <h5 style={{ fontSize: "1rem", fontWeight: 600, marginBottom: "0.5rem", textAlign: "left", marginRight: "auto", color: "#3f3f46" }}>
                Your privacy is important to us
              </h5>
              <p style={{ width: "100%", marginBottom: "1rem", fontSize: "1rem", textAlign: "justify" }}>
                We process your personal information to measure and improve our sites and
                services, to assist our campaigns and to provide personalised content.
                <br />
                For more information see our{" "}
                <a
                  href="/privacy"
                  target="_blank"
                  rel="noopener noreferrer"
                  style={{
                    marginBottom: "0.5rem",
                    fontSize: "1rem",
                    cursor: "pointer",
                    fontWeight: 600,
                    transition: "color 0.2s",
                    textDecoration: "underline",
                    textUnderlineOffset: "2px",
                    color: "#3f3f46"
                  }}
                  onMouseOver={e => (e.currentTarget.style.color = "#634647")}
                  onMouseOut={e => (e.currentTarget.style.color = "#3f3f46")}
                >
                  Privacy Policy
                </a>
              </p>
            </div>
            <button type="button" className="btn-accept" onClick={handleAccept}>
              Accept
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default CookiesConsent;

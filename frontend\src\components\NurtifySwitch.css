.nurtify-switch-wrapper {
    display: flex;
    align-items: center;
    gap: 12px;
}

.nurtify-switch-label {
    font-size: 14px;
    color: #6A7A99;
}

.nurtify-switch {
    position: relative;
    display: inline-block;
    width: 80px;
    height: 34px;
    cursor: pointer;
}

.nurtify-switch-slider {
    position: relative;
    width: 100%;
    height: 100%;
    background-color: #DFF3F5;
    border-radius: 34px;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    padding: 4px;
}

.nurtify-switch-slider.checked {
    background-color: #37B7C3;
}

.nurtify-switch-circle {
    position: absolute;
    height: 26px;
    width: 26px;
    left: 4px;
    background-color: white;
    border-radius: 50%;
    transition: all 0.3s ease;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.nurtify-switch-slider.checked .nurtify-switch-circle {
    transform: translateX(46px);
}

.nurtify-switch-text {
    color: #6A7A99;
    font-size: 12px;
    position: absolute;
    width: 100%;
    text-align: center;
    transition: color 0.3s ease;
}

.nurtify-switch-slider.checked .nurtify-switch-text {
    color: white;
}

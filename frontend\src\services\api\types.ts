export interface LoginCredentials {
  email: string;
  password: string;
}

export interface AuthResponse {
  token: string;
  refreshToken: string;
  user: {
    id: string;
    name: string;
    email: string;
    role: string;
  };
}

export interface User {
  id: number;
  uuid: string;
  identifier: string;
  first_name: string;
  last_name: string;
  email: string;
  phone_number: string;
  gender: string;
  speciality?: string;
  image?: File | null;
  registration_body?: string;
  created_at: string;
  updated_at: string;
  department: {
    uuid: string;
    id: number;
    name: string;
  };
  hospital: {
    uuid: string;
    id: number;
    name: string;
  };
  is_superuser?: boolean;
  is_hospital_admin?: boolean;
  is_admin?: boolean;
  is_staff?: boolean;
  user_type?: string;
  organization_name?: string;
}

export interface ApiError {
  message: string;
  code?: string;
  status?: number;
}

export interface Hospital {
  uuid?: string;
  name?: string;
  phone_number?: string;
  extension?: string;
  point_of_contact_Person?: string;
  primary_address?: string;
  secondary_address?: string;
  postcode?: string;
  country?: string;
  department_count?: string;
  departments?: Department[];
}

export interface HospitalSelectList extends Hospital {
  id?: number;
}

export interface Department {
  uuid?: string;
  name?: string;
  hospital?: string | number | Hospital;
  hospital_uuid?: string;
  phone_number?: string;
  extension?: string;
  primary_address?: string;
  secondary_address?: string;
  postcode?: string;
  country?: string;
}

export interface Counts {
  department_staffs?: string | number;
  department_admins?: string | number;
  hospital_admins?: string | number;
  patients?: string | number;
  sponsors?: string | number;
  studies?: string | number;
  hospitals?: string | number;
  policies?: string | number;
  departments?: string | number;
  forms?: string | number;
}

export interface Data {
  departments: object[];
  users: object[];
}

export interface Analytics {
  counts: Counts;
  data: Data;
}

export interface PatientsAnalytics {
  total: number;
  period_count?: number;
  prev_period_count?: number;
  difference?: number;
  period: string;
}

export interface PoliciesAnalytics {
  total: number;
  period_count?: number;
  prev_period_count?: number;
  difference?: number;
  period: string;
}

export interface SponsorsAnalytics {
  total: number;
  period_count?: number;
  prev_period_count?: number;
  difference?: number;
  period: string;
}

export interface StaffSpeciality {
  speciality: string;
  count: number;
}

export interface StaffAnalytics {
  total: number;
  period_count?: number;
  prev_period_count?: number;
  difference?: number;
  period: string;
  by_speciality: StaffSpeciality[];
}

export interface StudyInvitation {
  uuid: string;
  study: string | null;
  invited_by: string | null;
  status: string;
}

export interface PIAssignment {
  uuid: string;
  study: string;
  study_name?: string;
  department: string;
  department_name?: string;
  hospital_name?: string;
  investigator: string;
  investigator_name?: string;
  investigator_email?: string;
  investigator_speciality?: string;
  status: "pending" | "accepted" | "rejected";
  assigned_at?: string;
  accepted_at?: string;
  rejected_at?: string;
  responded_at?: string;
  sponsor_notes?: string;
}

export interface PIAssignmentResponse {
  success: string;
  pi_assignment?: PIAssignment;
}

export interface StudiesAnalytics {
  total: number;
  accepted: number;
  rejected: number;
  last_3: StudyInvitation[];
}

export interface RequestItem {
  first_name: string;
  last_name: string;
  nhs_number: string;
  status: string;
  source: "Doctor" | "Patient";
  created_at: string;
}

export interface RequestsAnalytics {
  total: number;
  accepted: number;
  pending: number;
  rejected: number;
  last_5: RequestItem[];
}

export interface DepartmentItem{
  uuid: string;
  name: string;
  admin_name: string;
  num_staff: number;
  num_patients: number;
  num_studies: number;
}

export interface departmentsAnalitics {
  departments : DepartmentItem[];
}

export interface Policy {
  id: number;
  attach_content: File | string | null;
  uuid: string;
  title: string;
  category: string;
  author_name: string;
  job_title: string;
  description: string;
  created_at: string;
  updated_at: string;
  study: {
    uuid: string;
    name: string;
    iras: string;
    description: string;
  };
  date_c?: string;
  policy_state: 'active' | 'superseded';
  access_level: 'patientAndStaff' | 'staff';
  policy_version: number;
  attachment_url?: string;
}

export type PolicyCreateData = FormData;

export type UserCreateData = FormData;

export interface FormPayload {
  name: string;
  description: string;
  categories: string[]; // Array of UUIDs
  privacy: string; // Must match choices from Django model: "public", "all hospital staff", "department staff", "private", "set password"
  password?: string;
  structure: string | Record<string, unknown>; // Will be serialized as JSON string on the backend
  user_identifier?: string;
  form_version_uuid?: string; // Used for form updates
  study: string | null;
}

export interface Form {
  uuid: string;
  name: string;
  description?: string;
  privacy: string;
  password?: string;
  user: {
    identifier: string;
    first_name: string;
    last_name: string;
    email: string;
  };
  status: "accepted" | "rejected" | "pending" | "archived";
  created_at: string;
  updated_at?: string;
  active_version?: FormVersionResponse;
}

export interface FormVersionResponse {
  uuid: string;
  version: number;
  description: string;
  categories: string[]; // Array of UUIDs
  privacy: string;
  password?: string;
  structure: string; // JSON string from the backend
  structure_data?: Record<string, unknown>; // Parsed structure
  status: "accepted" | "rejected" | "pending" | "archived";
  created_at?: string;
  is_active: boolean;
  form_structure?: Record<string, unknown>; // Used in some responses
}

export interface FormStatusUpdatePayload {
  uuid: string;
  status: "accepted" | "rejected" | "pending";
}

export interface FormUpdatePayload {
  name?: string;
  description?: string;
  categories?: string[];
  privacy?: string;
  password?: string;
  structure?: Record<string, unknown>;
  form_version_uuid?: string;
}

export interface Tag {
  uuid: string;
  name: string;
  created_at: string;
}

export interface SubmissionPayload {
  form: string;
  user: {
    identifier: string;
    first_name: string;
    last_name: string;
    email: string;
  };
  patient_uuid?: string;
  submission: Record<string, unknown>;
  is_completed?: boolean;
  existing_attachments?: string[]; // Add support for preserving existing attachments
  original_submission?: Record<string, unknown>; // Add support for original submission data
  attachments?: File[] | {
    image_attachments?: File[];
    video_attachments?: File[];
    document_attachments?: File[];
  };
}

export interface FormSubmissionAttachment {
  uuid: string;
  file: string;
  file_type: 'image' | 'video' | 'document';
  uploaded_at: string;
  uploaded_by: {
    identifier: string;
    first_name: string;
    last_name: string;
    email: string;
  };
}

export interface FormSubmission {
  updated_by: { identifier: string; first_name: string; last_name: string; email: string; } | undefined;
  updated_at: string;
  uuid: string;
  form: string | {
    uuid: string;
    name: string;
  };
  user: number | {
    id: number;
    identifier: string;
    first_name: string;
    last_name: string;
    email: string;
  };
  patient_uuid?: string;
  is_completed: boolean;
  submission: Record<string, unknown>;
  created_at: string;
  final_submission: boolean;
  last_updated_by?: {
    identifier: string;
    first_name: string;
    last_name: string;
    email: string;
  };
  last_updated_at: string;
  attachments?: FormSubmissionAttachment[];
}

export interface Snippet {
  uuid: string;
  name: string;
  description: string;
  content: string;
  created_at: string;
  user: {
    id: number;
    identifier: string;
    first_name: string;
    last_name: string;
    email: string;
  } | number;

}

export interface SnippetCreateData {
  name: string;
  description: string;
  content: string;
}


export interface Allergy {
  uuid?: string;
  name: string;
}

export interface Patient {
  uuid: string;
  nhs_number: string;
  first_name: string;
  last_name: string;
  date_of_birth: string;
  gender: string;
  medical_record_number?: string;
  address?: string;
  phone_number?: string;
  email?: string;
  next_of_kin_name?: string;
  next_of_kin_phone?: string;
  medical_history?: string;
  allergies: Allergy[];
  profile_picture?: string;
  profile_picture_url?: string;
  created_at: string;
  updated_at: string;
  forms_uuid: string[];
}

export interface PatientCreateData {
  first_name: string;
  last_name: string;
  date_of_birth: string;
  gender: string;
  nhs_number: string;
  medical_record_number?: string;
  address?: string;
  phone_number?: string;
  email?: string;
  next_of_kin_name?: string;
  next_of_kin_phone?: string;
  medical_history?: string;
  allergies?: Allergy[];
  department_uuid?: string;
  ethnic_background?: string;
  postcode: string; // Added postcode to patient creation data
}

export interface PatientSearchParams {
  nhs_number?: string;
  first_name?: string;
  last_name?: string;
  full_name?: string;
  date_of_birth?: string;
  medical_record_number?: string;
  gender?: string;
  study?: string;
}

export interface PatientDetails {
  first_name: string;
  last_name: string;
  email: string;
  nhs_number: string;
}

export interface TreatedByDetails {
  first_name: string;
  last_name: string;
  email: string;
  organization_name: string;
}

export interface Conversation {
  uuid: string;
  status: "pending" | "inprogress" | "forwarded_to_finance" | "rejected" | "reimbursed";
  reimbursement_status: "not_received" | "received";
  patient: string;
  patient_details: {
    first_name: string;
    last_name: string;
    email: string;
  };
  treated_by: string;
  treated_by_details: {
    first_name: string;
    last_name: string;
    email: string;
    organization_name: string;
  };
  questions: string;
  comments: string;
  last_update_person: string;
  created_at: string;
  updated_at: string;
  attach_content: string;
  attachment_url: string;
}

export interface ConversationUpdateData {
  status?: "pending" | "inprogress" | "forwarded_to_finance" | "rejected" | "reimbursed";
  reimbursement_status?: "not_received" | "received";
  comments?: string;
  questions?: string;
  attach_content?: string;
}

const conversationData: FormData = new FormData();
conversationData.append('attach_content', 'File | null');


export interface ConversationCreateData {
  uuid?: string;
  status: "pending" | "inprogress" | "forwarded_to_finance" | "rejected" | "reimbursed";
  reimbursement_status: "not_received" | "received";
  treated_by: string;
  questions: string;
  patient: string;
  last_update_person?: string;
  comments?: string;
  created_at?: string;
  updated_at?: string;
  attach_content?: string;
}
export interface Prescription {
  uuid?: string;
  patient_uuid: string;
  drug_name: string;
  height: string;
  weight: string;
  dose: string;
  unit: string;
  route: string;
  frequency: string;
  allergic_patient: boolean;
  status?: string;
  prescriber_identifier?: string;
  prescribed_at?: string;
}

export interface TagPayload {
  name: string;
  color?: string;
  description?: string;
}

export interface PrescriptionWithDetails extends Omit<Prescription, 'patient_uuid' | 'prescriber_identifier'> {
  patient: {
    uuid: string;
    nhs_number?: string;
    first_name: string;
    last_name: string;
  };
  prescribed_by?: {
    identifier: string;
    first_name: string;
    last_name: string;
  };
  prescribed_at?: string;

  prepared_by?: {
    identifier: string;
    first_name: string;
    last_name: string;
  };
  prepared_at?: string;

  collected_by?: {
    identifier: string;
    first_name: string;
    last_name: string;
  };
  collected_at?: string;

  administered_by?: {
    identifier: string;
    first_name: string;
    last_name: string;
  };
  administered_at?: string;

  checked_by?: {
    identifier: string;
    first_name: string;
    last_name: string;
  };
  checked_at?: string;
}

export interface PatientVisitToday {
  uuid: string;
  patient_details: {
    first_name: string;
    last_name: string;
    nhs_number: string;
    email: string;
    phone_number: string;
    date_of_birth?: string;
    gender?: string;
    profile_picture?: string;
    profile_picture_url?: string;
    created_at?: string;
    updated_at?: string;
  };
  visit_details: Array<{
    uuid: string;
    name: string;
    location?: string;
    visit_status: string;
    study_name: string;
    activities: string[];
    registration_status: string;
    comments: string;
    date: string;
    time: string;
    leading_team: string;
    nurse_details?: {
      identifier: string;
      first_name: string;
      last_name: string;
      organization_name: string;

    };
  }>;
}

export interface UpdateVisitData {
  registration_status: "Not Arrived" | "In Hospital" | "Discharged";
  location: string;
  nurse_identifier: string;
}

export interface UpdateVisitResponse {
  message: string;
  data: {
    uuid: string;
    registration_status: string;
    date: string;
    time: string;
    location: string;
    patient_details: {
      uuid: string;
      first_name: string;
      last_name: string;
      nhs_number: string;
    };
    nurse_details: {
      identifier: string;
      first_name: string;
      last_name: string;
      organization_name: string;
    };
    visit_status: string;
    comments: string;
    leading_team: string;
  };
}

export interface RegistrationStatusLog {
  new_status: string;
  updated_at: string;
  updated_by_details: {
    first_name: string;
    last_name: string;
  };
}
export interface FormSubmissionQuery {
  uuid: string;
  form_submission_uuid: string;
  question_number: string;
  description: string;
  priority: 'low' | 'medium' | 'high';
  created_by: {
    identifier: string;
    first_name: string;
    last_name: string;
    email: string;
  };
  created_at: string;
  resolved_at: string | null;
  is_resolved: boolean;
  resolution_notes: string | null;
}

export interface FormSubmissionQueryCreate {
  form_submission_uuid: string;
  question_number: string;
  description: string;
  priority: 'low' | 'medium' | 'high';
}

export interface FormSubmissionQueryUpdate {
  description?: string;
  priority?: 'low' | 'medium' | 'high';
}

export interface FormSubmissionQueryResolve {
  resolution_notes: string;
}

export interface QueryResponse {
  uuid: string;
  query: string;
  responder: {
    identifier: string;
    first_name: string;
    last_name: string;
    email: string;
  };
  message: string;
  created_at: string;
  is_clarification: boolean;
}

export interface QueryResponseCreate {
  query: string;
  message: string;
  is_clarification?: boolean;
}

export interface QueryResponseUpdate {
  message?: string;
  is_clarification?: boolean;
}
export interface PatientAccessLog {
  uuid: string;
  patient: number;  // Changed from object to number (patient ID)
  patient_name: string;
  accessed_by: number;
  accessed_by_name: string;
  access_type: 'view' | 'edit' | 'delete' | 'create' | 'print' | 'export';
  ip_address: string;
  created_at: string;
}

export interface AddPatientAccessLogData {
  patient_uuid: string;
  access_type?: 'view' | 'edit' | 'delete' | 'create' | 'print' | 'export';
}

export interface MedicalHistoryCondition {
  uuid: string;
  condition: string;
  start_date: string;
  status: 'resolved' | 'active' | 'pending';
  resolved_date?: string;
}

export interface MedicalHistory {
  uuid: string;
  patient: number;
  conditions: MedicalHistoryCondition[];
  created_at: string;
  updated_at: string;
}

export interface MedicalHistoryConditionCreate {
  condition: string;
  start_date: string;
  status: 'current' | 'resolved';
  resolved_date?: string;
}

export interface CreateMedicalHistoryData {
  conditions_data: MedicalHistoryConditionCreate[];
}

export interface PaginatedResponse<T> {
  count: number;
  next: string | null;
  previous: string | null;
  results: T[];
}

export interface ConcomitantMedication {
  uuid: string;
  patient: string;
  medication: string;
  non_drug_therapy?: string;
  indication: string;
  dose: number;
  dose_units: string;
  dose_units_display: string;
  schedule: string;
  schedule_display: string;
  dose_form: string;
  dose_form_display: string;
  route: string;
  route_display: string;
  start_date: string;
  end_date?: string;
  is_baseline: boolean;
  is_continuing: boolean;
  created_at: string;
  updated_at: string;
  added_by_patient: boolean | null;
}

export interface CreateConcomitantMedicationData {
  patient_uuid: string;
  medication: string;
  non_drug_therapy?: string;
  indication: string;
  dose: number;
  dose_units: string; // 1-7
  schedule: string; // 1-12
  dose_form: string; // 1-15, 99
  route: string; // 1-14
  start_date: string; // YYYY-MM-DD
  end_date?: string; // YYYY-MM-DD
  is_baseline: boolean;
  is_continuing: boolean;
}

export type WarningSeverity = 'low' | 'medium' | 'high' | 'critical';

export interface PatientWarning {
  uuid: string;
  patient: string;
  patient_uuid: string;
  name: string;
  description: string;
  severity: WarningSeverity;
  severity_display: string;
  is_active: boolean;
  created_at: string;
  updated_at: string;
  created_by: string;
  created_by_name: string;
}

export interface PatientWarningCreate {
  patient_uuid: string;
  name: string;
  description: string;
  severity: WarningSeverity;
  is_active: boolean;
}

export interface PatientWarningListResponse {
  results: PatientWarning[];
  count: number;
  next: string | null;
  previous: string | null;
}

export interface ConversationLog {
  uuid: string;
  conversation: string;
  conversation_details: {
    uuid: string;
    status: string;
    reimbursement_status: string;
  };
  field_changed: string;
  old_value: string;
  new_value: string;
  changed_by: string;
  changed_at: string;
}

export interface SponsorOrg {
  uuid?: string;
  name?: string;
  phone_number?: string;
  extension?: string;
  point_of_contact_Person?: string;
  primary_address?: string;
  secondary_address?: string;
  postcode?: string;
  country?: string;
  sponsor_count?: string;
  organization_type?: string;
}

export interface SponsorPatient {
  patient_uuid: string;
  patient_initials: string;
  nhs_number: string;
  patient_study_id: string;
  year_of_birth: number;
  site_name: string;
  hospital_name: string;
  enrollment_status: string;
  enrollment_date: string;
  study_name: string;
}

export interface SponsorPatientsResponse {
  count: number;
  next: string | null;
  previous: string | null;
  results: SponsorPatient[];
}

export interface SponsorPatientsFilters {
  hospital?: string;
  site?: string;
  search?: string;
}

// Report Types
export interface Report {
  uuid: string;
  patient: string;
  type: string;
  content_type: "TEXT" | "PDF";
  status: "REVIEWED" | "NOT_REVIEWED";
  text_content?: string;
  exam_date: string;
  uploaded_by: {
    uuid: string;
    identifier: string;
    first_name: string;
    last_name: string;
    email: string;
  };
  uploaded_at: string;
  sub_investigator?: {
    uuid: string;
    identifier: string;
    first_name: string;
    last_name: string;
    email: string;
  } | null;
  sub_investigator_name?: string;
  sub_investigator_last_name?: string;
  attach_content?: string;
  document_url?: string;
  attachment_url?: string;
}

export interface ReportCreateData {
  patient: string;
  type: string;
  content_type: "TEXT" | "PDF";
  text_content?: string;
  exam_date: string;
  sub_investigator?: string;
  attach_content?: File;
}

export interface ReportUpdateData {
  type?: string;
  content_type?: "TEXT" | "PDF";
  text_content?: string;
  exam_date?: string;
  sub_investigator?: string;
  attach_content?: File;
}

export interface ReviewReport {
  uuid: string;
  report: string;
  section_title: string;
  category: "NORMAL" | "ABNORMAL";
  abnormal_type?: "CLINICALLY_SIGNIFICANT" | "NOT_CLINICALLY_SIGNIFICANT";
  comment?: string;
  reviewed_by: {
    uuid: string;
    identifier: string;
    first_name: string;
    last_name: string;
    email: string;
  };
  reviewed_at: string;
}

export interface ReviewReportCreateData {
  report: string;
  section_title: string;
  category: "NORMAL" | "ABNORMAL";
  abnormal_type?: "CLINICALLY_SIGNIFICANT" | "NOT_CLINICALLY_SIGNIFICANT";
  comment?: string;
  reviewed_by?: string;
}

export interface ReportContent {
  content_type: "TEXT" | "PDF";
  content: string;
}

export interface ReportStats {
  total_reports: number;
  reports_by_type: Record<string, number>;
}

export interface ReportTypes {
  report_types: string[];
  content_types: string[];
}

import { Conversation, ConversationUpdateData, ConversationLog } from "./types";
import api from "@/services/api.ts";


export const ConversationService = {
  getAll: async (): Promise<Conversation[]> => {
    const response = await api.get("patient/conversations/");
    return response.data;
  },

  getById: async (uuid: string): Promise<Conversation> => {
    const response = await api.get(`patient/conversations/${uuid}/`);
    return response.data;
  },

  create: async (data: FormData): Promise<Conversation> => {
    const response = await api.post("patient/conversations/", data, {
      headers: {
        "Content-Type": "multipart/form-data",
      },
    });
    return response.data;
  },

  // update: async (uuid: string, data: ConversationUpdateData): Promise<Conversation> => {
  //   const response = await api.put(`patient/conversations/${uuid}/`, data);
  //   return response.data;
  // },
    // ... other methods
  update: async (uuid: string, data: Partial<ConversationUpdateData>): Promise<Conversation> => {
    const response = await api.patch(`/patient/conversations/${uuid}/`, data);
    return response.data;
  },


  patch: async (uuid: string, data: ConversationUpdateData): Promise<Conversation[]> => {
    const response = await api.patch(`patient/conversations/${uuid}/`, data);
    return response.data;
  },

  delete: async (uuid: string): Promise<void> => {
    await api.delete(`patient/conversations/${uuid}/`);
  },

  getPending: async (): Promise<Conversation[]> => {
    const response = await api.get("patient/conversations/pending/");
    return response.data;
  },
  getCompleted: async (): Promise<Conversation[]> => {
    const response = await api.get("patient/conversations/completed/");
    return response.data;
  },
  getInProgress: async (): Promise<Conversation[]> => {
    const response = await api.get("patient/conversations/inprogress/");
    return response.data;
  },
  getRejected: async (): Promise<Conversation[]> => {
    const response = await api.get("patient/conversations/rejected/");
    return response.data;
  },

  setInProgress: async (uuid: string): Promise<Conversation> => {
    const response = await api.patch(`patient/conversations/${uuid}/set-inprogress/`);
    return response.data;
  },

  setCompleted: async (uuid: string, data: { comment: string }): Promise<Conversation> => {
    const response = await api.patch(`patient/conversations/${uuid}/set-completed/`, data);
    return response.data;
  },

  setRejected: async (uuid: string, data: { comment: string }): Promise<Conversation> => {
    const response = await api.patch(`patient/conversations/${uuid}/set-rejected/`, data);
    return response.data;
  },

  getByPatient: async (patientUuid: string): Promise<Conversation[]> => {
    const response = await api.get(`/patient/conversations/patient/${patientUuid}/`);
    return response.data;
  },

  getForwarded: async (): Promise<Conversation[]> => {
    const response = await api.get("/patient/conversations/forwarded_to_finance/");
    return response.data;
  },

  getReimbursed: async (): Promise<Conversation[]> => {
    const response = await api.get("/patient/conversations/reimbursed/");
    return response.data;
  },

  setForwardedToFinance: async (uuid: string, data: { comment: string }): Promise<Conversation> => {
    const response = await api.patch(`patient/conversations/${uuid}/set-forwarded-to-finance/`, data);
    return response.data;
  },

  setReimbursed: async (uuid: string, data: { comment: string }): Promise<Conversation> => {
    const response = await api.patch(`patient/conversations/${uuid}/set-reimbursed/`, data);
    return response.data;
  },

  setReimbursementStatus: async (uuid: string, status: "not_received" | "received"): Promise<Conversation> => {
    const response = await api.patch(`patient/conversations/${uuid}/set-reimbursement-status/`, { status });
    return response.data;
  },

  updateReimbursementStatus: async (uuid: string, data: { reimbursement_status: "received" | "not_received" }): Promise<Conversation> => {
    const response = await api.patch(`patient/conversations/${uuid}/update-reimbursement-status/`, data);
    return response.data;
  },

  getLogs: async (uuid: string): Promise<ConversationLog[]> => {
    const response = await api.get(`/patient/conversations/${uuid}/logs/`);
    return response.data;
  }
};


/* Sponsor Dashboard Container */
.sponsor-dashboard-container {
    padding: 0;
    background-color: transparent;
    min-height: 100vh;
    width: 100%;
    max-width: none;
    margin: 0;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
  }

  /* Header Section */
  .sponsor-dashboard-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 30px;
    padding: 20px 20px 0 20px;
  }

  .sponsor-welcome-section {
    flex: 1;
  }

  .sponsor-welcome-title {
    font-size: 32px;
    font-weight: 600;
    color: #333333;
    margin: 0 0 8px 0;
  }

  .sponsor-name-highlight {
    color: #4ECDC4;
  }

  .sponsor-welcome-subtitle {
    color: #666666;
    font-size: 16px;
    margin: 0;
  }

  .sponsor-header-actions {
    display: flex;
    align-items: center;
    gap: 16px;
  }

  .sponsor-study-site-dropdown {
    position: relative;
    display: flex;
    align-items: center;
  }

  .sponsor-dropdown-select {
    background: white;
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    padding: 8px 32px 8px 12px;
    font-size: 14px;
    color: #333333;
    cursor: pointer;
    appearance: none;
    min-width: 120px;
  }

  .sponsor-dropdown-icon {
    position: absolute;
    right: 8px;
    color: #666666;
    pointer-events: none;
  }

  .sponsor-create-study-btn {
    background: #4ECDC4;
    color: white;
    border: none;
    border-radius: 8px;
    padding: 10px 20px;
    font-size: 14px;
    font-weight: 600;
    cursor: pointer;
    transition: background-color 0.2s ease;
  }

  .sponsor-create-study-btn:hover {
    background: #3bb3ac;
  }

  /* Study Safety Alert Banner */
  .sponsor-safety-alert-banner {
    width: calc(100% - 40px);
    height: auto;
    background-color: #FF4267;
    border-radius: 8px;
    padding: 12px 24px;
    margin: 0 20px 20px 20px;
    display: flex;
    justify-content: flex-start;
    align-items: center;
    color: white;
    font-weight: bold;
    gap: 4px;
    opacity: 1;
    animation: slideDown 0.5s ease-out;
    box-shadow: 0 2px 8px rgba(255, 66, 103, 0.2);
  }

  .sponsor-safety-alert-content {
    display: flex;
    align-items: center;
    gap: 8px;
  }

  .sponsor-safety-alert-title {
    font-weight: 600;
    font-size: 14px;
  }

  .sponsor-safety-alert-separator {
    font-size: 14px;
  }

  .sponsor-safety-alert-text {
    font-size: 14px;
    font-weight: normal;
  }

  @keyframes slideDown {
    from {
      opacity: 0;
      transform: translateY(-20px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  /* KPI Cards Grid */
  .sponsor-kpi-cards-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 20px;
    margin-bottom: 40px;
    padding: 0 20px;
  }

  .sponsor-kpi-card {
    background: white;
    border-radius: 12px;
    padding: 24px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    transition: transform 0.2s ease, box-shadow 0.2s ease;
  }

  .sponsor-kpi-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
  }

  .sponsor-kpi-header {
    display: flex;
    align-items: flex-start;
    gap: 12px;
    margin-bottom: 16px;
  }

  .sponsor-kpi-icon {
    width: 40px;
    height: 40px;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    flex-shrink: 0;
  }

  .sponsor-adverse-event-icon {
    background-color: #4ECDC4;
  }

  .sponsor-staff-icon {
    background-color: #4ECDC4;
  }

  .sponsor-patients-icon {
    background-color: #4ECDC4;
  }

  .sponsor-kpi-title-section {
    flex: 1;
  }

  .sponsor-kpi-title-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .sponsor-kpi-title {
    font-size: 16px;
    font-weight: 600;
    color: #333333;
  }

  .sponsor-kpi-period {
    position: relative;
  }

  .sponsor-kpi-period-select {
    background: #f8f9fa;
    border: 1px solid #e0e0e0;
    border-radius: 6px;
    padding: 4px 8px;
    font-size: 12px;
    color: #666666;
    cursor: pointer;
    appearance: none;
  }

  .sponsor-kpi-main-row {
    margin-bottom: 12px;
  }

  .sponsor-kpi-number {
    font-size: 48px;
    font-weight: 700;
    color: #333333;
    line-height: 1;
  }

  .sponsor-kpi-description {
    color: #666666;
    font-size: 14px;
    line-height: 1.4;
  }

  .sponsor-kpi-highlight {
    color: #FF4267;
    font-weight: 600;
  }

  /* Main Content Grid */
  .sponsor-main-content-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 30px;
    margin-bottom: 40px;
  }

  .sponsor-top-sites-section,
  .sponsor-policies-section {
    background: white;
    border-radius: 12px;
    padding: 24px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  }

  .sponsor-section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
  }

  .sponsor-section-title {
    font-size: 20px;
    font-weight: 600;
    color: #333333;
    margin: 0;
    display: flex;
    align-items: center;
    gap: 8px;
  }

  .sponsor-section-title::before {
    content: '';
    width: 4px;
    height: 20px;
    background-color: #4ECDC4;
    border-radius: 2px;
  }

  .sponsor-see-all-btn {
    background: none;
    border: none;
    color: #4ECDC4;
    font-size: 14px;
    cursor: pointer;
    font-weight: 500;
  }

  .sponsor-see-all-btn:hover {
    color: #3bb3ac;
  }

  .sponsor-sites-list,
  .sponsor-policies-list {
    display: flex;
    flex-direction: column;
    gap: 16px;
  }

  .sponsor-site-item,
  .sponsor-policy-item {
    padding: 16px;
    border: 1px solid #f0f0f0;
    border-radius: 8px;
    background: #fafafa;
  }

  .sponsor-site-item h4,
  .sponsor-policy-item h4 {
    font-size: 14px;
    font-weight: 600;
    color: #333333;
    margin: 0 0 4px 0;
  }

  .sponsor-site-item p,
  .sponsor-policy-item p {
    font-size: 12px;
    color: #666666;
    margin: 0;
  }

  .sponsor-enrollment-count {
    color: #4ECDC4;
    font-weight: 600;
  }

  /* Chart Section */
  .sponsor-chart-section {
    background: white;
    border-radius: 12px;
    padding: 24px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    margin-bottom: 40px;
  }

  .sponsor-chart-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 24px;
  }

  .sponsor-chart-header h3 {
    font-size: 20px;
    font-weight: 600;
    color: #333333;
    margin: 0;
    display: flex;
    align-items: center;
    gap: 8px;
  }

  .sponsor-chart-header h3::before {
    content: '';
    width: 4px;
    height: 20px;
    background-color: #4ECDC4;
    border-radius: 2px;
  }

  .sponsor-chart-options {
    color: #666666;
    cursor: pointer;
  }

  .sponsor-chart-content {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: 30px;
    align-items: start;
  }

  .sponsor-chart-visual {
    background: white;
    border-radius: 8px;
    padding: 20px;
    border: 1px solid #f0f0f0;
  }

  .sponsor-chart-stats {
    display: flex;
    flex-direction: column;
    gap: 24px;
    padding: 20px 0;
  }

  .sponsor-chart-stat {
    display: flex;
    flex-direction: column;
    gap: 8px;
  }

  .sponsor-stat-label {
    font-size: 14px;
    color: #666666;
    font-weight: 500;
  }

  .sponsor-stat-value {
    font-size: 32px;
    font-weight: 700;
    color: #333333;
  }

  .sponsor-chart-visual p {
    margin: 0 0 16px 0;
    font-size: 16px;
    font-weight: 600;
  }

  .sponsor-chart-visual ul {
    text-align: left;
    margin: 16px 0;
    padding-left: 20px;
  }

  .sponsor-chart-visual li {
    margin-bottom: 8px;
    font-size: 14px;
  }

  .sponsor-refresh-btn {
    background: #4ECDC4;
    color: white;
    border: none;
    border-radius: 6px;
    padding: 8px 16px;
    font-size: 14px;
    cursor: pointer;
    transition: background-color 0.2s ease;
  }

  .sponsor-refresh-btn:hover {
    background: #3bb3ac;
  }

  /* Bottom Sections */
  .sponsor-bottom-sections {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 30px;
  }

  .sponsor-study-overview-section,
  .sponsor-worksheet-section {
    background: white;
    border-radius: 12px;
    padding: 24px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  }

  .sponsor-filter-dropdown {
    position: relative;
  }

  .sponsor-filter-select {
    background: #f8f9fa;
    border: 1px solid #e0e0e0;
    border-radius: 6px;
    padding: 6px 12px;
    font-size: 14px;
    color: #666666;
    cursor: pointer;
    appearance: none;
  }

  /* Study Overview Breakdown */
  .sponsor-overview-breakdown {
    display: flex;
    flex-direction: column;
    gap: 20px;
  }

  .sponsor-overview-item {
    display: flex;
    flex-direction: column;
    gap: 8px;
  }

  .sponsor-overview-header {
    display: flex;
    align-items: center;
    gap: 12px;
  }

  .sponsor-overview-icon {
    width: 32px;
    height: 32px;
    border-radius: 6px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
  }

  .sponsor-teal-icon {
    background-color: #4ECDC4;
  }

  .sponsor-overview-header span:first-of-type {
    flex: 1;
    font-size: 14px;
    font-weight: 500;
    color: #333333;
  }

  .sponsor-overview-count {
    font-size: 16px;
    font-weight: 700;
    color: #333333;
  }

  .sponsor-progress-bar {
    width: 100%;
    height: 8px;
    background-color: #f0f0f0;
    border-radius: 4px;
    overflow: hidden;
  }

  .sponsor-progress-fill {
    height: 100%;
    border-radius: 4px;
    transition: width 0.3s ease;
  }

  .sponsor-teal-progress {
    background-color: #4ECDC4;
  }

  .sponsor-progress-percentage {
    font-size: 12px;
    color: #666666;
    font-weight: 500;
  }

  /* Worksheet Section */
  .sponsor-worksheet-stats {
    display: flex;
    justify-content: space-between;
    margin-bottom: 20px;
    padding: 16px;
    background: #f8f9fa;
    border-radius: 8px;
  }

  .sponsor-stat-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 4px;
  }

  .sponsor-stat-item .sponsor-stat-label {
    font-size: 12px;
    color: #666666;
    font-weight: 500;
  }

  .sponsor-stat-item .sponsor-stat-value {
    font-size: 18px;
    font-weight: 700;
    color: #333333;
  }

  /* Worksheet Table */
  .sponsor-worksheet-table-container {
    overflow-x: auto;
    border-radius: 8px;
    border: 1px solid #e0e0e0;
  }

  .sponsor-worksheet-table {
    width: 100%;
    border-collapse: collapse;
    background: white;
  }

  .sponsor-worksheet-table th {
    background-color: #f8f9fa;
    padding: 12px;
    text-align: left;
    font-weight: 600;
    font-size: 12px;
    color: #666666;
    border-bottom: 1px solid #e0e0e0;
  }

  .sponsor-worksheet-table td {
    padding: 12px;
    font-size: 14px;
    color: #333333;
    border-bottom: 1px solid #f0f0f0;
  }

  .sponsor-worksheet-table tbody tr:last-child td {
    border-bottom: none;
  }

  /* Status Badges */
  .sponsor-status-badge {
    padding: 4px 12px;
    border-radius: 16px;
    font-size: 12px;
    font-weight: 500;
    display: inline-block;
    text-align: center;
    min-width: 60px;
  }

  .sponsor-status-pending {
    background-color: #ffc107;
    color: #212529;
  }

  .sponsor-status-accepted {
    background-color: #28a745;
    color: white;
  }

  .sponsor-status-rejected {
    background-color: #dc3545;
    color: white;
  }

  /* Action Buttons */
  .sponsor-action-buttons {
    display: flex;
    gap: 8px;
  }

  .sponsor-action-btn {
    width: 32px;
    height: 32px;
    border: none;
    border-radius: 6px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.2s ease;
  }

  .sponsor-action-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
  }

  .sponsor-accept {
    background-color: #28a745;
    color: white;
  }

  .sponsor-accept:hover:not(:disabled) {
    background-color: #218838;
  }

  .sponsor-reject {
    background-color: #dc3545;
    color: white;
  }

  .sponsor-reject:hover:not(:disabled) {
    background-color: #c82333;
  }

  /* Responsive Design */
  @media (max-width: 1200px) {
    .sponsor-kpi-cards-grid {
      grid-template-columns: repeat(2, 1fr);
    }

    .sponsor-main-content-grid,
    .sponsor-bottom-sections {
      grid-template-columns: 1fr;
    }

    .sponsor-chart-content {
      grid-template-columns: 1fr;
    }
  }

  @media (max-width: 768px) {
    .sponsor-dashboard-container {
      padding: 16px;
    }

    .sponsor-dashboard-header {
      flex-direction: column;
      align-items: flex-start;
      gap: 16px;
    }

    .sponsor-header-actions {
      width: 100%;
      justify-content: space-between;
    }

    .sponsor-kpi-cards-grid {
      grid-template-columns: 1fr;
    }

    .sponsor-welcome-title {
      font-size: 24px;
    }

    .sponsor-worksheet-stats {
      flex-direction: column;
      gap: 12px;
    }

    .sponsor-stat-item {
      flex-direction: row;
      justify-content: space-between;
    }
  }

  @media (max-width: 480px) {
    .sponsor-header-actions {
      flex-direction: column;
      width: 100%;
      gap: 12px;
    }

    .sponsor-create-study-btn {
      width: 100%;
    }

    .sponsor-chart-stats {
      gap: 16px;
    }

    .sponsor-stat-value {
      font-size: 24px;
    }
  }

  /* Animation */
  .sponsor-kpi-card {
    animation: fadeInUp 0.3s ease-out forwards;
  }

  .sponsor-kpi-card:nth-child(2) {
    animation-delay: 0.1s;
  }

  .sponsor-kpi-card:nth-child(3) {
    animation-delay: 0.2s;
  }

  @keyframes fadeInUp {
    from {
      opacity: 0;
      transform: translateY(20px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

.patclin-dashboard {
  /* display: flex; */
  min-height: 100vh;
  background-color: #f8f9fa;
}

.patclin-content {
  flex: 1;
  padding: 20px;
  margin-left: 240px;
  transition: margin 0.3s ease;
}

.patient-sidebar.collapsed + .patclin-content {
  margin-left: 70px;
}

.patclin-container {
  min-height: calc(100vh - 15vh);
  margin: 0 auto;
  padding: 20px;
}

.patclin-header {
  margin-bottom: 30px;
}

.patclin-title {
  font-size: 2.5rem;
  font-weight: 700;
  color: #2c3e50;
  margin-bottom: 10px;
  display: flex;
  align-items: center;
  gap: 15px;
}

.patclin-wave-emoji {
  font-size: 2rem;
  transform-origin: 70% 70%;
  display: inline-block;
  animation: wave 1.5s ease-in-out 1;
}

@keyframes wave {
  0% { transform: rotate(0deg); }
  10% { transform: rotate(14deg); }
  20% { transform: rotate(-8deg); }
  30% { transform: rotate(14deg); }
  40% { transform: rotate(-4deg); }
  50% { transform: rotate(10deg); }
  60% { transform: rotate(0deg); }
  100% { transform: rotate(0deg); }
}

.patclin-subtitle {
  font-size: 1.2rem;
  color: #7f8c8d;
  margin-bottom: 30px;
}

.patclin-cards-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 20px;
}

.patclin-tab-content {
  margin-top: 20px;
}

.patclin-section-title {
  font-size: 2rem;
  font-weight: 600;
  color: #2c3e50;
  margin-bottom: 20px;
}

.patclin-empty-state {
  background-color: white;
  border-radius: 8px;
  padding: 30px;
  text-align: center;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
}

.patclin-empty-state p {
  color: #7f8c8d;
  font-size: 1.1rem;
}

/* BPOR Widget Styling */
.patclin-widget-container {
  background-color: white;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
}

.bpor-widget-container {
  padding: 15px;
}

.bpor-widget-container h3 {
  font-size: 1.5rem;
  font-weight: 600;
  color: #2c3e50;
  margin-bottom: 10px;
}

.bpor-widget-container p {
  color: #7f8c8d;
  margin-bottom: 20px;
}

@media (max-width: 991px) {
  .patclin-content {
    margin-left: 70px;
  }
  
  .patclin-cards-grid {
    grid-template-columns: 1fr;
  }
  
  .patclin-title {
    font-size: 2rem;
  }
}

@media (max-width: 768px) {
  .patclin-content {
    padding: 15px;
  }
  
  .patclin-container {
    padding: 15px;
  }
}

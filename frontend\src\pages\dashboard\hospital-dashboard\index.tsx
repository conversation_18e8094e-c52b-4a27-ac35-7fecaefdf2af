import "./analytics.css";
import { useState } from "react";
import { 
  <PERSON>, 
  FileText, 
  UserCheck, 
  Stethoscope, // Icon for Clinical Roles
  HelpCircle,  // Icon for Others
  FlaskConical, // Icon for Research Roles
  Microscope,
  Eye,  // Icon for Lab Roles
} from "lucide-react";
import { useCurrentUserQuery } from "@/hooks/user.query";
import { 
  usePatientsForHospitalDashboardQuery,
  usePoliciesForHospitalDashboardQuery,
  useSponsorsForHospitalDashboardQuery,
  useStaffForHospitalDashboardQuery,
  useStudiesForHospitalDashboardQuery,
  useDepartmentsFofHosHospitalDashboardQuery,
  useRequestsForHospitalDashboardQuery,
} from "@/hooks/user.query";
import { useNavigate } from "react-router-dom";
import DataTable from '@/components/common/DataTable';
import {DepartmentItem } from "@/services/api/types";


export default function AdminHospitalDashboard() {
  // Fetch current user and department UUID
  const currentUser = useCurrentUserQuery();
  const hospitalUuid = currentUser.data?.hospital?.uuid || "";

  // Options for KPI period selection
  const KPI_PERIOD_OPTIONS = [
    { value: "month", label: "This month" },
    { value: "year", label: "This year" },
    { value: "week", label: "This week" },
    { value: "all", label: "All" },
  ];


  // Function to determine icon and class based on speciality
  const getSpecialityIconAndClass = (speciality: string) => {
    const lowerCaseSpeciality = speciality.toLowerCase();
    let icon = <HelpCircle size={16} />; // Default icon for Others
    // Always use analytics-doctors-icon and analytics-doctors-progress as requested
    const iconClass = "analytics-doctors-icon"; 
    const progressClass = "analytics-doctors-progress";

    if (
      lowerCaseSpeciality.includes("doctor") ||
      lowerCaseSpeciality.includes("nurse") ||
      lowerCaseSpeciality.includes("matron") ||
      lowerCaseSpeciality.includes("clinical practitioner") ||
      lowerCaseSpeciality.includes("ward manager") ||
      lowerCaseSpeciality.includes("charge nurse") ||
      lowerCaseSpeciality.includes("clinical practice educator")
    ) {
      icon = <Stethoscope size={16} />;
    } else if (
      lowerCaseSpeciality.includes("research") ||
      lowerCaseSpeciality.includes("investigator") ||
      lowerCaseSpeciality.includes("trial manager") ||
      lowerCaseSpeciality.includes("crf manager") ||
      lowerCaseSpeciality.includes("study coordinator") ||
      lowerCaseSpeciality.includes("portfolio manager")
    ) {
      icon = <FlaskConical size={16} />;
    } else if (
      lowerCaseSpeciality.includes("receptionist") ||
      lowerCaseSpeciality.includes("data manager")
    ) {
      icon = <FileText size={16} />;
    } else if (
      lowerCaseSpeciality.includes("lab technician") ||
      lowerCaseSpeciality.includes("pharmacist")
    ) {
      icon = <Microscope size={16} />;
    }

    return { icon, iconClass, progressClass };
  };

  const navigate = useNavigate();

  // --- Patients KPI Logic and State ---
  // Initialize to undefined so the period is not passed on the very first query call
  const [selectedPatientsKpiPeriod, setSelectedPatientsKpiPeriod] = useState<string | undefined>(undefined); 
  // Fetch patients analytics. If selectedPatientsKpiPeriod is undefined, the hook should handle its default behavior.
  const patientsAnalytics = usePatientsForHospitalDashboardQuery(hospitalUuid || "", selectedPatientsKpiPeriod); 
  console.log("patientsAnalytics:", patientsAnalytics.data);

  const currentPatients = selectedPatientsKpiPeriod === "all"
    ? patientsAnalytics.data?.total ?? 0
    : patientsAnalytics.data?.period_count ?? 0;
  const previousPatients = patientsAnalytics.data?.prev_period_count ?? 0;
  const percentageChangePatients = previousPatients === 0
    ? currentPatients > 0
      ? "100"
      : "0"
    : (((currentPatients - previousPatients) / previousPatients) * 100).toFixed(1);
  const patientDifference = patientsAnalytics.data?.difference ?? 0;
  const isIncreasePatients = patientDifference > 0;
  const diffTextPatients = isIncreasePatients
    ? "Gained"
    : patientDifference < 0
      ? "Decreased"
      : "No Change";
  const diffValuePatients = patientDifference !== 0 ? `${isIncreasePatients ? "+" : ""}${patientDifference}` : "0";
  // Determine the label for the currently displayed period (defaults to "This month" if state is undefined)
  const periodLabelPatients = KPI_PERIOD_OPTIONS.find(opt => opt.value === (selectedPatientsKpiPeriod || KPI_PERIOD_OPTIONS[0].value))?.label;

  // --- Policies KPI Logic and State ---
  const [selectedPoliciesKpiPeriod, setSelectedPoliciesKpiPeriod] = useState<string | undefined>(undefined);
  const policiesAnalytics = usePoliciesForHospitalDashboardQuery(hospitalUuid || "", selectedPoliciesKpiPeriod);
  console.log("policiesAnalytics:", policiesAnalytics.data);

  const currentPolicies = selectedPoliciesKpiPeriod === "all"
    ? policiesAnalytics.data?.total ?? 0
    : policiesAnalytics.data?.period_count ?? 0;
  const previousPolicies = policiesAnalytics.data?.prev_period_count ?? 0;
  const percentageChangePolicies = previousPolicies === 0
    ? currentPolicies > 0
      ? "100"
      : "0"
    : (((currentPolicies - previousPolicies) / previousPolicies) * 100).toFixed(1);
  const policyDifference = policiesAnalytics.data?.difference ?? 0;
  const isIncreasePolicies = policyDifference > 0;
  const diffTextPolicies = isIncreasePolicies
    ? "Gained"
    : policyDifference < 0
      ? "Decreased"
      : "No Change";
  const diffValuePolicies = policyDifference !== 0 ? `${isIncreasePolicies ? "+" : ""}${policyDifference}` : "0";
  const periodLabelPolicies = KPI_PERIOD_OPTIONS.find(opt => opt.value === (selectedPoliciesKpiPeriod || KPI_PERIOD_OPTIONS[0].value))?.label;

  // --- Sponsors KPI Logic and State ---
  const [selectedSponsorsKpiPeriod, setSelectedSponsorsKpiPeriod] = useState<string | undefined>(undefined);
  const sponsorsAnalytics = useSponsorsForHospitalDashboardQuery(hospitalUuid || "", selectedSponsorsKpiPeriod);
  console.log("sponsorsAnalytics:", sponsorsAnalytics.data);

  const currentSponsors = selectedSponsorsKpiPeriod === "all"
    ? sponsorsAnalytics.data?.total ?? 0
    : sponsorsAnalytics.data?.period_count ?? 0;
  const previousSponsors = sponsorsAnalytics.data?.prev_period_count ?? 0;
  const percentageChangeSponsors = previousSponsors === 0
    ? currentSponsors > 0
      ? "100"
      : "0"
    : (((currentSponsors - previousSponsors) / previousSponsors) * 100).toFixed(1);
  const sponsorDifference = sponsorsAnalytics.data?.difference ?? 0;
  const isIncreaseSponsors = sponsorDifference > 0;
  const diffTextSponsors = isIncreaseSponsors
    ? "Gained"
    : sponsorDifference < 0
      ? "Decreased"
      : "No Change";
  const diffValueSponsors = sponsorDifference !== 0 ? `${isIncreaseSponsors ? "+" : ""}${sponsorDifference}` : "0";
  const periodLabelSponsors = KPI_PERIOD_OPTIONS.find(opt => opt.value === (selectedSponsorsKpiPeriod || KPI_PERIOD_OPTIONS[0].value))?.label;

  // --- Staff KPI Logic and State ---
  const [selectedStaffKpiPeriod, setSelectedStaffKpiPeriod] = useState<string | undefined>(undefined);
  const staffAnalytics = useStaffForHospitalDashboardQuery(hospitalUuid || "", selectedStaffKpiPeriod);
  console.log("staffAnalytics:", staffAnalytics.data);

  const currentStaff = selectedStaffKpiPeriod === "all"
    ? staffAnalytics.data?.total ?? 0
    : staffAnalytics.data?.period_count ?? 0;
  const previousStaff = staffAnalytics.data?.prev_period_count ?? 0;
  const percentageChangeStaff = previousStaff === 0
    ? currentStaff > 0
      ? "100"
      : "0"
    : (((currentStaff - previousStaff) / previousStaff) * 100).toFixed(1);
  const staffDifference = staffAnalytics.data?.difference ?? 0;
  const isIncreaseStaff = staffDifference > 0;
  const diffTextStaff = isIncreaseStaff
    ? "Added" 
    : staffDifference < 0
      ? "Removed" 
      : "No Change";
  const diffValueStaff = staffDifference !== 0 ? `${isIncreaseStaff ? "+" : ""}${staffDifference}` : "0";
  const periodLabelStaff = KPI_PERIOD_OPTIONS.find(opt => opt.value === (selectedStaffKpiPeriod || KPI_PERIOD_OPTIONS[0].value))?.label;


  // --- Staff Breakdown Logic ---
  const staffSpecialities = staffAnalytics.data?.by_speciality || [];
  const totalStaffCount = staffAnalytics.data?.total ?? 0;

  // Sort by count in descending order to get top specialities
  const sortedSpecialities = [...staffSpecialities].sort((a, b) => b.count - a.count);

  const top3Specialities = sortedSpecialities.slice(0, 3);
  let othersCount = 0;
  const totalSpecialitiesCount = sortedSpecialities.reduce((sum, spec) => sum + spec.count, 0);
  // Calculate count for "Others" if there are more than 3 specialities
  if (totalSpecialitiesCount < currentStaff) {
    othersCount = currentStaff - totalSpecialitiesCount;
  }

  // Prepare data for rendering staff breakdown
  const staffBreakdownData = top3Specialities.map(spec => {
    const percentage = totalStaffCount > 0 ? ((spec.count / totalStaffCount) * 100).toFixed(0) : 0;
    const { icon, iconClass, progressClass } = getSpecialityIconAndClass(spec.speciality);

    return {
      name: spec.speciality,
      count: spec.count,
      percentage: percentage,
      icon: icon,
      iconClass: iconClass,
      progressClass: progressClass,
    };
  });

  // Add "Others" category if there are remaining specialities
  if (othersCount > 0) {
    const othersPercentage = totalStaffCount > 0 ? ((othersCount / totalStaffCount) * 100).toFixed(0) : 0;
    staffBreakdownData.push({
      name: "Others",
      count: othersCount,
      percentage: othersPercentage,
      icon: <HelpCircle size={16} />, // Default icon for "Others"
      iconClass: "analytics-doctors-icon", // Use doctors icon class for Others as well
      progressClass: "analytics-doctors-progress", // Use doctors progress class for Others as well
    });
  }


  // Fetch studies and requests analytics (these do not have period selection in the current UI)
  const studiesAnalytics = useStudiesForHospitalDashboardQuery(hospitalUuid || "");
  const totalStudies = studiesAnalytics.data?.total ?? 0;
  const acceptedStudies = studiesAnalytics.data?.accepted ?? 0;
  const rejectedStudies = studiesAnalytics.data?.rejected ?? 0;



  
  console.log("studiesAnalytics:", studiesAnalytics.data);
  const requestsAnalytics = useRequestsForHospitalDashboardQuery(hospitalUuid || "");
  const totalRequests = requestsAnalytics.data?.total ?? 0;
  const pendingRequests = requestsAnalytics.data?.pending ?? 0;
  const acceptedRequests = requestsAnalytics.data?.accepted ?? 0;
  const rejectedRequests = requestsAnalytics.data?.rejected ?? 0;

  // Helper to get percentage string for CSS linear-gradient
  const getPercentageStyle = (count: number) => {
    const percentage = totalRequests > 0 ? (count / totalRequests) * 100 : 0;
    const clampedPercentage = Math.max(0, Math.min(100, percentage)); // Ensure percentage is between 0 and 100
    return `${clampedPercentage}%`;
  };

  const acceptedPercentageStyle = getPercentageStyle(acceptedRequests);
  const pendingPercentageStyle = getPercentageStyle(pendingRequests);
  const rejectedPercentageStyle = getPercentageStyle(rejectedRequests);


  console.log("requestsAnalytics:", requestsAnalytics.data);

  // Fetch departments analytics for hospital dashboard
  const { data, isLoading, isError } = useDepartmentsFofHosHospitalDashboardQuery(hospitalUuid || "");
  
  const columns = [
    { key: 'name', header: 'Department Name' },
    { key: 'admin_name', header: 'Admin Name' },
    { key: 'num_staff', header: 'Staff Count' },
    { key: 'num_patients', header: 'Patient Count' },
    { key: 'num_studies', header: 'Studies Count' },
  ] as import('@/components/common/DataTable').Column<DepartmentItem>[];

  const actions = [
    {
      icon: <Eye size={20} />,
      onClick: (row: DepartmentItem) => handleDepartmentDetails(row),
      tooltipText:"Show details",
    },
  ]

    const handleDepartmentDetails = (department: DepartmentItem) => {
      navigate(`/org/dashboard/department-details/${department.uuid}`);
    };

  return (
    <div className="analytics-dashboard-content">
      {/* Welcome Header */}
      <div className="analytics-welcome-header">
        <h1 className="analytics-welcome-title">
          Welcome Back, <span className="analytics-user-name">{currentUser.data?.first_name || "Amine Haouem"}</span>
        </h1>
        <p className="analytics-welcome-subtitle">Here’s a quick look at your latest team activity and updates.</p>
      </div>

      {/* KPI Cards */}
      <div className="analytics-kpi-cards-row">
        {/* Patients KPI Card */}
        <div className="analytics-kpi-card">
          <div className="analytics-kpi-header">
            <div className="analytics-kpi-icon analytics-patients-icon">
              <Users size={20} />
            </div>
            <div className="analytics-kpi-title-section">
              <div className="analytics-kpi-title-row">
                <span className="analytics-kpi-title">Patients</span>
                <div className="analytics-kpi-period">
                  <select
                      className="analytics-kpi-period-select"
                      // Display "This month" by default if selectedPatientsKpiPeriod is undefined
                      value={selectedPatientsKpiPeriod || KPI_PERIOD_OPTIONS[0].value}
                      onChange={e => setSelectedPatientsKpiPeriod(e.target.value)}
                    >
                      {KPI_PERIOD_OPTIONS.map(option => (
                        <option key={option.value} value={option.value}>
                          {option.label}
                        </option>
                      ))}
                    </select>
                </div>
              </div>
            </div>
          </div>
          <div className="analytics-kpi-main-row">
            <div className="analytics-kpi-number">
              {currentPatients === 0 ? "0" : String(currentPatients).padStart(2, '0')}
            </div>
            {selectedPatientsKpiPeriod !== "all" && (
            <div className={`analytics-kpi-percentage ${parseFloat(percentageChangePatients) > 0 ? "analytics-positive" : parseFloat(percentageChangePatients) < 0 ? "analytics-negative" : "analytics-neutral"}`}>
              {percentageChangePatients}% {parseFloat(percentageChangePatients) > 0 ? "↑" : parseFloat(percentageChangePatients) < 0 ? "↓" : ""}
            </div>
            )}
          </div>
          {selectedPatientsKpiPeriod !== "all" && (
          <div className={`analytics-kpi-change ${isIncreasePatients ? "analytics-positive" : "analytics-negative"}`}>
            <span className="analytics-change-text">
              {diffTextPatients} <span className="analytics-highlight">{diffValuePatients} Patients</span> {periodLabelPatients}!
            </span>
          </div>
          )}
          { selectedPatientsKpiPeriod === "all" && (
            <div className={`analytics-kpi-change ${isIncreasePatients ? "analytics-positive" : "analytics-negative"}`}>
              <span className="analytics-change-text">
              Total Patients to Date
              </span>
            </div>
          )}
        </div>

        {/* Policies KPI Card */}
        <div className="analytics-kpi-card">
          <div className="analytics-kpi-header">
            <div className="analytics-kpi-icon analytics-policies-icon">
              <FileText size={20} />
            </div>
            <div className="analytics-kpi-title-section">
              <div className="analytics-kpi-title-row">
                <span className="analytics-kpi-title">Policies</span>
                <div className="analytics-kpi-period">
                  <select
                      className="analytics-kpi-period-select"
                      value={selectedPoliciesKpiPeriod || KPI_PERIOD_OPTIONS[0].value}
                      onChange={e => setSelectedPoliciesKpiPeriod(e.target.value)}
                    >
                      {KPI_PERIOD_OPTIONS.map(option => (
                        <option key={option.value} value={option.value}>
                          {option.label}
                        </option>
                      ))}
                    </select>
                </div>
              </div>
            </div>
          </div>
          <div className="analytics-kpi-main-row">
            <div className="analytics-kpi-number">
              {currentPolicies === 0 ? "0" : String(currentPolicies).padStart(2, '0')}
            </div>
            {selectedPoliciesKpiPeriod !== "all" && (
            <div className={`analytics-kpi-percentage ${parseFloat(percentageChangePolicies) > 0 ? "analytics-positive" : parseFloat(percentageChangePolicies) < 0 ? "analytics-negative" : "analytics-neutral"}`}>
              {percentageChangePolicies}% {parseFloat(percentageChangePolicies) > 0 ? "↑" : parseFloat(percentageChangePolicies) < 0 ? "↓" : ""}
            </div>
            )}
          </div>
          {selectedPoliciesKpiPeriod !== "all" && (
          <div className={`analytics-kpi-change ${isIncreasePolicies ? "analytics-positive" : "analytics-negative"}`}>
            <span className="analytics-change-text">
              {diffTextPolicies} <span className="analytics-highlight">{diffValuePolicies} Policies</span> {periodLabelPolicies}!
            </span>
          </div>
          )}

          { selectedPoliciesKpiPeriod === "all" && (
            <div className={`analytics-kpi-change ${isIncreasePatients ? "analytics-positive" : "analytics-negative"}`}>
              <span className="analytics-change-text">
              Total Policies to Date
              </span>
            </div>
          )}
        </div>

        {/* Sponsors KPI Card */}
        <div className="analytics-kpi-card">
          <div className="analytics-kpi-header">
            <div className="analytics-kpi-icon analytics-sponsors-icon">
              <UserCheck size={20} />
            </div>
            <div className="analytics-kpi-title-section">
              <div className="analytics-kpi-title-row">
                <span className="analytics-kpi-title">Sponsors</span>
                <div className="analytics-kpi-period">
                  <select
                      className="analytics-kpi-period-select"
                      value={selectedSponsorsKpiPeriod || KPI_PERIOD_OPTIONS[0].value}
                      onChange={e => setSelectedSponsorsKpiPeriod(e.target.value)}
                    >
                      {KPI_PERIOD_OPTIONS.map(option => (
                        <option key={option.value} value={option.value}>
                          {option.label}
                        </option>
                      ))}
                    </select>
                </div>
              </div>
            </div>
          </div>
          <div className="analytics-kpi-main-row">
            <div className="analytics-kpi-number">
              {currentSponsors === 0 ? "0" : String(currentSponsors).padStart(2, '0')}
            </div>
            {selectedSponsorsKpiPeriod !== "all" && (
            <div className={`analytics-kpi-percentage ${parseFloat(percentageChangeSponsors) > 0 ? "analytics-positive" : parseFloat(percentageChangeSponsors) < 0 ? "analytics-negative" : "analytics-neutral"}`}>
              {percentageChangeSponsors}% {parseFloat(percentageChangeSponsors) > 0 ? "↑" : parseFloat(percentageChangeSponsors) < 0 ? "↓" : ""}
            </div>
            )}
          </div>
          {selectedSponsorsKpiPeriod !== "all" && (
          <div className={`analytics-kpi-change ${isIncreaseSponsors ? "analytics-positive" : "analytics-negative"}`}>
            <span className="analytics-change-text">
              {diffTextSponsors} <span className="analytics-highlight">{diffValueSponsors} Sponsors</span> {periodLabelSponsors}!
            </span>
          </div>
          )}

          { selectedSponsorsKpiPeriod === "all" && (
            <div className={`analytics-kpi-change ${isIncreasePatients ? "analytics-positive" : "analytics-negative"}`}>
              <span className="analytics-change-text">
              Total Sponsors to Date
              </span>
            </div>
          )}
        </div>
      </div>

      {/* Middle Section */}
      <div className="analytics-middle-section">
        {/* Staff/Users Card */}
        <div className="analytics-staff-card">
          <div className="analytics-staff-header">
            <div className="analytics-staff-title-section">
              <h3>Staff/Users</h3>
              <div className="analytics-staff-period">
                <select
                      className="analytics-kpi-period-select"
                      value={selectedStaffKpiPeriod || KPI_PERIOD_OPTIONS[0].value}
                      onChange={e => setSelectedStaffKpiPeriod(e.target.value)}
                    >
                      {KPI_PERIOD_OPTIONS.map(option => (
                        <option key={option.value} value={option.value}>
                          {option.label}
                        </option>
                      ))}
                    </select>
              </div>
            </div>
            
          </div>
          
          <div className="analytics-staff-main-row">
            <div className="analytics-staff-number">
              {currentStaff === 0 ? "0" : String(currentStaff).padStart(2, '0')}
            </div>
            {selectedStaffKpiPeriod !== "all" && (
            <div className={`analytics-staff-percentage ${parseFloat(percentageChangeStaff) > 0 ? "analytics-positive" : parseFloat(percentageChangeStaff) < 0 ? "analytics-negative" : "analytics-neutral"}`}>
              {percentageChangeStaff}% {parseFloat(percentageChangeStaff) > 0 ? "↑" : parseFloat(percentageChangeStaff) < 0 ? "↓" : ""}
            </div>
            )}
          </div>
          {selectedStaffKpiPeriod !== "all" && (
          <div className={`analytics-staff-change ${isIncreaseStaff ? "analytics-positive" : "analytics-negative"}`}>
            <span className="analytics-change-text">
              {diffTextStaff} <span className="analytics-highlight">{diffValueStaff} Users</span> {periodLabelStaff}!
            </span>
          </div>
          )}

          {/* Staff Breakdown Section */}
          <div className="analytics-staff-breakdown">
            {staffBreakdownData.map((staffType, index) => (
              <div className="analytics-staff-type" key={index}>
                <div className="analytics-staff-type-header">
                  <div className={`analytics-staff-type-icon ${staffType.iconClass}`}>
                    {staffType.icon}
                  </div>
                  <span>{staffType.name}</span>
                  <span className="analytics-staff-count">{String(staffType.count).padStart(2, '0')}</span>
                </div>
                <div className="analytics-progress-bar">
                  <div className={`analytics-progress-fill ${staffType.progressClass}`} style={{width: `${staffType.percentage}%`}}></div>
                </div>
                <span className="analytics-progress-percentage">{staffType.percentage}%</span>
              </div>
            ))}
          </div>
        </div>

        {/* Studies Invitations Card */}
        <div className="analytics-studies-card">
          <div className="analytics-requests-header">
            <h3>Studies Invitations</h3>
            
          </div>
          
          <div className="analytics-studies-stats">
            <div className="analytics-stat-item">
              <span className="analytics-stat-label">Total Invitations:</span>
              <span className="analytics-stat-value">
                {totalStudies === 0 ? "0" : String(totalStudies).padStart(2, '0')}
              </span>
            </div>
            <div className="analytics-stat-item">
              <span className="analytics-stat-label">Accepted:</span>
              <span className="analytics-stat-value">
                {acceptedStudies === 0 ? "0" : String(acceptedStudies).padStart(2, '0')}
              </span>
            </div>
            <div className="analytics-stat-item">
              <span className="analytics-stat-label">Rejected:</span>
              <span className="analytics-stat-value">
                {rejectedStudies === 0 ? "0" : String(rejectedStudies).padStart(2, '0')}
              </span>
            </div>
          </div>
          {/* Studies Invitations table */}
      <div className="analytics-studies-table-container">
        <table className="analytics-studies-table">
          <thead>
            <tr>
              <th>Study Name</th>
              <th>Source</th>
              <th>Status</th>
  
            </tr>
          </thead>
          <tbody>
            {/* Access last_3 from studiesAnalytics.data */}
            {studiesAnalytics.data?.last_3 && studiesAnalytics.data.last_3.length === 0 ? (
              <tr>
                <td colSpan={4} style={{ textAlign: "center" }}>No invitations found</td>
              </tr>
            ) : (
              studiesAnalytics.data?.last_3.map(inv => (
                <tr key={inv.uuid}>
                  <td>{inv.study || "-"}</td>
                  <td>{inv.invited_by || "-"}</td>
                  <td>
                    <span className={`analytics-status-badge analytics-${inv.status}`}>
                      {inv.status.charAt(0).toUpperCase() + inv.status.slice(1)}
                    </span>
                  </td>
                </tr>
              ))
            )}
          </tbody>
        </table>

      </div>
        </div>
      </div>

            {/* Departments Section */}
            <div className="analytics-departments-section">
      <div className="analytics-requests-main">
        <div className="analytics-requests-left">
          <div className="analytics-requests-header">
            <h3>Departments</h3>
            <button className="analytics-see-all-btn" onClick={() => navigate('/org/dashboard/department')}>
              See All
            </button>
          </div>

          <div className="analytics-requests-table-container">
            {isLoading && <p>Loading departments...</p>}
            {isError && <p className="text-red-500">Error loading departments. Please try again.</p>}
            {!isLoading && !isError && (
              <DataTable<DepartmentItem>
                data={data?.departments || []}
                columns={columns}
                actions={actions}
                noDataMessage="No departments found for this hospital."
                defaultItemsPerPage={5}
                hideItemsPerPageSelector={true}
                hideGlobalFilter={true}
              />
            )}
          </div>
        </div>
      </div>
    </div>
      
            {/* Requests Section */}
            <div className="analytics-requests-section">
        <div className="analytics-requests-main">
          <div className="analytics-requests-left">
            <div className="analytics-requests-header">
              <h3>Requests</h3>
              
            </div>

            <div className="analytics-requests-table-container">
              <table className="analytics-requests-table">
                <thead>
                  <tr>
                    <th>Name</th>
                    <th>NHS Id</th>
                    <th>Source</th>
                    <th>Status</th>
                  </tr>
                </thead>
                <tbody>
                  {requestsAnalytics.data?.last_5 && requestsAnalytics.data.last_5.length === 0 ? (
                    <tr>
                      <td colSpan={5} style={{ textAlign: "center" }}>No requests found</td>
                    </tr>
                  ) : (
                    requestsAnalytics.data?.last_5.map((request, index) => (
                      <tr key={index}> {/* Using index as key, consider a unique ID if available */}
                        <td>{`${request.first_name || ""} ${request.last_name || ""}`.trim() || "-"}</td>
                        <td>{request.nhs_number || "-"}</td>
                        <td>{request.source || "-"}</td>
                        <td>
                          <span className={`analytics-status-badge analytics-${request.status.toLowerCase()}`}>
                            {request.status.charAt(0).toUpperCase() + request.status.slice(1)}
                          </span>
                        </td>
                      </tr>
                    ))
                  )}
                </tbody>
              </table>
        </div>
          </div>

          <div className="analytics-requests-right">
            <div className="analytics-requests-stats">
              <div className="analytics-total-requests">Total Requests: 
                <span className="analytics-total-number">
                  {totalRequests === 0 ? "0" : String(totalRequests).padStart(2, '0')}
                </span>
              </div>
              
              <div className="analytics-requests-chart-section">
                <div className="analytics-chart-bars">
                  <div 
                    className="analytics-chart-bar analytics-accepted" 
                    style={{
                      height: '40px',
                      background: `linear-gradient(90deg, #10b981 0%, #10b981 ${acceptedPercentageStyle}, #dcfce7 ${acceptedPercentageStyle}, #dcfce7 100%)`
                    }}
                  >
                    <span className="analytics-bar-label">
                      {acceptedRequests === 0 ? "0" : String(acceptedRequests).padStart(2, '0')}
                    </span>
                  </div>
                  <div 
                    className="analytics-chart-bar analytics-pending" 
                    style={{
                      height: '40px',
                      background: `linear-gradient(90deg, #f59e0b 0%, #f59e0b ${pendingPercentageStyle}, #fef3c7 ${pendingPercentageStyle}, #fef3c7 100%)`
                    }}
                  >
                    <span className="analytics-bar-label">
                      {pendingRequests === 0 ? "0" : String(pendingRequests).padStart(2, '0')}
                    </span>
                  </div>
                  <div 
                    className="analytics-chart-bar analytics-rejected" 
                    style={{
                      height: '40px',
                      background: `linear-gradient(90deg, #ef4444 0%, #ef4444 ${rejectedPercentageStyle}, #fee2e2 ${rejectedPercentageStyle}, #fee2e2 100%)`
                    }}
                  >
                    <span className="analytics-bar-label">
                      {rejectedRequests === 0 ? "0" : String(rejectedRequests).padStart(2, '0')}
                    </span>
                  </div>
                </div>
              </div>
            </div>
        </div>
        </div>
      </div>
    </div>
  );
}



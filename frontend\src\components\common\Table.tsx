import { ReactNode } from "react";
import "./Table.css";
import { ChevronDown } from "lucide-react";

type Column<T> = {
  key: keyof T;
  header: string;
  render?: (value: T[keyof T], row?: T) => ReactNode; // Keep value, add optional row
  hidden?: boolean;
};

interface Action<T> {
  icon?: React.ReactNode;
  label?: string;
  path?: string;
  onClick?: (row: T) => void; // Now receives the entire row data
  tooltipText?: string; // Added optional tooltip text
}

type DataTableProps<T> = {
  data: T[];
  columns: Column<T>[];
  actions?: Action<T>[];
};

export default function DataTable<T extends { uuid?: number | string }>({
  data,
  columns,
  actions = [],
}: DataTableProps<T>) {
  return (
    <div className="table-container">
      <table className="min-w-full">
        <TableHeader columns={columns} actions={actions} />
        <tbody>
          {data.map((row) => (
            <TableRow key={row.uuid} row={row} columns={columns} actions={actions} />
          ))}
        </tbody>
      </table>
    </div>
  );
}

type TableHeaderProps<T> = {
  columns: Column<T>[];
  actions: Action<T>[];
};

function TableHeader<T>({ columns, actions }: TableHeaderProps<T>) {
  return (
    <thead>
      <tr > 
        {columns
          .filter((column) => !column.hidden)
          .map((column) => (
            <th key={String(column.key)}>
              {column.header} <ChevronDown />
            </th>
          ))}
        {actions.length > 0 && <th style={{textAlign:"center"}}>Actions</th>}
      </tr>
    </thead>
  );
}

type TableRowProps<T> = {
  row: T;
  columns: Column<T>[];
  actions: Action<T>[];
};

function TableRow<T extends { uuid?: number | string }>({
  row,
  columns,
  actions,
}: TableRowProps<T>) {
  return (
    <tr key={row.uuid}>
      {columns
        .filter((column) => !column.hidden)
        .map((column) => (
          <TableCell
            key={String(column.key)}
            value={row[column.key]}
            row={row} // Pass the entire row
            render={column.render}
          />
        ))}
      {actions.length > 0 && (
        <td className="d-flex gap-2 p-2 justify-content-center">
          {actions.map((action, index) => (
            <div key={index} className="button-wrapper">
              <button className="iconButton" onClick={() => action.onClick?.(row)}>
                {action.icon && <span className="mr-2">{action.icon}</span>}
                {action.label && <span>{action.label}</span>}
              </button>
              {action.tooltipText && <span className="tooltip">{action.tooltipText}</span>}
            </div>
          ))}
        </td>
      )}
    </tr>
  );
}

type TableCellProps<T> = {
  value: T[keyof T]; // Specify the type as the value type
  row?: T; // Optional row data
  render?: (value: T[keyof T], row?: T) => ReactNode;
};

function TableCell<T>({ value, row, render }: TableCellProps<T>) {
  return <td>{render ? render(value, row) : String(value)}</td>;
}

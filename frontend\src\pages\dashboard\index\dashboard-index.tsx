import DashboardFormCard from "@/components/DashboardFormCard";

export default function DashboardIndex() {
  const dashboardCards = [
    { title: "Patient Assessment Form", date: "2024-03-20" },
    { title: "Medication Record Form", date: "2024-03-19" },
    { title: "Vital Signs Chart", date: "2024-03-18" },
    { title: "Progress Notes", date: "2024-03-17" },
    { title: "Treatment Plan Form", date: "2024-03-16" },
  ];
  return (
    <div>
      <div style={{ marginTop: "30px", paddingBottom: "80px" }}>
        <div className="row y-gap-30 mt-30" style={{ marginBottom: "30px" }}>
          {dashboardCards.map((card, index) => (
            <div className="col-xl-12 col-lg-12 col-md-12" key={index}>
              <DashboardFormCard
                createdBy="John Doe"
                title={card.title}
                date={card.date}
              />
            </div>
          ))}
        </div>
      </div>
    </div>
  );
}

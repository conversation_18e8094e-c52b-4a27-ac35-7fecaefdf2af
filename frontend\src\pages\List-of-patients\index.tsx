import React, { useState, useMemo } from "react";
import "./listofpatients.css";
import { ArrowUpRight, Edit2, Info, ChevronLeft, ChevronRight, List, Calendar, User } from "lucide-react";
import { useVisits } from "@/hooks/patient.query";
import VisitCalendar from "@/components/VisitCalendar";
import NurtifyDateInput from "@/components/NurtifyDateInput";
import { PatientVisitToday, Patient } from "@/services/api/types";
import { useAddPatientAccessLogMutation } from "@/hooks/patient.query";
import { useNavigate } from "react-router-dom";
import useSelectedPatientStore from "@/store/SelectedPatientState";
import DataTable from "@/components/common/DataTable";
import { Filter } from 'lucide-react';
import LightFooter from "@/shared/LightFooter";
import EditPatientVisitModal from "@/components/modal/EditPatientVisitModal";
import VisitDetailsModal from "@/components/modal/VisitDetailsModal";
import { toast } from "sonner";
import NurtifyFilter, { NurtifyFilterItem } from '@/components/NurtifyFilter';

interface VisitDetail {
  uuid: string;
  name: string;
  location?: string;
  visit_status: string;
  study_name: string;
  activities: string[];
  registration_status: string;
  comments: string;
  date: string;
  time: string;
  leading_team: string;
  nurse_details?: {
    identifier: string;
    first_name: string;
    last_name: string;
    organization_name: string;
  };
}

const ListOfPatients: React.FC = () => {
  const [selectedCategories, setSelectedCategories] = useState<string[]>([]);
  const [isEditPatientVisitModalOpen, setIsEditPatientVisitModalOpen] = useState(false);
  const [isVisitDetailsModalOpen, setIsVisitDetailsModalOpen] = useState(false);
  type NewType = {
    patientUuid: string;
    visitUuid: string;
    visitName: string;
    visitStatus: string;
    location: string;
    nurse_identifier: string;
    registration_status: string;
    date: string;
    time: string;
    leading_team: string;
    activities: string[];
    comments: string;
  };

  const [selectedVisit, setSelectedVisit] = useState<NewType | null>(null);
  const [selectedDate, setSelectedDate] = useState<string>(new Date().toISOString().split('T')[0]); // Default to today
  const [viewMode, setViewMode] = useState<'list' | 'calendar'>('list'); // Default to list view
  const navigate = useNavigate();

  // Get the setSelectedPatient function from the store
  const { setSelectedPatient } = useSelectedPatientStore();
  console.log("visita", selectedVisit);

  // Use the new unified visits hook with day view
  const visitsQuery = useVisits(
    "D", // Always use day view for this component
    selectedDate, // Pass the selected date
    false // Don't use local date
  );

  const visits = visitsQuery.data || [];
  const isLoading = visitsQuery.isLoading;
  const error = visitsQuery.error;

  // Update the visit data when the selected date changes
  React.useEffect(() => {
    // The query will automatically refetch when selectedDate changes
    // because it's part of the query key
  }, [selectedDate]);

  // We'll keep this commented out since it's not currently used but might be needed in the future
  // const updateVisitMutation = useUpdateVisitMutation();

  // Additional hooks from merged branch
  const addPatientAccessLogMutation = useAddPatientAccessLogMutation();

  // Handle date change
  const handleDateChange = (event: { target: { name: string; value: string } }) => {
    setSelectedDate(event.target.value);
  };

  // State to track the calendar view type
  const [calendarViewType, setCalendarViewType] = useState<"D" | "W" | "M">("D");

  // Navigate to previous period based on current view type
  const goToPrevious = () => {
    const currentDate = new Date(selectedDate);
    const currentViewType = viewMode === 'list' ? "D" : calendarViewType;

    switch (currentViewType) {
      case "D":
        // Previous day
        currentDate.setDate(currentDate.getDate() - 1);
        break;
      case "W":
        // Previous week
        currentDate.setDate(currentDate.getDate() - 7);
        break;
      case "M":
        // Previous month
        currentDate.setMonth(currentDate.getMonth() - 1);
        break;
    }

    setSelectedDate(currentDate.toISOString().split('T')[0]);
    // Ensure we maintain the current view type (day, week, or month)
    // No need to update viewMode or calendarViewType as they should remain the same
  };

  // Navigate to next period based on current view type
  const goToNext = () => {
    const currentDate = new Date(selectedDate);
    const currentViewType = viewMode === 'list' ? "D" : calendarViewType;

    switch (currentViewType) {
      case "D":
        // Next day
        currentDate.setDate(currentDate.getDate() + 1);
        break;
      case "W":
        // Next week
        currentDate.setDate(currentDate.getDate() + 7);
        break;
      case "M":
        // Next month
        currentDate.setMonth(currentDate.getMonth() + 1);
        break;
    }

    setSelectedDate(currentDate.toISOString().split('T')[0]);
    // Ensure we maintain the current view type (day, week, or month)
    // No need to update viewMode or calendarViewType as they should remain the same
  };

  // No longer need legacy functions since we're using goToPrevious and goToNext directly

  // Filter categories
  const categories = [
    { id: "Not Arrived", label: "Not Arrived", count: visits?.filter((v: PatientVisitToday) => v.visit_details.some((vd: VisitDetail) => vd.registration_status === "Not Arrived")).length || 0 },
    { id: "In Hospital", label: "In Hospital", count: visits?.filter((v: PatientVisitToday) => v.visit_details.some((vd: VisitDetail) => vd.registration_status === "In Hospital")).length || 0 },
    { id: "Discharged", label: "Discharged", count: visits?.filter((v: PatientVisitToday) => v.visit_details.some((vd: VisitDetail) => vd.registration_status === "Discharged")).length || 0 },
    // { id: "noShow", label: "No Show", count: visits?.filter(v => v.visit_details.some(vd => vd.visit_status === "NoShow")).length || 0 },
  ];

  const handleCategoryChange = (categoryId: string) => {
    setSelectedCategories((prev) =>
      prev.includes(categoryId)
        ? prev.filter((id) => id !== categoryId)
        : [...prev, categoryId]
    );
  };

  // Filter visits based on categories
  const categoryFilteredVisits = useMemo(() => {
    if (!visits) return [];

    if (selectedCategories.length === 0) return visits;

    return visits.filter((visit: PatientVisitToday) =>
      selectedCategories.some(category => {
        return visit.visit_details.some((vd: VisitDetail) => {
          switch(category) {
            case "Not Arrived":
              return vd.registration_status === "Not Arrived";
            case "In Hospital":
              return vd.registration_status === "In Hospital";
            case "Discharged":
              return vd.registration_status === "Discharged";
            default:
              return true;
          }
        });
      })
    );
  }, [visits, selectedCategories]);

  // const handleEditVisit = (patientUuid: string, visitUuid: string, visitName: string, visitStatus: string) => {
  //   setSelectedVisit({
  //     patientUuid,
  //     visitUuid,
  //     visitName,
  //     visitStatus,
  //     date: new Date().toISOString().split('T')[0] // Today's date
  //   });
  //   setIsEditPatientVisitModalOpen(true);
  // };
  const handleEditVisit = (patientUuid: string, visitUuid: string, visitName: string, visitStatus: string) => {
    // Find the visit details from the visits data
    const patient = visits?.find((v: PatientVisitToday) => v.uuid === patientUuid);
    const visitDetails = patient?.visit_details.find((vd: VisitDetail) => vd.uuid === visitUuid);

    // Log for debugging
    console.log("handleEditVisit called:", { patientUuid, visitUuid, visitName, visitStatus, visitDetails });

    if (!patient || !visitDetails) {
      toast.error("Could not find visit details");
      return;
    }

    const newSelectedVisit = {
      patientUuid,
      visitUuid,
      visitName,
      time: visitDetails.time || "not specified",
      visitStatus,
      location: visitDetails.location || "",
      nurse_identifier: visitDetails.nurse_details?.identifier || "",
      registration_status: visitDetails.registration_status || "null",
      date: selectedDate, // Use the selected date instead of today's date
      leading_team: visitDetails.leading_team || "null",
      activities: visitDetails.activities || [],
      comments: visitDetails.comments || ""
    };

    setSelectedVisit(newSelectedVisit);
    setIsEditPatientVisitModalOpen(true);
  };

  const handleViewVisitDetails = (patientUuid: string, visitUuid: string) => {
    // Find the visit details from the visits data
    const patient = visits?.find((v: PatientVisitToday) => v.uuid === patientUuid);
    const visitDetails = patient?.visit_details.find((vd: VisitDetail) => vd.uuid === visitUuid);

    if (!patient || !visitDetails) {
      toast.error("Could not find visit details");
      return;
    }

    // Get staff member name if available
    let staffMemberName = "Not assigned";
    if (visitDetails.nurse_details) {
      staffMemberName = `${visitDetails.nurse_details.first_name} ${visitDetails.nurse_details.last_name}`;
    }

    const newSelectedVisit = {
      patientUuid,
      visitUuid,
      visitName: visitDetails.name,
      visitStatus: visitDetails.visit_status,
      location: visitDetails.location || "",
      nurse_identifier: staffMemberName,
      registration_status: visitDetails.registration_status,
      date: selectedDate, // Use the selected date instead of today's date
      time: visitDetails.time || "Not specified", // Add default value to prevent undefined
      leading_team: visitDetails.leading_team,
      activities: visitDetails.activities,
      comments: visitDetails.comments
    };

    setSelectedVisit(newSelectedVisit);
    setIsVisitDetailsModalOpen(true);
  };
  console.log("Selected Visit after set:", selectedVisit); // This will show the previous state
  // const handleUpdateVisit = async (any) => {
  //   if (selectedVisit) {
  //     try {
  //       // This function is not currently used but kept for reference
  //       // await updateVisitMutation.mutateAsync({
  //       //   visitUuid: selectedVisit.visitUuid,
  //       //   data
  //       // });
  //       setIsEditPatientVisitModalOpen(false);
  //       setSelectedVisit(null);
  //     } catch (error) {
  //       console.error('Error updating visit:', error);
  //     }
  //   }
  // };

  const handleRedirect = (uuid: string) => {
    const visit = visits?.find((v: PatientVisitToday) => v.uuid === uuid);
    if (visit) {
      // Add access log from merged branch
      addPatientAccessLogMutation.mutate({
        patient_uuid: uuid,
        access_type: 'view'
      });

      const patient: Patient = {
        uuid: visit.uuid,
        nhs_number: visit.patient_details.nhs_number,
        first_name: visit.patient_details.first_name,
        last_name: visit.patient_details.last_name,
        date_of_birth: visit.patient_details.date_of_birth || '',
        gender: visit.patient_details.gender || '',
        allergies: [],
        profile_picture: visit.patient_details.profile_picture || '',
        profile_picture_url: visit.patient_details.profile_picture_url || '',
        created_at: visit.patient_details.created_at || '',
        updated_at: visit.patient_details.updated_at || '',
        forms_uuid: []
      };

      setSelectedPatient(patient);
      navigate('/org/dashboard/patient-board');
    }
  };

  const categoryOptions = (() => {
    return categories.map((category) => ({
      label: category.label,
      value: category.id,
      count: category.count,
      checked: selectedCategories.includes(category.id),
      onChange: () => handleCategoryChange(category.id)
    }));
  })();
    

  

  const filters: NurtifyFilterItem[] = [
      {
      label: "Categories",
      type: "checkbox",
      options: categoryOptions,
      value: selectedCategories,
      onChange: setSelectedCategories as any,
    },
      
    ];

  // Handle loading and error states
  if (isLoading) {
    return <div className="loading">
      {selectedDate === new Date().toISOString().split('T')[0]
        ? "Loading today's visits..."
        : `Loading visits for ${new Date(selectedDate).toLocaleDateString()}...`}
    </div>;
  }

  if (error) {
    return <div className="error">
      {selectedDate === new Date().toISOString().split('T')[0]
        ? `Error loading today's visits: ${(error as Error).message}`
        : `Error loading visits for ${new Date(selectedDate).toLocaleDateString()}: ${(error as Error).message}`}
    </div>;
  }

  // We'll handle the empty state differently - we'll show the UI but with a message instead of the table
  const hasVisits = visits && visits.length > 0;

  return (
    <div className="content-wrapper js-content-wrapper">
      <div className="bg-light-4 px-3 py-5">
        <div className="container-fluid py-6 px-6">
          <div className="row y-gap-30">
            {/* Sidebar */}
            <div className="col-xl-3 col-lg-4">
                <div className="d-flex items-center gap-2 text-gray-800 mb-2 mt-3 pt-2">
                  <Filter className="text-indigo-600 h-6 w-6" />
                  <h3 className="text-2xl font-bold">Filter</h3>
                </div>
              <NurtifyFilter layout="horizontal" filters={filters} />
              
            </div>

            {/* Main content */}
            <div className="col-xl-9 col-lg-8">
              <div className="patient-details-container">
                <div className="patient-details-header">
                  <h1 className="page-title">
                    {selectedDate === new Date().toISOString().split('T')[0]
                      ? "Today's Visits"
                      : `Visits for ${new Date(selectedDate).toLocaleDateString()}`}
                  </h1>
                </div>

                {/* View toggle and date controls */}
                <div style={{
                  display: 'flex',
                  justifyContent: 'space-between',
                  alignItems: 'center',
                  marginBottom: '20px',
                  position: 'relative',
                  zIndex: 10,
                }}>
                  <div style={{ display: 'flex', alignItems: 'center', gap: '15px' }}>
                    <h3 style={{ margin: 0, fontSize: '18px', fontWeight: 600 }}>
                      Manage and view visits by date:
                    </h3>

                    {/* View toggle buttons */}
                    <div style={{
                      display: 'flex',
                      border: '1px solid #ddd',
                      borderRadius: '4px',
                      overflow: 'hidden',
                      marginLeft: '15px'
                    }}>
                      <button
                        onClick={() => setViewMode('list')}
                        style={{
                          padding: '8px 16px',
                          background: viewMode === 'list' ? '#23b7cd' : '#f5f5f5',
                          color: viewMode === 'list' ? 'white' : 'black',
                          border: 'none',
                          cursor: 'pointer',
                          display: 'flex',
                          alignItems: 'center',
                          gap: '5px'
                        }}
                      >
                        <List size={16} />
                        List
                      </button>
                      <button
                        onClick={() => setViewMode('calendar')}
                        style={{
                          padding: '8px 16px',
                          background: viewMode === 'calendar' ? '#23b7cd' : '#f5f5f5',
                          color: viewMode === 'calendar' ? 'white' : 'black',
                          border: 'none',
                          cursor: 'pointer',
                          display: 'flex',
                          alignItems: 'center',
                          gap: '5px'
                        }}
                      >
                        <Calendar size={16} />
                        Calendar
                      </button>
                    </div>
                    {/* Navigation buttons - shown in both list and calendar views */}
                    <div style={{ display: 'flex', alignItems: 'center', gap: '10px' }}>
                      <button
                        onClick={goToPrevious}
                        style={{
                          background: '#23b7cd',
                          border: 'none',
                          borderRadius: '50%',
                          width: '32px',
                          height: '32px',
                          display: 'flex',
                          alignItems: 'center',
                          justifyContent: 'center',
                          cursor: 'pointer',
                          color: 'white'
                        }}
                        title={`Previous ${viewMode === 'list' ? 'day' : calendarViewType === 'D' ? 'day' : calendarViewType === 'W' ? 'week' : 'month'}`}
                      >
                        <ChevronLeft size={18} />
                      </button>

                      {viewMode === 'list' && (
                        <div style={{ width: '200px', position: 'relative', zIndex: 9999 }}> {/* Increase z-index for date picker */}
                          <NurtifyDateInput
                            value={selectedDate}
                            onChange={handleDateChange}
                            label=""
                          />
                        </div>
                      )}

                      <button
                        onClick={goToNext}
                        style={{
                          background: '#23b7cd',
                          border: 'none',
                          borderRadius: '50%',
                          width: '32px',
                          height: '32px',
                          display: 'flex',
                          alignItems: 'center',
                          justifyContent: 'center',
                          cursor: 'pointer',
                          color: 'white'
                        }}
                        title={`Next ${viewMode === 'list' ? 'day' : calendarViewType === 'D' ? 'day' : calendarViewType === 'W' ? 'week' : 'month'}`}
                      >
                        <ChevronRight size={18} />
                      </button>
                    </div>
                  </div>
                </div>

                {/* Conditionally render list or calendar view based on viewMode */}
                {viewMode === 'list' ? (
                  // List View
                  hasVisits ? (
                    <DataTable<PatientVisitToday>
                      data={categoryFilteredVisits}
                      columns={[
                        {
                          key: 'uuid',
                          header: "Patient's Name",
                          sortable: true,
                          render: (_: string | PatientVisitToday['patient_details'] | PatientVisitToday['visit_details'], row?: PatientVisitToday) => {
                            if (!row?.patient_details) return '';
                            return `${row.patient_details.first_name} ${row.patient_details.last_name}`;
                          }
                        },
                        {
                          key: 'uuid',
                          header: 'NHS Number',
                          sortable: true,
                          render: (_: string | PatientVisitToday['patient_details'] | PatientVisitToday['visit_details'], row?: PatientVisitToday) => {
                            if (!row?.patient_details) return '';
                            return row.patient_details.nhs_number;
                          }
                        },
                        {
                          key: 'uuid',
                          header: 'Registration Status',
                          sortable: false,
                          render: (_: string | PatientVisitToday['patient_details'] | PatientVisitToday['visit_details'], row?: PatientVisitToday) => {
                            if (!row?.visit_details || !Array.isArray(row.visit_details)) return null;
                            return (
                              <div>
                                {row.visit_details.map((visit) => (
                                  <div key={visit.uuid} className="visit-detail">
                                    <span className="visit-name">{visit.registration_status}</span>
                                  </div>
                                ))}
                              </div>
                            );
                          }
                        },
                        {
                          key: 'uuid',
                          header: 'Study',
                          sortable: true,
                          render: (_: string | PatientVisitToday['patient_details'] | PatientVisitToday['visit_details'], row?: PatientVisitToday) => {
                            if (!row?.visit_details || !Array.isArray(row.visit_details)) return 'N/A';
                            return row.visit_details[0]?.study_name || 'N/A';
                          }
                        },
                        {
                          key: 'uuid',
                          header: 'Room',
                          sortable: true,
                          render: (_: string | PatientVisitToday['patient_details'] | PatientVisitToday['visit_details'], row?: PatientVisitToday) => {
                            if (!row?.visit_details || !Array.isArray(row.visit_details)) return 'N/A';
                            return row.visit_details[0]?.location || 'N/A';
                          }
                        },
                        {
                          key: 'uuid',
                          header: 'Profile Picture',
                          sortable: false,
                          render: (_: string | PatientVisitToday['patient_details'] | PatientVisitToday['visit_details'], row?: PatientVisitToday) => {
                            if (!row?.patient_details) return null;

                            const profilePictureUrl = row.patient_details.profile_picture_url;

                            if (profilePictureUrl) {
                              const fullUrl = profilePictureUrl.startsWith('http')
                                ? profilePictureUrl
                                : `${import.meta.env.VITE_API_URL || 'http://localhost:8000'}/${profilePictureUrl.startsWith('/') ? profilePictureUrl.substring(1) : profilePictureUrl}`;

                              return (
                                <div className="patient-profile-picture">
                                  <img
                                    src={fullUrl}
                                    alt={`${row.patient_details.first_name} ${row.patient_details.last_name}`}
                                    onError={(e) => {
                                      (e.target as HTMLImageElement).style.display = 'none';
                                      (e.target as HTMLImageElement).nextElementSibling?.classList.remove('hidden');
                                    }}
                                  />
                                  <div className="profile-picture-placeholder hidden">
                                    <User size={16} />
                                  </div>
                                </div>
                              );
                            }

                            return (
                              <div className="patient-profile-picture">
                                <div className="profile-picture-placeholder">
                                  <User size={16} />
                                </div>
                              </div>
                            );
                          }
                        }
                      ]}
                      actions={[
                        {
                          icon: <Info size={18} />,
                          tooltipText: "View visit details",
                          onClick: (visit: PatientVisitToday) => {
                            if (visit.visit_details.length > 0) {
                              const firstVisit = visit.visit_details[0];
                              const patientUuid = visit.uuid;
                              handleViewVisitDetails(patientUuid, firstVisit.uuid);
                            } else {
                              toast.error("No visit details available to view");
                            }
                          },
                        },
                        {
                          icon: <Edit2 size={18} />,
                          tooltipText: "Edit visit",
                          onClick: (visit: PatientVisitToday) => {
                            if (visit.visit_details.length > 0) {
                              const firstVisit = visit.visit_details[0];
                              // Ensure patientUuid is correct; adjust if patient UUID is elsewhere
                              const patientUuid = visit.uuid; // Confirm this is patient UUID
                              console.log("Opening modal for:", { patientUuid, visitUuid: firstVisit.uuid, visit });
                              handleEditVisit(
                                patientUuid,
                                firstVisit.uuid,
                                firstVisit.name,
                                firstVisit.visit_status
                              );
                            } else {
                              toast.error("No visit details available to edit");
                              console.error("No visit details for patient:", visit);
                            }
                          },
                        },
                        {
                          icon: <ArrowUpRight size={18} />,
                          tooltipText: "View patient details",
                          onClick: (visit: PatientVisitToday) => handleRedirect(visit.uuid),
                        },
                      ]}
                      noDataMessage="No visits available"
                      defaultItemsPerPage={10}
                    />
                  ) : (
                    <div className="no-visits-message" style={{
                      textAlign: 'center',
                      padding: '40px 20px',
                      fontSize: '18px',
                      backgroundColor: '#f8f9fa',
                      borderRadius: '8px',
                      marginTop: '20px'
                    }}>
                      {selectedDate === new Date().toISOString().split('T')[0]
                        ? "No visits scheduled for today."
                        : `No visits scheduled for ${new Date(selectedDate).toLocaleDateString()}.`}
                    </div>
                  )
                ) : (
                  // Calendar View
                  <VisitCalendar
                    onViewVisitDetails={handleViewVisitDetails}
                    onEditVisit={handleEditVisit}
                    hideDatePicker={true} // Hide the date picker in the calendar component
                    externalSelectedDate={selectedDate} // Control the date from the parent component
                    onDateChange={(date) => setSelectedDate(date)} // Update parent state when date changes in calendar
                    onViewTypeChange={(viewType) => setCalendarViewType(viewType)} // Update parent state when view type changes
                    initialViewType={calendarViewType} // Pass the current view type to initialize the calendar
                  />
                )}
              </div>
            </div>
          </div>
        </div>
      </div>

      <LightFooter />

      {/* Edit Visit Modal */}
      {isEditPatientVisitModalOpen && selectedVisit && (
        <EditPatientVisitModal
          isOpen={isEditPatientVisitModalOpen}
          onClose={() => {
            setIsEditPatientVisitModalOpen(false);
            setSelectedVisit(null);
          }}
          patientUuid={selectedVisit.patientUuid}
          visitUuid={selectedVisit.visitUuid}
          visitData={{
            name: selectedVisit.visitName,
            visit_status: selectedVisit.visitStatus,
            registration_status: selectedVisit.registration_status,
            date: selectedVisit.date,
            time: selectedVisit.time,
            location: selectedVisit.location,
            nurse_identifier: selectedVisit.nurse_identifier,
            leading_team: selectedVisit.leading_team,
            activities: selectedVisit.activities,
            comments: selectedVisit.comments
          }}
        />
      )}

      {/* Visit Details Modal */}
      {isVisitDetailsModalOpen && selectedVisit && (
        <VisitDetailsModal
          isOpen={isVisitDetailsModalOpen}
          onClose={() => {
            setIsVisitDetailsModalOpen(false);
          }}
          visitData={{
            patientName: `${visits?.find((v: PatientVisitToday) => v.uuid === selectedVisit.patientUuid)?.patient_details.first_name} ${visits?.find((v: PatientVisitToday) => v.uuid === selectedVisit.patientUuid)?.patient_details.last_name}`,
            room: selectedVisit.location,
            registrationStatus: selectedVisit.registration_status,
            staffMember: selectedVisit.nurse_identifier,
            leading_team: selectedVisit.leading_team,
            activities: selectedVisit.activities,
            comments: selectedVisit.comments,
            time: selectedVisit.time
          }}
          visitUuid={selectedVisit.visitUuid}
        />
      )}
    </div>
  );
};

export default ListOfPatients;

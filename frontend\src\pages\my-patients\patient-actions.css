/* MyPatient Page Header Styling */
.mypatient-header {
  margin-bottom: 30px;
}

.mypatient-title {
  font-size: 28px;
  font-weight: 600;
  color: #333;
  margin-bottom: 20px;
  margin-top: 0;
}

.mypatient-search-section {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 20px;
}

.mypatient-search-controls {
  display: flex;
  align-items: center;
  gap: 12px;
  position: relative;
}

.mypatient-search-dropdown {
  padding: 8px 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
  background: white;
  font-size: 14px;
  color: #666;
  min-width: 120px;
}

.mypatient-search-input {
  padding: 8px 40px 8px 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 14px;
  width: 300px;
  outline: none;
}

.mypatient-search-input:focus {
  border-color: #23b7cd;
  box-shadow: 0 0 0 2px rgba(35, 183, 205, 0.1);
}

.mypatient-search-icon {
  position: absolute;
  right: 12px;
  color: #999;
  pointer-events: none;
}

.mypatient-add-btn {
  background: #23b7cd;
  color: white;
  border: none;
  border-radius: 4px;
  padding: 8px 16px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.mypatient-add-btn:hover {
  background: #1c8e98;
}

/* MyPatient Page Actions Styling */
.mypatient-actions-container {
  display: flex;
  gap: 8px;
  align-items: center;
  justify-content: flex-start;
}

.mypatient-action-btn {
  padding: 6px 12px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 12px;
  font-weight: 500;
  transition: all 0.2s ease;
  white-space: nowrap;
}

.mypatient-action-btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.mypatient-action-btn.open-patient {
  background: #23b7cd;
  color: white;
}

.mypatient-action-btn.open-patient:hover {
  background: #1c8e98;
}

.mypatient-action-btn.remove-patient {
  background: #dc3545;
  color: white;
}

.mypatient-action-btn.remove-patient:hover {
  background: #c82333;
}

/* Button group styling for the top action buttons */
.mypatient-view-buttons {
  display: flex;
  gap: 8px;
  align-items: center;
}

.mypatient-view-btn {
  padding: 10px 16px;
  background: #23b7cd;
  color: white;
  border: 1px solid #ddd;
  border-radius: 6px;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 6px;
  font-weight: 500;
  transition: all 0.2s ease;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.mypatient-view-btn:hover {
  background: #1c8e98;
  transform: translateY(-1px);
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.15);
}

.mypatient-view-btn:active {
  background: #158a94;
  transform: translateY(0);
}

/* Improved spacing for the main container */
.mypatient-controls-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
  padding: 16px 0;
  border-bottom: 1px solid #e9ecef;
}

.mypatient-search-container {
  width: 320px;
}

.mypatient-actions-right {
  display: flex;
  align-items: center;
  gap: 16px;
}

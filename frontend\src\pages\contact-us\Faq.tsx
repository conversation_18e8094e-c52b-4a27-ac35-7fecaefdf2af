import { useState, useEffect } from "react";
import NurtifyAccordion from "@/components/NurtifyAccordion"; // Import the new component

export default function Faq(): JSX.Element {
  const [searchTerm, setSearchTerm] = useState("");
  // Define the type for FAQ items explicitly
  interface FaqItem {
    id: number;
    question: string;
    answer: string;
  }

  const faqData: FaqItem[] = [
    {
      id: 1,
      "question": "What is Nurtify CRM and How Does It Work?",
      "answer": "Nurtify CRM is a specialized platform designed for clinical research facilities to automate administrative tasks and ensure compliance with good practice regulations. It streamlines workflow management, enhances operational efficiency, and facilitates seamless collaboration within research teams.\n\nKey Features:\n1. **Automation of Administrative Tasks** - Reduce manual workload with automated scheduling, documentation, and reporting.\n2. **Compliance & Best Practices** - Ensure adherence to regulatory standards with built-in compliance tools.\n3. **Process Optimization** - Enhance efficiency with structured workflows for study management.\n4. **Team Collaboration** - Facilitate seamless communication and data sharing across departments.\n\nContact our sales <NAME_EMAIL> for more details."
    },
    {
      "id": 2,
      "question": "Who Can Benefit from Nurtify CRM?",
      "answer": "Nurtify CRM is designed for clinical research facilities, investigators, study coordinators, and administrative personnel. It helps research teams manage trials efficiently while ensuring compliance with industry standards."
    },
    {
      "id": 3,
      "question": "How Much Does Nurtify CRM Cost?",
      "answer": "Nurtify CRM offers a flexible pricing model based on the size and needs of your clinical research facility. Contact our sales <NAME_EMAIL> for a customized quote."
    },
    {
      "id": 4,
      "question": "How Does Nurtify CRM Improve Workflow Efficiency?",
      "answer": "Nurtify CRM enhances workflow efficiency by automating scheduling, documentation, and reporting. It also provides real-time tracking of ongoing studies, ensuring seamless coordination between research teams."
    },
    {
      "id": 5,
      "question": "What Compliance and Regulatory Features Does Nurtify CRM Offer?",
      "answer": "Nurtify CRM includes compliance management tools that help research facilities adhere to GCP (Good Clinical Practice) guidelines, FDA regulations, and other industry standards. It ensures proper documentation, audit readiness, and regulatory adherence."
    },
    {
      "id": 6,
      "question": "How Do I Get Started with Nurtify CRM?",
      "answer": "Getting started with Nurtify CRM is simple:\n1. **Request a Demo** - Contact our <NAME_EMAIL> to schedule a personalized demo.\n2. **Setup & Customization** - Our team will assist you in configuring the CRM to fit your facility’s specific needs.\n3. **Training & Implementation** - We provide training to ensure your team can effectively use the system.\n4. **Go Live** - Start managing your research projects with improved efficiency and compliance."
    },
    {
      "id": 7,
      "question": "What is the Nurtify CRM Support Policy?",
      answer: "Nurtify CRM offers comprehensive support services to ensure your success. Our support team is available to assist with any questions or issues you may encounter."
    },
  ];

  const [filteredFaqs, setFilteredFaqs] = useState<FaqItem[]>(faqData); // Initialize with all data

  useEffect(() => {
    setFilteredFaqs(
      faqData.filter(
        (item) =>
          item.question.toLowerCase().includes(searchTerm.toLowerCase()) ||
          item.answer.toLowerCase().includes(searchTerm.toLowerCase())
      )
    );
  }, [searchTerm]);

  // Shared style for both search and accordion container
  const contentContainerStyle: React.CSSProperties = {
    padding: "20px 80px",
    maxWidth: "900px",
    margin: "0 auto"
  };

  return (
    <section className="layout-pt-lg layout-pb-lg">
      <div className="container mb-5">
        <div className="row justify-center text-center">
          <div className="col-xl-12 col-lg-12 col-md-12">
              <h2 className="sectionTitle__title">
                Frequently Asked Questions
              </h2>

              <p className="sectionTitle__text">
                Find answers to common questions about Nurtify CRM and how it can benefit your clinical research
              </p>
            {/* Search Bar */}
            <div className="search-container mt-30 mb-20 mx-auto" style={contentContainerStyle}>
              <div className="search-wrapper" style={{ 
                position: 'relative',
                display: 'flex',
                alignItems: 'center',
                borderRadius: '16px',
                overflow: 'hidden',
                marginBottom: '30px',
                background: "none",
                border:"1px solid #EBF4F6",

              }}>
                <input
                  type="text"
                  placeholder="Search"
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  style={{
                    padding: '15px 48px 15px 24px', // extra right padding for icon
                    width: '100%',
                    minWidth: '100%',
                    maxWidth: '100%',
                    fontSize: '18px',
                    outline: 'none',
                    borderRadius:"14px",
                    background: "none"
                  }}
                />
                {/* The icon is visually inside the input by absolute positioning */}
                <span style={{
                  position: 'absolute',
                  right: '24px',
                  top: '50%',
                  transform: 'translateY(-50%)',
                  color: '#666',
                  fontSize: '20px',
                  display: 'flex',
                  alignItems: 'center',
                  pointerEvents: 'none', // so input is clickable under icon
                }}>
                  <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="#666" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                    <circle cx="11" cy="11" r="8" />
                    <line x1="21" y1="21" x2="16.65" y2="16.65" />
                  </svg>
                </span>
              </div>
            </div>

            {/* Use the new NurtifyAccordion component */}
            <div className="container" style={contentContainerStyle}>
              {filteredFaqs.length > 0 ? (
                <NurtifyAccordion items={filteredFaqs} />
              ) : (
                <div className="no-results" style={{
                  padding: '40px 20px',
                  textAlign: 'center',
                  background: 'none',
                  borderRadius: '10px',
                  margin: '30px 0'
                }}>
                  <h3 style={{ marginBottom: '15px', color: '#555' }}>No matching questions found</h3>
                  <p style={{ marginBottom: '20px', color: '#666' }}>Try different keywords or browse all questions by clearing the search.</p>
                  <button
                    onClick={() => setSearchTerm('')} // Clear search term
                    style={{
                      backgroundColor: '#3b82f6',
                      color: 'white',
                      border: 'none',
                      padding: '10px 20px',
                      borderRadius: '5px',
                      cursor: 'pointer',
                      fontWeight: '500'
                    }}
                  >
                    Show All FAQs
                  </button>
                </div>
              )}
            </div>

            {/* Still have questions call to action */}
            <div className="faq-cta" style={{
              marginTop: '50px',
              padding: '30px',
              borderRadius: '15px',
              border:"1px solid #21858E40",
              // maxWidth: '800px',
              margin: '50px auto 0'
            }}>
              <h3 style={{ marginBottom: '15px', fontSize: '22px' }}>Still have questions?</h3>
              <p style={{ marginBottom: '25px', color: '#666' }}>
                Our team is here to help. Contact us for personalized assistance.
              </p>
              <a 
                href="mailto:<EMAIL>"
                className="button -md -purple-1 text-white"
                style={{
                  display: 'inline-flex',
                  alignItems: 'center',
                  // backgroundColor: '#3b82f6',
                  // color: 'white !important',
                  padding: '12px 25px',
                  fontWeight: '500',
                  textDecoration: 'none',
                  transition: 'all 0.3s ease'
                }}
              >
                Contact Support
              </a>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}

import api from "@/services/api";

// Staff document interface
export interface StaffDocument {
  uuid: string;
  document_type: string;
  document_name: string | null;
  file_url: string;
  display_name: string;
  file_size: number;
  expiry_date: string | null;
  is_expired: boolean;
  days_until_expiry: number | null;
  uploaded_at: string;
  updated_at: string;
  staff_uuid: string;
  staff_name: string;
}

// Staff documents response interface
export interface StaffDocumentsResponse {
  count: number;
  next: string | null;
  previous: string | null;
  results: StaffDocument[];
}

// Get staff documents for a specific staff member
export const getStaffDocuments = async (staffUuid: string): Promise<StaffDocument[]> => {
  try {
    const response = await api.get(`/study/studies/sponsor-staff/${staffUuid}/documents/`);
    return response.data;
  } catch (error) {
    console.error("Error fetching staff documents:", error);
    throw error;
  }
};

// Get all staff documents for sponsor's studies
export const getAllStaffDocuments = async (): Promise<StaffDocumentsResponse> => {
  try {
    const response = await api.get("/study/studies/sponsor-staff/documents/");
    return response.data;
  } catch (error) {
    console.error("Error fetching all staff documents:", error);
    throw error;
  }
};

// Download staff document
export const downloadStaffDocument = async (documentUuid: string): Promise<Blob> => {
  try {
    const response = await api.get(`/study/studies/sponsor-staff/documents/${documentUuid}/download/`, {
      responseType: "blob",
    });
    return response.data;
  } catch (error) {
    console.error("Error downloading staff document:", error);
    throw error;
  }
}; 
import { useState, useEffect, useMemo, ReactN<PERSON>, useCallback } from "react";
import { useLocation } from "react-router-dom";
import StudyCard from "@/components/StudyCard";
import useScheduleEventStore, {
  Study,
  Visit,
  VisitTemplate,
} from "@/store/scheduleEventState";
import "./schedule-event.css";
import "../users-list/usersList.css";
import "./studyTeam.css";
import {
  Plus,
  X, 
  Search,
  Users,
  UserCheck,
  CheckCircle,
  Clock,
  BarChart3
} from "lucide-react";
import Preloader from "@/components/common/Preloader";
import Wrapper from "@/components/common/Wrapper";
import VisitModal from "@/components/modal/VisitModal";
import ViewToggle, { ViewType } from "@/components/common/ViewToggle";
import { useCurrentUserQuery, useUsersByDepartmentQuery } from "@/hooks/user.query";
import {
  useStudyQuery,
  useUpdateVisitMutation,
  useVisitTemplatesByStudyQuery,
  useStudyTeamMembersQuery,
  usePatchStudyMutation,
  useStudiesByDepartmentQuery,
  useAvailableInvestigatorsQuery,
  useStudiesNeedingPIQuery,
  useAssignPIMutation
} from "@/hooks/study.query";
import { User } from "@/types/types";
import DataTable, { DataTableSearch } from "@/components/common/DataTable";
import { UserMinus, UserPlus } from "lucide-react";
import { useQueryClient } from "@tanstack/react-query";
import { STUDY_KEYS } from "@/hooks/keys";
import VisitCalendar from '@/components/calendar/VisitCalendar';
import VisitDetailsByDateModal from '@/components/modal/VisitDetailsByDateModal';
import { ViewMode } from '@/components/calendar/ViewToggle';
import PIAssignmentsTracker from '@/components/PIAssignmentsTracker';

// Helper function to get query parameters from URL
const useQuery = () => {
  return new URLSearchParams(useLocation().search);
};

// Main ScheduleEvent component - now focused on PI Assignment
export default function ScheduleEvent(): JSX.Element {
  const { setCurrentStudy } = useScheduleEventStore();
  const [activeTab, setActiveTab] = useState<number>(1);
  const query = useQuery();
  const studyId = query.get("studyId");
  const { data: currentUser } = useCurrentUserQuery();
  
  // Fetch study data if studyId is provided
  const { data: study } = useStudyQuery(studyId || "");

  // Set current study when studyId is provided in URL
  useEffect(() => {
    if (studyId && study) {
      const studyWithUuid = {
        ...study,
        id: study.uuid || study.id,
        uuid: study.uuid || study.id
      };
      setCurrentStudy(studyWithUuid);
    }
  }, [studyId, study, setCurrentStudy]);

  return (
    <>
      <Wrapper>
        <Preloader />

        <div className="content-wrapper js-content-wrapper overflow-hidden" style={{paddingBottom: "88px"}}>
          <div className="dashboard__content bg-light-4">
            <div className="row y-gap-20">
              <div className="col-12">
                <div className="tabs-wrapper">
                  <button
                    onClick={() => setActiveTab(1)}
                    className={`tab-button ${activeTab === 1 ? "active" : ""}`}
                    type="button"
                  >
                    PI Assignment
                  </button>
                  <button
                    onClick={() => setActiveTab(2)}
                    className={`tab-button ${activeTab === 2 ? "active" : ""}`}
                    type="button"
                  >
                    Study Setup
                  </button>
                  <button
                    onClick={() => setActiveTab(3)}
                    className={`tab-button ${activeTab === 3 ? "active" : ""}`}
                    type="button"
                  >
                    Study Team
                  </button>
                  <button
                    onClick={() => setActiveTab(4)}
                    className={`tab-button ${activeTab === 4 ? "active" : ""}`}
                    type="button"
                  >
                    <BarChart3 size={16} style={{ marginRight: "8px" }} />
                    PI Tracker
                  </button>
                </div>

                <div className="tabs__content pt-30">
                  {activeTab === 1 && <PIAssignmentPhase />}
                  {activeTab === 2 && <StudySetupPhase />}
                  {activeTab === 3 && <StudyTeam />}
                  {activeTab === 4 && (
                    <div className="pi-tracker-container">
                      <div className="pi-tracker-header">
                        <div className="pi-header-content">
                          <h1>
                            <BarChart3 size={24} style={{ marginRight: "10px" }} />
                            PI Assignments Tracker
                          </h1>
                          <p>Monitor and track Principal Investigator assignments across all studies</p>
                        </div>
                      </div>
                      <PIAssignmentsTracker departmentUuid={currentUser?.department?.uuid} />
                    </div>
                  )}
                </div>
              </div>
            </div>
          </div>
        </div>
      </Wrapper>
    </>
  );
}

// New PI Assignment Component
const PIAssignmentPhase = () => {
  const { data: currentUser } = useCurrentUserQuery();
  const departmentUuid = currentUser?.department?.uuid || '';
  
  const { data: studiesNeedingPI, isLoading: isLoadingStudiesNeedingPI } = useStudiesNeedingPIQuery(departmentUuid);
  const { data: availableInvestigators, isLoading: isLoadingInvestigators } = useAvailableInvestigatorsQuery(departmentUuid);
  const assignPIMutation = useAssignPIMutation();
  
  const [selectedStudy, setSelectedStudy] = useState<any>(null);
  const [selectedInvestigator, setSelectedInvestigator] = useState<any>(null);
  const [isAssignmentModalOpen, setIsAssignmentModalOpen] = useState(false);
  const [searchTerm, setSearchTerm] = useState("");

  // Filter studies based on search term
  const filteredStudies = studiesNeedingPI?.studies_needing_pi?.filter((study: any) => {
    const matchesSearch = study.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      (study.description && study.description.toLowerCase().includes(searchTerm.toLowerCase()));
    return matchesSearch;
  }) || [];

  const handleAssignPI = () => {
    if (!selectedStudy || !selectedInvestigator || !departmentUuid) return;

    assignPIMutation.mutate(
      {
        study: selectedStudy.uuid,
        department: departmentUuid,
        investigator: selectedInvestigator.identifier
      },
      {
        onSuccess: () => {
          setIsAssignmentModalOpen(false);
          setSelectedStudy(null);
          setSelectedInvestigator(null);
        }
      }
    );
  };

  return (
    <div className="pi-assignment-container">
      <div className="pi-assignment-header">
        <div className="pi-header-content">
          <h1>
            <UserCheck size={24} style={{ marginRight: "10px" }} />
            Principal Investigator Assignment
          </h1>
          <p>Assign Principal Investigators to studies that need PI assignment</p>
        </div>
      </div>

      <div className="pi-assignment-content">
        {/* Summary Cards */}
        <div className="pi-summary-cards">
          <div className="pi-summary-card">
            <div className="pi-card-icon pi-pending">
              <Clock size={24} />
            </div>
            <div className="pi-card-content">
              <h3>{filteredStudies.length}</h3>
              <p>Studies Needing PI</p>
            </div>
          </div>
          <div className="pi-summary-card">
            <div className="pi-card-icon pi-available">
              <UserPlus size={24} />
            </div>
            <div className="pi-card-content">
              <h3>{availableInvestigators?.available_investigators?.length || 0}</h3>
              <p>Available Investigators</p>
            </div>
          </div>
        </div>

        {/* Studies Needing PI */}
        <div className="pi-studies-section">
          <div className="pi-section-header">
            <h2>Studies Requiring PI Assignment</h2>
            <div className="pi-search-container">
              <Search size={18} className="pi-search-icon" />
              <input
                type="text"
                placeholder="Search studies..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pi-search-input"
              />
            </div>
          </div>

          {isLoadingStudiesNeedingPI ? (
            <div className="pi-loading-message">Loading studies...</div>
          ) : filteredStudies.length > 0 ? (
            <div className="pi-studies-grid">
              {filteredStudies.map((study: any) => (
                <div key={study.uuid} className="pi-study-card">
                  <div className="pi-study-header">
                    <h3>{study.name}</h3>
                    <span className="pi-study-status pi-pending">
                      <Clock size={14} />
                      Needs PI
                    </span>
                  </div>
                  <div className="pi-study-details">
                    <p className="pi-study-description">{study.description}</p>
                    <div className="pi-study-meta">
                      <span className="pi-sponsor">Sponsor: {study.sponsor_name}</span>
                      <span className="pi-created-date">
                        Created: {new Date(study.created_at).toLocaleDateString()}
                      </span>
                    </div>
                  </div>
                  <div className="pi-study-actions">
                    <button
                      className="pi-assign-btn"
                      onClick={() => {
                        setSelectedStudy(study);
                        setIsAssignmentModalOpen(true);
                      }}
                    >
                      <UserCheck size={16} />
                      Assign PI
                    </button>
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <div className="pi-no-studies">
              <UserCheck size={48} className="pi-placeholder-icon" />
              <h3>No Studies Need PI Assignment</h3>
              <p>All studies in your department have been assigned Principal Investigators.</p>
            </div>
          )}
        </div>
      </div>

      {/* PI Assignment Modal */}
      {isAssignmentModalOpen && (
        <div className="pi-modal-overlay">
          <div className="pi-assignment-modal">
            <div className="pi-modal-header">
              <h2>Assign Principal Investigator</h2>
              <button
                className="pi-close-btn"
                onClick={() => setIsAssignmentModalOpen(false)}
              >
                <X size={20} />
              </button>
            </div>
            
            <div className="pi-modal-content">
              <div className="pi-study-info">
                <h3>Study: {selectedStudy?.name}</h3>
                <p>{selectedStudy?.description}</p>
              </div>

              <div className="pi-investigator-selection">
                <h4>Select Principal Investigator</h4>
                {isLoadingInvestigators ? (
                  <div className="pi-loading-message">Loading investigators...</div>
                ) : availableInvestigators?.available_investigators?.length > 0 ? (
                  <div className="pi-investigators-list">
                    {availableInvestigators.available_investigators.map((investigator: any) => (
                      <div
                        key={investigator.identifier}
                        className={`pi-investigator-item ${selectedInvestigator?.identifier === investigator.identifier ? 'pi-selected' : ''}`}
                        onClick={() => setSelectedInvestigator(investigator)}
                      >
                        <div className="pi-investigator-info">
                          <h5>{investigator.first_name} {investigator.last_name}</h5>
                          <p className="pi-speciality">{investigator.speciality}</p>
                          <p className="pi-email">{investigator.email}</p>
                        </div>
                        {selectedInvestigator?.identifier === investigator.identifier && (
                          <CheckCircle size={20} className="pi-selected-icon" />
                        )}
                      </div>
                    ))}
                  </div>
                ) : (
                  <div className="pi-no-investigators">
                    <p>No available investigators found.</p>
                  </div>
                )}
              </div>
            </div>

            <div className="pi-modal-actions">
              <button
                className="pi-cancel-btn"
                onClick={() => setIsAssignmentModalOpen(false)}
              >
                Cancel
              </button>
              <button
                className="pi-confirm-btn"
                onClick={handleAssignPI}
                disabled={!selectedInvestigator || assignPIMutation.isPending}
              >
                {assignPIMutation.isPending ? 'Assigning...' : 'Assign PI'}
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

// Component for Phase 1: Setting up Studies (existing code)
const StudySetupPhase = () => {
  const {
    currentStudy,
    availableTests,
    setCurrentStudy,
  } = useScheduleEventStore();

  // Use React Query with proper error handling
  const { data: currentUser = {} as any } = useCurrentUserQuery();
  const { data: apiStudies, isLoading: isLoadingStudies } = useStudiesByDepartmentQuery(currentUser?.department?.uuid || '');
  const updateVisitMutation = useUpdateVisitMutation();
  const [searchTerm, setSearchTerm] = useState("");
  const [isVisitModalOpen, setIsVisitModalOpen] = useState(false);
  const [viewType, setViewType] = useState<ViewType>('list');
  const [currentCalendarDate] = useState<Date>(new Date());
  const [isVisitDetailsModalOpen, setIsVisitDetailsModalOpen] = useState(false);
  const [selectedModalDate, setSelectedModalDate] = useState<Date | null>(null);

  // Fetch visit templates for the current study only when a study is selected
  const { data: visitTemplates, isLoading: isLoadingVisitTemplates } = useVisitTemplatesByStudyQuery(
    currentStudy?.uuid || ""
  );

  // Filter studies based on search term
  const filteredStudies = apiStudies?.filter((study: Study) => {
    const matchesSearch = study.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      (study.description && study.description.toLowerCase().includes(searchTerm.toLowerCase()));
    return matchesSearch;
  });

  // Handle saving a visit from the modal
  const handleSaveVisit = (visitData: Partial<VisitTemplate>) => {
    if (!currentStudy) return;

    // Ensure study-related fields are set
    const enhancedVisitData = {
      ...visitData,
      study_uuid: visitData.study_uuid || currentStudy.uuid || currentStudy.id,
    };
    if (apiStudies && enhancedVisitData.uuid) {
      updateVisitMutation.mutate({
        uuid: enhancedVisitData.uuid,
        data: enhancedVisitData
      });
    }
  };

  // Handle changing visit status
  const handleVisitStatusChange = (visit: Visit, status: 'planned' | 'on-going' | 'completed' | 'cancelled') => {
    if (!currentStudy) return;

    const updatedVisit = {
      ...visit,
      status
    };

    if (apiStudies && visit.uuid) {
      updateVisitMutation.mutate({
        uuid: visit.uuid,
        data: updatedVisit
      });
    }
  };

  // Get status badge class
  const getStatusBadgeClass = (status?: string) => {
    switch (status) {
      case 'completed':
        return 'status-badge completed';
      case 'not completed':
        return 'status-badge not-completed';
      case 'cancelled':
        return 'status-badge cancelled';
      case 'on-going':
        return 'status-badge on-going';
      case 'planned':
        return 'status-badge planned';
      case 'pending':
      default:
        return 'status-badge pending';
    }
  };

  return (
    <div className="schedule-event-container">
      <div className="schedule-event-header">
        <h1>Studies</h1>
      </div>

      <div className="schedule-event-content">
        <div className="schedule-event-list-container">
          <div className="search-container">
            <input
              type="text"
              placeholder="Search studies..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="search-input"
            />
          </div>
          {isLoadingStudies ? (
            <div className="loading-studies">Loading studies...</div>
          ) : filteredStudies && filteredStudies.length > 0 ? (
            <div className="studies-list">
              {filteredStudies.map((study: Study) => (
                <StudyCard
                  key={study.uuid}
                  study={study}
                  onSelect={setCurrentStudy}
                />
              ))}
            </div>
          ) : (
            <div className="no-studies">
              {searchTerm ? "No studies match your search criteria" : "No studies available for your department"}
            </div>
          )}
        </div>

        {currentStudy && (
          <div className="visit-management-section">
            <div className="section-header">
              <h3>Visits for: {currentStudy.name}</h3>
            </div>

            {/* View toggle for list/calendar */}
            <div className="view-toggle-container">
              <ViewToggle
                currentView={viewType}
                onViewChange={setViewType}
              />
            </div>

            {/* List view */}
            {viewType === 'list' && (
              <>
                {/* Visit Templates Section */}
                <div className="visit-templates-section">
                  <h4>Visit Templates</h4>
                  {isLoadingVisitTemplates ? (
                    <div className="loading-visits">Loading visit templates...</div>
                  ) : visitTemplates && visitTemplates.length > 0 ? (
                    <div className="visits-list">
                      {visitTemplates.map((template: VisitTemplate) => (
                        <div key={template.uuid} className="visit-item">
                          <div className="visit-info">
                            <div className="visit-header">
                              <div className="visit-name">
                                {template.name}
                              </div>
                              <div className="visit-day">
                                Day {template.day}
                              </div>
                            </div>
                            <div className="visit-details">
                              <span className="visit-window">
                                Window: {template.allowed_earlier_days} days before to {template.allowed_later_days} days after
                              </span>
                            </div>
                            {template.activities && template.activities.length > 0 && (
                              <div className="visit-activities">
                                <strong>Activities:</strong> {template.activities.join(', ')}
                              </div>
                            )}
                            {template.comments && (
                              <div className="visit-comments">
                                <strong>Comments:</strong> {template.comments}
                              </div>
                            )}
                          </div>
                        </div>
                      ))}
                    </div>
                  ) : (
                    <div className="no-visits">No visit templates defined</div>
                  )}
                </div>

                {/* Regular Visits Section */}
                <div className="visits-section">
                  <h4>Scheduled Visits</h4>
                  {currentStudy?.visits?.length > 0 ? (
                    <div className="visits-list">
                      {currentStudy.visits.map((visit) => {
                        if ("dayNumber" in visit) {
                          const visitItem = visit as Visit;
                          return (
                            <div key={visitItem.id} className="visit-item">
                              <div className="visit-info">
                                <div className="visit-header">
                                  <div className="visit-name">
                                    {visitItem.name}
                                  </div>
                                  <div className={getStatusBadgeClass("status" in visitItem ? visitItem.status : undefined)}>
                                    {"status" in visitItem ? visitItem.status || 'Pending' : 'Pending'}
                                  </div>
                                </div>
                                <div className="visit-details">
                                  <span className="visit-reminders">
                                    Reminders: {"reminderDays" in visitItem ? visitItem.reminderDays?.join(', ') || '7' : '7'} days before
                                  </span>
                                </div>
                                {"comments" in visitItem && visitItem.comments && (
                                  <div className="visit-comments">
                                    <strong>Comments:</strong> {visitItem.comments}
                                  </div>
                                )}
                                <div className="visit-tests">
                                  <strong>Tests:</strong> {"tests" in visitItem && visitItem.tests && visitItem.tests.length > 0
                                    ? visitItem.tests.map((test: any) => test.name).join(', ')
                                    : 'None'}
                                </div>
                              </div>
                              <div className="visit-actions">
                                <select
                                  className="status-select"
                                  value={"status" in visitItem ? visitItem.status || 'planned' : 'planned'}
                                  onChange={(e) => {
                                    if ("dayNumber" in visitItem) {
                                      const visit = visitItem as Visit;
                                      handleVisitStatusChange(
                                        visit,
                                        e.target.value as 'planned' | 'on-going' | 'completed' | 'cancelled'
                                      );
                                    }
                                  }}
                                >
                                  <option value="planned">Planned</option>
                                  <option value="on-going">On-going</option>
                                  <option value="completed">Completed</option>
                                  <option value="cancelled">Cancelled</option>
                                </select>
                              </div>
                            </div>
                          );
                        } else {
                          return null;
                        }
                      })}
                    </div>
                  ) : (
                    <div className="no-visits">No visits scheduled yet</div>
                  )}
                </div>
              </>
            )}

            {/* Month view */}
            {viewType === 'calendar' && currentStudy && (
              <VisitCalendar
                visits={[]}
                isLoading={isLoadingStudies}
                error={null}
                onViewVisitDetails={() => {}}
                onEditVisit={() => {}}
                selectedDate={selectedModalDate ? selectedModalDate.toISOString().split('T')[0] : currentCalendarDate.toISOString().split('T')[0]}
                viewMode={'month' as ViewMode}
                onDateClick={(date: Date) => {
                  setSelectedModalDate(date);
                  setIsVisitDetailsModalOpen(true);
                }}
              />
            )}
          </div>
        )}

        {/* Visit Modal */}
        <VisitModal
          isOpen={isVisitModalOpen}
          onClose={() => setIsVisitModalOpen(false)}
          onSave={handleSaveVisit}
          availableTests={availableTests}
        />

        {/* Visit Details By Date Modal */}
        <VisitDetailsByDateModal
          isOpen={isVisitDetailsModalOpen}
          onClose={() => {
            setIsVisitDetailsModalOpen(false);
          }}
          selectedDate={selectedModalDate}
        />
      </div>
    </div>
  );
};

// Extended User type with fullName property for sorting
interface ExtendedUser extends User {
  fullName: string;
}

// Component for Study Team Management (existing code)
const StudyTeam = () => {
  const { currentStudy } = useScheduleEventStore();
  const { data: teamMembers, isLoading: isLoadingTeam } = useStudyTeamMembersQuery(currentStudy?.uuid ?? '');
  const currentUser = useCurrentUserQuery();
  const patchStudyMutation = usePatchStudyMutation();
  const queryClient = useQueryClient();
  const departmentUuid = currentUser.data?.department?.uuid || '';

  // Use React Query to fetch users from the department
  const { data: availableUsers, isLoading: isLoadingUsers, isError } = useUsersByDepartmentQuery(departmentUuid);

  const [isAddingUsers, setIsAddingUsers] = useState(false);
  const [selectedUserIds, setSelectedUserIds] = useState<string[]>([]);
  const [filteredData, setFilteredData] = useState<ExtendedUser[]>([]);

  // Handle removing a user from the team
  const handleExcludeUser = useCallback((userIdentifier: string) => {
    console.log('Removing user with identifier:', userIdentifier);
    console.log('Current team members:', teamMembers);
    console.log('Current study:', currentStudy);

    if (!teamMembers || !currentStudy?.uuid) {
      console.log('Missing required data:', { teamMembers, currentStudy });
      return;
    }
    
    // Get all team identifiers except the one we want to remove
    const newTeamIdentifiers = teamMembers
      .filter((user: User) => user.identifier !== userIdentifier)
      .map((user: User) => user.identifier);
    
    console.log('New team identifiers:', newTeamIdentifiers);
  
    // Update the study with the new team identifiers array
    patchStudyMutation.mutate(
      {
        uuid: currentStudy.uuid,
        data: { 
          team_identifiers: newTeamIdentifiers,
          team: [] // Ensure team array is also updated
        },
      },
      {
        onSuccess: (response) => {
          console.log('Successfully removed user from study team:', response);
          // Invalidate and refetch the team members query using the correct query key
          queryClient.invalidateQueries({ queryKey: [STUDY_KEYS.GET_TEAM_MEMBERS, currentStudy.uuid] });
          // Also invalidate the study query to ensure all study data is up to date
          queryClient.invalidateQueries({ queryKey: [STUDY_KEYS.GET_BY_UUID, currentStudy.uuid] });
        },
        onError: (error) => {
          console.error('Failed to remove user from study team:', error);
        }
      }
    );
  }, [teamMembers, currentStudy, patchStudyMutation, queryClient]);

  // Handle checkbox selection for adding users
  const handleUserSelection = (userIdentifier: string, isChecked: boolean) => {
    if (isChecked) {
      setSelectedUserIds(prev => [...prev, userIdentifier]);
    } else {
      setSelectedUserIds(prev => prev.filter(id => id !== userIdentifier));
    }
  };

  // Handle adding selected users to study team
  const handleAddSelectedUsers = () => {
    if (!teamMembers || selectedUserIds.length === 0) return;
  
    const newTeamIdentifiers = [
      ...teamMembers.map((user: User) => user.identifier),
      ...selectedUserIds
    ];
  
    patchStudyMutation.mutate(
      { 
        uuid: currentStudy?.uuid || '', 
        data: { team_identifiers: newTeamIdentifiers } 
      },
      {
        onSuccess: (response) => {
          console.log('Successfully added users to study team:', response);
          // Invalidate and refetch the team members query
          queryClient.invalidateQueries({ queryKey: [STUDY_KEYS.GET_TEAM_MEMBERS, currentStudy?.uuid] });
          // Also invalidate the study query to ensure all study data is up to date
          queryClient.invalidateQueries({ queryKey: [STUDY_KEYS.GET_BY_UUID, currentStudy?.uuid] });
          setSelectedUserIds([]);
          setIsAddingUsers(false);
        },
        onError: (error) => {
          console.error('Failed to add users to study team:', error);
        }
      }
    );
  };

  // Define columns for the study team table
  const studyTeamColumns = useMemo(() => [
    {
      key: "fullName" as keyof ExtendedUser,
      header: "User Name",
      sortable: true,
      render: (_value: unknown, row?: ExtendedUser) => {
        return `${row?.first_name || ''} ${row?.last_name || ''}`;
      },
    },
    {
      key: "speciality" as keyof ExtendedUser,
      header: "Job Title",
      sortable: true,
      render: (value: unknown): ReactNode => {
        return value ? String(value) : "N/A";
      },
    },
    {
      key: "phone_number" as keyof ExtendedUser,
      header: "Phone Number",
      sortable: true,
    },
    {
      key: "email" as keyof ExtendedUser,
      header: "Email",
      sortable: true,
    },
  ], []);

  // Define columns for adding users table (with checkboxes)
  const addUsersColumns = useMemo(() => [
    {
      key: "select" as keyof ExtendedUser,
      header: "Select",
      sortable: false,
      render: (_value: unknown, row?: ExtendedUser) => {
        if (!row) return null;
        return (
          <input
            type="checkbox"
            checked={selectedUserIds.includes(row.identifier)}
            onChange={(e) => handleUserSelection(row.identifier, e.target.checked)}
            className="user-checkbox"
          />
        );
      },
    },
    {
      key: "fullName" as keyof ExtendedUser,
      header: "User Name",
      sortable: true,
      render: (_value: unknown, row?: ExtendedUser) => {
        return `${row?.first_name || ''} ${row?.last_name || ''}`;
      },
    },
    {
      key: "speciality" as keyof ExtendedUser,
      header: "Job Title",
      sortable: true,
      render: (value: unknown): ReactNode => {
        return value ? String(value) : "N/A";
      },
    },
    {
      key: "email" as keyof ExtendedUser,
      header: "Email",
      sortable: true,
    },
  ], [selectedUserIds]);

  // Define actions for the study team table
  const studyTeamActions = useMemo(() => [
    {
      icon: <UserMinus size={18} />,
      tooltipText: "Remove from Study Team",
      onClick: (user: ExtendedUser) => {
        console.log('Action clicked for user:', user);
        handleExcludeUser(user.identifier);
      },
    },
  ], [handleExcludeUser]);

  // Prepare data for the tables
  const availableUsersData = useMemo<ExtendedUser[]>(() => {
    if (!availableUsers || !teamMembers) return [];

    // Filter out users who are already in the study team
    const teamMemberIds = teamMembers.map((member: User) => member.identifier);
    
    return availableUsers
      .filter((user: User) => !teamMemberIds.includes(user.identifier))
      .map((user: User): ExtendedUser => ({
        ...user,
        fullName: `${user.first_name || ''} ${user.last_name || ''}`,
      }));
  }, [availableUsers, teamMembers]);

  const studyTeamData = useMemo<ExtendedUser[]>(() => {
    if (!teamMembers) return [];
    
    return teamMembers.map((user: User): ExtendedUser => ({
      ...user,
      fullName: `${user.first_name || ''} ${user.last_name || ''}`,
    }));
  }, [teamMembers]);

  if (!currentStudy) {
    return (
      <div className="schedule-event-container">
        <div className="schedule-event-header">
          <h1>Study Team Management</h1>
        </div>
        <div className="no-study-selected">
          <Users size={48} className="placeholder-icon" />
          <h3>No Study Selected</h3>
          <p>Please select a study from the Study Setup tab to manage its team members.</p>
        </div>
      </div>
    );
  }

  return (
    <div className="users-container">
      <div className="users-header">
        <div className="users-title">
          <h1>
            <Users size={24} style={{ marginRight: "10px" }} />
            Study Team: {currentStudy.name}
          </h1>
        </div>
        <div className="users-subtitle">
          <h6>Manage team members for this study</h6>
        </div>
      </div>

      <div className="users-controls">
        <div className="users-search-container">
          <div className="users-search-box">
            <Search size={18} className="search-icon" />
            <DataTableSearch
              data={isAddingUsers ? availableUsersData : studyTeamData}
              onFilter={setFilteredData}
              placeholder={isAddingUsers ? "Search available users..." : "Search team members..."}
            />
          </div>
          {isAddingUsers ? (
            <div className="add-users-actions">
              <button
                type="button"
                className="cancel-btn"
                onClick={() => {
                  setIsAddingUsers(false);
                  setSelectedUserIds([]);
                }}
              >
                <X size={20} /> Cancel
              </button>
              <button
                type="button"
                className="users-add-button"
                onClick={handleAddSelectedUsers}
                disabled={selectedUserIds.length === 0}
              >
                <UserPlus size={20} /> Add Selected ({selectedUserIds.length})
              </button>
            </div>
          ) : (
            <button
              type="button"
              className="users-add-button"
              onClick={() => setIsAddingUsers(true)}
            >
              Add Users <Plus size={20} />
            </button>
          )}
        </div>
      </div>

      <div className="users-table-container">
        {isLoadingTeam || isLoadingUsers ? (
          <div className="loading-message">Loading users...</div>
        ) : isError ? (
          <div className="error-message">Error loading users</div>
        ) : (
          <DataTable
            data={isAddingUsers ? availableUsersData : studyTeamData}
            filteredData={filteredData}
            columns={isAddingUsers ? addUsersColumns : studyTeamColumns}
            actions={studyTeamActions}
            noDataMessage={
              isAddingUsers
                ? "No available users to add"
                : "No team members added yet"
            }
            defaultItemsPerPage={10}
          />
        )}
      </div>
    </div>
  );
};

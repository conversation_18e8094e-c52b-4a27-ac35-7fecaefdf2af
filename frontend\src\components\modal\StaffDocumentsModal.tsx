import React from "react";
import { X, Download, FileText, Calendar, AlertTriangle } from "lucide-react";
import { useStaffDocumentsQuery, useDownloadStaffDocumentMutation } from "@/hooks/staffDocument.query";
import { createFileNameWithExtension, downloadBlob, formatFileSize } from "@/utils/fileUtils";
import "./StaffDocumentsModal.css";

interface StaffDocumentsModalProps {
  isOpen: boolean;
  onClose: () => void;
  staffUuid: string;
  staffName: string;
}

const StaffDocumentsModal: React.FC<StaffDocumentsModalProps> = ({
  isOpen,
  onClose,
  staffUuid,
  staffName,
}) => {
  const { data: documents, isLoading, error } = useStaffDocumentsQuery(staffUuid);
  const downloadMutation = useDownloadStaffDocumentMutation();

  const handleDownload = (documentUuid: string, displayName: string, fileUrl: string) => {
    downloadMutation.mutate(documentUuid, {
      onSuccess: (blob) => {
        try {
          // Create filename with proper extension
          const filename = createFileNameWithExtension(displayName, fileUrl);
          
          // Download the file
          downloadBlob(blob, filename);
        } catch (error) {
          console.error("Error handling download:", error);
          // Fallback: download with UUID if filename creation fails
          downloadBlob(blob, `staff-document-${documentUuid}`);
        }
      },
      onError: (error) => {
        console.error("Download failed:", error);
        // You might want to show a user-friendly error message here
      },
    });
  };

  const formatDate = (dateString: string): string => {
    return new Date(dateString).toLocaleDateString("en-US", {
      year: "numeric",
      month: "short",
      day: "numeric",
    });
  };

  const getDocumentIcon = (documentType: string) => {
    switch (documentType.toLowerCase()) {
      case "cv":
      case "resume":
        return <FileText size={20} />;
      case "certification":
      case "license":
        return <FileText size={20} />;
      default:
        return <FileText size={20} />;
    }
  };

  if (!isOpen) return null;

  return (
    <div className="staff-documents-modal-overlay">
      <div className="staff-documents-modal">
        <div className="staff-documents-modal-header">
          <h2>Documents - {staffName}</h2>
          <button className="staff-documents-modal-close" onClick={onClose}>
            <X size={24} />
          </button>
        </div>

        <div className="staff-documents-modal-content">
          {isLoading && (
            <div className="staff-documents-loading">
              <p>Loading documents...</p>
            </div>
          )}

          {error && (
            <div className="staff-documents-error">
              <p>Error loading documents: {error.message}</p>
            </div>
          )}

          {!isLoading && !error && (!documents || documents.length === 0) && (
            <div className="staff-documents-empty">
              <FileText size={48} />
              <p>No documents found for this staff member.</p>
            </div>
          )}

          {!isLoading && !error && documents && documents.length > 0 && (
            <div className="staff-documents-list">
              {documents.map((document) => (
                <div key={document.uuid} className="staff-document-item">
                  <div className="staff-document-info">
                    <div className="staff-document-icon">
                      {getDocumentIcon(document.document_type)}
                    </div>
                    <div className="staff-document-details">
                      <h4>{document.display_name}</h4>
                      <p className="staff-document-type">{document.document_type}</p>
                      <div className="staff-document-meta">
                        <span className="staff-document-size">
                          {formatFileSize(document.file_size)}
                        </span>
                        <span className="staff-document-date">
                          Uploaded: {formatDate(document.uploaded_at)}
                        </span>
                        {document.expiry_date && (
                          <span className={`staff-document-expiry ${document.is_expired ? 'expired' : ''}`}>
                            <Calendar size={14} />
                            Expires: {formatDate(document.expiry_date)}
                            {document.is_expired && <AlertTriangle size={14} />}
                          </span>
                        )}
                      </div>
                    </div>
                  </div>
                  <div className="staff-document-actions">
                    <button
                      className="staff-document-download-btn"
                      onClick={() => handleDownload(document.uuid, document.display_name, document.file_url)}
                      disabled={downloadMutation.isPending}
                    >
                      <Download size={16} />
                      {downloadMutation.isPending ? "Downloading..." : "Download"}
                    </button>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default StaffDocumentsModal; 
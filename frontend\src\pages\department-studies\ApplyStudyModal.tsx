import { useState, useEffect } from "react";
import { X, CheckSquare, Square, FileText, AlertCircle, Inbox } from "lucide-react";
import { useTasksByStudyQuery } from "@/hooks/task.query";
import { StudyTask } from "@/services/api/task.service";
import "./ApplyStudyModal.css";

interface Study {
  uuid: string;
  iras: string;
  name: string;
  description: string;
  sponsor: {
    identifier: string;
    first_name: string;
    last_name: string;
  };
  created_at: string;
}

interface ApplyStudyModalProps {
  isOpen: boolean;
  study: Study | null;
  onClose: () => void;
  onConfirm: (selectedTaskUuids: string[]) => void;
  isLoading: boolean;
  isReapplication?: boolean;
}

export default function ApplyStudyModal({
  isOpen,
  study,
  onClose,
  onConfirm,
  isLoading,
  isReapplication = false
}: ApplyStudyModalProps) {
  const [selectedTaskUuids, setSelectedTaskUuids] = useState<string[]>([]);
  
  // Fetch tasks for the selected study
  const { data: tasksData, isLoading: isLoadingTasks, error: tasksError } = useTasksByStudyQuery(study?.uuid || '');
  
  const tasks = tasksData?.results || [];

  // Reset selected tasks when study changes
  useEffect(() => {
    setSelectedTaskUuids([]);
  }, [study?.uuid]);

  const handleTaskToggle = (taskUuid: string) => {
    setSelectedTaskUuids(prev => 
      prev.includes(taskUuid) 
        ? prev.filter(uuid => uuid !== taskUuid)
        : [...prev, taskUuid]
    );
  };

  const handleConfirm = () => {
    onConfirm(selectedTaskUuids);
  };

  const handleClose = () => {
    setSelectedTaskUuids([]);
    onClose();
  };

  if (!isOpen || !study) return null;

  return (
    <div className="dept-studies-modal-overlay">
      <div className="dept-studies-modal-container">
        <div className="dept-studies-modal-header">
          <h2>
            <div className="dept-studies-modal-header-icon">
              <FileText size={20} />
            </div>
            {isReapplication ? 'Reapply for Study' : 'Apply for Study'}
          </h2>
          <button 
            className="dept-studies-modal-close-button" 
            onClick={handleClose} 
            disabled={isLoading}
          >
            <X size={20} />
          </button>
        </div>
        
        <div className="dept-studies-modal-content">
          <div className="dept-studies-modal-description">
            {isReapplication 
              ? 'Please update your task selection for this study. Your previous application will be updated with the new selection.'
              : 'Please select the tasks you would like to perform for this study. Your application will be reviewed by the Principal Investigator.'
            }
          </div>
          
          <div className="dept-studies-modal-study-info">
            <div className="dept-studies-modal-info-item">
              <span className="dept-studies-modal-info-label">Study Name:</span>
              <span className="dept-studies-modal-info-value">{study.name}</span>
            </div>
            <div className="dept-studies-modal-info-item">
              <span className="dept-studies-modal-info-label">IRAS Number:</span>
              <span className="dept-studies-modal-info-value">{study.iras || "N/A"}</span>
            </div>
            <div className="dept-studies-modal-info-item">
              <span className="dept-studies-modal-info-label">Sponsor:</span>
              <span className="dept-studies-modal-info-value">
                {study.sponsor ? `${study.sponsor.first_name} ${study.sponsor.last_name}` : "N/A"}
              </span>
            </div>
            <div className="dept-studies-modal-info-item">
              <span className="dept-studies-modal-info-label">Description:</span>
              <span className="dept-studies-modal-info-value">
                {study.description && study.description.length > 200 
                  ? `${study.description.substring(0, 200)}...` 
                  : study.description || "No description available"}
              </span>
            </div>
          </div>

          <div className="dept-studies-modal-tasks-section">
            <div className="dept-studies-modal-tasks-header">
              <div className="dept-studies-modal-tasks-icon">
                <CheckSquare size={16} />
              </div>
              <h3>Available Tasks</h3>
            </div>
            
            {isLoadingTasks && (
              <div className="dept-studies-modal-loading">
                <div className="dept-studies-modal-spinner"></div>
                <span className="dept-studies-modal-loading-text">Loading tasks...</span>
              </div>
            )}
            
            {tasksError && (
              <div className="dept-studies-modal-error">
                <AlertCircle size={20} className="dept-studies-modal-error-icon" />
                Error loading tasks. Please try again.
              </div>
            )}
            
            {!isLoadingTasks && !tasksError && tasks.length === 0 && (
              <div className="dept-studies-modal-empty">
                <div className="dept-studies-modal-empty-icon">
                  <Inbox size={24} />
                </div>
                <p>No tasks available for this study.</p>
              </div>
            )}
            
            {!isLoadingTasks && !tasksError && tasks.length > 0 && (
              <div className="dept-studies-modal-tasks-list">
                {tasks.map((task: StudyTask) => (
                  <div 
                    key={task.uuid} 
                    className={`dept-studies-modal-task-item ${selectedTaskUuids.includes(task.uuid) ? 'selected' : ''}`}
                    onClick={() => handleTaskToggle(task.uuid)}
                  >
                    <div className="dept-studies-modal-task-checkbox">
                      {selectedTaskUuids.includes(task.uuid) ? (
                        <CheckSquare size={20} />
                      ) : (
                        <Square size={20} />
                      )}
                    </div>
                    <div className="dept-studies-modal-task-content">
                      <h4 className="dept-studies-modal-task-name">{task.name}</h4>
                      <p className="dept-studies-modal-task-description">{task.description}</p>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>
          
          <div className="dept-studies-modal-summary">
            {isReapplication 
              ? 'This will update your delegation log request and reset it to pending status for review by the Principal Investigator.'
              : 'This will create a delegation log request that will be reviewed by the Principal Investigator.'
            }
            {selectedTaskUuids.length > 0 && (
              <span className="dept-studies-modal-summary-highlight">
                <CheckSquare size={14} />
                {selectedTaskUuids.length} task{selectedTaskUuids.length !== 1 ? 's' : ''} selected
              </span>
            )}
          </div>
        </div>
        
        <div className="dept-studies-modal-footer">
          <div className="dept-studies-modal-footer-buttons">
            <button 
              className="dept-studies-modal-btn dept-studies-modal-btn-secondary"
              onClick={handleClose}
              disabled={isLoading}
            >
              Cancel
            </button>
            <button 
              className="dept-studies-modal-btn dept-studies-modal-btn-primary"
              onClick={handleConfirm}
              disabled={isLoading || selectedTaskUuids.length === 0}
            >
              {isLoading ? (
                <div className="dept-studies-modal-btn-loading">
                  <div className="dept-studies-modal-btn-spinner"></div>
                  {isReapplication ? 'Reapplying...' : 'Applying...'}
                </div>
              ) : (
                <>
                  <FileText size={16} />
                  {isReapplication ? "Reapply for Study" : "Apply for Study"}
                </>
              )}
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}

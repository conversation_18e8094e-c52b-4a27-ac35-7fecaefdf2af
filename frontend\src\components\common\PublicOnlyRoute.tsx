import { ReactNode } from 'react';
import { Navigate } from 'react-router-dom';
import { useCurrentUserQuery } from '@/hooks/user.query';
import Preloader from './Preloader';

interface PublicOnlyRouteProps {
  children: ReactNode;
}

/**
 * A route that is only accessible to non-authenticated users.
 * If the user is authenticated, they will be redirected to the home page.
 */
const PublicOnlyRoute = ({ children }: PublicOnlyRouteProps) => {
  const { data: currentUser, isLoading } = useCurrentUserQuery();
  
  // Skip loading state to avoid flashing header
  if (isLoading) {
    // If we're on a public page, show the preloader
    // If we're trying to access a protected page, redirect immediately
    return <Preloader />;
  }

  // If user is authenticated, redirect to home
  if (currentUser) {
    return <Navigate to="/home" replace />;
  }

  // Otherwise, render the children
  return <>{children}</>;
};

export default PublicOnlyRoute;

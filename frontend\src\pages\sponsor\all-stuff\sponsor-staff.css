/* Sponsor Staff Page Styles */
.sponsor-staff-container {
  padding: 30px;
  min-height: calc(100vh - 80px);
  background-color: #f5f7fa;
  font-family: var(--font-primary);
}

/* Page Title */
.sponsor-staff-container h1 {
  font-size: 28px;
  font-weight: 700;
  color: var(--color-dark-1);
  margin: 0 0 30px 0;
  display: flex;
  align-items: center;
  gap: 12px;
}

/* Filters Section */
.filters-section {
  background: #ffffff;
  border-radius: 16px;
  padding: 24px;
  margin-bottom: 30px;
  box-shadow: 0 8px 24px rgba(55, 183, 195, 0.08),
              0 2px 4px rgba(55, 183, 195, 0.04);
  border: 1px solid rgba(55, 183, 195, 0.1);
  transition: all 0.3s ease;
}

.filters-section:hover {
  box-shadow: 0 12px 32px rgba(55, 183, 195, 0.12),
              0 4px 8px rgba(55, 183, 195, 0.06);
  transform: translateY(-2px);
}

/* Search and Filter Container */
.search-filter-container {
  margin-bottom: 20px;
}

/* Policy Search Container (reusing existing styles) */
.policy-search-container {
  position: relative;
  width: 100%;
  max-width: 500px;
}

.policy-search-box {
  position: relative;
  display: flex;
  align-items: center;
  background: rgba(55, 183, 195, 0.03);
  border: 1px solid rgba(55, 183, 195, 0.15);
  border-radius: 12px;
  transition: all 0.3s ease;
  overflow: hidden;
}

.policy-search-box:hover {
  background: rgba(55, 183, 195, 0.05);
  border-color: rgba(55, 183, 195, 0.25);
  box-shadow: 0 4px 12px rgba(55, 183, 195, 0.08);
}

.policy-search-box:focus-within {
  background: #ffffff;
  border-color: var(--color-purple-1);
  box-shadow: 0 0 0 4px rgba(55, 183, 195, 0.1);
  transform: translateY(-1px);
}

.search-icon {
  position: absolute;
  left: 16px;
  color: var(--color-purple-1);
  z-index: 2;
  transition: all 0.3s ease;
}

.policy-search-input {
  width: 100%;
  padding: 16px 20px;
  padding-left: 50px;
  padding-right: 50px;
  border: none;
  background: transparent;
  font-size: 15px;
  color: var(--color-dark-1);
  outline: none;
  transition: all 0.3s ease;
}

.policy-search-input::placeholder {
  color: var(--color-light-1);
  font-style: italic;
}

.clear-search {
  position: absolute;
  right: 16px;
  background: none;
  border: none;
  color: var(--color-light-1);
  cursor: pointer;
  padding: 4px;
  border-radius: 50%;
  transition: all 0.3s ease;
  z-index: 2;
}

.clear-search:hover {
  background: rgba(55, 183, 195, 0.1);
  color: var(--color-purple-1);
  transform: scale(1.1);
}

/* Filter Dropdowns */
.filter-dropdowns {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 16px;
  margin-top: 20px;
}

.filter-group {
  display: flex;
  align-items: center;
  gap: 12px;
  background: rgba(55, 183, 195, 0.02);
  border: 1px solid rgba(55, 183, 195, 0.1);
  border-radius: 12px;
  padding: 4px 16px;
  transition: all 0.3s ease;
}

.filter-group:hover {
  background: rgba(55, 183, 195, 0.05);
  border-color: rgba(55, 183, 195, 0.2);
  box-shadow: 0 4px 8px rgba(55, 183, 195, 0.06);
}

.filter-group:focus-within {
  background: #ffffff;
  border-color: var(--color-purple-1);
  box-shadow: 0 0 0 3px rgba(55, 183, 195, 0.1);
}

.filter-group svg {
  color: var(--color-purple-1);
  flex-shrink: 0;
}

.filter-group select {
  flex: 1;
  padding: 12px 8px;
  border: none;
  background: transparent;
  font-size: 14px;
  font-weight: 500;
  color: var(--color-dark-1);
  outline: none;
  cursor: pointer;
  appearance: none;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='16' height='16' viewBox='0 0 24 24' fill='none' stroke='%2337B7C3' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpath d='M6 9l6 6 6-6'/%3E%3C/svg%3E");
  background-repeat: no-repeat;
  background-position: right 8px center;
  padding-right: 32px;
}

.filter-group select:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.filter-group select option {
  background: #ffffff;
  color: var(--color-dark-1);
  padding: 8px;
}

/* View Documents Button */
.view-documents-btn {
  display: inline-flex;
  align-items: center;
  gap: 6px;
  padding: 8px 16px;
  background: linear-gradient(135deg, var(--color-purple-1) 0%, #2d919a 100%);
  color: white;
  border: none;
  border-radius: 8px;
  font-size: 13px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 2px 8px rgba(55, 183, 195, 0.25);
  text-decoration: none;
}

.view-documents-btn:hover {
  background: linear-gradient(135deg, #2d919a 0%, #237a82 100%);
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(55, 183, 195, 0.4);
  color: white;
}

.view-documents-btn:active {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(55, 183, 195, 0.3);
}

.view-documents-btn:focus {
  outline: none;
  box-shadow: 0 0 0 3px rgba(55, 183, 195, 0.3), 0 6px 20px rgba(55, 183, 195, 0.4);
}

/* No Results Message */
.no-results {
  background: #ffffff;
  border-radius: 16px;
  padding: 60px 20px;
  text-align: center;
  color: var(--color-light-1);
  font-size: 16px;
  font-weight: 500;
  box-shadow: 0 4px 12px rgba(55, 183, 195, 0.05);
  border: 1px solid rgba(55, 183, 195, 0.1);
  margin-top: 20px;
}

/* Loading State */
.sponsor-staff-container .loading {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
  background: #ffffff;
  border-radius: 16px;
  box-shadow: 0 4px 12px rgba(55, 183, 195, 0.05);
  border: 1px solid rgba(55, 183, 195, 0.1);
  color: var(--color-light-1);
  font-size: 16px;
  font-weight: 500;
}

.sponsor-staff-container .loading::before {
  content: "";
  width: 24px;
  height: 24px;
  border: 3px solid rgba(55, 183, 195, 0.2);
  border-top: 3px solid var(--color-purple-1);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-right: 12px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Enhanced DataTable Integration */
.sponsor-staff-container .data-table-container {
  border-radius: 16px;
  box-shadow: 0 8px 24px rgba(55, 183, 195, 0.08),
              0 2px 4px rgba(55, 183, 195, 0.04);
  border: 1px solid rgba(55, 183, 195, 0.1);
  overflow: hidden;
  transition: all 0.3s ease;
}

.sponsor-staff-container .data-table-container:hover {
  box-shadow: 0 12px 32px rgba(55, 183, 195, 0.12),
              0 4px 8px rgba(55, 183, 195, 0.06);
  transform: translateY(-2px);
}

/* Custom table header styling */
.sponsor-staff-container .data-table thead th {
  background: linear-gradient(135deg, rgba(55, 183, 195, 0.08) 0%, rgba(55, 183, 195, 0.12) 100%);
  color: var(--color-dark-1);
  font-weight: 700;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  font-size: 12px;
  border-bottom: 2px solid rgba(55, 183, 195, 0.15);
}

.sponsor-staff-container .data-table tbody tr:hover {
  background: rgba(55, 183, 195, 0.03);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(55, 183, 195, 0.08);
}

/* Pagination styling */
.sponsor-staff-container .data-table-pagination {
  background: linear-gradient(135deg, rgba(55, 183, 195, 0.02) 0%, rgba(55, 183, 195, 0.05) 100%);
  border-top: 1px solid rgba(55, 183, 195, 0.15);
}

.sponsor-staff-container .pagination-page.active {
  background: var(--color-purple-1);
  border-color: var(--color-purple-1);
  color: white;
  box-shadow: 0 4px 12px rgba(55, 183, 195, 0.3);
}

.sponsor-staff-container .pagination-button:hover:not(:disabled),
.sponsor-staff-container .pagination-page:hover:not(.active) {
  background: rgba(55, 183, 195, 0.1);
  border-color: rgba(55, 183, 195, 0.2);
  color: var(--color-purple-1);
}

/* Status badges for admin status */
.admin-status-badge {
  display: inline-flex;
  align-items: center;
  gap: 6px;
  padding: 6px 12px;
  border-radius: 20px;
  font-size: 12px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.admin-status-badge.admin {
  background: rgba(34, 197, 94, 0.1);
  color: #16a34a;
  border: 1px solid rgba(34, 197, 94, 0.2);
}

.admin-status-badge.regular {
  background: rgba(107, 114, 128, 0.1);
  color: #6b7280;
  border: 1px solid rgba(107, 114, 128, 0.2);
}

/* Studies display styling */
.studies-list {
  max-width: 300px;
}

.study-item {
  padding: 8px 12px;
  background: rgba(55, 183, 195, 0.05);
  border: 1px solid rgba(55, 183, 195, 0.1);
  border-radius: 8px;
  margin-bottom: 6px;
  transition: all 0.3s ease;
}

.study-item:hover {
  background: rgba(55, 183, 195, 0.1);
  border-color: rgba(55, 183, 195, 0.2);
  transform: translateX(4px);
}

.study-item:last-child {
  margin-bottom: 0;
}

.study-name {
  font-weight: 600;
  color: var(--color-dark-1);
  font-size: 13px;
}

.study-iras {
  font-size: 12px;
  color: var(--color-light-1);
  font-style: italic;
}

/* Responsive Design */
@media (max-width: 1200px) {
  .filter-dropdowns {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (max-width: 991px) {
  .sponsor-staff-container {
    padding: 20px;
  }
  
  .sponsor-staff-container h1 {
    font-size: 24px;
    margin-bottom: 20px;
  }
  
  .filters-section {
    padding: 20px;
    margin-bottom: 20px;
  }
  
  .filter-dropdowns {
    grid-template-columns: 1fr;
    gap: 12px;
  }
  
  .policy-search-container {
    max-width: 100%;
  }
}

@media (max-width: 767px) {
  .sponsor-staff-container {
    padding: 15px;
  }
  
  .sponsor-staff-container h1 {
    font-size: 22px;
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }
  
  .filters-section {
    padding: 16px;
  }
  
  .search-filter-container {
    margin-bottom: 16px;
  }
  
  .policy-search-input {
    padding: 14px 18px;
    padding-left: 45px;
    padding-right: 45px;
    font-size: 14px;
  }
  
  .filter-group {
    padding: 8px 12px;
  }
  
  .filter-group select {
    padding: 10px 6px;
    font-size: 13px;
  }
  
  .view-documents-btn {
    padding: 6px 12px;
    font-size: 12px;
  }
  
  .studies-list {
    max-width: 100%;
  }
  
  .study-item {
    padding: 6px 10px;
  }
  
  .study-name {
    font-size: 12px;
  }
  
  .study-iras {
    font-size: 11px;
  }
}

@media (max-width: 480px) {
  .policy-search-box {
    border-radius: 10px;
  }
  
  .filter-group {
    border-radius: 10px;
  }
  
  .view-documents-btn {
    width: 100%;
    justify-content: center;
  }
  
  .admin-status-badge {
    font-size: 11px;
    padding: 4px 8px;
  }
}

/* Animation for smooth transitions */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.sponsor-staff-container > * {
  animation: fadeInUp 0.3s ease-out forwards;
}

.sponsor-staff-container > *:nth-child(2) {
  animation-delay: 0.1s;
}

.sponsor-staff-container > *:nth-child(3) {
  animation-delay: 0.2s;
}

/* Focus states for accessibility */
.policy-search-input:focus,
.filter-group select:focus,
.view-documents-btn:focus {
  outline: 2px solid var(--color-purple-1);
  outline-offset: 2px;
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .filters-section {
    border: 2px solid var(--color-dark-1);
  }
  
  .policy-search-box {
    border: 2px solid var(--color-dark-1);
  }
  
  .filter-group {
    border: 2px solid var(--color-dark-1);
  }
  
  .view-documents-btn {
    border: 2px solid var(--color-dark-1);
  }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}

/* Print styles */
@media print {
  .sponsor-staff-container {
    background: white;
    padding: 0;
  }
  
  .filters-section {
    display: none;
  }
  
  .view-documents-btn {
    display: none;
  }
  
  .data-table-pagination {
    display: none;
  }
}

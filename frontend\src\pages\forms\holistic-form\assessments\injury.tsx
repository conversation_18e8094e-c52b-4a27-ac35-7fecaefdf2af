import { useState } from "react";
import injuryImage from "./static/images/added/injury.jpg";
import romImage from "./static/images/added/rom.jpg";
import useHolisticFormStore from "@/store/holisticFormState";
import NurtifyRadio from "@/components/NurtifyRadio";
import NurtifyCheckBox from "@/components/NurtifyCheckBox";
import NurtifyText from "@/components/NurtifyText";
import NurtifyInput from "@/components/NurtifyInput";

const Injury = () => {
  const { assessment, setAssessment } = useHolisticFormStore();
  const [decreasedSensationDesc, setDecreasedSensationDesc] = useState<string[]>(
    assessment?.injury?.decreasedSensationDesc || []
  );

   
  const handleInjuryChange = (field: string, value: any) => {
    setAssessment({
      ...assessment,
      injury: {
        ...assessment.injury,
        [field]: value
      }
    });
  };

  const handleCheckboxChange = (value: string, checked: boolean) => {
    const updatedDesc = checked
      ? [...decreasedSensationDesc, value]
      : decreasedSensationDesc.filter(item => item !== value);
    
    setDecreasedSensationDesc(updatedDesc);
    handleInjuryChange("decreasedSensationDesc", updatedDesc);
  };

  return (
    <form
      id="division-39"
      className="container-fluid p-4"
      >
        <div className="row">
          <div className="col-12">
            <div className="d-flex align-items-center mb-4 mt-2 headinqQuestion">
              <img
                src={injuryImage}
                className="imageEtiquette me-3"
                alt="Image describing injury assessment section"
              />
              <h2 className="fs-2 mb-0 etiquetteHeadingForms">
                Injury Assessment
              </h2>
            </div>

            <div className="card p-3 mb-4">
              <NurtifyText 
                label="Any injury/trauma during presentation:" 
                className="headinqQuestion text-dark fw-bold mb-3" 
              />

              <div className="d-flex gap-4">
                <NurtifyRadio
                  name="patientHasTrauma"
                  value="true"
                  label="Yes"
                  checked={assessment?.injury?.patientHasTrauma === true}
                  onChange={() => handleInjuryChange("patientHasTrauma", true)}
                />

                <NurtifyRadio
                  name="patientHasTrauma"
                  value="false"
                  label="No"
                  checked={assessment?.injury?.patientHasTrauma === false}
                  onChange={() => handleInjuryChange("patientHasTrauma", false)}
                />
              </div>
            </div>
          </div>
        </div>

        {/* Location of injury */}
        {assessment?.injury?.patientHasTrauma === true && (
          <div className="row">
            <div className="col-12">
              <div className="card p-4 border border-primary-subtle">
                <h3 className="subheading text-dark fw-bold mb-3">
                  Injury Details
                </h3>

                <div className="mb-4">
                  <div className="row align-items-center mb-2">
                    <div className="col-md-3 col-sm-12">
                      <label className="form-label fw-semibold mb-0">Affected Body Area:</label>
                    </div>
                    <div className="col-md-9 col-sm-12">
                      <NurtifyInput
                        type="text"
                        value={assessment?.injury?.injuryLocation || ""}
                        onChange={(e) => handleInjuryChange("injuryLocation", e.target.value)}
                        name="injury_location"
                      />
                    </div>
                  </div>
                </div>

                <h4 className="subheading text-dark fw-semibold mb-3">
                  Any reduce range of movement?
                </h4>

                <div className="mb-4">
                  <div className="d-flex gap-4">
                    <NurtifyRadio
                      name="reducedMovement"
                      value="Yes"
                      label="Yes"
                      checked={assessment?.injury?.anyReducedMovement === "Yes"}
                      onChange={() => handleInjuryChange("anyReducedMovement", "Yes")}
                    />

                    <NurtifyRadio
                      name="reducedMovement"
                      value="No"
                      label="No"
                      checked={assessment?.injury?.anyReducedMovement === "No"}
                      onChange={() => handleInjuryChange("anyReducedMovement", "No")}
                    />
                  </div>
                </div>

                {assessment?.injury?.anyReducedMovement === "Yes" && (
                  <div className="mb-4">
                    <h4 className="subheading text-dark fw-semibold mb-3" title="Range of Movement">
                      Please specify ROM Type
                    </h4>

                    <div className="row">
                      <div className="col-lg-6 col-md-12">
                        <div className="row g-3">
                          <div className="col-md-6 col-sm-12">
                            <NurtifyCheckBox
                              value="Flexion"
                              label="Flexion"
                              checked={decreasedSensationDesc.includes("Flexion")}
                              onChange={(e) => handleCheckboxChange("Flexion", e.target.checked)}
                            />
                          </div>
                          <div className="col-md-6 col-sm-12">
                            <NurtifyCheckBox
                              value="Extension"
                              label="Extension"
                              checked={decreasedSensationDesc.includes("Extension")}
                              onChange={(e) => handleCheckboxChange("Extension", e.target.checked)}
                            />
                          </div>
                          <div className="col-md-6 col-sm-12">
                            <NurtifyCheckBox
                              value="Abduction"
                              label="Abduction"
                              checked={decreasedSensationDesc.includes("Abduction")}
                              onChange={(e) => handleCheckboxChange("Abduction", e.target.checked)}
                            />
                          </div>
                          <div className="col-md-6 col-sm-12">
                            <NurtifyCheckBox
                              value="Adduction"
                              label="Adduction"
                              checked={decreasedSensationDesc.includes("Adduction")}
                              onChange={(e) => handleCheckboxChange("Adduction", e.target.checked)}
                            />
                          </div>
                          <div className="col-md-6 col-sm-12">
                            <NurtifyCheckBox
                              value="Internal Rotation"
                              label="Internal Rotation"
                              checked={decreasedSensationDesc.includes("Internal Rotation")}
                              onChange={(e) => handleCheckboxChange("Internal Rotation", e.target.checked)}
                            />
                          </div>
                          <div className="col-md-6 col-sm-12">
                            <NurtifyCheckBox
                              value="External Rotation"
                              label="External Rotation"
                              checked={decreasedSensationDesc.includes("External Rotation")}
                              onChange={(e) => handleCheckboxChange("External Rotation", e.target.checked)}
                            />
                          </div>
                          <div className="col-md-6 col-sm-12">
                            <NurtifyCheckBox
                              value="Pronation"
                              label="Pronation"
                              checked={decreasedSensationDesc.includes("Pronation")}
                              onChange={(e) => handleCheckboxChange("Pronation", e.target.checked)}
                            />
                          </div>
                          <div className="col-md-6 col-sm-12">
                            <NurtifyCheckBox
                              value="Supination"
                              label="Supination"
                              checked={decreasedSensationDesc.includes("Supination")}
                              onChange={(e) => handleCheckboxChange("Supination", e.target.checked)}
                            />
                          </div>
                          <div className="col-md-6 col-sm-12">
                            <NurtifyCheckBox
                              value="Circumduction"
                              label="Circumduction"
                              checked={decreasedSensationDesc.includes("Circumduction")}
                              onChange={(e) => handleCheckboxChange("Circumduction", e.target.checked)}
                            />
                          </div>
                          <div className="col-md-6 col-sm-12">
                            <NurtifyCheckBox
                              value="Inversion"
                              label="Inversion"
                              checked={decreasedSensationDesc.includes("Inversion")}
                              onChange={(e) => handleCheckboxChange("Inversion", e.target.checked)}
                            />
                          </div>
                          <div className="col-md-6 col-sm-12">
                            <NurtifyCheckBox
                              value="Eversion"
                              label="Eversion"
                              checked={decreasedSensationDesc.includes("Eversion")}
                              onChange={(e) => handleCheckboxChange("Eversion", e.target.checked)}
                            />
                          </div>
                          <div className="col-md-6 col-sm-12">
                            <NurtifyCheckBox
                              value="Dorsiflexion"
                              label="Dorsiflexion"
                              checked={decreasedSensationDesc.includes("Dorsiflexion")}
                              onChange={(e) => handleCheckboxChange("Dorsiflexion", e.target.checked)}
                            />
                          </div>
                          <div className="col-md-6 col-sm-12">
                            <NurtifyCheckBox
                              value="Plantarflexion"
                              label="Plantar-flexion"
                              checked={decreasedSensationDesc.includes("Plantarflexion")}
                              onChange={(e) => handleCheckboxChange("Plantarflexion", e.target.checked)}
                            />
                          </div>
                          <div className="col-md-6 col-sm-12">
                            <NurtifyCheckBox
                              value="Lateral Flexion"
                              label="Lateral Flexion"
                              checked={decreasedSensationDesc.includes("Lateral Flexion")}
                              onChange={(e) => handleCheckboxChange("Lateral Flexion", e.target.checked)}
                            />
                          </div>
                        </div>
                      </div>

                      <div className="col-lg-6 col-md-12 d-flex justify-content-center align-items-center">
                        <img
                          className="img-fluid"
                          src={romImage}
                          alt="Types of anatomical range of movement"
                        />
                      </div>
                    </div>
                  </div>
                )}

                <div className="mb-4">
                  <h4 className="subheading text-dark fw-semibold mb-3">
                    Any decrease of sensation
                  </h4>

                  <div className="d-flex gap-4 mb-3">
                    <NurtifyRadio
                      name="reducedSensation"
                      value="Yes"
                      label="Yes"
                      checked={assessment?.injury?.anyDecreasedSensation === "Yes"}
                      onChange={() => handleInjuryChange("anyDecreasedSensation", "Yes")}
                    />

                    <NurtifyRadio
                      name="reducedSensation"
                      value="No"
                      label="No"
                      checked={assessment?.injury?.anyDecreasedSensation === "No"}
                      onChange={() => handleInjuryChange("anyDecreasedSensation", "No")}
                    />
                  </div>
                </div>

                <div className="mb-4">
                  <h4 
                    className="subheading text-dark fw-semibold mb-3"
                    title="(loss of sensation or tingling)"
                  >
                    Is there any Paresthesia
                  </h4>
                  
                  <div className="d-flex gap-4 mb-3">
                    <NurtifyRadio
                      name="paresthesia"
                      value="Yes"
                      label="Yes"
                      checked={assessment?.injury?.paresthesia === "Yes"}
                      onChange={() => handleInjuryChange("paresthesia", "Yes")}
                    />

                    <NurtifyRadio
                      name="paresthesia"
                      value="No"
                      label="No"
                      checked={assessment?.injury?.paresthesia === "No"}
                      onChange={() => handleInjuryChange("paresthesia", "No")}
                    />
                  </div>
                </div>

                <div className="mb-4">
                  <h4 className="subheading text-dark fw-semibold mb-3">
                    Any injury on the lower limb?
                  </h4>
                  
                  <div className="d-flex gap-4 mb-3">
                    <NurtifyRadio
                      name="injuryLowerLimb"
                      value="Yes"
                      label="Yes"
                      checked={assessment?.injury?.injuryLowerLimb === "Yes"}
                      onChange={() => handleInjuryChange("injuryLowerLimb", "Yes")}
                    />

                    <NurtifyRadio
                      name="injuryLowerLimb"
                      value="No"
                      label="No"
                      checked={assessment?.injury?.injuryLowerLimb === "No"}
                      onChange={() => handleInjuryChange("injuryLowerLimb", "No")}
                    />
                  </div>
                </div>

                {assessment?.injury?.injuryLowerLimb === "Yes" && (
                  <div className="border-top pt-4">
                    <h4 className="subheading text-dark fw-semibold mb-3">
                      Lower Limb Assessment
                    </h4>
                    
                    <div className="mb-4">
                      <h5 className="text-dark fw-medium mb-2">
                        Is patient able to bear weight?
                      </h5>

                      <div className="d-flex gap-4 mb-3">
                        <NurtifyRadio
                          name="bearWeightOnLimb"
                          value="Yes"
                          label="Yes"
                          checked={assessment?.injury?.bearWeightOnLimb === "Yes"}
                          onChange={() => handleInjuryChange("bearWeightOnLimb", "Yes")}
                        />

                        <NurtifyRadio
                          name="bearWeightOnLimb"
                          value="No"
                          label="No"
                          checked={assessment?.injury?.bearWeightOnLimb === "No"}
                          onChange={() => handleInjuryChange("bearWeightOnLimb", "No")}
                        />
                      </div>
                    </div>

                    <div className="mb-4">
                      <h5 className="text-dark fw-medium mb-2">
                        Is there any pedal pulse?
                      </h5>

                      <div className="d-flex gap-4 mb-3">
                        <NurtifyRadio
                          name="pedalPulse"
                          value="Yes"
                          label="Yes"
                          checked={assessment?.injury?.pedalPulse === "Yes"}
                          onChange={() => handleInjuryChange("pedalPulse", "Yes")}
                        />

                        <NurtifyRadio
                          name="pedalPulse"
                          value="No"
                          label="No"
                          checked={assessment?.injury?.pedalPulse === "No"}
                          onChange={() => handleInjuryChange("pedalPulse", "No")}
                        />
                      </div>
                    </div>

                    <div className="mb-4">
                      <h5 className="text-dark fw-medium mb-2">
                        Is the patient able to move toes?
                      </h5>

                      <div className="d-flex gap-4 mb-3">
                        <NurtifyRadio
                          name="ableToMoveToes"
                          value="Yes"
                          label="Yes"
                          checked={assessment?.injury?.ableToMoveToes === "Yes"}
                          onChange={() => handleInjuryChange("ableToMoveToes", "Yes")}
                        />

                        <NurtifyRadio
                          name="ableToMoveToes"
                          value="No"
                          label="No"
                          checked={assessment?.injury?.ableToMoveToes === "No"}
                          onChange={() => handleInjuryChange("ableToMoveToes", "No")}
                        />
                      </div>
                    </div>
                  </div>
                )}
              </div>
            </div>
          </div>
        )}
        <div className="d-flex justify-content-end">
          <button type="button" className="btn btn-primary" onClick={() => console.log(assessment)}>Log</button>
        </div>
      </form>
  );
};

export default Injury;

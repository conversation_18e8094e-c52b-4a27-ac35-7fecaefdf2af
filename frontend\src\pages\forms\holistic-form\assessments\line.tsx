import { useState } from "react";
import LineImage from "./static/images/added/line.jpg";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { faAdd } from "@fortawesome/free-solid-svg-icons";
import useHolisticFormStore from "@/store/holisticFormState";

const Line = () => {
  const { assessment, setAssessment } = useHolisticFormStore();
  const [, setOptionsModalShowing] = useState(false);
  const [addLinesFormShowing, setAddLinesFormShowing] = useState(false);

  const handleAddLines = (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    const form = e.currentTarget;
    const formData = new FormData(form);
    const newLine = {
      d1: formData.get('line_name') as string,
      d2: formData.get('line_site') as string,
      d3: formData.get('line_date') as string,
      d4: formData.get('line_size') as string,
      d5: formData.get('line_patency') as string,
    };

    // Update the lines in the assessment using setAssessment
    setAssessment({
      ...assessment,
      lines: [...assessment.lines, newLine]
    });

    setOptionsModalShowing(false);
    setAddLinesFormShowing(false);
  };

  return (
    <div className="block align-items-*-start flex-column flex-md-row p-4 gap-5">
      <div id="division-32" className="">
        {/* Line Section Title  */}
        <div className="inlineBlock mb-4 headinqQuestion">
          <img
            src={LineImage}
            className="imageEtiquette"
            alt="patient face image round"
          />
          <span className="mb-2 pb-2 fs-2 text-start etiquetteHeadingForms">
            Lines/Catheter/Tubes
          </span>
        </div>

        <div className="d-flex lines flex-column gap-2" >
          {assessment?.lines?.map((line, index) => (
            <div className="pain" key={index} style={{backgroundColor: "#112D4E", color: "white", padding: "10px", borderRadius: "10px", width: "15%"}}>
              <span>{line.d1}</span>
            </div>
          ))}
        </div>
        <div className="d-flex flex-row align-items-center justify-content-start mt-4">
          <button
            onClick={() => {
              setOptionsModalShowing(true);
              setAddLinesFormShowing(true);
            }}
            className="btn-nurtify"
          >
            Add Line / Tube <FontAwesomeIcon icon={faAdd} />
          </button>
        </div>
      </div>

      {addLinesFormShowing && (
        <div className="options-modal">
          <div className="center">
            <form onSubmit={handleAddLines} className="d-flex flex-column gap-1">
              <div className="input">
                <span className="subheading">
                  Lines / Cathether (e.g: IV line/Uritheral Catheter)
                </span>
                <input
                  type="text"
                  name="line_name"
                  className="form-control mb-3"
                />
              </div>
              <div className="input">
                <span className="subheading">
                  Size (in numbers with Unit)?
                </span>
                <input
                  type="text"
                  name="line_size"
                  className="form-control mb-3"
                />
              </div>
              <div className="input">
                <span className="subheading">Insertion Site</span>
                <input
                  type="text"
                  name="line_site"
                  className="form-control mb-3"
                />
              </div>
              <div className="input">
                <span className="subheading">Patency (Yes/No)</span>
                <select
                  name="line_patency"
                  id=""
                  className="form-control mb-3"
                >
                  <option value=""></option>
                  <option value="Yes">Yes</option>
                  <option value="No">No</option>
                </select>
              </div>
              <div className="input">
                <span className="subheading">Insertion Date</span>
                <input
                  type="date"
                  name="line_date"
                  className="form-control mb-3"
                />
              </div>
              <div className="d-flex btns gap-3 mt-3 flex-row">
                <button
                  onClick={(e) => {
                    e.preventDefault();
                    setOptionsModalShowing(false);
                    setAddLinesFormShowing(false);
                  }}
                  className="btn btn-secondary"
                >
                  CANCEL
                </button>
                <button type="submit" className="my-primary-btn" onClick={()=> handleAddLines}>
                  save
                </button>
              </div>
            </form>
          </div>
         
        </div>
      )}
    </div>
  );
};

export default Line;

import React, { useState, useMemo } from "react";
import { motion } from "framer-motion";
import { Plus, History, Edit, Eye, CheckCircle, Clock } from "lucide-react";
import DataTable, { Column } from "@/components/common/DataTable";
import { type Symptom, type CreateSymptomData } from "@/services/api/symptom.service";
import AddSymptomModal from "@/components/patient-clinical/AddSymptomModal";
import SymptomLogsModal from "@/components/patient-clinical/SymptomLogsModal";
import "@/components/patient-clinical/AddSymptomModal.css";
import "./patientdiary.css";
import { useSymptomsQuery, useUpdateSymptomMutation, useMarkSymptomAsReviewedMutation } from "@/hooks/symptom.query";
import useSelectedPatientStore from "@/store/SelectedPatientState";
import Preloader from "@/components/common/Preloader";
import NurtifyFilter, { NurtifyFilterItem } from "@/components/NurtifyFilter";
import { useCurrentUserQuery } from "@/hooks/user.query";

interface EditSymptomModalProps {
  isOpen: boolean;
  onClose: () => void;
  symptom: Symptom | null;
  onSave: (updatedSymptom: Partial<CreateSymptomData>) => Promise<void>;
}

interface ViewSymptomModalProps {
  isOpen: boolean;
  onClose: () => void;
  symptom: Symptom | null;
}

const SEVERITY_MAP = {
  mild: "Mild",
  moderate: "Moderate",
  severe: "Severe",
  critical: "Critical",
  life_threatening: "Life Threatening",
  death: "Death (Fatal)",
};

const STATUS_MAP = {
  resolved: "Resolved",
  recovered: "Recovered with sequelae",
  ongoing_treatment: "Ongoing / Continuing treatment",
  condition_worsening: "Condition worsening",
  unknown: "Unknown"
};

const RELATEDNESS_MAP = {
  definitely_related: "Definitely related",
  probably_related: "Probably related",
  possibly_related: "Possibly related",
  unlikely_related: "Unlikely",
  unrelated: "Unrelated",
};

const CATEGORY_MAP = {
  nan: "N/A",
  sae: "SAEs",
  ae: "AEs",
  ar: "ARs",
  ssar: "SSARs",
  susar: "SUSARs",
};

const ViewSymptomModal: React.FC<ViewSymptomModalProps> = ({
  isOpen,
  onClose,
  symptom,
}) => {
  if (!isOpen || !symptom) return null;

  return (
    <div className="modal-overlay">
      <div className="modal-content" style={{ maxWidth: '800px', width: '90%' }}>
        <div className="modal-header" style={{
          background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
          color: 'white',
          borderBottom: '1px solid #e5e7eb',
          padding: '24px 32px'
        }}>
          <h3 style={{ margin: 0, fontSize: '1.5rem', fontWeight: 600 }}>
            <span style={{ marginRight: '12px' }}>📋</span>
            View Symptom Details
          </h3>
          <button
            className="close-button"
            onClick={onClose}
            style={{
              background: 'rgba(255, 255, 255, 0.2)',
              border: 'none',
              borderRadius: '8px',
              padding: '8px',
              color: 'white',
              cursor: 'pointer',
              fontSize: '1.2rem',
              width: '40px',
              height: '40px',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              transition: 'all 0.2s ease'
            }}
            onMouseOver={(e) => {
              e.currentTarget.style.background = 'rgba(255, 255, 255, 0.3)';
            }}
            onMouseOut={(e) => {
              e.currentTarget.style.background = 'rgba(255, 255, 255, 0.2)';
            }}
          >
            ×
          </button>
        </div>

        <div style={{ padding: '32px', maxHeight: '70vh', overflowY: 'auto' }}>
          <div className="row g-4">
            {/* Basic Information Section */}
            <div className="col-12">
              <div style={{
                background: 'linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%)',
                padding: '20px',
                borderRadius: '12px',
                marginBottom: '24px',
                border: '1px solid #e2e8f0'
              }}>
                <h4 style={{
                  margin: '0 0 16px 0',
                  color: '#1e293b',
                  fontSize: '1.1rem',
                  fontWeight: 600,
                  display: 'flex',
                  alignItems: 'center',
                  gap: '8px'
                }}>
                  <span>🔍</span>
                  Basic Information
                </h4>
                <div className="row g-3">
                  <div className="col-md-6">
                    <div style={{ marginBottom: '16px' }}>
                      <label style={{
                        display: 'block',
                        marginBottom: '6px',
                        color: '#64748b',
                        fontSize: '0.875rem',
                        fontWeight: 500
                      }}>
                        Symptom Name
                      </label>
                      <div style={{
                        background: 'white',
                        padding: '12px 16px',
                        borderRadius: '8px',
                        border: '1px solid #e2e8f0',
                        color: '#1e293b',
                        fontWeight: 500,
                        fontSize: '1rem'
                      }}>
                        {symptom.name || 'N/A'}
                      </div>
                    </div>
                  </div>
                  <div className="col-md-6">
                    <div style={{ marginBottom: '16px' }}>
                      <label style={{
                        display: 'block',
                        marginBottom: '6px',
                        color: '#64748b',
                        fontSize: '0.875rem',
                        fontWeight: 500
                      }}>
                        Severity
                      </label>
                      <div style={{
                        background: 'white',
                        padding: '12px 16px',
                        borderRadius: '8px',
                        border: '1px solid #e2e8f0',
                        color: '#1e293b',
                        fontWeight: 500,
                        fontSize: '1rem'
                      }}>
                        {SEVERITY_MAP[symptom.severity as keyof typeof SEVERITY_MAP] || 'N/A'}
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* Description Section */}
            <div className="col-12">
              <div style={{ marginBottom: '24px' }}>
                <label style={{
                  display: 'block',
                  marginBottom: '8px',
                  color: '#64748b',
                  fontSize: '0.875rem',
                  fontWeight: 500
                }}>
                  Description
                </label>
                <div style={{
                  background: '#f8fafc',
                  padding: '16px',
                  borderRadius: '8px',
                  border: '1px solid #e2e8f0',
                  color: '#1e293b',
                  minHeight: '100px',
                  whiteSpace: 'pre-wrap',
                  lineHeight: '1.6'
                }}>
                  {symptom.description || 'No description provided'}
                </div>
              </div>
            </div>

            {/* Timing Information Section */}
            <div className="col-12">
              <div style={{
                background: 'linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%)',
                padding: '20px',
                borderRadius: '12px',
                marginBottom: '24px',
                border: '1px solid #bae6fd'
              }}>
                <h4 style={{
                  margin: '0 0 16px 0',
                  color: '#0c4a6e',
                  fontSize: '1.1rem',
                  fontWeight: 600,
                  display: 'flex',
                  alignItems: 'center',
                  gap: '8px'
                }}>
                  <span>⏰</span>
                  Timing Information
                </h4>
                <div className="row g-3">
                  <div className="col-md-6">
                    <div style={{ marginBottom: '16px' }}>
                      <label style={{
                        display: 'block',
                        marginBottom: '6px',
                        color: '#0c4a6e',
                        fontSize: '0.875rem',
                        fontWeight: 500
                      }}>
                        Start Date
                      </label>
                      <div style={{
                        background: 'white',
                        padding: '12px 16px',
                        borderRadius: '8px',
                        border: '1px solid #bae6fd',
                        color: '#0c4a6e',
                        fontWeight: 500,
                        fontSize: '1rem'
                      }}>
                        {symptom.start_date || 'N/A'}
                      </div>
                    </div>
                  </div>
                  <div className="col-md-6">
                    <div style={{ marginBottom: '16px' }}>
                      <label style={{
                        display: 'block',
                        marginBottom: '6px',
                        color: '#0c4a6e',
                        fontSize: '0.875rem',
                        fontWeight: 500
                      }}>
                        Start Time
                      </label>
                      <div style={{
                        background: 'white',
                        padding: '12px 16px',
                        borderRadius: '8px',
                        border: '1px solid #bae6fd',
                        color: '#0c4a6e',
                        fontWeight: 500,
                        fontSize: '1rem'
                      }}>
                        {symptom.start_time || 'N/A'}
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* Status and Classification Section */}
            <div className="col-12">
              <div style={{
                background: 'linear-gradient(135deg, #fef3c7 0%, #fde68a 100%)',
                padding: '20px',
                borderRadius: '12px',
                marginBottom: '24px',
                border: '1px solid #fcd34d'
              }}>
                <h4 style={{
                  margin: '0 0 16px 0',
                  color: '#92400e',
                  fontSize: '1.1rem',
                  fontWeight: 600,
                  display: 'flex',
                  alignItems: 'center',
                  gap: '8px'
                }}>
                  <span>📊</span>
                  Status & Classification
                </h4>
                <div className="row g-3">
                  <div className="col-md-6">
                    <div style={{ marginBottom: '16px' }}>
                      <label style={{
                        display: 'block',
                        marginBottom: '6px',
                        color: '#92400e',
                        fontSize: '0.875rem',
                        fontWeight: 500
                      }}>
                        Status
                      </label>
                      <div style={{
                        background: 'white',
                        padding: '12px 16px',
                        borderRadius: '8px',
                        border: '1px solid #fcd34d',
                        color: '#92400e',
                        fontWeight: 500,
                        fontSize: '1rem'
                      }}>
                        {STATUS_MAP[symptom.status as keyof typeof STATUS_MAP] || 'N/A'}
                      </div>
                    </div>
                  </div>
                  <div className="col-md-6">
                    <div style={{ marginBottom: '16px' }}>
                      <label style={{
                        display: 'block',
                        marginBottom: '6px',
                        color: '#92400e',
                        fontSize: '0.875rem',
                        fontWeight: 500
                      }}>
                        Relatedness
                      </label>
                      <div style={{
                        background: 'white',
                        padding: '12px 16px',
                        borderRadius: '8px',
                        border: '1px solid #fcd34d',
                        color: '#92400e',
                        fontWeight: 500,
                        fontSize: '1rem'
                      }}>
                        {RELATEDNESS_MAP[symptom.relatedness as keyof typeof RELATEDNESS_MAP] || 'N/A'}
                      </div>
                    </div>
                  </div>
                  <div className="col-md-6">
                    <div style={{ marginBottom: '16px' }}>
                      <label style={{
                        display: 'block',
                        marginBottom: '6px',
                        color: '#92400e',
                        fontSize: '0.875rem',
                        fontWeight: 500
                      }}>
                        Category
                      </label>
                      <div style={{
                        background: 'white',
                        padding: '12px 16px',
                        borderRadius: '8px',
                        border: '1px solid #fcd34d',
                        color: '#92400e',
                        fontWeight: 500,
                        fontSize: '1rem'
                      }}>
                        {CATEGORY_MAP[symptom.category as keyof typeof CATEGORY_MAP] || 'N/A'}
                      </div>
                    </div>
                  </div>
                  <div className="col-md-6">
                    <div style={{ marginBottom: '16px' }}>
                      <label style={{
                        display: 'block',
                        marginBottom: '6px',
                        color: '#92400e',
                        fontSize: '0.875rem',
                        fontWeight: 500
                      }}>
                        Review Status
                      </label>
                      <div style={{
                        background: 'white',
                        padding: '12px 16px',
                        borderRadius: '8px',
                        border: '1px solid #fcd34d',
                        color: '#92400e',
                        fontWeight: 500,
                        fontSize: '1rem'
                      }}>
                        {symptom.review_status === "reviewed" ? (
                          <div style={{
                            display: 'inline-flex',
                            alignItems: 'center',
                            gap: '8px',
                            padding: '6px 12px',
                            backgroundColor: '#f0fdf4',
                            border: '1px solid #22c55e',
                            borderRadius: '16px',
                            color: '#166534',
                            fontSize: '0.875rem',
                            fontWeight: '600',
                            textTransform: 'uppercase'
                          }}>
                            <CheckCircle size={16} />
                            Reviewed
                          </div>
                        ) : (
                          <div style={{
                            display: 'inline-flex',
                            alignItems: 'center',
                            gap: '8px',
                            padding: '6px 12px',
                            backgroundColor: '#fff7ed',
                            border: '1px solid #f97316',
                            borderRadius: '16px',
                            color: '#ea580c',
                            fontSize: '0.875rem',
                            fontWeight: '600',
                            textTransform: 'uppercase'
                          }}>
                            <Clock size={16} />
                            Not Reviewed
                          </div>
                        )}
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* Additional Information Section */}
            <div className="col-12">
              <div style={{
                background: 'linear-gradient(135deg, #f1f5f9 0%, #e2e8f0 100%)',
                padding: '20px',
                borderRadius: '12px',
                marginBottom: '24px',
                border: '1px solid #cbd5e1'
              }}>
                <h4 style={{
                  margin: '0 0 16px 0',
                  color: '#334155',
                  fontSize: '1.1rem',
                  fontWeight: 600,
                  display: 'flex',
                  alignItems: 'center',
                  gap: '8px'
                }}>
                  <span>ℹ️</span>
                  Additional Information
                </h4>
                <div className="row g-3">
                  <div className="col-md-6">
                    <div style={{ marginBottom: '16px' }}>
                      <label style={{
                        display: 'block',
                        marginBottom: '6px',
                        color: '#334155',
                        fontSize: '0.875rem',
                        fontWeight: 500
                      }}>
                        Hospitalization Required
                      </label>
                      <div style={{
                        background: 'white',
                        padding: '12px 16px',
                        borderRadius: '8px',
                        border: '1px solid #cbd5e1',
                        color: '#334155',
                        fontWeight: 500,
                        fontSize: '1rem'
                      }}>
                        {symptom.hospitalization_required ? "Yes" : "No"}
                      </div>
                    </div>
                  </div>
                  {symptom.resolved_date && (
                    <div className="col-md-6">
                      <div style={{ marginBottom: '16px' }}>
                        <label style={{
                          display: 'block',
                          marginBottom: '6px',
                          color: '#334155',
                          fontSize: '0.875rem',
                          fontWeight: 500
                        }}>
                          Resolved Date
                        </label>
                        <div style={{
                          background: 'white',
                          padding: '12px 16px',
                          borderRadius: '8px',
                          border: '1px solid #cbd5e1',
                          color: '#334155',
                          fontWeight: 500,
                          fontSize: '1rem'
                        }}>
                          {symptom.resolved_date}
                        </div>
                      </div>
                    </div>
                  )}
                </div>
              </div>
            </div>

            {/* Patient Comment Section */}
            {symptom.patient_comment && (
              <div className="col-12">
                <div style={{ marginBottom: '24px' }}>
                  <label style={{
                    display: 'block',
                    marginBottom: '8px',
                    color: '#64748b',
                    fontSize: '0.875rem',
                    fontWeight: 500
                  }}>
                    Reviewed Comment
                  </label>
                  <div style={{
                    background: '#f8fafc',
                    padding: '16px',
                    borderRadius: '8px',
                    border: '1px solid #e2e8f0',
                    color: '#1e293b',
                    minHeight: '80px',
                    whiteSpace: 'pre-wrap',
                    lineHeight: '1.6'
                  }}>
                    {symptom.patient_comment}
                  </div>
                </div>
              </div>
            )}
          </div>
        </div>

        <div className="modal-footer" style={{
          padding: '24px 32px',
          borderTop: '1px solid #e5e7eb',
          background: '#f9fafb',
          display: 'flex',
          justifyContent: 'flex-end'
        }}>
          <button
            type="button"
            className="btn btn-secondary"
            onClick={onClose}
            style={{
              background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
              color: 'white',
              border: 'none',
              padding: '12px 24px',
              borderRadius: '8px',
              fontSize: '1rem',
              fontWeight: 500,
              cursor: 'pointer',
              transition: 'all 0.2s ease'
            }}
            onMouseOver={(e) => {
              e.currentTarget.style.transform = 'translateY(-2px)';
              e.currentTarget.style.boxShadow = '0 4px 12px rgba(102, 126, 234, 0.4)';
            }}
            onMouseOut={(e) => {
              e.currentTarget.style.transform = 'translateY(0)';
              e.currentTarget.style.boxShadow = 'none';
            }}
          >
            Close
          </button>
        </div>
      </div>
    </div>
  );
};

const EditSymptomModal: React.FC<EditSymptomModalProps> = ({
  isOpen,
  onClose,
  symptom,
  onSave,
}) => {
  const { data: currentUser } = useCurrentUserQuery();
  const isPatient = currentUser?.user_type === "patient";
  const markAsReviewedMutation = useMarkSymptomAsReviewedMutation();

  const [editedSymptom, setEditedSymptom] = useState<Partial<CreateSymptomData>>({});
  const [originalComment, setOriginalComment] = useState<string>("");

  React.useEffect(() => {
    if (symptom) {
      setEditedSymptom({
        name: symptom.name,
        description: symptom.description,
        start_date: symptom.start_date,
        start_time: symptom.start_time,
        severity: symptom.severity,
        hospitalization_required: symptom.hospitalization_required,
        status: symptom.status,
        relatedness: symptom.relatedness,
        category: symptom.category,
        patient_comment: symptom.patient_comment,
        resolved_date: symptom.resolved_date || undefined
      });
      setOriginalComment(symptom.patient_comment || "");
    }
  }, [symptom]);

  if (!isOpen || !symptom) return null;

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    const dataToSubmit = {
      ...editedSymptom,
      // Set default values for patient users
      ...(isPatient && { category: "nan", patient_comment: "" })
    };

    await onSave(dataToSubmit);

    // Check if a comment was added and mark as reviewed
    if (!isPatient && editedSymptom.patient_comment && editedSymptom.patient_comment.trim() !== "" &&
        editedSymptom.patient_comment !== originalComment && symptom.review_status === "not_reviewed") {
      try {
        await markAsReviewedMutation.mutateAsync(symptom.uuid);
      } catch (error) {
        console.error("Error marking symptom as reviewed:", error);
      }
    }
  };

  return (
    <div className="modal-overlay">
      <div className="modal-content">
        <div className="modal-header">
          <h3>Edit And Review Symptom</h3>
          <button className="close-button" onClick={onClose}>×</button>
        </div>
        <form onSubmit={handleSubmit}>
          <div className="row g-3">
            {/* Review Status Display */}
            <div className="col-12">
              <div className="form-group">
                <label>Review Status</label>
                <div style={{ marginTop: '8px' }}>
                  {symptom.review_status === "reviewed" ? (
                    <div style={{
                      display: 'inline-flex',
                      alignItems: 'center',
                      gap: '8px',
                      padding: '8px 16px',
                      backgroundColor: '#f0fdf4',
                      border: '1px solid #22c55e',
                      borderRadius: '20px',
                      color: '#166534',
                      fontSize: '0.875rem',
                      fontWeight: '600',
                      textTransform: 'uppercase'
                    }}>
                      <CheckCircle size={16} />
                      Reviewed
                    </div>
                  ) : (
                    <div style={{
                      display: 'inline-flex',
                      alignItems: 'center',
                      gap: '8px',
                      padding: '8px 16px',
                      backgroundColor: '#fff7ed',
                      border: '1px solid #f97316',
                      borderRadius: '20px',
                      color: '#ea580c',
                      fontSize: '0.875rem',
                      fontWeight: '600',
                      textTransform: 'uppercase'
                    }}>
                      <Clock size={16} />
                      Not Reviewed
                    </div>
                  )}
                </div>
              </div>
            </div>
            <div className="col-md-6">
              <div className="form-group">
                <label htmlFor="name">Symptom Name</label>
                <input
                  type="text"
                  className="form-control"
                  id="name"
                  value={editedSymptom.name || ""}
                  onChange={(e) => setEditedSymptom(prev => ({ ...prev, name: e.target.value }))}
                  required
                />
              </div>
            </div>
            <div className="col-md-6">
              <div className="form-group">
                <label htmlFor="severity">Severity</label>
                <select
                  className="form-control"
                  id="severity"
                  value={editedSymptom.severity?.toString() || ""}
                  onChange={(e) => setEditedSymptom(prev => ({ ...prev, severity: e.target.value }))}
                  required
                >
                  <option value="mild">Mild</option>
                  <option value="moderate">Moderate</option>
                  <option value="severe">Severe</option>
                  <option value="critical">Critical</option>
                  <option value="life_threatening">Life Threatening</option>
                  <option value="death">Death (Fatal)</option>
                </select>
              </div>
            </div>
            <div className="col-12">
              <div className="form-group">
                <label htmlFor="description">Description</label>
                <textarea
                  className="form-control"
                  id="description"
                  value={editedSymptom.description || ""}
                  onChange={(e) => setEditedSymptom(prev => ({ ...prev, description: e.target.value }))}
                  required
                  rows={4}
                />
              </div>
            </div>
            <div className="col-md-6">
              <div className="form-group">
                <label htmlFor="start_date">Start Date</label>
                <input
                  type="date"
                  className="form-control"
                  id="start_date"
                  value={editedSymptom.start_date || ""}
                  onChange={(e) => setEditedSymptom(prev => ({ ...prev, start_date: e.target.value }))}
                  required
                />
              </div>
            </div>
            <div className="col-md-6">
              <div className="form-group">
                <label htmlFor="start_time">Start Time</label>
                <input
                  type="time"
                  className="form-control"
                  id="start_time"
                  value={editedSymptom.start_time || ""}
                  onChange={(e) => setEditedSymptom(prev => ({ ...prev, start_time: e.target.value }))}
                  required
                />
              </div>
            </div>
            <div className="col-md-6">
              <div className="form-group">
                <label htmlFor="status">Status</label>
                <select
                  className="form-control"
                  id="status"
                  value={editedSymptom.status || ""}
                  onChange={(e) => setEditedSymptom(prev => ({ ...prev, status: e.target.value }))}
                  required
                >
                  <option value="resolved">Resolved</option>
                  <option value="recovered">Recovered with sequelae</option>
                  <option value="ongoing_treatment">Ongoing / Continuing treatment</option>
                  <option value="condition_worsening">Condition worsening</option>
                  <option value="unknown">Unknown</option>
                </select>
              </div>
            </div>
            {editedSymptom.status === "resolved" && (
              <div className="col-md-6">
                <div className="form-group">
                  <label htmlFor="resolved_date">Resolved Date</label>
                  <input
                    type="date"
                    className="form-control"
                    id="resolved_date"
                    value={editedSymptom.resolved_date || ""}
                    onChange={(e) => setEditedSymptom(prev => ({ ...prev, resolved_date: e.target.value }))}
                    required
                  />
                </div>
              </div>
            )}
            <div className="col-md-6">
              <div className="form-group">
                <label htmlFor="relatedness">Relatedness</label>
                <select
                  className="form-control"
                  id="relatedness"
                  value={editedSymptom.relatedness || ""}
                  onChange={(e) => setEditedSymptom(prev => ({ ...prev, relatedness: e.target.value }))}
                  required
                >
                  <option value="definitely_related">Definitely related</option>
                  <option value="probably_related">Probably related</option>
                  <option value="possibly_related">Possibly related</option>
                  <option value="unlikely_related">Unlikely</option>
                  <option value="unrelated">Unrelated</option>
                </select>
              </div>
            </div>
            {!isPatient && (
              <div className="col-md-6">
                <div className="form-group">
                  <label htmlFor="category">Category</label>
                  <select
                    className="form-control"
                    id="category"
                    value={editedSymptom.category || ""}
                    onChange={(e) => setEditedSymptom(prev => ({ ...prev, category: e.target.value }))}
                    required
                  >
                    <option value="nan">N/A</option>
                    <option value="sae">SAEs</option>
                    <option value="ae">AEs</option>
                    <option value="ar">ARs</option>
                    <option value="ssar">SSARs</option>
                    <option value="susar">SUSARs</option>
                  </select>
                </div>
              </div>
            )}
            <div className="col-md-6">
              <div className="form-group">
                <label htmlFor="hospitalization_required">Hospitalization Required</label>
                <select
                  className="form-control"
                  id="hospitalization_required"
                  value={editedSymptom.hospitalization_required ? "true" : "false"}
                  onChange={(e) => setEditedSymptom(prev => ({ ...prev, hospitalization_required: e.target.value === "true" }))}
                  required
                >
                  <option value="false">No</option>
                  <option value="true">Yes</option>
                </select>
              </div>
            </div>
            {!isPatient && (
              <div className="col-12">
                <div className="form-group">
                  <label htmlFor="patient_comment">Reviewed Comment</label>
                  <textarea
                    className="form-control"
                    id="patient_comment"
                    value={editedSymptom.patient_comment || ""}
                    onChange={(e) => setEditedSymptom(prev => ({ ...prev, patient_comment: e.target.value }))}
                    rows={3}
                  />
                </div>
              </div>
            )}
          </div>
          <div className="modal-footer">
            <button type="submit" className="button -md btn-nurtify text-white me-2">
              Save Changes
            </button>
            <button type="button" className="button -md btn-nurtify-lighter" onClick={onClose}>
              Cancel
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

const PatientDiary: React.FC = () => {
  const { selectedPatient, isLoading: isPatientLoading } = useSelectedPatientStore();
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [editingSymptom, setEditingSymptom] = useState<Symptom | null>(null);
  const [viewingSymptom, setViewingSymptom] = useState<Symptom | null>(null);
  const [viewingLogsFor, setViewingLogsFor] = useState<string | null>(null);

  // State for filters
  const [selectedStatus, setSelectedStatus] = useState<string[]>([]);
  const [selectedSeverities, setSelectedSeverities] = useState<string[]>([]);
  const [selectedReviewStatuses, setSelectedReviewStatuses] = useState<string[]>([]);

  const updateSymptomMutation = useUpdateSymptomMutation();
  const { data: symptoms = [], isLoading: isLoadingSymptoms } = useSymptomsQuery(selectedPatient?.uuid || "");

  // Memoized filter options with counts based on the raw symptoms data
  const filterOptionsWithCounts = useMemo(() => {
    const statusCounts = new Map<string, number>();
    const severityCounts = new Map<string, number>();
    const reviewStatusCounts = new Map<string, number>();

    symptoms.forEach(symptom => {
      const statusKey = symptom.status;
      statusCounts.set(statusKey, (statusCounts.get(statusKey) || 0) + 1);

      const severityKey = symptom.severity;
      severityCounts.set(severityKey, (severityCounts.get(severityKey) || 0) + 1);

      const reviewStatusKey = symptom.review_status;
      reviewStatusCounts.set(reviewStatusKey, (reviewStatusCounts.get(reviewStatusKey) || 0) + 1);
    });

    const statusOptions = Array.from(statusCounts.keys()).map(key => ({
      label: `${STATUS_MAP[key as keyof typeof STATUS_MAP]} (${statusCounts.get(key)})`,
      value: key
    }));

    const severityOptions = Array.from(severityCounts.keys()).map(key => ({
      label: `${SEVERITY_MAP[key as keyof typeof SEVERITY_MAP]} (${severityCounts.get(key)})`,
      value: key
    }));

    const reviewStatusOptions = Array.from(reviewStatusCounts.keys()).map(key => ({
      label: key === "reviewed" ? `Reviewed (${reviewStatusCounts.get(key)})` : `Not Reviewed (${reviewStatusCounts.get(key)})`,
      value: key
    }));

    return { statusOptions, severityOptions, reviewStatusOptions };
  }, [symptoms]);

  // Memoized filtered symptoms based on selected filters
  const filteredSymptoms = useMemo(() => {
    let filtered = symptoms;

    if (selectedStatus.length > 0) {
      filtered = filtered.filter(symptom =>
        selectedStatus.includes(symptom.status)
      );
    }

    if (selectedSeverities.length > 0) {
      filtered = filtered.filter(symptom =>
        selectedSeverities.includes(symptom.severity)
      );
    }

    if (selectedReviewStatuses.length > 0) {
      filtered = filtered.filter(symptom =>
        selectedReviewStatuses.includes(symptom.review_status)
      );
    }

    // Sort by created_at in descending order (newest first)
    filtered = filtered.sort((a, b) => {
      const dateA = new Date(a.created_at).getTime();
      const dateB = new Date(b.created_at).getTime();
      return dateB - dateA; // Descending order (newest first)
    });

    // Add status_display property with type-safe conversion
    return filtered.map(symptom => {
      return {
        ...symptom,
        status_display: STATUS_MAP[symptom.status as keyof typeof STATUS_MAP] || "Unknown"
      };
    });
  }, [symptoms, selectedStatus, selectedSeverities, selectedReviewStatuses]);

  const filters: NurtifyFilterItem[] = [
    {
      label: "Status",
      type: "checkbox",
      options: filterOptionsWithCounts.statusOptions,
      value: selectedStatus,
      onChange: setSelectedStatus as any,
    },
    {
      label: "Severity",
      type: "checkbox",
      options: filterOptionsWithCounts.severityOptions,
      value: selectedSeverities,
      onChange: setSelectedSeverities as any,
    },
    {
      label: "Review Status",
      type: "checkbox",
      options: filterOptionsWithCounts.reviewStatusOptions,
      value: selectedReviewStatuses,
      onChange: setSelectedReviewStatuses as any,
    },
  ];

  if (isPatientLoading) {
    return <Preloader />;
  }

  if (!selectedPatient) {
    return <div>No patient selected</div>;
  }

  const handleEditSymptom = (symptom: Symptom) => {
    setEditingSymptom(symptom);
  };

  const handleCancelEdit = () => {
    setEditingSymptom(null);
  };

  const handleSaveEdit = async (updatedSymptom: Partial<CreateSymptomData>) => {
    if (!editingSymptom) return;

    try {
      await updateSymptomMutation.mutateAsync({
        uuid: editingSymptom.uuid,
        data: updatedSymptom
      });
      setEditingSymptom(null);
    } catch (error) {
      console.error("Error updating symptom:", error);
    }
  };

  const handleViewSymptom = (symptom: Symptom) => {
    setViewingSymptom(symptom);
  };

  const handleCloseView = () => {
    setViewingSymptom(null);
  };

  const handleViewLogs = (symptomUuid: string) => {
    setViewingLogsFor(symptomUuid);
  };

  const handleCloseLogs = () => {
    setViewingLogsFor(null);
  };

  const symptomColumns: Column<Symptom>[] = [
    { key: "name", header: "Symptom Name", sortable: true },
    { key: "description", header: "Description", sortable: true },
    { key: "start_date", header: "Start Date", sortable: true },
    { key: "start_time", header: "Start Time", sortable: true },
    {
      key: "severity",
      header: "Severity",
      sortable: true,
      render: (value: string | number | boolean | null | undefined) => {
        return SEVERITY_MAP[value as keyof typeof SEVERITY_MAP] || String(value ?? "");
      }
    },
    {
      key: "category",
      header: "Category",
      sortable: true,
      render: (value: string | number | boolean | null | undefined) => {
        return CATEGORY_MAP[value as keyof typeof CATEGORY_MAP] || String(value ?? "");
      }
    },
    {
      key: "review_status",
      header: "Review Status",
      sortable: true,
      render: (value: string | number | boolean | null | undefined) => {
        const reviewStatus = value as string;
        if (reviewStatus === "reviewed") {
          return (
            <div style={{
              display: 'inline-flex',
              alignItems: 'center',
              gap: '6px',
              padding: '6px 12px',
              backgroundColor: '#f0fdf4',
              border: '1px solid #22c55e',
              borderRadius: '16px',
              color: '#166534',
              fontSize: '0.75rem',
              fontWeight: '600',
              textTransform: 'uppercase'
            }}>
              <CheckCircle size={14} />
              Reviewed
            </div>
          );
        } else {
          return (
            <div style={{
              display: 'inline-flex',
              alignItems: 'center',
              gap: '6px',
              padding: '6px 12px',
              backgroundColor: '#fff7ed',
              border: '1px solid #f97316',
              borderRadius: '16px',
              color: '#ea580c',
              fontSize: '0.75rem',
              fontWeight: '600',
              textTransform: 'uppercase'
            }}>
              <Clock size={14} />
              Not Reviewed
            </div>
          );
        }
      }
    },
    {
      key: "actions" as keyof Symptom,
      header: "Actions",
      sortable: false,
      render: (_: string | number | boolean | null | undefined, row?: Symptom) => row && (
        <div className="actions-cell">
          <div className="action-button-wrapper">
            <button
              className="action-button"
              onClick={() => handleViewSymptom(row)}
              title="View details"
            >
              <span className="action-icon">
                <Eye size={16} />
              </span>
            </button>
            <div className="tooltip">View Details</div>
          </div>
          <div className="action-button-wrapper">
            <button
              className="action-button"
              onClick={() => handleViewLogs(row.uuid)}
              title="View history"
            >
              <span className="action-icon">
                <History size={16} />
              </span>
            </button>
            <div className="tooltip">View History</div>
          </div>
          <div className="action-button-wrapper">
            <button
              className="action-button"
              onClick={() => handleEditSymptom(row)}
              title="Edit symptom"
            >
              <span className="action-icon">
                <Edit size={16} />
              </span>
            </button>
            <div className="tooltip">Edit Symptom</div>
          </div>
        </div>
      )
    }
  ];

  const handleAddSymptom = () => {
    if (!selectedPatient?.uuid) {
      console.error('No patient UUID available');
      return;
    }
    setIsModalOpen(true);
  };

  const handleModalClose = () => {
    setIsModalOpen(false);
  };

  return (
    <motion.div
      className="patclin-tab-content"
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      transition={{ duration: 0.5 }}
    >
      <div className="d-flex justify-content-between align-items-center mb-4">
        <h2 className="patclin-section-title">Patient Health Diary</h2>
        <button
          className="btn btn-primary d-flex align-items-center gap-2"
          onClick={handleAddSymptom}
        >
          <Plus size={20} />
          Add Symptom
        </button>
      </div>

      {isLoadingSymptoms ? (
        <div className="text-center">Loading symptoms...</div>
      ) : filteredSymptoms.length > 0 ? (
        <>
        <div style={{ paddingBottom: "15px"  }}>
          <NurtifyFilter layout="horizontal" filters={filters} />
        </div>
        <DataTable
          data={filteredSymptoms}
          columns={symptomColumns}
          noDataMessage="No symptoms recorded yet"
        />
        </>
      ) : (
        <div className="patclin-empty-state">
          <p>No symptoms recorded for this patient or no symptoms match the current filter.</p>
        </div>
      )}

      <AddSymptomModal
        isOpen={isModalOpen}
        onClose={handleModalClose}
        patientUuid={selectedPatient.uuid}
        onSuccess={handleModalClose}
      />

      <ViewSymptomModal
        isOpen={!!viewingSymptom}
        onClose={handleCloseView}
        symptom={viewingSymptom}
      />

      <EditSymptomModal
        isOpen={!!editingSymptom}
        onClose={handleCancelEdit}
        symptom={editingSymptom}
        onSave={handleSaveEdit}
      />

      <SymptomLogsModal
        isOpen={!!viewingLogsFor}
        onClose={handleCloseLogs}
        symptomUuid={viewingLogsFor || ""}
      />
    </motion.div>
  );
};

export default PatientDiary;

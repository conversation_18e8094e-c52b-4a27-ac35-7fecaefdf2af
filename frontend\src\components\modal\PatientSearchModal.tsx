import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { Patient, PaginatedResponse } from '@/services/api/types';
import PatientSearchForm from '../PatientSearchForm';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faTimes, faUserPlus } from '@fortawesome/free-solid-svg-icons';
import useSelectedPatientStore from '@/store/SelectedPatientState';
import { useAddPatientAccessLogMutation } from '@/hooks/patient.query';
import './PatientSearchModal.css';

interface PatientSearchModalProps {
  isOpen: boolean;
  onClose: () => void;
  onOpenAddPatientModal: () => void;
}

const PatientSearchModal: React.FC<PatientSearchModalProps> = ({
  isOpen,
  onClose,
  onOpenAddPatientModal
}) => {
  const navigate = useNavigate();
  const { setSelectedPatient } = useSelectedPatientStore();
  const [searchSubmitted, setSearchSubmitted] = useState(false);
  const [searchResults, setSearchResults] = useState<PaginatedResponse<Patient> | null>(null);

  const addPatientAccessLogMutation = useAddPatientAccessLogMutation();

  const handleSelectPatient = (patient: Patient) => {
    // Set the selected patient in the store
    setSelectedPatient(patient);

    // Call the mutation to log the access
    addPatientAccessLogMutation.mutate({
      patient_uuid: patient.uuid,
      access_type: 'view', // Or a more specific type if available
    });

    // Navigate to patient dashboard
    navigate('/org/dashboard/patient-board');
    onClose();
  };

  const handleAddNewPatient = () => {
    onClose();
    onOpenAddPatientModal();
  };

  if (!isOpen) return null;

  return (
    <div className="patient-search-modal-overlay">
      <div className="patient-search-modal">
        <div className="patient-search-modal-header">
          <h2 className="patient-search-modal-title">Search / Add Patient</h2>
          <button
            className="patient-search-modal-close-button"
            onClick={onClose}
            aria-label="Close modal"
          >
            <FontAwesomeIcon icon={faTimes} />
          </button>
        </div>

        <div className="patient-search-modal-content">
          <PatientSearchForm
            onSelectPatient={handleSelectPatient}
            onSearchSubmitted={setSearchSubmitted}
            onSearchResults={setSearchResults}
          />

          {/* Show "Add New Patient" button when no results found */}
          {searchSubmitted && searchResults && searchResults.results.length === 0 && (
            <div className="patient-search-modal-no-results-section">
              <div className="patient-search-modal-no-results-message">
                <p>No patients found matching your search criteria.</p>
                <button
                  className="patient-search-modal-add-patient-button"
                  onClick={handleAddNewPatient}
                >
                  <FontAwesomeIcon icon={faUserPlus} />
                  Add New Patient
                </button>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default PatientSearchModal;

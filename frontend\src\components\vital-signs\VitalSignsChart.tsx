import React, { useState } from 'react';
import {
  LineChart,
  Line,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer,
  ComposedChart,
  Bar,
  Area,
  AreaChart
} from 'recharts';
import { ChartDataPoint } from '../../services/api/vital-signs.service';
import { formatDate } from '../../utils/vitalSignsUtils';
import './VitalSignsChart.css';

interface VitalSignsChartProps {
  data: ChartDataPoint[];
  isLoading?: boolean;
}

type ChartType = 'line' | 'area' | 'composed';
type VitalSignType = 'temperature' | 'heart_rate' | 'blood_pressure' | 'respiratory_rate' | 'oxygen_saturation' | 'news_score';

const VitalSignsChart: React.FC<VitalSignsChartProps> = ({ data, isLoading = false }) => {
  const [selectedChartType, setSelectedChartType] = useState<ChartType>('line');
  const [selectedVitalSign, setSelectedVitalSign] = useState<VitalSignType>('temperature');

  if (isLoading) {
    return (
      <div className="chart-loading">
        <div className="loading-spinner"></div>
        <p>Loading chart data...</p>
      </div>
    );
  }

  if (!data || data.length === 0) {
    return (
      <div className="chart-no-data">
        <p>No chart data available</p>
      </div>
    );
  }

  // Process data for charts
  const processedData = data.map(item => ({
    ...item,
    date: formatDate(item.recorded_at),
    blood_pressure_systolic: item.systolic_bp,
    blood_pressure_diastolic: item.diastolic_bp,
  }));

  // Color mapping
  const getColorForVitalSign = (vitalSign: VitalSignType, item: ChartDataPoint): string => {
    switch (vitalSign) {
      case 'temperature':
        return item.temperature_color === 'green' ? '#10b981' : 
               item.temperature_color === 'red' ? '#ef4444' : 
               item.temperature_color === 'orange' ? '#f59e0b' : '#3b82f6';
      case 'heart_rate':
        return item.heart_rate_color === 'green' ? '#10b981' : 
               item.heart_rate_color === 'red' ? '#ef4444' : 
               item.heart_rate_color === 'orange' ? '#f59e0b' : '#3b82f6';
      case 'blood_pressure':
        return item.blood_pressure_color === 'green' ? '#10b981' : 
               item.blood_pressure_color === 'red' ? '#ef4444' : 
               item.blood_pressure_color === 'orange' ? '#f59e0b' : '#3b82f6';
      case 'respiratory_rate':
        return item.respiratory_rate_color === 'green' ? '#10b981' : 
               item.respiratory_rate_color === 'red' ? '#ef4444' : 
               item.respiratory_rate_color === 'orange' ? '#f59e0b' : '#3b82f6';
      case 'oxygen_saturation':
        return item.oxygen_saturation_color === 'green' ? '#10b981' : 
               item.oxygen_saturation_color === 'red' ? '#ef4444' : 
               item.oxygen_saturation_color === 'orange' ? '#f59e0b' : '#3b82f6';
      case 'news_score':
        return item.news_score_color === 'green' ? '#10b981' : 
               item.news_score_color === 'red' ? '#ef4444' : 
               item.news_score_color === 'orange' ? '#f59e0b' : '#3b82f6';
      default:
        return '#6b7280';
    }
  };

  const getYAxisDomain = (vitalSign: VitalSignType) => {
    switch (vitalSign) {
      case 'temperature':
        return [30, 45];
      case 'heart_rate':
        return [40, 200];
      case 'blood_pressure':
        return [60, 200];
      case 'respiratory_rate':
        return [5, 40];
      case 'oxygen_saturation':
        return [85, 100];
      case 'news_score':
        return [0, 20];
      default:
        return ['auto', 'auto'];
    }
  };

  const getYAxisLabel = (vitalSign: VitalSignType) => {
    switch (vitalSign) {
      case 'temperature':
        return 'Temperature (°C)';
      case 'heart_rate':
        return 'Heart Rate (bpm)';
      case 'blood_pressure':
        return 'Blood Pressure (mmHg)';
      case 'respiratory_rate':
        return 'Respiratory Rate (/min)';
      case 'oxygen_saturation':
        return 'Oxygen Saturation (%)';
      case 'news_score':
        return 'NEWS Score';
      default:
        return '';
    }
  };

  const renderChart = () => {
    const color = getColorForVitalSign(selectedVitalSign, processedData[0]);

    switch (selectedChartType) {
      case 'line':
        if (selectedVitalSign === 'blood_pressure') {
          return (
            <ResponsiveContainer width="100%" height={400}>
              <LineChart data={processedData}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="date" />
                <YAxis domain={getYAxisDomain(selectedVitalSign)} label={{ value: getYAxisLabel(selectedVitalSign), angle: -90, position: 'insideLeft' }} />
                <Tooltip />
                <Legend />
                <Line 
                  type="monotone" 
                  dataKey="blood_pressure_systolic" 
                  stroke="#ef4444" 
                  strokeWidth={2}
                  name="Systolic BP"
                  dot={{ fill: '#ef4444', strokeWidth: 2, r: 4 }}
                />
                <Line 
                  type="monotone" 
                  dataKey="blood_pressure_diastolic" 
                  stroke="#3b82f6" 
                  strokeWidth={2}
                  name="Diastolic BP"
                  dot={{ fill: '#3b82f6', strokeWidth: 2, r: 4 }}
                />
              </LineChart>
            </ResponsiveContainer>
          );
        } else {
          return (
            <ResponsiveContainer width="100%" height={400}>
              <LineChart data={processedData}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="date" />
                <YAxis domain={getYAxisDomain(selectedVitalSign)} label={{ value: getYAxisLabel(selectedVitalSign), angle: -90, position: 'insideLeft' }} />
                <Tooltip />
                <Legend />
                <Line 
                  type="monotone" 
                  dataKey={selectedVitalSign} 
                  stroke={color} 
                  strokeWidth={2}
                  name={selectedVitalSign.replace('_', ' ').toUpperCase()}
                  dot={{ fill: color, strokeWidth: 2, r: 4 }}
                />
              </LineChart>
            </ResponsiveContainer>
          );
        }

      case 'area':
        return (
          <ResponsiveContainer width="100%" height={400}>
            <AreaChart data={processedData}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="date" />
              <YAxis domain={getYAxisDomain(selectedVitalSign)} label={{ value: getYAxisLabel(selectedVitalSign), angle: -90, position: 'insideLeft' }} />
              <Tooltip />
              <Legend />
              <Area 
                type="monotone" 
                dataKey={selectedVitalSign} 
                stroke={color} 
                fill={color}
                fillOpacity={0.3}
                name={selectedVitalSign.replace('_', ' ').toUpperCase()}
              />
            </AreaChart>
          </ResponsiveContainer>
        );

      case 'composed':
        return (
          <ResponsiveContainer width="100%" height={400}>
            <ComposedChart data={processedData}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="date" />
              <YAxis domain={getYAxisDomain(selectedVitalSign)} label={{ value: getYAxisLabel(selectedVitalSign), angle: -90, position: 'insideLeft' }} />
              <Tooltip />
              <Legend />
              <Bar 
                dataKey={selectedVitalSign} 
                fill={color}
                fillOpacity={0.7}
                name={selectedVitalSign.replace('_', ' ').toUpperCase()}
              />
              <Line 
                type="monotone" 
                dataKey={selectedVitalSign} 
                stroke={color} 
                strokeWidth={2}
                name={`${selectedVitalSign.replace('_', ' ').toUpperCase()} Trend`}
              />
            </ComposedChart>
          </ResponsiveContainer>
        );

      default:
        return null;
    }
  };

  return (
    <div className="vital-signs-chart">
      <div className="chart-header">
        <h3>Vital Signs Trends</h3>
        <div className="chart-controls">
          <div className="chart-type-selector">
            <label>Chart Type:</label>
            <select 
              value={selectedChartType} 
              onChange={(e) => setSelectedChartType(e.target.value as ChartType)}
            >
              <option value="line">Line Chart</option>
              <option value="area">Area Chart</option>
              <option value="composed">Composed Chart</option>
            </select>
          </div>
          <div className="vital-sign-selector">
            <label>Vital Sign:</label>
            <select 
              value={selectedVitalSign} 
              onChange={(e) => setSelectedVitalSign(e.target.value as VitalSignType)}
            >
              <option value="temperature">Temperature</option>
              <option value="heart_rate">Heart Rate</option>
              <option value="blood_pressure">Blood Pressure</option>
              <option value="respiratory_rate">Respiratory Rate</option>
              <option value="oxygen_saturation">Oxygen Saturation</option>
              <option value="news_score">NEWS Score</option>
            </select>
          </div>
        </div>
      </div>

      <div className="chart-container">
        {renderChart()}
      </div>

      <div className="chart-legend">
        <div className="legend-item">
          <span className="legend-color green"></span>
          <span>Normal</span>
        </div>
        <div className="legend-item">
          <span className="legend-color blue"></span>
          <span>Low</span>
        </div>
        <div className="legend-item">
          <span className="legend-color red"></span>
          <span>High</span>
        </div>
        <div className="legend-item">
          <span className="legend-color orange"></span>
          <span>Critical</span>
        </div>
      </div>
    </div>
  );
};

export default VitalSignsChart; 
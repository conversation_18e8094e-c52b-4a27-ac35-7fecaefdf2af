.live-chat-container {
  max-width: 800px;
  margin: 0 auto;
  padding: 20px;
  background-color: #ffffff;
  min-height: 100vh;
}

.live-chat-header {
  margin-bottom: 24px;
}

.live-chat-header h1 {
  font-size: 32px;
  font-weight: 700;
  color: #1f2937;
  margin: 0;
}

.search-container {
  margin-bottom: 24px;
}

.search-input-wrapper {
  position: relative;
  max-width: 400px;
}

.search-icon {
  position: absolute;
  left: 12px;
  top: 50%;
  transform: translateY(-50%);
  color: #9ca3af;
  pointer-events: none;
}

.search-input {
  width: 100%;
  padding: 12px 12px 12px 44px;
  border: none;
  border-radius: 12px;
  background-color: #f3f4f6;
  font-size: 14px;
  color: #374151;
  box-sizing: border-box;
  transition: background-color 0.2s ease;
}

.search-input:focus {
  outline: none;
  background-color: #e5e7eb;
}

.search-input::placeholder {
  color: #9ca3af;
  text-transform: lowercase;
}

.ai-agent-section {
  margin-bottom: 32px;
}

.ai-agent-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 16px;
  border-radius: 12px;
  background-color: #f8fafc;
  border: 1px solid #e2e8f0;
  cursor: pointer;
  transition: all 0.2s ease;
}

.ai-agent-item:hover {
  background-color: #f1f5f9;
  border-color: #cbd5e1;
}

.ai-agent-icon {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background-color: #37b7c3;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
}

.ai-agent-item span {
  font-size: 16px;
  font-weight: 500;
  color: #374151;
}

.chat-list {
  margin-bottom: 32px;
}

.chat-item {
  display: flex;
  gap: 12px;
  padding: 16px;
  border-radius: 12px;
  cursor: pointer;
  transition: background-color 0.2s ease;
  margin-bottom: 8px;
}

.chat-item:hover {
  background-color: #f9fafb;
}

.chat-avatar {
  flex-shrink: 0;
}

.avatar-circle {
  width: 48px;
  height: 48px;
  border-radius: 50%;
  background-color: #37b7c3;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-weight: 600;
  font-size: 14px;
}

.chat-content {
  flex: 1;
  min-width: 0;
}

.chat-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 4px;
}

.chat-name {
  font-weight: 600;
  color: #1f2937;
  font-size: 16px;
}

.chat-time {
  font-size: 12px;
  color: #9ca3af;
  flex-shrink: 0;
}

.chat-message {
  color: #6b7280;
  font-size: 14px;
  margin-bottom: 8px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.chat-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.department-tag {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 12px;
  color: #37b7c3;
  font-weight: 500;
}

.status-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  display: inline-block;
}

.unread-badge {
  background-color: #ef4444;
  color: white;
  font-size: 12px;
  font-weight: 600;
  padding: 2px 6px;
  border-radius: 10px;
  min-width: 18px;
  text-align: center;
}

.empty-state {
  text-align: center;
  padding: 48px 24px;
  color: #6b7280;
}

.empty-icon {
  color: #d1d5db;
  margin-bottom: 16px;
}

.empty-state p {
  margin: 8px 0;
  font-size: 16px;
}

.empty-subtitle {
  font-size: 14px !important;
  color: #9ca3af !important;
}

.start-conversation-container {
  position: fixed;
  bottom: 24px;
  right: 24px;
  z-index: 100;
}

.start-conversation-btn {
  background-color: #37b7c3;
  color: white;
  border: none;
  border-radius: 25px;
  padding: 16px 24px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  box-shadow: 0 4px 12px rgba(55, 183, 195, 0.3);
  transition: all 0.2s ease;
}

.start-conversation-btn:hover {
  background-color: #2d919a;
  transform: translateY(-2px);
  box-shadow: 0 6px 16px rgba(55, 183, 195, 0.4);
}

.loading-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 48px 24px;
  color: #6b7280;
}

.loading-spinner {
  width: 32px;
  height: 32px;
  border: 3px solid #e5e7eb;
  border-top: 3px solid #37b7c3;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 16px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Responsive design */
@media (max-width: 768px) {
  .live-chat-container {
    padding: 16px;
  }

  .live-chat-header h1 {
    font-size: 28px;
  }

  .search-input-wrapper {
    max-width: 100%;
  }

  .chat-item {
    padding: 12px;
  }

  .avatar-circle {
    width: 40px;
    height: 40px;
    font-size: 12px;
  }

  .start-conversation-container {
    bottom: 16px;
    right: 16px;
  }

  .start-conversation-btn {
    padding: 14px 20px;
    font-size: 14px;
  }
}

@media (max-width: 480px) {
  .chat-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 4px;
  }

  .chat-time {
    align-self: flex-end;
  }

  .start-conversation-btn {
    padding: 12px 16px;
    font-size: 14px;
    border-radius: 20px;
  }
}

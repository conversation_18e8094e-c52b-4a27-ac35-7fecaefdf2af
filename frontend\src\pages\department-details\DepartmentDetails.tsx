import "./department-details.css";
import { useState, useRef, useEffect } from "react";
import { Pencil, PencilOff } from "lucide-react";
import NurtifyText from "@components/NurtifyText.tsx";
import NurtifyInput from "@components/NurtifyInput.tsx";
import { motion } from "framer-motion";
import DeleteDepartmentModal from "@/components/modal/DeleteDepartmentModal";
import { Department as DepartmentData } from "@/services/api/types.ts";
import {
  useDepartmentQuery,
  useUpdateDepartmentMutation,
  useDeleteDepartmentMutation,
} from "@/hooks/department.query";
import { useCurrentUserQuery } from "@/hooks/user.query";
import { getUserRole, can} from "@/services/permission-system";
import { useNavigate } from "react-router-dom";

import { CountrySelect  } from "react-country-state-city";
import "react-country-state-city/dist/react-country-state-city.css";

interface DepartmentDetailsProps {
  uuid: string;
}

type Country = {
  id: number;
  name: string;
  iso2: string;
};

const DepartmentDetails: React.FC<DepartmentDetailsProps> = ({ uuid }) => {
  const navigate = useNavigate();
  const { data: departmentData, isLoading } = useDepartmentQuery(uuid!);
  const updateDepartmentMutation = useUpdateDepartmentMutation();
  const deleteDepartmentMutation = useDeleteDepartmentMutation();

  const { data: currentUser} = useCurrentUserQuery();
  const role = getUserRole(currentUser);

  const [editableDepartment, setEditableDepartment] = useState<DepartmentData | null>(null);
  const [hospitalName, setHospitalName] = useState<string>("");
  const [changedFields, setChangedFields] = useState<Partial<DepartmentData>>({});
  const [isEditing, setIsEditing] = useState(false);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [isUpdateEnabled, setIsUpdateEnabled] = useState(false);

  const targetRef = useRef<HTMLDivElement>(null);

  const [selectedCountry, setSelectedCountry] = useState<Country | null>(null);

  useEffect(() => {
    if (departmentData) {
      setEditableDepartment({
        ...departmentData,
        hospital: departmentData.hospital, // Preserve original hospital data
      });
      setHospitalName(
        typeof departmentData.hospital === "object" && departmentData.hospital !== null
          ? departmentData.hospital.name || ""
          : ""
      );

      setSelectedCountry({
        id: 0,
        iso2: "",
        name: departmentData.country || "",
      } as Country);
    }
  }, [departmentData]);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    if (!editableDepartment || !departmentData) return;

    const { id, value } = e.target;

    setEditableDepartment((prev) => ({
      ...prev!,
      [id]: value,
    }));

    setChangedFields((prev) => {
      const updatedChanges = { ...prev };
      if (departmentData[id as keyof DepartmentData] !== value) {
        updatedChanges[id as keyof DepartmentData] = value;
      } else {
        delete updatedChanges[id as keyof DepartmentData];
      }
      setIsUpdateEnabled(Object.keys(updatedChanges).length > 0);
      return updatedChanges;
    });
  };

  const handleDeleteClick = () => {
    setIsEditing(false);
    setIsModalOpen(true);
  };

  const handleCloseModal = () => {
    setIsEditing(true);
    setIsModalOpen(false);
  };

  const handleCountrySelectChange = (val: Country | null) => {
  setSelectedCountry(val);

  if (!editableDepartment || !departmentData) return;

  const newCountry = val?.name || "";

  setEditableDepartment((prev) => ({
    ...prev!,
    country: newCountry,
  }));

  setChangedFields((prev) => {
    const updatedChanges = { ...prev };
    const currentValue = departmentData.country;
    if (currentValue !== newCountry) {
      updatedChanges.country = newCountry;
    } else {
      delete updatedChanges.country;
    }
    setIsUpdateEnabled(Object.keys(updatedChanges).length > 0);
    return updatedChanges;
  });
};  

  const handleSubmitModal = async () => {
    if (!uuid) return;

    try {
      await deleteDepartmentMutation.mutateAsync({ uuid });
      navigate("/org/dashboard/department", { replace: true });
    } catch (error) {
      console.error("Error deleting department:", error);
    }
  };

  const handleScroll = () => {
    if (targetRef.current) {
      targetRef.current.scrollIntoView({ behavior: "smooth" });
    }
  };

  const handleUpdateClick = () => {
    setIsEditing(true);
    handleScroll();
  };

  const handleCancelClick = () => {
    setEditableDepartment(departmentData || null);
    setChangedFields({});
    setIsEditing(false);
    setIsUpdateEnabled(false);
  };

  const handleSaveClick = async () => {
    if (!uuid || !isUpdateEnabled || Object.keys(changedFields).length === 0) {
      console.error("UUID is undefined or no changes to update.");
      return;
    }

    try {
      await updateDepartmentMutation.mutateAsync({
        uuid,
        data: changedFields,
      });
      navigate("/org/dashboard/department", { replace: true });
    } catch (error) {
      console.error("Error updating department:", error);
    }
  };

  const loadingMessage = isLoading ? <p>is loading ...</p> : null;

  return (
    <>
      {loadingMessage}
      {!isLoading && !editableDepartment && <p>not found!</p>}
      <div className="editableDepartment-container">
        {can(role, "Hospital", "update") && !isLoading && editableDepartment && (
          <>
            {!isEditing && (
              <button
                className="dep-details-btn-custom gap-2"
                onClick={handleUpdateClick}
              >
                <Pencil size={18} /> Edit
              </button>
            )}
            {isEditing && (
              <button
                className="dep-details-btn-custom gap-2"
                onClick={handleCancelClick}
              >
                <PencilOff size={18} /> Cancel
              </button>
            )}
          </>
        )}
      </div>
      <form className="add-policy-form" onSubmit={(e) => e.preventDefault()}>
        <motion.div
          className="mb-4"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.3, duration: 0.5 }}
        >
          <div className="row y-gap-30">
            <div className="col-md-6" style={{ marginTop: "10px" }}>
              <NurtifyText label="Department Name*" />
              <NurtifyInput
                type="text"
                name="name"
                value={editableDepartment?.name}
                onChange={handleChange}
                disabled={!isEditing}
                placeholder="Enter Department Name here"
                required
              />
            </div>
          </div>
          <div className="row y-gap-30">
            <div className="col-md-6" style={{ marginTop: "10px" }}>
              <NurtifyText label="Hospital Name*" />
              <NurtifyInput
                type="text"
                name="hospital_name"
                value={hospitalName}
                disabled
                placeholder="Hospital Name"
              />
            </div>
          </div>
          <div className="row y-gap-30" style={{ marginTop: "10px" }}>
            <div className="col-md-6">
              <NurtifyText label="Phone*" />
              <NurtifyInput
                type="number"
                name="phone_number"
                value={editableDepartment?.phone_number}
                onChange={handleChange}
                disabled={!isEditing}
                placeholder="Enter Phone Number here"
                required
              />
            </div>
            <div className="col-md-6">
              <NurtifyText label="Extension*" />
              <NurtifyInput
                type="number"
                name="extension"
                value={editableDepartment?.extension}
                onChange={handleChange}
                disabled={!isEditing}
                placeholder="Enter Extension here"
                required
              />
            </div>
          </div>
          <div className="row y-gap-30" style={{ marginTop: "10px" }}>
            <div className="col-md-6">
              <NurtifyText label="Address Line 1*" />
              <div className="department-detail-input">
                <textarea
                  id="primary_address"
                  name="primary_address"
                  value={editableDepartment?.primary_address || ""}
                  onChange={handleChange}
                  placeholder="Enter Address Line 1"
                  disabled={!isEditing}
                  className={`${!isEditing ? "text-muted" : ""}`}
                  style={{
                    border: "none",
                    outline: "none",
                    backgroundColor: "transparent",
                    padding: "0px",
                  }}
                  required
                />
              </div>
            </div>
            <div className="col-md-6">
              <NurtifyText label="Address Line 2*" />
              <div className="department-detail-input">
                <textarea
                  id="secondary_address"
                  name="secondary_address"
                  value={editableDepartment?.secondary_address || ""}
                  onChange={handleChange}
                  placeholder="Enter Address Line 2"
                  disabled={!isEditing}
                  className={`${!isEditing ? "text-muted" : ""}`}
                  style={{
                    border: "none",
                    outline: "none",
                    backgroundColor: "transparent",
                    padding: "0px",
                  }}
                  required
                />
              </div>
            </div>
          </div>
          <div className="row y-gap-30" style={{ marginTop: "10px" }}>
            <div className="col-md-6">
              <NurtifyText label="Postcode*" />
              <NurtifyInput
                type="text"
                name="postcode"
                value={editableDepartment?.postcode}
                onChange={handleChange}
                disabled={!isEditing}
                placeholder="Enter Postcode here"
                required
              />
            </div>
            <div className="col-md-6">
              <NurtifyText label="Country*" />
              <CountrySelect
                  containerClassName="form-group"
                  inputClassName=""
                  defaultValue={selectedCountry as any}
                  value={selectedCountry as any}
                  onChange={handleCountrySelectChange as any}
                  placeHolder="Select Country"
                  disabled={!isEditing}
                  required
                />
            </div>
          </div>
          <div className="mt-3" style={{ textAlign: "right" }}>
            {isEditing && (
              <>
                <button
                  className="hospital-details-form-btn-custom Delete"
                  onClick={handleDeleteClick}
                >
                  Delete Department
                </button>
                <button
                  className="hospital-details-form-btn-custom Update"
                  disabled={!isUpdateEnabled}
                  onClick={handleSaveClick}
                >
                  {updateDepartmentMutation.isPending ? "Updating..." : "Update Department"}
                </button>
              </>
            )}
          </div>
        </motion.div>
        {isModalOpen && (
          <DeleteDepartmentModal
            isOpen={isModalOpen}
            onClose={handleCloseModal}
            onDelete={handleSubmitModal}
          />
        )}
      </form>
    </>
  );
};

export default DepartmentDetails;
import React from 'react';
import "@/components/modal/DeletePatientModal.css";
type DeletePatientModalProps = {
  isOpen: boolean;
  onClose: () => void;
};

const DeletePatientModal: React.FC<DeletePatientModalProps> = ({ isOpen, onClose }) => {
  const handleDelete = () => {
    // Add delete logic here
    onClose();
  };

  if (!isOpen) return null;

  return (
    <div className="modal-overlay">
      <div className="modal-content">
        <div className="modal-header">
          <h2>Delete Patient</h2>
        </div>
        <div className="modal-body">
          <p>Are you sure you want to proceed with deleting all pending patient notes?</p>
        </div>
        <div className="modal-footer">
          <button className="delete-modal-btn btn-secondary" onClick={onClose}>Cancel</button>
          <button className="delete-modal-btn btn-danger" onClick={handleDelete}>Yes</button>
        </div>
      </div>
    </div>
  );
};

export default DeletePatientModal;

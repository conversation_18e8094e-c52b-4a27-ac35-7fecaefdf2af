import api from "@/services/api.ts";
import { Hospital } from "./types.ts";

export const getAllHospitals = async () => {
    try {
        const response = await api.get("hospital/hospitals/");
        return response.data.results;
    } catch (error) {
        console.error("Error fetching hospitals:", error);
        throw error;
    }
};

export const getHospitalsSelectList = async () => {
    try {
        const response = await api.get("hospital/hospitals/select-list/");
        return response.data.results;        
    } catch (error) {
        console.error("Error fetching hospitals select list:", error);
        throw error;
    }
};

export const getHospitalById = async (uuid: string) => {
    try {
        const response = await api.get(`hospital/hospitals/${uuid}/`);
        return response.data;        
    } catch (error) {
        console.error("Error fetching hospital:", error);
        throw error;
    }
};

export const createHospital = async (HospitalData: Hospital) => {
    const { data } = await api.post("hospital/hospitals/", HospitalData);
    return data;
};

export const updateHospital = async (uuid: string, hospitalData: Partial<Hospital>) => {
    const { data } = await api.put(`hospital/hospitals/${uuid}/`, hospitalData);
    return data;
};

export const deleteHospital = async (uuid: string) => {
    try {
        await api.delete(`hospital/hospitals/${uuid}/`)
    } catch (error) {
        console.error("Error fetching hospital:", error);
        throw error;
    }
};

export const getAllHospitalsWithDepartments = async (): Promise<Hospital[]> => {
    try {
        const response = await api.get("hospital/hospitals/with-departments/");
        return response.data; // no `.results` if not paginated
    } catch (error) {
        console.error("Error fetching hospitals with departments:", error);
        throw error;
    }
};
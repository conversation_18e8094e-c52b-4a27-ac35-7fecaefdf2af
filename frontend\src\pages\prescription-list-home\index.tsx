import React, { useEffect, useState } from "react";
import "./prescriptionList.css";
import { Search, RefreshCw, Eye } from "lucide-react";
import { format } from "date-fns";
import Preloader from "@/components/common/Preloader";
import LightFooter from "@/shared/LightFooter";
import DataTable, { Column, Action } from "@/components/common/DataTable";
import PrescriptionDescriptionModal from "@/components/modal/PrescriptionDescriptionModal";
import { usePrescriptionsQuery } from "@/hooks/prescription.query";
import { useCurrentUserQuery } from "@/hooks/user.query";
import { PrescriptionWithDetails } from "@/services/api/types";

// Define the types for the tabs
type TabType = "All Prescriptions" | "New Prescriptions" | "Prepared" | "Collected";

export default function PrescriptionList() {
    const [activeTab, setActiveTab] = React.useState<TabType>("All Prescriptions");
    const { data, isLoading, isError, error, refetch } = usePrescriptionsQuery();
    const [isShowPresDescModalOpen,setIsShowPresDescModalOpen] = useState(false);
    const [prescriptionDetails, setPrescriptionDetails] = useState<PrescriptionWithDetails | null>(null);
    const {data:CurrentUser} = useCurrentUserQuery();
    const user_identifier = CurrentUser?.identifier || "";
    const [prescriptionUpdated,setPrescriptionUpdated] = useState(false);

    // Effect to refetch prescriptions when an update occurs
    useEffect(() => {
        if(prescriptionUpdated){
            refetch();
            setPrescriptionUpdated(false);
        }
    }, [prescriptionUpdated, refetch]);

    // Normalize the prescription status for display
    const normalizeStatus = (status?: string): string => {
        return status
          ? (status.charAt(0).toUpperCase() + status.slice(1).toLowerCase() )
          : 'Prescribed';
    };

    // Handler for showing prescription details modal
    const handlePrescriptionDescription = (row :PrescriptionWithDetails) => {
        setPrescriptionDetails(row);
        setIsShowPresDescModalOpen(true);
    };

    // Filter prescriptions based on the active tab
    const filteredPrescriptions = data?.filter(prescription => {
        let matchesTab = true;
        switch (activeTab) {
            case "New Prescriptions":
                matchesTab = prescription.status?.toLowerCase() === "prescribed";
                break;
            case "Prepared":
                matchesTab = prescription.status?.toLowerCase() === "prepared";
                break;
            case "Collected":
                matchesTab = prescription.status?.toLowerCase() === "collected";
                break;
            case "All Prescriptions":
            default:
                matchesTab = true;
                break;
        }
        return matchesTab;
    });

    // Define columns for the DataTable
    const prescriptionColumns: Column<PrescriptionWithDetails>[] = [
        { key: "drug_name", header: "Drug Name", sortable: true },
        {
            key: "dose",
            header: "Dose",
            render: (_value, row) => row ? `${row.dose} ${row.unit}` : "",
            sortable: true,
        },
        {
            key: "patient",
            header: "Patient Name",
            render: (_value, row) => row ? `${row.patient.first_name} ${row.patient.last_name}` : "",
            sortable: true,
        },
        {
            key: "nhs_number" as keyof PrescriptionWithDetails,
            header: "NHS Number",
            render: (_value, row) => row?.patient.nhs_number || "",
            sortable: true,
        },
        {
            key: "prescribed_at",
            header: "Prescribed at",
            render: (value) =>
                value && typeof value === 'string'
                    ? format(new Date(value), 'MMMM d, yyyy, hh:mm a')
                    : 'N/A',
            sortable: true,
        },
        ...(activeTab === "All Prescriptions"
            ? [{
                key: "status" as keyof PrescriptionWithDetails,
                header: "Status",
                render: (value:any) => value ? normalizeStatus(value) : 'N/A',
                sortable: true,
            }]
            : []),
    ];

    // Define actions for the DataTable
    const actions: Action<PrescriptionWithDetails>[] = [
        {
            icon: <Eye size={18} />,
            onClick: (row) => handlePrescriptionDescription(row),
            tooltipText: "Show Details",
        },
    ];

    // Show a preloader while data is loading
    if (isLoading) {
        return <Preloader />;
    }

    // Show an error message if data failed to load
    if (isError) return (
        <div className="main-content bg-light-4">
            <div className="content-wrapper js-content-wrapper">
                <div className="dashboard__content bg-light-4">
                    <div className="container-fluid px-0">
                        <div className="error-container">
                            <h2>Error Loading Prescriptions</h2>
                            <p>{(error as Error).message}</p>
                            <button className="refresh-button" onClick={() => refetch()}>
                                <RefreshCw size={16} /> Try Again
                            </button>
                        </div>
                    </div>
                </div>
                <LightFooter />
            </div>
        </div>
    );

    return (
        <div className="main-content bg-light-4">
            <div className="content-wrapper js-content-wrapper">
                <div className="dashboard__content bg-light-4" style={{ backgroundColor: "white" }}>
                    <div className="container mx-auto px-4 py-8">
                        <div className="text-center mb-8">
                            <h1 className="text-4xl font-bold text-gray-800">Prescription List</h1>
                            <p className="text-lg text-gray-600 mt-2">Manage and organize prescriptions efficiently.</p>
                        </div>
                        <div className="flex justify-center mb-6 space-x-2">
                            <button
                                className={`px-4 py-2 rounded-full font-semibold transition-colors duration-200 ${activeTab === "All Prescriptions" ? "bg-indigo-700 text-[#37B7C3] shadow-lg" : "bg-gray-200 text-gray-700 hover:bg-gray-300"}`}
                                onClick={() => setActiveTab("All Prescriptions")}
                            >
                                All Prescriptions
                            </button>
                            <button
                                className={`px-4 py-2 rounded-full font-semibold transition-colors duration-200 ${activeTab === "New Prescriptions" ? "bg-indigo-700 text-[#37B7C3] shadow-lg" : "bg-gray-200 text-gray-700 hover:bg-gray-300"}`}
                                onClick={() => setActiveTab("New Prescriptions")}
                            >
                                New Prescriptions
                            </button>
                            <button
                                className={`px-4 py-2 rounded-full font-semibold transition-colors duration-200 ${activeTab === "Prepared" ? "bg-indigo-700 text-[#37B7C3] shadow-lg" : "bg-gray-200 text-gray-700 hover:bg-gray-300"}`}
                                onClick={() => setActiveTab("Prepared")}
                            >
                                Prepared
                            </button>
                            <button
                                className={`px-4 py-2 rounded-full font-semibold transition-colors duration-200 ${activeTab === "Collected" ? "bg-indigo-700 text-[#37B7C3] shadow-lg" : "bg-gray-200 text-gray-700 hover:bg-gray-300"}`}
                                onClick={() => setActiveTab("Collected")}
                            >
                                Collected
                            </button>
                        </div>
                        <div className="bg-white rounded-lg shadow-lg overflow-hidden">
                            <div className="p-4">
                                {isError && (
                                    <div className="error-message">
                                        Error loading policies. Please try again.
                                    </div>
                                )}
                                <div className="policy-table-container">
                                    {filteredPrescriptions?.length === 0 ? (
                                        <div className="no-results">
                                            <Search size={30} />
                                            <p>No prescriptions found.</p>
                                        </div>
                                    ) : (
                                        <DataTable<PrescriptionWithDetails>
                                            data={filteredPrescriptions || []}
                                            columns={prescriptionColumns}
                                            actions={actions}
                                            noDataMessage="No prescriptions found."
                                            globalFilterPlaceholder="Search prescriptions..."
                                            defaultItemsPerPage={10}
                                        />
                                    )}
                                    <PrescriptionDescriptionModal
                                        isOpen={isShowPresDescModalOpen}
                                        setIsModalOpen={setIsShowPresDescModalOpen}
                                        prescriptionDetails={prescriptionDetails}
                                        userIdentifier={user_identifier}
                                        initialStatus={normalizeStatus(prescriptionDetails?.status)}
                                        setPrescriptionUpdated={setPrescriptionUpdated}
                                        role="pharmacy"
                                    />
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <LightFooter />
        </div>
    );
}

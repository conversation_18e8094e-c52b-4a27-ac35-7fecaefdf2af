import { useState, useEffect, useRef } from 'react';
import { Bell } from 'lucide-react';
import { useNavigate } from 'react-router-dom';
import { useQueryClient } from '@tanstack/react-query';
import { useKeycloak } from '@react-keycloak/web';
import './SystemNotificationBell.css';
import {
  useRecentNotificationsQuery,
  useUnreadCountQuery,
  useMarkNotificationAsReadMutation,
  useMarkAllNotificationsAsReadMutation,
  NOTIFICATION_KEYS
} from '@/hooks/notification.query';
import {
  getNotificationWebSocketUrl,
  formatNotificationTime,
  getPriorityColor,
  getPriorityIcon
} from '@/services/api/notification.service';
import type { SystemNotification, NotificationWebSocketMessage } from '@/services/api/notification.types';

const SystemNotificationBell = () => {
  const [isOpen, setIsOpen] = useState(false);
  const [notifications, setNotifications] = useState<SystemNotification[]>([]);
  const dropdownRef = useRef<HTMLDivElement>(null);
  const navigate = useNavigate();
  const queryClient = useQueryClient();
  const { keycloak } = useKeycloak();

  // Fetch unread count
  const { data: unreadCountData } = useUnreadCountQuery();
  const unreadCount = unreadCountData?.count || 0;

  // Fetch recent notifications
  const { data: notificationsData } = useRecentNotificationsQuery();

  // Mutations
  const markAsReadMutation = useMarkNotificationAsReadMutation();
  const markAllAsReadMutation = useMarkAllNotificationsAsReadMutation();

  useEffect(() => {
    if (notificationsData) {
      setNotifications(notificationsData);
    }
  }, [notificationsData]);

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setIsOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  // WebSocket connection for real-time notifications
  useEffect(() => {
    // Get JWT token from Keycloak
    if (!keycloak.authenticated || !keycloak.token) return;

    const wsUrl = getNotificationWebSocketUrl(keycloak.token!);
    const ws = new WebSocket(wsUrl);

    ws.onopen = () => {
      console.log('System notification WebSocket connection established');
    };

    ws.onerror = (error) => {
      console.error('System notification WebSocket error:', error);
    };

    ws.onclose = (event) => {
      console.log('System notification WebSocket connection closed:', event.code, event.reason);
    };

    ws.onmessage = (event) => {
      try {
        const data: NotificationWebSocketMessage = JSON.parse(event.data);
        
        switch(data.type) {
          case 'notification':
            if (data.notification) {
              // Add new notification to the list
              setNotifications(prev => [data.notification!, ...prev.slice(0, 9)]); // Keep only 10 most recent
              // Refetch unread count
              queryClient.invalidateQueries({ queryKey: [NOTIFICATION_KEYS.GET_UNREAD_COUNT] });
            }
            break;
            
          case 'notification_update':
            if (data.notification) {
              // Update existing notification
              setNotifications(prev => 
                prev.map(n => n.uuid === data.notification!.uuid ? data.notification! : n)
              );
            }
            break;
            
          case 'unread_count_update':
          case 'unread_count':
            if (data.count !== undefined) {
              // Update unread count
              queryClient.setQueryData([NOTIFICATION_KEYS.GET_UNREAD_COUNT], { count: data.count });
            }
            break;
        }
      } catch (error) {
        console.error('Error parsing system notification WebSocket message:', error);
      }
    };

    return () => {
      if (ws.readyState === WebSocket.OPEN) {
        ws.close();
      }
    };
  }, [queryClient, keycloak.authenticated, keycloak.token]);

  const handleNotificationClick = async (notification: SystemNotification) => {
    // Mark as read
    try {
      await markAsReadMutation.mutateAsync(notification.uuid);
      
      // Update local state
      setNotifications(prev => 
        prev.map(n => n.uuid === notification.uuid ? { ...n, read_at: new Date().toISOString() } : n)
      );

      // Handle navigation based on notification type and data
      if (notification.data?.url) {
        navigate(notification.data.url);
      } else {
        // Default navigation or show notification details
        console.log('Notification clicked:', notification);
      }
    } catch (error) {
      console.error('Error marking notification as read:', error);
    }

    // Close dropdown
    setIsOpen(false);
  };

  const handleMarkAllAsRead = async () => {
    try {
      await markAllAsReadMutation.mutateAsync();
      
      // Update local state
      setNotifications(prev => 
        prev.map(n => ({ ...n, read_at: new Date().toISOString() }))
      );
    } catch (error) {
      console.error('Error marking all notifications as read:', error);
    }
  };

  return (
    <div className="system-notification-bell" ref={dropdownRef}>
      <div
        className="menu-icon-wrapper"
        onClick={() => setIsOpen(!isOpen)}
        tabIndex={0}
        role="button"
        aria-label="System notifications"
        onKeyDown={(e) => {
          if (e.key === 'Enter' || e.key === ' ') setIsOpen(!isOpen);
        }}

      >
        <Bell size={24} />
        {unreadCount > 0 && (
          <span className="notification-badge">{unreadCount}</span>
        )}
      </div>

      {isOpen && (
        <div className="notification-dropdown">
          <div className="notification-dropdown-header">
            <span>Notifications</span>
            {notifications.length > 0 && (
              <button
                className="mark-all-read"
                onClick={handleMarkAllAsRead}
              >
                Mark all as read
              </button>
            )}
          </div>

          <div className="notification-list">
            {notifications.length === 0 ? (
              <div className="no-notifications">
                No notifications
              </div>
            ) : (
              notifications.map((notification) => (
                <div
                  key={notification.uuid}
                  className={`notification-item ${!notification.read_at ? 'unread' : ''}`}
                  onClick={() => handleNotificationClick(notification)}
                >
                  <div className="notification-content">
                    <div className="notification-header">
                      <div className="notification-title">
                        {getPriorityIcon(notification.priority)} {notification.title}
                      </div>
                      <div
                        className="notification-priority"
                        style={{ backgroundColor: getPriorityColor(notification.priority) }}
                      >
                        {notification.priority}
                      </div>
                    </div>
                    <div className="notification-message">
                      {notification.message}
                    </div>
                    <div className="notification-meta">
                      <span className="notification-type">
                        {notification.notification_type.description}
                      </span>
                      <span className="notification-time">
                        {formatNotificationTime(notification.created_at)}
                      </span>
                    </div>
                  </div>
                </div>
              ))
            )}
          </div>
        </div>
      )}
    </div>
  );
};

export default SystemNotificationBell;

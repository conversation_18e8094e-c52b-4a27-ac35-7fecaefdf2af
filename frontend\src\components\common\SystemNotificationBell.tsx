import { useState, useEffect, useRef } from 'react';
import { Bell } from 'lucide-react';
import { useNavigate } from 'react-router-dom';
import { useQueryClient } from '@tanstack/react-query';
import { useKeycloak } from '@react-keycloak/web';
import './SystemNotificationBell.css';
import {
  useRecentNotificationsQuery,
  useUnreadCountQuery,
  useMarkNotificationAsReadMutation,
  useMarkAllNotificationsAsReadMutation,
  NOTIFICATION_KEYS
} from '@/hooks/notification.query';
import {
  getNotificationWebSocketUrl,
  formatNotificationTime,
  getPriorityColor,
  getPriorityIcon
} from '@/services/api/notification.service';
import type { SystemNotification, NotificationWebSocketMessage } from '@/services/api/notification.types';

const SystemNotificationBell = () => {
  const [isOpen, setIsOpen] = useState(false);
  const [notifications, setNotifications] = useState<SystemNotification[]>([]);
  const [connectionStatus, setConnectionStatus] = useState<'connecting' | 'connected' | 'disconnected'>('disconnected');
  const dropdownRef = useRef<HTMLDivElement>(null);
  const webSocketRef = useRef<WebSocket | null>(null);
  const reconnectTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const navigate = useNavigate();
  const queryClient = useQueryClient();
  const { keycloak } = useKeycloak();

  // Fetch unread count
  const { data: unreadCountData } = useUnreadCountQuery();
  const unreadCount = unreadCountData?.count || 0;

  // Fetch recent notifications
  const { data: notificationsData } = useRecentNotificationsQuery();

  // Mutations
  const markAsReadMutation = useMarkNotificationAsReadMutation();
  const markAllAsReadMutation = useMarkAllNotificationsAsReadMutation();

  useEffect(() => {
    if (notificationsData) {
      setNotifications(notificationsData);
    }
  }, [notificationsData]);

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setIsOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  // WebSocket connection for real-time notifications
  useEffect(() => {
    // Get JWT token from Keycloak
    if (!keycloak.authenticated || !keycloak.token) return;

    const connectWebSocket = () => {
      const wsUrl = getNotificationWebSocketUrl(keycloak.token!);
      console.log('🔔 Connecting to system notification WebSocket:', wsUrl);
      setConnectionStatus('connecting');
      const ws = new WebSocket(wsUrl);
      webSocketRef.current = ws;

      ws.onopen = () => {
        console.log('✅ System notification WebSocket connection established');
        setConnectionStatus('connected');
        // Clear any reconnection timeout
        if (reconnectTimeoutRef.current) {
          clearTimeout(reconnectTimeoutRef.current);
          reconnectTimeoutRef.current = null;
        }
      };

      ws.onerror = (error) => {
        console.error('System notification WebSocket error:', error);
        setConnectionStatus('disconnected');
      };

      ws.onclose = (event) => {
        console.log('System notification WebSocket connection closed:', event.code, event.reason);
        setConnectionStatus('disconnected');

        // Implement reconnection logic for unexpected closures
        if (event.code !== 1000 && keycloak.authenticated) { // 1000 = normal closure
          reconnectTimeoutRef.current = setTimeout(() => {
            console.log('Attempting to reconnect system notification WebSocket...');
            connectWebSocket(); // Recursively reconnect
          }, 3000);
        }
      };

      ws.onmessage = (event) => {
        try {
          const data: NotificationWebSocketMessage = JSON.parse(event.data);
          console.log('📨 System notification WebSocket message received:', data);

          switch(data.type) {
            case 'notification':
              if (data.notification) {
                console.log('🔔 New notification received:', data.notification);
                // Add new notification to the list
                setNotifications(prev => [data.notification!, ...prev.slice(0, 9)]); // Keep only 10 most recent
                // Refetch unread count
                queryClient.invalidateQueries({ queryKey: [NOTIFICATION_KEYS.GET_UNREAD_COUNT] });
              }
              break;

            case 'notification_update':
              if (data.notification) {
                console.log('🔄 Notification updated:', data.notification);
                // Update existing notification
                setNotifications(prev =>
                  prev.map(n => n.uuid === data.notification!.uuid ? data.notification! : n)
                );
              }
              break;

            case 'unread_count_update':
            case 'unread_count':
              if (data.count !== undefined) {
                console.log('📊 Unread count updated:', data.count);
                // Update unread count
                queryClient.setQueryData([NOTIFICATION_KEYS.GET_UNREAD_COUNT], { count: data.count });
              }
              break;
          }
        } catch (error) {
          console.error('❌ Error parsing system notification WebSocket message:', error);
        }
      };
    };

    // Initial connection
    connectWebSocket();

    return () => {
      if (webSocketRef.current) {
        webSocketRef.current.close(1000); // Normal closure
        webSocketRef.current = null;
      }
      if (reconnectTimeoutRef.current) {
        clearTimeout(reconnectTimeoutRef.current);
        reconnectTimeoutRef.current = null;
      }
    };
  }, [queryClient, keycloak.authenticated, keycloak.token]);

  const handleNotificationClick = async (notification: SystemNotification) => {
    // Mark as read via WebSocket if connected, otherwise use HTTP
    if (webSocketRef.current && webSocketRef.current.readyState === WebSocket.OPEN) {
      try {
        webSocketRef.current.send(JSON.stringify({
          type: 'mark_read',
          notification_uuid: notification.uuid
        }));

        // Update local state immediately for better UX
        setNotifications(prev =>
          prev.map(n => n.uuid === notification.uuid ? { ...n, read_at: new Date().toISOString() } : n)
        );
      } catch (error) {
        console.error('Error sending WebSocket mark_read message:', error);
        // Fallback to HTTP
        await markAsReadMutation.mutateAsync(notification.uuid);
      }
    } else {
      // Fallback to HTTP if WebSocket is not connected
      try {
        await markAsReadMutation.mutateAsync(notification.uuid);

        // Update local state
        setNotifications(prev =>
          prev.map(n => n.uuid === notification.uuid ? { ...n, read_at: new Date().toISOString() } : n)
        );
      } catch (error) {
        console.error('Error marking notification as read:', error);
      }
    }

    // Handle navigation based on notification type and data
    if (notification.data?.url) {
      navigate(notification.data.url);
    } else {
      // Default navigation or show notification details
      console.log('Notification clicked:', notification);
    }

    // Close dropdown
    setIsOpen(false);
  };

  const handleMarkAllAsRead = async () => {
    // Mark all as read via WebSocket if connected, otherwise use HTTP
    if (webSocketRef.current && webSocketRef.current.readyState === WebSocket.OPEN) {
      try {
        webSocketRef.current.send(JSON.stringify({
          type: 'mark_all_read'
        }));

        // Update local state immediately for better UX
        setNotifications(prev =>
          prev.map(n => ({ ...n, read_at: new Date().toISOString() }))
        );
      } catch (error) {
        console.error('Error sending WebSocket mark_all_read message:', error);
        // Fallback to HTTP
        await markAllAsReadMutation.mutateAsync();
      }
    } else {
      // Fallback to HTTP if WebSocket is not connected
      try {
        await markAllAsReadMutation.mutateAsync();

        // Update local state
        setNotifications(prev =>
          prev.map(n => ({ ...n, read_at: new Date().toISOString() }))
        );
      } catch (error) {
        console.error('Error marking all notifications as read:', error);
      }
    }
  };

  return (
    <div className="system-notification-bell" ref={dropdownRef}>
      <div
        className="menu-icon-wrapper"
        onClick={() => setIsOpen(!isOpen)}
        tabIndex={0}
        role="button"
        aria-label="System notifications"
        onKeyDown={(e) => {
          if (e.key === 'Enter' || e.key === ' ') setIsOpen(!isOpen);
        }}

      >
        <Bell
          size={24}
          style={{
            color: connectionStatus === 'connected' ? '#37B7C3' :
                   connectionStatus === 'connecting' ? '#FF9500' : '#8E8E93'
          }}
        />
        {unreadCount > 0 && (
          <span className="notification-badge">{unreadCount}</span>
        )}
      </div>

      {isOpen && (
        <div className="notification-dropdown">
          <div className="notification-dropdown-header">
            <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
              <span>Notifications</span>
              <div
                style={{
                  width: '8px',
                  height: '8px',
                  borderRadius: '50%',
                  backgroundColor: connectionStatus === 'connected' ? '#00FF84' :
                                 connectionStatus === 'connecting' ? '#FF9500' : '#ff6b6b'
                }}
                title={`WebSocket ${connectionStatus}`}
              />
            </div>
            {notifications.length > 0 && (
              <button
                className="mark-all-read"
                onClick={handleMarkAllAsRead}
              >
                Mark all as read
              </button>
            )}
          </div>

          <div className="notification-list">
            {notifications.length === 0 ? (
              <div className="no-notifications">
                No notifications
              </div>
            ) : (
              notifications.map((notification) => (
                <div
                  key={notification.uuid}
                  className={`notification-item ${!notification.read_at ? 'unread' : ''}`}
                  onClick={() => handleNotificationClick(notification)}
                >
                  <div className="notification-content">
                    <div className="notification-header">
                      <div className="notification-title">
                        {getPriorityIcon(notification.priority)} {notification.title}
                      </div>
                      <div
                        className="notification-priority"
                        style={{ backgroundColor: getPriorityColor(notification.priority) }}
                      >
                        {notification.priority}
                      </div>
                    </div>
                    <div className="notification-message">
                      {notification.message}
                    </div>
                    <div className="notification-meta">
                      <span className="notification-type">
                        {notification.notification_type.description}
                      </span>
                      <span className="notification-time">
                        {formatNotificationTime(notification.created_at)}
                      </span>
                    </div>
                  </div>
                </div>
              ))
            )}
          </div>
        </div>
      )}
    </div>
  );
};

export default SystemNotificationBell;


import { useState } from "react";
import { Plus, Users, FileText, Search, Filter, Building2, UserCheck } from "lucide-react";
import Preloader from "@/components/common/Preloader";
import Wrapper from "@/components/common/Wrapper";
import DataTable from "@/components/common/DataTable";
import NurtifyButton from "@/components/NurtifyButton";
import { useStudiesBySponsorQuery, useCreateStudyInvitationMutation } from "@/hooks/study.query";
import { useCurrentUserQuery } from "@/hooks/user.query";
import InviteDepartmentModal from "@/components/modal/InviteDepartmentModal";
import PIAssignmentsList from "./PIAssignmentsList";
import DepartmentDelegationLogs from "./DepartmentDelegationLogs";
import "./sponsor-studies-invitation.css";


// Define types
interface Study {
  uuid: string;
  name: string;
  description?: string;
  full_title?: string;
  team_email?: string;
  leading_team?: string;
  created_by?: {
    first_name?: string;
    last_name?: string;
  };
  iras: string;
  created_at: string;
  updated_at: string;
}

export default function SponsorStudiesInvitationSection() {
  const { data: currentUser } = useCurrentUserQuery();
  const { data: studies, isLoading: isLoadingStudies } = useStudiesBySponsorQuery(currentUser?.identifier || "");
  const [isInviteModalOpen, setIsInviteModalOpen] = useState(false);
  const [selectedStudy, setSelectedStudy] = useState<Study | null>(null);
  const [showPIAssignments, setShowPIAssignments] = useState(false);
  const [selectedStudyForPI, setSelectedStudyForPI] = useState<Study | null>(null);
  const [activeTab, setActiveTab] = useState<'studies' | 'delegation-logs'>('studies');
  const [searchTerm, setSearchTerm] = useState("");
  const [filterStatus, setFilterStatus] = useState("all");
  const createStudyInvitationMutation = useCreateStudyInvitationMutation();

  const handleInviteDepartments = (study: Study) => {
    setSelectedStudy(study);
    setIsInviteModalOpen(true);
  };

  const handleCloseInviteModal = () => {
    setIsInviteModalOpen(false);
    setSelectedStudy(null);
  };

  const handleSaveInvite = async (selectedDepartments: string[]) => {
    if (!selectedStudy) return;

    try {
      await createStudyInvitationMutation.mutateAsync({
        study: selectedStudy.uuid,
        departments: selectedDepartments
      });
      handleCloseInviteModal();
    } catch (error) {
      console.error("Error sending invitations:", error);
      // You might want to show an error message to the user here
    }
  };

  const handleViewPIAssignments = (study: Study) => {
    setSelectedStudyForPI(study);
    setShowPIAssignments(true);
  };

  const handleClosePIAssignments = () => {
    setShowPIAssignments(false);
    setSelectedStudyForPI(null);
  };

  // Define columns for the DataTable
  const columns = [
    {
      key: "name" as keyof Study,
      header: "Study Name",
      sortable: true,
    },
    {
      key: "description" as keyof Study,
      header: "Description",
      sortable: true,
      render: (value: any) => {
        const description = value as string | undefined;
        return description
          ? description.length > 100
            ? `${description.substring(0, 100)}...`
            : description
          : "No description";
      },
    },
    {
      key: "full_title" as keyof Study,
      header: "Full Title",
      sortable: true,
      render: (value: any) => {
        const fullTitle = value as string | undefined;
        return fullTitle || "No full title";
      },
    },
    {
      key: "iras" as keyof Study,
      header: "IRAS",
      sortable: true,
      render: (value: any) => {
        const iras = value as string | undefined;
        return iras || "No IRAS";
      },
    },
    {
      key: "leading_team" as keyof Study,
      header: "Leading Team",
      sortable: true,
      render: (value: any) => {
        const leadingTeam = value as string | undefined;
        return leadingTeam || "No leading team";
      },
    },
  ];

  // Filter studies based on search term
  const filteredStudies = studies?.filter((study: Study) => {
    const matchesSearch = 
      study.name?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      study.description?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      study.full_title?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      study.iras?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      study.leading_team?.toLowerCase().includes(searchTerm.toLowerCase());
    
    return matchesSearch;
  }) || [];

  // Define actions for the DataTable
  const actions = [
    {
      icon: <Plus size={16} />,
      tooltipText: "Invite Departments",
      onClick: (row: Study) => handleInviteDepartments(row),
    },
    {
      icon: <Users size={16} />,
      tooltipText: "View PI Assignments",
      onClick: (row: Study) => handleViewPIAssignments(row),
    },
  ];

  return (
    <Wrapper>
      <Preloader />
      <div style={{ paddingBottom: "88px" }}>
        <div className="ssi-container">
          <div>
            <div>
              <div>
                {/* Enhanced Header */}
                <div className="ssi-header">
                  <div className="ssi-header-content">
                    <div className="ssi-header-left">
                      <h1>
                        <Building2 size={28} />
                        Study Invitations Management
                      </h1>
                      <p>
                        Manage study invitations and track department applications across your research portfolio
                      </p>
                    </div>
                    <div className="ssi-header-stats">
                      <div className="ssi-stat-card">
                        <div className="ssi-stat-icon">
                          <FileText size={20} />
                        </div>
                        <div className="ssi-stat-number">{studies?.length || 0}</div>
                        <div className="ssi-stat-label">Active Studies</div>
                      </div>
                      <div className="ssi-stat-card">
                        <div className="ssi-stat-icon">
                          <UserCheck size={20} />
                        </div>
                        <div className="ssi-stat-number">{filteredStudies.length}</div>
                        <div className="ssi-stat-label">Available for Invitation</div>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Enhanced Tab Navigation */}
                <div className="ssi-tabs">
                  <div className="ssi-tabs-nav">
                    <button
                      className={`ssi-tab-button ${activeTab === 'studies' ? 'active' : ''}`}
                      onClick={() => setActiveTab('studies')}
                    >
                      <div className="ssi-tab-icon">
                        <Plus size={18} />
                      </div>
                      <div className="ssi-tab-content">
                        <h3>Study Management</h3>
                        <p>Invite departments to studies</p>
                      </div>
                    </button>
                    <button
                      className={`ssi-tab-button ${activeTab === 'delegation-logs' ? 'active' : ''}`}
                      onClick={() => setActiveTab('delegation-logs')}
                    >
                      <div className="ssi-tab-icon">
                        <FileText size={18} />
                      </div>
                      <div className="ssi-tab-content">
                        <h3>Applications Review</h3>
                        <p>Review department applications</p>
                      </div>
                    </button>
                  </div>
                </div>

                {/* Tab Content */}
                {activeTab === 'studies' && (
                  <div>
                    {/* Search and Filter Controls */}
                    <div className="ssi-controls">
                      <div className="ssi-controls-header">
                        <h3>Study Portfolio</h3>
                        <p>Search and filter your studies to manage invitations</p>
                      </div>
                      <div className="ssi-controls-row">
                        <div className="ssi-search-group">
                          <div className="ssi-search-wrapper">
                            <Search size={18} className="ssi-search-icon" />
                            <input
                              type="text"
                              className="ssi-search-input"
                              placeholder="Search studies by name, description, IRAS, or team..."
                              value={searchTerm}
                              onChange={(e) => setSearchTerm(e.target.value)}
                            />
                            {searchTerm && (
                              <button
                                className="ssi-search-clear"
                                onClick={() => setSearchTerm("")}
                                title="Clear search"
                              >
                                ×
                              </button>
                            )}
                          </div>
                        </div>
                        <div className="ssi-filter-group">
                          <div className="ssi-filter-wrapper">
                            <Filter size={16} />
                            <select
                              className="ssi-filter-select"
                              value={filterStatus}
                              onChange={(e) => setFilterStatus(e.target.value)}
                            >
                              <option value="all">All Studies</option>
                              <option value="active">Active Studies</option>
                              <option value="recent">Recently Created</option>
                            </select>
                          </div>
                        </div>
                      </div>
                    </div>

                    {/* Studies Table */}
                    <div className="ssi-card">
                      {isLoadingStudies ? (
                        <div className="ssi-loading">
                          <div className="ssi-loading-spinner"></div>
                          <div className="ssi-loading-text">Loading Studies</div>
                          <div className="ssi-loading-subtext">Please wait while we fetch your study portfolio...</div>
                        </div>
                      ) : filteredStudies && filteredStudies.length > 0 ? (
                        <DataTable
                          data={filteredStudies}
                          columns={columns}
                          actions={actions}
                          defaultItemsPerPage={10}
                          itemsPerPageOptions={[5, 10, 25, 50]}
                          noDataMessage="No studies match your search criteria"
                        />
                      ) : (
                        <div className="ssi-empty-state">
                          <div className="ssi-empty-icon">
                            <FileText size={48} />
                          </div>
                          <h3 className="ssi-empty-title">No Studies Found</h3>
                          <p className="ssi-empty-description">
                            {searchTerm 
                              ? "No studies match your search criteria. Try adjusting your search terms."
                              : "No studies are currently available for invitation. Studies will appear here once they are created and approved."
                            }
                          </p>
                          {searchTerm && (
                            <NurtifyButton
                              variant="primary"
                              size="regular"
                              onClick={() => setSearchTerm("")}
                            >
                              Clear Search
                            </NurtifyButton>
                          )}
                        </div>
                      )}
                    </div>
                  </div>
                )}

                {activeTab === 'delegation-logs' && (
                  <div>
                    <DepartmentDelegationLogs />
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>
      </div>

      {selectedStudy && (
        <InviteDepartmentModal
          isOpen={isInviteModalOpen}
          onClose={handleCloseInviteModal}
          onSave={handleSaveInvite}
          studyName={selectedStudy.name}
          studyUuid={selectedStudy.uuid}
        />
      )}

      {showPIAssignments && selectedStudyForPI && (
        <div className="ssi-modal-overlay">
          <div className="ssi-modal">
            <div className="ssi-modal-header">
              <h2 className="ssi-modal-title">PI Assignments - {selectedStudyForPI.name}</h2>
              <button
                className="ssi-modal-close"
                onClick={handleClosePIAssignments}
              >
                ×
              </button>
            </div>
            <div className="ssi-modal-body">
              <PIAssignmentsList
                studyUuid={selectedStudyForPI.uuid}
                studyName={selectedStudyForPI.name}
              />
            </div>
          </div>
        </div>
      )}
    </Wrapper>
  );
}

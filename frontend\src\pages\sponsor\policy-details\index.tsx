import { useEffect } from "react";
import { useParams, useNavigate } from "react-router-dom";
import { ArrowLeft, Calendar, User, Building, FileText, Tag, CheckCircle, AlertCircle } from "lucide-react";
import Preloader from "@/components/common/Preloader";
import PdfViewer from "@components/PdfViewer.tsx";
import { usePolicyQuery } from "@/hooks/policy.query.ts";
import { usePolicyStore } from "@/store/policyState.tsx";
import "./sponsor-policy-details.css";

const SponsorPolicyDetails = () => {
    const { uuid } = useParams();
    const navigate = useNavigate();
    const { data: policy, isLoading, error } = usePolicyQuery(uuid as string);
    const setPolicy = usePolicyStore((state) => state.setPolicy);

    useEffect(() => {
        if (policy) {
            setPolicy(policy);
        }
    }, [policy, setPolicy]);

    if (isLoading) return <Preloader />;
    if (error) return <p>Error loading policy details.</p>;
    if (!policy) return <p>Policy not found.</p>;

    const attachUrl = policy.attach_content ? `${policy.attach_content}` : null;

    // Liste des extensions d'image courantes
    const imageExtensions = [".png", ".jpg", ".jpeg", ".gif", ".bmp"];
    // Vérifie si l'URL est une image ou un PDF
    const isImage = attachUrl && imageExtensions.some(ext => attachUrl.toLowerCase().endsWith(ext));
    const isPdf = attachUrl && attachUrl.toLowerCase().endsWith(".pdf");

    const handleGoBack = () => {
        navigate('/sponsor/policy');
    };

    const getStatusIcon = (status: string) => {
        switch (status?.toLowerCase()) {
            case 'active':
            case 'published':
                return <CheckCircle className="status-icon status-active" size={16} />;
            case 'draft':
            case 'pending':
                return <AlertCircle className="status-icon status-pending" size={16} />;
            default:
                return <FileText className="status-icon status-default" size={16} />;
        }
    };

    return (
        <div className="sponsor-policy-details-container">
            {/* Header with back button and policy info */}
            <div className="policy-header">
                <button className="back-button" onClick={handleGoBack}>
                    <ArrowLeft size={20} />
                    <span>Back to Policies</span>
                </button>

                <div className="policy-header-info">
                    <div className="policy-category">
                        <Tag size={16} />
                        <span>{policy.category}</span>
                    </div>
                    <h1 className="policy-title">{policy.title}</h1>

                    <div className="policy-meta">
                        <div className="meta-item">
                            <Calendar size={16} />
                            <span>Created: {new Date(policy.created_at).toLocaleDateString('en-US', {
                                year: 'numeric',
                                month: 'long',
                                day: 'numeric'
                            })}</span>
                        </div>
                        <div className="meta-item">
                            <User size={16} />
                            <span>Author: {policy.author_name}</span>
                        </div>
                        <div className="meta-item">
                            <Building size={16} />
                            <span>Study: {policy.study.name}</span>
                        </div>
                        <div className="meta-item status-meta">
                            {getStatusIcon(policy.policy_state)}
                            <span>Status: {policy.policy_state}</span>
                        </div>
                    </div>
                </div>
            </div>

            {/* Policy Content */}
            <div className="policy-content">
                {policy.description && (
                    <div className="policy-description">
                        <h3>Description</h3>
                        <p>{policy.description}</p>
                    </div>
                )}

                {/* Document Viewer */}
                <div className="document-section">
                    <h3>Policy Document</h3>
                    <div className="document-viewer">
                        {attachUrl ? (
                            isPdf ? (
                                <PdfViewer fileUrl={attachUrl} />
                            ) : isImage ? (
                                <div className="image-viewer">
                                    <img
                                        src={attachUrl}
                                        alt="Policy attachment"
                                        className="policy-image"
                                    />
                                </div>
                            ) : (
                                <div className="unsupported-format">
                                    <FileText size={48} />
                                    <h4>Unsupported File Format</h4>
                                    <p>This file format is not supported for preview.</p>
                                    <a href={attachUrl} target="_blank" rel="noopener noreferrer" className="download-link">
                                        Download File
                                    </a>
                                </div>
                            )
                        ) : (
                            <div className="no-document">
                                <FileText size={48} />
                                <h4>No Document Attached</h4>
                                <p>This policy doesn't have any attached document.</p>
                            </div>
                        )}
                    </div>
                </div>
            </div>
        </div>
    );
};

export default SponsorPolicyDetails;

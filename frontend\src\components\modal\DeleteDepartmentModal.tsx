import React from 'react';
import "@/components/modal/DeleteDepartmentModal.css";
import {useNavigate} from "react-router-dom";

type DeleteDepartmentModalProps = {
  isOpen: boolean;
  onDelete: () => void;
  onClose: () => void;
};

const DeleteDepartmentModal: React.FC<DeleteDepartmentModalProps> = ({ isOpen, onClose, onDelete }) => {
  const navigate = useNavigate();
  const handleDelete = () => {
    // Add delete logic here
    navigate("/org/dashboard/department",{ replace: true });
    onDelete();
    onClose();
  };

  if (!isOpen) return null;

  return (
    <div className="modal-overlay">
      <div className="modal-content">
        <div className="modal-header">
          <h2>Confirm Department Deletion</h2>
        </div>
        <div className="modal-body text-sm-start">
          <p>Are you sure you want to <strong>delete this Department</strong>?</p>
          <p className="m-y-5">This action is permanent and cannot be undone. All associated data will be lost.</p>
        </div>
        <div className="modal-footer">
          <button className="delete-modal-btn btn-secondary" onClick={onClose}>Cancel</button>
          <button className="delete-modal-btn btn-danger" onClick={handleDelete}>Yes</button>
        </div>
      </div>
    </div>
  );
};

export default DeleteDepartmentModal;

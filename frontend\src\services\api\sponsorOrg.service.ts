import api from "@/services/api.ts";
import { SponsorOrg } from "./types.ts";




export const getAllSponsorOrgs = async () => {
    try {

        const response = await api.get("sponsor-org/sponsor-orgs/");
        return response.data.results;
    } catch (error) {
        console.error("Error fetching Sponsor Orgs:", error);
        throw error;
    }
};


export const getSponsorOrgById = async (uuid: string) => {
    try {
        const response = await api.get(`sponsor-org/sponsor-orgs/${uuid}/`);
        return response.data;        
    } catch (error) {
        console.error("Error fetching Sponsor Org:", error);
        throw error;
    }
    
};

export const createSponsorOrg = async (HospitalData: SponsorOrg) => {
    const { data } = await api.post("sponsor-org/sponsor-orgs/", HospitalData);
    return data;
};

export const updateSponsorOrg = async (uuid: string, hospitalData: Partial<SponsorOrg>) => {
    const { data } = await api.put(`sponsor-org/sponsor-orgs/${uuid}/`, hospitalData);
    return data;
};

export const deleteSponsorOrg = async (uuid: string) => {
    try {
        await api.delete(`sponsor-org/sponsor-orgs/${uuid}/`)
        
    } catch (error) {
        console.error("Error fetching Sponsor Org:", error);
        throw error;
    }
    
}

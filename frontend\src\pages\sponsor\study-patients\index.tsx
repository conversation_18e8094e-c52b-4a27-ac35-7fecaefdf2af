import React, { useState, useMemo } from "react";
import { useNavigate } from "react-router-dom";
import { useSponsorPatientsQuery } from "@/hooks/sponsorPatients.query";
import { useHospitalsQuery } from "@/hooks/hospital.query";
import { useDepartmentsQuery } from "@/hooks/department.query";
import { Search, X, Eye } from "lucide-react";
import DataTable from "@/components/common/DataTable";
import { SponsorPatient } from "@/services/api/types";
import "./study-patients.css";

// Extended Patient interface to include uuid for DataTable
interface PatientWithUuid extends SponsorPatient {
  uuid: string; // For DataTable compatibility
}

const StudyPatients: React.FC = () => {
  const navigate = useNavigate();
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedHospital, setSelectedHospital] = useState("");
  const [selectedDepartment, setSelectedDepartment] = useState("");

  // Fetch data
  const { data: patientsResponse, isLoading: isLoadingPatients } = useSponsorPatientsQuery();
  const { data: hospitals } = useHospitalsQuery();
  const { data: departments } = useDepartmentsQuery();

  // Extract patients array from response and add uuid for DataTable
  const patients = useMemo(() => {
    if (!patientsResponse?.results) return [];
    
    return patientsResponse.results.map((patient: SponsorPatient) => ({
      ...patient,
      uuid: patient.patient_uuid, // Use the actual patient_uuid for DataTable compatibility
    })) as PatientWithUuid[];
  }, [patientsResponse]);

  const handleSearch = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchTerm(e.target.value);
  };

  const handleHospitalFilter = (e: React.ChangeEvent<HTMLSelectElement>) => {
    setSelectedHospital(e.target.value);
  };

  const handleDepartmentFilter = (e: React.ChangeEvent<HTMLSelectElement>) => {
    setSelectedDepartment(e.target.value);
  };

  const handleViewPatient = (patient: PatientWithUuid) => {
    navigate(`/sponsor/study-patients/${patient.patient_uuid}`);
  };

  // Filter patients based on search and filters
  const filteredPatients = useMemo(() => {
    if (!patients) return [];

    return patients.filter((patient: PatientWithUuid) => {
      const matchesSearch = searchTerm === "" || 
        (patient.patient_initials?.toLowerCase() || "").includes(searchTerm.toLowerCase()) ||
        (patient.patient_study_id?.toLowerCase() || "").includes(searchTerm.toLowerCase()) ||
        (patient.nhs_number?.toLowerCase() || "").includes(searchTerm.toLowerCase()) ||
        (patient.site_name?.toLowerCase() || "").includes(searchTerm.toLowerCase()) ||
        (patient.hospital_name?.toLowerCase() || "").includes(searchTerm.toLowerCase()) ||
        (patient.study_name?.toLowerCase() || "").includes(searchTerm.toLowerCase());

      const matchesHospital = selectedHospital === "" || patient.hospital_name === selectedHospital;
      const matchesDepartment = selectedDepartment === "" || patient.site_name === selectedDepartment;

      return matchesSearch && matchesHospital && matchesDepartment;
    });
  }, [patients, searchTerm, selectedHospital, selectedDepartment]);

  // Prepare data for DataTable
  const tableData = useMemo(() => {
    return filteredPatients.map((patient: PatientWithUuid) => ({
      ...patient,
      uuid: patient.patient_uuid, // Ensure DataTable has a uuid field
    }));
  }, [filteredPatients]);

  const columns = [
    {
      key: "patient_initials" as keyof PatientWithUuid,
      header: "Patient Initials",
      sortable: true,
    },
    {
      key: "year_of_birth" as keyof PatientWithUuid,
      header: "Year of Birth",
      sortable: true,
      render: (value: any) => String(value),
    },
    {
      key: "site_name" as keyof PatientWithUuid,
      header: "Site",
      sortable: true,
    },
    {
      key: "hospital_name" as keyof PatientWithUuid,
      header: "Hospital",
      sortable: true,
    },
    {
      key: "study_name" as keyof PatientWithUuid,
      header: "Study",
      sortable: true,
    },
    {
      key: "enrollment_status" as keyof PatientWithUuid,
      header: "Enrollment Status",
      sortable: true,
    },
    {
      key: "enrollment_date" as keyof PatientWithUuid,
      header: "Enrollment Date",
      sortable: true,
      render: (value: any) => new Date(value).toLocaleDateString(),
    },
    {
      key: "actions" as keyof PatientWithUuid,
      header: "Actions",
      sortable: false,
      render: (_: any, row: PatientWithUuid) => (
        <button
          className="view-patient-btn"
          onClick={() => handleViewPatient(row)}
          title="View Patient Details"
        >
          <Eye size={16} />
          View Patient
        </button>
      ),
    },
  ] as any;

  if (isLoadingPatients) {
    return (
      <div className="study-patients-page">
        <div className="loading">Loading patients...</div>
      </div>
    );
  }

  return (
    <div className="study-patients-page">
      <div className="page-header">
        <h1>Study Patients</h1>
        <p>View and manage patients enrolled in your studies</p>
      </div>

      {/* Filters */}
      <div className="filters-section">
        <div className="search-filter">
          <div className="search-input-wrapper">
            <Search size={20} className="search-icon" />
            <input
              type="text"
              placeholder="Search patients..."
              value={searchTerm}
              onChange={handleSearch}
              className="search-input"
            />
            {searchTerm && (
              <button
                className="clear-search"
                onClick={() => setSearchTerm("")}
                title="Clear search"
              >
                <X size={16} />
              </button>
            )}
          </div>
        </div>

        <div className="filter-controls">
          <div className="filter-group">
            <label htmlFor="hospital-filter">Hospital:</label>
            <select
              id="hospital-filter"
              value={selectedHospital}
              onChange={handleHospitalFilter}
              className="filter-select"
            >
              <option value="">All Hospitals</option>
              {hospitals?.map((hospital: any) => (
                <option key={hospital.uuid} value={hospital.name}>
                  {hospital.name}
                </option>
              ))}
            </select>
          </div>

          <div className="filter-group">
            <label htmlFor="department-filter">Department:</label>
            <select
              id="department-filter"
              value={selectedDepartment}
              onChange={handleDepartmentFilter}
              className="filter-select"
            >
              <option value="">All Departments</option>
              {departments?.map((department: any) => (
                <option key={department.uuid} value={department.name}>
                  {department.name}
                </option>
              ))}
            </select>
          </div>
        </div>
      </div>

      {/* Results Summary */}
      <div className="results-summary">
        <span>
          Showing {filteredPatients.length} of {patients.length} patients
        </span>
      </div>

      {/* Data Table */}
      <div className="table-container">
        <DataTable
          data={tableData}
          columns={columns}
        />
      </div>
    </div>
  );
};

export default StudyPatients; 
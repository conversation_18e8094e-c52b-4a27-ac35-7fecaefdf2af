/* Studies Page Styles */

.studies-container {
  background-color: #fff;
  border-radius: 0; /* Changed */
  box-shadow: none; /* Changed */
  padding: 15px 24px 24px 24px; /* Changed top padding */
  margin-bottom: 30px;
}

.studies-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
  border-bottom: 1px solid #e5e7eb;
  padding-bottom: 16px;
}

.studies-header h1 {
  font-size: 24px;
  font-weight: 600;
  color: #1f2937;
  margin: 0;
}

.create-study-btn {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 10px 16px;
  border-radius: 0; /* Changed */
  font-size: 14px;
  font-weight: 500;
  background-color: #36b6c2;
  color: white;
  border: 1px solid #2da1ac;
  cursor: pointer;
  transition: all 0.2s ease;
}

.create-study-btn:hover {
  background-color: #2da1ac;
}

/* Search and Filter Styles */
.studies-controls {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
  margin-bottom: 20px;
}

.search-filter-container {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
  flex: 1;
}

.policy-search-container {
  flex: 1;
  min-width: 250px;
}

.policy-search-box {
  position: relative;
  width: 100%;
}

.search-icon {
  position: absolute;
  left: 10px;
  top: 50%;
  transform: translateY(-50%);
  color: #666;
}

.policy-search-input {
  width: 100%;
  padding: 12px 40px 12px 40px;
  border: 1px solid #ddd;
  border-radius: 0; /* Changed */
  font-size: 14px;
  box-shadow: none; /* Changed */
}

.clear-search {
  position: absolute;
  right: 10px;
  top: 50%;
  transform: translateY(-50%);
  background: none;
  border: none;
  color: #666;
  cursor: pointer;
  padding: 0;
  display: flex;
  align-items: center;
  justify-content: center;
}

.clear-search:hover {
  color: #333;
}

.filter-container {
  min-width: 200px;
  flex: 0 0 auto;
}

.filter-select {
  width: 100%;
  padding: 10px 12px;
  border: 1px solid #d1d5db;
  border-radius: 0; /* Changed */
  font-size: 14px;
  background-color: white;
}

/* View Toggle Styles */
.view-toggle-container {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-left: auto;
}

.view-toggle-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 2px 5px;
  border: 1px solid #d1d5db;
  background-color: #f9fafb;
  color: #4b5563;
  border-radius: 0; /* Changed */
  cursor: pointer;
  transition: all 0.2s ease;
}

.view-toggle-btn.active {
  background-color: #37B7C3;
  color: white;
  border-color: #37B7C3;
}

.view-toggle-btn:hover:not(.active) {
  background-color: #e5e7eb;
}

/* Table Styles */
.policy-table-container {
  overflow-x: auto;
  background-color: white;
  border-radius: 0; /* Changed */
  box-shadow: none; /* Changed */
  margin-top: 20px;
}

.policy-table {
  width: 100%;
  border-collapse: collapse;
}

.policy-table th, .policy-table td {
  padding: 15px;
  text-align: left;
  border-bottom: 1px solid #eee;
}

.policy-table th {
  background-color: #37B7C3;
  color: #fff;
  font-weight: 600;
  position: sticky;
  top: 0;
}

.policy-table tr:hover {
  background-color: #f5f9fa;
}


/* Card View Styles */
.studies-card-view {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 20px;
  margin-top: 20px;
}

.study-card {
  background-color: white;
  border-radius: 0; /* Changed */
  box-shadow: none; /* Changed */
  padding: 20px;
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.study-card:hover {
  transform: none; /* Changed */
  box-shadow: none; /* Changed */
}

.study-card-header {
  margin-bottom: 12px;
}

.study-card-title {
  font-size: 18px;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 8px;
}

.study-card-description {
  color: #4b5563;
  font-size: 14px;
  margin-bottom: 16px;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.study-card-meta {
  display: flex;
  flex-direction: column;
  gap: 8px;
  margin-bottom: 16px;
}

.study-card-meta-item {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
  color: #4b5563;
}

.study-card-actions {
  display: flex;
  gap: 8px;
  justify-content: flex-end;
  margin-top: 12px;
}

/* Button Styles */
.action-button {
  background-color: #37B7C3;
  border: none;
  border-radius: 0; /* Changed */
  color: white;
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s ease;
}

.action-button:hover {
  background-color: #2da8b4;
  transform: none; /* Changed */
}

.edit-btn {
  background-color: #f59e0b;
  color: white;
  border: none;
  border-radius: 0; /* Changed */
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s ease;
}

.edit-btn:hover {
  background-color: #d97706;
  transform: none; /* Changed */
}

.delete-btn {
  background-color: #ef4444;
  color: white;
  border: none;
  border-radius: 0; /* Changed */
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s ease;
}

.delete-btn:hover {
  background-color: #dc2626;
  transform: none; /* Changed */
}

/* Status Indicators */
.study-patients,
.study-visits,
.study-created-by {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
  color: #4b5563;
}

/* Empty and Loading States */
.no-studies,
.loading-studies {
  padding: 40px;
  text-align: center;
  color: #6b7280;
  background-color: #f9fafb;
  border: 1px dashed #e5e7eb;
  border-radius: 0; /* Changed */
  font-size: 16px;
}

.loading-studies::after {
  content: "...";
  animation: loading-dots 1.5s infinite;
}

@keyframes loading-dots {
  0%, 20% { content: "."; }
  40% { content: ".."; }
  60%, 100% { content: "..."; }
}

/* Studies Chart Styles */
.studies-chart-container {
  background-color: white;
  border-radius: 0; /* Changed */
  box-shadow: none; /* Changed */
  padding: 20px;
  margin-top: 30px;
}

.studies-chart-container h3 {
  font-size: 18px;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 20px;
  padding-bottom: 10px;
  border-bottom: 1px solid #e5e7eb;
}

.chart-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 300px;
}

/* Responsive Styles */
@media (max-width: 992px) {
  .studies-card-view {
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
  }
}

@media (max-width: 768px) {
  .studies-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 16px;
  }
  
  .studies-header h1 {
    margin-bottom: 16px;
  }
  
  .create-study-btn {
    width: 100%;
    justify-content: center;
  }
  
  .studies-controls {
    flex-direction: column;
  }
  
  .search-filter-container {
    flex-direction: column;
    width: 100%;
  }
  
  .policy-search-container,
  .filter-container {
    width: 100%;
  }
  
  .view-toggle-container {
    margin-left: 0;
    margin-top: 16px;
  }
  
  .policy-table th, 
  .policy-table td {
    padding: 12px 8px;
    font-size: 13px;
    background-color: red;
  }
  
  
  .studies-card-view {
    grid-template-columns: 1fr;
  }
  
  .studies-chart-container {
    padding: 16px;
  }
  
  .chart-container {
    height: 250px;
  }
}

@media (max-width: 480px) {
  .studies-container {
    padding: 16px;
  }
  
  .policy-table th:nth-child(2),
  .policy-table td:nth-child(2) {
    display: none;
  }
  
  .studies-chart-container h3 {
    font-size: 16px;
    margin-bottom: 12px;
  }
  
  .chart-container {
    height: 200px;
  }
}

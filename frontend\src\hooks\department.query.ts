import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { getAllDepartments, getDepartmentById, createDepartment ,updateDepartment ,deleteDepartment, getDepartmentsByHospital} from "@/services/api/department.service";
import { Department_KEYS } from './keys';
import { Department } from "@/services/api/types";

export const useDepartmentsQuery = (params?: string) => {
    return useQuery<Department[], Error>({
        queryKey: [Department_KEYS.GET_ALL],
        queryFn: () => getAllDepartments(params),
    });
};

export const useDepartmentsByHospitalQuery = (hospitalUuid: string, isAdmin = true) => {
    return useQuery<Department[], Error>({
        queryKey: [Department_KEYS.GET_BY_HOSPITAL, hospitalUuid],
        queryFn: () => getDepartmentsByHospital(hospitalUuid),
        enabled: !!hospitalUuid && !isAdmin,
    });
};

export const useDepartmentQuery = (id: string) => {
    return useQuery<Department, Error>({
        queryKey: [Department_KEYS.GET_BY_ID, id],
        queryFn: () => getDepartmentById(id),
        enabled: !!id,
    });
};

export const useCreateDepartmentMutation = () => {
    const queryClient = useQueryClient();

    return useMutation({
        mutationFn: createDepartment,
        onSuccess: () => {
            // Invalidate and refetch department list
            queryClient.invalidateQueries({ queryKey: [Department_KEYS.CREATE] });
        },
    });
};


export const useUpdateDepartmentMutation = () => {
    const queryClient = useQueryClient();

    return useMutation({
        mutationFn: ({ uuid, data }: { uuid: string; data: Partial<Department> }) => 
            updateDepartment(uuid, data),
        onSuccess: (_, { uuid }) => {
            // Invalidate hospital list and the updated hospital details
            queryClient.invalidateQueries({ queryKey: [Department_KEYS.GET_ALL] });
            queryClient.invalidateQueries({ queryKey: [Department_KEYS.GET_BY_ID, uuid] });
        },
    });
};

export const useDeleteDepartmentMutation = () => {
    const queryClient = useQueryClient();

    return useMutation({
        mutationFn: ({ uuid}: { uuid: string }) => 
            deleteDepartment(uuid),
        onSuccess: () => {
            // Invalidate hospital list and the updated hospital details
            queryClient.invalidateQueries({ queryKey: [Department_KEYS.GET_ALL] });
            
        },
    });

};
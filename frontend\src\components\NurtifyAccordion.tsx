import { useState } from "react";
import { ChevronDown, ChevronUp } from "lucide-react";

interface AccordionItem {
  id: number | string;
  question: string;
  answer: string;
}

interface NurtifyAccordionProps {
  items: AccordionItem[];
  className?: string; // Optional className for custom styling
}

export default function NurtifyAccordion({ items, className = "" }: NurtifyAccordionProps): JSX.Element {
  const [activeItem, setActiveItem] = useState<number | string | null>(null);

  // Function to properly render text with line breaks and formatting
  const formatAnswer = (text: string) => {
    // Split by newlines and map each line
    return text.split('\n').map((line, index) => {
      // Check if line starts with a list marker (e.g., 1. **)
      if (line.match(/^\d+\.\s\*\*(.*?)\*\*/)) {
        const content = line.replace(/^\d+\.\s\*\*(.*?)\*\*/, '$1');
        return (
          <div key={index} className="faq-list-item font-semibold">
            {index + 1}. {content}
          </div>
        );
      }
      // Check if line is a numbered list item (e.g., 1. )
      else if (line.match(/^\d+\.\s/)) {
         return <div key={index} className="faq-list-item ml-4">{line}</div>;
      }
      // Check if line is a header (starts with **)
      else if (line.match(/^\*\*(.*?)\*\*/)) {
        const content = line.replace(/^\*\*(.*?)\*\*/, '$1');
        return <strong key={index} className="block mt-2 mb-1">{content}</strong>;
      }
      // Check if line is empty (for spacing)
      else if (line.trim() === '') {
        return <br key={index} />;
      }
      // Regular line
      else {
        return (
          <span key={index} className="block">
            {line}
          </span>
        );
      }
    });
  };

  const handleToggle = (id: number | string) => {
    setActiveItem((prev) => (prev === id ? null : id));
  };

  return (
    <div className={`accordion -block text-left js-accordion ${className}`} style={{ margin: '0 auto' }}>
      {items.map((item) => {
        const isActive = activeItem === item.id;
        return (
          <div
            key={item.id}
            className={`accordion__item ${isActive ? "is-active" : ""}`}
            style={{
              border: '1px solid #e7e7e7',
              borderRadius: '24px',
              marginBottom: '24px',
              overflow: 'hidden',
              transition: 'all 0.3s ease',
              background: isActive ? '#F0FAFF' : '#fff',
            }}
          >
            <div
              className="accordion__button flex items-center"
              onClick={() => handleToggle(item.id)}
              style={{
                padding: '20px 25px',
                cursor: 'pointer',
                backgroundColor: isActive ? '#EBF4F6' : 'white',
                transition: 'background-color 0.3s ease',
                alignItems: 'center',
                // display: 'flex' handled by Tailwind
              }}
            >
              <span
                className="text-17 fw-500 text-dark-1"
                style={{
                  fontSize: '20px',
                  fontWeight: isActive ? '700' : '600',
                  flexGrow: 1,
                  color: '#222',
                  textAlign:"left",
                  letterSpacing: '-0.01em',
                  marginRight: '18px', // marginRight instead of marginLeft
                }}
              >
                {item.question}
              </span>
              {/* Use up/down icon based on open/closed state */}
              <div
                className="accordion__icon"
                style={{
                  width: '35px',
                  height: '35px',
                  marginRight: '22px',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  color: '#374957',
                }}
              >
                {isActive ? (
                  <ChevronUp
                    className="icon2"
                    style={{
                      width: '24px',
                      height: '24px',
                    }}
                    stroke="currentColor"
                  />
                ) : (
                  <ChevronDown
                    className="icon2"
                    style={{
                      width: '24px',
                      height: '24px',
                    }}
                    stroke="currentColor"
                  />
                )}
              </div>
            </div>

            <div
              style={{
                maxHeight: isActive ? "900px" : "0",
                transition: "max-height 0.5s ease-in-out",
                overflow: "hidden",
                backgroundColor: 'white'
              }}
              className="accordion__content"
            >
              <div className="accordion__content__inner" style={{
                padding: '20px 0 28px 40px',
                lineHeight: '1.7',
                color: '#4b5563',
                fontSize: '17px',
                textAlign:"left"
              }}>
                <div className="faq-answer">
                  {formatAnswer(item.answer)}
                </div>
              </div>
            </div>
          </div>
        );
      })}
    </div>
  );
}

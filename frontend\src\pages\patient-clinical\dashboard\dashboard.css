.dashboard-container {
  padding: 20px;
  background-color: #f8f9fa;
  min-height: 100vh;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
}

/* Dashboard Banner Message */
.dashboard-banner-message {
  animation: slideDown 0.5s ease-out;
  box-shadow: 0 2px 8px rgba(191, 49, 49, 0.2);
  border-left: 4px solid #BF3131;
  transition: all 0.3s ease;
}

.dashboard-banner-message:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(191, 49, 49, 0.25);
}

@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateY(-20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}


/* Welcome Header */
.welcome-header {
  margin-bottom: 30px;
}

.welcome-title {
  font-size: 32px;
  font-weight: 600;
  color: #333333;
  margin: 0 0 8px 0;
}

.name-highlight {
  color: #4ECDC4;
}

.welcome-subtitle {
  color: #666666;
  font-size: 16px;
  margin: 0;
}

/* Section Styles */
.section {
  margin-bottom: 40px;
}

.section-title {
  font-size: 20px;
  font-weight: 600;
  color: #333333;
  margin: 0 0 20px 0;
  display: flex;
  align-items: center;
  gap: 8px;
}

.section-title::before {
  content: '';
  width: 4px;
  height: 20px;
  background-color: #4ECDC4;
  border-radius: 2px;
}

/* Report Cards Grid */
.report-cards-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 20px;
  margin-bottom: 40px;
}

.report-card {
  background: white;
  border-radius: 12px;
  padding: 24px;
  text-align: center;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  cursor: pointer;
  transition: transform 0.2s ease, box-shadow 0.2s ease;
  min-height: 120px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.report-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
}

.report-card.new-medication {
  background: linear-gradient(135deg, #E3F2FD 0%, #BBDEFB 100%);
}

.report-card.report-symptoms {
  background: linear-gradient(135deg, #F3E5F5 0%, #E1BEE7 100%);
}

.report-card.request-refund {
  background: linear-gradient(135deg, #E8F5E8 0%, #C8E6C9 100%);
}

.report-card.other-queries {
  background: linear-gradient(135deg, #FFF3E0 0%, #FFE0B2 100%);
}

.card-icon {
  width: 48px;
  height: 48px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 12px;
  color: white;
}

.new-medication .card-icon {
  background-color: #4ECDC4;
}

.report-symptoms .card-icon {
  background-color: #9C27B0;
}

.request-refund .card-icon {
  background-color: #4CAF50;
}

.other-queries .card-icon {
  background-color: #FF9800;
}

.report-card h3 {
  font-size: 14px;
  font-weight: 600;
  color: #333333;
  margin: 0;
  text-align: center;
}

/* Main Content Grid */
.main-content-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 30px;
  margin-bottom: 40px;
}

/* Section Header */
.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.see-all-btn {
  background: none;
  border: none;
  color: #4ECDC4;
  font-size: 14px;
  cursor: pointer;
  font-weight: 500;
}

.see-all-btn:hover {
  color: #3bb3ac;
}

/* Appointments Section */
.appointments-section {
  background: white;
  border-radius: 12px;
  padding: 24px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.appointments-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.appointment-item {
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 16px;
  border: 1px solid #f0f0f0;
  border-radius: 8px;
  background: #fafafa;
}

.doctor-avatar {
  width: 48px;
  height: 48px;
  border-radius: 50%;
  background: linear-gradient(135deg, #4ECDC4, #44A08D);
  flex-shrink: 0;
}

.appointment-details {
  flex: 1;
}

.appointment-details h4 {
  font-size: 16px;
  font-weight: 600;
  color: #333333;
  margin: 0 0 4px 0;
}

.appointment-details p {
  font-size: 14px;
  color: #666666;
  margin: 0 0 8px 0;
}

.appointment-time {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 12px;
  color: #888888;
}

.appointment-time svg {
  color: #4ECDC4;
}

.appointment-action {
  color: #4ECDC4;
  cursor: pointer;
}

.appointment-action:hover {
  color: #3bb3ac;
}

/* Activities Section */
.activities-section {
  background: white;
  border-radius: 12px;
  padding: 24px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.activities-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.activity-item {
  padding: 16px;
  border: 1px solid #f0f0f0;
  border-radius: 8px;
  background: #fafafa;
}

.activity-item h4 {
  font-size: 14px;
  font-weight: 600;
  color: #333333;
  margin: 0 0 4px 0;
}

.activity-item p {
  font-size: 12px;
  color: #666666;
  margin: 0;
}

/* Diary Section */
.diary-section {
  background: white;
  border-radius: 12px;
  padding: 24px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  margin-bottom: 40px;
}

.diary-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
}

.diary-actions {
  display: flex;
  gap: 12px;
}


.diary-content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 30px;
  align-items: start;
}

.diary-table-section {
  display: flex;
  flex-direction: column;
  height: 100%;
}

.diary-table-section h3 {
  font-size: 18px;
  font-weight: 600;
  color: #333333;
  margin: 0 0 16px 0;
  flex-shrink: 0;
}

/* Table Styles */
.table-container {
  overflow-x: auto;
  border-radius: 8px;
  border: 1px solid #e0e0e0;
  flex: 1;
  min-height: 200px;
  display: flex;
  flex-direction: column;
}

.diary-table,
.studies-table,
.forms-table {
  width: 100%;
  border-collapse: collapse;
  background: white;
  flex: 1;
}

.diary-table th,
.studies-table th,
.forms-table th {
  background-color: #f8f9fa;
  padding: 12px;
  text-align: left;
  font-weight: 600;
  font-size: 12px;
  color: #666666;
  border-bottom: 1px solid #e0e0e0;
}

.diary-table td,
.studies-table td,
.forms-table td {
  padding: 12px;
  font-size: 14px;
  color: #333333;
  border-bottom: 1px solid #f0f0f0;
}

.diary-table tbody tr:last-child td,
.studies-table tbody tr:last-child td,
.forms-table tbody tr:last-child td {
  border-bottom: none;
}

/* Bottom Sections */
.bottom-sections {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 30px;
}

.studies-section,
.forms-section {
  background: white;
  border-radius: 12px;
  padding: 24px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.active-indicator {
  background-color: #4ECDC4;
  color: white;
  padding: 4px 12px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;
}

.status-high {
  background-color: #ff4757;
  color: white;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
}

/* Dynamic Status Badge Styles */
.status-success {
  background-color: #28a745;
  color: white;
  padding: 4px 12px;
  border-radius: 16px;
  font-size: 12px;
  font-weight: 500;
  display: inline-block;
  text-align: center;
  min-width: 60px;
}

.status-warning {
  background-color: #ffc107;
  color: #212529;
  padding: 4px 12px;
  border-radius: 16px;
  font-size: 12px;
  font-weight: 500;
  display: inline-block;
  text-align: center;
  min-width: 60px;
}

.status-info {
  background-color: #17a2b8;
  color: white;
  padding: 4px 12px;
  border-radius: 16px;
  font-size: 12px;
  font-weight: 500;
  display: inline-block;
  text-align: center;
  min-width: 60px;
}

.status-danger {
  background-color: #dc3545;
  color: white;
  padding: 4px 12px;
  border-radius: 16px;
  font-size: 12px;
  font-weight: 500;
  display: inline-block;
  text-align: center;
  min-width: 60px;
}

.status-default {
  background-color: #6c757d;
  color: white;
  padding: 4px 12px;
  border-radius: 16px;
  font-size: 12px;
  font-weight: 500;
  display: inline-block;
  text-align: center;
  min-width: 60px;
}


/* Responsive Design */
@media (max-width: 1200px) {
  .report-cards-grid {
    grid-template-columns: repeat(2, 1fr);
  }

  .main-content-grid,
  .diary-content,
  .bottom-sections {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 768px) {
  .dashboard-container {
    padding: 16px;
  }

  .report-cards-grid {
    grid-template-columns: 1fr;
  }

  .welcome-title {
    font-size: 24px;
  }

  .diary-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 16px;
  }

  .appointment-item {
    flex-direction: column;
    align-items: flex-start;
    text-align: left;
  }
}

@media (max-width: 480px) {
  .diary-actions {
    flex-direction: column;
    width: 100%;
  }

  .add-btn {
    width: 100%;
  }
}

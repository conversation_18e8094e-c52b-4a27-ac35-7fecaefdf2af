import Preloader from "@/components/common/Preloader";
import { useNavigate } from "react-router-dom";
import "./user-selection-page.css";
import LightFooter from "@/shared/LightFooter";
import { motion } from "framer-motion";
import { Stethoscope, User } from "lucide-react";

const UserSelectionPage = () => {
  const navigate = useNavigate();

  const handleStaffSelection = () => {
    navigate("/login");
  };

  const handlePatientSelection = () => {
    navigate("/patient");
  };

  return (
    <div className="main-content bg-light-4">
      <Preloader />
      <main className="content-wrapper js-content-wrapper">
        <div className="dashboard__content bg-light-4">
          <div className="container-fluid px-0">
            <div className="row justify-content-center" style={{ height: "100vh" }}>
              <div className="col-md-6 col-lg-5 col-xl-4">
                <motion.div 
                  className="selection-card bg-white mb-5 border-0"
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.5 }}
                >
                  <div className="card-body p-5 text-center">
                    <motion.h3 
                      className="mb-4 selection-title"
                      initial={{ opacity: 0, y: -10 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ delay: 0.1, duration: 0.5 }}
                    >
                      Welcome to Nurtify
                    </motion.h3>
                    <motion.p
                      className="text-muted mb-5"
                      initial={{ opacity: 0 }}
                      animate={{ opacity: 1 }}
                      transition={{ delay: 0.2, duration: 0.5 }}
                    >
                      Please select how you would like to use the application
                    </motion.p>
                    <div className="role-selection-container">
                      <motion.div 
                        className="role-card"
                        initial={{ opacity: 0, x: -20 }}
                        animate={{ opacity: 1, x: 0 }}
                        transition={{ delay: 0.3, duration: 0.5 }}
                        whileHover={{ 
                          scale: 1.05,
                          boxShadow: "0 10px 25px rgba(0, 0, 0, 0.1)"
                        }}
                        onClick={handleStaffSelection}
                      >
                        <div className="role-icon staff">
                          <Stethoscope size={32} />
                        </div>
                        <h4>Clinical Staff</h4>
                        <p>Access clinical tools and patient management</p>
                      </motion.div>
                      
                      <motion.div 
                        className="role-card"
                        initial={{ opacity: 0, x: 20 }}
                        animate={{ opacity: 1, x: 0 }}
                        transition={{ delay: 0.4, duration: 0.5 }}
                        whileHover={{ 
                          scale: 1.05,
                          boxShadow: "0 10px 25px rgba(0, 0, 0, 0.1)"
                        }}
                        onClick={handlePatientSelection}
                      >
                        <div className="role-icon patient">
                          <User size={32} />
                        </div>
                        <h4>Patient</h4>
                        <p>View your medical information and communicate with your care team</p>
                      </motion.div>
                    </div>
                  </div>
                </motion.div>
              </div>
            </div>
          </div>
        </div>
      </main>
      <LightFooter />
    </div>
  );
};

export default UserSelectionPage;

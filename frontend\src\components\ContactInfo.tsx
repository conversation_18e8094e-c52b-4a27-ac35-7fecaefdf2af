import React from "react";

interface ContactDataItem {
  id: number;
  icon: string;
  address?: string;
  phoneNumber?: string;
  email?: string;
}

interface ContactInfoProps {
  contactData: ContactDataItem[];
}

const ContactInfo: React.FC<ContactInfoProps> = ({ contactData }) => {
  return (
    <div className="row y-gap-50 justify-between mt-5">
      <div className="col-lg-12">
        <h3 className="text-24 fw-500">Keep In Touch With Us.</h3>
        <p className="mt-25">
          Contact us for any questions or concerns. We are here to help.
        </p>

        <div className="y-gap-30 pt-60 lg:pt-40">
          {contactData.map((elm, i) => (
            <div
              key={i}
              className="d-flex items-center"
              style={{ gap: "20px", marginBottom: "20px" }}
            >
              <div className="d-flex justify-center items-center size-60 rounded-full bg-light-7">
                <img src={elm.icon} alt="icon" />
              </div>
              <div className="ml-20">
                {elm.address
                  ? `${elm.address
                      .split(" ")
                      .slice(0, 4)
                      .join(" ")} \n ${elm.address
                      .split(" ")
                      .slice(4, -1)
                      .join(" ")}`
                  : elm.email || elm.phoneNumber}
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

export default ContactInfo;

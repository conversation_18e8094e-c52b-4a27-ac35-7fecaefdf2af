/* VitalSignsHistory.css - Enhanced UI/UX */

/* Modal Overlay */
.vital-signs-history-overlay {
  position: fixed;
  inset: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: 1rem;
  animation: fadeInOverlay 0.3s ease-out;
}

/* Modal Container */
.vital-signs-history-modal {
  background-color: white;
  border-radius: 16px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
  max-height: 90vh;
  max-width: 1200px;
  width: 95%;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  animation: fadeInModal 0.3s ease-out;
}

/* Header */
.history-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 1.5rem 2rem;
  border-bottom: 1px solid var(--color-light-2);
  background: linear-gradient(135deg, var(--color-light-6) 0%, var(--color-light-4) 100%);
  border-radius: 16px 16px 0 0;
}

.history-header h2 {
  font-size: 1.5rem;
  font-weight: 600;
  color: var(--color-dark-1);
  margin: 0;
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.back-button {
  background: var(--color-purple-1);
  color: white;
  border: none;
  border-radius: 50%;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 1.2rem;
  margin-right: 1rem;
}

.back-button:hover {
  background: var(--color-dark-1);
  transform: translateX(-2px);
}

.close-button {
  background: var(--color-light-2);
  color: var(--color-dark-3);
  border: none;
  border-radius: 50%;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 1.2rem;
}

.close-button:hover {
  background: var(--color-red-1);
  color: white;
  transform: scale(1.1);
}

/* Debug Info */
.debug-info {
  background: var(--color-light-4);
  padding: 1rem;
  margin: 1rem 2rem;
  border-radius: 12px;
  font-size: 0.875rem;
  border: 1px solid var(--color-light-2);
  color: var(--color-dark-3);
}

.debug-info strong {
  color: var(--color-dark-1);
}

/* Filter Bar */
.vital-signs-filter-bar {
  display: flex;
  flex-wrap: wrap;
  gap: 1rem;
  padding: 1.5rem 2rem;
  background: var(--color-light-4);
  border-bottom: 1px solid var(--color-light-2);
  align-items: center;
}

.vital-signs-filter-bar label {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.875rem;
  font-weight: 500;
  color: var(--color-dark-1);
  white-space: nowrap;
}

.vital-signs-filter-bar select,
.vital-signs-filter-bar input[type="date"],
.vital-signs-filter-bar input[type="number"] {
  border: 1px solid var(--color-light-2);
  border-radius: 8px;
  padding: 0.5rem 0.75rem;
  font-size: 0.875rem;
  background: white;
  transition: all 0.3s ease;
  min-width: 120px;
}

.vital-signs-filter-bar select:focus,
.vital-signs-filter-bar input:focus {
  outline: none;
  border-color: var(--color-purple-1);
  box-shadow: 0 0 0 3px rgba(55, 183, 195, 0.1);
}

.vital-signs-filter-bar input[type="checkbox"] {
  width: 18px;
  height: 18px;
  accent-color: var(--color-purple-1);
}

.filter-clear-btn {
  background: var(--color-light-2);
  color: var(--color-dark-3);
  border: none;
  border-radius: 8px;
  padding: 0.5rem 1rem;
  font-size: 0.875rem;
  cursor: pointer;
  transition: all 0.3s ease;
  margin-left: auto;
}

.filter-clear-btn:hover {
  background: var(--color-dark-3);
  color: white;
}

/* Content Area */
.vital-signs-list {
  padding: 2rem;
  overflow-y: auto;
  flex: 1;
}

.vital-signs-list h3 {
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--color-dark-1);
  margin: 0 0 1.5rem 0;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.vital-signs-list h3::before {
  content: "📊";
  font-size: 1.5rem;
}

/* Record List */
.vital-signs-type-list {
  list-style: none;
  padding: 0;
  margin: 0;
  display: grid;
  gap: 1rem;
}

.vital-sign-type-btn {
  width: 100%;
  background: white;
  border: 2px solid var(--color-light-2);
  border-radius: 12px;
  padding: 1.5rem;
  text-align: left;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 0.95rem;
  line-height: 1.5;
  color: var(--color-dark-1);
  position: relative;
  overflow: hidden;
}

.vital-sign-type-btn::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 4px;
  height: 100%;
  background: var(--color-purple-1);
  transform: scaleY(0);
  transition: transform 0.3s ease;
}

.vital-sign-type-btn:hover {
  border-color: var(--color-purple-1);
  background: var(--color-light-6);
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(55, 183, 195, 0.15);
}

.vital-sign-type-btn:hover::before {
  transform: scaleY(1);
}

.vital-sign-type-btn:active {
  transform: translateY(0);
}

/* History Filters */
.history-filters {
  display: flex;
  flex-wrap: wrap;
  gap: 1.5rem;
  padding: 1.5rem 2rem;
  background: var(--color-light-4);
  border-bottom: 1px solid var(--color-light-2);
  align-items: flex-end;
}

.filter-group {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  min-width: 150px;
}

.filter-group label {
  font-size: 0.875rem;
  font-weight: 500;
  color: var(--color-dark-1);
}

.filter-group input,
.filter-group select {
  border: 1px solid var(--color-light-2);
  border-radius: 8px;
  padding: 0.75rem;
  font-size: 0.875rem;
  background: white;
  transition: all 0.3s ease;
}

.filter-group input:focus,
.filter-group select:focus {
  outline: none;
  border-color: var(--color-purple-1);
  box-shadow: 0 0 0 3px rgba(55, 183, 195, 0.1);
}

.date-range {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.date-range span {
  color: var(--color-dark-3);
  font-size: 0.875rem;
}

/* History Content */
.history-content {
  padding: 2rem;
  overflow-y: auto;
  flex: 1;
}

/* Loading State */
.history-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 3rem;
  color: var(--color-dark-3);
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 3px solid var(--color-light-2);
  border-top: 3px solid var(--color-purple-1);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 1rem;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Error State */
.history-error {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 3rem;
  color: var(--color-red-1);
  text-align: center;
}

.history-error p {
  margin-bottom: 1.5rem;
  font-size: 1.1rem;
}

/* Empty State */
.history-empty {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 3rem;
  color: var(--color-dark-3);
  text-align: center;
}

.history-empty::before {
  content: "📋";
  font-size: 4rem;
  margin-bottom: 1rem;
  opacity: 0.5;
}

.history-empty p {
  font-size: 1.1rem;
  margin: 0;
}

/* Timeline */
.history-timeline {
  position: relative;
  padding-left: 2rem;
}

.history-timeline::before {
  content: "";
  position: absolute;
  left: 20px;
  top: 0;
  bottom: 0;
  width: 2px;
  background: linear-gradient(to bottom, var(--color-purple-1), var(--color-light-2));
}

.history-entry {
  position: relative;
  margin-bottom: 2rem;
  background: white;
  border-radius: 12px;
  border: 1px solid var(--color-light-2);
  overflow: hidden;
  transition: all 0.3s ease;
}

.history-entry:hover {
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
  transform: translateX(5px);
}

.history-timeline-marker {
  position: absolute;
  left: -30px;
  top: 1.5rem;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 3px solid white;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.change-icon {
  font-size: 1.2rem;
}

.history-entry-content {
  padding: 1.5rem;
}

.history-entry-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
}

.history-entry-header h4 {
  font-size: 1.1rem;
  font-weight: 600;
  color: var(--color-dark-1);
  margin: 0;
}

.change-type {
  font-size: 0.875rem;
  font-weight: 500;
  padding: 0.25rem 0.75rem;
  border-radius: 20px;
  background: var(--color-light-4);
  text-transform: capitalize;
}

.history-entry-details {
  margin-bottom: 1rem;
}

.value-change {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1rem;
  background: var(--color-light-4);
  border-radius: 8px;
  margin-bottom: 1rem;
}

.value-added {
  padding: 1rem;
  background: var(--color-green-6);
  border-radius: 8px;
  margin-bottom: 1rem;
}

.old-value,
.new-value {
  font-weight: 500;
  padding: 0.5rem 1rem;
  border-radius: 6px;
  background: white;
  border: 1px solid var(--color-light-2);
}

.old-value {
  color: var(--color-red-1);
  text-decoration: line-through;
  opacity: 0.7;
}

.new-value {
  color: var(--color-green-5);
}

.arrow {
  font-size: 1.2rem;
  color: var(--color-dark-3);
}

/* History Meta */
.history-meta {
  display: grid;
  gap: 0.75rem;
  padding-top: 1rem;
  border-top: 1px solid var(--color-light-2);
  font-size: 0.875rem;
}

.history-user,
.history-timestamp,
.history-notes {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.user-label,
.timestamp-label,
.notes-label {
  font-weight: 500;
  color: var(--color-dark-3);
  min-width: 80px;
}

.user-name {
  font-weight: 600;
  color: var(--color-dark-1);
}

.user-email {
  color: var(--color-dark-3);
  font-style: italic;
}

.timestamp-value {
  color: var(--color-dark-1);
  font-weight: 500;
}

.notes-value {
  color: var(--color-dark-1);
  font-style: italic;
}

/* Buttons */
.btn {
  border: none;
  border-radius: 8px;
  padding: 0.75rem 1.5rem;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  text-decoration: none;
}

.btn-primary {
  background: var(--color-purple-1);
  color: white;
}

.btn-primary:hover {
  background: var(--color-dark-1);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(55, 183, 195, 0.3);
}

.btn-secondary {
  background: var(--color-light-2);
  color: var(--color-dark-1);
}

.btn-secondary:hover {
  background: var(--color-dark-3);
  color: white;
}

.btn-small {
  padding: 0.5rem 1rem;
  font-size: 0.8rem;
}

/* Footer Actions */
.history-footer {
  display: flex;
  justify-content: center;
  padding: 1.5rem 2rem;
  border-top: 1px solid var(--color-light-2);
  background: var(--color-light-4);
}

/* Animations */
@keyframes fadeInOverlay {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes fadeInModal {
  from { 
    opacity: 0; 
    transform: translateY(-20px) scale(0.95); 
  }
  to { 
    opacity: 1; 
    transform: translateY(0) scale(1); 
  }
}

/* Responsive Design */
@media (max-width: 768px) {
  .vital-signs-history-modal {
    width: 98%;
    max-height: 95vh;
  }

  .history-header {
    padding: 1rem 1.5rem;
  }

  .history-header h2 {
    font-size: 1.25rem;
  }

  .vital-signs-filter-bar,
  .history-filters {
    padding: 1rem 1.5rem;
    flex-direction: column;
    align-items: stretch;
  }

  .vital-signs-list,
  .history-content {
    padding: 1.5rem;
  }

  .value-change {
    flex-direction: column;
    align-items: stretch;
    gap: 0.5rem;
  }

  .history-meta {
    grid-template-columns: 1fr;
  }

  .filter-clear-btn {
    margin-left: 0;
    margin-top: 1rem;
  }
}

@media (max-width: 480px) {
  .vital-signs-history-overlay {
    padding: 0.5rem;
  }

  .history-header {
    padding: 1rem;
  }

  .back-button,
  .close-button {
    width: 36px;
    height: 36px;
    font-size: 1rem;
  }

  .vital-signs-filter-bar,
  .history-filters {
    padding: 1rem;
  }

  .vital-signs-list,
  .history-content {
    padding: 1rem;
  }

  .history-timeline {
    padding-left: 1.5rem;
  }

  .history-timeline-marker {
    left: -25px;
    width: 32px;
    height: 32px;
  }

  .history-entry-content {
    padding: 1rem;
  }
}

/* Accessibility */
@media (prefers-reduced-motion: reduce) {
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}

/* Focus styles for keyboard navigation */
.vital-sign-type-btn:focus,
.btn:focus,
.back-button:focus,
.close-button:focus {
  outline: 2px solid var(--color-purple-1);
  outline-offset: 2px;
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .vital-signs-history-modal {
    border: 2px solid var(--color-dark-1);
  }
  
  .vital-sign-type-btn {
    border-width: 2px;
  }
  
  .history-entry {
    border-width: 2px;
  }
}

/* Search Bar Styles */
.search-container {
  margin-bottom: 2rem;
}

.search-input {
  width: 100%;
  max-width: 500px;
  padding: 12px 16px;
  border: 2px solid #e1e5e9;
  border-radius: 8px;
  font-size: 1rem;
  background-color: #fff;
  transition: border-color 0.3s ease;
}

.search-input:focus {
  outline: none;
  border-color: #00a3c8;
  box-shadow: 0 0 0 3px rgba(0, 163, 200, 0.1);
}

.search-input::placeholder {
  color: #9ca3af;
}

/* Resources Container */
.resources-container {
  margin-bottom: 2rem;
}

.resources-title {
  font-size: 2rem;
  font-weight: 700;
  text-align: center;
  margin-bottom: 1rem;
  color: #2c3e50;
}

.resources-subtitle {
  font-size: 1.1rem;
  text-align: center;
  margin-bottom: 2rem;
  color: #6c757d;
  max-width: 600px;
  margin-left: auto;
  margin-right: auto;
}

.text-highlight {
  color: #00a3c8;
}

/* Policy Cards Grid */
.policies-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 20px;
  margin-top: 2rem;
}

/* Policy Card Styles - Figma Design */
.policy-card {
  background: #EBF4F6;
  border-radius: 12px;
  padding: 24px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
  border: none;
  transition: box-shadow 0.3s ease;
  display: flex;
  flex-direction: column;
  min-height: 180px;
  max-width: 280px;
}

.policy-card:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.12);
}

.policy-card-title {
  font-size: 1.1rem;
  font-weight: 600;
  color: #2c3e50;
  margin: 0 0 8px 0;
  line-height: 1.3;
}

.policy-card-description {
  font-size: 0.9rem;
  color: #6c757d;
  line-height: 1.4;
  margin: 0 0 20px 0;
  flex-grow: 1;
}

.policy-card-button {
  background-color: #37B7C3;
  color: #ffffff;
  border: none;
  border-radius: 20px;
  padding: 8px 16px;
  font-size: 0.85rem;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.3s ease;
  align-self: flex-start;
  min-width: 60px;
}

.policy-card-button:hover {
  background-color: #2da5b1;
}

.policy-card-button:active {
  background-color: #25939f;
}

/* State Messages */
.loading-state,
.error-state,
.no-results-state,
.no-policies-state {
  text-align: center;
  padding: 3rem 1rem;
  color: #6c757d;
}

.loading-state p,
.error-state p,
.no-results-state p,
.no-policies-state p {
  font-size: 1.1rem;
  margin: 0;
}

.error-state p {
  color: #dc3545;
}

/* Responsive Design */
@media (max-width: 768px) {
  .resources-title {
    font-size: 1.75rem;
  }

  .policies-grid {
    grid-template-columns: 1fr;
    gap: 16px;
  }

  .policy-card {
    padding: 20px;
    min-height: 180px;
  }

  .search-input {
    font-size: 16px; /* Prevents zoom on iOS */
  }
}

@media (max-width: 480px) {
  .resources-title {
    font-size: 1.5rem;
  }

  .resources-subtitle {
    font-size: 1rem;
  }

  .policy-card {
    padding: 16px;
  }

  .policy-card-title {
    font-size: 1.1rem;
  }

  .policy-card-description {
    font-size: 0.9rem;
  }
}

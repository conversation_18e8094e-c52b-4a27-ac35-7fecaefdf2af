import { useNavigate } from "react-router-dom";
import SEOH<PERSON> from "@/components/SEO/SEOHead";
import { usePageSEO } from "@/hooks/usePageSEO";

export default function ContactPage(): JSX.Element {
  const navigate = useNavigate();
  const seoConfig = usePageSEO({
    structuredData: {
      "@context": "https://schema.org",
      "@type": "ContactPage",
      "name": "Contact Nurtify",
      "description": "Get in touch with Nurtify's clinical trial experts for support, referrals, study participation, or business opportunities.",
      "contactPoint": [
        {
          "@type": "ContactPoint",
          "contactType": "customer service",
          "description": "General inquiries and support"
        },
        {
          "@type": "ContactPoint", 
          "contactType": "sales",
          "description": "Business partnerships and collaborations"
        }
      ]
    }
  });

  const goTo = (cat: string) => {
    navigate(`/contact-us/${cat}`);
  };

  return (
    <>
      <SEOHead {...seoConfig} />
      <section className="contact-header-section">
        <img
          src="/assets/img/contact-us/ContactUsHeader.png"
          alt="Contact Us Header"
        />
        <div className="contact-header-content">
          <h1 className="responsive-heading">
            What can we help <br />you with?
          </h1>
          <p className="responsive-desc">
            Choose a category below to get started.
          </p>
        </div>
      </section>

      <section className="layout-pt-md">
        <div className="container p-2 p-md-4">

          <div className="row mt-4 mb-5">
            <div className="col-12">
              {/* Responsive style for 2 cards per row, 1 per row on mobile */}
              <style>
                {`
                  @media (max-width: 700px) {
                    .category-card {
                      width: 100% !important;
                      max-width: 100% !important;
                      margin-bottom: 16px;
                    }
                  }
                `}
              </style>
              <div
                className="category-buttons-container"
                style={{
                  display: "flex",
                  flexWrap: "wrap",
                  justifyContent: "space-between",
                  gap: "24px",
                  padding: "16px",
                  background: "none",
                  borderRadius: "10px",
                  boxSizing: "border-box"
                }}
              >
                {/* General Inquiry */}
                <div
                  className="category-card"
                  style={{
                    background: "#EBF4F6",
                    borderRadius: "8px",
                    padding: "28px 20px 24px 20px",
                    display: "flex",
                    flexDirection: "column",
                    minHeight: "200px",
                    boxShadow: "0 1px 4px 0 rgba(0,0,0,0.03)",
                    width: "48%",
                    maxWidth: "48%",
                    marginBottom: "24px"
                  }}
                >
                  <div style={{ fontWeight: 700, fontSize: "22px", marginBottom: "8px", color: "#1A2B3B" }}>
                    General Inquiry
                  </div>
                  <div style={{ fontSize: "16px", color: "#3B4A5A", marginBottom: "18px", flex: 1 }}>
                    Have questions or need support? Reach out to us and we'll get back to you as soon as possible.
                  </div>
                  <button
                    style={{
                      background: "#37B7C3",
                      color: "#fff",
                      border: "none",
                      borderRadius: "20px",
                      padding: "8px 22px",
                      fontWeight: 600,
                      fontSize: "15px",
                      cursor: "pointer",
                      alignSelf: "flex-start"
                    }}
                    onClick={() => goTo("support")}
                  >
                    Send a Message
                  </button>
                </div>
                {/* Doctor Referring a Patient */}
                <div
                  className="category-card"
                  style={{
                    background: "#EBF4F6",
                    borderRadius: "8px",
                    padding: "28px 20px 24px 20px",
                    display: "flex",
                    flexDirection: "column",
                    minHeight: "200px",
                    boxShadow: "0 1px 4px 0 rgba(0,0,0,0.03)",
                    width: "48%",
                    maxWidth: "48%",
                    marginBottom: "24px"
                  }}
                >
                  <div style={{ fontWeight: 700, fontSize: "22px", marginBottom: "8px", color: "#1A2B3B" }}>
                    Doctor Referring a Patient
                  </div>
                  <div style={{ fontSize: "16px", color: "#3B4A5A", marginBottom: "18px", flex: 1 }}>
                    Are you a doctor looking to refer a patient for a study or care? Share the details securely with our team.
                  </div>
                  <button
                    style={{
                      background: "#37B7C3",
                      color: "#fff",
                      border: "none",
                      borderRadius: "20px",
                      padding: "8px 22px",
                      fontWeight: 600,
                      fontSize: "15px",
                      cursor: "pointer",
                      alignSelf: "flex-start"
                    }}
                    onClick={() => goTo("doctor")}
                  >
                    Refer a Patient
                  </button>
                </div>
                {/* Patient Joining a Study */}
                <div
                  className="category-card"
                  style={{
                    background: "#EBF4F6",
                    borderRadius: "8px",
                    padding: "28px 20px 24px 20px",
                    display: "flex",
                    flexDirection: "column",
                    minHeight: "200px",
                    boxShadow: "0 1px 4px 0 rgba(0,0,0,0.03)",
                    width: "48%",
                    maxWidth: "48%",
                    marginBottom: "24px"
                  }}
                >
                  <div style={{ fontWeight: 700, fontSize: "22px", marginBottom: "8px", color: "#1A2B3B" }}>
                    Patient Joining a Study
                  </div>
                  <div style={{ fontSize: "16px", color: "#3B4A5A", marginBottom: "18px", flex: 1 }}>
                    Interested in participating in one of our clinical studies? Complete the form to start your journey.
                  </div>
                  <button
                    style={{
                      background: "#37B7C3",
                      color: "#fff",
                      border: "none",
                      borderRadius: "20px",
                      padding: "8px 22px",
                      fontWeight: 600,
                      fontSize: "15px",
                      cursor: "pointer",
                      alignSelf: "flex-start"
                    }}
                    onClick={() => goTo("patient")}
                  >
                    Join a Study
                  </button>
                </div>
                {/* Business or Collaboration */}
                <div
                  className="category-card"
                  style={{
                    background: "#EBF4F6",
                    borderRadius: "8px",
                    padding: "28px 20px 24px 20px",
                    display: "flex",
                    flexDirection: "column",
                    minHeight: "200px",
                    boxShadow: "0 1px 4px 0 rgba(0,0,0,0.03)",
                    width: "48%",
                    maxWidth: "48%",
                    marginBottom: "24px"
                  }}
                >
                  <div style={{ fontWeight: 700, fontSize: "22px", marginBottom: "8px", color: "#1A2B3B" }}>
                    Business or Collaboration
                  </div>
                  <div style={{ fontSize: "16px", color: "#3B4A5A", marginBottom: "18px", flex: 1 }}>
                    For partnerships, sponsorships, or other business opportunities, connect with our team.
                  </div>
                  <button
                    style={{
                      background: "#37B7C3",
                      color: "#fff",
                      border: "none",
                      borderRadius: "20px",
                      padding: "8px 22px",
                      fontWeight: 600,
                      fontSize: "15px",
                      cursor: "pointer",
                      alignSelf: "flex-start"
                    }}
                    onClick={() => goTo("business")}
                  >
                    Get In Touch
                  </button>
                </div>
              </div>
            </div>
          </div>

        </div>
      </section>
    </>
  );
}

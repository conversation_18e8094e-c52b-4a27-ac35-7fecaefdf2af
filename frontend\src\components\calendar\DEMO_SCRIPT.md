# Calendar View Demo Script

This document provides a script for creating a demo video showcasing the enhanced Calendar View functionality.

## Setup

1. Ensure the application is running locally:
   ```
   cd frontend
   npm run dev
   ```

2. Navigate to the Visits page at `/org/dashboard/list-of-patients`

## Demo Sequence

### 1. Introduction (10 seconds)

"Today I'll demonstrate the new Calendar View feature for managing patient visits. This enhancement allows users to visualize and manage visits in Day, Week, and Month views, in addition to the existing List view."

### 2. View Switching (20 seconds)

- Start with the default List View
- "Let's start with the familiar List View, which shows all visits in a tabular format."
- Click the "Day" button in the view toggle
- "Switching to Day View gives us a detailed timeline for a single day."
- Click the "Week" button
- "Week View provides a Monday-to-Sunday grid, similar to Outlook's calendar."
- Click the "Month" button
- "Month View gives us a broader perspective of all visits across the month."
- Click the "List" button to return to List View
- "We can easily switch back to List View when needed."

### 3. Keyboard Shortcuts (15 seconds)

- "The Calendar View also supports keyboard shortcuts for quick navigation."
- Press 'D' key to switch to Day View
- "Pressing 'D' switches to Day View."
- Press 'W' key to switch to Week View
- "Pressing 'W' switches to Week View."
- Press 'M' key to switch to Month View
- "Pressing 'M' switches to Month View."
- Press 'L' key to switch back to List View
- "And 'L' returns us to List View."

### 4. Date Navigation (15 seconds)

- Switch to Week View
- "In Week View, we can navigate between weeks using the arrow buttons."
- Click the left arrow button
- "This takes us to the previous week."
- Click the right arrow button twice
- "And we can move forward to future weeks."
- Use the date picker to select a specific date
- "We can also jump directly to a specific date using the date picker."

### 5. Visit Details (20 seconds)

- Switch to Day View
- "Each visit appears as a block in the calendar, color-coded by registration status."
- Point out the color coding
- "Teal indicates patients who are in the hospital, amber shows patients who haven't arrived yet, and gray represents discharged patients."
- Click on a visit block
- "Clicking on a visit opens the same detail modal we use in List View, maintaining consistency across the application."
- Close the modal

### 6. Drag and Drop (20 seconds)

- "One of the key benefits of Calendar View is the ability to reschedule visits using drag and drop."
- Drag a visit block to a different time slot
- "I can simply drag a visit to reschedule it to a different time."
- Resize a visit block
- "I can also resize a visit to change its duration."
- "These actions open the edit modal, allowing me to confirm the changes before saving."

### 7. Month View Features (15 seconds)

- Switch to Month View
- "Month View provides a comprehensive overview of all visits in the month."
- "Each visit appears as a pill in the day cell, with the same color coding as in Day and Week views."
- "For days with many visits, a 'more' link appears, which can be clicked to see all visits for that day."

### 8. Responsive Behavior (15 seconds)

- "The Calendar View is fully responsive."
- Resize the browser window to less than 768px width
- "On small screens, the application automatically switches to List View for better usability."
- "A message informs users that Calendar View is not available on small screens."
- Resize the browser back to a larger size
- "On larger screens, all view options are available."

### 9. Conclusion (10 seconds)

"This enhanced Calendar View provides users with flexible options for visualizing and managing patient visits. The consistent color coding, interaction patterns, and responsive design ensure a seamless user experience across all views."

## Recording Tips

1. Use a screen recording tool like OBS Studio, ScreenToGif, or QuickTime (Mac).
2. Record at 1920x1080 resolution if possible.
3. Speak clearly and at a moderate pace.
4. Ensure the application has some sample visit data for the demonstration.
5. Edit the final video to keep it under 2 minutes for better engagement.
6. Add captions if possible for accessibility.

## Converting to GIF

If a GIF is required for the deliverable:

1. Record the full demo as a video first.
2. Use a tool like FFmpeg, ScreenToGif, or an online converter to convert the video to GIF.
3. Consider optimizing the GIF for file size while maintaining clarity:
   ```
   ffmpeg -i input.mp4 -vf "fps=15,scale=800:-1:flags=lanczos" -c:v gif output.gif
   ```
4. For better quality with reasonable file size, consider using a compressed video format (MP4 with H.264) instead of GIF if allowed.

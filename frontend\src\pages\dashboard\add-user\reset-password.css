.reset-password-card {
  border-radius: 0; /* Changed */
  box-shadow: none; /* Changed */
  transition: none; /* Changed */
}

.reset-password-card:hover {
  transform: none; /* Changed */
  box-shadow: none; /* Changed */
}

.password-input-container {
  position: relative;
  margin-bottom: 16px;
}

.password-input {
  width: 100%;
  padding: 12px;
  border-radius: 0; /* Changed */
  border: 2px solid #e4e7ea;
  background-color: #f7f8fb;
  font-size: 16px;
  transition: all 0.3s ease;
  color: var(--color-dark-1);
  outline: none;
}

.password-input:focus {
  border-color: var(--color-purple-1);
  box-shadow: none; /* Changed */
  background-color: white;
}

.password-toggle-btn {
  position: absolute;
  right: 0px;
  top: 25px;
  bottom: 0;
  margin: auto;
  background: none;
  border: none;
  color: #94a3b8;
  cursor: pointer;
  width: 40px;
  height: 40px;
  padding: 0;
  display: flex;
  align-items: center;
  justify-content: center;
}

.error-container {
  background-color: rgba(237, 76, 92, 0.1);
  border-radius: 0; /* Changed */
  padding: 10px;
  margin-bottom: 16px;
}

.error-message {
  color: #ed4c5c;
  font-size: 14px;
  margin: 0;
}

.success-message {
  background-color: rgba(16, 185, 129, 0.1);
  color: #10b981;
  font-size: 14px;
  font-weight: 500;
  padding: 10px;
  border-radius: 0; /* Changed */
  margin-top: 10px;
  animation: fadeIn 0.3s ease-in-out;
}

.password-strength-container {
  margin-bottom: 16px;
}

.password-strength-bars {
  display: flex;
  gap: 4px;
  margin-bottom: 4px;
}

.password-strength-bar {
  height: 4px;
  flex: 1;
  border-radius: 0; /* Changed */
  transition: background-color 0.3s ease;
}

.password-strength-text {
  font-size: 12px;
  transition: color 0.3s ease;
}

.password-requirements {
  font-size: 12px;
  color: #64748b;
  margin-bottom: 16px;
}

.password-requirement {
  margin-top: 4px;
  transition: color 0.3s ease;
}

.password-requirement.met {
  color: #10B981;
}

.btn-nurtify {
  background-color: #37B7C3;
  color: white;
  border: none;
  border-radius: 0; /* Changed */
  padding: 12px 24px;
  font-weight: 500;
  transition: background-color 0.3s ease, transform 0.2s ease;
  cursor: pointer;
  width: 100%;
}

.btn-nurtify:hover {
  background-color: #2da8b4;
  transform: none; /* Changed */
}

.btn-nurtify:disabled {
  background-color: #B0BEC5;
  cursor: not-allowed;
  transform: none;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Mobile responsiveness */
@media (max-width: 576px) {
  .password-input {
    font-size: 14px;
  }
}

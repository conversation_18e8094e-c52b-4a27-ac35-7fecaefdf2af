/* Tasks Modal Styles - Prefixed with .tasks-modal- */

/* Modal Overlay */
.tasks-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.6);
  backdrop-filter: blur(4px);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: 20px;
  animation: tasks-modal-fadeIn 0.3s ease-out;
}

@keyframes tasks-modal-fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

/* Main Modal Container */
.tasks-modal-container {
  background: var(--color-white);
  border-radius: 16px;
  box-shadow: 0 25px 80px rgba(0, 0, 0, 0.2);
  width: 100%;
  max-width: 800px;
  max-height: 90vh;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  animation: tasks-modal-slideUp 0.4s cubic-bezier(0.34, 1.56, 0.64, 1);
  border: 1px solid var(--color-light-2);
}

@keyframes tasks-modal-slideUp {
  from {
    opacity: 0;
    transform: translateY(30px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

/* Modal Header */
.tasks-modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 28px 36px;
  border-bottom: 1px solid var(--color-light-2);
  background: linear-gradient(135deg, var(--color-purple-1) 0%, var(--color-blue-1) 100%);
  color: white;
  position: relative;
}

.tasks-modal-header::before {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
}

.tasks-modal-header-content {
  display: flex;
  align-items: center;
  gap: 12px;
}

.tasks-modal-header-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.2);
}

.tasks-modal-header h2 {
  margin: 0;
  font-size: var(--text-24);
  font-weight: 700;
  font-family: var(--font-primary);
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.tasks-modal-close-button {
  background: none;
  border: none;
  color: white;
  cursor: pointer;
  padding: 10px;
  border-radius: 8px;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  display: flex;
  align-items: center;
  justify-content: center;
}

.tasks-modal-close-button:hover {
  background-color: rgba(255, 255, 255, 0.15);
  transform: scale(1.05);
}

.tasks-modal-close-button:active {
  transform: scale(0.95);
}

/* Modal Content */
.tasks-modal-content {
  flex: 1;
  overflow-y: auto;
  padding: 36px;
  background: var(--color-white);
}

.tasks-modal-content::-webkit-scrollbar {
  width: 6px;
}

.tasks-modal-content::-webkit-scrollbar-track {
  background: var(--color-light-3);
  border-radius: 3px;
}

.tasks-modal-content::-webkit-scrollbar-thumb {
  background: var(--color-light-1);
  border-radius: 3px;
}

.tasks-modal-content::-webkit-scrollbar-thumb:hover {
  background: var(--color-purple-1);
}

/* Study Info Section */
.tasks-modal-study-info {
  background: linear-gradient(135deg, var(--color-light-6) 0%, var(--color-white) 100%);
  padding: 24px;
  border-radius: 12px;
  margin-bottom: 32px;
  border: 2px solid var(--color-light-2);
  position: relative;
  overflow: hidden;
}

.tasks-modal-study-info::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, var(--color-purple-1), var(--color-blue-1));
}

.tasks-modal-study-info-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20px;
}

.tasks-modal-info-item {
  display: flex;
  flex-direction: column;
  gap: 6px;
}

.tasks-modal-info-label {
  font-weight: 600;
  color: var(--color-light-1);
  font-size: var(--text-13);
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.tasks-modal-info-value {
  font-weight: 600;
  color: var(--color-dark-1);
  font-size: var(--text-16);
  line-height: 1.4;
  word-break: break-word;
}

/* Tasks Section */
.tasks-modal-tasks-section {
  margin-bottom: 32px;
}

.tasks-modal-tasks-header {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 24px;
  padding-bottom: 16px;
  border-bottom: 2px solid var(--color-light-2);
}

.tasks-modal-tasks-header h3 {
  font-size: var(--text-20);
  font-weight: 700;
  color: var(--color-dark-1);
  margin: 0;
  font-family: var(--font-primary);
}

.tasks-modal-tasks-count {
  background: linear-gradient(135deg, var(--color-purple-1) 0%, var(--color-blue-1) 100%);
  color: white;
  padding: 4px 12px;
  border-radius: 12px;
  font-size: var(--text-12);
  font-weight: 700;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

/* Tasks List */
.tasks-modal-tasks-list {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.tasks-modal-task-item {
  background: var(--color-white);
  border: 2px solid var(--color-light-2);
  border-radius: 16px;
  padding: 24px;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
}

.tasks-modal-task-item::before {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  width: 4px;
  background: linear-gradient(135deg, var(--color-purple-1) 0%, var(--color-blue-1) 100%);
  transform: scaleY(0);
  transition: transform 0.3s ease;
}

.tasks-modal-task-item:hover {
  border-color: var(--color-purple-1);
  box-shadow: 0 8px 24px rgba(55, 183, 195, 0.15);
  transform: translateY(-2px);
}

.tasks-modal-task-item:hover::before {
  transform: scaleY(1);
}

.tasks-modal-task-header {
  display: flex;
  align-items: flex-start;
  justify-content: space-between;
  gap: 16px;
  margin-bottom: 16px;
}

.tasks-modal-task-title-section {
  flex: 1;
}

.tasks-modal-task-number {
  background: linear-gradient(135deg, var(--color-purple-1) 0%, var(--color-blue-1) 100%);
  color: white;
  padding: 6px 12px;
  border-radius: 8px;
  font-size: var(--text-12);
  font-weight: 700;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  margin-bottom: 8px;
  display: inline-block;
}

.tasks-modal-task-name {
  font-size: var(--text-18);
  font-weight: 700;
  color: var(--color-dark-1);
  margin: 0;
  line-height: 1.3;
  font-family: var(--font-primary);
}

.tasks-modal-task-description {
  color: var(--color-light-1);
  font-size: var(--text-14);
  line-height: 1.6;
  margin-bottom: 20px;
  padding: 16px 20px;
  background: var(--color-light-6);
  border-radius: 10px;
  border-left: 4px solid var(--color-purple-1);
}

.tasks-modal-task-meta {
  display: flex;
  align-items: center;
  gap: 20px;
  flex-wrap: wrap;
}

.tasks-modal-task-meta-item {
  display: flex;
  align-items: center;
  gap: 8px;
  color: var(--color-light-1);
  font-size: var(--text-13);
  font-weight: 500;
}

.tasks-modal-task-meta-icon {
  width: 16px;
  height: 16px;
  color: var(--color-purple-1);
}

/* Empty State */
.tasks-modal-empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
  text-align: center;
  background: linear-gradient(135deg, var(--color-light-6) 0%, var(--color-white) 100%);
  border-radius: 16px;
  border: 2px dashed var(--color-light-2);
}

.tasks-modal-empty-icon {
  width: 64px;
  height: 64px;
  color: var(--color-light-1);
  margin-bottom: 16px;
  opacity: 0.6;
}

.tasks-modal-empty-text {
  font-size: var(--text-16);
  color: var(--color-light-1);
  font-weight: 500;
  margin: 0;
}

/* Modal Footer */
.tasks-modal-footer {
  padding: 28px 36px;
  border-top: 1px solid var(--color-light-2);
  background: linear-gradient(135deg, var(--color-light-6) 0%, var(--color-white) 100%);
}

.tasks-modal-footer-buttons {
  display: flex;
  justify-content: flex-end;
  align-items: center;
}

/* Button Styles */
.tasks-modal-btn {
  padding: 14px 28px;
  border-radius: 10px;
  font-weight: 600;
  cursor: pointer;
  border: none;
  display: flex;
  align-items: center;
  gap: 10px;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  font-size: var(--text-14);
  font-family: var(--font-primary);
  position: relative;
  overflow: hidden;
  min-width: 120px;
  justify-content: center;
}

.tasks-modal-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s;
}

.tasks-modal-btn:hover::before {
  left: 100%;
}

.tasks-modal-btn-close {
  background: linear-gradient(135deg, var(--color-light-1) 0%, var(--color-dark-3) 100%);
  color: white;
  box-shadow: 0 4px 16px rgba(111, 122, 153, 0.3);
}

.tasks-modal-btn-close:hover {
  background: linear-gradient(135deg, var(--color-dark-3) 0%, var(--color-dark-4) 100%);
  transform: translateY(-2px);
  box-shadow: 0 8px 24px rgba(111, 122, 153, 0.4);
}

.tasks-modal-btn:active {
  transform: translateY(0);
}

/* Responsive Design */
@media (max-width: 1024px) {
  .tasks-modal-container {
    max-width: 95vw;
    margin: 10px;
  }
}

@media (max-width: 768px) {
  .tasks-modal-container {
    max-height: 95vh;
    border-radius: 12px;
  }

  .tasks-modal-header,
  .tasks-modal-content,
  .tasks-modal-footer {
    padding: 20px 24px;
  }

  .tasks-modal-header h2 {
    font-size: var(--text-20);
  }

  .tasks-modal-study-info-grid {
    grid-template-columns: 1fr;
    gap: 16px;
  }

  .tasks-modal-task-item {
    padding: 20px;
  }

  .tasks-modal-task-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
  }

  .tasks-modal-task-meta {
    gap: 16px;
  }

  .tasks-modal-btn {
    width: 100%;
    justify-content: center;
  }
}

@media (max-width: 480px) {
  .tasks-modal-content {
    padding: 16px 20px;
  }

  .tasks-modal-study-info,
  .tasks-modal-task-item {
    padding: 16px;
  }

  .tasks-modal-btn {
    padding: 12px 20px;
    font-size: var(--text-13);
  }

  .tasks-modal-header-icon {
    width: 36px;
    height: 36px;
  }

  .tasks-modal-task-name {
    font-size: var(--text-16);
  }

  .tasks-modal-task-meta {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
  }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .tasks-modal-container {
    border: 3px solid var(--color-dark-1);
  }

  .tasks-modal-task-item {
    border-width: 3px;
  }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  .tasks-modal-container,
  .tasks-modal-overlay,
  .tasks-modal-btn,
  .tasks-modal-task-item {
    animation: none;
    transition: none;
  }
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
  .tasks-modal-container {
    background: var(--color-dark-4);
    border-color: var(--color-dark-3);
  }
  
  .tasks-modal-content {
    background: var(--color-dark-4);
  }
  
  .tasks-modal-study-info,
  .tasks-modal-task-item,
  .tasks-modal-empty-state {
    background: linear-gradient(135deg, var(--color-dark-3) 0%, var(--color-dark-4) 100%);
    border-color: var(--color-dark-3);
  }
  
  .tasks-modal-task-description {
    background: var(--color-dark-3);
  }
  
  .tasks-modal-footer {
    background: linear-gradient(135deg, var(--color-dark-3) 0%, var(--color-dark-4) 100%);
  }
}

/* Focus styles for accessibility */
.tasks-modal-close-button:focus,
.tasks-modal-btn:focus {
  outline: 2px solid var(--color-purple-1);
  outline-offset: 2px;
}

/* Print styles */
@media print {
  .tasks-modal-overlay {
    position: static;
    background: none;
    backdrop-filter: none;
  }
  
  .tasks-modal-container {
    box-shadow: none;
    border: 1px solid #ccc;
    max-width: none;
    max-height: none;
  }
  
  .tasks-modal-close-button,
  .tasks-modal-footer {
    display: none;
  }
  
  .tasks-modal-task-item {
    break-inside: avoid;
    page-break-inside: avoid;
  }
}

/* Animation for task items */
.tasks-modal-task-item:nth-child(1) {
  animation: tasks-modal-slideInLeft 0.6s ease-out 0.1s both;
}

.tasks-modal-task-item:nth-child(2) {
  animation: tasks-modal-slideInLeft 0.6s ease-out 0.2s both;
}

.tasks-modal-task-item:nth-child(3) {
  animation: tasks-modal-slideInLeft 0.6s ease-out 0.3s both;
}

.tasks-modal-task-item:nth-child(n+4) {
  animation: tasks-modal-slideInLeft 0.6s ease-out 0.4s both;
}

@keyframes tasks-modal-slideInLeft {
  from {
    opacity: 0;
    transform: translateX(-30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

/* Disable animations for reduced motion */
@media (prefers-reduced-motion: reduce) {
  .tasks-modal-task-item:nth-child(n) {
    animation: none;
  }
}

/* Page Layout */
.hospital-details__content {
  background-color: #f5f7fa; /* Match dashboard background */
}

.hospital-card {
  background-color: #ffffff;
  padding: 30px;
}

.hospital-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30px;
  border-bottom: 1px solid #e5e7eb;
}

.hospital-title {
  font-size: 24px;
  font-weight: 600;
  color: #1a1a1a;
  margin: 0;
}

/* Search Field */
.hospital-search-field {
  position: relative;
  margin-top: 16px;
}

.hospital-search-input {
  width: 100%;
  padding: 14px 18px;
  padding-left: 44px;
  border: 1px solid rgba(55, 183, 195, 0.15);
  border-radius: 12px;
  font-size: 14px;
  transition: all 0.2s ease;
  background-color: rgba(55, 183, 195, 0.03);
}

.hospital-search-input:focus {
  border-color: #37B7C3;
  box-shadow: 0 0 0 4px rgba(55, 183, 195, 0.1);
  background-color: #fff;
}

.hospital-search-icon {
  position: absolute;
  left: 12px;
  top: 50%;
  transform: translateY(-50%);
  color: #9ca3af;
  font-size: 18px;
}

/* Tags Styling */
.hospital-tags-wrapper {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.hospital-tag-item {
  display: inline-block;
  padding: 8px 14px;
  background: rgba(55, 183, 195, 0.08);
  color: #37B7C3;
  border-radius: 10px;
  font-size: 13px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  border: 1px solid rgba(55, 183, 195, 0.12);
}

.hospital-tag-item:hover {
  background: rgba(55, 183, 195, 0.12);
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(55, 183, 195, 0.12);
}

/* Form Cards Grid */
.hospital-forms-grid {
  display: grid;
  gap: 30px;
  grid-template-columns: repeat(4, 1fr);
  margin-bottom: 30px;
}

.hospital-details-header {
  display: flex;
  flex-direction: column;
  margin-bottom: 30px;
  padding-bottom: 15px;
  border-bottom: 1px solid #e5e7eb;
}

.hospital-details-title {
  margin-bottom: 10px;
}

.hospital-details-title h1 {
  font-size: 24px;
  font-weight: 600;
  color: #1a1a1a;
  display: flex;
  align-items: center;
  gap: 10px;
  margin: 0;
}

.editablehospital-container {
  display: flex;
  justify-content: flex-end;
}

.row,
.col-md-6 {
  overflow: visible !important;
  position: relative !important;
}


/* Responsive Adjustments */
@media (max-width: 1400px) {
  .hospital-forms-grid {
      grid-template-columns: repeat(3, 1fr);
  }
}

@media (max-width: 991px) {
  .hospital-forms-grid {
      grid-template-columns: repeat(2, 1fr);
      gap: 20px;
  }

  .editablehospital-container {
      flex-direction: column;
      width: 100%;
  }
}

@media (max-width: 767px) {
  .hospital-forms-grid {
      grid-template-columns: 1fr;
      gap: 15px;
  }
  .hospital-details-title h1 {
      font-size: 20px;
  }
}

/* Form Card Animation */
.hospital-forms-grid > * {
  animation: hospital-fadeIn 0.3s ease-out forwards;
}

@keyframes hospital-fadeIn {
  from {
      opacity: 0;
      transform: translateY(10px);
  }
  to {
      opacity: 1;
      transform: translateY(0);
  }
}

/* Transitions and Hover Effects */
.hospital-hover-shadow-2 {
  transition: all 0.3s ease;
}

.hospital-hover-shadow-2:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 20px rgba(0, 0, 0, 0.08) !important;
}

/* Utility Classes */
.hospital-shadow-1 {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
}

.hospital-border-light {
  border: 1px solid rgba(0, 0, 0, 0.08);
}

.hospital-bg-light-4 {
  background-color: #f5f7fa;
}

/* Loading States */
.hospital-form-checkbox__input:disabled + .hospital-form-checkbox__label {
  opacity: 0.5;
  cursor: not-allowed;
}

/* Tabs Styling */
.hospital-tabs-wrapper {
  display: flex;
  gap: 1rem;
  border-bottom: 1px solid #e5e7eb;
  padding-bottom: 0;
  margin-bottom: 30px;
}

.hospital-tab-button {
  background: none;
  border: none;
  padding: 0.75rem 1.25rem;
  font-size: 1rem;
  color: #6b7280;
  position: relative;
  cursor: pointer;
  transition: all 0.3s ease;
  text-align: center;
  border-radius: 8px 8px 0 0;
}

.hospital-tab-button:hover {
  color: #374151;
  background-color: rgba(55, 183, 195, 0.05);
}

.hospital-tab-button.active {
  color: #37B7C3;
  background-color: rgba(55, 183, 195, 0.08);
}

.hospital-tab-button.active::after {
  content: '';
  position: absolute;
  bottom: -1px;
  left: 0;
  width: 100%;
  height: 3px;
  background-color: #37B7C3;
}

/* Form Styling */
.hospital-form-section {
  margin-bottom: 30px;
}

.hospital-form-row {
  margin-bottom: 20px;
}

/* Button Styles */
.hospital-btn-custom {
  min-width: 150px;
  height: 50px;
  border-radius: 12px;
  background-color: #37B7C3;
  color: #fff;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  text-align: center;
  padding: 0 25px;
  margin-left: 10px;
  cursor: pointer;
  border: none;
  transition: all 0.3s ease;
  font-weight: 500;
  box-shadow: 0 4px 12px rgba(55, 183, 195, 0.15);
}

.hospital-btn-custom:hover {
  background-color: #2d919a;
  transform: translateY(-2px);
  box-shadow: 0 6px 15px rgba(55, 183, 195, 0.2);
}

.hospital-btn-secondary {
  background-color: #f5f7fa;
  color: #4f547b;
  border: 1px solid #e5e7eb;
  box-shadow: none;
}

.hospital-btn-secondary:hover {
  background-color: #e5e7eb;
  color: #1a1a1a;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
}

.hospital-btn-container {
  display: flex;
  justify-content: flex-end;
  margin-top: 30px;
  gap: 10px;
}

/* Disable border for password input */
.hospital-no-border {
  border: none !important;
  background-color: transparent !important;
  box-shadow: none !important;
}

.hospital-page-wrapper {
  display: flex;
  flex-direction: column;
  height: 100%;
  background: #f5f7fa;
}

.hospital-layout {
  display: flex;
  height: 100%;
}

.hospital-tab-button {
  cursor: pointer;
  padding: 0.75rem;
  font-size: 1rem;
  transition: color 0.3s ease;
}

.hospital-tab-button.active {
  color: #37b7c3;
}

/* Responsive Adjustments */
@media (max-width: 991px) {
  .hospital-details__content {
      padding: 20px;
  }
  
  .hospital-card {
      padding: 20px;
  }

  .hospital-header {
      flex-direction: column;
      align-items: flex-start;
      gap: 15px;
  }

  .hospital-btn-container {
      flex-direction: column;
      width: 100%;
  }

  .hospital-btn-custom {
      width: 100%;
      margin-left: 0;
      margin-top: 10px;
  }

  .hospital-tabs-wrapper {
      flex-wrap: wrap;
      gap: 0.5rem;
  }

  .hospital-tab-button {
      flex: 1 0 auto;
      min-width: 120px;
      text-align: center;
  }
}

@media (max-width: 767px) {
  .hospital-details__content {
      padding: 15px;
  }
  
  .hospital-card {
      padding: 15px;
  }
  
  .hospital-tabs-wrapper {
      flex-direction: column;
      gap: 0.5rem;
  }
  
  .hospital-tab-button {
      width: 100%;
      text-align: left;
      border-radius: 8px;
  }
  
  .hospital-tab-button.active::after {
      display: none;
  }
  
  .hospital-tab-button.active {
      background-color: rgba(55, 183, 195, 0.15);
  }
}


.hospital-detail-input {
  display: flex;
  flex-direction: column;
  justify-content: start;
  background:none;
  border: 1px solid #37B7C3;
  border-radius: 4px;
  width: auto;
  padding: 10px;
  color: black;
}

.hospital-detail-input[type="text"],
.hospital-detail-input input[type="number"],
.hospital-detail-input input[type="password"] {
border: none;
outline: none;
background-color: transparent;
padding: 0px;
}

.custom-button{

      display: flex;
    justify-content: flex-end;

}

.hosp-details-btn-custom {
  width: 90px;
  height: 40px;
  border-radius: 43px;
  background-color: #37B7C3;
  color: #fff;
  display: inline-flex;
  align-items: center;
  align-self: flex-end;
  justify-content: center;
  text-align: center;
  line-height: 50px;
  margin-left: 10px;
  cursor: pointer;
  border: none;
  transition: background-color 0.3s ease;
}

.hosp-details-btn-custom:hover {
  background-color: #2d919a;
}
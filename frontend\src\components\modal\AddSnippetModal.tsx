import './AddSnippetModal.css';
import { useState } from 'react';
import { Blocks } from 'lucide-react';
import { useCreateSnippetMutation } from '@/hooks/snippet.query';

const AddSnippetModal = ({ isOpen, onClose, onSave }: { isOpen: boolean, onClose: () => void, onSave: (title: string, subject: string, description: string) => void }) => {
  const [name, setName] = useState('');
  const [description, setDescription] = useState('');
  const [content, setContent] = useState('');
  const createSnippetMutation = useCreateSnippetMutation();

  if (!isOpen) return null;

  const handleSave = () => {
    createSnippetMutation.mutate({ name, description, content });
    onSave(name, description, content);

    // Reset all fields after saving
    setName('');
    setDescription('');
    setContent('');

    onClose();
  };

  // Reset fields when modal is closed without saving
  const handleClose = () => {
    setName('');
    setDescription('');
    setContent('');
    onClose();
  };

  return (
    <div className="modal-overlay">
      <div className="add-snippet-modal-content">
        <h2 className="modal-name">Add New Snippet</h2>
        <input
          type="text"
          placeholder="Snippet Name"
          value={name}
          onChange={(e) => setName(e.target.value)}
        />
        <input
          type="text"
          placeholder="Snippet description"
          value={description}
          onChange={(e) => setDescription(e.target.value)}
        />
        <textarea
          placeholder="Snippet content"
          value={content}
          onChange={(e) => setContent(e.target.value)}
        />
        <div className="modal-actions">
          <button className="cancel-snippet-button" onClick={handleClose}>
            Cancel
          </button>
          <button className="save-snippet-button" onClick={handleSave}>
            Save <Blocks size={16} style={{ marginLeft: "5px" }} />
          </button>
        </div>
      </div>
    </div>
  );
};

export default AddSnippetModal;

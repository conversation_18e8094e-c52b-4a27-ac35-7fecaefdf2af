import React, { useState, useEffect } from "react";
import { motion } from "framer-motion";
import Preloader from "@/components/common/Preloader";
import "./AboutUsPage.css";
import LandingHeader from "@/shared/LandingHeader";
import NurtifyFooter from "@/components/NurtifyFooter";
import ContactUsCTA from "@/components/ContactUsCTA";

const AboutUsPage: React.FC = () => {
  // Typewriter effect state
  const [currentSentenceIndex, setCurrentSentenceIndex] = useState(0);
  const [currentText, setCurrentText] = useState("");
  const [isTyping, setIsTyping] = useState(true);

  const sentences = [
    "Accelerating Recruitment.",
    "Empowering Clinical trial Teams.",
    "Improving Patient Outcomes.",
    "Life science at its best."
  ];

  // Typewriter effect
  useEffect(() => {
    let timeout: NodeJS.Timeout;

    const currentSentence = sentences[currentSentenceIndex];

    if (isTyping) {
      if (currentText.length < currentSentence.length) {
        timeout = setTimeout(() => {
          setCurrentText(currentSentence.substring(0, currentText.length + 1));
        }, 100);
      } else {
        timeout = setTimeout(() => {
          setIsTyping(false);
        }, 2000); // Wait 2 seconds before starting to delete
      }
    } else {
      if (currentText.length > 0) {
        timeout = setTimeout(() => {
          setCurrentText(currentText.substring(0, currentText.length - 1));
        }, 50);
      } else {
        timeout = setTimeout(() => {
          setCurrentSentenceIndex((prev) => (prev + 1) % sentences.length);
          setIsTyping(true);
        }, 500); // Wait 500ms before starting next sentence
      }
    }

    return () => clearTimeout(timeout);
  }, [currentText, currentSentenceIndex, isTyping, sentences]);

  // Define testimonials data
  const testimonials = [
    {
      name: "Dr. Sarah John",
      title: "Clinical Research Director",
      organization: "Parkview Medical Center",
      quote: "Nurtify transformed how we run clinical trials. Recruitment timelines shortened by 45%, data quality improved dramatically, and our team spends less time on paperwork and more time with patients.",
      avatar: "/assets/img/landing/avatar-placeholder.svg"
    },
    {
      name: "Dr. Michael Chen",
      title: "Head of Clinical Operations",
      organization: "Sunrise University Hospital",
      quote: "Since implementing Nurtify, we've seen a 38% increase in patient retention rates. The platform's intuitive design makes it easy for both our staff and trial participants to stay engaged throughout the entire process.",
      avatar: "/assets/img/landing/avatar-placeholder.svg"
    },
    {
      name: "Dr. Emily Rodriguez",
      title: "Research Program Director",
      organization: "Sunrise Medical Center",
      quote: "The real-time data visibility has been a game-changer for our multi-site trials. We can now identify and address issues before they become problems, saving us countless hours and resources.",
      avatar: "/assets/img/landing/avatar-placeholder.svg"
    }
  ];

  // State to track current testimonial
  const [currentTestimonialIndex, setCurrentTestimonialIndex] = useState(0);
  const [isAnimating, setIsAnimating] = useState(false);

  // Auto-rotate testimonials
  React.useEffect(() => {
    const autoRotateInterval = setInterval(() => {
      if (!isAnimating) {
        const currentCard = document.querySelector('.testimonial-card-large');
        if (currentCard) {
          setIsAnimating(true);
          currentCard.classList.add('flip-out-right');

          // Reduced timeout to make transition faster and reduce blank period
          setTimeout(() => {
            const nextIndex = (currentTestimonialIndex + 1) % testimonials.length;
            setCurrentTestimonialIndex(nextIndex);
            currentCard.classList.remove('flip-out-right');
            currentCard.classList.add('flip-in-left');

            // Reduced timeout for smoother transition
            setTimeout(() => {
              currentCard.classList.remove('flip-in-left');
              setIsAnimating(false);
            }, 400);
          }, 300);
        }
      }
    }, 7000); // Change testimonial every 7 seconds

    return () => clearInterval(autoRotateInterval);
  }, [currentTestimonialIndex, isAnimating, testimonials.length]);

  // Core Values data and state
  const coreValues = [
    {
      letter: "N",
      title: "Nurturing Researcher",
      body:
        "We believe that every clinical trial is ultimately about improving human lives. Our technology prioritizes the experience of both participants and clinical professionals, ensuring that innovation serves humanity first."
    },
    {
      letter: "U",
      title: "Unified Collaboration",
      body:
        "We connect sponsors, sites, CROs and patients so multidisciplinary teams operate as one—aligned, coordinated and focused on outcomes."
    },
    {
      letter: "R",
      title: "Relentless Simplification",
      body:
        "We remove friction from complex workflows, automate the repetitive, and keep only what adds value—so teams can focus on care and science."
    },
    {
      letter: "T",
      title: "Transparent Data & Integrity",
      body:
        "We enable real‑time visibility, auditability and trust in data across the trial lifecycle to support faster, safer decisions."
    },
    {
      letter: "I",
      title: "Informed Empowerment",
      body:
        "We equip clinicians and participants with the right information at the right moment to act confidently and responsibly."
    },
    {
      letter: "F",
      title: "Future‑Driven Evolution",
      body:
        "We iterate fast with secure, scalable, modern technology—continuously improving to meet tomorrow’s research needs."
    },
    {
      letter: "Y",
      title: "Yielded Governance",
      body:
        "Compliance by design—respecting privacy, regulation and ethical obligations while enabling progress."
    }
  ];
  const [openValueIndex, setOpenValueIndex] = useState(0);

  return (
    <div>
      <Preloader />
      <div>
        {/* HEADER */}

        {/* HERO SECTION */}
        <div className="hero-container-landing">
          <LandingHeader />
          <div className="hero-split-background" style={{ position: "relative" }}>
            <video
              className="hero-bg-video"
              src="/assets/img/landing/video.mp4"
              autoPlay
              muted
              loop
              playsInline
              preload="auto"
              aria-label="Hero Section Nurtify"
            />

            <div
              className="header__container px-5"
              style={{
                paddingTop: "120px",
              }}
            >
              <div className="hero-content">
                <motion.h1
                  className="hero-title-enhanced"
                  initial={{ opacity: 0, scale: 0.8 }}
                  animate={{
                    opacity: 1,
                    scale: 1,
                    textShadow: [
                      "0px 0px 0px rgba(55, 183, 195, 0)",
                      "0px 0px 20px rgba(55, 183, 195, 0.3)",
                      "0px 0px 0px rgba(55, 183, 195, 0)"
                    ]
                  }}
                  transition={{
                    duration: 0.8,
                    delay: 0.2,
                    textShadow: {
                      duration: 2,
                      repeat: Infinity,
                      repeatType: "reverse"
                    }
                  }}
                >
                  Human-Centered Innovation
                  in Clinical Trials <br />
                </motion.h1>

                <motion.div
                  className="hero-subtitle-enhanced"
                  initial={{ opacity: 0, y: -10 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.4, delay: 0.5 }}
                >
                  <span className="typewriter-text">
                    {currentText}
                    <span className="cursor">|</span>
                  </span>
                  <div className="subtitle-static">
                    The Premier Solution for Source Data Management and Real-Time Clinical<br />
                    trial monitoring in the UK.
                  </div>
                </motion.div>

                <motion.a
                  href="https://meetings.hubspot.com/nurtify?embed=true"
                  className="mt-5"
                  target="_blank" rel="noopener noreferrer"
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.6, delay: 0.8 }}
                >
                  <div className="space-button-container">
                    <button className="space-button">

                      <svg width="18" height="18" viewBox="0 0 18 18" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <g clip-path="url(#clip0_1891_5647)">
                          <path d="M7.14724 9.36237C6.79849 9.24612 6.79849 8.75364 7.14724 8.63739L9.19429 7.95574C9.64429 7.8057 10.0531 7.55291 10.3885 7.2174C10.7238 6.8819 10.9764 6.4729 11.1262 6.02282L11.8078 3.97682C11.9241 3.62807 12.4165 3.62807 12.5328 3.97682L13.2144 6.02388C13.3645 6.47387 13.6173 6.88273 13.9528 7.21806C14.2883 7.55338 14.6973 7.80595 15.1473 7.95574L17.1933 8.63739C17.2697 8.66245 17.3362 8.71099 17.3834 8.77609C17.4305 8.84118 17.4559 8.9195 17.4559 8.99988C17.4559 9.08025 17.4305 9.15857 17.3834 9.22367C17.3362 9.28876 17.2697 9.3373 17.1933 9.36237L15.1463 10.044C14.6964 10.1939 14.2876 10.4466 13.9523 10.7819C13.617 11.1172 13.3644 11.526 13.2144 11.9759L12.5328 14.0229C12.5077 14.0993 12.4592 14.1658 12.3941 14.2129C12.329 14.2601 12.2507 14.2855 12.1703 14.2855C12.0899 14.2855 12.0116 14.2601 11.9465 14.2129C11.8814 14.1658 11.8329 14.0993 11.8078 14.0229L11.1262 11.9759C10.9762 11.526 10.7236 11.1172 10.3883 10.7819C10.053 10.4466 9.64417 10.1939 9.19429 10.044L7.14724 9.36237ZM1.75852 13.4449C1.71275 13.4297 1.67293 13.4005 1.6447 13.3614C1.61647 13.3223 1.60128 13.2754 1.60128 13.2272C1.60128 13.1789 1.61647 13.132 1.6447 13.0929C1.67293 13.0538 1.71275 13.0246 1.75852 13.0094L2.98654 12.6005C3.53397 12.4176 3.96304 11.9886 4.14587 11.4411L4.55486 10.2131C4.57001 10.1673 4.59921 10.1275 4.63829 10.0993C4.67737 10.0711 4.72435 10.0559 4.77257 10.0559C4.82077 10.0559 4.86776 10.0711 4.90684 10.0993C4.94592 10.1275 4.97511 10.1673 4.99027 10.2131L5.39926 11.4411C5.48921 11.7111 5.64081 11.9564 5.84204 12.1577C6.04327 12.3589 6.2886 12.5105 6.55859 12.6005L7.78661 13.0094C7.83238 13.0246 7.8722 13.0538 7.90043 13.0929C7.92866 13.132 7.94385 13.1789 7.94385 13.2272C7.94385 13.2754 7.92866 13.3223 7.90043 13.3614C7.8722 13.4005 7.83238 13.4297 7.78661 13.4449L6.55859 13.8538C6.2886 13.9438 6.04327 14.0954 5.84204 14.2966C5.64081 14.4979 5.48921 14.7432 5.39926 15.0132L4.99027 16.2412C4.97511 16.287 4.94592 16.3268 4.90684 16.355C4.86776 16.3832 4.82078 16.3984 4.77257 16.3984C4.72436 16.3984 4.67737 16.3832 4.63829 16.355C4.59921 16.3268 4.57001 16.287 4.55486 16.2412L4.14587 15.0132C4.05592 14.7432 3.90432 14.4979 3.70309 14.2966C3.50186 14.0954 3.25653 13.9438 2.98654 13.8538L1.75852 13.4449ZM0.649917 5.97421C0.619826 5.96377 0.593732 5.94423 0.575265 5.91828C0.556798 5.89233 0.546875 5.86127 0.546875 5.82942C0.546875 5.79757 0.556798 5.76652 0.575265 5.74057C0.593732 5.71462 0.619826 5.69507 0.649917 5.68464L1.46789 5.41198C1.83355 5.29045 2.11995 5.00405 2.24149 4.63839L2.51414 3.82041C2.52458 3.79032 2.54413 3.76423 2.57007 3.74576C2.59602 3.72729 2.62708 3.71737 2.65893 3.71737C2.69078 3.71737 2.72183 3.72729 2.74778 3.74576C2.77373 3.76423 2.79328 3.79032 2.80371 3.82041L3.07637 4.63839C3.13632 4.81859 3.23745 4.98233 3.37173 5.11662C3.50602 5.2509 3.66976 5.35203 3.84996 5.41198L4.66794 5.68464C4.69803 5.69507 4.72412 5.71462 4.74259 5.74057C4.76106 5.76652 4.77098 5.79757 4.77098 5.82942C4.77098 5.86127 4.76106 5.89233 4.74259 5.91828C4.72412 5.94423 4.69803 5.96377 4.66794 5.97421L3.84996 6.24687C3.66976 6.30681 3.50602 6.40794 3.37173 6.54223C3.23745 6.67651 3.13632 6.84026 3.07637 7.02046L2.80371 7.83738C2.79328 7.86747 2.77373 7.89356 2.74778 7.91203C2.72183 7.9305 2.69078 7.94042 2.65893 7.94042C2.62708 7.94042 2.59602 7.9305 2.57007 7.91203C2.54413 7.89356 2.52458 7.86747 2.51414 7.83738L2.24149 7.0194C2.11995 6.65374 1.83355 6.36734 1.46789 6.24581L0.650974 5.97421L0.649917 5.97421Z" fill="white" />
                        </g>
                        <defs>
                          <clipPath id="clip0_1891_5647">
                            <rect width="16.9091" height="16.9091" fill="white" transform="translate(0.546875 17.4551) rotate(-90)" />
                          </clipPath>
                        </defs>
                      </svg>
                      <span>Book a demo</span>
                    </button>
                  </div>
                </motion.a>
              </div>
            </div>
          </div>
        </div>

        {/* NURSES OVERVIEW SECTION */}
        <section className="nurses-overview-section">
          <div className="container nurses-overview-container">
            <div className="nurses-overview-image">
              <img
                src="/assets/img/about-us/our-story.png"
                alt="Nurtify Nurses"
                className="nurses-image"
                style={{ maxWidth: "100%", height: "auto" }}
              />
            </div>
            <div className="nurses-overview-content">
              <h2 className="nurses-overview-title">Our Story</h2>
              <p className="nurses-overview-description">
                Nurtify is a UK-based digital health platform designed to transform clinical trial management. We connect sponsors, researchers, clinicians, and patients through an intelligent, human-centered ecosystem.
                <br /><br />
                Our goal is to accelerate medical discoveries, reduce administrative burdens, and put the patient experience at the heart of research.
                <br /><br />
                We’re not just building software — we’re building trust, transparency, and a future-ready foundation for the next generation of clinical trials.
              </p>
            </div>
          </div>
        </section>

        {/* CORE VALUES SECTION */}
        <section className="core-values-section">
          <h2 className="core-values-title">Our Core Values</h2>
          <div className="container core-values-container">
            <div className="core-values-left">
              <div className="values-accordion">
                {coreValues.map((v, i) => (
                  <div key={v.letter} className={`value-item ${openValueIndex === i ? "active" : ""}`}>
                    <div
                      className="value-header"
                      onClick={() => setOpenValueIndex(openValueIndex === i ? -1 : i)}
                      aria-expanded={openValueIndex === i}
                      aria-controls={`value-body-${i}`}
                    >
                      <span className="value-letter">{v.letter}</span>
                      <span className="value-title">{v.title}</span>
                      <svg className="chevron" width="18" height="18" viewBox="0 0 24 24" fill="none" aria-hidden="true">
                        <path d="M6 9l6 6 6-6" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
                      </svg>
                    </div>
                    <div id={`value-body-${i}`} className="value-body">
                      {v.body}
                    </div>
                  </div>
                ))}
              </div>
            </div>

            <div className="core-values-right">
              <div className="core-values-image-wrap">
                <img
                  src="/assets/img/about-us/patientDoctor.png"
                  alt="Clinicians collaborating with patients"
                />
              </div>
            </div>
          </div>
        </section>

        {/* VISION AND MISSION SECTION */}
        <section className="vision-mission-section">
          <div className="container">
            <div className="vision-mission-container">
              <div className="vision-card">
                <div className="vision-icon">
                  <svg width="64" height="64" viewBox="0 0 64 64" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path opacity="0.3" d="M31.9987 3.33398C16.1915 3.33398 3.33203 16.1934 3.33203 32.0006C3.33203 47.8078 16.1915 60.6673 31.9987 60.6673C47.8059 60.6673 60.6654 47.8078 60.6654 32.0006C60.6654 16.1934 47.8059 3.33398 31.9987 3.33398ZM31.9987 56.6673C18.3971 56.6673 7.33203 45.6022 7.33203 32.0006C7.33203 18.3991 18.3971 7.33398 31.9987 7.33398C45.6003 7.33398 56.6654 18.3991 56.6654 32.0006C56.6654 45.6022 45.6003 56.6673 31.9987 56.6673Z" fill="white" />
                    <path d="M32 14C22.0755 14 14 22.0755 14 32C14 41.9245 22.0755 50 32 50C41.9245 50 50 41.9245 50 32C50 22.0755 41.9245 14 32 14ZM32 46C24.2813 46 18 39.7187 18 32C18 24.2813 24.2813 18 32 18C39.7187 18 46 24.2813 46 32C46 39.7187 39.7187 46 32 46ZM32 24.6667C27.9557 24.6667 24.6667 27.9557 24.6667 32C24.6667 36.0443 27.9557 39.3333 32 39.3333C36.0443 39.3333 39.3333 36.0443 39.3333 32C39.3333 27.9557 36.0443 24.6667 32 24.6667ZM32 35.3333C30.1613 35.3333 28.6667 33.8387 28.6667 32C28.6667 30.1613 30.1613 28.6667 32 28.6667C33.8387 28.6667 35.3333 30.1613 35.3333 32C35.3333 33.8387 33.8387 35.3333 32 35.3333Z" fill="white" />
                  </svg>
                </div>
                <h2 className="vision-title">Our Vision</h2>
                <p className="vision-description">
                  To transform clinical research into a seamless, human-centered experience where groundbreaking medical discoveries happen faster, safer, and with greater impact for patients worldwide.
                </p>
              </div>

              <div className="mission-content">
                <div className="mission-icon">
                  <svg width="64" height="64" viewBox="0 0 64 64" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M22.731 12.3723L21.4758 18.6493L33.4134 30.5869C34.1947 31.3683 34.1947 32.6339 33.4134 33.4149C33.0227 33.8056 32.5123 34.0008 31.9992 34.0008C31.4862 34.0008 30.9758 33.8056 30.5851 33.4149L18.6475 21.4773L12.3707 22.7328C11.9336 22.8203 11.4816 22.6832 11.1664 22.3683L5.96591 17.1677C5.21284 16.4149 5.60297 15.1264 6.64724 14.9176L12.6659 13.7139C13.1936 13.6083 13.6062 13.1957 13.7118 12.668L14.9155 6.64907C15.1243 5.60481 16.4128 5.21494 17.1659 5.96774L22.3662 11.1683C22.6814 11.4835 22.8184 11.9352 22.731 12.3723Z" fill="#088395" />
                    <path opacity="0.5" d="M3.33203 32.0006C3.33203 29.6945 3.6355 27.4628 4.15203 25.3129L7.57416 28.7351C7.4315 29.8062 7.33203 30.8911 7.33203 32.0009C7.33203 45.6025 18.3971 56.6676 31.9987 56.6676C45.6003 56.6676 56.6654 45.6025 56.6654 32.0009C56.6654 18.3993 45.6003 7.33398 31.9987 7.33398C30.8891 7.33398 29.8043 7.43318 28.7328 7.57612L25.3107 4.15398C27.4606 3.63745 29.6923 3.33398 31.9984 3.33398C47.8056 3.33398 60.6651 16.1934 60.6651 32.0006C60.6651 47.8078 47.8056 60.6673 31.9984 60.6673C16.1912 60.6673 3.33203 47.8078 3.33203 32.0006ZM31.9987 46.0006C24.3334 46.0006 18.1008 39.8022 18.0144 32.1572L15.1158 29.2585L14.2019 29.4417C14.0819 30.2798 13.9984 31.13 13.9984 32.0009C13.9984 41.9254 22.0739 50.0009 31.9984 50.0009C41.923 50.0009 49.9984 41.9254 49.9984 32.0009C49.9984 22.0764 41.923 14.0009 31.9984 14.0009C31.1275 14.0009 30.2776 14.0841 29.4392 14.2044L29.256 15.1182L32.1547 18.0169C39.7998 18.1031 45.9982 24.3358 45.9982 32.0012C45.9982 39.7198 39.7174 46.0006 31.9987 46.0006Z" fill="#088395" />
                  </svg>
                </div>
                <h2 className="mission-title">Our Mission</h2>
                <p className="mission-intro">We NURTIFY clinical research by focusing on:</p>
                <ul className="mission-list">
                  <li>Nurturing Humanity</li>
                  <li>Unified Collaboration</li>
                  <li>Relentless Simplification</li>
                  <li>Transparent Data & Integrity</li>
                  <li>Informed Empowerment</li>
                  <li>Future-Driven Evolution</li>
                  <li>Yielded Governance</li>
                </ul>
              </div>
            </div>
          </div>
        </section>

        {/* PEOPLE BEHIND THE PLATFORM SECTION */}
        <section className="team-section">
          <div className="container">
            <div className="team-header">
              <h2 className="team-title">The People Behind the Platform</h2>
              <p className="team-subtitle">
                Our team is made up of innovators, clinicians, researchers, and technologists<br />
                committed to transforming clinical trials from the inside out.
              </p>
              <div className="team-divider"></div>
            </div>

            <div className="team-grid">
              {/* Top row - 2 larger cards with horizontal layout */}
              <div className="team-row team-row-large">
                <div className="team-member-card team-member-card-large">
                  <a href="#" className="linkedin-icon">
                    <svg height="800px" width="800px" version="1.1" id="Layer_1" xmlns="http://www.w3.org/2000/svg" xmlnsXlink="http://www.w3.org/1999/xlink"
                      viewBox="0 0 291.319 291.319" xmlSpace="preserve">
                      <g>
                        <path style={{ fill: "#0E76A8" }} d="M145.659,0c80.45,0,145.66,65.219,145.66,145.66s-65.21,145.659-145.66,145.659S0,226.1,0,145.66
      S65.21,0,145.659,0z"/>
                        <path style={{ fill: "#FFFFFF" }} d="M82.079,200.136h27.275v-90.91H82.079V200.136z M188.338,106.077
      c-13.237,0-25.081,4.834-33.483,15.504v-12.654H127.48v91.21h27.375v-49.324c0-10.424,9.55-20.593,21.512-20.593
      s14.912,10.169,14.912,20.338v49.57h27.275v-51.6C218.553,112.686,201.584,106.077,188.338,106.077z M95.589,100.141
      c7.538,0,13.656-6.118,13.656-13.656S103.127,72.83,95.589,72.83s-13.656,6.118-13.656,13.656S88.051,100.141,95.589,100.141z"/>
                      </g>
                    </svg>
                  </a>
                  <div className="team-member-image-large">
                    <img src="/assets/img/about-us/user-image.png" alt="Team Member" />
                  </div>
                  <div className="team-member-content-large">
                    <h3 className="team-member-name">Name</h3>
                    <p className="team-member-title">CEO</p>
                    <p className="team-member-description">
                      Our team is made up of innovators, clinicians, researchers, and technologists committed to transforming clinical trials from the inside out. Our team is made up of innovators, clinicians, researchers, and technologists committed to transforming clinical trials from.
                    </p>
                  </div>
                </div>

                <div className="team-member-card team-member-card-large">
                  <a href="#" className="linkedin-icon">
                    <svg height="800px" width="800px" version="1.1" id="Layer_1" xmlns="http://www.w3.org/2000/svg" xmlnsXlink="http://www.w3.org/1999/xlink"
                      viewBox="0 0 291.319 291.319" xmlSpace="preserve">
                      <g>
                        <path style={{ fill: "#0E76A8" }} d="M145.659,0c80.45,0,145.66,65.219,145.66,145.66s-65.21,145.659-145.66,145.659S0,226.1,0,145.66
      S65.21,0,145.659,0z"/>
                        <path style={{ fill: "#FFFFFF" }} d="M82.079,200.136h27.275v-90.91H82.079V200.136z M188.338,106.077
      c-13.237,0-25.081,4.834-33.483,15.504v-12.654H127.48v91.21h27.375v-49.324c0-10.424,9.55-20.593,21.512-20.593
      s14.912,10.169,14.912,20.338v49.57h27.275v-51.6C218.553,112.686,201.584,106.077,188.338,106.077z M95.589,100.141
      c7.538,0,13.656-6.118,13.656-13.656S103.127,72.83,95.589,72.83s-13.656,6.118-13.656,13.656S88.051,100.141,95.589,100.141z"/>
                      </g>
                    </svg>
                  </a>
                  <div className="team-member-image-large">
                    <img src="/assets/img/about-us/user-image.png" alt="Team Member" />
                  </div>
                  <div className="team-member-content-large">
                    <h3 className="team-member-name">Name</h3>
                    <p className="team-member-title">CEO</p>
                    <p className="team-member-description">
                      Our team is made up of innovators, clinicians, researchers, and technologists committed to transforming clinical trials from the inside out. Our team is made up of innovators, clinicians, researchers, and technologists committed to transforming clinical trials from.
                    </p>
                  </div>
                </div>
              </div>

              {/* Bottom row - 4 smaller cards with vertical layout */}
              <div className="team-row team-row-small">
                <div className="team-member-card team-member-card-small">
                  <a href="#" className="linkedin-icon">
                    <svg height="800px" width="800px" version="1.1" id="Layer_1" xmlns="http://www.w3.org/2000/svg" xmlnsXlink="http://www.w3.org/1999/xlink"
                      viewBox="0 0 291.319 291.319" xmlSpace="preserve">
                      <g>
                        <path style={{ fill: "#0E76A8" }} d="M145.659,0c80.45,0,145.66,65.219,145.66,145.66s-65.21,145.659-145.66,145.659S0,226.1,0,145.66
      S65.21,0,145.659,0z"/>
                        <path style={{ fill: "#FFFFFF" }} d="M82.079,200.136h27.275v-90.91H82.079V200.136z M188.338,106.077
      c-13.237,0-25.081,4.834-33.483,15.504v-12.654H127.48v91.21h27.375v-49.324c0-10.424,9.55-20.593,21.512-20.593
      s14.912,10.169,14.912,20.338v49.57h27.275v-51.6C218.553,112.686,201.584,106.077,188.338,106.077z M95.589,100.141
      c7.538,0,13.656-6.118,13.656-13.656S103.127,72.83,95.589,72.83s-13.656,6.118-13.656,13.656S88.051,100.141,95.589,100.141z"/>
                      </g>
                    </svg>
                  </a>
                  <div className="team-member-image-small">
                    <img src="/assets/img/about-us/user-image.png" alt="Team Member" />
                  </div>
                  <div className="team-member-content-small">
                    <h3 className="team-member-name">Name</h3>
                    <p className="team-member-title">CEO</p>
                    <p className="team-member-description">
                      Our team is made up of innovators, clinicians, researchers, and technologists committed to transforming clinical trials from the inside out.
                    </p>
                  </div>
                </div>

                <div className="team-member-card team-member-card-small">
                  <a href="#" className="linkedin-icon">
                    <svg height="800px" width="800px" version="1.1" id="Layer_1" xmlns="http://www.w3.org/2000/svg" xmlnsXlink="http://www.w3.org/1999/xlink"
                      viewBox="0 0 291.319 291.319" xmlSpace="preserve">
                      <g>
                        <path style={{ fill: "#0E76A8" }} d="M145.659,0c80.45,0,145.66,65.219,145.66,145.66s-65.21,145.659-145.66,145.659S0,226.1,0,145.66
      S65.21,0,145.659,0z"/>
                        <path style={{ fill: "#FFFFFF" }} d="M82.079,200.136h27.275v-90.91H82.079V200.136z M188.338,106.077
      c-13.237,0-25.081,4.834-33.483,15.504v-12.654H127.48v91.21h27.375v-49.324c0-10.424,9.55-20.593,21.512-20.593
      s14.912,10.169,14.912,20.338v49.57h27.275v-51.6C218.553,112.686,201.584,106.077,188.338,106.077z M95.589,100.141
      c7.538,0,13.656-6.118,13.656-13.656S103.127,72.83,95.589,72.83s-13.656,6.118-13.656,13.656S88.051,100.141,95.589,100.141z"/>
                      </g>
                    </svg>
                  </a>
                  <div className="team-member-image-small">
                    <img src="/assets/img/about-us/user-image.png" alt="Team Member" />
                  </div>
                  <div className="team-member-content-small">
                    <h3 className="team-member-name">Name</h3>
                    <p className="team-member-title">CEO</p>
                    <p className="team-member-description">
                      Our team is made up of innovators, clinicians, researchers, and technologists committed to transforming clinical trials from the inside out.
                    </p>
                  </div>
                </div>

                <div className="team-member-card team-member-card-small">
                  <a href="#" className="linkedin-icon">
                    <svg height="800px" width="800px" version="1.1" id="Layer_1" xmlns="http://www.w3.org/2000/svg" xmlnsXlink="http://www.w3.org/1999/xlink"
                      viewBox="0 0 291.319 291.319" xmlSpace="preserve">
                      <g>
                        <path style={{ fill: "#0E76A8" }} d="M145.659,0c80.45,0,145.66,65.219,145.66,145.66s-65.21,145.659-145.66,145.659S0,226.1,0,145.66
      S65.21,0,145.659,0z"/>
                        <path style={{ fill: "#FFFFFF" }} d="M82.079,200.136h27.275v-90.91H82.079V200.136z M188.338,106.077
      c-13.237,0-25.081,4.834-33.483,15.504v-12.654H127.48v91.21h27.375v-49.324c0-10.424,9.55-20.593,21.512-20.593
      s14.912,10.169,14.912,20.338v49.57h27.275v-51.6C218.553,112.686,201.584,106.077,188.338,106.077z M95.589,100.141
      c7.538,0,13.656-6.118,13.656-13.656S103.127,72.83,95.589,72.83s-13.656,6.118-13.656,13.656S88.051,100.141,95.589,100.141z"/>
                      </g>
                    </svg>
                  </a>
                  <div className="team-member-image-small">
                    <img src="/assets/img/about-us/user-image.png" alt="Team Member" />
                  </div>
                  <div className="team-member-content-small">
                    <h3 className="team-member-name">Name</h3>
                    <p className="team-member-title">CEO</p>
                    <p className="team-member-description">
                      Our team is made up of innovators, clinicians, researchers, and technologists committed to transforming clinical trials from the inside out.
                    </p>
                  </div>
                </div>

                <div className="team-member-card team-member-card-small">
                  <a href="#" className="linkedin-icon">
                    <svg height="800px" width="800px" version="1.1" id="Layer_1" xmlns="http://www.w3.org/2000/svg" xmlnsXlink="http://www.w3.org/1999/xlink"
                      viewBox="0 0 291.319 291.319" xmlSpace="preserve">
                      <g>
                        <path style={{ fill: "#0E76A8" }} d="M145.659,0c80.45,0,145.66,65.219,145.66,145.66s-65.21,145.659-145.66,145.659S0,226.1,0,145.66
      S65.21,0,145.659,0z"/>
                        <path style={{ fill: "#FFFFFF" }} d="M82.079,200.136h27.275v-90.91H82.079V200.136z M188.338,106.077
      c-13.237,0-25.081,4.834-33.483,15.504v-12.654H127.48v91.21h27.375v-49.324c0-10.424,9.55-20.593,21.512-20.593
      s14.912,10.169,14.912,20.338v49.57h27.275v-51.6C218.553,112.686,201.584,106.077,188.338,106.077z M95.589,100.141
      c7.538,0,13.656-6.118,13.656-13.656S103.127,72.83,95.589,72.83s-13.656,6.118-13.656,13.656S88.051,100.141,95.589,100.141z"/>
                      </g>
                    </svg>
                  </a>
                  <div className="team-member-image-small">
                    <img src="/assets/img/about-us/user-image.png" alt="Team Member" />
                  </div>
                  <div className="team-member-content-small">
                    <h3 className="team-member-name">Name</h3>
                    <p className="team-member-title">CEO</p>
                    <p className="team-member-description">
                      Our team is made up of innovators, clinicians, researchers, and technologists committed to transforming clinical trials from the inside out.
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </section>

        <section id="career" className="career-section">
          <div className="container">
            <div className="career-header">
              <h2 className="career-title">Open Roles and Internship</h2>
              <p className="career-subtitle">
                We're building a future where clinical trials are smarter, faster, and more human. If <br />
                you're passionate about healthcare innovation, explore our open positions and be part
                <br />
                of the journey.
              </p>
            </div>

            <div className="career-roles-container">
              <div className="career-role-item">
                <span className="role-title">Job Role here</span>
                <a href="mailto:<EMAIL>?subject=Job Application" className="apply-button">
                  Apply here
                  <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M5 12h14M12 5l7 7-7 7" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
                  </svg>
                </a>
              </div>

              <div className="career-role-item">
                <span className="role-title">Job Role here</span>
                <a href="mailto:<EMAIL>?subject=Job Application" className="apply-button">
                  Apply here
                  <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M5 12h14M12 5l7 7-7 7" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
                  </svg>
                </a>
              </div>

              <div className="career-role-item">
                <span className="role-title">Job Role here</span>
                <a href="mailto:<EMAIL>?subject=Job Application" className="apply-button">
                  Apply here
                  <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M5 12h14M12 5l7 7-7 7" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
                  </svg>
                </a>
              </div>
            </div>
          </div>
        </section>


        {/* SERVICE SECTION */}
        <ContactUsCTA />
        <br />
        <NurtifyFooter />
      </div>
    </div>
  );
};

export default AboutUsPage;

import { X, FileText, Calendar, Clock } from "lucide-react";
import { SelectedTask } from "@/services/api/delegation.service";
import "./TasksModal.css";

interface TasksModalProps {
  isOpen: boolean;
  tasks: SelectedTask[];
  studyName: string;
  teamMemberName: string;
  onClose: () => void;
}

export default function TasksModal({
  isOpen,
  tasks,
  studyName,
  teamMemberName,
  onClose
}: TasksModalProps) {
  if (!isOpen) return null;

  const handleClose = () => {
    onClose();
  };

  return (
    <div className="tasks-modal-overlay">
      <div className="tasks-modal-container">
        <div className="tasks-modal-header">
          <div className="tasks-modal-header-content">
            <div className="tasks-modal-header-icon">
              <FileText size={20} />
            </div>
            <h2>Selected Tasks</h2>
          </div>
          <button className="tasks-modal-close-button" onClick={handleClose}>
            <X size={20} />
          </button>
        </div>
        
        <div className="tasks-modal-content">
          <div className="tasks-modal-study-info">
            <div className="tasks-modal-study-info-grid">
              <div className="tasks-modal-info-item">
                <div className="tasks-modal-info-label">Study</div>
                <div className="tasks-modal-info-value">{studyName}</div>
              </div>
              <div className="tasks-modal-info-item">
                <div className="tasks-modal-info-label">Team Member</div>
                <div className="tasks-modal-info-value">{teamMemberName}</div>
              </div>
            </div>
          </div>
          
          <div className="tasks-modal-tasks-section">
            <div className="tasks-modal-tasks-header">
              <h3>Tasks</h3>
              <div className="tasks-modal-tasks-count">
                {tasks?.length || 0} {tasks?.length === 1 ? 'Task' : 'Tasks'}
              </div>
            </div>
            
            {tasks && tasks.length > 0 ? (
              <div className="tasks-modal-tasks-list">
                {tasks.map((task, index) => (
                  <div key={task.uuid} className="tasks-modal-task-item">
                    <div className="tasks-modal-task-header">
                      <div className="tasks-modal-task-title-section">
                        <div className="tasks-modal-task-number">Task {index + 1}</div>
                        <h4 className="tasks-modal-task-name">{task.name}</h4>
                      </div>
                    </div>
                    <div className="tasks-modal-task-description">
                      {task.description}
                    </div>
                    <div className="tasks-modal-task-meta">
                      <div className="tasks-modal-task-meta-item">
                        <Calendar size={14} className="tasks-modal-task-meta-icon" />
                        <span>Created: {new Date(task.created_at).toLocaleDateString()}</span>
                      </div>
                      <div className="tasks-modal-task-meta-item">
                        <Clock size={14} className="tasks-modal-task-meta-icon" />
                        <span>Updated: {new Date(task.updated_at).toLocaleDateString()}</span>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <div className="tasks-modal-empty-state">
                <FileText size={48} className="tasks-modal-empty-icon" />
                <p className="tasks-modal-empty-text">No tasks selected for this delegation</p>
              </div>
            )}
          </div>
        </div>
        
        <div className="tasks-modal-footer">
          <div className="tasks-modal-footer-buttons">
            <button 
              type="button"
              className="tasks-modal-btn tasks-modal-btn-close"
              onClick={handleClose}
            >
              Close
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}

.comment-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.comment-modal-content {
  background-color: white;
  padding: 2rem;
  border-radius: 8px;
  max-width: 500px;
  width: 90%;
}

.comment-modal-title {
  color: #333;
  margin-bottom: 1.5rem;
  font-size: 1.5rem;
}

.comment-modal-body {
  margin-bottom: 1.5rem;
}

.comment-modal-body label {
  display: block;
  margin-bottom: 0.5rem;
  color: #666;
}

.comment-modal-body textarea {
  width: 100%;
  padding: 0.75rem;
  border: 1px solid #ddd;
  border-radius: 4px;
  resize: vertical;
  min-height: 100px;
}

.comment-modal-actions {
  display: flex;
  justify-content: flex-end;
  gap: 1rem;
}

.comment-modal-button {
  padding: 0.5rem 1rem;
  border-radius: 4px;
  border: none;
  cursor: pointer;
  font-weight: 500;
}

.comment-modal-button.cancel {
  background-color: #e0e0e0;
  color: #333;
}

.comment-modal-button.submit {
  background-color: #3dc6d6;
  color: white;
}

.comment-modal-button:hover {
  opacity: 0.9;
}
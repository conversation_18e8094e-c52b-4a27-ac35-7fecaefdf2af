 
import React, { useState } from "react";
import { useNavigate } from "react-router-dom";
import { useConversationsByPatient } from "@/hooks/conversation.query";
import useSelectedPatientStore from "@/store/SelectedPatientState";
import DataTable, { DataTableSearch } from "@/components/common/DataTable";
import { MessageSquare } from "lucide-react";
import "./patientConversations.css";

// Define interface for conversation data
interface ConversationData {
  uuid: string;
  status: string;
  created_at: string;
  updated_at: string;
  last_update_person?: string;
  comments?: string;
  patient_details: {
    first_name: string;
    last_name: string;
  };
}

const PatientConversations: React.FC = () => {
  const navigate = useNavigate();
  const { selectedPatient, isLoading: patientLoading } = useSelectedPatientStore();
  const { data: conversations, isLoading: convLoading, error } = useConversationsByPatient(
    selectedPatient?.uuid || ""
  );
  const [filteredConversations, setFilteredConversations] = useState<ConversationData[]>([]);
  
  console.log("conversations", conversations);
  
  const handleConversationClick = (conversationUuid: string) => {
    navigate(`/org/dashboard/patient-board/patient-conversation-details/${conversationUuid}`);
  };

  if (patientLoading || convLoading) {
    return <div className="loading">Loading conversations...</div>;
  }

  if (!selectedPatient) {
    return <div className="error">No patient selected</div>;
  }

  if (error) {
    return <div className="error">Error loading conversations: {(error as Error).message}</div>;
  }

  if (!conversations || conversations.length === 0) {
    return <div className="no-studies">
      You are not currently enrolled in any conversations.
    </div>;
  }

  return (
    <div className="content-wrapper js-content-wrapper">
      <div className="bg-light-4 px-3 py-5">
        <div className="container-fluid py-6 px-6">
          <div className="patient-details-container">
            <div className="patient-details-header">
              <h1 className="page-title">Conversations for {selectedPatient.first_name} {selectedPatient.last_name}</h1>
            </div>
            
            <div style={{ 
              display: 'flex', 
              justifyContent: 'space-between', 
              alignItems: 'center',
              marginBottom: '20px',
            }}>
              <h3 style={{ margin: 0, fontSize: '18px', fontWeight: 600 }}></h3>
              <div style={{ width: '300px' }}>
                <DataTableSearch
                  data={conversations as ConversationData[]}
                  onFilter={setFilteredConversations}
                  placeholder="Search conversations..."
                />
              </div>
            </div>
            
            <DataTable<ConversationData>
          data={filteredConversations.length > 0 ? filteredConversations : conversations as ConversationData[]}
          columns={[
            {
              key: "patient_details" as keyof ConversationData,
              header: "Patient",
              render: (value: any) => {
                if (value && typeof value === 'object' && 'first_name' in value && 'last_name' in value) {
                  return `${value.first_name} ${value.last_name}`;
                }
                return "Unknown";
              }
            },
            {
              key: "status" as keyof ConversationData,
              header: "Status"
            },
            {
              key: "created_at" as keyof ConversationData,
              header: "Created At",
              render: (value: any) => {
                if (value && typeof value === 'string') {
                  return new Date(value).toLocaleDateString();
                }
                return "N/A";
              }
            },
            {
              key: "updated_at" as keyof ConversationData,
              header: "Last Updated",
              render: (value: any) => {
                if (value && typeof value === 'string') {
                  return new Date(value).toLocaleDateString();
                }
                return "N/A";
              }
            },
            {
              key: "last_update_person" as keyof ConversationData,
              header: "Last Update By",
              render: (value: any) => value || "N/A"
            },
            {
              key: "comments" as keyof ConversationData,
              header: "Comments",
              render: (value: any) => value || "No comments"
            }
          ]}
          actions={[
            {
              icon: <MessageSquare size={16} />,
              tooltipText: "View Conversation",
              onClick: (row) => handleConversationClick(row.uuid)
            }
          ]}
            />
          </div>
        </div>
      </div>
    </div>
  );
};

export default PatientConversations;

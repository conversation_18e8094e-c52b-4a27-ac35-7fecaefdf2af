import { create } from 'zustand';

// Define the state interface
interface PrescriptionState {
  patient_uuid:string;
  drug_name: string;
  height: number | null;
  weight: number | null;
  dose: number | null;
  unit: string;
  route: string;
  frequency: string;
  allergic_patient: boolean; 
  confirmModalOpen: boolean;
  setField: <K extends keyof Omit<PrescriptionState, 'setField' | 'resetForm' | 'setConfirmModalOpen'>>(
    field: K,
    value: PrescriptionState[K]
  ) => void;
  resetForm: () => void;
  setConfirmModalOpen: (value: boolean) => void;
}

// Create the store
export const usePrescriptionStore = create<PrescriptionState>((set) => ({
  patient_uuid:'',
  drug_name: '',
  height: null,
  weight: null,
  dose: null,
  unit: '',
  route: '',
  frequency: '',
  allergic_patient:false,
  confirmModalOpen: false,

  setField: (field, value) =>
    set((state) => ({
      ...state,
      [field]: value,
    })),

  resetForm: () =>
    set({
      drug_name: '',
      height: null,
      weight: null,
      dose: null,
      unit: '',
      route: '',
      frequency: '',
      allergic_patient:false,
      confirmModalOpen: false,
    }),

  setConfirmModalOpen: (value) =>
    set((state) => ({
      ...state,
      confirmModalOpen: value,
    })),
}));
import React, { useState } from "react";
import "./mypatients.css";
import "./patient-actions.css";
import {
  User,
  Users,
  FileText,
  ClipboardList,
  HelpCircle,
  LayoutDashboard,
  Eye,
  UserMinus
} from "lucide-react";
import { format } from "date-fns";
import PatientSearchModal from "@/components/modal/PatientSearchModal";
import AddPatientFormModal from "@/components/modal/AddPatientFormModal";
import { useMyPatientsQuery, useRemoveFavoritePatientMutation } from "@/hooks/patient.query";
import { Patient as PatientData } from "@/services/api/types.ts";
import { useNavigate } from 'react-router-dom';
import useSelectedPatientStore from '@/store/SelectedPatientState';
import { useAddPatientAccessLogMutation } from '@/hooks/patient.query';
import { useQueryClient } from "@tanstack/react-query";
import { PATIENT_KEYS } from "@/hooks/keys";
import DataTable, { Column } from '@/components/common/DataTable';

interface ExtendedPatient extends PatientData {
  fullName: string;
  actions?: string;
  studies?: string;
}

interface SidebarItem {
  id: string;
  label: string;
  icon: JSX.Element;
  active?: boolean;
  path?: string;
}

const sidebarItems: SidebarItem[] = [
  { id: 'dashboard', label: 'Dashboard', icon: <LayoutDashboard size={20} />, path: '/clinical' },
  { id: 'manage-patient', label: 'Manage Patient', icon: <User size={20} />, path: '/my-patients', active: true },
  { id: 'patient-visits', label: 'Patient Visits', icon: <Users size={20} />, path: '/patients' },
  { id: 'create-worksheet', label: 'Create Worksheet', icon: <FileText size={20} />, path: '/survey-forms' },
  { id: 'nursing-care-plan', label: 'Nursing Care Plan', icon: <ClipboardList size={20} />, path: '/forms' },
  { id: 'my-worksheet', label: 'My Worksheet', icon: <FileText size={20} />, path: '/templates' },
  { id: 'help-support', label: 'Help and Support', icon: <HelpCircle size={20} />, path: '#' }
];

interface MyPatientsSidebarProps {
  isMinimized: boolean;
  onToggleMinimize: () => void;
  onItemClick: (item: SidebarItem) => void;
  currentPath: string;
}

const MyPatientsSidebar: React.FC<MyPatientsSidebarProps> = ({
  isMinimized,
  onToggleMinimize,
  onItemClick,
  currentPath
}) => {
  return (
    <div className={`mypatients-sidebar ${isMinimized ? 'mypatients-sidebar-minimized' : ''}`}>
      <div className="mypatients-sidebar-header">
        <button className="mypatients-minimize-btn" onClick={onToggleMinimize}>
          {isMinimized ? '→' : '←'} {!isMinimized && 'Minimize'}
        </button>
      </div>

      <ul className="mypatients-sidebar-menu">
        {sidebarItems.map((item) => {
          const isActive = item.active || item.path === currentPath;
          return (
            <li key={item.id} className="mypatients-sidebar-item">
              <button
                className={`mypatients-sidebar-link ${isActive ? 'mypatients-active' : ''}`}
                onClick={() => onItemClick(item)}
              >
                <span className="mypatients-sidebar-icon">{item.icon}</span>
                {!isMinimized && item.label}
              </button>
            </li>
          );
        })}
      </ul>
    </div>
  );
};

const MyPatients: React.FC = () => {
  const { data = [], isLoading, error } = useMyPatientsQuery();

  // Debug logging
  console.log('My Patients Query - Data:', data);
  console.log('My Patients Query - Loading:', isLoading);
  console.log('My Patients Query - Error:', error);
  const [isSearchModalOpen, setIsSearchModalOpen] = useState(false);
  const [isAddPatientModalOpen, setIsAddPatientModalOpen] = useState(false);
  const [isMinimized, setIsMinimized] = useState(false);
  const navigate = useNavigate();
  const { setSelectedPatient } = useSelectedPatientStore();
  const addPatientAccessLogMutation = useAddPatientAccessLogMutation();
  const removeFavoritePatientMutation = useRemoveFavoritePatientMutation();
  const queryClient = useQueryClient();

  const handleToggleMinimize = () => {
    setIsMinimized(!isMinimized);
  };

  const handleSidebarItemClick = (item: SidebarItem) => {
    if (item.path && item.path !== '#') {
      navigate(item.path);
    }
  };

  const handleSearchPatient = () => {
    setIsSearchModalOpen(true);
  };

  const handleSelectPatient = (patient: PatientData) => {
  setSelectedPatient(patient);
  addPatientAccessLogMutation.mutate({
    patient_uuid: patient.uuid,
    access_type: 'view',
  });
  // Invalidate the favorite status query to force a fresh fetch in the medical dashboard
  queryClient.invalidateQueries({
    queryKey: [PATIENT_KEYS.CHECK_FAVORITE, patient.uuid]
  });
  navigate('/org/dashboard/patient-board');
};


  const handleRemovePatient: (patientUuid: string) => void = (patientUuid: string) => {
    // Find the patient in the data to get the favorite_uuid
    const patient = data.find(p => p.uuid === patientUuid);
    if (patient && (patient as any).favorite_uuid) {
      console.log('Removing patient from favorites:', patientUuid, 'favorite_uuid:', (patient as any).favorite_uuid);
      removeFavoritePatientMutation.mutate((patient as any).favorite_uuid);
    } else {
      console.error('Could not find favorite_uuid for patient:', patientUuid);
    }
  };

  // No custom search, so filteredData is just data
  const filteredData = data;

  const MyPatientColumns: Array<{
    key: keyof ExtendedPatient | 'actions';
    header: string;
    hidden?: boolean;
    render?: (value: unknown, row?: ExtendedPatient) => React.ReactNode;
  }> = [
    { key: "uuid", header: "ID", hidden: true },
    {
      key: "fullName",
      header: "Name",
      render: (_value: unknown, row?: ExtendedPatient) => {
        return `${row?.first_name || ''} ${row?.last_name || ''}`;
      },
    },
    { key: "nhs_number", header: "NHS Number" },
    { key: "medical_record_number", header: "MRN" },
    {
      key: "date_of_birth",
      header: "DOB",
      render: (value) => {
        if (typeof value === 'string') {
          return format(new Date(value), 'yyyy-MM-dd');
        }
        return '';
      }
    },
    { key: "gender", header: "Gender" },
    // { key: "studies", header: "Studies", render: () => "06" },
    {
      key: "actions",
      header: "Action",
      render: (_value: unknown, row?: ExtendedPatient) => {
        return (
          <div className="mypatient-actions-container">
            <button
              onClick={() => row && handleSelectPatient(row)}
              className="mypatient-action-btn open-patient"
              title="View Patient Details"
            >
              <Eye size={14} />
            </button>
            <button
              onClick={() => row && handleRemovePatient(row.uuid)}
              className="mypatient-action-btn remove-patient"
              title="Remove Patient from Favorites"
            >
              <UserMinus size={14} />
            </button>
          </div>
        );
      },
    },
  ];

  return (
    <div className="mypatients-dashboard-container">
      <MyPatientsSidebar
        isMinimized={isMinimized}
        onToggleMinimize={handleToggleMinimize}
        onItemClick={handleSidebarItemClick}
        currentPath="/my-patients"
      />
      <div className={`mypatients-main-content ${isMinimized ? 'mypatients-main-content-expanded' : ''}`}>
        <div className="mypatients-header">
          <h1 className="mypatients-title">Manage your Patients</h1>

            <div style={{ display: 'flex', justifyContent: 'flex-end', marginTop: '1rem' }}>
              <button
                onClick={handleSearchPatient}
                className="mypatients-add-btn"
              >
                Search / Add patient to your list
              </button>
            </div>
        </div>

        <div className="mypatients-table-section">
          <div className="mypatients-table-container">
            <DataTable<ExtendedPatient>
              data={filteredData.map(patient => ({
                ...patient,
                fullName: `${patient.first_name || ''} ${patient.last_name || ''}`
              }))}
              columns={MyPatientColumns.filter(col => !col.hidden) as Column<ExtendedPatient>[]}
              noDataMessage={data.length === 0 ? 'No favorite patients found' : 'No patients match your criteria'}
              defaultItemsPerPage={10}
            />
          </div>
        </div>
      </div>

      {/* Modals */}
      <PatientSearchModal
        isOpen={isSearchModalOpen}
        onClose={() => setIsSearchModalOpen(false)}
        onOpenAddPatientModal={() => {
          setIsSearchModalOpen(false);
          setIsAddPatientModalOpen(true);
        }}
      />

      <AddPatientFormModal
        isOpen={isAddPatientModalOpen}
        setIsModalOpen={setIsAddPatientModalOpen}
        setSwitchModal={() => {}}
        switchModal={false}
      />
    </div>
  );
};

export default MyPatients;

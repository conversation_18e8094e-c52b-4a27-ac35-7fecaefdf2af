import React, { useState } from "react";
import { X, Download, FileText, Calendar, AlertTriangle, CheckCircle, XCircle, Loader2, User } from "lucide-react";
import { useStaffDocumentsQuery, useDownloadStaffDocumentMutation } from "@/hooks/staffDocument.query";
import { useDelegationLogByUuidQuery } from "@/hooks/delegation.query";
import { useSponsorApproveDelegationLogMutation, useSponsorRejectDelegationLogMutation } from "@/hooks/delegation.query";
import { createFileNameWithExtension, downloadBlob, formatFileSize } from "@/utils/fileUtils";
import { toast } from "sonner";
import "./sponsor-studies-invitation.css";

interface DelegationDocumentsModalProps {
  isOpen: boolean;
  onClose: () => void;
  delegationUuid: string;
  delegationData: {
    study_name: string;
    team_member_name: string;
    department_name: string;
    pi_name: string;
    study_task: string;
    status: string;
  };
}

const DelegationDocumentsModal: React.FC<DelegationDocumentsModalProps> = ({
  isOpen,
  onClose,
  delegationUuid,
  delegationData,
}) => {
  const [notes, setNotes] = useState("");
  const [actionType, setActionType] = useState<'approve' | 'reject' | null>(null);
  const [showActionForm, setShowActionForm] = useState(false);

  // Get full delegation log to get team member UUID
  const { data: fullDelegationLog, isLoading: isLoadingDelegation } = useDelegationLogByUuidQuery(delegationUuid);
  
  // Debug logging to see what we're getting
  console.log('Full delegation log:', fullDelegationLog);
  console.log('Team member identifier:', fullDelegationLog?.team_member_identifier);
  console.log('Team member identifier type:', typeof fullDelegationLog?.team_member_identifier);
  
  // Use the full team member identifier directly
  const teamMemberIdentifier = fullDelegationLog?.team_member_identifier || "";
  console.log('Team member identifier:', teamMemberIdentifier);
  console.log('Staff documents API URL will be:', `/study/studies/sponsor-staff/${teamMemberIdentifier}/documents/`);
  
  // Get documents for the team member
  const { data: documents, isLoading: isLoadingDocuments, error } = useStaffDocumentsQuery(teamMemberIdentifier);
  
  // Show error if we can't get the team member identifier
  if (!isLoadingDelegation && !teamMemberIdentifier) {
    console.error('No team member identifier found in delegation log:', fullDelegationLog);
  }
  
  const downloadMutation = useDownloadStaffDocumentMutation();
  const approveDelegationMutation = useSponsorApproveDelegationLogMutation();
  const rejectDelegationMutation = useSponsorRejectDelegationLogMutation();

  const handleDownload = (documentUuid: string, displayName: string, fileUrl: string) => {
    downloadMutation.mutate(documentUuid, {
      onSuccess: (blob) => {
        try {
          const filename = createFileNameWithExtension(displayName, fileUrl);
          downloadBlob(blob, filename);
        } catch (error) {
          console.error("Error handling download:", error);
          downloadBlob(blob, `staff-document-${documentUuid}`);
        }
      },
      onError: (error) => {
        console.error("Download failed:", error);
        toast.error("Failed to download document");
      },
    });
  };

  const handleAction = (action: 'approve' | 'reject') => {
    setActionType(action);
    setShowActionForm(true);
  };

  const handleConfirmAction = async () => {
    if (!notes.trim()) {
      toast.error("Please provide notes for your decision");
      return;
    }

    try {
      if (actionType === 'approve') {
        await approveDelegationMutation.mutateAsync({
          delegationUuid,
          sponsorNotes: notes.trim()
        });
        toast.success("Application approved successfully!");
      } else {
        await rejectDelegationMutation.mutateAsync({
          delegationUuid,
          sponsorNotes: notes.trim()
        });
        toast.success("Application rejected successfully!");
      }

      // Close modal and reset state
      setShowActionForm(false);
      setActionType(null);
      setNotes("");
      onClose();
    } catch (error) {
      console.error(`Error ${actionType}ing application:`, error);
      toast.error(`Failed to ${actionType} application. Please try again.`);
    }
  };

  const handleClose = () => {
    setShowActionForm(false);
    setActionType(null);
    setNotes("");
    onClose();
  };

  const formatDate = (dateString: string): string => {
    return new Date(dateString).toLocaleDateString("en-US", {
      year: "numeric",
      month: "short",
      day: "numeric",
    });
  };

  const getDocumentIcon = (documentType: string) => {
    switch (documentType.toLowerCase()) {
      case "cv":
      case "resume":
        return <FileText size={20} />;
      case "certification":
      case "license":
        return <FileText size={20} />;
      default:
        return <FileText size={20} />;
    }
  };

  if (!isOpen) return null;

  const isLoading = isLoadingDelegation || isLoadingDocuments;
  const canTakeAction = delegationData.status === 'pending_sponsor';

  return (
    <div className="ssi-modal-overlay">
      <div className="ssi-modal">
        <div className="ssi-modal-header">
          <h2 className="ssi-modal-title">
            <User size={24} />
            Documents & Review - {delegationData.team_member_name}
          </h2>
          <button className="ssi-modal-close" onClick={handleClose}>
            <X size={24} />
          </button>
        </div>

        <div className="ssi-modal-body">
          {/* Application Details Section */}
          <div className="ssi-delegation-details">
            <h3>Application Details</h3>
            <div className="ssi-detail-grid">
              <div className="ssi-detail-item">
                <span className="ssi-detail-label">Study</span>
                <span className="ssi-detail-value">{delegationData.study_name}</span>
              </div>
              <div className="ssi-detail-item">
                <span className="ssi-detail-label">Department</span>
                <span className="ssi-detail-value">{delegationData.department_name}</span>
              </div>
              <div className="ssi-detail-item">
                <span className="ssi-detail-label">Principal Investigator</span>
                <span className="ssi-detail-value">{delegationData.pi_name}</span>
              </div>
              <div className="ssi-detail-item">
                <span className="ssi-detail-label">Study Task</span>
                <span className="ssi-detail-value">{delegationData.study_task}</span>
              </div>
              <div className="ssi-detail-item">
                <span className="ssi-detail-label">Status</span>
                <span className={`ssi-status-badge ${
                  delegationData.status === 'pending_sponsor' ? 'ssi-status-pending' :
                  delegationData.status === 'accepted' ? 'ssi-status-approved' :
                  delegationData.status === 'rejected' ? 'ssi-status-rejected' : ''
                }`}>
                  {delegationData.status === 'pending_sponsor' ? 'Pending Sponsor Review' : delegationData.status}
                </span>
              </div>
            </div>
          </div>

          {/* Documents Section */}
          <div className="ssi-documents-section">
            <h3>Team Member Documents</h3>
            
            {isLoading && (
              <div className="ssi-loading">
                <div className="ssi-loading-spinner"></div>
                <div className="ssi-loading-text">Loading Documents</div>
                <div className="ssi-loading-subtext">Please wait while we fetch the documents...</div>
              </div>
            )}

            {error && (
              <div className="ssi-empty-state">
                <div className="ssi-empty-icon">
                  <AlertTriangle size={48} />
                </div>
                <h3 className="ssi-empty-title">Error Loading Documents</h3>
                <p className="ssi-empty-description">
                  Error loading documents: {error.message}
                </p>
              </div>
            )}

            {!isLoadingDelegation && !fullDelegationLog?.team_member_identifier && (
              <div className="ssi-empty-state">
                <div className="ssi-empty-icon">
                  <AlertTriangle size={48} />
                </div>
                <h3 className="ssi-empty-title">Error</h3>
                <p className="ssi-empty-description">
                  Could not retrieve team member information. Please try again or contact support.
                </p>
              </div>
            )}

            {!isLoading && !error && (!documents || documents.length === 0) && (
              <div className="ssi-empty-state">
                <div className="ssi-empty-icon">
                  <FileText size={48} />
                </div>
                <h3 className="ssi-empty-title">No Documents Found</h3>
                <p className="ssi-empty-description">
                  No documents found for this team member.
                </p>
              </div>
            )}

            {!isLoading && !error && documents && documents.length > 0 && (
              <div className="ssi-documents-list">
                {documents.map((document) => (
                  <div key={document.uuid} className="ssi-document-item">
                    <div className="ssi-document-icon">
                      {getDocumentIcon(document.document_type)}
                    </div>
                    <div className="ssi-document-info">
                      <h4 className="ssi-document-name">{document.display_name}</h4>
                      <p className="ssi-document-type">{document.document_type}</p>
                      <div className="ssi-document-meta">
                        <span className="ssi-document-meta-item">
                          {formatFileSize(document.file_size)}
                        </span>
                        <span className="ssi-document-meta-item">
                          <Calendar size={12} />
                          Uploaded: {formatDate(document.uploaded_at)}
                        </span>
                        {document.expiry_date && (
                          <span className="ssi-document-meta-item">
                            <Calendar size={12} />
                            Expires: {formatDate(document.expiry_date)}
                            {document.is_expired && <AlertTriangle size={12} />}
                          </span>
                        )}
                      </div>
                    </div>
                    <div className="ssi-document-actions">
                      <button
                        className="ssi-download-btn"
                        onClick={() => handleDownload(document.uuid, document.display_name, document.file_url)}
                        disabled={downloadMutation.isPending}
                      >
                        <Download size={16} />
                        {downloadMutation.isPending ? "Downloading..." : "Download"}
                      </button>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>

          {/* Action Section */}
          {canTakeAction && !showActionForm && (
            <div className="ssi-action-form">
              <h3>
                <CheckCircle size={20} />
                Review Application
              </h3>
              <p>After reviewing the documents, you can approve or reject this application.</p>
              <div className="ssi-action-buttons">
                <button
                  className="ssi-btn ssi-btn-success"
                  onClick={() => handleAction('approve')}
                  disabled={isLoading}
                >
                  <CheckCircle size={16} />
                  Approve Application
                </button>
                <button
                  className="ssi-btn ssi-btn-danger"
                  onClick={() => handleAction('reject')}
                  disabled={isLoading}
                >
                  <XCircle size={16} />
                  Reject Application
                </button>
              </div>
            </div>
          )}

          {/* Action Form */}
          {showActionForm && actionType && (
            <div className="ssi-action-form">
              <h3>
                {actionType === 'approve' ? <CheckCircle size={20} /> : <XCircle size={20} />}
                {actionType === 'approve' ? 'Approve' : 'Reject'} Application
              </h3>
              <div className="ssi-form-group">
                <label htmlFor="notes" className="ssi-form-label">
                  {actionType === 'approve' ? 'Approval' : 'Rejection'} Notes:
                </label>
                <textarea
                  id="notes"
                  className="ssi-textarea"
                  value={notes}
                  onChange={(e) => setNotes(e.target.value)}
                  placeholder={actionType === 'approve' 
                    ? "Provide approval notes (e.g., 'Documents reviewed and approved. Welcome to the study team!')"
                    : "Provide rejection notes (e.g., 'Rejected - Missing required certifications')"
                  }
                  rows={4}
                  required
                  disabled={approveDelegationMutation.isPending || rejectDelegationMutation.isPending}
                />
                <p className="ssi-form-help">
                  These notes will be shared with the team member and department.
                </p>
              </div>
              <div className="ssi-action-buttons">
                <button
                  type="button"
                  className="ssi-btn ssi-btn-secondary"
                  onClick={() => setShowActionForm(false)}
                  disabled={approveDelegationMutation.isPending || rejectDelegationMutation.isPending}
                >
                  Cancel
                </button>
                <button
                  type="button"
                  className={`ssi-btn ${actionType === 'approve' ? 'ssi-btn-success' : 'ssi-btn-danger'}`}
                  onClick={handleConfirmAction}
                  disabled={approveDelegationMutation.isPending || rejectDelegationMutation.isPending || !notes.trim()}
                >
                  {approveDelegationMutation.isPending || rejectDelegationMutation.isPending ? (
                    <>
                      <Loader2 size={16} className="ssi-spin" />
                      Processing...
                    </>
                  ) : (
                    actionType === 'approve' ? 'Approve' : 'Reject'
                  )}
                </button>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default DelegationDocumentsModal;

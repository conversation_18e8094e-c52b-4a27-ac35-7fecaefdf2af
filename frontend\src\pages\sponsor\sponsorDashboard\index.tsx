import React, { useState } from "react";
import { motion } from "framer-motion";
import { useNavigate } from "react-router-dom";
import {
  Users,
  User<PERSON>he<PERSON>,
  AlertTriangle,
  CheckCircle,
  XCircle,
  MoreHorizontal,
  ChevronDown
} from "lucide-react";
import { usePoliciesQuery } from "@/hooks/policy.query";
import { useSponsorStaffQuery } from "@/hooks/sponsorStaff.query";
import { useSponsorPatientsQuery } from "@/hooks/sponsorPatients.query";
import { useStudyInvitationsByInviterQuery, useStudiesBySponsorForInvitationsQuery } from "@/hooks/study.query";
import { useCurrentUserQuery } from "@/hooks/user.query";
import { useSponsorEnrollmentsQuery } from "@/hooks/sponsorEnrollments.query";
import { useGetAllOpenQueriesQuery } from "@/hooks/form.query";
import './SponsorDashboard.css';

const SponsorDashboard: React.FC = () => {
  const navigate = useNavigate();
  const [showSafetyAlert] = useState(true);
  const [selectedAdverseEventPeriod, setSelectedAdverseEventPeriod] = useState("severity");
  const [selectedStaffPeriod, setSelectedStaffPeriod] = useState("all");
  const [selectedPatientsPeriod, setSelectedPatientsPeriod] = useState("status");
  const [selectedHospitalFilter, setSelectedHospitalFilter] = useState("Filter Hospital");
  const [selectedStudySite, setSelectedStudySite] = useState("Study Site");

  // Fetch policies using the hook
  const { data: policiesData, isLoading: policiesLoading, error: policiesError } = usePoliciesQuery();

  // Fetch sponsor staff using the hook
  const { data: sponsorStaffData, isLoading: staffLoading, error: staffError } = useSponsorStaffQuery();

  // Fetch sponsor patients using the hook
  const { data: sponsorPatientsData, isLoading: patientsLoading, error: patientsError } = useSponsorPatientsQuery();

  // Fetch current user for invitations
  const { data: currentUser } = useCurrentUserQuery();

  // Fetch sponsor invitations using the hook
  const { data: invitationsData, isLoading: invitationsLoading, error: invitationsError } = useStudyInvitationsByInviterQuery(
    currentUser?.identifier || ""
  );

  // Fetch sponsor enrollments by department
  const { data: enrollmentsData, isLoading: enrollmentsLoading, error: enrollmentsError } = useSponsorEnrollmentsQuery();

  // Fetch studies by sponsor for invitations count
  const { data: studiesBySponsor, isLoading: studiesLoading } = useStudiesBySponsorForInvitationsQuery(
    currentUser?.identifier || ""
  );

  // Fetch all open queries
  const { data: openQueriesData, isLoading: openQueriesLoading } = useGetAllOpenQueriesQuery();

  // Mock data - replace with actual API calls
  const adverseEventsData = {
    total: 12,
    label: "Total number of",
    highlight: "Adverse Events",
    period: "."
  };

  const staffData = {
    total: sponsorStaffData?.count || 0,
    label: "Total number of staff"
  };

  const patientsData = {
    total: sponsorPatientsData?.count || 0,
    label: "Total number of enrolled patients."
  };

  const enrollmentsChartData = {
    totalEnrollments: 1134,
    dropoutRate: 5.3,
    avgMonthlyEnrollment: 12.5
  };

  // Calculate dynamic counts for Study Overview
  const invitationsCount = studiesLoading ? "..." : (studiesBySponsor?.count || 0);
  const openQueriesCount = openQueriesLoading ? "..." : (Array.isArray(openQueriesData) ? openQueriesData.length : 0);

  // Calculate percentages (you can adjust these calculations based on your business logic)
  const invitationsPercentage = typeof invitationsCount === 'number' && invitationsCount > 0 ? Math.min((invitationsCount / 10) * 100, 100) : 0;
  const openQueriesPercentage = typeof openQueriesCount === 'number' && openQueriesCount > 0 ? Math.min((openQueriesCount / 20) * 100, 100) : 0;

  const studyOverviewData = [
    { type: "Amendments", count: "06", percentage: 56, icon: <Users size={16} />, color: "teal" },
    { type: "Deviation", count: "08", percentage: 56, icon: <AlertTriangle size={16} />, color: "teal" },
    { type: "Invitations", count: typeof invitationsCount === 'number' ? String(invitationsCount).padStart(2, '0') : invitationsCount, percentage: Math.round(invitationsPercentage), icon: <UserCheck size={16} />, color: "teal" },
    { type: "Open Queries", count: typeof openQueriesCount === 'number' ? String(openQueriesCount).padStart(2, '0') : openQueriesCount, percentage: Math.round(openQueriesPercentage), icon: <MoreHorizontal size={16} />, color: "teal" }
  ];

  // Get top performing sites from enrollments data
  const topPerformingSites = enrollmentsData?.results?.slice(0, 3) || [];

  // Get the three most recent policies from the fetched data
  const lastThreePolicies = policiesData?.results?.slice(0, 3) || [];

  // Calculate invitation statistics
  const invitationStats = {
    total: invitationsData?.length || 0,
    accepted: invitationsData?.filter((inv: any) => inv.status === "accepted")?.length || 0,
    rejected: invitationsData?.filter((inv: any) => inv.status === "rejected")?.length || 0,
    pending: invitationsData?.filter((inv: any) => inv.status === "pending")?.length || 0
  };

  // Get the invitations data for the table - only show the last 4 invitations
  const worksheetData = invitationsData
    ? invitationsData
        .sort((a: any, b: any) => {
          const dateA = a.invited_at ? new Date(a.invited_at).getTime() : 0;
          const dateB = b.invited_at ? new Date(b.invited_at).getTime() : 0;
          return dateB - dateA; // Sort by most recent first
        })
        .slice(0, 4)
    : [];

  const KPI_PERIOD_OPTIONS = [
    { value: "severity", label: "Severity" },
    { value: "this_week", label: "This Week" },
    { value: "this_month", label: "This Month" },
    { value: "all", label: "All Time" }
  ];

  const STAFF_PERIOD_OPTIONS = [
    { value: "all", label: "All" },
    { value: "this_week", label: "This Week" },
    { value: "this_month", label: "This Month" }
  ];

  const PATIENT_PERIOD_OPTIONS = [
    { value: "status", label: "Status" },
    { value: "this_week", label: "This Week" },
    { value: "this_month", label: "This Month" }
  ];

  const handleAcceptInvitation = (uuid: string) => {
    console.log("Accept invitation:", uuid);
  };

  const handleRejectInvitation = (uuid: string) => {
    console.log("Reject invitation:", uuid);
  };

  return (
    <motion.div
      className="sponsor-dashboard-container"
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      transition={{ duration: 0.5 }}
    >
      {/* Header Section */}
      <div className="sponsor-dashboard-header">
        <div className="sponsor-welcome-section">
          <h1 className="sponsor-welcome-title">Welcome Back, <span className="sponsor-name-highlight">{currentUser?.first_name || "User"}</span></h1>
          <p className="sponsor-welcome-subtitle">Welcome back to your dashboard</p>
        </div>
        <div className="sponsor-header-actions">
          <div className="sponsor-study-site-dropdown">
            <select
              className="sponsor-dropdown-select"
              value={selectedStudySite}
              onChange={(e) => setSelectedStudySite(e.target.value)}
            >
              <option value="Study Site">Study Site</option>
              <option value="Site 1">Site 1</option>
              <option value="Site 2">Site 2</option>
            </select>
            <ChevronDown size={16} className="sponsor-dropdown-icon" />
          </div>
          <button
            className="sponsor-create-study-btn"
            onClick={() => navigate('/sponsor/studies')}
          >
            Create New Study
          </button>
        </div>
      </div>

      {/* Study Safety Alert Banner */}
      {showSafetyAlert && (
        <div className="sponsor-safety-alert-banner">
          <div className="sponsor-safety-alert-content">
            <span className="sponsor-safety-alert-title">Study Safety Alert</span>
            <span className="sponsor-safety-alert-separator">•</span>
            <span className="sponsor-safety-alert-text">Lorem Ipsum</span>
          </div>
        </div>
      )}

      {/* KPI Cards Section */}
      <div className="sponsor-kpi-cards-grid">
        {/* Adverse Event Card */}
        <div className="sponsor-kpi-card">
          <div className="sponsor-kpi-header">
            <div className="sponsor-kpi-icon sponsor-adverse-event-icon">
              <AlertTriangle size={20} />
            </div>
            <div className="sponsor-kpi-title-section">
              <div className="sponsor-kpi-title-row">
                <span className="sponsor-kpi-title">Adverse Event</span>
                <div className="sponsor-kpi-period">
                  <select
                    className="sponsor-kpi-period-select"
                    value={selectedAdverseEventPeriod}
                    onChange={(e) => setSelectedAdverseEventPeriod(e.target.value)}
                  >
                    {KPI_PERIOD_OPTIONS.map(option => (
                      <option key={option.value} value={option.value}>
                        {option.label}
                      </option>
                    ))}
                  </select>
                </div>
              </div>
            </div>
          </div>
          <div className="sponsor-kpi-main-row">
            <div className="sponsor-kpi-number">
              {adverseEventsData.total === 0 ? "0" : String(adverseEventsData.total).padStart(2, '0')}
            </div>
          </div>
          <div className="sponsor-kpi-description">
            <span className="sponsor-kpi-description-text">
              {adverseEventsData.label} <span className="sponsor-kpi-highlight">{adverseEventsData.highlight}</span>{adverseEventsData.period}
            </span>
          </div>
        </div>

        {/* Staff Card */}
        <div className="sponsor-kpi-card">
          <div className="sponsor-kpi-header">
            <div className="sponsor-kpi-icon sponsor-staff-icon">
              <UserCheck size={20} />
            </div>
            <div className="sponsor-kpi-title-section">
              <div className="sponsor-kpi-title-row">
                <span className="sponsor-kpi-title">Staff</span>
                <div className="sponsor-kpi-period">
                  <select
                    className="sponsor-kpi-period-select"
                    value={selectedStaffPeriod}
                    onChange={(e) => setSelectedStaffPeriod(e.target.value)}
                  >
                    {STAFF_PERIOD_OPTIONS.map(option => (
                      <option key={option.value} value={option.value}>
                        {option.label}
                      </option>
                    ))}
                  </select>
                </div>
              </div>
            </div>
          </div>
          <div className="sponsor-kpi-main-row">
            <div className="sponsor-kpi-number">
              {staffLoading ? (
                "Loading..."
              ) : staffError ? (
                "Error"
              ) : (
                staffData.total === 0 ? "0" : String(staffData.total).padStart(2, '0')
              )}
            </div>
          </div>
          <div className="sponsor-kpi-description">
            <span className="sponsor-kpi-description-text">
              {staffData.label}
            </span>
          </div>
        </div>

        {/* Patients Card */}
        <div className="sponsor-kpi-card">
          <div className="sponsor-kpi-header">
            <div className="sponsor-kpi-icon sponsor-patients-icon">
              <Users size={20} />
            </div>
            <div className="sponsor-kpi-title-section">
              <div className="sponsor-kpi-title-row">
                <span className="sponsor-kpi-title">Patients</span>
                <div className="sponsor-kpi-period">
                  <select
                    className="sponsor-kpi-period-select"
                    value={selectedPatientsPeriod}
                    onChange={(e) => setSelectedPatientsPeriod(e.target.value)}
                  >
                    {PATIENT_PERIOD_OPTIONS.map(option => (
                      <option key={option.value} value={option.value}>
                        {option.label}
                      </option>
                    ))}
                  </select>
                </div>
              </div>
            </div>
          </div>
          <div className="sponsor-kpi-main-row">
            <div className="sponsor-kpi-number">
              {patientsLoading ? (
                "Loading..."
              ) : patientsError ? (
                "Error"
              ) : (
                patientsData.total === 0 ? "0" : String(patientsData.total).padStart(2, '0')
              )}
            </div>
          </div>
          <div className="sponsor-kpi-description">
            <span className="sponsor-kpi-description-text">
              {patientsData.label}
            </span>
          </div>
        </div>
      </div>

      {/* Main Content Grid */}
      <div className="sponsor-main-content-grid">
        {/* Top Performing Sites Section */}
        <div className="sponsor-top-sites-section">
          <div className="sponsor-section-header">
            <h2 className="sponsor-section-title">Top Performing Sites</h2>
            <button
              className="sponsor-see-all-btn"
              onClick={() => navigate('/sponsor/study-patients')}
            >
              See All
            </button>
          </div>
          <div className="sponsor-sites-list">
            {enrollmentsLoading ? (
              <div className="sponsor-site-item">
                <p>Loading top performing sites...</p>
              </div>
            ) : enrollmentsError ? (
              <div className="sponsor-site-item">
                <p>Error loading top performing sites</p>
              </div>
            ) : topPerformingSites.length === 0 ? (
              <div className="sponsor-site-item">
                <p>No enrollment data available</p>
              </div>
            ) : (
              topPerformingSites.map((site, index) => (
                <div key={index} className="sponsor-site-item">
                  <h4>{site.department_name}</h4>
                  <p>Hospital: {site.hospital_name}</p>
                  <p>Enrollments: <span className="sponsor-enrollment-count">{site.enrollment_count}</span></p>
                </div>
              ))
            )}
          </div>
        </div>

        {/* Policies Section */}
        <div className="sponsor-policies-section">
          <div className="sponsor-section-header">
            <h2 className="sponsor-section-title">Policies</h2>
            <button
              className="sponsor-see-all-btn"
              onClick={() => navigate('/sponsor/policy')}
            >
              See All
            </button>
          </div>
          <div className="sponsor-policies-list">
            {policiesLoading ? (
              <div className="sponsor-policy-item">
                <p>Loading policies...</p>
              </div>
            ) : policiesError ? (
              <div className="sponsor-policy-item">
                <p>Error loading policies</p>
              </div>
            ) : lastThreePolicies.length === 0 ? (
              <div className="sponsor-policy-item">
                <p>No policies available</p>
              </div>
            ) : (
              lastThreePolicies.map((policy, index) => (
                <div key={policy.uuid || index} className="sponsor-policy-item">
                  <h4>{policy.title}</h4>
                  <p>{policy.description}</p>
                </div>
              ))
            )}
          </div>
        </div>
      </div>

      {/* Chart Section */}
      <div className="sponsor-chart-section">
        <div className="sponsor-chart-header">
          <h3>Enrollments</h3>
          <div className="sponsor-chart-options">
            <MoreHorizontal size={20} />
          </div>
        </div>
        <div className="sponsor-chart-content">
          <div className="sponsor-chart-visual">
            <svg width="100%" height="300" viewBox="0 0 600 300">
              {/* Chart background */}
              <rect width="600" height="300" fill="#f8f9fa" rx="8" />

              {/* Y-axis labels */}
              <text x="20" y="50" fontSize="12" fill="#666666">10k</text>
              <text x="20" y="100" fontSize="12" fill="#666666">8k</text>
              <text x="20" y="150" fontSize="12" fill="#666666">6k</text>
              <text x="20" y="200" fontSize="12" fill="#666666">4k</text>
              <text x="20" y="250" fontSize="12" fill="#666666">2k</text>

              {/* X-axis labels */}
              <text x="60" y="280" fontSize="12" fill="#666666">Jan</text>
              <text x="110" y="280" fontSize="12" fill="#666666">Feb</text>
              <text x="160" y="280" fontSize="12" fill="#666666">Mar</text>
              <text x="210" y="280" fontSize="12" fill="#666666">Apr</text>
              <text x="260" y="280" fontSize="12" fill="#666666">May</text>
              <text x="310" y="280" fontSize="12" fill="#666666">Jun</text>
              <text x="360" y="280" fontSize="12" fill="#666666" fontWeight="bold">Jul</text>
              <text x="410" y="280" fontSize="12" fill="#666666">Aug</text>
              <text x="460" y="280" fontSize="12" fill="#666666">Sep</text>
              <text x="510" y="280" fontSize="12" fill="#666666">Oct</text>
              <text x="560" y="280" fontSize="12" fill="#666666">Nov</text>

              {/* Chart line */}
              <polyline
                fill="none"
                stroke="#4ECDC4"
                strokeWidth="3"
                points="60,180 110,160 160,170 210,140 260,120 310,100 360,80 410,90 460,95 510,100 560,120"
              />

              {/* Data points */}
              <circle cx="60" cy="180" r="4" fill="#4ECDC4" />
              <circle cx="110" cy="160" r="4" fill="#4ECDC4" />
              <circle cx="160" cy="170" r="4" fill="#4ECDC4" />
              <circle cx="210" cy="140" r="4" fill="#4ECDC4" />
              <circle cx="260" cy="120" r="4" fill="#4ECDC4" />
              <circle cx="310" cy="100" r="4" fill="#4ECDC4" />
              <circle cx="360" cy="80" r="6" fill="#4ECDC4" />
              <circle cx="410" cy="90" r="4" fill="#4ECDC4" />
              <circle cx="460" cy="95" r="4" fill="#4ECDC4" />
              <circle cx="510" cy="100" r="4" fill="#4ECDC4" />
              <circle cx="560" cy="120" r="4" fill="#4ECDC4" />

              {/* Highlight tooltip for July */}
              <rect x="340" y="55" width="40" height="20" fill="#333333" rx="4" />
              <text x="360" y="68" fontSize="10" fill="white" textAnchor="middle">1,354</text>

              {/* Area under curve */}
              <path
                d="M60,180 L110,160 L160,170 L210,140 L260,120 L310,100 L360,80 L410,90 L460,95 L510,100 L560,120 L560,260 L60,260 Z"
                fill="url(#gradient)"
                opacity="0.3"
              />

              {/* Gradient definition */}
              <defs>
                <linearGradient id="gradient" x1="0%" y1="0%" x2="0%" y2="100%">
                  <stop offset="0%" stopColor="#4ECDC4" stopOpacity="0.8"/>
                  <stop offset="100%" stopColor="#4ECDC4" stopOpacity="0.1"/>
                </linearGradient>
              </defs>
            </svg>
          </div>
          <div className="sponsor-chart-stats">
            <div className="sponsor-chart-stat">
              <span className="sponsor-stat-label">Total Enrollments</span>
              <span className="sponsor-stat-value">{enrollmentsChartData.totalEnrollments}</span>
            </div>
            <div className="sponsor-chart-stat">
              <span className="sponsor-stat-label">Dropout Rate (%)</span>
              <span className="sponsor-stat-value">{enrollmentsChartData.dropoutRate}</span>
            </div>
            <div className="sponsor-chart-stat">
              <span className="sponsor-stat-label">Avg Monthly Enrollment</span>
              <span className="sponsor-stat-value">{enrollmentsChartData.avgMonthlyEnrollment}%</span>
            </div>
          </div>
        </div>
      </div>

      {/* Bottom Sections */}
      <div className="sponsor-bottom-sections">
        {/* Study Overview Section */}
        <div className="sponsor-study-overview-section">
          <div className="sponsor-section-header">
            <h2 className="sponsor-section-title">Study Overview</h2>
            <div className="sponsor-filter-dropdown">
              <select
                className="sponsor-filter-select"
                value={selectedHospitalFilter}
                onChange={(e) => setSelectedHospitalFilter(e.target.value)}
              >
                <option value="Filter Hospital">Filter Hospital</option>
                <option value="Hospital 1">Hospital 1</option>
                <option value="Hospital 2">Hospital 2</option>
              </select>
            </div>
          </div>

          <div className="sponsor-overview-breakdown">
            {studyOverviewData.map((item, index) => (
              <div className="sponsor-overview-item" key={index}>
                <div className="sponsor-overview-header">
                  <div className={`sponsor-overview-icon sponsor-${item.color}-icon`}>
                    {item.icon}
                  </div>
                  <span>{item.type}</span>
                  <span className="sponsor-overview-count">{item.count}</span>
                </div>
                <div className="sponsor-progress-bar">
                  <div className={`sponsor-progress-fill sponsor-${item.color}-progress`} style={{width: `${item.percentage}%`}}></div>
                </div>
                <span className="sponsor-progress-percentage">{item.percentage}%</span>
              </div>
            ))}
          </div>
        </div>

        {/* Worksheet Card */}
        <div className="sponsor-worksheet-section">
          <div className="sponsor-section-header">
            <h3>Study Invitations</h3>
            <button
              className="sponsor-see-all-btn"
              onClick={() => navigate('/sponsor/invitations')}
            >
              See All
            </button>
          </div>

          <div className="sponsor-worksheet-stats">
            <div className="sponsor-stat-item">
              <span className="sponsor-stat-label">Total Invitations:</span>
              <span className="sponsor-stat-value">
                {invitationsLoading ? "Loading..." : String(invitationStats.total).padStart(2, '0')}
              </span>
            </div>
            <div className="sponsor-stat-item">
              <span className="sponsor-stat-label">Accepted:</span>
              <span className="sponsor-stat-value">
                {invitationsLoading ? "Loading..." : String(invitationStats.accepted).padStart(2, '0')}
              </span>
            </div>
            <div className="sponsor-stat-item">
              <span className="sponsor-stat-label">Rejected:</span>
              <span className="sponsor-stat-value">
                {invitationsLoading ? "Loading..." : String(invitationStats.rejected).padStart(2, '0')}
              </span>
            </div>
          </div>

          <div className="sponsor-worksheet-table-container">
            <table className="sponsor-worksheet-table">
              <thead>
                <tr>
                  <th>Study Name</th>
                  <th>Source</th>
                  <th>Status</th>
                  <th>Action</th>
                </tr>
              </thead>
              <tbody>
                {invitationsLoading ? (
                  <tr>
                    <td colSpan={4} style={{ textAlign: 'center', padding: '20px' }}>
                      Loading invitations...
                    </td>
                  </tr>
                ) : invitationsError ? (
                  <tr>
                    <td colSpan={4} style={{ textAlign: 'center', padding: '20px', color: 'red' }}>
                      Error loading invitations
                    </td>
                  </tr>
                ) : worksheetData.length === 0 ? (
                  <tr>
                    <td colSpan={4} style={{ textAlign: 'center', padding: '20px' }}>
                      No invitations available
                    </td>
                  </tr>
                ) : (
                  worksheetData.map((item: any) => (
                    <tr key={item.uuid}>
                      <td>{item.study_name || item.study?.name || "N/A"}</td>
                      <td>{item.department_name || item.department?.name || "N/A"}</td>
                      <td>
                        <span className={`sponsor-status-badge sponsor-status-${item.status}`}>
                          {item.status.charAt(0).toUpperCase() + item.status.slice(1)}
                        </span>
                      </td>
                      <td>
                        <div className="sponsor-action-buttons">
                          <button
                            className="sponsor-action-btn sponsor-accept"
                            onClick={() => handleAcceptInvitation(item.uuid)}
                            disabled={item.status === "accepted" || item.status === "rejected"}
                            title="Accept"
                          >
                            <CheckCircle size={16} />
                          </button>
                          <button
                            className="sponsor-action-btn sponsor-reject"
                            onClick={() => handleRejectInvitation(item.uuid)}
                            disabled={item.status === "rejected" || item.status === "accepted"}
                            title="Reject"
                          >
                            <XCircle size={16} />
                          </button>
                        </div>
                      </td>
                    </tr>
                  ))
                )}
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </motion.div>
  );
};

export default SponsorDashboard;

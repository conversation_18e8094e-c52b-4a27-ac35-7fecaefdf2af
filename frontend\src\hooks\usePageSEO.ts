import { useLocation } from 'react-router-dom';
import { getSEOConfig, SEOConfig } from '@/config/seoConfig';

interface UsePageSEOProps {
  title?: string;
  description?: string;
  keywords?: string;
  image?: string;
  noIndex?: boolean;
  structuredData?: object;
  canonicalUrl?: string;
}

export const usePageSEO = (overrides: UsePageSEOProps = {}) => {
  const location = useLocation();
  const defaultConfig = getSEOConfig(location.pathname);
  
  const seoConfig: SEOConfig & { noIndex?: boolean; canonicalUrl?: string } = {
    ...defaultConfig,
    ...overrides,
    url: `https://nurtify.co.uk${location.pathname}${location.search}`,
    canonicalUrl: overrides.canonicalUrl || `https://nurtify.co.uk${location.pathname}`
  };

  return seoConfig;
};

export default usePageSEO;

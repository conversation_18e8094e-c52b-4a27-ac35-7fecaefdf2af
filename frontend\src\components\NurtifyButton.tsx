import React from 'react';
import "./styles.css";

interface NurtifyButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {
    variant?: 'primary' | 'success' | 'danger' | 'warning' | 'info' | 'light' | 'dark' | 'link';
    size?: 'small' | 'regular' | 'medium';
    outline?: boolean;
    children: React.ReactNode;
    className?: string;
}

const NurtifyButton: React.FC<NurtifyButtonProps> = ({
    variant = 'primary',
    size = 'medium',
    outline = false,
    children,
    className = '',
    disabled = false,
    ...rest
}) => {
    const buttonClass = `nurtify-button nurtify-button--${variant} nurtify-button--${size} ${
        outline ? 'nurtify-button--outline' : ''
    } ${disabled ? 'nurtify-button--disabled' : ''} ${className}`.trim();

    return (
        <button
            className={buttonClass}
            disabled={disabled}
            {...rest}
        >
            {children}
        </button>
    );
};

export default NurtifyButton;

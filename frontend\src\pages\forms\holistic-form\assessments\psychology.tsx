 
 
import { useState, useEffect } from "react";
import mentalhealthImage from "./static/images/added/mh.png";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { faAdd } from "@fortawesome/free-solid-svg-icons";
import useHolisticFormStore from "@/store/holisticFormState";
import NurtifyRadio from "@/components/NurtifyRadio";
import NurtifyText from "@/components/NurtifyText";
import NurtifyTextArea from "@/components/NurtifyTextArea";
import NurtifyDateInput from "@/components/NurtifyDateInput";
import NurtifyInput from "@/components/NurtifyInput";

const Psychology = () => {
  const { assessment, setAssessment } = useHolisticFormStore();
  
  const setPsychology = (psychologyData: any) => {
    setAssessment({
      ...assessment,
      psychology: {
        ...assessment.psychology,
        ...psychologyData
      }
    });
  };


  const setPostureGait = (value: any) => {
    setPsychology({
      ...assessment.psychology,
      postureGait: value,
    })
  }

  const setGrooming = (value: any) => {
    setPsychology({
      ...assessment.psychology,

      grooming: value,
    })
  }
  const setFacialExpression = (value: any) => {
    setPsychology({
      ...assessment.psychology,

      facialExpression: value,
    })
  }
  const setBodyLanguage = (value: any) => {
    setPsychology({
      ...assessment.psychology,

      bodyLanguage: value,
    })
  }
  const setAbilityToFollowRequest = (value: any) => {
    setPsychology({
      ...assessment.psychology,

      abilityToFollowRequest: value,
    })
  }
  const setRapport = (value: any) => {
    setPsychology({
      ...assessment.psychology,

      rapport: value,
    })
  }
  const setMoodState = (value: any) => {
    setPsychology({
      ...assessment.psychology,

      moodState: value,
    })
  }
  const setPsychomotorActivity = (value: any) => {
    setPsychology({
      ...assessment.psychology,

      psychomotorActivity: value,
    })
  }
  const setLevelOfArousal = (value: any) => {
    setPsychology({
      ...assessment.psychology,
      levelOfArousal: value,
    })
  }
  const setObviousSelfHarm = (value: any) => {
    setPsychology({
      ...assessment.psychology,

      obviousSelfHarm: value,
    })
  }
  const setEyeContact = (value: any) => {
    setPsychology({
      ...assessment.psychology,

      eyeContact: value,
    })
  }

  const setAlcoholAssociated = (value: any) => {
    setPsychology({
      ...assessment.psychology,

      alcoholAssociated: value,
    })
  }

  const setDrugsAssociated = (value: any) => {
    setPsychology({
      ...assessment.psychology,

      drugsAssociated: value,
    })
  }

  const setOverdoseAssociated = (value: any) => {
    setPsychology({
      ...assessment.psychology,

      overdoseAssociated: value,
    })
  }

  const setRate = (value: any) => {
    setPsychology({
      ...assessment.psychology,

      rate: value,
    })
  }

  const setQuantity = (value: any) => {
    setPsychology({
      ...assessment.psychology,

      quantity: value,
    })
  }

  const setTone = (value: any) => {
    setPsychology({
      ...assessment.psychology,

      tone: value,
    })
  }
  

  const setVolume = (value: any) => {
    setPsychology({
      ...assessment.psychology,
      volume: value,
    })
  }

  const setFluencyAndRhythm = (value: any) => {
    setPsychology({
      ...assessment.psychology,

      fluencyAndRhythm: value,
    })
  }
  const setSuicidalThoughts = (value: any) => {
    setPsychology({
      ...assessment.psychology,

      suicidalThoughts: value,
    })
  }
  const setSuicidalPlanDesc = (value: any) => {
    setPsychology({
      ...assessment.psychology,

      suicidalPlanDesc: value,
    })
  }

  const setPreviousSuidicalAttempt = (value: string) => {
    setPsychology({
      ...assessment.psychology,
      PreviousSuidicalAttempt: value,
    })
  }
  const setHarmOthers = (value: string) => {
    setPsychology({
      ...assessment.psychology,
      HarmOthers: value,
    })
  }
  const setHomicidalThoughts = (value: string) => {
    setPsychology({
      ...assessment.psychology,
      HomicidalThoughts: value,
    })
  }
  const setHalucination = (value: string) => {
    if (value === "Yes") {
      setPsychology({
        ...assessment.psychology,
        Halucination: value,
      })
    } else {
      setPsychology({
        ...assessment.psychology,
        Halucination: value,
        HalucinationDesc: "Not applicable",
      })
    }
  }
  const setOrientation = (value: any) => {
    setPsychology({
      ...assessment.psychology,

      Orientation: value,
    })
  }
  const setAskingForHelp = (value: any) => {
    setPsychology({
      ...assessment.psychology,

      AskingForHelp: value,
    })
  }

  const setOtherMentalHealthDetails = (value: any) => {
    setPsychology({
      ...assessment.psychology,

      otherMentalHealthDetails: value,
    })
    }
  const setHalucinationDesc = (value: string) => {
    setPsychology({
      ...assessment.psychology,
      HalucinationDesc: value,
    })
  }

  const [postureGaitTextareaShown, setPostureGaitTextareaShown] =
    useState(false);
  const [groomingTextareaShown, setGroomingTextareaShown] = useState(false);
  const [facialExpressionTextareaShown, setFacialExpressionTextareaShown] =
    useState(false);
  const [bodyLanguageTextareaShown, setBodyLanguageTextareaShown] =
    useState(false);

  const [
    psychomotorActivityTextareaShown,
    setPsychomotorActivityTextareaShown,
  ] = useState(false);
  const [
    abilityToFollowRequestTextareaShown,
    setAbilityToFollowRequestTextareaShown,
  ] = useState(false);
  const [rapportTextareaShown, setRapportTextareaShown] = useState(false);
  const [moodStateTextareaShown, setMoodStateTextareaShown] = useState(false);

  const setSuicidalPlanInPlace = (value: string) => {
    setPsychology({
      ...assessment.psychology,
      suicidalPlanInPlace: value,
    })
  }

  const [mentalMedications, setMentalMedications] = useState<any[]>(
    assessment?.psychology?.mentalMedications || []
  );
  
  // Initialize state from assessment when component mounts
  useEffect(() => {
    if (assessment?.psychology?.mentalMedications) {
      setMentalMedications(assessment.psychology.mentalMedications);
    }
  }, [assessment?.psychology?.mentalMedications]);
 
  const [addMentalMedicationFormShowing, setAddMentalMedicationFormShowing] =
    useState(false);
  const [, setOptionsModalShowing] = useState(false);

  const [medName, setMedName] = useState("");
  const [dose, setDose] = useState("");
  const [date, setDate] = useState("");
  const [time, setTime] = useState("");
  
  const handleAddMentalMedications = (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    const form = e.currentTarget;
    const formData = new FormData(form);
    const newMedication = {
      d1: formData.get("med_name") as string,
      d2: formData.get("dose") as string,
      d3: date || (formData.get("date") as string),
      d4: time || (formData.get("time") as string),
    };

    console.log("New Medication:", newMedication); // Debug: Verify data

    // Update the assessment state directly
    setAssessment({
      ...assessment,
      psychology: {
        ...assessment.psychology,
        mentalMedications: [
          ...(assessment.psychology?.mentalMedications || []),
          newMedication,
        ],
      },
    });

    console.log("Updated Assessment:", assessment); // Debug: Check state after update

    // Reset form and UI state
    setOptionsModalShowing(false);
    setAddMentalMedicationFormShowing(false);
    setMedName("");
    setDose("");
    setDate("");
    setTime("");
  };
  
  return (
      <div className="block align-items-*-start flex-column flex-md-row">
        <div className="mt-5 mb-3" id="division-34">

          <div className="inlineBlock mb-4 headinqQuestion">
            <img
              src={mentalhealthImage}
              className="imageEtiquette"
              alt="Image describing mental health section"
            />
            <span className="mb-2 py-2 fs-2 text-start etiquetteHeadingForms">
              Mental Health Assessment
            </span>
          </div>

          {/* Is there any MH Concern ? */}
          <div className="list-group me-4 mt-3 col-xl-4 col-lg-8 col-md-12">
            <NurtifyText label="Any Mental Health Concern?" className="mb-2 fw-bold" />

            <div onClick={() => {
              setPsychology({
                ...assessment.psychology,
                hasMentalHealthConcern: true,
              })
            }}>
              <NurtifyRadio
                name="mentalHealthConcern"
                value="yes"
                label="Yes"
                checked={assessment?.psychology?.hasMentalHealthConcern === true}
                onChange={() => {}}
              />
            </div>
            
            <div onClick={() => {
              setPsychology({
                ...assessment.psychology,
                hasMentalHealthConcern: false,
              })
            }}>
              <NurtifyRadio
                name="mentalHealthConcern"
                value="no"
                label="No"
                checked={assessment?.psychology?.hasMentalHealthConcern === false}
                onChange={() => {}}
              />
            </div>
          </div>
        </div>

        {assessment?.psychology?.hasMentalHealthConcern == true ? (
          <form
            onSubmit={(e) => e.preventDefault()}
            id="division-35"
            className="mt-5 mb-3 list-group "
          >
            <NurtifyText label="Mental Health Assessment" className="mb-2 fw-bold" />

            {/* Input text Mental Health Questions */}
            <div>
              <div className="block mt-4">
                <NurtifyText label="Distinctive Features:" className="mb-2" />
                <div className="col-lg-6 col-xl-6 col-md-12 mt-2">
                  <NurtifyTextArea
                    onChange={(e) => {
                      setPsychology({
                        ...assessment.psychology,
                        distinctiveFeatures: e.target.value,
                      })
                    }}
                    name="DISTINCTIVE-FEATURES"
                    // className="form-control border border-black"
                    value={assessment?.psychology?.distinctiveFeatures || ""}
                  />
                </div>
              </div>

              <div className="block mt-4">
                <NurtifyText label="Clothing" className="mb-2" />
                <div className="col-lg-6 col-xl-6 col-md-12 mt-2">
                  <NurtifyTextArea
                    onChange={(e) => {
                      setPsychology({
                        ...assessment.psychology,
                        clothing: e.target.value,
                      })
                    }}
                    name="Clothing"
                    // className="form-control border border-black"
                    value={assessment?.psychology?.clothing || ""}
                  />
                </div>
              </div>

              {/* Appearance MH Assessment   */}

              <div className="d-flex flex-column my-4 border border-1 badge">
                <NurtifyText label="Appearance Assessment" className="mb-2 fw-bold" />

                <NurtifyText label="Posture/Gait" className="mb-2" />

              <div className="list-group d-flex flex-row gap-2 me-4 mt-2 flex-wrap mb-4">
                <div>
                  <div className="labelSBARForm cursorPointer rounded-3" style={{ width: "208px" }}>
                    <NurtifyRadio
                      name="posture"
                      value="Upright"
                      label="Upright"
                      checked={assessment?.psychology?.postureGait === "Upright"}
                      onChange={(e) => {
                        setPostureGait(e.target.value);
                        setPostureGaitTextareaShown(false);
                      }}
                    />
                  </div>
                </div>
                <div>
                  <div className="labelSBARForm cursorPointer rounded-3" style={{ width: "208px" }}>
                    <NurtifyRadio
                      name="posture"
                      value="Slouched"
                      label="Slouched"
                      checked={assessment?.psychology?.postureGait === "Slouched"}
                      onChange={(e) => {
                        setPostureGait(e.target.value);
                        setPostureGaitTextareaShown(false);
                      }}
                    />
                  </div>
                </div>
                <div>
                  <div className="labelSBARForm cursorPointer rounded-3" style={{ width: "208px" }}>
                    <NurtifyRadio
                      name="posture"
                      value="Stiff"
                      label="Stiff"
                      checked={assessment?.psychology?.postureGait === "Stiff"}
                      onChange={(e) => {
                        setPostureGait(e.target.value);
                        setPostureGaitTextareaShown(false);
                      }}
                    />
                  </div>
                </div>
                <div>
                  <div className="labelSBARForm cursorPointer rounded-3" style={{ width: "208px" }}>
                    <NurtifyRadio
                      name="posture"
                      value="Shuffling"
                      label="Shuffling"
                      checked={assessment?.psychology?.postureGait === "Shuffling"}
                      onChange={(e) => {
                        setPostureGait(e.target.value);
                        setPostureGaitTextareaShown(false);
                      }}
                    />
                  </div>
                </div>
                <div>
                  <div className="labelSBARForm cursorPointer rounded-3" style={{ width: "208px" }}>
                    <NurtifyRadio
                      name="posture"
                      checked={assessment?.psychology?.postureGait === "Normal"}
                      value="Normal"
                      label="Normal"
                      onChange={(e) => {
                        setPostureGait(e.target.value);
                        setPostureGaitTextareaShown(false);
                      }}
                    />
                  </div>
                </div>
                <div>
                  <div className="labelSBARForm cursorPointer rounded-3" style={{ width: "208px" }}>
                    <NurtifyRadio
                      name="posture"
                      checked={assessment?.psychology?.postureGait === "Unsteady"}
                      value="Unsteady"
                      label="Unsteady"
                      onChange={(e) => {
                        setPostureGait(e.target.value);
                        setPostureGaitTextareaShown(false);
                      }}
                    />
                  </div>
                </div>
                <div>
                  <div className="labelSBARForm cursorPointer rounded-3" style={{ width: "208px" }}>
                    <NurtifyRadio
                      name="posture"
                      checked={assessment?.psychology?.postureGait === "Restless"}
                      value="Restless"
                      label="Restless"
                      onChange={(e) => {
                        setPostureGait(e.target.value);
                        setPostureGaitTextareaShown(false);
                      }}
                    />
                  </div>
                </div>
                <div>
                  <div className="labelSBARForm cursorPointer rounded-3" style={{ width: "208px" }}>
                    <NurtifyRadio
                      name="posture"
                      value="Other"
                      checked={assessment?.psychology?.postureGait === "Other"}
                      label="Other"
                      onChange={(e) => {
                        setPostureGait(e.target.value);
                        setPostureGaitTextareaShown(true);
                      }}
                    />
                  </div>
                </div>
                </div>

                {postureGaitTextareaShown && (
                  <div className="col-lg-6 col-xl-6 col-md-12 my-2">
                    <NurtifyTextArea
                      placeholder="Type here for posture/Gait .... "
                      name="POSTURE"
                      onChange={(e) => setPostureGait(e.target.value)}
                      // className="form-control border border-black"
                    />
                  </div>
                )}

                <NurtifyText label="Grooming/Hygiene" className="mb-2" />

                <div className="list-group d-flex flex-row gap-2 me-4 mt-2 flex-wrap mb-4">
                  <div>
                    <div className="labelSBARForm cursorPointer rounded-3" style={{ width: "208px" }}>
                      <NurtifyRadio
                        name="grooming"
                        value="Well-groomed"
                        checked={assessment?.psychology?.grooming === "Well-groomed"}
                        label="Well-groomed"
                        onChange={(e) => {
                          setGrooming(e.target.value);
                          setGroomingTextareaShown(false);
                        }}
                      />
                    </div>
                  </div>
                  <div>
                    <div className="labelSBARForm cursorPointer rounded-3" style={{ width: "208px" }}>
                      <NurtifyRadio
                        name="grooming"
                        value="Disheveled"
                        checked={assessment?.psychology?.grooming === "Disheveled"}
                        label="Disheveled"
                        onChange={(e) => {
                          setGrooming(e.target.value);
                          setGroomingTextareaShown(false);
                        }}
                      />
                    </div>
                  </div>
                  <div>
                    <div className="labelSBARForm cursorPointer rounded-3" style={{ width: "208px" }}>
                      <NurtifyRadio
                        name="grooming"
                        value="Neat"
                        checked={assessment?.psychology?.grooming === "Neat"}
                        label="Neat"
                        onChange={(e) => {
                          setGrooming(e.target.value);
                          setGroomingTextareaShown(false);
                        }}
                      />
                    </div>
                  </div>
                  <div>
                    <div className="labelSBARForm cursorPointer rounded-3" style={{ width: "208px" }}>
                      <NurtifyRadio
                        name="grooming"
                        value="Unkempt"
                        checked={assessment?.psychology?.grooming === "Unkempt"}
                        label="Unkempt"
                        onChange={(e) => {
                          setGrooming(e.target.value);
                          setGroomingTextareaShown(false);
                        }}
                      />
                    </div>
                  </div>
                  <div>
                    <div className="labelSBARForm cursorPointer rounded-3" style={{ width: "208px" }}>
                      <NurtifyRadio
                        name="grooming"
                        value="Odorous"
                        checked={assessment?.psychology?.grooming === "Odorous"}
                        label="Odorous"
                        onChange={(e) => {
                          setGrooming(e.target.value);
                          setGroomingTextareaShown(false);
                        }}
                      />
                    </div>
                  </div>
                  <div>
                    <div className="labelSBARForm cursorPointer rounded-3" style={{ width: "208px" }}>
                      <NurtifyRadio
                        name="grooming"
                        value="Clean"
                        checked={assessment?.psychology?.grooming === "Clean"}
                        onChange={(e) => {
                          setGrooming(e.target.value);
                          setGroomingTextareaShown(false);
                        }}
                        label="Clean"
                      />
                    </div>
                  </div>
                  <div>
                    <div className= "labelSBARForm cursorPointer rounded-3" style={{ width: "208px" }}>
                      <NurtifyRadio
                        name="grooming"
                        checked={assessment?.psychology?.grooming === "Other"}
                        value="Other"
                        label="Other"
                        onChange={(e) => {
                          setGrooming(e.target.value);
                          setGroomingTextareaShown(true);
                        }}
                      />
                    </div>
                  </div>
                </div>

                {groomingTextareaShown && (
                  <div className="col-lg-6 col-xl-6 col-md-12 mt-2">
                    <NurtifyTextArea
                      name="GROOMING"
                      onChange={(e) => setGrooming(e.target.value)}
                      // className="form-control border border-black"
                    />
                  </div>
                )}

                <NurtifyText label="Facial Expression" className="mb-2" />

                <div className="list-group d-flex flex-row gap-2 me-4 mt-2 flex-wrap  mb-4">
                  <div>
                    <div className="labelSBARForm cursorPointer rounded-3" style={{ width: "208px" }}>
                      <NurtifyRadio
                        checked={assessment?.psychology?.facialExpression === "Neutral"}
                        label="Neutral"
                        name="facial_expression"
                        value="Neutral"
                        onChange={(e) => {
                          setFacialExpression(e.target.value);
                          setFacialExpressionTextareaShown(false);
                        }}
                      />
                    </div>
                  </div>
                  <div>
                    <div className="labelSBARForm cursorPointer rounded-3" style={{ width: "208px" }}>
                      <NurtifyRadio
                        checked={assessment?.psychology?.facialExpression === "Happy"}
                        label="Happy"
                        name="facial_expression"
                        value="Happy"
                        onChange={(e) => {
                          setFacialExpression(e.target.value);
                          setFacialExpressionTextareaShown(false);
                        }}
                      />
                    </div>
                  </div>
                  <div>
                    <div className="labelSBARForm cursorPointer rounded-3" style={{ width: "208px" }}>
                      <NurtifyRadio
                        label="Sad"
                        checked={assessment?.psychology?.facialExpression === "Sad"}
                        name="facial_expression"
                        value="Sad"
                        onChange={(e) => {
                          setFacialExpression(e.target.value);
                          setFacialExpressionTextareaShown(false);
                        }}
                      />
                    </div>
                  </div>
                  <div>
                    <div className="labelSBARForm cursorPointer rounded-3" style={{ width: "208px" }}>
                      <NurtifyRadio
                        label="Angry"
                        checked={assessment?.psychology?.facialExpression === "Angry"}
                        name="facial_expression"
                        value="Angry"
                        onChange={(e) => {
                          setFacialExpression(e.target.value);
                          setFacialExpressionTextareaShown(false);
                        }}
                      />
                    </div>
                  </div>
                  <div>
                    <div className="labelSBARForm cursorPointer rounded-3" style={{ width: "208px" }}>
                      <NurtifyRadio
                        checked={assessment?.psychology?.facialExpression === "Anxious"}
                        label="Anxious"
                        name="facial_expression"
                        value="Anxious"
                        onChange={(e) => {
                          setFacialExpression(e.target.value);
                          setFacialExpressionTextareaShown(false);
                        }}
                      />
                    </div>
                  </div>
                  <div>
                    <div className="labelSBARForm cursorPointer rounded-3" style={{ width: "208px" }}>
                      <NurtifyRadio
                        checked={assessment?.psychology?.facialExpression === "Flat affect"}
                        label="Flat affect"
                        name="facial_expression"
                        value="Flat affect"
                        onChange={(e) => {
                          setFacialExpression(e.target.value);
                          setFacialExpressionTextareaShown(false);
                        }}
                      />
                    </div>
                  </div>

                  <div>
                    <div className="labelSBARForm cursorPointer rounded-3" style={{ width: "208px" }}>
                      <NurtifyRadio
                        label="Other"
                        name="facial_expression"
                        value="Other"
                        checked={assessment?.psychology?.facialExpression === "Other"}
                        onChange={(e) => {
                          setFacialExpression(e.target.value);
                          setFacialExpressionTextareaShown(true);
                        }}
                      />
                    </div>
                  </div>
                </div>

                {facialExpressionTextareaShown && (
                  <div className="col-lg-6 col-xl-6 col-md-12 mt-2">
                    <NurtifyTextArea
                      onChange={(e) => setFacialExpression(e.target.value)}
                      name="facial_expression"
                      // className="form-control border border-black"
                    />
                  </div>
                )}

                <NurtifyText label="Body Language/Gestures" className="mb-2" />

                <div className="list-group d-flex flex-row gap-2 me-4 mt-2 flex-wrap  mb-4">
                  <div>
                    <div className="labelSBARForm cursorPointer rounded-3" style={{ width: "208px" }}>
                      <NurtifyRadio
                        checked={assessment?.psychology?.bodyLanguage === "Relaxed"}
                        label="Relaxed"
                        name="body_language"
                        value="Relaxed"
                        onChange={(e) => {
                          setBodyLanguage(e.target.value);
                          setBodyLanguageTextareaShown(false);
                        }}
                      />
                    </div>
                  </div>
                  <div>
                    <div className="labelSBARForm cursorPointer rounded-3" style={{ width: "208px" }}>
                      <NurtifyRadio
                        checked={assessment?.psychology?.bodyLanguage === "Tense"}
                        label="Tense"
                        name="body_language"
                        value="Tense"
                        onChange={(e) => {
                          setBodyLanguage(e.target.value);
                          setBodyLanguageTextareaShown(false);
                        }}
                      />
                    </div>
                  </div>
                  <div>
                    <div className="labelSBARForm cursorPointer rounded-3" style={{ width: "208px" }}>
                      <NurtifyRadio
                        checked={assessment?.psychology?.bodyLanguage === "Open"}
                        label="Open"
                        name="body_language"
                        value="Open"
                        onChange={(e) => {
                          setBodyLanguage(e.target.value);
                          setBodyLanguageTextareaShown(false);
                        }}
                      />
                    </div>
                  </div>
                  <div>
                    <div className="labelSBARForm cursorPointer rounded-3" style={{ width: "208px" }}>
                      <NurtifyRadio
                        checked={assessment?.psychology?.bodyLanguage === "Defensive"}
                        label="Defensive"
                        name="body_language"
                        value="Defensive"
                        onChange={(e) => {
                          setBodyLanguage(e.target.value);
                          setBodyLanguageTextareaShown(false);
                        }}
                      />
                    </div>
                  </div>
                  <div>
                    <div className="labelSBARForm cursorPointer rounded-3" style={{ width: "208px" }}>
                      <NurtifyRadio
                        checked={assessment?.psychology?.bodyLanguage === "Agitated"}
                        label="Agitated"
                        name="body_language"
                        value="Agitated"
                        onChange={(e) => {
                          setBodyLanguage(e.target.value);
                          setBodyLanguageTextareaShown(false);
                        }}
                      />
                    </div>
                  </div>
                  <div>
                    <div className="labelSBARForm cursorPointer rounded-3" style={{ width: "208px" }}>
                      <NurtifyRadio
                        label="Calm"
                        checked={assessment?.psychology?.bodyLanguage === "Calm"}
                        name="body_language"
                        value="Calm"
                        onChange={(e) => {
                          setBodyLanguage(e.target.value);
                          setBodyLanguageTextareaShown(false);
                        }}
                      />
                    </div>
                  </div>
                  <div>
                    <div className="labelSBARForm cursorPointer rounded-3" style={{ width: "208px" }}>
                      <NurtifyRadio
                        label="Engaging"
                        name="body_language"
                        value="Engaging"
                        checked={assessment?.psychology?.bodyLanguage === "Engaging"}
                        onChange={(e) => {
                          setBodyLanguage(e.target.value);
                          setBodyLanguageTextareaShown(false);
                        }}
                      />
                    </div>
                  </div>
                  <div>
                    <div className="labelSBARForm cursorPointer rounded-3" style={{ width: "208px" }}>
                      <NurtifyRadio
                        label="Avoidant"
                        name="body_language"
                        value="Avoidant"
                        checked={assessment?.psychology?.bodyLanguage === "Avoidant"}
                        onChange={(e) => {
                          setBodyLanguage(e.target.value);
                          setBodyLanguageTextareaShown(false);
                        }}
                      />
                    </div>
                  </div>
                  <div>
                    <div className="labelSBARForm cursorPointer rounded-3" style={{ width: "208px" }}>
                      <NurtifyRadio
                        label="Other"
                        name="body_language"
                        value="Other"
                        checked={assessment?.psychology?.bodyLanguage === "Other"}
                        onChange={(e) => {
                          setBodyLanguage(e.target.value);
                          setBodyLanguageTextareaShown(true);
                        }}
                      />
                    </div>
                  </div>
                </div>

                {bodyLanguageTextareaShown && (
                  <div className="col-lg-6 col-xl-6 col-md-12 mt-2">
                    <NurtifyTextArea
                      onChange={(e) => setBodyLanguage(e.target.value)}
                      name="body_language"
                      // className="form-control border border-black"
                    />
                  </div>
                )}

                <NurtifyText label="Ability to Follow Request" className="mb-2" />

                <div className="list-group d-flex flex-row gap-2 me-4 mt-2 flex-wrap  mb-4">
                  <div>
                    <div className="labelSBARForm cursorPointer rounded-3" style={{ width: "208px" }}>
                      <NurtifyRadio
                        label="Follows easily"
                        checked={assessment?.psychology?.abilityToFollowRequest === "Follows easily"}
                        name="ability_to_follow"
                        value="Follows easily"
                        onChange={(e) => {
                          setAbilityToFollowRequest(e.target.value);
                          setAbilityToFollowRequestTextareaShown(false);
                        }}
                      />
                    </div>
                  </div>
                  <div>
                    <div className="labelSBARForm cursorPointer rounded-3" style={{ width: "208px" }}>
                      <NurtifyRadio
                        label="Needs prompting"
                        checked={assessment?.psychology?.abilityToFollowRequest === "Needs prompting"}
                        name="ability_to_follow"
                        value="Needs prompting"
                        onChange={(e) => {
                          setAbilityToFollowRequest(e.target.value);
                          setAbilityToFollowRequestTextareaShown(false);
                        }}
                      />
                    </div>
                  </div>
                  <div>
                    <div className="labelSBARForm cursorPointer rounded-3" style={{ width: "208px" }}>
                      <NurtifyRadio
                        label="Refuses"
                        checked={assessment?.psychology?.abilityToFollowRequest === "Refuses"}
                        name="ability_to_follow"
                        value="Refuses"
                        onChange={(e) => {
                          setAbilityToFollowRequest(e.target.value);
                          setAbilityToFollowRequestTextareaShown(false);
                        }}
                      />
                    </div>
                  </div>
                  <div>
                    <div className="labelSBARForm cursorPointer rounded-3" style={{ width: "208px" }}>
                      <NurtifyRadio
                        label="Slow to respond"
                        checked={assessment?.psychology?.abilityToFollowRequest === "Slow to respond"}
                        name="ability_to_follow"
                        value="Slow to respond"
                        onChange={(e) => {
                          setAbilityToFollowRequest(e.target.value);
                          setAbilityToFollowRequestTextareaShown(false);
                        }}
                      />
                    </div>
                  </div>
                  <div>
                    <div className="labelSBARForm cursorPointer rounded-3" style={{ width: "208px" }}>
                      <NurtifyRadio
                        label="Compliant"
                        checked={assessment?.psychology?.abilityToFollowRequest === "Compliant"}
                        name="ability_to_follow"
                        value="Compliant"
                        onChange={(e) => {
                          setAbilityToFollowRequest(e.target.value);
                          setAbilityToFollowRequestTextareaShown(false);
                        }}
                      />
                    </div>
                  </div>
                  <div>
                    <div className="labelSBARForm cursorPointer rounded-3" style={{ width: "208px" }}>
                      <NurtifyRadio
                        label="Uncooperative"
                        checked={assessment?.psychology?.abilityToFollowRequest === "Uncooperative"}
                        name="ability_to_follow"
                        value="Uncooperative"
                        onChange={(e) => {
                          setAbilityToFollowRequest(e.target.value);
                          setAbilityToFollowRequestTextareaShown(false);
                        }}
                      />
                    </div>
                  </div>
                  <div>
                    <div className="labelSBARForm cursorPointer rounded-3" style={{ width: "208px" }}>
                      <NurtifyRadio
                        label="Other"
                        name="ability_to_follow"
                        value="Other"
                        checked={assessment?.psychology?.abilityToFollowRequest === "Other"}
                        onChange={(e) => {
                          setAbilityToFollowRequest(e.target.value);
                          setAbilityToFollowRequestTextareaShown(true);
                        }}
                      />
                    </div>
                  </div>
                </div>

                {abilityToFollowRequestTextareaShown && (
                  <div className="col-lg-6 col-xl-6 col-md-12 mt-2">
                    <NurtifyTextArea
                      onChange={(e) =>
                        setAbilityToFollowRequest(e.target.value)
                      }
                      name="ability_to_follow_request"
                      // className="form-control border border-black"
                    />
                  </div>
                )}

                <NurtifyText label="Rapport/Engagement" className="mb-2" />
                <div className="list-group d-flex flex-row gap-2 me-4 mt-2 flex-wrap mb-4">
                  <div>
                    <div className="labelSBARForm cursorPointer rounded-3" style={{ width: "208px" }}>
                      <NurtifyRadio
                        label="Engaged"
                        checked={assessment?.psychology?.rapport === "Engaged"}
                        name="rapport"
                        value="Engaged"
                        onChange={(e) => {
                          setRapport(e.target.value);
                          setRapportTextareaShown(false);
                        }}
                      />
                    </div>
                  </div>
                  <div>
                    <div className="labelSBARForm cursorPointer rounded-3" style={{ width: "208px" }}>
                      <NurtifyRadio
                        label="Withdrawn"
                        checked={assessment?.psychology?.rapport === "Withdrawn"}
                        name="rapport"
                        value="Withdrawn"
                        onChange={(e) => {
                          setRapport(e.target.value);
                          setRapportTextareaShown(false);
                        }}
                      />
                    </div>
                  </div>
                  <div>
                    <div className="labelSBARForm cursorPointer rounded-3" style={{ width: "208px" }}>
                      <NurtifyRadio
                        label="Friendly"
                        checked={assessment?.psychology?.rapport === "Friendly"}
                        name="rapport"
                        value="Friendly"
                        onChange={(e) => {
                          setRapport(e.target.value);
                          setRapportTextareaShown(false);
                        }}
                      />
                    </div>
                  </div>
                  <div>
                    <div className="labelSBARForm cursorPointer rounded-3" style={{ width: "208px" }}>
                      <NurtifyRadio
                        label="Hostile"
                        checked={assessment?.psychology?.rapport === "Hostile"}
                        name="rapport"
                        value="Hostile"
                        onChange={(e) => {
                          setRapport(e.target.value);
                          setRapportTextareaShown(false);
                        }}
                      />
                    </div>
                  </div>
                  <div>
                    <div className="labelSBARForm cursorPointer rounded-3" style={{ width: "208px" }}>
                      <NurtifyRadio
                        label="Passive"
                        checked={assessment?.psychology?.rapport === "Passive"}
                        name="rapport"
                        value="Passive"
                        onChange={(e) => {
                          setRapport(e.target.value);
                          setRapportTextareaShown(false);
                        }}
                      />
                    </div>
                  </div>
                  <div>
                    <div className="labelSBARForm cursorPointer rounded-3" style={{ width: "208px" }}>
                      <NurtifyRadio
                        label="Responsive"
                        checked={assessment?.psychology?.rapport === "Responsive"}
                        name="rapport"
                        value="Responsive"
                        onChange={(e) => {
                          setRapport(e.target.value);
                          setRapportTextareaShown(false);
                        }}
                      />
                    </div>
                  </div>
                  <div>
                    <div className="labelSBARForm cursorPointer rounded-3" style={{ width: "208px" }}>
                      <NurtifyRadio
                        label="Apathetic"
                        checked={assessment?.psychology?.rapport === "Apathetic"}
                        name="rapport"
                        value="Apathetic"
                        onChange={(e) => {
                          setRapport(e.target.value);
                          setRapportTextareaShown(false);
                        }}
                      />
                    </div>
                  </div>
                  <div>
                    <div className="labelSBARForm cursorPointer rounded-3" style={{ width: "208px" }}>
                      <NurtifyRadio
                        label="Other"
                        name="rapport"
                        value="Other"
                        checked={assessment?.psychology?.rapport === "Other"}
                        onChange={(e) => {
                          setRapport(e.target.value);
                          setRapportTextareaShown(true);
                        }}
                      />
                    </div>
                  </div>
                </div>

                {rapportTextareaShown && (
                  <div className="col-lg-6 col-xl-6 col-md-12 mt-2">
                    <NurtifyTextArea
                      onChange={(e) => setRapport(e.target.value)}
                      name="rapport"
                      // className="form-control border border-black"
                    />
                  </div>
                )}

                <NurtifyText label="Mood State" className="mb-2" />

                <div className="list-group d-flex flex-row gap-2 me-4 mt-2 flex-wrap  mb-4">
                  <div>
                    <div className="labelSBARForm cursorPointer rounded-3" style={{ width: "208px" }}>
                      <NurtifyRadio
                        checked={assessment?.psychology?.moodState === "Euthymic"}
                        label="Euthymic"
                        name="mood_state"
                        value="Euthymic"
                        onChange={(e) => {
                          setMoodState(e.target.value);
                          setMoodStateTextareaShown(false);
                        }}
                      />
                    </div>
                  </div>
                  <div>
                    <div className="labelSBARForm cursorPointer rounded-3" style={{ width: "208px" }}>
                      <NurtifyRadio
                        checked={assessment?.psychology?.moodState === "Depressed"}
                        label="Depressed"
                        name="mood_state"
                        value="Depressed"
                        onChange={(e) => {
                          setMoodState(e.target.value);
                          setMoodStateTextareaShown(false);
                        }}
                      />
                    </div>
                  </div>
                  <div>
                    <div className="labelSBARForm cursorPointer rounded-3" style={{ width: "208px" }}>
                      <NurtifyRadio
                        checked={assessment?.psychology?.moodState === "Anxious"}
                        label="Anxious"
                        name="mood_state"
                        value="Anxious"
                        onChange={(e) => {
                          setMoodState(e.target.value);
                          setMoodStateTextareaShown(false);
                        }}
                      />
                    </div>
                  </div>
                  <div>
                    <div className="labelSBARForm cursorPointer rounded-3" style={{ width: "208px" }}>
                      <NurtifyRadio
                        checked={assessment?.psychology?.moodState === "Irritable"}
                        label="Irritable"
                        name="mood_state"
                        value="Irritable"
                        onChange={(e) => {
                          setMoodState(e.target.value);
                          setMoodStateTextareaShown(false);
                        }}
                      />
                    </div>
                  </div>
                  <div>
                    <div className="labelSBARForm cursorPointer rounded-3" style={{ width: "208px" }}>
                      <NurtifyRadio
                        checked={assessment?.psychology?.moodState === "Elated"}
                        label="Elated"
                        name="mood_state"
                        value="Elated"
                        onChange={(e) => {
                          setMoodState(e.target.value);
                          setMoodStateTextareaShown(false);
                        }}
                      />
                    </div>
                  </div>
                  <div>
                    <div className="labelSBARForm cursorPointer rounded-3" style={{ width: "208px" }}>
                      <NurtifyRadio
                        checked={assessment?.psychology?.moodState === "Angry"}
                        label="Angry"
                        name="mood_state"
                        value="Angry"
                        onChange={(e) => {
                          setMoodState(e.target.value);
                          setMoodStateTextareaShown(false);
                        }}
                      />
                    </div>
                  </div>
                  <div>
                    <div className="labelSBARForm cursorPointer rounded-3" style={{ width: "208px" }}>
                      <NurtifyRadio
                        checked={assessment?.psychology?.moodState === "Labile"}
                        label="Labile"
                        name="mood_state"
                        value="Labile"
                        onChange={(e) => {
                          setMoodState(e.target.value);
                          setMoodStateTextareaShown(false);
                        }}
                      />
                    </div>
                  </div>
                  <div>
                    <div className="labelSBARForm cursorPointer rounded-3" style={{ width: "208px" }}>
                      <NurtifyRadio
                        checked={assessment?.psychology?.moodState === "Other"}
                        label="Other"
                        name="mood_state"
                        value="Other"
                        onChange={(e) => {
                          setMoodState(e.target.value);
                          setMoodStateTextareaShown(true);
                        }}
                      />
                    </div>
                  </div>
                </div>

                {moodStateTextareaShown && (
                  <div className="col-lg-6 col-xl-6 col-md-12 mt-2">
                    <NurtifyTextArea
                      onChange={(e) => setMoodState(e.target.value)}
                      name="mood_state"
                      // className="form-control border border-black"
                    />
                  </div>
                )}

                <NurtifyText label="Psychomotor Activity" className="mb-2" />

                <div className="list-group d-flex flex-row gap-2 me-4 mt-2 flex-wrap  mb-4">
                  <div>
                    <div className="labelSBARForm cursorPointer rounded-3" style={{ width: "208px" }}>
                      <NurtifyRadio
                        checked={assessment?.psychology?.psychomotorActivity === "Normal"}
                        label="Normal"
                        name="psychomotor_activity"
                        value="Normal"
                        onChange={(e) => {
                          setPsychomotorActivity(e.target.value);
                          setPsychomotorActivityTextareaShown(false);
                        }}
                      />
                    </div>
                  </div>
                  <div>
                    <div className="labelSBARForm cursorPointer rounded-3" style={{ width: "208px" }}>
                      <NurtifyRadio
                        checked={assessment?.psychology?.psychomotorActivity === "Restless"}
                        label="Restless"
                        name="psychomotor_activity"
                        value="Restless"
                        onChange={(e) => {
                          setPsychomotorActivity(e.target.value);
                          setPsychomotorActivityTextareaShown(false);
                        }}
                      />
                    </div>
                  </div>
                  <div>
                    <div className="labelSBARForm cursorPointer rounded-3" style={{ width: "208px" }}>
                      <NurtifyRadio
                        checked={assessment?.psychology?.psychomotorActivity === "Hyperactive"}
                        label="Hyperactive"
                        name="psychomotor_activity"
                        value="Hyperactive"
                        onChange={(e) => {
                          setPsychomotorActivity(e.target.value);
                          setPsychomotorActivityTextareaShown(false);
                        }}
                      />
                    </div>
                  </div>
                  <div>
                    <div className="labelSBARForm cursorPointer rounded-3" style={{ width: "208px" }}>
                      <NurtifyRadio
                        checked={assessment?.psychology?.psychomotorActivity === "Slowed"}
                        label="Slowed"
                        name="psychomotor_activity"
                        value="Slowed"
                        onChange={(e) => {
                          setPsychomotorActivity(e.target.value);
                          setPsychomotorActivityTextareaShown(false);
                        }}
                      />
                    </div>
                  </div>
                  <div>
                    <div className="labelSBARForm cursorPointer rounded-3" style={{ width: "208px" }}>
                      <NurtifyRadio
                        checked={assessment?.psychology?.psychomotorActivity === "Lethargic"}
                        label="Lethargic"
                        name="psychomotor_activity"
                        value="Lethargic"
                        onChange={(e) => {
                          setPsychomotorActivity(e.target.value);
                          setPsychomotorActivityTextareaShown(false);
                        }}
                      />
                    </div>
                  </div>
                  <div>
                    <div className="labelSBARForm cursorPointer rounded-3" style={{ width: "208px" }}>
                      <NurtifyRadio
                        checked={assessment?.psychology?.psychomotorActivity === "Catatonic"}
                        label="Catatonic"
                        name="psychomotor_activity"
                        value="Catatonic"
                        onChange={(e) => {
                          setPsychomotorActivity(e.target.value);
                          setPsychomotorActivityTextareaShown(false);
                        }}
                      />
                    </div>
                  </div>
                  <div>
                    <div className="labelSBARForm cursorPointer rounded-3" style={{ width: "208px" }}>
                      <NurtifyRadio
                        checked={assessment?.psychology?.psychomotorActivity === "Pacing"}
                        label="Pacing"
                        name="psychomotor_activity"
                        value="Pacing"
                        onChange={(e) => {
                          setPsychomotorActivity(e.target.value);
                          setPsychomotorActivityTextareaShown(false);
                        }}
                      />
                    </div>
                  </div>
                  <div>
                    <div className="labelSBARForm cursorPointer rounded-3" style={{ width: "208px" }}>
                      <NurtifyRadio
                        checked={assessment?.psychology?.psychomotorActivity === "Fidgeting"}
                        label="Fidgeting"
                        name="psychomotor_activity"
                        value="Fidgeting"
                        onChange={(e) => {
                          setPsychomotorActivity(e.target.value);
                          setPsychomotorActivityTextareaShown(false);
                        }}
                      />
                    </div>
                  </div>
                  <div>
                    <div className="labelSBARForm cursorPointer rounded-3" style={{ width: "208px" }}>
                      <NurtifyRadio
                        checked={assessment?.psychology?.psychomotorActivity === "Tremors"}
                        label="Tremors"
                        name="psychomotor_activity"
                        value="Tremors"
                        onChange={(e) => {
                          setPsychomotorActivity(e.target.value);
                          setPsychomotorActivityTextareaShown(false);
                        }}
                      />
                    </div>
                  </div>
                  <div>
                    <div className="labelSBARForm cursorPointer rounded-3" style={{ width: "208px" }}>
                      <NurtifyRadio
                        checked={assessment?.psychology?.psychomotorActivity === "Other"}
                        label="Other"
                        name="psychomotor_activity"
                        value="Other"
                        onChange={(e) => {
                          setPsychomotorActivity(e.target.value);
                          setPsychomotorActivityTextareaShown(true);
                        }}
                      />
                    </div>
                  </div>
                </div>

                {psychomotorActivityTextareaShown && (
                  <div className="col-lg-6 col-xl-6 col-md-12 mt-2">
                    <NurtifyTextArea
                      onChange={(e) => setPsychomotorActivity(e.target.value)}
                      name="psychomotor_activity"
                      // className="form-control border border-black"
                    />
                  </div>
                )}

                <NurtifyText label="Level of Arrousal" className="mb-2" />

                <div className="list-group d-flex flex-row gap-2 me-4 mt-2 flex-wrap  mb-4">
                  <div>
                    <div className="labelSBARForm cursorPointer rounded-3" style={{ width: "208px" }}>
                      <NurtifyRadio
                        label="Calm"
                        name="level_of_arousal"
                        checked={assessment?.psychology?.levelOfArousal === "Calm"}
                        value="Calm"
                        onChange={(e) => setLevelOfArousal(e.target.value)}
                      />
                    </div>
                  </div>
                  <div>
                    <div className="labelSBARForm cursorPointer rounded-3" style={{ width: "208px" }}>
                      <NurtifyRadio
                        label="Agitated"
                        name="level_of_arousal"
                        checked={assessment?.psychology?.levelOfArousal === "Agitated"}
                        value="Agitated"
                        onChange={(e) => setLevelOfArousal(e.target.value)}
                      />
                    </div>
                  </div>
                  <div>
                    <div className="labelSBARForm cursorPointer rounded-3" style={{ width: "208px" }}>
                      <NurtifyRadio
                        label="Aggressive"
                        name="level_of_arousal"
                        checked={assessment?.psychology?.levelOfArousal === "Aggressive"}
                        value="Aggressive"
                        onChange={(e) => setLevelOfArousal(e.target.value)}
                      />
                    </div>
                  </div>
                </div>

              <div className="subheading" style={{ width: "220px" }}>
                <NurtifyText label="Obvious signs of Self Harm?" />
              </div>

              <div className="list-group d-flex flex-row gap-2 me-4 mt-2 flex-wrap mb-4">
                <div>
                  <div className="labelSBARForm cursorPointer rounded-3" style={{ width: "208px" }}>
                    <NurtifyRadio
                      name="self_harm"
                      value="Yes"
                      label="Yes"
                      checked={assessment?.psychology?.obviousSelfHarm === "Yes"}
                      onChange={() => setObviousSelfHarm("Yes")}
                    />
                  </div>
                </div>

                <div>
                  <div className="labelSBARForm cursorPointer rounded-3" style={{ width: "208px" }}>
                    <NurtifyRadio
                      name="self_harm"
                      value="No"
                      label="No"
                      checked={assessment?.psychology?.obviousSelfHarm === "No"}
                      onChange={() => setObviousSelfHarm("No")}
                    />
                  </div>
                </div>
              </div>

              <div className="subheading" style={{ width: "220px" }}>
                <NurtifyText label="Eye Contact" />
              </div>

              <div className="list-group d-flex flex-row gap-2 me-4 mt-2 flex-wrap mb-4">
                <div>
                  <div className="labelSBARForm cursorPointer rounded-3" style={{ width: "208px" }}>
                    <NurtifyRadio
                      name="eye_contact"
                      value="Yes"
                      label="Yes"
                      checked={assessment?.psychology?.eyeContact === "Yes"}
                      onChange={() => setEyeContact("Yes")}
                    />
                  </div>
                </div>

                <div>
                  <div className="labelSBARForm cursorPointer rounded-3" style={{ width: "208px" }}>
                    <NurtifyRadio
                      name="eye_contact"
                      value="No"
                      label="No"
                      checked={assessment?.psychology?.eyeContact === "No"}
                      onChange={() => setEyeContact("No")}
                    />
                  </div>
                </div>
              </div>
              </div>
            </div>
            {/* Appearance MH Assessment END */}

            <div className="d-flex flex-column mb-2 border border-1 badge border border-primary-subtle">
              <span className=" headinqQuestion" style={{ width: "400px" }}>
                Prearrival substance Misuse:
              </span>

              <div className="subheading" style={{ width: "220px" }}>
                <NurtifyText label="Alcohol" />
              </div>

              <div className="list-group d-flex flex-row gap-2 me-4 mt-2 flex-wrap mb-4">
                <div>
                  <div className="labelSBARForm cursorPointer rounded-3" style={{ width: "208px" }}>
                    <NurtifyRadio
                      name="alcohol"
                      value="Yes"
                      label="Yes"
                      checked={assessment?.psychology?.alcoholAssociated === "Yes"}
                      onChange={() => setAlcoholAssociated("Yes")}
                    />
                  </div>
                </div>
                <div>
                  <div className="labelSBARForm cursorPointer rounded-3" style={{ width: "208px" }}>
                    <NurtifyRadio
                      name="alcohol"
                      value="No"
                      label="No"
                      checked={assessment?.psychology?.alcoholAssociated === "No"}
                      onChange={() => setAlcoholAssociated("No")}
                    />
                  </div>
                </div>
              </div>

              <div className="subheading" style={{ width: "220px" }}>
                <NurtifyText label="Recreational Drugs" />
              </div>

              <div className="list-group d-flex flex-row gap-2 me-4 mt-2 flex-wrap mb-4">
                <div>
                  <div className="labelSBARForm cursorPointer rounded-3" style={{ width: "208px" }}>
                    <NurtifyRadio
                      name="drugs"
                      value="Yes"
                      label="Yes"
                      checked={assessment?.psychology?.drugsAssociated === "Yes"}
                      onChange={() => setDrugsAssociated("Yes")}
                    />
                  </div>
                </div>
                <div>
                  <div className="labelSBARForm cursorPointer rounded-3" style={{ width: "208px" }}>
                    <NurtifyRadio
                      name="drugs"
                      value="No"
                      label="No"
                      checked={assessment?.psychology?.drugsAssociated === "No"}
                      onChange={() => setDrugsAssociated("No")}
                    />
                  </div>
                </div>
              </div>

              <div className="subheading" style={{ width: "220px" }}>
                <NurtifyText label="Medication Overdose" />
              </div>

              <div className="list-group d-flex flex-row gap-2 me-4 mt-2 flex-wrap mb-4">
                <div>
                  <div className="labelSBARForm cursorPointer rounded-3" style={{ width: "208px" }}>
                    <NurtifyRadio
                      name="overdose"
                      value="Yes"
                      label="Yes"
                      checked={assessment?.psychology?.overdoseAssociated === "Yes"}
                      onChange={() => setOverdoseAssociated("Yes")}
                    />
                  </div>
                </div>
                <div>
                  <div className="labelSBARForm cursorPointer rounded-3" style={{ width: "208px" }}>
                    <NurtifyRadio
                      name="overdose"
                      value="No"
                      label="No"
                      checked={assessment?.psychology?.overdoseAssociated === "No"}
                      onChange={() => setOverdoseAssociated("No")}
                    />
                  </div>
                </div>
              </div>

              <NurtifyText label="Add Used Substance" className="mb-2" />

              <div className="medications d-flex flex-wrap flex-row gap-2 align-items-center justify-content-start">
                {mentalMedications.map((med, index) => {
                  return (
                    <div key={index} className="medication mt-2" style={{backgroundColor: "#112D4E", color: "white", padding: "10px", borderRadius: "10px", width: "15%"}}>
                      <span>{med.d1}</span>
                    </div>
                  );
                })}
              </div>
              <button
                onClick={() => {
                  setOptionsModalShowing(true);
                  setAddMentalMedicationFormShowing(true);
                }}
                className="btn-nurtify mt-3"
                style={{ width: "400px" }}
              >
                If Yes please Add all known used substance{" "}
                <FontAwesomeIcon icon={faAdd} />
              </button>
            </div>

            {/* Speech MH  */}
            <div className="d-flex flex-column mb-2 border border-1 badge border border-primary-subtle">
              <span className=" headinqQuestion" style={{ width: "400px" }}>
                Speech
              </span>

              <NurtifyText label="Rate" className="subheading" />

              <div className="list-group d-flex flex-row gap-2 me-4 mt-2 flex-wrap mb-4">
                <div>
                  <div className="labelSBARForm cursorPointer rounded-3" style={{ width: "208px" }}>
                    <NurtifyRadio
                      label="Normal"
                      checked={assessment?.psychology?.rate === "Normal"}
                      name="rate"
                      value="Normal"
                      onChange={(e) => setRate(e.target.value)}
                    />
                  </div>
                </div>
                <div>
                  <div className="labelSBARForm cursorPointer rounded-3" style={{ width: "208px" }}>
                    <NurtifyRadio
                      label="Pressured"
                      checked={assessment?.psychology?.rate === "Pressured"}
                      name="rate"
                      value="Pressured"
                      onChange={(e) => setRate(e.target.value)}
                    />
                  </div>
                </div>
                <div>
                  <div className="labelSBARForm cursorPointer rounded-3" style={{ width: "208px" }}>
                    <NurtifyRadio
                      label="Slowed"
                      checked={assessment?.psychology?.rate === "Slowed"}
                      name="rate"
                      value="Slowed"
                      onChange={(e) => setRate(e.target.value)}
                    />
                  </div>
                </div>
                <div>
                  <div className="labelSBARForm cursorPointer rounded-3" style={{ width: "208px" }}>
                    <NurtifyRadio
                      label="Halting"
                      checked={assessment?.psychology?.rate === "Halting"}
                      name="rate"
                      value="Halting"
                      onChange={(e) => setRate(e.target.value)}
                    />
                  </div>
                </div>
              </div>

              <NurtifyText label="Quantity" className="subheading" />

              <div className="list-group d-flex flex-row flex-wrap gap-2 me-4 mt-2 mb-4">
                <div>
                  <div className="labelSBARForm cursorPointer rounded-3" style={{ width: "208px" }}>
                    <NurtifyRadio
                      label="Normal"
                      checked={assessment?.psychology?.quantity === "Normal"}
                      name="quantity"
                      value="Normal"
                      onChange={(e) => setQuantity(e.target.value)}
                    />
                  </div>
                </div>
                <div>
                  <div className="labelSBARForm cursorPointer rounded-3" style={{ width: "208px" }}>
                    <NurtifyRadio
                      label="Minimal"
                      checked={assessment?.psychology?.quantity === "Minimal"}
                      name="quantity"
                      value="Minimal"
                      onChange={(e) => setQuantity(e.target.value)}
                    />
                  </div>
                </div>
                <div>
                  <div className="labelSBARForm cursorPointer rounded-3" style={{ width: "208px" }}>
                    <NurtifyRadio
                      label="Excessive"
                      checked={assessment?.psychology?.quantity === "Excessive"}
                      name="quantity"
                      value="Excessive"
                      onChange={(e) => setQuantity(e.target.value)}
                    />
                  </div>
                </div>
                <div>
                  <div className="labelSBARForm cursorPointer rounded-3" style={{ width: "208px" }}>
                    <NurtifyRadio
                      label="Absent"
                      name="quantity"
                      value="Absent"
                      checked={assessment?.psychology?.quantity === "Absent"}
                      onChange={(e) => setQuantity(e.target.value)}
                    />
                  </div>
                </div>
              </div>

              <NurtifyText label="Tone" className="subheading"/>

              <div className="list-group d-flex flex-row flex-wrap gap-2 me-4 mt-2 mb-4">
                <div>
                  <div className="labelSBARForm cursorPointer rounded-3" style={{ width: "208px" }}>
                    <NurtifyRadio
                      label="Monotonous"
                      name="tone"
                      value="Monotonous"
                      checked={assessment?.psychology?.tone === "Monotonous"}
                      onChange={(e) => setTone(e.target.value)}
                    />
                  </div>
                </div>
                <div>
                  <div className="labelSBARForm cursorPointer rounded-3" style={{ width: "208px" }}>
                    <NurtifyRadio
                      label="Tremulous"
                      name="tone"
                      value="Tremulous"
                      checked={assessment?.psychology?.tone === "Tremulous"}
                      onChange={(e) => setTone(e.target.value)}
                    />
                  </div>
                </div>
                <div>
                  <div className="labelSBARForm cursorPointer rounded-3" style={{ width: "208px" }}>
                    <NurtifyRadio
                      label="Flat"
                      name="tone"
                      value="Flat"
                      checked={assessment?.psychology?.tone === "Flat"}
                      onChange={(e) => setTone(e.target.value)}
                    />
                  </div>
                </div>
              </div>

              <div className="list-group d-flex flex-row flex-wrap gap-2 me-4 mt-2 mb-4">
                <div>
                  <div className="labelSBARForm cursorPointer rounded-3" style={{ width: "208px" }}>
                    <NurtifyRadio
                      label="Warm"
                      name="tone"
                      value="Warm"
                      checked={assessment?.psychology?.tone === "Warm"}
                      onChange={(e) => setTone(e.target.value)}
                    />
                  </div>
                </div>
                <div>
                  <div className="labelSBARForm cursorPointer rounded-3" style={{ width: "208px" }}>
                    <NurtifyRadio
                      label="Harsh"
                      name="tone"
                      checked={assessment?.psychology?.tone === "Harsh"}
                      value="Harsh"
                      onChange={(e) => setTone(e.target.value)}
                    />
                  </div>
                </div>
                <div>
                  <div className="labelSBARForm cursorPointer rounded-3" style={{ width: "208px" }}>
                    <NurtifyRadio
                      label="Irritable"
                      name="tone"
                      value="Irritable"
                      checked={assessment?.psychology?.tone === "Irritable"}
                      onChange={(e) => setTone(e.target.value)}
                    />
                  </div>
                </div>
              </div>

              <NurtifyText label="Fluency and Rythm" className="subheading" />

              <div className="list-group d-flex flex-row flex-wrap gap-2 me-4 mt-2 mb-4">
                <div>
                  <div className="labelSBARForm cursorPointer rounded-3" style={{ width: "208px" }}>
                    <NurtifyRadio
                      label="Fluent"
                      name="fluency_and_rhythm"
                      value="Fluent"
                      checked={assessment?.psychology?.fluencyAndRhythm === "Fluent"}
                      onChange={(e) => setFluencyAndRhythm(e.target.value)}
                    />
                  </div>
                </div>
                <div>
                  <div className="labelSBARForm cursorPointer rounded-3" style={{ width: "208px" }}>
                    <NurtifyRadio
                      label="Stuttering"
                      name="fluency_and_rhythm"
                      value="Stuttering"
                      checked={assessment?.psychology?.fluencyAndRhythm === "Stuttering"}
                      onChange={(e) => setFluencyAndRhythm(e.target.value)}
                    />
                  </div>
                </div>
                <div>
                  <div className="labelSBARForm cursorPointer rounded-3" style={{ width: "208px" }}>
                    <NurtifyRadio
                      label="Stammering"
                      name="fluency_and_rhythm"
                      value="Stammering"
                      checked={assessment?.psychology?.fluencyAndRhythm === "Stammering"}
                      onChange={(e) => setFluencyAndRhythm(e.target.value)}
                    />
                  </div>
                </div>
              </div>

              <div className="list-group d-flex flex-row flex-wrap gap-2 me-4 mt-2 mb-4">
                <div>
                  <div className="labelSBARForm cursorPointer rounded-3" style={{ width: "208px" }}>
                    <NurtifyRadio
                      label="Disfluent"
                      name="fluency_and_rhythm"
                      value="Disfluent"
                      checked={assessment?.psychology?.fluencyAndRhythm === "Disfluent"}
                      onChange={(e) => setFluencyAndRhythm(e.target.value)}
                    />
                  </div>
                </div>
                <div>
                  <div className="labelSBARForm cursorPointer rounded-3" style={{ width: "208px" }}>
                    <NurtifyRadio
                      label="Rhythmic"
                      name="fluency_and_rhythm"
                      value="Rhythmic"
                      checked={assessment?.psychology?.fluencyAndRhythm === "Rhythmic"}
                      onChange={(e) => setFluencyAndRhythm(e.target.value)}
                    />
                  </div>
                </div>
                <div>
                  <div className="labelSBARForm cursorPointer rounded-3" style={{ width: "208px" }}>
                    <NurtifyRadio
                      label="Choppy"
                      name="fluency_and_rhythm"
                      value="Choppy"
                      checked={assessment?.psychology?.fluencyAndRhythm === "Choppy"}
                      onChange={(e) => setFluencyAndRhythm(e.target.value)}
                    />
                  </div>
                </div>
              </div>

              <NurtifyText label="Speech Volume" className="subheading" />

              <div className="list-group d-flex flex-row flex-wrap gap-2 me-4 mt-2 mb-4">
                <div>
                  <div className="labelSBARForm cursorPointer rounded-3" style={{ width: "208px" }}>
                    <NurtifyRadio
                      label="Normal"
                      name="volume"
                      value="Normal"
                      checked={assessment?.psychology?.volume === "Normal"}
                      onChange={(e) => setVolume(e.target.value)}
                    />
                  </div>
                </div>
                <div>
                  <div className="labelSBARForm cursorPointer rounded-3" style={{ width: "208px" }}>
                    <NurtifyRadio
                      label="Loud"
                      name="volume"
                      value="Loud"
                      checked={assessment?.psychology?.volume === "Loud"}
                      onChange={(e) => setVolume(e.target.value)}
                    />
                  </div>
                </div>
                <div>
                  <div className="labelSBARForm cursorPointer rounded-3" style={{ width: "208px" }}>
                    <NurtifyRadio
                      label="Soft"
                      name="volume"
                      value="Soft"
                      checked={assessment?.psychology?.volume === "Soft"}
                      onChange={(e) => setVolume(e.target.value)}
                    />
                  </div>
                </div>
              </div>

              <div className="list-group d-flex flex-row flex-wrap gap-2 me-4 mt-2 mb-4">
                <div>
                  <div className="labelSBARForm cursorPointer rounded-3" style={{ width: "208px" }}>
                    <NurtifyRadio
                      label="Whispered"
                      name="volume"
                      value="Whispered"
                      checked={assessment?.psychology?.volume === "Whispered"}
                      onChange={(e) => setVolume(e.target.value)}
                    />
                  </div>
                </div>
                <div>
                  <div className="labelSBARForm cursorPointer rounded-3" style={{ width: "208px" }}>
                    <NurtifyRadio
                      label="Shouting"
                      name="volume"
                      value="Shouting"
                      checked={assessment?.psychology?.volume === "Shouting"}
                      onChange={(e) => setVolume(e.target.value)}
                    />
                  </div>
                </div>
                <div>
                  <div className="labelSBARForm cursorPointer rounded-3" style={{ width: "208px" }}>
                    <NurtifyRadio
                      label="Muffled"
                      name="volume"
                      value="Muffled"
                      checked={assessment?.psychology?.volume === "Muffled"}
                      onChange={(e) => setVolume(e.target.value)}
                    />
                  </div>
                </div>
              </div>
            </div>

            {/* Speech MH END  */}

            {/* Associated Thoughts */}

            <div className="d-flex flex-column mb-2 border border-1 badge border border-primary-subtle">
              <span className=" headinqQuestion" style={{ width: "400px" }}>
                Associated Thoughts
              </span>

              <span className="subheading" style={{ width: "220px" }}>
                <NurtifyText label="Suicidal Thoughts" />
              </span>

              <div className="list-group d-flex flex-row gap-2 me-4 mt-2 flex-wrap  mb-4">
                <div>
                  <div className="labelSBARForm cursorPointer rounded-3" style={{ width: "208px" }}>
                    <NurtifyRadio
                      label="YES"
                      name="suicidal_thoughts"
                      value="YES"
                      checked={assessment?.psychology?.suicidalThoughts === "YES"}
                      onChange={(e) => setSuicidalThoughts(e.target.value)}
                    />
                  </div>
                </div>
                <div>
                  <div className="labelSBARForm cursorPointer rounded-3" style={{ width: "208px" }}>
                    <NurtifyRadio
                      label="NO"
                      name="suicidal_thoughts"
                      value="NO"
                      checked={assessment?.psychology?.suicidalThoughts === "NO"}
                      onChange={(e) => setSuicidalThoughts(e.target.value)}
                    />
                  </div>
                </div>
              </div>

              <span className="subheading" style={{ width: "220px" }}>
                <NurtifyText label="Any Suicidal Plan in Place" />
              </span>

              <div className="list-group d-flex flex-row gap-2 me-4 mt-2 flex-wrap  mb-4">
                <div>
                  <div className="labelSBARForm cursorPointer rounded-3" style={{ width: "208px" }}>
                    <NurtifyRadio
                      label="YES"
                      name="suicidal_plan"
                      value="YES"
                      checked={assessment?.psychology?.suicidalPlanInPlace === "YES"}
                      onChange={(e) => setSuicidalPlanInPlace(e.target.value)}
                    />
                  </div>
                </div>
                <div>
                  <div className="labelSBARForm cursorPointer rounded-3" style={{ width: "208px" }}>
                    <NurtifyRadio
                      label="NO"
                      name="suicidal_plan"
                      value="NO"
                      checked={assessment?.psychology?.suicidalPlanInPlace === "NO"}
                      onChange={(e) => setSuicidalPlanInPlace(e.target.value)}
                    />
                  </div>
                </div>
                <div>
                  <div className="labelSBARForm cursorPointer rounded-3" style={{ width: "208px" }}>
                    <NurtifyRadio
                      label="N/A"
                      name="suicidal_plan"
                      value="N/A"
                      checked={assessment?.psychology?.suicidalPlanInPlace === "N/A"}
                      onChange={(e) => setSuicidalPlanInPlace(e.target.value)}
                    />
                  </div>
                </div>
              </div>

              {assessment?.psychology?.suicidalPlanInPlace === "YES" && (
                <div>
                  <div className="subheading" style={{ width: "220px" }}>
                    <NurtifyText label="Suicidal Plan Description" />
                  </div>

                  <div className="buttonTitle d-flex align-items-center mt-2 col-xl-6 col-md-12">
                    <NurtifyTextArea
                      onChange={(e) => setSuicidalPlanDesc(e.target.value)}
                      name="suicide_plan_desc"
                      // className="form-control mb-3 border border-info"
                    />
                  </div>
                </div>
              )}

              <span className="subheading" style={{ width: "350px" }}>
                <NurtifyText label="Any Previous Suicidal attempt in the past?" />
              </span>

              <div className="list-group d-flex flex-row gap-2 me-4 mt-2 flex-wrap  mb-4">
                <div>
                  <div className="labelSBARForm cursorPointer rounded-3" style={{ width: "208px" }}>
                    <NurtifyRadio
                      checked={assessment?.psychology?.PreviousSuidicalAttempt === "YES"}
                      label="YES"
                      name="suicide_attempt_in_past"
                      value="YES"
                      onChange={(e) => setPreviousSuidicalAttempt(e.target.value)}
                    />
                  </div>
                </div>
                <div>
                  <div className="labelSBARForm cursorPointer rounded-3" style={{ width: "208px" }}>
                    <NurtifyRadio
                      checked={assessment?.psychology?.PreviousSuidicalAttempt === "NO"}
                      label="NO"
                      name="suicide_attempt_in_past"
                      value="NO"
                      onChange={(e) => setPreviousSuidicalAttempt(e.target.value)}
                    />
                  </div>
                </div>
              </div>

              <span className="subheading" style={{ width: "220px" }}>
                <NurtifyText label="Thoughts of harm-other" />
              </span>

              <div className="list-group d-flex flex-row gap-2 me-4 mt-2 flex-wrap  mb-4">
                <div>
                  <div className="labelSBARForm cursorPointer rounded-3" style={{ width: "208px" }}>
                    <NurtifyRadio
                      label="YES"
                      name="harm_others_thoughts"
                      value="YES"
                      checked={assessment?.psychology?.HarmOthers === "YES"}
                      onChange={(e) => setHarmOthers(e.target.value)}
                    />
                  </div>
                </div>
                <div>
                  <div className="labelSBARForm cursorPointer rounded-3" style={{ width: "208px" }}>
                    <NurtifyRadio
                      label="NO"
                      name="harm_others_thoughts"
                      value="NO"
                      checked={assessment?.psychology?.HarmOthers === "NO"}
                      onChange={(e) => setHarmOthers(e.target.value)}
                    />
                  </div>
                </div>
              </div>

              <span className="subheading" style={{ width: "220px" }}>
                <NurtifyText label="Homocidal/Violent thoughts" />
              </span>

              <div className="list-group d-flex flex-row gap-2 me-4 mt-2 flex-wrap  mb-4">
                <div>
                  <div className="labelSBARForm cursorPointer rounded-3" style={{ width: "208px" }}>
                    <NurtifyRadio
                      label="YES"
                      name="homicidal_thoughts"
                      value="YES"
                      checked={assessment?.psychology?.HomicidalThoughts === "YES"}
                      onChange={(e) => setHomicidalThoughts(e.target.value)}
                    />
                  </div>
                </div>
                <div>
                  <div className="labelSBARForm cursorPointer rounded-3" style={{ width: "208px" }}>
                    <NurtifyRadio
                      label="NO"
                      name="homicidal_thoughts"
                      checked={assessment?.psychology?.HomicidalThoughts === "NO"}
                      value="NO"
                      onChange={(e) => setHomicidalThoughts(e.target.value)}
                    />
                  </div>
                </div>
              </div>

              <span className="subheading" style={{ width: "220px" }}>
                <NurtifyText label="Hallucination" />
              </span>

              <div className="list-group d-flex flex-row gap-2 me-4 mt-2 flex-wrap  mb-4">
                <div>
                  <div className="labelSBARForm cursorPointer rounded-3" style={{ width: "208px" }}>
                    <NurtifyRadio
                      checked={assessment?.psychology?.Halucination === "Yes"}
                      label="YES"
                      name="hallucination"
                      value="Yes"
                      onChange={(e) => setHalucination(e.target.value)}
                    />
                  </div>
                </div>
                <div>
                  <div className="labelSBARForm cursorPointer rounded-3" style={{ width: "208px" }}>
                    <NurtifyRadio
                      checked={assessment?.psychology?.Halucination === "No"}
                      label="NO"
                      name="hallucination"
                      value="No"
                      onChange={(e) => {
                        setHalucination(e.target.value);
                      }}
                    />
                  </div>
                </div>
              </div>
              {/* Condition if hallucination == yes */}

              {assessment?.psychology?.Halucination === "Yes" && (
                <div className="align-items-start">
                  <div className="subheading align-items-start">
                    <NurtifyText label="Please describe how the patient is hallucinating" />
                  </div>

                  <div className="buttonTitle d-flex align-items-start mt-2 col-xl-6 col-md-12">
                    <NurtifyTextArea
                      onChange={(e) => setHalucinationDesc(e.target.value)}
                      name="halucinating_desc"
                      // className="form-control mb-3 border border-info"
                    />
                  </div>
                </div>
              )}
            </div>

            <div className="d-flex flex-column mb-2 border border-1 badge border border-primary-subtle">
              <span className=" headinqQuestion" style={{ width: "400px" }}>
                Cognition
              </span>

              <span className="subheading" style={{ width: "220px" }}>
                <NurtifyText label="Orientation" />
              </span>

              <div className="list-group d-flex flex-row gap-2 me-4 mt-2 flex-wrap  mb-4">
                <div>
                  <div className="labelSBARForm cursorPointer rounded-3" style={{ width: "208px" }}>
                    <NurtifyRadio
                      label="YES"
                      checked={assessment?.psychology?.Orientation === "YES"}
                      name="orientation"
                      value="YES"
                      onChange={(e) => setOrientation(e.target.value)}
                    />
                  </div>
                </div>
                <div>
                  <div className="labelSBARForm cursorPointer rounded-3" style={{ width: "208px" }}>
                    <NurtifyRadio
                      label="NO"
                      checked={assessment?.psychology?.Orientation === "NO"}
                      name="orientation"
                      value="NO"
                      onChange={(e) => setOrientation(e.target.value)}
                    />
                  </div>
                </div>
              </div>

              <span className="subheading" style={{ width: "350px" }}>
                <NurtifyText label="Is the patient asking for help?" />
              </span>

              <div className="list-group d-flex flex-row gap-2 me-4 mt-2 flex-wrap  mb-4">
                <div>
                  <div className="labelSBARForm cursorPointer rounded-3" style={{ width: "208px" }}>
                    <NurtifyRadio
                      label="YES"
                      checked={assessment?.psychology?.AskingForHelp === "YES"}
                      name="asking_for_help"
                      value="YES"
                      onChange={(e) => setAskingForHelp(e.target.value)}
                    />
                  </div>
                </div>
                <div>
                  <div className="labelSBARForm cursorPointer rounded-3" style={{ width: "208px" }}>
                    <NurtifyRadio
                      label="NO"
                      checked={assessment?.psychology?.AskingForHelp === "NO"}
                      name="asking_for_help"
                      value="NO"
                      onChange={(e) => setAskingForHelp(e.target.value)}
                    />
                  </div>
                </div>
              </div>
            </div>

            <div className="headinqQuestion" style={{ width: "400px" }}>
              <NurtifyText label="ANY OTHER DETAILS" className="subheading" />
            </div>
            <NurtifyTextArea
              rows={10}
              cols={60}
              onChange={(e) => setOtherMentalHealthDetails(e.target.value)}
              placeholder="Type here..."
              // className="form-control mb-3 border border-info"
              name="other_mental_concerns_details"
            />
          </form>
        ) : null}
        {addMentalMedicationFormShowing === true ? (
          <div className="options-modal">
            <div className="center">
              <>
                <form
                  onSubmit={handleAddMentalMedications}
                  className="d-flex flex-column gap-1"
                >
                  <div className="input">
                    <NurtifyText label="Medication" className="subheading" />
                    <NurtifyInput
                      type="text"
                      name="med_name"
                      className="form-control mb-3"
                      value={medName}
                      onChange={(e) => setMedName(e.target.value)}
                    />
                  </div>
                  <div className="input">
                    <NurtifyText label="Dose" className="subheading" />
                    <NurtifyInput
                      type="text"
                      name="dose"
                      className="form-control mb-3"
                      value={dose}
                      onChange={(e) => setDose(e.target.value)}
                    />
                  </div>
                  <div className="input">
                    <NurtifyText label="Date" className="subheading" />
                    <NurtifyDateInput
                      name="date"
                      className="form-control mb-3"
                      value={date}
                      onChange={(e) => setDate(e.target.value)}
                    />
                  </div>
                  <div className="input">
                    <NurtifyText label="Time" className="subheading" />
                    <NurtifyInput
                      type="time"
                      name="time"
                      className="form-control mb-3"
                      value={time}
                      onChange={(e) => setTime(e.target.value)}
                    />
                  </div>
                  <div className="d-flex btns gap-3 mt-3 flex-row">
                    <button
                      onClick={(e) => {
                        e.preventDefault();
                        setOptionsModalShowing(false);
                        setAddMentalMedicationFormShowing(false);
                      }}
                      className="btn btn-secondary"
                    >
                      CANCEL
                    </button>
                    <button type="submit" className="my-primary-btn">
                      save
                    </button>
                  </div>
                </form>
              </>
            </div>
          </div>
        ) : null}
      </div>
  );
};
export default Psychology;

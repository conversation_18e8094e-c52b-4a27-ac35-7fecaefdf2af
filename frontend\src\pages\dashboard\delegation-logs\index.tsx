import { useState, useMemo, useCallback } from "react";
import { Search, FileText, User, Clock, CheckCircle, XCircle, List } from "lucide-react";
import { toast } from "sonner";
import DataTable from "@/components/common/DataTable";
import { useCurrentUserQuery } from "@/hooks/user.query";
import { useDelegationLogsQuery, usePiApproveDelegationLogMutation, usePiRejectDelegationLogMutation } from "@/hooks/delegation.query";
import Preloader from "@/components/common/Preloader";
import Wrapper from "@/components/common/Wrapper";
import DelegationReviewModal from "./DelegationReviewModal";
import TasksModal from "./TasksModal";
import { SelectedTask } from "@/services/api/delegation.service";
import NurtifyFilter, { NurtifyFilterItem } from "@/components/NurtifyFilter";
import "./delegation-logs.css";

interface DelegationLogListItem {
  uuid: string;
  study_name: string;
  department_name: string;
  team_member_name: string;
  pi_name: string;
  study_task: string;
  selected_tasks?: SelectedTask[];
  status: string;
  pi_notes: string | null;
  sponsor_notes: string | null;
  created_at: string;
  updated_at: string;
}

export default function DelegationLogsReview(): JSX.Element {
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedStatus, setSelectedStatus] = useState<string[]>([]);
  const [selectedDelegation, setSelectedDelegation] = useState<DelegationLogListItem | null>(null);
  const [showReviewModal, setShowReviewModal] = useState(false);
  const [reviewAction, setReviewAction] = useState<'approve' | 'reject' | null>(null);
  const [showTasksModal, setShowTasksModal] = useState(false);
  const [selectedTasks, setSelectedTasks] = useState<SelectedTask[]>([]);
  const [selectedStudyName, setSelectedStudyName] = useState("");
  const [selectedTeamMemberName, setSelectedTeamMemberName] = useState("");
  
  const { data: currentUser } = useCurrentUserQuery();
  const { data: delegationLogs, isLoading, isError } = useDelegationLogsQuery();
  const approveDelegationMutation = usePiApproveDelegationLogMutation();
  const rejectDelegationMutation = usePiRejectDelegationLogMutation();

  // Filter delegation logs for PIs (only show logs where current user is the PI)
  const piDelegationLogs = useMemo(() => {
    if (!delegationLogs?.results || !currentUser) return [];
    
    return delegationLogs.results.filter(log => 
      log.pi_name === `${currentUser.first_name} ${currentUser.last_name}`
    );
  }, [delegationLogs, currentUser]);

  // Define the filter options for status
  const statusOptions = useMemo(() => [
    { label: "Pending PI Review", value: "pending_pi" },
    { label: "Pending Sponsor Review", value: "pending_sponsor" },
    { label: "Accepted", value: "accepted" },
    { label: "PI Rejected", value: "pi_rejected" },
    { label: "Sponsor Rejected", value: "sponsor_rejected" }
  ], []);

  // Create a handler function to bridge the type mismatch
  const handleFilterChange = useCallback((value: string | string[]) => {
    if (Array.isArray(value)) {
      setSelectedStatus(value);
    }
  }, []);

  // Define the filters array for the NurtifyFilter component, explicitly typed
  const filters: NurtifyFilterItem[] = useMemo(() => [
    {
      label: "Status",
      type: "checkbox",
      options: statusOptions,
      value: selectedStatus,
      onChange: handleFilterChange,
    },
  ], [statusOptions, selectedStatus, handleFilterChange]);

  // Filter by search term and selected status
  const filteredDelegationLogs = useMemo(() => {
    if (!piDelegationLogs) return [];
    
    return piDelegationLogs.filter((log: DelegationLogListItem) => {
      const matchesSearch = 
        log.study_name?.toLowerCase().includes(searchTerm.toLowerCase()) ||
        log.team_member_name?.toLowerCase().includes(searchTerm.toLowerCase()) ||
        log.department_name?.toLowerCase().includes(searchTerm.toLowerCase()) ||
        log.study_task?.toLowerCase().includes(searchTerm.toLowerCase()) ||
        log.status?.toLowerCase().includes(searchTerm.toLowerCase());
      
      const matchesStatus = selectedStatus.length === 0 || selectedStatus.includes(log.status);
      
      return matchesSearch && matchesStatus;
    });
  }, [piDelegationLogs, searchTerm, selectedStatus]);

  const handleReviewDelegation = useCallback((delegation: DelegationLogListItem, action: 'approve' | 'reject') => {
    setSelectedDelegation(delegation);
    setReviewAction(action);
    setShowReviewModal(true);
  }, []);

  const handleConfirmReview = async (notes: string) => {
    if (!selectedDelegation || !reviewAction) return;

    try {
      if (reviewAction === 'approve') {
        await approveDelegationMutation.mutateAsync({
          delegationUuid: selectedDelegation.uuid,
          piNotes: notes
        });
        toast.success("Delegation log approved successfully!");
      } else {
        await rejectDelegationMutation.mutateAsync({
          delegationUuid: selectedDelegation.uuid,
          piNotes: notes
        });
        toast.success("Delegation log rejected successfully!");
      }

      // Close modal and reset state
      setShowReviewModal(false);
      setSelectedDelegation(null);
      setReviewAction(null);
    } catch (error) {
      console.error(`Error ${reviewAction}ing delegation log:`, error);
      toast.error(`Failed to ${reviewAction} delegation log. Please try again.`);
    }
  };

  const handleCloseReviewModal = () => {
    setShowReviewModal(false);
    setSelectedDelegation(null);
    setReviewAction(null);
  };

  const handleShowTasks = useCallback((delegation: DelegationLogListItem) => {
    setSelectedTasks(delegation.selected_tasks || []);
    setSelectedStudyName(delegation.study_name);
    setSelectedTeamMemberName(delegation.team_member_name);
    setShowTasksModal(true);
  }, []);

  const handleCloseTasksModal = () => {
    setShowTasksModal(false);
    setSelectedTasks([]);
    setSelectedStudyName("");
    setSelectedTeamMemberName("");
  };

  // Define columns for the DataTable
  const columns = useMemo(() => [
    {
      key: "study_name" as keyof DelegationLogListItem,
      header: "Study Name",
      sortable: true,
      render: (value: unknown): React.ReactNode => {
        return value ? String(value) : "N/A";
      },
    },
    {
      key: "team_member_name" as keyof DelegationLogListItem,
      header: "Team Member",
      sortable: true,
      render: (value: unknown): React.ReactNode => {
        return value ? String(value) : "N/A";
      },
    },
    {
      key: "department_name" as keyof DelegationLogListItem,
      header: "Department",
      sortable: true,
      render: (value: unknown): React.ReactNode => {
        return value ? String(value) : "N/A";
      },
    },
    {
      key: "study_task" as keyof DelegationLogListItem,
      header: "Study Task",
      sortable: true,
      render: (value: unknown): React.ReactNode => {
        const task = value ? String(value) : "";
        return task.length > 50 
          ? `${task.substring(0, 50)}...` 
          : task || "N/A";
      },
    },
    {
      key: "status" as keyof DelegationLogListItem,
      header: "Status",
      sortable: true,
      render: (value: unknown): React.ReactNode => {
        const status = value ? String(value) : "";
        switch (status) {
          case 'pending_pi':
            return <span>Pending PI Review</span>;
          case 'pending_sponsor':
            return <span>Pending Sponsor Review</span>;
          case 'accepted':
            return <span>Accepted</span>;
          case 'pi_rejected':
            return <span>PI Rejected</span>;
          case 'sponsor_rejected':
            return <span>Sponsor Rejected</span>;
          default:
            return <span>{status}</span>;
        }
      },
    },
    {
      key: "created_at" as keyof DelegationLogListItem,
      header: "Requested Date",
      sortable: true,
      render: (value: unknown): React.ReactNode => {
        return value ? new Date(String(value)).toLocaleDateString() : "N/A";
      },
    },
  ], []);

  // Define actions for the DataTable
  const actions = useMemo(() => [
    {
      icon: <List size={16} />,
      tooltipText: "View Selected Tasks",
      onClick: (delegation: DelegationLogListItem) => handleShowTasks(delegation),
      disabled: () => false
    },
    {
      icon: <CheckCircle size={16} />,
      tooltipText: "Approve Application",
      onClick: (delegation: DelegationLogListItem) => handleReviewDelegation(delegation, 'approve'),
      disabled: (delegation: DelegationLogListItem) => delegation.status !== 'pending_pi'
    },
    {
      icon: <XCircle size={16} />,
      tooltipText: "Reject Application",
      onClick: (delegation: DelegationLogListItem) => handleReviewDelegation(delegation, 'reject'),
      disabled: (delegation: DelegationLogListItem) => delegation.status !== 'pending_pi'
    }
  ], [handleReviewDelegation, handleShowTasks]);

  if (isLoading) {
    return (
      <Wrapper>
        <Preloader />
        <div style={{paddingBottom: "88px"}}>
          <div>
            <div>
              <div>
                <div>Loading delegation logs...</div>
              </div>
            </div>
          </div>
        </div>
      </Wrapper>
    );
  }

  if (isError) {
    return (
      <Wrapper>
        <Preloader />
        <div style={{paddingBottom: "88px"}}>
          <div>
            <div>
              <div>
                <div>
                  <h3>Error Loading Delegation Logs</h3>
                  <p>There was an error loading the delegation logs. Please try again later.</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </Wrapper>
    );
  }

  return (
    <Wrapper>
      <Preloader />
      <div className="delegation-logs-container">
        <div className="delegation-logs-header">
          <div className="delegation-logs-header-content">
            <div className="delegation-logs-header-left">
              <div className="delegation-logs-title-section">
                <div className="delegation-logs-icon-wrapper">
                  <FileText size={24} />
                </div>
                <div className="delegation-logs-title-content">
                  <h1>Delegation Logs Review</h1>
                  <p>Review and manage team member access requests for your studies</p>
                </div>
              </div>

              {/* Summary Stats */}
              <div className="delegation-logs-stats">
                <div className="delegation-logs-stat-card">
                  <div className="delegation-logs-stat-icon pending">
                    <Clock size={24} />
                  </div>
                  <div className="delegation-logs-stat-content">
                    <div className="delegation-logs-stat-number">
                      {filteredDelegationLogs.filter(log => log.status === 'pending_pi').length}
                    </div>
                    <div className="delegation-logs-stat-label">Pending Review</div>
                  </div>
                </div>
                <div className="delegation-logs-stat-card">
                  <div className="delegation-logs-stat-icon sponsor">
                    <CheckCircle size={24} />
                  </div>
                  <div className="delegation-logs-stat-content">
                    <div className="delegation-logs-stat-number">
                      {filteredDelegationLogs.filter(log => log.status === 'pending_sponsor').length}
                    </div>
                    <div className="delegation-logs-stat-label">Pending Sponsor</div>
                  </div>
                </div>
                <div className="delegation-logs-stat-card">
                  <div className="delegation-logs-stat-icon total">
                    <User size={24} />
                  </div>
                  <div className="delegation-logs-stat-content">
                    <div className="delegation-logs-stat-number">
                      {filteredDelegationLogs.length}
                    </div>
                    <div className="delegation-logs-stat-label">Total Requests</div>
                  </div>
                </div>
              </div>
              </div>
          </div>
        </div>

        {/* Search and Controls */}
        <div className="delegation-logs-search-section">
          <div className="delegation-logs-search-wrapper">
            <Search size={18} className="delegation-logs-search-icon" />
            <input
              type="text"
              className="delegation-logs-search-input"
              placeholder="Search by study name, team member, department, or status..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
            />
          </div>
        </div>

        {/* Delegation Logs Table */}
        <div className="delegation-logs-table-section">
          <div className="delegation-logs-table-header">
            <div className="delegation-logs-table-title">
              <h3>
                <FileText size={20} />
                Delegation Requests
              </h3>
              <p>Manage team member access requests for your studies</p>
            </div>
          </div>
          <div className="delegation-logs-table-content">
            <div style={{ paddingTop: "20px", paddingBottom: "5px", paddingLeft: "10px" }}>
            <NurtifyFilter layout="horizontal" filters={filters} />
            </div>
            <DataTable
              data={filteredDelegationLogs}
              columns={columns}
              actions={actions}
              noDataMessage="No delegation logs found for your studies"
              defaultItemsPerPage={10}
              globalFilterPlaceholder="Search delegation logs..."
            />
            <br />
          </div>
        </div>

        {/* Review Modal */}
        <DelegationReviewModal
          isOpen={showReviewModal}
          delegation={selectedDelegation}
          action={reviewAction}
          onClose={handleCloseReviewModal}
          onConfirm={handleConfirmReview}
          isLoading={approveDelegationMutation.isPending || rejectDelegationMutation.isPending}
        />

        {/* Tasks Modal */}
        <TasksModal
          isOpen={showTasksModal}
          tasks={selectedTasks}
          studyName={selectedStudyName}
          teamMemberName={selectedTeamMemberName}
          onClose={handleCloseTasksModal}
        />
      </div>
    </Wrapper>
  );
}

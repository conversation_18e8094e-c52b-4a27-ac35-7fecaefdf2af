import React, { useState, useEffect } from 'react';
import FullCalendar from '@fullcalendar/react';
import timeGridPlugin from '@fullcalendar/timegrid';
import dayGridPlugin from '@fullcalendar/daygrid';
import interactionPlugin from '@fullcalendar/interaction';
import { EventClickArg, EventDropArg, DateSelectArg } from '@fullcalendar/core';
import { EventResizeStopArg } from '@fullcalendar/interaction';
import { PatientVisitToday } from '@/services/api/types';
import { ViewMode } from './ViewToggle';
import { useVisitsByMonthQuery, useVisitsByWeekQuery } from '@/hooks/patient.query';
import './VisitCalendar.css';
import { toast } from 'sonner';

interface VisitCalendarProps {
  visits: PatientVisitToday[];
  isLoading: boolean;
  error: Error | null;
  onViewVisitDetails: (patientUuid: string, visitUuid: string) => void;
  onEditVisit: (patientUuid: string, visitUuid: string, visitName: string, visitStatus: string) => void;
  onCreateVisit?: (date: Date) => void;
  selectedDate: string;
  viewMode: ViewMode;
  onDateClick?: (date: Date) => void;
}

interface VisitEvent {
  id: string;
  title: string;
  start: string;
  end: string;
  extendedProps: {
    patientUuid: string;
    visitUuid: string;
    visitName: string;
    visitStatus: string;
    registrationStatus: string;
    studyName: string;
    room: string;
  };
  backgroundColor: string;
  borderColor: string;
}

const VisitCalendar: React.FC<VisitCalendarProps> = ({
  visits,
  isLoading,
  error,
  onViewVisitDetails,
  onEditVisit,
  onCreateVisit,
  selectedDate,
  viewMode,
  onDateClick,
}) => {
  const [events, setEvents] = useState<VisitEvent[]>([]);
  // These will be used when implementing patient details navigation
  // const navigate = useNavigate();
  // const { setSelectedPatient } = useSelectedPatientStore();
  // const addPatientAccessLogMutation = useAddPatientAccessLogMutation();

  // Fetch data based on view mode, conditionally enabled
  const weekVisitsQuery = useVisitsByWeekQuery(selectedDate, { enabled: viewMode === 'week' });
  const monthVisitsQuery = useVisitsByMonthQuery(selectedDate, { enabled: viewMode === 'month' });

  // Determine which visits data to use based on view mode
  let visitsToUse: PatientVisitToday[] = [];
  let isLoadingData = isLoading;
  let errorData = error;

  if (viewMode === 'day') {
    // Use the provided visits data for Day View
    visitsToUse = visits;
    isLoadingData = isLoading;
    errorData = error;
  } else if (viewMode === 'week') {
    // Use week-specific data for Week View
    visitsToUse = weekVisitsQuery.data || [];
    isLoadingData = weekVisitsQuery.isLoading;
    errorData = weekVisitsQuery.error as Error | null;
  } else if (viewMode === 'month') {
    // Use month-specific data for Month View
    visitsToUse = monthVisitsQuery.data || [];
    isLoadingData = monthVisitsQuery.isLoading;
    errorData = monthVisitsQuery.error as Error | null;
  }

  // Convert visits to calendar events
  useEffect(() => {
    if (!visitsToUse) return;

    const newEvents: VisitEvent[] = [];

    visitsToUse.forEach((visit) => {
      visit.visit_details.forEach((detail) => {
        // Parse the date and time
        const dateStr = detail.date || selectedDate;
        const timeStr = detail.time || '09:00';

        // Create start time
        const startDateTime = new Date(`${dateStr}T${timeStr}`);

        // Default duration is 1 hour if not specified
        const endDateTime = new Date(startDateTime);
        endDateTime.setHours(endDateTime.getHours() + 1);

        // Determine color based on registration status
        let backgroundColor = '#6c757d'; // Default gray
        let borderColor = '#5a6268';

        if (detail.registration_status === 'In Hospital') {
          backgroundColor = '#20c997'; // Teal
          borderColor = '#1ba87e';
        } else if (detail.registration_status === 'Not Arrived') {
          backgroundColor = '#ffc107'; // Amber
          borderColor = '#d39e00';
        }

        newEvents.push({
          id: detail.uuid,
          title: `${visit.patient_details.first_name} ${visit.patient_details.last_name} - ${detail.study_name} - ${detail.location || 'No Room'}`,
          start: startDateTime.toISOString(),
          end: endDateTime.toISOString(),
          extendedProps: {
            patientUuid: visit.uuid,
            visitUuid: detail.uuid,
            visitName: detail.name,
            visitStatus: detail.visit_status,
            registrationStatus: detail.registration_status,
            studyName: detail.study_name,
            room: detail.location || 'No Room',
          },
          backgroundColor,
          borderColor,
        });
      });
    });

    setEvents(newEvents);
  }, [visitsToUse, selectedDate]);

  const handleEventClick = (info: EventClickArg) => {
    const { patientUuid, visitUuid } = info.event.extendedProps;
    onViewVisitDetails(patientUuid, visitUuid);
  };

  const handleDateSelect = (selectInfo: DateSelectArg) => {
    if (onCreateVisit) {
      onCreateVisit(selectInfo.start);
    } else {
      toast.info('New visit creation is not available');
    }
  };

  const handleEventDrop = (dropInfo: EventDropArg) => {
    const { patientUuid, visitUuid, visitName, visitStatus } = dropInfo.event.extendedProps;
    // Here you would update the visit with the new time
    // For now, we'll just open the edit modal
    onEditVisit(patientUuid, visitUuid, visitName, visitStatus);
  };

  const handleEventResize = (resizeInfo: EventResizeStopArg) => {
    const { patientUuid, visitUuid, visitName, visitStatus } = resizeInfo.event.extendedProps;
    // Here you would update the visit with the new duration
    // For now, we'll just open the edit modal
    onEditVisit(patientUuid, visitUuid, visitName, visitStatus);
  };

  // Add handler for date clicks
  const handleDateClick = (arg: { date: Date }) => {
    if (onDateClick) {
      onDateClick(arg.date);
    }
  };

  if (isLoadingData) {
    return <div className="loading">Loading visits...</div>;
  }

  if (errorData) {
    return <div className="error">Error loading visits: {errorData.message}</div>;
  }

  // Map view mode to FullCalendar view
  const getCalendarView = () => {
    switch (viewMode) {
      case 'day':
        return 'timeGridDay';
      case 'week':
        return 'timeGridWeek';
      case 'month':
        return 'dayGridMonth';
      default:
        return 'timeGridWeek';
    }
  };

  return (
    <div className="visit-calendar-container">
      <FullCalendar
        plugins={[timeGridPlugin, dayGridPlugin, interactionPlugin]}
        initialView={getCalendarView()}
        headerToolbar={{
          left: '',
          center: '',
          right: '',
        }}
        initialDate={selectedDate}
        slotMinTime="08:00:00"
        slotMaxTime="18:00:00"
        allDaySlot={false}
        editable={true}
        selectable={true}
        selectMirror={true}
        dayMaxEvents={true}
        weekends={true}
        events={events}
        eventClick={handleEventClick}
        select={handleDateSelect}
        eventDrop={handleEventDrop}
        eventResize={handleEventResize}
        dateClick={handleDateClick}
        height="auto"
        slotDuration="00:30:00"
        slotLabelInterval="01:00"
        expandRows={true}
        stickyHeaderDates={true}
        nowIndicator={true}
        eventTimeFormat={{
          hour: '2-digit',
          minute: '2-digit',
          meridiem: false,
          hour12: false
        }}
        slotLabelFormat={{
          hour: '2-digit',
          minute: '2-digit',
          meridiem: false,
          hour12: false
        }}
        // Month view specific options
        dayMaxEventRows={viewMode === 'month' ? 3 : false}
        moreLinkClick="popover"
        // Navigation keyboard shortcuts
        navLinks={true}
      />
    </div>
  );
};

export default VisitCalendar;

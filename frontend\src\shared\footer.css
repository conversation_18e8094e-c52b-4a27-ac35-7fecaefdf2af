.modern-footer {
  background-color: #f8f9fa;
  border-top: 1px solid #e9ecef;
  padding: 24px 90px;
  margin-top: auto;
  width: 100%;
  opacity: 1;
}

.modern-footer__content {
  width: 100%;
}

.modern-footer__row {
  display: flex;
  align-items: center;
  justify-content: space-between;
  flex-wrap: wrap;
  gap: 20px;
}

.modern-footer__copyright {
  font-size: 14px;
  color: white;
  font-weight: 500;
}

.modern-footer__right {
  display: flex;
  align-items: center;
  gap: 30px;
}

.modern-footer__links {
  display: flex;
  align-items: center;
  gap: 24px;
  flex-wrap: wrap;
}

.modern-footer__link {
  color: white !important;
  font-size: 14px;
  font-weight: 500;
  transition: color 0.2s ease;
  text-decoration: none;
  position: relative;
}

.modern-footer__link:hover {
  color: var(--color-purple-1) !important;
}

.modern-footer__link:after {
  content: "";
  position: absolute;
  width: 0;
  height: 2px;
  bottom: -4px;
  left: 0;
  background-color: var(--color-purple-1);
  transition: width 0.3s ease;
}

.modern-footer__link:hover:after {
  width: 100%;
}

.modern-footer__language {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 16px;
  border-radius: 8px;
  background-color: white;
  color: #6c757d;
  font-size: 14px;
  font-weight: 500;
  border: 1px solid #e9ecef;
  cursor: pointer;
  transition: all 0.2s ease;
}

.modern-footer__language:hover {
  background-color: #f1f3f5;
  border-color: #dee2e6;
}

.modern-footer__icon {
  color: var(--color-purple-1);
}

/* Footer Dark Styles */
.footer-dark {
  background-color: #101828;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  padding: 20px 0;
  width: 100%;
  opacity: 1;
  margin-top: auto;
  bottom: 0;
}

/* Footer Type-1 Styles */
.footer.-type-1 {
  opacity: 1;
  width: 100%;
  margin-top: auto;
  bottom: 0;
}

/* Mobile Footer Styles */
.mobile-footer {
  background-color: #f8f9fa;
  border-top: 1px solid #e9ecef;
  padding: 20px 0;
  width: 100%;
  opacity: 1;
  margin-top: auto;
  bottom: 0;
}

.mobile-footer__content {
  width: 100%;
}

.mobile-footer__row {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  gap: 15px;
}

.mobile-footer__copyright {
  font-size: 13px;
  color: #6c757d;
  font-weight: 500;
}

.mobile-footer__right {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 15px;
}

.mobile-footer__links {
  display: flex;
  justify-content: center;
  flex-wrap: wrap;
  gap: 15px;
}

.mobile-footer__link {
  color: #6c757d !important;
  font-size: 13px;
  font-weight: 500;
  text-decoration: none;
}

.mobile-footer__language {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 16px;
  border-radius: 8px;
  background-color: white;
  color: #6c757d;
  font-size: 13px;
  font-weight: 500;
  border: 1px solid #e9ecef;
}

.mobile-footer__icon {
  color: var(--color-purple-1);
}

/* Mobile responsiveness */
@media (max-width: 768px) {
  .modern-footer__row {
    flex-direction: column;
    text-align: center;
  }
  
  .modern-footer__right {
    flex-direction: column;
    gap: 20px;
  }
  
  .modern-footer__links {
    justify-content: center;
    gap: 16px;
  }
  
  .footer-dark__row {
    flex-direction: column;
    text-align: center;
  }
  
  .footer-dark__right {
    flex-direction: column;
    gap: 20px;
  }
}

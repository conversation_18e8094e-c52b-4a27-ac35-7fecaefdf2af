import React, { useState } from 'react';
import { Star } from 'lucide-react';
import { useNavigate } from 'react-router-dom';

interface FormCardProps {
  label: string;
  onClick?: () => void;
  link?: string;
}

const FormCard: React.FC<FormCardProps> = ({ label, onClick, link }) => {
  const [isStarred, setIsStarred] = useState(false);
  const navigate = useNavigate();

  const handleStarClick = (e: React.MouseEvent) => {
    e.stopPropagation();
    setIsStarred(!isStarred);
  };

  const handleClick = () => {
    if (link) {
      navigate(link);
    }
  };
  
  return (
    <div
      className="card-container"
      onClick={onClick}
      style={{
        display: 'flex',
        justifyContent: 'space-between',
        alignItems: 'center',
        padding: '16px',
        backgroundColor: '#D7F1F3',
        borderRadius: '8px',
        boxShadow: '0 2px 4px rgba(0, 0, 0, 0.1)',
        cursor: 'pointer',
        transition: 'background-color 0.3s ease',
      }}
      onMouseEnter={(e) => {
        e.currentTarget.style.backgroundColor = '#071952CF';
        e.currentTarget.style.color = 'white';
      }}
      onMouseLeave={(e) => {
        e.currentTarget.style.backgroundColor = '#D7F1F3';
        e.currentTarget.style.color = 'black';
      }}
    >
      <span onClick={handleClick}>{label}</span>
      <div onClick={handleStarClick}>
        <Star
          size={20}
          fill={isStarred ? '#FECE23' : 'none'}
          color={isStarred ? '#FECE23' : 'white'}
        />
      </div>
    </div>
  );
};

export default FormCard;
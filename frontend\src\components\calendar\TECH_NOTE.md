# Enhanced Calendar View Implementation for Visits

This technical note explains the implementation of the enhanced Calendar View feature for the Visits page, including Day, Week, and Month views, along with key design decisions and implementation details.

## Overview

The Calendar View provides an alternative way to visualize patient visits, complementing the existing List View. Users can toggle between four views (List, Day, Week, and Month) using a segmented control, with their preference persisted in localStorage. Keyboard shortcuts (L, D, W, M) are also available for quick view switching.

## Architecture

### Components

1. **VisitCalendar**: The main calendar component that displays visits as events in a calendar grid.
   - Uses `@fullcalendar/react` for the calendar UI
   - Supports Day, Week, and Month views
   - Supports dragging and resizing events (in Day and Week views)
   - Color-codes visits based on registration status
   - Handles event clicks to show visit details
   - Adapts display based on view mode

2. **ViewToggle**: A reusable toggle component for switching between List, Day, Week, and Month views.
   - Persists user preference in localStorage
   - Provides accessible UI with keyboard navigation support
   - Includes keyboard shortcuts (L, D, W, M) for quick view switching

### Data Flow

1. The main `ListOfPatients` component fetches visit data using the existing query hooks.
2. Based on the selected view mode, it renders either the List View or one of the Calendar Views.
3. For Day and Week views, we use the same data source as the List View.
4. For Month view, we use a specialized query hook that fetches data for the entire month.
5. The Calendar View transforms the visit data into the format expected by FullCalendar.

### Responsive Design

- On screens smaller than 768px, the application defaults to List View.
- A message is displayed when a user tries to access Calendar View on a small screen.
- The calendar is scrollable on medium-sized screens.

### Accessibility

- High-contrast focus states for keyboard navigation
- ARIA attributes for toggle buttons
- Keyboard navigation support for calendar (arrow keys for week/hour navigation)

## Key Design Decisions

### Using FullCalendar

We chose `@fullcalendar/react` for several reasons:
- MIT license as required
- Robust API with excellent TypeScript support
- Built-in support for drag-and-drop and event resizing
- Extensive customization options
- Active maintenance and community support

### State Management

- We continue to use Redux Toolkit for state management, consistent with the rest of the application.
- The view mode preference is stored in localStorage for persistence across sessions.
- We reuse existing API endpoints and query hooks for data fetching.

### Color Coding

Visits are color-coded based on registration status:
- In Hospital → teal (#20c997)
- Not Arrived → amber (#ffc107)
- Discharged → gray (#6c757d)

This provides immediate visual cues about the status of each visit.

## Performance Considerations

### Lazy Loading

- Visits are loaded week-by-week as the user navigates through the calendar.
- We leverage the existing `useVisitsByDateQuery` hook, which already supports date-based filtering.

### Virtual Scrolling

- The FullCalendar component handles virtual scrolling for long day columns.
- This ensures good performance even with many events.

## Implementation Details

### View Modes

1. **Day View**:
   - Vertical timeline from 8:00 to 18:00
   - Shows all visits for a single day
   - Supports drag-and-drop for rescheduling
   - Supports resizing for changing visit duration

2. **Week View**:
   - Monday to Sunday grid with hour slots
   - Shows all visits for the week
   - Same drag-and-drop and resize functionality as Day view

3. **Month View**:
   - Classic 7×5 grid showing the entire month
   - Shows visits as pills inside day cells
   - Truncates long titles with tooltips on hover
   - Uses a specialized query hook to fetch all visits for the month

### Data Fetching Strategy

We've implemented a smart data fetching strategy to optimize performance:

1. **Day/Week Views**: Use the existing `useVisitsByDateQuery` hook that fetches visits for a specific date.
2. **Month View**: Use the new `useVisitsByMonthQuery` hook that:
   - Calculates the start and end dates of the month
   - Uses `useVisitsByDateRangeQuery` to fetch all visits within that range
   - This ensures we only load the data we need for each view

### Keyboard Navigation

- Arrow keys (←/→/↑/↓) for navigating between days/weeks
- Page Up/Down for changing months
- Keyboard shortcuts (L, D, W, M) for switching between views

### Responsive Design

- On screens smaller than 768px, the application defaults to List View
- A message is displayed when a user tries to access Calendar View on a small screen
- For medium-sized screens, the calendar becomes horizontally scrollable

## Future Enhancements

### Potential Improvements

1. **Agenda View**: Add a fourth calendar view that shows events in a list format grouped by day.
2. **Resource View**: Add the ability to view visits by resource (e.g., room, staff member).
3. **Print View**: Add a print-friendly version of the calendar.
4. **Export**: Allow exporting the calendar to iCal or other formats.
5. **Recurring Visits**: Support for recurring visits with pattern editing.

### Performance Optimizations

1. **Virtual Scrolling**: Implement virtual scrolling for very large datasets.
2. **Caching**: Implement more aggressive caching for calendar data.
3. **Lazy Loading Images**: If patient avatars are added in the future, implement lazy loading.

## Conclusion

The enhanced Calendar View implementation provides a more visual and interactive way to manage visits. It offers multiple viewing options to suit different user needs and workflows. The implementation follows the same design patterns and state management approach used throughout the application.

The modular architecture makes it straightforward to extend with additional views or features in the future, while maintaining performance and usability.

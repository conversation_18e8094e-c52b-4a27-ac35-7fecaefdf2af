.nurtify-filter {
  font-family: inherit;
  color: #222;
}

.nurtify-filter--sidebar {
  width: 320px;
  background: #fff;
  border-radius: 10px;
  padding: 16px 12px;
  display: flex;
  flex-direction: column;
  gap: 16px;
  transition: width 0.3s cubic-bezier(0.4,0,0.2,1), padding 0.3s cubic-bezier(0.4,0,0.2,1);
}

.nurtify-filter--sidebar.nurtify-filter--minimized {
  width: 48px;
  min-width: 48px;
  max-width: 48px;
  padding: 8px 4px;
  overflow: hidden;
  align-items: center;
  justify-content: flex-start;
  background: #fff;
  border-radius: 10px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.04);
}

.nurtify-filter--sidebar.nurtify-filter--minimized .nurtify-filter__minimize {
  width: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-bottom: 0;
}

.nurtify-filter--sidebar.nurtify-filter--minimized .nurtify-filter__minimize button {
  padding: 0 0;
  font-size: 18px;
  background: none;
  border: none;
  color: #0099b0;
  cursor: pointer;
}

.nurtify-filter--sidebar.nurtify-filter--minimized .nurtify-filter__filters {
  display: none;
}

.nurtify-filter--vertical {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.nurtify-filter--horizontal {
  display: flex;
  flex-direction: row;
  gap: 24px;
  align-items: flex-start;
}

.nurtify-filter--horizontal .nurtify-filter__filters {
  flex-direction: row !important;
  gap: 16px;
  width: 100%;
  overflow: visible;
}

.nurtify-filter--horizontal .nurtify-filter__dropdown {
  flex: 1 1 0;
  min-width: 0;
  max-width: none;
  position: relative;
  box-sizing: border-box;
}

.nurtify-filter__minimize {
  display: flex;
  justify-content: flex-end;
  margin-bottom: 8px;
}

.nurtify-filter__minimize button {
  color: #0099b0;
  border: none;
  border-radius: 6px;
  padding: 4px 14px;
  font-size: 14px;
  cursor: pointer;
  transition: background 0.2s;
}


.nurtify-filter__filters {
  display: flex;
  flex-direction: inherit;
  gap: 16px;
  width: 95%;
}

.nurtify-filter__dropdown {
  background: #fff;
  border-radius: 8px;
  margin-bottom: 0;
  width: 100%;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  gap: 10px;
  min-width: 180px;
  position: relative; /* For absolute dropdown content */
}

.nurtify-filter--horizontal .nurtify-filter__dropdown {
  min-width: 120px;
  max-width: 260px;
}

.nurtify-filter__dropdown-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px 16px;
  cursor: pointer;
  font-size: 16px;
  border-radius: 8px;
  border: 1px solid #D2DFE1;
  outline: none;
  transition: background 0.2s;
  user-select: none;
}
.nurtify-filter__dropdown-header:focus {
  outline: none !important;
  box-shadow: none !important;
}

.nurtify-filter__dropdown-icon {
  font-size: 18px;
  margin-left: 8px;
}

.nurtify-filter__dropdown-content {
  background: #fff;
  border-radius: 8px;
  border-top: none;
  padding: 12px 16px;
  margin-top: 8px; /* Gap between header and dropdown */
  display: flex;
  flex-direction: column;
  gap: 10px;
  border: 1px solid #D2DFE1;
  animation: fadeIn 0.18s;
  position: absolute;
  top: 100%;
  left: 0;
  width: 100%;
  z-index: 100;
  box-shadow: 0 4px 16px rgba(0,0,0,0.08);
}

@keyframes fadeIn {
  from { opacity: 0; transform: translateY(-8px);}
  to { opacity: 1; transform: translateY(0);}
}

.nurtify-filter__option {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 15px;
  cursor: pointer;
  padding: 2px 0;
}

.nurtify-filter__option input[type="checkbox"],
.nurtify-filter__option input[type="radio"] {
  accent-color: #0099b0;
  width: 18px;
  height: 18px;
  margin-right: 6px;
}

.nurtify-filter--minimized .nurtify-filter__filters {
  display: none;
}

/**
 * Utility function to check if user has scrolled to the bottom of the page
 * @param offset - Optional offset from the bottom in pixels (default: 100)
 * @returns boolean - True if user has scrolled to the bottom
 */
export const isScrolledToBottom = (offset: number = 100): boolean => {
  const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
  const scrollHeight = document.documentElement.scrollHeight;
  const clientHeight = document.documentElement.clientHeight;
  
  return scrollTop + clientHeight >= scrollHeight - offset;
};

/**
 * Utility function to toggle footer visibility based on scroll position
 * @param footerSelector - CSS selector for the footer element
 * @param offset - Optional offset from the bottom in pixels (default: 100)
 */
export const handleFooterVisibility = (footerSelector: string, offset: number = 100): void => {
  const footer = document.querySelector(footerSelector);
  if (!footer) return;
  
  if (isScrolledToBottom(offset)) {
    footer.classList.add('visible');
  } else {
    footer.classList.remove('visible');
  }
};

/**
 * Setup scroll listener for footer visibility
 * @param footerSelector - CSS selector for the footer element
 * @param offset - Optional offset from the bottom in pixels (default: 100)
 * @returns cleanup function to remove event listener
 */
export const setupFooterScrollListener = (footerSelector: string, offset: number = 100): () => void => {
  const handleScroll = () => {
    handleFooterVisibility(footerSelector, offset);
  };
  
  // Initial check
  handleScroll();
  
  // Add scroll event listener
  window.addEventListener('scroll', handleScroll, { passive: true });
  
  // Return cleanup function
  return () => {
    window.removeEventListener('scroll', handleScroll);
  };
};

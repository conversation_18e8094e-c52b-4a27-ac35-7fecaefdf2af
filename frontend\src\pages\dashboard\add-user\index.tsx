import "./addUser.css";
import { useState } from "react";
import NurtifyText from "@components/NurtifyText.tsx";
import NurtifyInput from "@components/NurtifyInput.tsx";
import { motion } from "framer-motion";
import { useCreateUserMutation, useCurrentUserQuery } from "@/hooks/user.query.ts";
import { useNavigate } from "react-router-dom";
import ConfirmationModal from "./AddUserModal";
import { User, Mail } from "lucide-react";

interface FormData {
    email: string;
    department_uuid: string;
    speciality: string;
}

const SPECIALITY_CHOICES = [
    { value: "Registered Nurse", label: "Registered Nurse" },
    { value: "Senior Registered Nurse", label: "Senior Registered Nurse" },
    { value: "Doctor", label: "Doctor" },
    { value: "SpR Doctor", label: "SpR Doctor" },
    { value: "Consultant", label: "Consultant" },
    { value: "Professor", label: "Professor" },
    { value: "Pharmacist", label: "Pharmacist" },
    { value: "Senior Pharmacist", label: "Senior Pharmacist" },
    { value: "Clinical Research Practitioner", label: "Clinical Research Practitioner" },
    { value: "Advanced Clinical Practitioner", label: "Advanced Clinical Practitioner" },
    { value: "Clinical Research Associate", label: "Clinical Research Associate" },
    { value: "Primary Investigator", label: "Primary Investigator" },
    { value: "Sub-Investigator", label: "Sub-Investigator" },
    { value: "Lab Technician", label: "Lab Technician" },
    { value: "Receptionist", label: "Receptionist" },
    { value: "Data Manager", label: "Data Manager" },
    { value: "Matron", label: "Matron" },
    { value: "Senior Matron", label: "Senior Matron" },
    { value: "Lead Nurse", label: "Lead Nurse" },
    { value: "Ward Manager", label: "Ward Manager" },
    { value: "Charge Nurse", label: "Charge Nurse" },
    { value: "Clinical Practice Educator", label: "Clinical Practice Educator" },
    { value: "Trial Manager", label: "Trial Manager" },
    { value: "CRF Manager", label: "CRF Manager" },
    { value: "Study Coordinator", label: "Study Coordinator" },
    { value: "Portfolio Manager", label: "Portfolio Manager" },
    { value: "Other", label: "Other, please specify" },
  ];
export default function AddUser() {
    const navigate = useNavigate();
    const createUserMutation = useCreateUserMutation();
    const currentUser = useCurrentUserQuery();

    const [isConfirmModalOpen, setIsConfirmModalOpen] = useState(false);

    // UUID statique correspondant à un vrai département
    //const DepartmentUuid = "dcc4bd74-4f6f-4b0e-9ae1-e6bf5e7fa5de";

    const [formData, setFormData] = useState<FormData>({
        email: "",
        department_uuid: currentUser.data?.department?.uuid || "",
        speciality: "",

    });


    const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        const { name, value } = e.target;
        setFormData(prev => ({
            ...prev,
            [name]: value
        }));
    };

    const handleSelectChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
        const { name, value } = e.target;
        setFormData(prev => ({
            ...prev,
            [name]: value
        }));
    };

    const handleSubmit = (e: React.FormEvent) => {
        e.preventDefault();
        if (!formData.email || !formData.department_uuid || !formData.speciality) {
            return; // Ne soumet pas si les champs requis sont vides
        }
        setIsConfirmModalOpen(true);
    };

    const handleConfirmSubmit = async () => {
        const submitData = new FormData();
        submitData.append('email', formData.email);
        submitData.append('department_uuid', formData.department_uuid);
        submitData.append("speciality", formData.speciality); // Add speciality to submission

        try {
            await createUserMutation.mutateAsync(submitData);
            setIsConfirmModalOpen(false);
            navigate("/org/dashboard/users");
         
        } catch (error: any) {
            console.error("Error creating user:", error);
            alert(`Failed to create user: ${error.response?.data || error.message}`);
            setIsConfirmModalOpen(false);
        }
    };

    return (
        <div className="add-user-container">
            <div className="add-user-header">
                <div className="add-user-title">
                    <h1>
                        <User size={24} style={{ marginRight: "10px" }} />
                        Create New User
                    </h1>
                </div>
                <div className="add-user-subtitle">
                    <h6>Add a new user to your organization</h6>
                </div>
            </div>

            <form className="add-user-form" onSubmit={handleSubmit}>
        <motion.div
          className="user-form-section"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.2, duration: 0.4 }}
        >
          <h3 className="user-form-section-title">
            <Mail size={18} style={{ marginRight: "8px", verticalAlign: "middle" }} />
            User Information
          </h3>
          <div className="row y-gap-30">
            <div className="col-md-6">
              <NurtifyText label="Professional Email*" />
              <NurtifyInput
                type="email"
                name="email"
                value={formData.email}
                onChange={handleInputChange}
                placeholder="Enter Email here"
                required
              />
            </div>
            <div className="col-md-6">
              <NurtifyText label="Speciality*" />
              <select
                name="speciality"
                value={formData.speciality}
                onChange={handleSelectChange}
                className="nurtify-select" // Custom class for styling
                required
              >
                <option value="">Select Speciality</option>
                {SPECIALITY_CHOICES.map((option) => (
                  <option key={option.value} value={option.value}>
                    {option.label}
                  </option>
                ))}
              </select>
            </div>
          </div>
        </motion.div>

                <div className="user-form-actions">
                    <button
                        type="submit"
                        className="user-btn-submit"
                        disabled={createUserMutation.isPending}
                    >
                        {createUserMutation.isPending ? 'Creating...' : 'Create User'}
                    </button>
                </div>
            </form>
            <ConfirmationModal
                isOpen={isConfirmModalOpen}
                onClose={() => setIsConfirmModalOpen(false)}
                onConfirm={handleConfirmSubmit}
                title="Confirm adding user"
                message="Are you sure you want to add this new user?"
                subMessage="This action will create a new user in the system."
            />
        </div>
    );
}

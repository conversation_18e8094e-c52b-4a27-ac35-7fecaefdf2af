import React, { useState, useEffect } from 'react';
import { useSearchPatients } from '@/hooks/patient.query';
import { PatientSearchParams, Patient, PaginatedResponse } from '@/services/api/types';
import NurtifyInput from './NurtifyInput';
import NurtifySelect from './NurtifySelect';
import NurtifyDateInput from './NurtifyDateInput';
import StudySelector from './StudySelector';
import { Search } from 'lucide-react';
import { format } from 'date-fns';
import './PatientSearchForm.css';

interface PatientSearchFormProps {
  onSelectPatient: (patient: Patient) => void;
  initialSearchParams?: PatientSearchParams;
  onSearchSubmitted?: (submitted: boolean) => void;
  onSearchResults?: (results: PaginatedResponse<Patient> | null) => void;
}

const PatientSearchForm: React.FC<PatientSearchFormProps> = ({
  onSelectPatient,
  initialSearchParams = {},
  onSearchSubmitted,
  onSearchResults
}) => {
  const [searchParams, setSearchParams] = useState<PatientSearchParams>(initialSearchParams);
  const [searchSubmitted, setSearchSubmitted] = useState(false);

  // Use the search hook with enabled=false initially to prevent auto-search
  const { data: searchResults, isLoading, isError, error } = useSearchPatients(
    searchParams,
    searchSubmitted
  );

  // Reset search submitted flag when search params change
  useEffect(() => {
    setSearchSubmitted(false);
  }, [searchParams]);

  // Call onSearchSubmitted callback when searchSubmitted changes
  useEffect(() => {
    if (onSearchSubmitted) {
      onSearchSubmitted(searchSubmitted);
    }
  }, [searchSubmitted, onSearchSubmitted]);

  // Call onSearchResults callback when searchResults changes
  useEffect(() => {
    if (onSearchResults) {
      onSearchResults(searchResults || null);
    }
  }, [searchResults, onSearchResults]);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setSearchParams(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleDateChange = (event: { target: { name: string; value: string } }) => {
    const { name, value } = event.target;
    setSearchParams(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    setSearchSubmitted(true);
  };

  const handleClear = () => {
    setSearchParams({});
    setSearchSubmitted(false);
  };

  const handleSelectPatient = (patient: Patient) => {
    onSelectPatient(patient);
  };

  return (
    <div className="patient-search-container">
      <h2 className="search-title">
        <Search size={20} className="search-icon" />
        Search Patient
      </h2>

      <form onSubmit={handleSearch} className="search-form">
        <div className="search-form-row">
          <div className="search-form-field">
            <label htmlFor="nhs_number">NHS Number</label>
            <NurtifyInput
              id="nhs_number"
              name="nhs_number"
              value={searchParams.nhs_number || ''}
              onChange={handleInputChange}
              placeholder="Enter NHS number"
            />
          </div>

          <div className="search-form-field">
            <label htmlFor="medical_record_number">Medical Record Number</label>
            <NurtifyInput
              id="medical_record_number"
              name="medical_record_number"
              value={searchParams.medical_record_number || ''}
              onChange={handleInputChange}
              placeholder="Enter MRN"
            />
          </div>
        </div>

        <div className="search-form-row">
          <div className="search-form-field">
            <label htmlFor="first_name">First Name</label>
            <NurtifyInput
              id="first_name"
              name="first_name"
              value={searchParams.first_name || ''}
              onChange={handleInputChange}
              placeholder="Enter first name"
            />
          </div>

          <div className="search-form-field">
            <label htmlFor="last_name">Last Name</label>
            <NurtifyInput
              id="last_name"
              name="last_name"
              value={searchParams.last_name || ''}
              onChange={handleInputChange}
              placeholder="Enter last name"
            />
          </div>
        </div>

        <div className="search-form-row">
          <div className="search-form-field">
            <label htmlFor="full_name">Full Name</label>
            <NurtifyInput
              id="full_name"
              name="full_name"
              value={searchParams.full_name || ''}
              onChange={handleInputChange}
              placeholder="Search by full name"
            />
          </div>

          <div className="search-form-field">
            <label htmlFor="date_of_birth">Date of Birth</label>
            <NurtifyDateInput
              name="date_of_birth"
              value={searchParams.date_of_birth || ''}
              onChange={handleDateChange}
            />
          </div>
        </div>

        <div className="search-form-row">
          <div className="search-form-field">
            <label htmlFor="gender">Gender</label>
            <NurtifySelect
              name="gender"
              value={searchParams.gender || ''}
              onChange={handleInputChange}
              options={[
                { value: '', label: 'Select gender' },
                { value: 'Male', label: 'Male' },
                { value: 'Female', label: 'Female' },
                { value: 'Prefer_not_to_disclose', label: 'Prefer not to disclose' }
              ]}
            />
          </div>

          <div className="search-form-field">
            <label htmlFor="study">Study</label>
            <StudySelector
              value={searchParams.study || ''}
              onChange={(value: string) => setSearchParams(prev => ({ ...prev, study: value }))}
            />
          </div>
        </div>

        <div className="search-form-actions">
          <button type="button" onClick={handleClear} className="clear-button">
            Clear
          </button>
          <button type="submit" className="search-button">
            <Search size={16} />
            Search
          </button>
        </div>
      </form>

      {isLoading && <div className="search-loading">Searching patients...</div>}

      {isError && (
        <div className="search-error">
          Error searching patients: {(error as Error).message}
        </div>
      )}

      {searchSubmitted && searchResults && (
        <div className="search-results">
          <h3>Search Results ({searchResults.count} patients found)</h3>

          {searchResults.results.length === 0 ? (
            <div className="no-results">No patients found matching your search criteria.</div>
          ) : (
            <div className="results-list">
              {searchResults.results.map((patient) => (
                <div
                  key={patient.uuid}
                  className="patient-result-item"
                  onClick={() => handleSelectPatient(patient)}
                >
                  <div className="patient-info">
                    <div className="patient-name">
                      {patient.first_name} {patient.last_name}
                    </div>
                    <div className="patient-details">
                      <span className="patient-nhs">NHS: {patient.nhs_number}</span>
                      <span className="patient-dob">
                        Date of Birth: {patient.date_of_birth ? format(new Date(patient.date_of_birth), 'dd/MM/yyyy') : 'N/A'}
                      </span>
                      {patient.medical_record_number && (
                        <span className="patient-mrn">MRN: {patient.medical_record_number}</span>
                      )}
                    </div>
                  </div>
                  <button
                    className="select-patient-btn"
                    onClick={(e) => {
                      e.stopPropagation();
                      handleSelectPatient(patient);
                    }}
                  >
                    Open Patient
                  </button>
                </div>
              ))}
            </div>
          )}

          {searchResults.next && (
            <div className="pagination">
              <span>More results available. Please refine your search.</span>
            </div>
          )}
        </div>
      )}
    </div>
  );
};

export default PatientSearchForm;

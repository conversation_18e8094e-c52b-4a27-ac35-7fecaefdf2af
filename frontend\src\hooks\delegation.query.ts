import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { DelegationService, DelegationLogCreateData, DelegationLogListItem, DelegationLogReapplyData, DelegationLog } from "@/services/api/delegation.service";

export const DELEGATION_KEYS = {
  all: ['delegation-logs'] as const,
  byStudy: (studyUuid: string) => [...DELEGATION_KEYS.all, 'study', studyUuid] as const,
  byDepartment: (departmentUuid: string) => [...DELEGATION_KEYS.all, 'department', departmentUuid] as const,
};

export const useDelegationLogsQuery = () => {
  return useQuery<{ count: number; next: string | null; previous: string | null; results: DelegationLogListItem[] }, Error>({
    queryKey: DELEGATION_KEYS.all,
    queryFn: DelegationService.getDelegationLogs,
  });
};

export const useDelegationLogByUuidQuery = (delegationUuid: string) => {
  return useQuery<DelegationLog, Error>({
    queryKey: [...DELEGATION_KEYS.all, 'byUuid', delegationUuid],
    queryFn: () => DelegationService.getDelegationLogByUuid(delegationUuid),
    enabled: !!delegationUuid,
  });
};

export const useCreateDelegationLogMutation = () => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: (data: DelegationLogCreateData) => DelegationService.createDelegationLog(data),
    onSuccess: (data) => {
      // Invalidate relevant queries
      queryClient.invalidateQueries({ queryKey: DELEGATION_KEYS.all });
      queryClient.invalidateQueries({ queryKey: DELEGATION_KEYS.byStudy(data.study) });
      queryClient.invalidateQueries({ queryKey: DELEGATION_KEYS.byDepartment(data.department) });
    },
    onError: (error) => {
      console.error("Error creating delegation log:", error);
    },
  });
};

export const usePiApproveDelegationLogMutation = () => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: ({ delegationUuid, piNotes }: { delegationUuid: string; piNotes: string }) => 
      DelegationService.piApproveDelegationLog(delegationUuid, piNotes),
    onSuccess: () => {
      // Invalidate delegation logs queries
      queryClient.invalidateQueries({ queryKey: DELEGATION_KEYS.all });
    },
    onError: (error) => {
      console.error("Error approving delegation log:", error);
    },
  });
};

export const usePiRejectDelegationLogMutation = () => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: ({ delegationUuid, piNotes }: { delegationUuid: string; piNotes: string }) => 
      DelegationService.piRejectDelegationLog(delegationUuid, piNotes),
    onSuccess: () => {
      // Invalidate delegation logs queries
      queryClient.invalidateQueries({ queryKey: DELEGATION_KEYS.all });
    },
    onError: (error) => {
      console.error("Error rejecting delegation log:", error);
    },
  });
};

export const useSponsorApproveDelegationLogMutation = () => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: ({ delegationUuid, sponsorNotes }: { delegationUuid: string; sponsorNotes: string }) => 
      DelegationService.sponsorApproveDelegationLog(delegationUuid, sponsorNotes),
    onSuccess: () => {
      // Invalidate delegation logs queries
      queryClient.invalidateQueries({ queryKey: DELEGATION_KEYS.all });
    },
    onError: (error) => {
      console.error("Error approving delegation log:", error);
    },
  });
};

export const useSponsorRejectDelegationLogMutation = () => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: ({ delegationUuid, sponsorNotes }: { delegationUuid: string; sponsorNotes: string }) => 
      DelegationService.sponsorRejectDelegationLog(delegationUuid, sponsorNotes),
    onSuccess: () => {
      // Invalidate delegation logs queries
      queryClient.invalidateQueries({ queryKey: DELEGATION_KEYS.all });
    },
    onError: (error) => {
      console.error("Error rejecting delegation log:", error);
    },
  });
};

export const useReapplyDelegationLogMutation = () => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: ({ delegationUuid, data }: { delegationUuid: string; data: DelegationLogReapplyData }) => 
      DelegationService.reapplyDelegationLog(delegationUuid, data),
    onSuccess: () => {
      // Invalidate delegation logs queries
      queryClient.invalidateQueries({ queryKey: DELEGATION_KEYS.all });
    },
    onError: (error) => {
      console.error("Error reapplying delegation log:", error);
    },
  });
}; 
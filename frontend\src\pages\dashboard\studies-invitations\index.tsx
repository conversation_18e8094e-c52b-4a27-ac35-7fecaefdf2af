 
import { useState } from "react";
import { LayoutGrid, List, Check, X } from "lucide-react";
import Preloader from "@/components/common/Preloader";
import Wrapper from "@/components/common/Wrapper";
import DataTable from "@/components/common/DataTable";
import ConfirmationModal from "./ConfirmationModal";
import { useCurrentUserQuery } from "@/hooks/user.query";
import {
  useStudyInvitationsByDepartmentQuery,
} from "@/hooks/study.query";
import { useQueryClient } from "@tanstack/react-query";
import { STUDY_KEYS } from "@/hooks/keys";
import { acceptStudyInvitation, rejectStudyInvitation } from "@/services/api/study.service";
import "./studies.css";

interface StudyInvitation {
  uuid?: string;
  study?: string;
  study_name?: string;
  department?: string;
  department_name?: string;
  hospital_name?: string;
  invited_by?: {
    identifier: string;
    first_name: string;
    last_name: string;
    email: string;
    organization_name: string | null;
  };
  status?: "pending" | "accepted" | "rejected";
  invited_at?: string;
  responded_at?: string | null;
}

export default function StudyInvitations() {
  const [searchTerm] = useState("");
  const [viewMode, setViewMode] = useState<"table" | "card">("table");
  const [modal, setModal] = useState<{
    isOpen: boolean;
    action: "accept" | "reject" | null;
    invitationUuid: string | null;
  }>({ isOpen: false, action: null, invitationUuid: null });

  // Fetch data
  const { data: currentUser = {} as any } = useCurrentUserQuery();
  const { data: invitations, isLoading: isLoadingInvitations } = useStudyInvitationsByDepartmentQuery(
    currentUser?.department?.uuid || ""
  );
  const queryClient = useQueryClient();

  // Filter invitations based on search term
  const filteredInvitations = invitations?.filter((invitation: StudyInvitation) =>
    (invitation.study_name?.toLowerCase() || "").includes(searchTerm.toLowerCase()) ||
    (invitation.hospital_name?.toLowerCase() || "").includes(searchTerm.toLowerCase()) ||
    (invitation.department_name?.toLowerCase() || "").includes(searchTerm.toLowerCase())
  );

  // Handle accept/reject actions
  const handleAccept = (uuid: string) => {
    setModal({ isOpen: true, action: "accept", invitationUuid: uuid });
  };

  const handleReject = (uuid: string) => {
    setModal({ isOpen: true, action: "reject", invitationUuid: uuid });
  };

  const handleConfirmModal = () => {
    if (modal.invitationUuid) {
      if (modal.action === "accept") {
        acceptStudyInvitation(modal.invitationUuid)
          .then(() => {
            // Refresh the invitations list
            queryClient.invalidateQueries({ queryKey: [STUDY_KEYS.GET_INVITATIONS_BY_DEPARTMENT] });
          })
          .catch((error: Error) => {
            console.error("Error accepting invitation:", error);
          });
      } else if (modal.action === "reject") {
        rejectStudyInvitation(modal.invitationUuid)
          .then(() => {
            // Refresh the invitations list
            queryClient.invalidateQueries({ queryKey: [STUDY_KEYS.GET_INVITATIONS_BY_DEPARTMENT] });
          })
          .catch((error: Error) => {
            console.error("Error rejecting invitation:", error);
          });
      }
    }
    setModal({ isOpen: false, action: null, invitationUuid: null });
  };

  const handleCancelModal = () => {
    setModal({ isOpen: false, action: null, invitationUuid: null });
  };

  // Define DataTable columns
  const columns = [
    {
      key: "study_name" as keyof StudyInvitation,
      header: "Study",
      sortable: true,
      render: (value: any) => value || "N/A",
    },
    {
      key: "hospital_name" as keyof StudyInvitation,
      header: "Hospital",
      sortable: true,
      render: (value: any) => value || "N/A",
    },
    {
      key: "department_name" as keyof StudyInvitation,
      header: "Department",
      sortable: true,
      render: (value: any) => value || "N/A",
    },
    {
      key: "status" as keyof StudyInvitation,
      header: "Status",
      sortable: true,
      render: (value: any) => (
        <span className={`status-${value?.toLowerCase() || "pending"}`}>
          {value ? value.charAt(0).toUpperCase() + value.slice(1) : "Pending"}
        </span>
      ),
    },
    {
      key: "invited_at" as keyof StudyInvitation,
      header: "Invited At",
      sortable: true,
      render: (value: any) => value ? new Date(value).toLocaleDateString() : "N/A",
    },
  ];

  // Define DataTable actions
  const actions = [
    {
      icon: <Check size={16} />,
      tooltipText: "Accept Invitation",
      onClick: (row: StudyInvitation) => row.status === "pending" && row.uuid && handleAccept(row.uuid),
      disabled: (row: StudyInvitation) => row.status !== "pending",
    },
    {
      icon: <X size={16} />,
      tooltipText: "Reject Invitation",
      onClick: (row: StudyInvitation) => row.status === "pending" && row.uuid && handleReject(row.uuid),
      disabled: (row: StudyInvitation) => row.status !== "pending",
    },
  ];

  // Render card view for invitations
  const renderCardView = () => (
    <div className="studies-card-view">
      {filteredInvitations?.map((invitation: StudyInvitation) => (
        <div key={invitation.uuid} className="study-card">
          <div className="study-card-header">
            <h3 className="study-card-title">{invitation.study_name || "N/A"}</h3>
          </div>
          <div className="study-card-meta">
            <div className="study-card-meta-item">
              <span>Hospital: {invitation.hospital_name || "N/A"}</span>
            </div>
            <div className="study-card-meta-item">
              <span>Department: {invitation.department_name || "N/A"}</span>
            </div>
            <div className="study-card-meta-item">
              <span>
                Status:{" "}
                <span className={`status-${invitation.status?.toLowerCase() || "pending"}`}>
                  {invitation.status
                    ? invitation.status.charAt(0).toUpperCase() + invitation.status.slice(1)
                    : "Pending"}
                </span>
              </span>
            </div>
            <div className="study-card-meta-item">
              <span>
                Invited: {invitation.invited_at ? new Date(invitation.invited_at).toLocaleDateString() : "N/A"}
              </span>
            </div>
          </div>
          {invitation.status === "pending" && (
            <div className="study-card-actions">
              <button
                className="action-button accept-btn"
                onClick={() => invitation.uuid && handleAccept(invitation.uuid)}
                title="Accept Invitation"
              >
                <Check size={16} />
              </button>
              <button
                className="action-button reject-btn"
                onClick={() => invitation.uuid && handleReject(invitation.uuid)}
                title="Reject Invitation"
                style={{ marginLeft: "10px" }}
              >
                <X size={16} />
              </button>
            </div>
          )}
        </div>
      ))}
    </div>
  );

  return (
    <Wrapper>
      <Preloader />
      <div
        className="content-wrapper js-content-wrapper overflow-hidden"
        style={{ paddingBottom: "88px" }}
      >
        <div className="dashboard__content bg-light-4">
          <div className="row">
            <div className="col-12">
              <div className="studies-container">
                <div className="studies-header">
                  <h1>Study Invitations</h1>
                </div>

                {/* List of invitations */}
                <div className="studies-controls">
                  <div className="view-toggle-container">
                    <button
                      className={`view-toggle-btn ${viewMode === "table" ? "active" : ""}`}
                      onClick={() => setViewMode("table")}
                      title="Table View"
                    >
                      <List size={16} />
                    </button>
                    <button
                      className={`view-toggle-btn ${viewMode === "card" ? "active" : ""}`}
                      onClick={() => setViewMode("card")}
                      title="Card View"
                    >
                      <LayoutGrid size={16} />
                    </button>
                  </div>
                </div>

                {isLoadingInvitations ? (
                  <div className="loading-studies">Loading invitations...</div>
                ) : filteredInvitations && filteredInvitations.length > 0 ? (
                  <>
                    {viewMode === "table" ? (
                      <DataTable
                        data={filteredInvitations}
                        columns={columns}
                        actions={actions}
                        defaultItemsPerPage={10}
                        itemsPerPageOptions={[5, 10, 25, 50]}
                        noDataMessage="No invitations available"
                      />
                    ) : (
                      renderCardView()
                    )}
                  </>
                ) : (
                  <div className="no-studies">
                    {searchTerm
                      ? "No invitations match your search criteria"
                      : "No invitations received for your department."}
                  </div>
                )}

                {/* Confirmation Modal */}
                <ConfirmationModal
                  isOpen={modal.isOpen}
                  title={modal.action === "accept" ? "Accept Invitation" : "Reject Invitation"}
                  message={`Are you sure you want to ${modal.action} this invitation for ${filteredInvitations?.find((inv:StudyInvitation) => inv.uuid === modal.invitationUuid)?.study_name || "the study"}?`}
                  confirmText={modal.action === "accept" ? "Accept" : "Reject"}
                  onConfirm={handleConfirmModal}
                  onCancel={handleCancelModal}
                />
              </div>
            </div>
          </div>
        </div>
      </div>
    </Wrapper>
  );
}
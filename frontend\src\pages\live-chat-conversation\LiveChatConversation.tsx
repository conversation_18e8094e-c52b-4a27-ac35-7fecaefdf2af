import React, { useState, useEffect, useRef } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { useQueryClient } from '@tanstack/react-query';
import { 
  ArrowLeft, 
  Send, 
  Phone, 
  Video,
  Smile, 
  Info,
  Check<PERSON>he<PERSON>,
  <PERSON>,
  Pin,
  BellOff,
  Sun,
  Moon,
  <PERSON>ly,
  XCircle, // Icon for Close Chat
} from 'lucide-react';
import './LiveChatConversation.css';
import { 
  useLiveChatQuery, 
  useLiveChatMessagesQuery, 
  useSendLiveChatMessageMutation, // Re-enable for HTTP sending
  useUpdateChatStatusMutation,
  useMarkChatMessagesAsReadMutation 
} from '@/hooks/livechat.query'; 
import { LIVE_CHAT_KEYS } from '@/hooks/keys';
import type { LiveChatMessage as APILiveChatMessage, LiveChat, LiveChatUser } from '@/services/api/livechat.types'; // Renamed to avoid conflict
// Import emoji picker
import emojiData from '@emoji-mart/data'; // Renamed to avoid conflict
import Picker from '@emoji-mart/react';

// Define types for reactions
interface MessageReactionData {
  uuid: string;
  message_uuid: string; // Added this field
  user: LiveChatUser; // Re-use existing LiveChatUser for simplicity
  emoji: string;
  created_at: string;
}

// Extend LiveChatMessage to include reactions
interface LiveChatMessage extends APILiveChatMessage {
  reactions?: MessageReactionData[];
}


// Helper to group messages by sender and by date
const groupMessagesBySender = (messages: LiveChatMessage[]) => {
  if (!messages || messages.length === 0) return [];
  
  const groups: LiveChatMessage[][] = [];
  let currentGroup: LiveChatMessage[] = [messages[0]];
  
  for (let i = 1; i < messages.length; i++) {
    const currentMessage = messages[i];
    const previousMessage = messages[i - 1];
    
    // Check if messages are from the same sender and within 5 minutes of each other
    const sameUser = currentMessage.sender_role === previousMessage.sender_role;
    const timeDiff = new Date(currentMessage.created_at).getTime() - 
                    new Date(previousMessage.created_at).getTime();
    const closeInTime = timeDiff < 5 * 60 * 1000; // 5 minutes in milliseconds
    
    if (sameUser && closeInTime) {
      currentGroup.push(currentMessage);
    } else {
      groups.push([...currentGroup]);
      currentGroup = [currentMessage];
    }
  }
  
  if (currentGroup.length > 0) {
    groups.push(currentGroup);
  }
  
  return groups;
};

// Format date for the date separator
const formatMessageDate = (dateString: string) => {
  const messageDate = new Date(dateString);
  const today = new Date();
  const yesterday = new Date(today);
  yesterday.setDate(yesterday.getDate() - 1);
  
  // Check if message is from today
  if (messageDate.toDateString() === today.toDateString()) {
    return 'Today';
  }
  
  // Check if message is from yesterday
  if (messageDate.toDateString() === yesterday.toDateString()) {
    return 'Yesterday';
  }
  
  // Otherwise, return formatted date
  return messageDate.toLocaleDateString('en-US', {
    month: 'short',
    day: 'numeric',
    year: messageDate.getFullYear() !== today.getFullYear() ? 'numeric' : undefined
  });
};

// Check if two dates are on different days for date separators
const isDifferentDay = (date1: string, date2: string) => {
  const d1 = new Date(date1);
  const d2 = new Date(date2);
  return d1.toDateString() !== d2.toDateString();
};

// Note: The module-scoped `isCurrentUser` function has been removed.
// It will be defined inside the LiveChatConversation component for access to `chat` data.

const LiveChatConversation: React.FC = () => {
  const { chatId } = useParams<{ chatId: string }>();
  const navigate = useNavigate();
  const [newMessage, setNewMessage] = useState('');
  const webSocketRef = useRef<WebSocket | null>(null); // Ref to hold the WebSocket instance
  const reconnectTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const [connectionStatus, setConnectionStatus] = useState<'connecting' | 'connected' | 'disconnected'>('disconnected');

  const [showEmojiPicker, setShowEmojiPicker] = useState(false);
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const textareaRef = useRef<HTMLTextAreaElement>(null);
  const emojiButtonRef = useRef<HTMLButtonElement>(null);
  
  // States for new features
  const [darkMode, setDarkMode] = useState(false);
  const [pinnedMessages, setPinnedMessages] = useState<Set<string>>(new Set());
  const [mutedMessages, setMutedMessages] = useState<Set<string>>(new Set());
  const [showEmojiAutocomplete, setShowEmojiAutocomplete] = useState(false);
  const [emojiQuery, setEmojiQuery] = useState('');
  const [recentEmojis, setRecentEmojis] = useState(['❤️', '👍', '😂', '🔥', '👏', '😢', '🎉', '🙏']);

  // State for typing indicators (REMOVED)
  // const [typingUsers, setTypingUsers] = useState<Array<{ identifier: string; name: string }>>([]);
  // const typingTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  // Fix TypeScript errors by using the correct property names
  const { data: chat, isLoading: chatLoading } = useLiveChatQuery(chatId!);
  const { data: messages, isLoading: messagesLoading } = useLiveChatMessagesQuery(chatId!);
  const sendMessageMutation = useSendLiveChatMessageMutation(); // Use the HTTP mutation
  const updateChatStatusMutation = useUpdateChatStatusMutation();
  const markChatMessagesAsRead = useMarkChatMessagesAsReadMutation();
  const queryClient = useQueryClient();

  // Define isCurrentUser based on the current chat's patient identifier.
  // This assumes the user viewing this component IS the patient of this chat.
  // This function is used for UI rendering (e.g., styling sent/received messages).
  const isCurrentUser = (message: LiveChatMessage): boolean => {
    // If chat data or patient info isn't loaded, or message sender info is missing,
    // we cannot reliably determine if it's the current user by identifier.
    if (chatLoading || !chat || !chat.patient || !chat.patient.identifier || !message.sender || !message.sender.identifier) {
      // Fallback: Rely on the message's sender_role.
      // This is less precise if multiple patients could have sender_role 'PATIENT'
      // but is better than defaulting to false if identifier check isn't possible.
      return message.sender_role === 'PATIENT';
    }
    // Primary check: is the message sender the patient of *this* chat?
    return message.sender.identifier === chat.patient.identifier;
  };

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  // WebSocket connection with proper reconnection
  useEffect(() => {
    if (!chatId) return;

    const connectWebSocket = () => {
      const apiBaseUrl = import.meta.env.VITE_API_BASE_URL || 'http://localhost:8000/api';
      const wsProtocol = window.location.protocol === 'https:' ? 'wss' : 'ws';
      const wsHost = new URL(apiBaseUrl).host;
      const wsUrl = `${wsProtocol}://${wsHost}/socket/live-chat/${chatId}/`;
      setConnectionStatus('connecting');
      const socket = new WebSocket(wsUrl);
      webSocketRef.current = socket;
      socket.onopen = () => {
        setConnectionStatus('connected');
        // Clear any reconnection timeout
        if (reconnectTimeoutRef.current) {
          clearTimeout(reconnectTimeoutRef.current);
          reconnectTimeoutRef.current = null;
        }
      };

      socket.onmessage = (event) => {
        try {
          const eventData = JSON.parse(event.data as string); 

          if (eventData.type === 'chat_message' && eventData.message) {
            const rawMessage = eventData.message;
            const backendSender = rawMessage.sender || {}; // Data from websocket message payload's sender object

            // Determine sender_role. Priority to backendSender.role.
            // The backend should reliably send 'rawMessage.sender.role'.
            let finalSenderRole = backendSender.role;
            if (!finalSenderRole) {
              // Fallback: if current user is the patient of this chat AND the message sender is this patient, assume PATIENT.
              // This requires knowing the current user's identity and role.
              // For simplicity, if role is missing from backend, we'll mark it UNKNOWN.
              // A robust solution requires either backend to always send role, or client to reliably determine it.
              if (chat?.patient?.identifier && backendSender.identifier === chat.patient.identifier) {
                finalSenderRole = 'PATIENT'; // Heuristic: if sender is the chat's patient
              } else {
                // Add more heuristics if possible, e.g., comparing to current staff user ID
                finalSenderRole = 'UNKNOWN'; // Default if no role can be determined.
              }
            }

            const newMessage: LiveChatMessage = {
              uuid: rawMessage.uuid,
              content: rawMessage.content,
              sender: { // Map directly from backendSender, ensuring all LiveChatUser fields are covered
                identifier: backendSender.identifier || '',
                first_name: backendSender.first_name || '',
                last_name: backendSender.last_name || '',
                email: backendSender.email || '', 
                image: backendSender.image,
              },
              sender_role: finalSenderRole,
              created_at: rawMessage.created_at,
              is_system_message: rawMessage.is_system_message || false,
              is_read: rawMessage.is_read || false, // This 'is_read' refers to whether a recipient read it
            };

            // Update React Query cache for messages
            queryClient.setQueryData<LiveChatMessage[]>(
              [LIVE_CHAT_KEYS.GET_MESSAGES, chatId],
              (currentMessages) => {
                const existingMessages = currentMessages ?? []; // Handle undefined initial state

                // Check if the message (by UUID) already exists in the cache
                if (existingMessages.some(msg => msg.uuid === newMessage.uuid)) {
                  return existingMessages; // Return existing messages if duplicate
                }
                
                // Add the new message and sort the array by creation date
                const updatedMessages = [...existingMessages, newMessage].sort(
                  (a, b) => new Date(a.created_at).getTime() - new Date(b.created_at).getTime()
                );
                
                return updatedMessages; // Return the new, sorted array of messages
              }
            );
          } 
          
          if (eventData.type === 'user_status') {
            // Handle user online/offline status updates
            const updatedUserIdentifier = eventData.user.identifier;
            const isOnline = eventData.user.is_online;

            queryClient.setQueryData<LiveChat | undefined>(
              [LIVE_CHAT_KEYS.GET_BY_UUID, chatId],
              (oldChatData) => {
                if (!oldChatData) return undefined;
                return {
                  ...oldChatData,
                  participants: oldChatData.participants.map(p => 
                    p.user.identifier === updatedUserIdentifier 
                      ? { ...p, is_online: isOnline, last_seen_at: new Date().toISOString() } 
                      : p
                  ),
                };
              }
            );
            // Also update the messages query if sender info there needs to reflect online status (though less common)
            // queryClient.invalidateQueries({ queryKey: [LIVE_CHAT_KEYS.GET_MESSAGES, chatId] }); // Might be too much
          } else if (eventData.type === 'typing_status') {
            // Assuming current user's identifier is available, e.g., from a user context/store
            // For now, we'll need a placeholder for the current user's ID to avoid showing "self" typing.
            // const currentUserId = "current-user-placeholder-id"; // Replace with actual current user ID
            // For demo, let's assume we have access to `chat.patient.identifier` if current user is patient
            // or some other way to get current user's ID.
            // Typing status logic (REMOVED)
            // } else if (eventData.type === 'typing_status') {
            // console.log('Typing status update:', eventData.user);
            // const currentUserId = chat?.patient?.identifier; 
            // if (eventData.user.identifier !== currentUserId) { 
            //   setTypingUsers(prev => {
            //     if (eventData.is_typing) {
            //       if (!prev.some(u => u.identifier === eventData.user.identifier)) {
            //         return [...prev, { identifier: eventData.user.identifier, name: eventData.user.name }];
            //       }
            //     } else {
            //       return prev.filter(u => u.identifier !== eventData.user.identifier);
            //     }
            //     return prev;
            //   });
            // }
          } else if (eventData.type === 'message_read_status' && eventData.message_uuid) {
            // Handle message read status updates
            queryClient.setQueryData<LiveChatMessage[]>([LIVE_CHAT_KEYS.GET_MESSAGES, eventData.chat_uuid], (oldMessages = []) =>
              oldMessages.map(msg =>
                msg.uuid === eventData.message_uuid ? { ...msg, is_read: true } : msg
              )
            );
          } else if (eventData.type === 'message_reaction_update' && eventData.reaction) {
            const reactionData = eventData.reaction as MessageReactionData;
            queryClient.setQueryData<LiveChatMessage[]>(
              [LIVE_CHAT_KEYS.GET_MESSAGES, chatId],
              (currentMessages = []) => {
                return currentMessages.map(msg => {
                  if (msg.uuid === reactionData.message_uuid) {
                    const existingReactions = msg.reactions || [];
                    // Check if this user already reacted with this emoji (remove if so - for toggle, not implemented here)
                    // Or, if user reacts with a different emoji, replace their old one.
                    // For simplicity, this adds or updates the reaction.
                    // Backend ensures one reaction per user per message (updates emoji if same user reacts again).
                    const reactionIndex = existingReactions.findIndex(
                      r => r.user.identifier === reactionData.user.identifier // && r.emoji === reactionData.emoji (if allowing multiple distinct emojis per user)
                    );

                    let updatedReactions;
                    if (reactionIndex > -1) {
                      // User is updating their reaction
                      updatedReactions = existingReactions.map((r, index) => 
                        index === reactionIndex ? reactionData : r
                      );
                    } else {
                      // New reaction from this user
                      updatedReactions = [...existingReactions, reactionData];
                    }
                    return { ...msg, reactions: updatedReactions.sort((a,b) => new Date(a.created_at).getTime() - new Date(b.created_at).getTime()) };
                  }
                  return msg;
                });
              }
            );
          }
        } catch (e) {
          console.error('Error processing WebSocket message:', e);
        }
      };

      socket.onclose = (event) => {
        setConnectionStatus('disconnected');
        
        // Implement reconnection logic for unexpected closures
        if (event.code !== 1000 && chatId) { // 1000 = normal closure
          reconnectTimeoutRef.current = setTimeout(() => {
            connectWebSocket(); // Recursively reconnect
          }, 3000);
        }
      };

      socket.onerror = () => {
        setConnectionStatus('disconnected');
      };
    };

    // Initial connection
    connectWebSocket();

    return () => {
      if (webSocketRef.current) {
        webSocketRef.current.close(1000); // Normal closure
        webSocketRef.current = null;
      }
      if (reconnectTimeoutRef.current) {
        clearTimeout(reconnectTimeoutRef.current);
        reconnectTimeoutRef.current = null;
      }
      // if (typingTimeoutRef.current) { // REMOVED
      //   clearTimeout(typingTimeoutRef.current);
      //   typingTimeoutRef.current = null;
      // }
    };
  }, [chatId, queryClient, chat?.patient?.identifier]);

  // Mark messages as read when chat is opened/focused
  useEffect(() => {
    if (chatId) {
      const markMessagesAsReadForChat = async () => {
        try {
          console.log("Attempting to mark messages as read for chat:", chatId);
          await markChatMessagesAsRead.mutateAsync(chatId);
          // onSuccess in the hook will invalidate queries, updating the UI.
        } catch (error) {
          console.error("Error marking messages as read for chat:", error);
        }
      };
      markMessagesAsReadForChat();
    }
  }, [chatId]); // Removed markChatMessagesAsRead from dependencies


  // Handle clicks outside of emoji picker to close it
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        showEmojiPicker && 
        emojiButtonRef.current && 
        !emojiButtonRef.current.contains(event.target as Node) &&
        // Check if click is inside emoji picker
        !(event.target as Element)?.closest('.emoji-picker-container')
      ) {
        setShowEmojiPicker(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [showEmojiPicker]);

  // const sendTypingStatus = (isTyping: boolean) => { // REMOVED
  //   if (webSocketRef.current && webSocketRef.current.readyState === WebSocket.OPEN) {
  //     webSocketRef.current.send(JSON.stringify({
  //       type: 'typing_status',
  //       is_typing: isTyping,
  //     }));
  //   }
  // };

  // const handleTyping = () => { // REMOVED
  //   if (typingTimeoutRef.current) {
  //     clearTimeout(typingTimeoutRef.current);
  //   } else {
  //     sendTypingStatus(true);
  //   }
  //   typingTimeoutRef.current = setTimeout(() => {
  //     sendTypingStatus(false);
  //     typingTimeoutRef.current = null; 
  //   }, 3000); 
  // };

  // Toggle dark mode
  const toggleDarkMode = () => {
    setDarkMode(!darkMode);
    document.body.classList.toggle('dark-mode');
  };

  // Pin/unpin message
  const togglePinMessage = (messageId: string) => {
    setPinnedMessages(prev => {
      const newPinned = new Set(prev);
      if (newPinned.has(messageId)) {
        newPinned.delete(messageId);
      } else {
        newPinned.add(messageId);
      }
      return newPinned;
    });
  };

  // Mute/unmute message
  const toggleMuteMessage = (messageId: string) => {
    setMutedMessages(prev => {
      const newMuted = new Set(prev);
      if (newMuted.has(messageId)) {
        newMuted.delete(messageId);
      } else {
        newMuted.add(messageId);
      }
      return newMuted;
    });
  };

  // Add emoji reaction
  const addReaction = (messageId: string, emoji: string) => {
    if (!webSocketRef.current || webSocketRef.current.readyState !== WebSocket.OPEN) {
      console.error("Cannot send reaction: WebSocket not open.");
      return;
    }
    try {
      webSocketRef.current.send(JSON.stringify({
        type: 'message_reaction',
        message_uuid: messageId,
        emoji: emoji
      }));
      // Add to recent emojis if not already in the list
      if (!recentEmojis.includes(emoji)) {
        setRecentEmojis(prev => [emoji, ...prev.slice(0, 7)]);
      }
      // UI update will happen when the WebSocket broadcasts the reaction_update event
    } catch (error) {
      console.error('Error sending reaction via WebSocket:', error);
    }
  };

  // Handle emoji autocomplete
  useEffect(() => {
    const handleEmojiAutoComplete = () => {
      if (newMessage.includes(':') && newMessage.split(':').length > 1 && !showEmojiPicker) {
        const parts = newMessage.split(':');
        const query = parts[parts.length - 1];
        if (query && !query.includes(' ')) {
          setEmojiQuery(query);
          setShowEmojiAutocomplete(true);
        } else {
          setShowEmojiAutocomplete(false);
        }
      } else {
        setShowEmojiAutocomplete(false);
      }
    };

    handleEmojiAutoComplete();
  }, [newMessage, showEmojiPicker]);

  // Auto-adjust textarea height
  useEffect(() => {
    if (textareaRef.current) {
      textareaRef.current.style.height = 'auto';
      textareaRef.current.style.height = `${textareaRef.current.scrollHeight}px`;
    }
  }, [newMessage]);

  const handleEmojiSelect = (emoji: any) => {
    setNewMessage(prev => prev + emoji.native);
    setShowEmojiPicker(false);
    // Focus the textarea after emoji selection
    if (textareaRef.current) {
      textareaRef.current.focus();
    }
  };

  const handleCloseChat = async () => {
    if (!chatId || !chat || chat.status === 'CLOSED') return;

    try {
      await updateChatStatusMutation.mutateAsync({
        uuid: chatId, // Corrected parameter name
        status: 'CLOSED',
      });
      // Refetch chat details to get the updated status and potentially system message
      queryClient.invalidateQueries({ queryKey: [LIVE_CHAT_KEYS.GET_BY_UUID, chatId] });
      queryClient.invalidateQueries({ queryKey: [LIVE_CHAT_KEYS.GET_MESSAGES, chatId] }); // To get system message
      // Optionally, update local chat state immediately for faster UI response
      queryClient.setQueryData<LiveChat | undefined>([LIVE_CHAT_KEYS.GET_BY_UUID, chatId], (oldChat) => 
        oldChat ? { ...oldChat, status: 'CLOSED' } : undefined
      );
    } catch (error) {
      console.error('Error closing chat:', error);
      // Handle error (e.g., show a notification)
    }
  };

  const handleSendMessage = () => {
    const messageToSend = newMessage.trim();

    if (!messageToSend || !chatId) {
      return;
    }

    if (!chat || !chat.patient) {
      // Attempt to send without optimistic update if critical data is missing
      sendMessageMutation.mutate(
        { chatUuid: chatId, messageData: { content: messageToSend } },
        {
          onError: () => {
            setNewMessage(messageToSend); // Restore message to input
          },
          onSettled: () => {
            // Invalidate queries even if optimistic update wasn't possible,
            // though onSuccess of mutation already handles this.
            // queryClient.invalidateQueries({ queryKey: [LIVE_CHAT_KEYS.GET_MESSAGES, chatId] });
          }
        }
      );
      setNewMessage(''); // Clear input
      // if (typingTimeoutRef.current) { clearTimeout(typingTimeoutRef.current); typingTimeoutRef.current = null; sendTypingStatus(false); } // REMOVED
      return;
    }
    
    const tempId = crypto.randomUUID();
    // Ensure chat.patient has all fields required by LiveChatUser for the optimistic message
    const optimisticSender: LiveChatUser = {
        // uuid: chat.patient.uuid || `optimistic-patient-${tempId}`, // LiveChatUser does not have uuid
        identifier: chat.patient.identifier, // identifier is guaranteed by LiveChatUser type
        first_name: chat.patient.first_name,
        last_name: chat.patient.last_name,
        email: chat.patient.email, // email is guaranteed by LiveChatUser type
        image: chat.patient.image,
    };

    const optimisticMessage: LiveChatMessage = {
      uuid: tempId, // This is the message's own UUID, which is fine
      content: messageToSend,
      sender: optimisticSender,
      sender_role: 'PATIENT',
      created_at: new Date().toISOString(),
      is_system_message: false,
      is_read: false, 
      reactions: [],
    };

    // Optimistically update the UI
    queryClient.setQueryData<LiveChatMessage[]>(
      [LIVE_CHAT_KEYS.GET_MESSAGES, chatId],
      (oldMessages = []) => [...oldMessages, optimisticMessage]
    );
    
    setNewMessage(''); // Clear input

    sendMessageMutation.mutate(
      { chatUuid: chatId, messageData: { content: messageToSend } },
      {
        onSuccess: (actualMessageFromServer) => {
          // Replace optimistic message with actual message from server
          queryClient.setQueryData<LiveChatMessage[]>(
            [LIVE_CHAT_KEYS.GET_MESSAGES, chatId],
            (oldMessages = []) => {
              const updatedMessages = oldMessages.filter(msg => msg.uuid !== tempId);
              if (!updatedMessages.some(msg => msg.uuid === actualMessageFromServer.uuid)) {
                updatedMessages.push(actualMessageFromServer);
              }
              return updatedMessages.sort((a, b) => new Date(a.created_at).getTime() - new Date(b.created_at).getTime());
            }
          );
        },
        onError: (error) => {
          console.error('Error sending message via HTTP:', error);
          // Rollback: remove the optimistic message
          queryClient.setQueryData<LiveChatMessage[]>(
            [LIVE_CHAT_KEYS.GET_MESSAGES, chatId],
            (oldMessages = []) => oldMessages.filter(msg => msg.uuid !== tempId)
          );
          setNewMessage(messageToSend); // Restore the message to the input field
        },
      }
    );
    // if (typingTimeoutRef.current) { // REMOVED
    //   clearTimeout(typingTimeoutRef.current);
    //   typingTimeoutRef.current = null;
    //   sendTypingStatus(false);
    // }
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }
  };

  const formatTime = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleTimeString('en-US', {
      hour: '2-digit',
      minute: '2-digit',
      hour12: false
    });
  };

  // Get message status based on actual data
  const getMessageStatus = (message: LiveChatMessage) => {
    if (isCurrentUser(message)) { // Only show status for messages sent by the current user
      if (message.is_read) { // is_read on LiveChatMessage means a recipient has read it
        return 'seen'; 
      }
      // Add a 'delivered' status if backend provides such info (e.g., via WebSocket ack or another field)
      // For now, if not 'seen', it's considered 'sent' (or 'delivered' if we assume successful send)
      // This part might need more backend support for true "delivered" status.
      // Let's assume 'sent' if not explicitly 'seen'.
      // If the message object had a 'delivered_at' timestamp, we could use that.
      // For now, we'll simplify: if it's sent by current user and `is_read` is true, it's 'seen'. Otherwise, 'sent'.
      return 'sent'; // Or 'delivered' if we want to show two plain ticks by default after sending
    }
    return null; // No status for received messages
  };

  if (chatLoading || messagesLoading) {
    return (
      <div className="nurtify-conversation-container">
        <div className="nurtify-conversation-header">
          <button className="nurtify-back-button" onClick={() => navigate(-1)}>
            <ArrowLeft size={24} />
          </button>
          <div className="header-info">
            <h2>Loading...</h2>
          </div>
        </div>
        <div className="loading-state">
          <div className="loading-spinner"></div>
          <p>Loading conversation...</p>
        </div>
      </div>
    );
  }

  if (!chat) {
    return (
      <div className="nurtify-conversation-container">
        <div className="nurtify-conversation-header">
          <button className="nurtify-back-button" onClick={() => navigate(-1)}>
            <ArrowLeft size={24} />
          </button>
          <div className="header-info">
            <h2>Chat not found</h2>
          </div>
        </div>
      </div>
    );
  }

  // Group messages by sender
  const messageGroups = groupMessagesBySender(messages || []);

  // Determine other party for header display (name, avatar, online status)
  // This logic assumes a 1-to-1 or 1-to-department context.
  // `isCurrentUser` helps determine if the viewer is the patient.
  const viewingAsPatient = chat?.patient?.identifier ? isCurrentUser({ sender: chat.patient } as LiveChatMessage) : true; 
  
  let headerName = chat.department.name;
  let headerAvatarChar = chat.department.name.charAt(0);
  let isOtherPartyOnline = false; // Default to false

  if (viewingAsPatient) {
    // Patient is viewing, show department info, and online status of any staff participant
    const staffParticipant = chat.participants.find(p => p.role === 'STAFF' || p.role === 'ADMIN');
    if (staffParticipant) {
      // headerName = staffParticipant.user.first_name ? `${staffParticipant.user.first_name} ${staffParticipant.user.last_name}`.trim() : chat.department.name;
      // headerAvatarChar = staffParticipant.user.first_name ? staffParticipant.user.first_name.charAt(0) : chat.department.name.charAt(0);
      // For now, keep department name, but use staff's online status
      isOtherPartyOnline = staffParticipant.is_online ?? false;
    }
  } else {
    // Staff is viewing, show patient info
    if (chat.patient) {
      headerName = `${chat.patient.first_name} ${chat.patient.last_name}`.trim();
      headerAvatarChar = chat.patient.first_name ? chat.patient.first_name.charAt(0) : 'P';
      const patientParticipant = chat.participants.find(p => p.user.identifier === chat.patient.identifier);
      isOtherPartyOnline = patientParticipant?.is_online ?? false;
    }
  }


  return (
    <div className="nurtify-conversation-container">
      {/* Connection Status Banner */}
      {connectionStatus !== 'connected' && (
        <div className={`connection-status-banner ${connectionStatus}`}>
          {connectionStatus === 'connecting' && 'Connecting...'}
          {connectionStatus === 'disconnected' && 'Disconnected. Attempting to reconnect...'}
        </div>
      )}

      {/* Enhanced header with more actions */}
      <div className="nurtify-conversation-header">
        <button className="nurtify-back-button" onClick={() => navigate(-1)}>
          <ArrowLeft size={22} />
        </button>
        <div className="header-info">
          <div className="chat-avatar">
            <div className="avatar-circle">
              {headerAvatarChar}
            </div>
            <div className={`online-indicator ${isOtherPartyOnline ? 'online' : ''}`}></div>
          </div>
          <div className="nurtify-chat-details">
            <h2>{headerName}</h2>
            <span className={`nurtify-chat-status ${connectionStatus}`}>
              {connectionStatus === 'connected' ? 'Connected' : 
               connectionStatus === 'connecting' ? 'Connecting...' : 
               'Disconnected'}
              {isOtherPartyOnline && connectionStatus === 'connected' ? ' - Online' : ''}
            </span>
          </div>
        </div>
        <div className="header-actions">
          <button className="header-action-button action-tooltip" data-tooltip="Call">
            <Phone size={20} />
          </button>
          <button className="header-action-button action-tooltip" data-tooltip="Video">
            <Video size={20} />
          </button>
          <button className="header-action-button action-tooltip" data-tooltip="Info">
            <Info size={20} />
          </button>
          <button 
            className="header-action-button action-tooltip" 
            data-tooltip={darkMode ? "Light mode" : "Dark mode"}
            onClick={toggleDarkMode}
          >
            {darkMode ? <Sun size={20} /> : <Moon size={20} />}
          </button>
          {chat && chat.status !== 'CLOSED' && ( // Only show if chat is not closed
            <button 
              className="header-action-button action-tooltip" 
              data-tooltip="Close Chat"
              onClick={handleCloseChat}
              disabled={updateChatStatusMutation.isPending}
            >
              <XCircle size={20} />
            </button>
          )}
        </div>
      </div>

      {/* Chat Closed Banner */}
      {chat && chat.status === 'CLOSED' && (
        <div className="chat-closed-banner">
          This chat session has been closed.
        </div>
      )}

      {/* Messages container with date separators */}
      <div className="nurtify-messages-container">
        {!messages || messages.length === 0 ? (
          <div className="empty-messages">
            <div className="empty-messages-icon">
              <Send size={28} />
            </div>
            <p>No messages yet. Start the conversation!</p>
          </div>
        ) : (
          <>
            {/* Display date separator for first message */}
            {messageGroups.length > 0 && (
              <div className="message-date-separator">
                {formatMessageDate(messageGroups[0][0].created_at)}
              </div>
            )}
            
            {messageGroups.map((group, groupIndex) => {
              // Add date separators between days
              const dateElement = groupIndex > 0 && 
                isDifferentDay(group[0].created_at, messageGroups[groupIndex - 1][0].created_at) ? (
                <div key={`date-${group[0].uuid}`} className="message-date-separator">
                  {formatMessageDate(group[0].created_at)}
                </div>
              ) : null;
              
              const isUserMessage = isCurrentUser(group[0]);
              
              return (
                <React.Fragment key={`group-${group[0].uuid}`}>
                  {dateElement}
                  <div className={`message-group ${isUserMessage ? 'sent' : 'received'}`}>
                    {group.map((message, messageIndex) => (
                      <div
                        key={message.uuid}
                        className={`nurtify-message ${isUserMessage ? 'sent' : 'received'}`}
                      >
                        {!isUserMessage && messageIndex === 0 && (
                          <div className="nurtify-message-avatar">
                            <div className="avatar-circle">
                              {chat.department.name.charAt(0)}
                            </div>
                          </div>
                        )}
                        <div className="nurtify-message-content">
                          {messageIndex === 0 && !isUserMessage && (
                            <div className="nurtify-message-header">
                              <span className="sender-name">
                                {message.sender.first_name} {message.sender.last_name}
                              </span>
                            </div>
                          )}
                          <div className="nurtify-message-bubble">
                            {/* Quick reactions panel */}
                            <div className="quick-reactions">
                              {recentEmojis.slice(0, 5).map((emoji, i) => (
                                <div 
                                  key={i} 
                                  className="reaction-emoji"
                                  onClick={() => addReaction(message.uuid, emoji)}
                                >
                                  {emoji}
                                </div>
                              ))}
                            </div>

                            {/* Message actions */}
                            <div className="nurtify-message-actions">
                              <button className="message-action-btn" title="Reply">
                                <Reply size={16} />
                              </button>
                              <button 
                                className="message-action-btn" 
                                title={pinnedMessages.has(message.uuid) ? "Unpin" : "Pin"}
                                onClick={() => togglePinMessage(message.uuid)}
                              >
                                <Pin size={16} />
                              </button>
                              <button 
                                className="message-action-btn" 
                                title={mutedMessages.has(message.uuid) ? "Unmute" : "Mute"}
                                onClick={() => toggleMuteMessage(message.uuid)}
                              >
                                <BellOff size={16} />
                              </button>
                            </div>

                            {/* Pin/mute indicators */}
                            {(pinnedMessages.has(message.uuid) || mutedMessages.has(message.uuid)) && (
                              <div className="message-indicators">
                                {pinnedMessages.has(message.uuid) && (
                                  <div className="message-indicator pinned">
                                    <Pin size={14} />
                                  </div>
                                )}
                                {mutedMessages.has(message.uuid) && (
                                  <div className="message-indicator muted">
                                    <BellOff size={14} />
                                  </div>
                                )}
                              </div>
                            )}

                            <p>{message.content}</p>
                            {/* Display reactions */}
                            {message.reactions && message.reactions.length > 0 && (
                              <div className="message-reactions-display">
                                {message.reactions.map(reaction => (
                                  <span 
                                    key={reaction.uuid} 
                                    className="displayed-reaction"
                                    title={`${reaction.user.first_name} ${reaction.user.last_name}`}
                                    onClick={() => addReaction(message.uuid, reaction.emoji)} // Allow clicking to re-react or remove (future)
                                  >
                                    {reaction.emoji}
                                    {/* Optionally show count if multiple users use same emoji */}
                                    {/* {message.reactions.filter(r => r.emoji === reaction.emoji).length > 1 ? 
                                      <span className="reaction-count">{message.reactions.filter(r => r.emoji === reaction.emoji).length}</span> : ''} 
                                    */}
                                  </span>
                                ))}
                              </div>
                            )}
                          </div>
                          {messageIndex === group.length - 1 && (
                            <div className="nurtify-message-meta">
                              <span className="nurtify-message-time">
                                {formatTime(message.created_at)}
                              </span>
                              {isUserMessage && (
                                <span className="message-status">
                                  {getMessageStatus(message) === 'seen' ? (
                                    <CheckCheck size={14} className="message-status-icon" />
                                  ) : getMessageStatus(message) === 'sent' ? ( // Changed from 'delivered' to 'sent' for simplicity
                                    <Check size={14} className="message-status-icon" /> 
                                  ) : null}
                                </span>
                              )}
                            </div>
                          )}
                        </div>
                        {isUserMessage && messageIndex === 0 && (
                          <div className="nurtify-message-avatar">
                            <div className="avatar-circle">
                              {chat.patient.first_name.charAt(0)}
                            </div>
                          </div>
                        )}
                      </div>
                    ))}
                  </div>
                </React.Fragment>
              );
            })}
            {/* Typing indicator (REMOVED) */}
            {/* {typingUsers.length > 0 && ( ... )} */}
            
            <div ref={messagesEndRef} />
          </>
        )}
      </div>

      {/* Enhanced input area with emoji picker */}
      <div className="message-input-container">
        <div className="message-input-wrapper">
          <div className="message-actions">
             {/*<button className="message-action-button">
              <Paperclip size={20} />
            </button>
           <button className="message-action-button">
              <Image size={20} />
            </button> */}
            <button 
              ref={emojiButtonRef}
              className="message-action-button" 
              onClick={() => setShowEmojiPicker(!showEmojiPicker)}
            >
              <Smile size={20} />
            </button>
          </div>
          
          {/* Emoji picker */}
          {showEmojiPicker && (
            <div className="emoji-picker-container">
              <div className="recent-emojis">
                {recentEmojis.map((emoji, i) => (
                  <div 
                    key={i} 
                    className="recent-emoji-item"
                    onClick={() => {
                      setNewMessage(prev => prev + emoji);
                      setShowEmojiPicker(false);
                    }}
                  >
                    {emoji}
                  </div>
                ))}
              </div>
              <Picker 
                data={emojiData} 
                onEmojiSelect={handleEmojiSelect}
                set="native"
                theme={darkMode ? "dark" : "light"}
                previewPosition="none"
              />
            </div>
          )}
          
          {/* Emoji autocomplete */}
          {showEmojiAutocomplete && (
            <div className="emoji-autocomplete visible">
              {['😀', '😃', '😄', '😁', '😆', '😅', '🤣', '😂']
                .filter(emoji => emoji.includes(emojiQuery) || `grinning face`.includes(emojiQuery))
                .slice(0, 5)
                .map((emoji, index) => (
                  <div 
                    key={index} 
                    className="emoji-suggestion"
                    onClick={() => {
                      // Replace the query with the emoji
                      const parts = newMessage.split(':');
                      parts.pop();
                      setNewMessage(parts.join(':') + emoji);
                      setShowEmojiAutocomplete(false);
                    }}
                  >
                    <span className="emoji-suggestion-emoji">{emoji}</span>
                    <span className="emoji-suggestion-name">grinning face</span>
                  </div>
                ))
              }
            </div>
          )}
          
          <textarea
            ref={textareaRef}
            value={newMessage}
            onChange={(e) => {
              setNewMessage(e.target.value);
              // handleTyping(); // REMOVED
            }}
            // onBlur={() => { // REMOVED
            //   if (typingTimeoutRef.current) {
            //     clearTimeout(typingTimeoutRef.current);
            //     typingTimeoutRef.current = null;
            //   }
            //   if (newMessage.trim() !== '') { 
            //      sendTypingStatus(false);
            //   }
            // }}
            // onFocus={() => { // REMOVED
            //   if (newMessage.trim() !== '') {
            //     handleTyping(); 
            //   }
            // }}
            onKeyPress={handleKeyPress}
            placeholder="Type a message"
            className="message-input"
            rows={1}
            disabled={(chat && chat.status === 'CLOSED') || sendMessageMutation.isPending} 
          />
          <button
            onClick={handleSendMessage}
            disabled={!newMessage.trim() || (chat && chat.status === 'CLOSED') || sendMessageMutation.isPending} 
            className="send-button"
            >
            
              <Send size={18} />
        
          </button>
        </div>
      </div>
    </div>
  );
};

export default LiveChatConversation;

.open-query-sponsor-container {
  padding: 2rem;
  max-width: 1400px;
  margin: 0 auto;
  min-height: 100vh;
  background: #f8fafc;
}

/* Loading and Error States */
.loading-container,
.error-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 400px;
  text-align: center;
  background: white;
  border-radius: 12px;
  padding: 2rem;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid #e2e8f0;
  border-top: 4px solid #3b82f6;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 1rem;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.error-container svg {
  color: #ef4444;
  margin-bottom: 1rem;
}

.error-container p {
  color: #ef4444;
  font-size: 1.1rem;
}

/* Header Section */
.open-query-header {
  background: white;
  border-radius: 12px;
  padding: 2rem;
  margin-bottom: 2rem;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
}

.header-left h1 {
  font-size: 2rem;
  font-weight: 700;
  color: #1e293b;
  margin: 0 0 0.5rem 0;
}

.header-left p {
  color: #64748b;
  font-size: 1rem;
  margin: 0;
}

.header-stats {
  display: flex;
  gap: 1rem;
}

.stat-card {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 1rem 1.5rem;
  background: linear-gradient(135deg, #f1f5f9 0%, #e2e8f0 100%);
  border-radius: 8px;
  border-left: 4px solid #3b82f6;
}

.stat-number {
  font-size: 1.5rem;
  font-weight: 700;
  color: #1e293b;
  margin-bottom: 0.25rem;
}

.stat-label {
  font-size: 0.875rem;
  color: #64748b;
  text-align: center;
}

/* Filters Section */
.filters-section {
  background: white;
  border-radius: 12px;
  padding: 1.5rem;
  margin-bottom: 2rem;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  display: flex;
  gap: 1rem;
  align-items: center;
  flex-wrap: wrap;
}

.search-container {
  position: relative;
  flex: 1;
  min-width: 300px;
}

.search-container svg {
  position: absolute;
  left: 12px;
  top: 50%;
  transform: translateY(-50%);
  color: #64748b;
}

.search-input {
  width: 100%;
  padding: 0.75rem 0.75rem 0.75rem 2.5rem;
  border: 2px solid #e2e8f0;
  border-radius: 8px;
  font-size: 0.875rem;
  transition: border-color 0.2s ease;
}

.search-input:focus {
  outline: none;
  border-color: #3b82f6;
}

.filter-container {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.filter-container svg {
  color: #64748b;
}

.priority-filter {
  padding: 0.75rem 1rem;
  border: 2px solid #e2e8f0;
  border-radius: 8px;
  font-size: 0.875rem;
  background: white;
  cursor: pointer;
  transition: border-color 0.2s ease;
}

.priority-filter:focus {
  outline: none;
  border-color: #3b82f6;
}

/* Queries Table */
.queries-table-container {
  background: white;
  border-radius: 12px;
  padding: 0;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.queries-table {
  width: 100%;
  border-collapse: collapse;
  table-layout: auto;
}

.queries-table th {
  background: #f8fafc;
  padding: 1rem;
  text-align: left;
  font-weight: 600;
  color: #374151;
  border-bottom: 2px solid #e5e7eb;
  font-size: 0.875rem;
}



.queries-table th svg {
  margin-right: 0.5rem;
  vertical-align: middle;
}

.queries-table th:nth-child(1) { min-width: 150px; } /* Patient */
.queries-table th:nth-child(2) { min-width: 80px; }  /* Question # */
.queries-table th:nth-child(3) { min-width: 120px; } /* Form Name */
.queries-table th:nth-child(4) { min-width: 150px; } /* Description */
.queries-table th:nth-child(5) { min-width: 100px; } /* Priority */
.queries-table th:nth-child(6) { min-width: 100px; } /* Created */
.queries-table th:nth-child(7) { min-width: 120px; } /* Created By */
.queries-table th:nth-child(8) { min-width: 80px; }  /* Status */
.queries-table th:nth-child(9) { min-width: 100px; } /* Actions */

.queries-table td {
  padding: 0.75rem;
  border-bottom: 1px solid #f1f5f9;
  vertical-align: top;
  word-wrap: break-word;
  overflow: hidden;
}

.query-row:hover {
  background: #f8fafc;
}



.patient-info {
  min-width: 180px;
  max-width: 200px;
}

.patient-details {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
  margin-bottom: 0.5rem;
}

.patient-initials {
  font-weight: 600;
  color: #1e293b;
  font-size: 0.875rem;
}

.patient-study-id {
  color: #64748b;
  font-size: 0.75rem;
  font-family: monospace;
}

.patient-site {
  color: #6b7280;
  font-size: 0.75rem;
  font-style: italic;
  margin-bottom: 0.5rem;
}



.question-number {
  text-align: center;
  vertical-align: middle;
}

.question-number-badge {
  background: #0ea5e9;
  color: white;
  padding: 0.25rem 0.5rem;
  border-radius: 6px;
  font-size: 0.75rem;
  font-weight: 600;
  font-family: monospace;
  text-align: center;
  display: inline-block;
  min-width: 40px;
  margin: 0 auto;
}

.form-name {
  font-weight: 500;
  color: #374151;
}

.description {
  color: #64748b;
  line-height: 1.4;
  max-width: 200px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.priority-badge {
  display: inline-flex;
  align-items: center;
  gap: 0.25rem;
  padding: 0.25rem 0.75rem;
  border-radius: 20px;
  font-size: 0.75rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.priority-badge svg {
  width: 12px;
  height: 12px;
}

.created-date {
  color: #64748b;
  font-size: 0.875rem;
}

.created-by {
  color: #374151;
  font-size: 0.875rem;
}

.status-badge {
  display: inline-flex;
  align-items: center;
  gap: 0.25rem;
  padding: 0.25rem 0.75rem;
  border-radius: 20px;
  font-size: 0.75rem;
  font-weight: 600;
  text-transform: uppercase;
}

.status-badge.pending {
  background: #fef3c7;
  color: #d97706;
}

.status-badge svg {
  width: 12px;
  height: 12px;
}

.actions-cell {
  min-width: 120px;
}

.query-actions {
  display: flex;
  gap: 0.5rem;
  flex-direction: column;
}

.action-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.25rem;
  padding: 0.5rem 0.75rem;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.2s ease;
  font-size: 0.75rem;
  font-weight: 500;
  min-width: 80px;
}

.action-text {
  font-size: 0.75rem;
  font-weight: 500;
}

.view-btn {
  background: #eff6ff;
  color: #2563eb;
  border: 1px solid #dbeafe;
}

.view-btn:hover {
  background: #dbeafe;
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(37, 99, 235, 0.1);
}

.external-btn {
  background: #f0fdf4;
  color: #16a34a;
  border: 1px solid #dcfce7;
}

.external-btn:hover {
  background: #dcfce7;
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(22, 163, 74, 0.1);
}

/* No Queries State */
.no-queries {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 4rem 2rem;
  text-align: center;
}

.no-queries svg {
  color: #9ca3af;
  margin-bottom: 1rem;
}

.no-queries h3 {
  font-size: 1.25rem;
  font-weight: 600;
  color: #374151;
  margin: 0 0 0.5rem 0;
}

.no-queries p {
  color: #6b7280;
  margin: 0;
}

/* Summary Footer */
.queries-summary {
  background: white;
  border-radius: 12px;
  padding: 1rem 1.5rem;
  margin-top: 1rem;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  text-align: center;
}

.queries-summary p {
  margin: 0;
  color: #64748b;
  font-size: 0.875rem;
}

.queries-summary span {
  font-weight: 600;
  color: #374151;
}

/* Query Detail View */
.query-detail-view {
  background: white;
  border-radius: 12px;
  padding: 2rem;
  margin-top: 2rem;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.back-button {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  background: none;
  border: none;
  color: #3b82f6;
  font-size: 0.875rem;
  cursor: pointer;
  margin-bottom: 1.5rem;
  padding: 0.5rem;
  border-radius: 6px;
  transition: background-color 0.2s ease;
}

.back-button:hover {
  background: #eff6ff;
}

.query-card {
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  overflow: hidden;
}

.query-header {
  background: #f8fafc;
  padding: 1.5rem;
  border-bottom: 1px solid #e5e7eb;
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
}

.query-info h3 {
  margin: 0 0 0.5rem 0;
  font-size: 1.25rem;
  color: #1e293b;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.query-description {
  color: #64748b;
  margin: 0 0 1rem 0;
  line-height: 1.5;
}

.query-creator,
.patient-info {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-bottom: 0.5rem;
  font-size: 0.875rem;
}

.creator-label,
.patient-label {
  color: #6b7280;
  font-weight: 500;
}

.creator-name,
.patient-name {
  color: #374151;
  font-weight: 600;
}

.query-meta {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
  align-items: flex-end;
}

.query-date {
  display: flex;
  align-items: center;
  gap: 0.25rem;
  color: #64748b;
  font-size: 0.875rem;
}

.resolve-query-btn {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  background: #10b981;
  color: white;
  border: none;
  padding: 0.5rem 1rem;
  border-radius: 6px;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.resolve-query-btn:hover {
  background: #059669;
}

.responses-section {
  padding: 1.5rem;
  max-height: 400px;
  overflow-y: auto;
}

.loading-responses,
.no-responses {
  text-align: center;
  color: #64748b;
  padding: 2rem;
}

.response-item {
  border: 1px solid #e5e7eb;
  border-radius: 6px;
  padding: 1rem;
  margin-bottom: 1rem;
  background: #f9fafb;
}

.response-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.75rem;
}

.response-meta {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.responder-name {
  font-weight: 600;
  color: #374151;
}

.clarification-badge {
  display: flex;
  align-items: center;
  gap: 0.25rem;
  background: #fef3c7;
  color: #d97706;
  padding: 0.25rem 0.5rem;
  border-radius: 12px;
  font-size: 0.75rem;
  font-weight: 500;
}

.response-date {
  display: flex;
  align-items: center;
  gap: 0.25rem;
  color: #64748b;
  font-size: 0.875rem;
}

.response-message {
  color: #374151;
  line-height: 1.5;
  margin: 0;
}

.resolved-query-notice {
  background: #f0fdf4;
  border: 1px solid #bbf7d0;
  border-radius: 6px;
  padding: 1rem;
  margin: 1.5rem;
  display: flex;
  align-items: center;
  gap: 0.75rem;
  color: #166534;
}

.resolution-notes {
  margin-top: 1rem;
  padding-top: 1rem;
  border-top: 1px solid #bbf7d0;
}

.resolution-notes h4 {
  margin: 0 0 0.5rem 0;
  font-size: 0.875rem;
  font-weight: 600;
}

.resolution-notes p {
  margin: 0;
  font-size: 0.875rem;
  line-height: 1.5;
}

.response-form {
  padding: 1.5rem;
  border-top: 1px solid #e5e7eb;
  background: #f9fafb;
}

.response-input-container {
  margin-bottom: 1rem;
}

.response-input-container textarea {
  width: 100%;
  padding: 0.75rem;
  border: 2px solid #e5e7eb;
  border-radius: 6px;
  font-size: 0.875rem;
  resize: vertical;
  min-height: 80px;
  transition: border-color 0.2s ease;
}

.response-input-container textarea:focus {
  outline: none;
  border-color: #3b82f6;
}

.response-options {
  margin-top: 0.75rem;
}

.clarification-toggle {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  cursor: pointer;
  font-size: 0.875rem;
  color: #64748b;
}

.clarification-toggle input[type="checkbox"] {
  margin: 0;
}

.toggle-label {
  display: flex;
  align-items: center;
  gap: 0.25rem;
}

.submit-response-btn {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  background: #3b82f6;
  color: white;
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: 6px;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.submit-response-btn:hover:not(:disabled) {
  background: #2563eb;
}

.submit-response-btn:disabled {
  background: #9ca3af;
  cursor: not-allowed;
}

/* Modal Styles */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.modal-content {
  background: white;
  border-radius: 12px;
  padding: 2rem;
  max-width: 500px;
  width: 90%;
  max-height: 80vh;
  overflow-y: auto;
}

.modal-content h3 {
  margin: 0 0 0.5rem 0;
  color: #1e293b;
  font-size: 1.25rem;
}

.modal-content p {
  color: #64748b;
  margin: 0 0 1.5rem 0;
  font-size: 0.875rem;
}

.modal-content textarea {
  width: 100%;
  padding: 0.75rem;
  border: 2px solid #e5e7eb;
  border-radius: 6px;
  font-size: 0.875rem;
  resize: vertical;
  min-height: 100px;
  margin-bottom: 1.5rem;
  transition: border-color 0.2s ease;
}

.modal-content textarea:focus {
  outline: none;
  border-color: #3b82f6;
}

.modal-actions {
  display: flex;
  gap: 1rem;
  justify-content: flex-end;
}

.cancel-btn {
  background: #f3f4f6;
  color: #374151;
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: 6px;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.cancel-btn:hover {
  background: #e5e7eb;
}

.resolve-btn {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  background: #10b981;
  color: white;
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: 6px;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.resolve-btn:hover {
  background: #059669;
}

/* Responsive Design */
@media (max-width: 768px) {
  .open-query-sponsor-container {
    padding: 1rem;
  }

  .open-query-header,
  .filters-section,
  .queries-table-container,
  .queries-summary {
    padding: 1.5rem;
  }

  .header-content {
    flex-direction: column;
    gap: 1rem;
  }

  .header-left h1 {
    font-size: 1.5rem;
  }

  .filters-section {
    flex-direction: column;
    align-items: stretch;
  }

  .search-container {
    min-width: auto;
  }

  .queries-table-container {
    overflow-x: auto;
  }

  .queries-table {
    min-width: 800px;
  }

  .description {
    max-width: 200px;
  }
}
import api from "../api";

export interface Symptom {
  uuid: string;
  patient: string;
  name: string;
  description: string;
  start_date: string;
  start_time: string;
  severity: string;
  severity_display: string;
  hospitalization_required: boolean;
  status: string;
  status_display: string;
  relatedness: string;
  relatedness_display: string;
  category: string;
  category_display: string;
  review_status: string;
  review_status_display: string;
  patient_comment: string;
  created_at: string;
  updated_at: string;
  resolved_date: string | null;
  resolved_by: string | null;
}

export interface PaginatedResponse<T> {
  count: number;
  next: string | null;
  previous: string | null;
  results: T[];
}

export interface CreateSymptomData {
  patient_uuid: string;
  name: string;
  description: string;
  start_date: string;
  start_time: string;
  severity: string;
  hospitalization_required: boolean;
  status: string;
  relatedness: string;
  category: string;
  review_status?: string;
  patient_comment: string;
  resolved_date?: string;
}

export interface SymptomLog {
  uuid: string;
  symptom: string;
  symptom_uuid: string;
  old_status: string;
  old_status_display: string;
  new_status: string;
  new_status_display: string;
  changed_by: {
    identifier: string;
    first_name: string;
    last_name: string;
    email: string;
  };
  changed_at: string;
  resolved_date: string | null;
  notes: string | null;
}

export const getAllSymptoms = async (patientUuid: string): Promise<Symptom[]> => {
  const response = await api.get<PaginatedResponse<Symptom>>(`/patient/symptoms/?patient_uuid=${patientUuid}`);
  return response.data.results;
};

export const createSymptom = async (data: CreateSymptomData): Promise<Symptom> => {
  if (!data.patient_uuid) {
    throw new Error("Patient UUID is required");
  }

  console.log('Creating symptom with data:', data); // Debug log
  const response = await api.post<Symptom>("/patient/symptoms/", data);
  return response.data;
};

export const updateSymptom = async (uuid: string, data: Partial<CreateSymptomData>): Promise<Symptom> => {
  const response = await api.patch<Symptom>(`/patient/symptoms/${uuid}/`, data);
  return response.data;
};

export const getSymptomLogs = async (symptomUuid: string): Promise<PaginatedResponse<SymptomLog>> => {
  const response = await api.get<PaginatedResponse<SymptomLog>>(`/patient/symptom-logs/?symptom_uuid=${symptomUuid}`);
  return response.data;
};

// New API endpoints for filtering and actions
export const getSymptomsByCategory = async (category: string, patientUuid?: string): Promise<Symptom[]> => {
  const params = new URLSearchParams({ category });
  if (patientUuid) {
    params.append('patient_uuid', patientUuid);
  }
  const response = await api.get<Symptom[]>(`/patient/symptoms/by-category/?${params}`);
  return response.data;
};

export const getSymptomsBySeverity = async (severity: string, patientUuid?: string): Promise<Symptom[]> => {
  const params = new URLSearchParams({ severity });
  if (patientUuid) {
    params.append('patient_uuid', patientUuid);
  }
  const response = await api.get<Symptom[]>(`/patient/symptoms/by-severity/?${params}`);
  return response.data;
};

export const getSymptomsByReviewStatus = async (reviewStatus: string, patientUuid?: string): Promise<Symptom[]> => {
  const params = new URLSearchParams({ review_status: reviewStatus });
  if (patientUuid) {
    params.append('patient_uuid', patientUuid);
  }
  const response = await api.get<Symptom[]>(`/patient/symptoms/by-review-status/?${params}`);
  return response.data;
};

export const getHospitalizationRequiredSymptoms = async (patientUuid?: string): Promise<Symptom[]> => {
  const params = patientUuid ? `?patient_uuid=${patientUuid}` : '';
  const response = await api.get<Symptom[]>(`/patient/symptoms/hospitalization-required/${params}`);
  return response.data;
};

export const markSymptomAsReviewed = async (symptomUuid: string): Promise<Symptom> => {
  const response = await api.patch<Symptom>(`/patient/symptoms/${symptomUuid}/mark-reviewed/`);
  return response.data;
};

// Enhanced getAllSymptoms with filtering options
export const getAllSymptomsWithFilters = async (
  patientUuid: string,
  filters?: {
    severity?: string;
    status?: string;
    relatedness?: string;
    category?: string;
    review_status?: string;
    hospitalization_required?: boolean;
    search?: string;
  }
): Promise<Symptom[]> => {
  const params = new URLSearchParams({ patient_uuid: patientUuid });

  if (filters) {
    Object.entries(filters).forEach(([key, value]) => {
      if (value !== undefined && value !== null) {
        params.append(key, String(value));
      }
    });
  }

  const response = await api.get<PaginatedResponse<Symptom>>(`/patient/symptoms/?${params}`);
  return response.data.results;
};
import api from "@/services/api";
import type { Study, Visit, PatientEnrollment } from "@/store/scheduleEventState";
import { RescheduleRequest, RescheduleResponse, RescheduleRequestResponse } from '@/types/study';

// Study API functions
export const getAllStudies = async () => {
  const { data } = await api.get("study/studies/");
  return data.results;
};

export const getStudiesBySponsor = async (identifier: string) => {
  const { data } = await api.get(`study/studies/by-sponsor/${identifier}/`);
  return data.results;
};

export const getStudiesCountBySponsor = async (identifier: string) => {
  const { data } = await api.get(`study/studies/by-sponsor/${identifier}/`);
  return data;
};

export const getStudyByUuid = async (uuid: string) => {
  const { data } = await api.get(`study/studies/${uuid}/`);
  return data;
};

export const createStudy = async (studyData: Partial<Study>) => {
  const { data } = await api.post("study/studies/", studyData);
  return data;
};

export const updateStudy = async (uuid: string, studyData: Partial<Study>) => {
  const { data } = await api.put(`study/studies/${uuid}/`, studyData);
  return data;
};

export const patchStudy = async (uuid: string, studyData: Partial<Study>) => {
  const { data } = await api.patch(`study/studies/${uuid}/`, studyData);
  return data;
};

export const deleteStudy = async (uuid: string) => {
  const { data } = await api.delete(`study/studies/${uuid}/`);
  return data;
};

export const getPatientsPerStudy = async () => {
  const { data } = await api.get("study/studies/kpi/patients-per-study/");
  return data;
};

export const getPatientsPerStatus = async (studyUuid: string) => {
  const { data } = await api.get(`study/studies/${studyUuid}/kpi/patients-per-status/`);
  const patientStatusData = [];

  if (data.progress_count > 0) {
    patientStatusData.push({ name: 'Pending', value: data.progress_count });
  }
  if (data.success_count > 0) {
    patientStatusData.push({ name: 'Success', value: data.success_count });
  }
  if (data.failed_count > 0) {
    patientStatusData.push({ name: 'Failed', value: data.failed_count });
  }

  return patientStatusData;
};

export const getStudyEnrollmentsByPatient = async (patientUuid: string) => {
  const { data } = await api.get(`study/enrollments/by-patient/${patientUuid}/`);
  return data.results || data;
};

export const getStudyEnrollmentsByStudy = async (studyUuid: string) => {
  const { data } = await api.get(`study/enrollments/by-study/${studyUuid}/`);
  return data.results || data;
};

// Visit API functions
export const getAllVisits = async () => {
  const { data } = await api.get("study/visits/");
  return data;
};

export const getVisitByUuid = async (uuid: string) => {
  const { data } = await api.get(`study/visits/${uuid}/`);
  return data;
};

// Update enrollment status
export const updateEnrollmentStatus = async (enrollmentUuid: string, statusData: { status: string }) => {
  const { data } = await api.patch(`study/enrollments/${enrollmentUuid}/update-status/`, statusData);
  return data;
};

export const createVisitTemplate = async (visitData: Partial<Visit>) => {
  const { data } = await api.post("study/visit-templates/", visitData);
  return data;
};

export const updateVisitTemplate = async (uuid: string, visitData: Partial<Visit>) => {
  const { data } = await api.put(`study/visit-templates/${uuid}/`, visitData);
  return data;
};

export const deleteVisitTemplate = async (uuid: string) => {
  const { data } = await api.delete(`study/visit-templates/${uuid}/`);
  return data;
};

export const getVisitTemplates = async () => {
  const { data } = await api.get("study/visit-templates/");
  return data;
};

export const getVisitTemplatesByStudy = async (studyUuid: string) => {
  const { data } = await api.get(`study/studies/${studyUuid}/templates/`);
  return data;
};

export const getVisitTemplateByUuid = async (uuid: string) => {
  const { data } = await api.get(`study/visit-templates/${uuid}/`);
  return data;
};

export const updateVisit = async (uuid: string, visitData: Partial<Visit>) => {
  const { data } = await api.put(`study/visits/${uuid}/`, visitData);
  return data;
};

export const patchVisit = async (uuid: string, visitData: Partial<Visit>) => {
  const { data } = await api.patch(`study/visits/${uuid}/`, visitData);
  return data;
};

export const deleteVisit = async (uuid: string) => {
  const { data } = await api.delete(`study/visits/${uuid}/`);
  return data;
};

// Create a new visit (not a template)
export const createVisit = async (visitData: Partial<Visit>) => {
  const { data } = await api.post("study/visits/", visitData);
  return data;
};

// Get visits by study
export const getVisitsByStudy = async (studyUuid: string) => {
  const { data } = await api.get(`study/visits/?study_uuid=${studyUuid}`);
  return data.results;
};

// Get visits by enrollment
export const getVisitsByEnrollment = async (enrollmentUuid: string) => {
  const { data } = await api.get(`study/visits/by-enrollment/${enrollmentUuid}/`);
  return data;
};

export const getStudyTeamMembers = async (studyUuid: string) => {
  try {
    const response = await api.get(`/study/studies/${studyUuid}/team-members/`);
    console.log('Team members response:', response.data);

    // Handle different possible response structures
    if (response.data.team_users) {
      return response.data.team_users;
    } else if (response.data.results) {
      return response.data.results;
    } else if (Array.isArray(response.data)) {
      return response.data;
    } else {
      console.warn('Unexpected team members response structure:', response.data);
      return [];
    }
  } catch (error) {
    console.error('Error fetching team members:', error);
    throw error;
  }
};

// Create patient enrollment
export const createPatientEnrollment = async (enrollment: PatientEnrollment): Promise<PatientEnrollment> => {
  const response = await api.post("/study/enrollments/enroll/", enrollment);
  return response.data;
};

export const createRescheduleRequest = async (data: RescheduleRequest): Promise<RescheduleResponse> => {
  const response = await api.post<RescheduleResponse>('/study/reschedule-requests/', data);
  return response.data;
};

export const getRescheduleRequestsByPatient = async (patientUuid: string): Promise<RescheduleRequestResponse[]> => {
  const { data } = await api.get(`/study/reschedule-requests/by_patient/?patient_uuid=${patientUuid}`);
  return Array.isArray(data) ? data : (data.results || []);
};

export const acceptRescheduleRequest = async (uuid: string): Promise<RescheduleResponse> => {
  const response = await api.post<RescheduleResponse>(`/study/reschedule-requests/${uuid}/accept/`);
  return response.data;
};

export const rejectRescheduleRequest = async (uuid: string): Promise<RescheduleResponse> => {
  const response = await api.post<RescheduleResponse>(`/study/reschedule-requests/${uuid}/reject/`);
  return response.data;
};

export async function getAcceptedRescheduleRequestsByPatient(patientUuid: string) {
  return api.get(`/study/reschedule-requests/accepted/?patient_uuid=${patientUuid}`);
}

export async function getPendingRescheduleRequestsByPatient(patientUuid: string) {
  return api.get(`/study/reschedule-requests/pending/?patient_uuid=${patientUuid}`);
}

export async function getRejectedRescheduleRequestsByPatient(patientUuid: string) {
  return api.get(`/study/reschedule-requests/rejected/?patient_uuid=${patientUuid}`);
}

export const createStudyInvitation = async (data: { study: string; departments: string[] }) => {
  try {
    const response = await api.post('study/study-invitations/', data);
    return response.data;
  } catch (error) {
    console.error("Error creating study invitation:", error);
    throw error;
  }
};

export const getStudyInvitationsByDepartment = async (departmentUuid: string) => {
  try {
    const response = await api.get(`study/study-invitations/by-department/${departmentUuid}/`);
    return response.data;
  } catch (error) {
    console.error("Error fetching study invitations:", error);
    throw error;
  }
};

export const acceptStudyInvitation = async (invitationUuid: string) => {
  try {
    const response = await api.post(`study/study-invitations/${invitationUuid}/accept/`);
    return response.data;
  } catch (error) {
    console.error("Error accepting study invitation:", error);
    throw error;
  }
};

export const rejectStudyInvitation = async (invitationUuid: string) => {
  try {
    const response = await api.post(`study/study-invitations/${invitationUuid}/reject/`);
    return response.data;
  } catch (error) {
    console.error("Error rejecting study invitation:", error);
    throw error;
  }
};

export const getStudiesByDepartment = async (departmentUuid: string) => {
  const { data } = await api.get(`study/studies/by-department/${departmentUuid}/`);
  return data.results || data;
};

export const getStudyInvitationsByInviter = async (inviterIdentifier: string) => {
  const { data } = await api.get(`study/study-invitations/by-inviter/?identifier=${inviterIdentifier}`);
  return data.results || data;
};

// Principal Investigator API functions
export const assignPrincipalInvestigator = async (data: {
  study: string;
  department: string;
  investigator: string;
}) => {
  const { data: response } = await api.post("study/principal-investigators/", data);
  return response;
};

export const getAvailableInvestigators = async (departmentUuid: string) => {
  const { data } = await api.get(`study/principal-investigators/available-investigators/${departmentUuid}/`);
  return data;
};

export const getStudiesNeedingPI = async (departmentUuid: string) => {
  const { data } = await api.get(`study/principal-investigators/pending-assignments/${departmentUuid}/`);
  return data;
};

export const getPIAssignmentsByStudy = async (studyUuid: string) => {
  try {
    const response = await api.get(`study/principal-investigators/by-study/${studyUuid}/`);
    return response.data;
  } catch (error) {
    console.error("Error fetching PI assignments by study:", error);
    throw error;
  }
};

export const getPIAssignmentsByDepartment = async (departmentUuid: string) => {
  try {
    const response = await api.get(`study/principal-investigators/by-department/${departmentUuid}/`);
    return response.data;
  } catch (error) {
    console.error("Error fetching PI assignments by department:", error);
    throw error;
  }
};

export const acceptPIAssignment = async (piUuid: string) => {
  try {
    const response = await api.post(`study/principal-investigators/${piUuid}/accept/`);
    return response.data;
  } catch (error) {
    console.error("Error accepting PI assignment:", error);
    throw error;
  }
};

export const rejectPIAssignment = async (piUuid: string) => {
  try {
    const response = await api.post(`study/principal-investigators/${piUuid}/reject/`);
    return response.data;
  } catch (error) {
    console.error("Error rejecting PI assignment:", error);
    throw error;
  }
};

export const getAllPIAssignments = async (departmentUuid: string) => {
  const { data } = await api.get(`study/principal-investigators/by-department/${departmentUuid}/`);
  return data;
};

// Get approved studies for current user based on delegation logs
export const getMyApprovedStudies = async () => {
  const { data } = await api.get("study/delegation-logs/my-approved-studies/");
  return data;
};

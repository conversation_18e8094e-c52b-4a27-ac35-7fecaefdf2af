import React, { useState } from 'react';
import MedicationTable, { TableColumn, TableAction } from './MedicationTable';

const MedicationTableExample: React.FC = () => {
  const [currentPage, setCurrentPage] = useState(1);

  // Define columns matching the image
  const columns: TableColumn[] = [
    { key: 'medication', label: 'Medication', width: '120px' },
    { key: 'indication', label: 'Indication', width: '100px' },
    { key: 'dose', label: 'Dose', width: '80px' },
    { key: 'schedule', label: 'Schedule', width: '100px' },
    { key: 'form', label: 'Form', width: '80px' },
    { key: 'route', label: 'Route', width: '80px' },
    { key: 'startDate', label: 'Start Date', width: '100px' },
    { key: 'endDate', label: 'End Date', width: '100px' },
    { key: 'status', label: 'Status', width: '100px' }
  ];

  // Sample data matching the image
  const medicationData = [
    {
      medication: 'Codeine',
      indication: 'Fever',
      dose: '500 mg',
      schedule: 'QD (Once)',
      form: 'Tablet',
      route: 'Oral',
      startDate: '6/4/2025',
      endDate: '6/4/2025',
      status: <span className="status-continue">Continue</span>
    },
    {
      medication: 'Codeine',
      indication: 'Fever',
      dose: '500 mg',
      schedule: 'QD (Once)',
      form: 'Tablet',
      route: 'Oral',
      startDate: '6/4/2025',
      endDate: '6/4/2025',
      status: <span className="status-continue">Continue</span>
    },
    {
      medication: 'Codeine',
      indication: 'Fever',
      dose: '500 mg',
      schedule: 'QD (Once)',
      form: 'Tablet',
      route: 'Oral',
      startDate: '6/4/2025',
      endDate: '6/4/2025',
      status: <span className="status-continue">Continue</span>
    },
    {
      medication: 'Codeine',
      indication: 'Fever',
      dose: '500 mg',
      schedule: 'QD (Once)',
      form: 'Tablet',
      route: 'Oral',
      startDate: '6/4/2025',
      endDate: '6/4/2025',
      status: <span className="status-continue">Continue</span>
    },
    {
      medication: 'Codeine',
      indication: 'Fever',
      dose: '500 mg',
      schedule: 'QD (Once)',
      form: 'Tablet',
      route: 'Oral',
      startDate: '6/4/2025',
      endDate: '6/4/2025',
      status: <span className="status-continue">Continue</span>
    }
  ];

  // Define actions matching the image (edit and delete icons)
  const actions: TableAction[] = [
    {
      type: 'edit',
      onClick: (row, index) => {
        console.log('Edit medication:', row, 'at index:', index);
        // Handle edit action
      }
    },
    {
      type: 'delete',
      onClick: (row, index) => {
        console.log('Delete medication:', row, 'at index:', index);
        // Handle delete action
      }
    }
  ];

  const handlePageChange = (page: number) => {
    setCurrentPage(page);
    console.log('Page changed to:', page);
  };

  return (
    <div style={{ padding: '20px', maxWidth: '1200px', margin: '0 auto' }}>
      <h2 style={{ marginBottom: '20px', color: '#374151' }}>Medication Table Example</h2>
      
      <MedicationTable
        columns={columns}
        data={medicationData}
        actions={actions}
        currentPage={currentPage}
        totalPages={10}
        onPageChange={handlePageChange}
        loading={false}
        emptyMessage="No medications found"
      />
    </div>
  );
};

export default MedicationTableExample;

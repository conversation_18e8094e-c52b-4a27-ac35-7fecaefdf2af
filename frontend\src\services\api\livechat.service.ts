import api from "@/services/api"; // Ensure this path corresponds to your axios config
import type {
  LiveChat,
  LiveChatCreateData,
  LiveChatMessage,
  LiveChatMessageCreateData,
  LiveChatNotification,
  LiveChatAnalytics,
  LiveChatFilterParams,
  LiveChatPaginatedResponse,
  LiveChatActionResponse,
  LiveChatSendMessageResponse,
  LiveChatMarkNotificationResponse,
  DepartmentStudyTeamUsersResponse
} from "./livechat.types";

// Live Chat CRUD Operations
export const getAllLiveChats = async (params?: LiveChatFilterParams): Promise<LiveChatPaginatedResponse<LiveChat>> => {
  const queryParams = new URLSearchParams();

  if (params) {
    Object.entries(params).forEach(([key, value]) => {
      if (value !== undefined && value !== null) {
        queryParams.append(key, value.toString());
      }
    });
  }

  const url = `/live-chat/chats/${queryParams.toString() ? `?${queryParams.toString()}` : ''}`;
  const response = await api.get(url);
  return response.data;
};

export const getLiveChatByUuid = async (uuid: string): Promise<LiveChat> => {
  const { data } = await api.get(`/live-chat/chats/${uuid}/`);
  return data;
};

export const createLiveChat = async (chatData: LiveChatCreateData): Promise<LiveChat> => {
  const { data } = await api.post("/live-chat/chats/", chatData);
  return data;
};

export const updateLiveChat = async (
  uuid: string,
  chatData: Partial<LiveChat>
): Promise<LiveChat> => {
  const response = await api.put(`/live-chat/chats/${uuid}/`, chatData);
  return response.data;
};

export const partialUpdateLiveChat = async (
  uuid: string,
  chatData: Partial<LiveChat>
): Promise<LiveChat> => {
  const response = await api.patch(`/live-chat/chats/${uuid}/`, chatData);
  return response.data;
};

export const deleteLiveChat = async (uuid: string): Promise<void> => {
  await api.delete(`/live-chat/chats/${uuid}/`);
};

// Live Chat Actions
export const closeLiveChat = async (uuid: string): Promise<LiveChatActionResponse> => {
  const { data } = await api.post(`/live-chat/chats/${uuid}/close/`);
  return data;
};

export const reopenLiveChat = async (uuid: string): Promise<LiveChatActionResponse> => {
  const { data } = await api.post(`/live-chat/chats/${uuid}/reopen/`);
  return data;
};

export const updateChatStatus = async (uuid: string, status: 'OPEN' | 'CLOSED'): Promise<LiveChat> => {
  const { data } = await api.post(`/live-chat/chats/${uuid}/update-status/`, { status });
  return data;
};

// Live Chat Messages
export const getLiveChatMessages = async (chatUuid: string): Promise<LiveChatMessage[]> => {
  const { data } = await api.get(`/live-chat/chats/${chatUuid}/messages/`);
  return data;
};

export const sendLiveChatMessage = async (
  chatUuid: string,
  messageData: LiveChatMessageCreateData
): Promise<LiveChatSendMessageResponse> => {
  const { data } = await api.post(`/live-chat/chats/${chatUuid}/send_message/`, messageData);
  return data;
};

export const markAllMessagesAsRead = async (chatUuid: string): Promise<{ status: string; messages_marked_as_read: number }> => {
  const { data } = await api.post(`/live-chat/chats/${chatUuid}/mark_all_as_read/`);
  return data;
};

// Live Chat Analytics
export const getLiveChatAnalytics = async (): Promise<LiveChatAnalytics> => {
  const { data } = await api.get("/live-chat/chats/analytics/");
  return data;
};

// Live Chat Notifications
export const getAllLiveChatNotifications = async (): Promise<LiveChatPaginatedResponse<LiveChatNotification>> => {
  const response = await api.get("/live-chat/notifications/");
  return response.data;
};

export const getLiveChatNotificationById = async (id: number): Promise<LiveChatNotification> => {
  const { data } = await api.get(`/live-chat/notifications/${id}/`);
  return data;
};

export const markLiveChatNotificationAsRead = async (id: number): Promise<LiveChatNotification> => {
  const { data } = await api.post(`/live-chat/notifications/${id}/mark_as_read/`);
  return data;
};

export const markAllLiveChatNotificationsAsRead = async (): Promise<LiveChatMarkNotificationResponse> => {
  const { data } = await api.post("/live-chat/notifications/mark_all_as_read/");
  return data;
};

// WebSocket Connection Helper
export const getLiveChatWebSocketUrl = (chatUuid: string): string => {
  // Use VITE_SOCKET_BASE_URL from environment variables for WebSocket base URL
  const socketBaseUrl = import.meta.env.VITE_SOCKET_BASE_URL || window.location.origin;
  const wsProtocol = socketBaseUrl.startsWith('https') ? 'wss' : 'ws';
  const wsBaseUrl = socketBaseUrl.replace(/^https?/, wsProtocol);

  return `${wsBaseUrl}/live-chat/${chatUuid}/`;
};

// Utility Functions
export const filterLiveChatsByStatus = async (status: 'OPEN' | 'PENDING' | 'CLOSED'): Promise<LiveChatPaginatedResponse<LiveChat>> => {
  return getAllLiveChats({ status });
};

export const searchLiveChats = async (searchTerm: string): Promise<LiveChatPaginatedResponse<LiveChat>> => {
  return getAllLiveChats({ search: searchTerm });
};

export const getActiveLiveChats = async (): Promise<LiveChatPaginatedResponse<LiveChat>> => {
  return getAllLiveChats({ is_active: true });
};

export const getInactiveLiveChats = async (): Promise<LiveChatPaginatedResponse<LiveChat>> => {
  return getAllLiveChats({ is_active: false });
};

// Sorting helpers
export const getLiveChatsSortedByDate = async (ascending = false): Promise<LiveChatPaginatedResponse<LiveChat>> => {
  const ordering = ascending ? 'created_at' : '-created_at';
  return getAllLiveChats({ ordering });
};

export const getLiveChatsSortedByLastMessage = async (ascending = false): Promise<LiveChatPaginatedResponse<LiveChat>> => {
  const ordering = ascending ? 'last_message_at' : '-last_message_at';
  return getAllLiveChats({ ordering });
};

// Department Study Team Users
export const getDepartmentStudyTeamUsers = async (
  patientId: string,
  departmentId: string
): Promise<DepartmentStudyTeamUsersResponse> => {
  const { data } = await api.get(`/live-chat/chats/get_department_study_team_users/`, {
    params: {
      patient_id: patientId,
      department_id: departmentId
    }
  });
  return data;
};

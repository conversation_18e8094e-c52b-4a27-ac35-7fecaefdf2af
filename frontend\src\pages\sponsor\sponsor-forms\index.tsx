import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { useNavigate } from 'react-router-dom';
import { 
  FileText, 
  Search, 
  Filter,
  Eye,
  Calendar,
  Tag,
  Building,
  BookOpen
} from 'lucide-react';
import { useCurrentUserQuery } from '@/hooks/user.query';
import { useGetFormsBySponsor } from '@/hooks/form.query';
import { Form } from '@/types/types';
import './sponsor-forms.css';



interface Department {
  uuid: string;
  name: string;
  hospital?: {
    uuid: string;
    name: string;
  };
}

interface Study {
  uuid: string;
  name: string;
  description: string;
}

const SponsorForms: React.FC = () => {
  const navigate = useNavigate();
  const { data: currentUser } = useCurrentUserQuery();
  const { data: formsData, isLoading, error } = useGetFormsBySponsor(currentUser?.identifier || "");
  
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('all');
  const [categoryFilter, setCategoryFilter] = useState('all');
  const [departmentFilter, setDepartmentFilter] = useState('all');
  const [studyFilter, setStudyFilter] = useState('all');

  // Extract unique departments and studies from forms data
  const [availableDepartments, setAvailableDepartments] = useState<Department[]>([]);
  const [availableStudies, setAvailableStudies] = useState<Study[]>([]);
  const [availableCategories, setAvailableCategories] = useState<string[]>([]);

  useEffect(() => {
    if (formsData?.results) {
      // Debug: Log form statuses
      console.log('Form statuses:', formsData.results.map(form => ({
        name: form.name,
        status: form.status,
        activeVersionStatus: form.active_version?.status
      })));

      // Extract unique departments from forms
      const departments = new Map<string, Department>();
      const studies = new Map<string, Study>();
      const categories = new Set<string>();

      formsData.results.forEach(form => {
        // Extract department from user object
        if (form.user && 'department_name' in form.user && form.user.department_name) {
          const deptUuid = (form.user as any).department_uuid || 'unknown';
          departments.set(deptUuid, {
            uuid: deptUuid,
            name: (form.user as any).department_name,
            hospital: {
              uuid: 'unknown',
              name: 'Unknown Hospital'
            }
          });
        }

        // Extract study from active_version
        if (form.active_version?.study_uuid && (form.active_version as any).study_name) {
          studies.set(form.active_version.study_uuid, {
            uuid: form.active_version.study_uuid,
            name: (form.active_version as any).study_name,
            description: form.active_version.description || ''
          });
        }

        // Extract categories from active_version
        if (form.active_version && (form.active_version as any).categories_names) {
          (form.active_version as any).categories_names.forEach((category: string) => {
            categories.add(category);
          });
        }
      });

      setAvailableDepartments(Array.from(departments.values()));
      setAvailableStudies(Array.from(studies.values()));
      setAvailableCategories(Array.from(categories.values()));
    }
  }, [formsData]);

  // Filter forms based on search and filters
  const filteredForms = formsData?.results?.filter(form => {
    const matchesSearch = form.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         ((form as any).form_description && (form as any).form_description.toLowerCase().includes(searchTerm.toLowerCase()));
    
    const formStatus = form.active_version?.status || form.status;
    const matchesStatus = statusFilter === 'all' || formStatus === statusFilter;
    
    const formCategories = (form.active_version as any)?.categories_names || [];
    const matchesCategory = categoryFilter === 'all' || 
                           formCategories.some((cat: string) => cat.toLowerCase().includes(categoryFilter.toLowerCase()));
    
    const matchesDepartment = departmentFilter === 'all' || 
                             ((form.user as any)?.department_uuid === departmentFilter);
    
    const matchesStudy = studyFilter === 'all' || 
                        (form.active_version?.study_uuid === studyFilter);
    
    return matchesSearch && matchesStatus && matchesCategory && matchesDepartment && matchesStudy;
  }) || [];

  const handleViewForm = (form: Form) => {
    // Navigate to form preview page
    navigate(`/form/preview/${form.uuid}`);
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  const clearAllFilters = () => {
    setSearchTerm('');
    setStatusFilter('all');
    setCategoryFilter('all');
    setDepartmentFilter('all');
    setStudyFilter('all');
  };

  const getActiveFiltersCount = () => {
    let count = 0;
    if (searchTerm) count++;
    if (statusFilter !== 'all') count++;
    if (categoryFilter !== 'all') count++;
    if (departmentFilter !== 'all') count++;
    if (studyFilter !== 'all') count++;
    return count;
  };

  if (isLoading) {
    return (
      <div className="sponsor-forms-container">
        <div className="loading-container">
          <div className="loading-spinner"></div>
          <p>Loading forms...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="sponsor-forms-container">
        <div className="error-container">
          <p>Error loading forms. Please try again later.</p>
        </div>
      </div>
    );
  }

  return (
    <motion.div
      className="sponsor-forms-container"
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
    >
      {/* Header Section */}
      <div className="forms-header">
        <div className="header-content">
          <div className="header-left">
            <h1>My Forms</h1>
            <p>Manage and view all forms connected to your organization</p>
          </div>
        </div>
        
        {/* Sponsor Info */}
        {formsData?.sponsor && (
          <div className="sponsor-info">
            <div className="sponsor-details">
              <h3>{formsData.sponsor.name}</h3>
              <p>{formsData.sponsor.email}</p>
            </div>
            <div className="forms-count">
              <span className="count-number">{formsData.count}</span>
              <span className="count-label">Total Forms</span>
            </div>
          </div>
        )}
      </div>

      {/* Filters and Search */}
      <div className="forms-controls">
        <div className="search-section">
          <div className="search-input-wrapper">
            <Search size={16} className="search-icon" />
            <input
              type="text"
              placeholder="Search forms by name or description..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="search-input"
            />
          </div>
        </div>
        
        <div className="filters-section">
          <div className="filter-group">
            <label>Status:</label>
            <select 
              value={statusFilter} 
              onChange={(e) => setStatusFilter(e.target.value)}
              className="filter-select"
            >
              <option value="all">All Status</option>
              <option value="pending">Pending</option>
              <option value="accepted">Accepted</option>
              <option value="rejected">Rejected</option>
            </select>
          </div>
          
          <div className="filter-group">
            <label>Category:</label>
            <select 
              value={categoryFilter} 
              onChange={(e) => setCategoryFilter(e.target.value)}
              className="filter-select"
            >
              <option value="all">All Categories</option>
              {availableCategories.map(category => (
                <option key={category} value={category}>
                  {category}
                </option>
              ))}
            </select>
          </div>

          <div className="filter-group">
            <label>
              <Building size={14} />
              Department:
            </label>
            <select 
              value={departmentFilter} 
              onChange={(e) => setDepartmentFilter(e.target.value)}
              className="filter-select"
            >
              <option value="all">All Departments</option>
              {availableDepartments.map(dept => (
                <option key={dept.uuid} value={dept.uuid}>
                  {dept.name}
                </option>
              ))}
            </select>
          </div>

          <div className="filter-group">
            <label>
              <BookOpen size={14} />
              Study:
            </label>
            <select 
              value={studyFilter} 
              onChange={(e) => setStudyFilter(e.target.value)}
              className="filter-select"
            >
              <option value="all">All Studies</option>
              {availableStudies.map(study => (
                <option key={study.uuid} value={study.uuid}>
                  {study.name}
                </option>
              ))}
            </select>
          </div>

          {getActiveFiltersCount() > 0 && (
            <div className="filter-actions">
              <button 
                onClick={clearAllFilters}
                className="clear-filters-btn"
              >
                <Filter size={14} />
                Clear Filters ({getActiveFiltersCount()})
              </button>
            </div>
          )}
        </div>
      </div>

      {/* Forms Grid */}
      <div className="forms-content">
        {filteredForms.length === 0 ? (
          <div className="no-forms">
            <FileText size={48} className="no-forms-icon" />
            <h3>No forms found</h3>
            <p>
              {searchTerm || statusFilter !== 'all' || categoryFilter !== 'all' || 
               departmentFilter !== 'all' || studyFilter !== 'all'
                ? 'Try adjusting your search or filters'
                : 'No forms available at the moment'
              }
            </p>
          </div>
        ) : (
          <div className="forms-grid">
            {filteredForms.map((form) => (
              <motion.div
                key={form.uuid}
                className="form-card"
                initial={{ opacity: 0, scale: 0.9 }}
                animate={{ opacity: 1, scale: 1 }}
                transition={{ duration: 0.3 }}
                whileHover={{ y: -5, boxShadow: '0 8px 25px rgba(0,0,0,0.15)' }}
              >
                <div className="form-card-header">
                  <div className="form-status">
                    <span className={`status-badge ${(form.active_version?.status || form.status || 'unknown').toLowerCase()}`}>
                      {form.active_version?.status || form.status || 'Unknown'}
                    </span>
                  </div>
                  <div className="form-actions">
                    <button 
                      className="action-btn view-btn"
                      onClick={() => handleViewForm(form)}
                      title="View Form"
                    >
                      <Eye size={16} />
                    </button>
                  </div>
                </div>
                
                <div className="form-card-content">
                  <h3 className="form-title">{form.name}</h3>
                  <p className="form-description">
                    {(form as any).form_description || form.description || 'No description available'}
                  </p>
                  
                  <div className="form-meta">
                    <div className="meta-item">
                      <Calendar size={12} />
                      <span>Created: {formatDate(form.created_at)}</span>
                    </div>
                    {form.updated_at !== form.created_at && (
                      <div className="meta-item">
                        <Calendar size={12} />
                        <span>Updated: {formatDate(form.updated_at)}</span>
                      </div>
                    )}
                    {(form.user as any)?.department_name && (
                      <div className="meta-item">
                        <Building size={12} />
                        <span>Department: {(form.user as any).department_name}</span>
                      </div>
                    )}
                    {(form.active_version as any)?.study_name && (
                      <div className="meta-item">
                        <BookOpen size={12} />
                        <span>Study: {(form.active_version as any).study_name}</span>
                      </div>
                    )}
                  </div>
                  
                  {(form.active_version as any)?.categories_names && (form.active_version as any).categories_names.length > 0 && (
                    <div className="form-categories">
                      {(form.active_version as any).categories_names.map((category: string, index: number) => (
                        <span key={index} className="category-tag">
                          <Tag size={10} />
                          {category}
                        </span>
                      ))}
                    </div>
                  )}
                </div>
              </motion.div>
            ))}
          </div>
        )}
      </div>
    </motion.div>
  );
};

export default SponsorForms;

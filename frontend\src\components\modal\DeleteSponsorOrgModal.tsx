import React from 'react';
import "@/components/modal/DeleteSponsorOrgModal.css"; // Updated CSS import
import { useNavigate } from "react-router-dom";

type DeleteSponsorOrgModalProps = { // Renamed Props type
  isOpen: boolean;
  onDelete: () => void;
  onClose: () => void;
};

const DeleteSponsorOrgModal: React.FC<DeleteSponsorOrgModalProps> = ({ isOpen, onClose, onDelete }) => { // Renamed component
  const navigate = useNavigate();
  const handleDelete = () => {
    // Delete logic is called via onDelete prop
    navigate("/org/dashboard/sponsor-orgs", { replace: true }); // Updated navigation path
    onDelete();
    onClose();
  };

  if (!isOpen) return null;

  return (
    <div className="modal-overlay">
      <div className="modal-content">
        <div className="modal-header">
          <h2>Confirm Sponsor Org Deletion</h2> {/* Updated text */}
        </div>
        <div className="modal-body text-sm-start">
          <p>Are you sure you want to <strong>delete this Sponsor Org</strong>?</p> {/* Updated text */}
          <p className="m-y-5">This action is permanent and cannot be undone. All associated data will be lost.</p>
        </div>
        <div className="modal-footer">
          <button className="delete-modal-btn btn-secondary" onClick={onClose}>Cancel</button>
          <button className="delete-modal-btn btn-danger" onClick={handleDelete}>Yes</button>
        </div>
      </div>
    </div>
  );
};

export default DeleteSponsorOrgModal; // Renamed export
import React from 'react';
import { FileText } from 'lucide-react';
import './DashboardFormCard.css';

interface DashboardFormCardProps {
  title: string;
  date?: string;
  createdBy?: string;
  lastUpdatedAt?: string;
  lastUpdatedBy?: string;
  status?: string;
  submissionCount?: number;
}
      
const DashboardFormCard: React.FC<DashboardFormCardProps> = ({ 
  title, 
  date, 
  createdBy, 
  lastUpdatedAt, 
  lastUpdatedBy,
  status,
  submissionCount
}) => {
  

  return (
    <div className="outerCard">
      <div className="innerCard">
        <div className="cardMainContent">
          <div className="formTitleSection">
            <FileText size={20} className="formIcon" />
            <span className="formTitle">{title}</span>
            {status && (
              <span className="formStatus" style={{
                display: 'inline-block',
                backgroundColor: 
                  status === 'Completed' ? 'rgba(0, 200, 83, 0.15)' : 
                  status === 'Draft' ? 'rgba(255, 152, 0, 0.15)' : 
                  status === 'Submitted' ? 'rgba(33, 150, 243, 0.15)' : 
                  'rgba(158, 158, 158, 0.15)',
                color: 
                  status === 'Completed' ? '#00913c' : 
                  status === 'Draft' ? '#e65100' : 
                  status === 'Submitted' ? '#0069c0' : 
                  '#424242',
                padding: '4px 8px',
                borderRadius: '4px',
                fontSize: '0.8rem',
                fontWeight: '600',
                marginLeft: '10px'
              }}>
                {status}
              </span>
            )}
            {submissionCount !== undefined && submissionCount > 0 && (
              <span className="formSubmissionCount" style={{
                display: 'inline-block',
                backgroundColor: 'rgba(55, 183, 195, 0.15)',
                color: '#37B7C3',
                padding: '4px 8px',
                borderRadius: '4px',
                fontSize: '0.8rem',
                fontWeight: '600',
                marginLeft: '10px'
              }}>
                {submissionCount} {submissionCount === 1 ? 'Submission' : 'Submissions'}
              </span>
            )}
            
            
          </div>
          
          <div className="formMetadata">
            {createdBy && (
              <div className="metadataItem">
                <span className="metadataLabel">Created By:</span>
                <span className="metadataValue">{createdBy}</span>
              </div>
            )}
            {date && (
              <div className="metadataItem">
                <span className="metadataLabel">Date:</span>
                <span className="metadataValue">{date}</span>
              </div>
            )}
            {lastUpdatedAt && (
              <div className="metadataItem">
                <span className="metadataLabel">Last Updated:</span>
                <span className="metadataValue">{lastUpdatedAt}</span>
              </div>
            )}
            {lastUpdatedBy && (
              <div className="metadataItem">
                <span className="metadataLabel">Updated By:</span>
                <span className="metadataValue">{lastUpdatedBy}</span>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default DashboardFormCard;

import { useQuery } from "@tanstack/react-query";
import { TaskService } from "@/services/api/task.service";

export const TASK_KEYS = {
  all: ['tasks'] as const,
  byStudy: (studyUuid: string) => [...TASK_KEYS.all, 'study', studyUuid] as const,
};

export const useTasksByStudyQuery = (studyUuid: string) => {
  return useQuery({
    queryKey: TASK_KEYS.byStudy(studyUuid),
    queryFn: () => TaskService.getTasksByStudy(studyUuid),
    enabled: !!studyUuid,
  });
};

export const useAllTasksQuery = (studyUuid?: string) => {
  return useQuery({
    queryKey: [...TASK_KEYS.all, studyUuid],
    queryFn: () => TaskService.getAllTasks(studyUuid),
    enabled: true,
  });
}; 
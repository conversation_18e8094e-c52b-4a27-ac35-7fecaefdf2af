import api from "@/services/api";

// Document interface based on API response
export interface UserDocument {
  uuid: string;
  document_type: string;
  document_name: string | null;
  file_url: string;
  display_name: string;
  file_size: number;
  expiry_date: string | null;
  is_expired: boolean;
  days_until_expiry: number | null;
  uploaded_at: string;
  updated_at: string;
}

// Document upload payload interface
export interface DocumentUploadPayload {
  file: File;
  document_type: string;
  document_name?: string;
  expiry_date?: string;
}

// Document update payload interface
export interface DocumentUpdatePayload {
  file?: File;
  document_type?: string;
  document_name?: string;
  expiry_date?: string;
}

// Get all user documents
export const getUserDocuments = async (): Promise<UserDocument[]> => {
  try {
    console.log("Fetching user documents from API...");
    const response = await api.get("/user/documents/");
    console.log("API response:", response);
    console.log("Documents data:", response.data);
    return response.data;
  } catch (error) {
    console.error("Error fetching user documents:", error);
    throw error;
  }
};

// Upload new document
export const uploadDocument = async (payload: DocumentUploadPayload): Promise<UserDocument> => {
  try {
    const formData = new FormData();
    formData.append("file", payload.file);
    formData.append("document_type", payload.document_type);
    
    if (payload.document_name) {
      formData.append("document_name", payload.document_name);
    }
    
    if (payload.expiry_date) {
      formData.append("expiry_date", payload.expiry_date);
    }

    const response = await api.post("/user/documents/", formData, {
      headers: {
        "Content-Type": "multipart/form-data",
      },
    });
    return response.data;
  } catch (error) {
    console.error("Error uploading document:", error);
    throw error;
  }
};

// Get document details
export const getDocumentDetails = async (uuid: string): Promise<UserDocument> => {
  try {
    const response = await api.get(`/user/documents/${uuid}/`);
    return response.data;
  } catch (error) {
    console.error("Error fetching document details:", error);
    throw error;
  }
};

// Update document
export const updateDocument = async (uuid: string, payload: DocumentUpdatePayload): Promise<UserDocument> => {
  try {
    const formData = new FormData();
    
    if (payload.file) {
      formData.append("file", payload.file);
    }
    if (payload.document_type) {
      formData.append("document_type", payload.document_type);
    }
    if (payload.document_name) {
      formData.append("document_name", payload.document_name);
    }
    if (payload.expiry_date) {
      formData.append("expiry_date", payload.expiry_date);
    }

    const response = await api.put(`/user/documents/${uuid}/`, formData, {
      headers: {
        "Content-Type": "multipart/form-data",
      },
    });
    return response.data;
  } catch (error) {
    console.error("Error updating document:", error);
    throw error;
  }
};

// Delete document
export const deleteDocument = async (uuid: string): Promise<void> => {
  try {
    await api.delete(`/user/documents/${uuid}/`);
  } catch (error) {
    console.error("Error deleting document:", error);
    throw error;
  }
};

// Download document
export const downloadDocument = async (uuid: string): Promise<Blob> => {
  try {
    const response = await api.get(`/user/documents/${uuid}/download/`, {
      responseType: "blob",
    });
    return response.data;
  } catch (error) {
    console.error("Error downloading document:", error);
    throw error;
  }
};

// Get available document types
export const getDocumentTypes = async (): Promise<string[]> => {
  try {
    const response = await api.get("/user/documents/document-types/");
    return response.data;
  } catch (error) {
    console.error("Error fetching document types:", error);
    throw error;
  }
};

// Get expired documents
export const getExpiredDocuments = async (): Promise<UserDocument[]> => {
  try {
    const response = await api.get("/user/documents/expired/");
    return response.data;
  } catch (error) {
    console.error("Error fetching expired documents:", error);
    throw error;
  }
};

// Get documents expiring soon (within 30 days)
export const getDocumentsExpiringSoon = async (): Promise<UserDocument[]> => {
  try {
    const response = await api.get("/user/documents/expiring-soon/");
    return response.data;
  } catch (error) {
    console.error("Error fetching documents expiring soon:", error);
    throw error;
  }
};

// Get expiry summary
export const getExpirySummary = async (): Promise<{
  total_documents: number;
  expired_documents: number;
  expiring_soon: number;
  no_expiry_date: number;
  valid_documents: number;
}> => {
  try {
    const response = await api.get("/user/documents/expiry-summary/");
    return response.data;
  } catch (error) {
    console.error("Error fetching expiry summary:", error);
    throw error;
  }
}; 
import React, { useState, useEffect, useMemo } from "react";
import { useCurrentUserQuery } from "@/hooks/user.query";
import { useDoctorReferralsQuery, usePatientApplicationsQuery } from "@/hooks/contact.query";
import "./styles.css";
import { Eye } from "lucide-react";
import DataTable, { Column, Action, DataTableSearch } from "@/components/common/DataTable";
import RequestDetail from "./RequestDetail";

// Types
interface FormData {
  // Common fields
  name?: string;
  email?: string;
  phone?: string;
  age?: string;
  
  // Patient specific fields
  postcode?: string;
  hospital?: string;
  location?: string;
  departments?: string[];
  availableTrials?: string;
  medicalHistory?: string;
  medications?: string;
  familyHistory?: string;
  
  // Doctor specific fields
  doctorName?: string;
  nhsEmail?: string;
  practice?: string;
  patientInitials?: string;
  condition?: string;
  nhsNumber?: string;
  notes?: string;
}

interface CurrentUser {
  first_name?: string;
  last_name?: string;
  is_superuser?: boolean;
  is_admin?: boolean;
  department?: {
    name: string;
  };
}

interface PendingRequest {
  id: string;
  uuid: string;
  name: string;
  age: string;
  nhsId: string;
  source: string;
  study: string;
  trials: string;
  date: string;
  status: "Pending" | "Approved" | "Rejected" | "In Progress";
  department: string;
  formData: FormData;
}

const PendingRequests: React.FC = () => {
  const { data: currentUser, isLoading: isUserLoading } = useCurrentUserQuery();
  const { data: doctorReferrals, isLoading: isReferralsLoading, refetch: refetchDoctorReferrals } = useDoctorReferralsQuery();
  const { data: patientApplications, isLoading: isApplicationsLoading, refetch: refetchPatientApplications } = usePatientApplicationsQuery();
  
  const [selectedRequest, setSelectedRequest] = useState<PendingRequest | null>(null);
  const [requests, setRequests] = useState<PendingRequest[]>([]);
  const [filteredRequests, setFilteredRequests] = useState<PendingRequest[]>([]);
  const [selectedStatuses, setSelectedStatuses] = useState<string[]>([]);
  const [selectedDepartments, setSelectedDepartments] = useState<string[]>([]);
  const [selectedSources, setSelectedSources] = useState<string[]>([]);

  // Function to refetch all data
  const refetchAllData = async () => {
    try {
      const [doctorReferralsResult, patientApplicationsResult] = await Promise.all([
        refetchDoctorReferrals(),
        refetchPatientApplications()
      ]);
      
      // Transform the new data
      const transformedRequests = transformRequests(
        doctorReferralsResult.data?.results || [],
        patientApplicationsResult.data?.results || []
      );
      
      // Update the state with new data
      setRequests(transformedRequests);
      setFilteredRequests(transformedRequests);
    } catch (error) {
      console.error('Error refetching data:', error);
    }
  };

  // Function to transform requests
  const transformRequests = (doctorReferrals: any[], patientApplications: any[]) => {
    const calculateAge = (dateOfBirth: string) => {
      const dob = new Date(dateOfBirth);
      const today = new Date();
      let age = today.getFullYear() - dob.getFullYear();
      const monthDiff = today.getMonth() - dob.getMonth();
      
      if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < dob.getDate())) {
        age--;
      }
      
      return String(age);
    };

    const formatStatus = (status: string) => {
      // Replace underscores with spaces and capitalize each word
      return status
        .split('_')
        .map(word => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase())
        .join(' ');
    };

    return [
      ...doctorReferrals.map(referral => ({
        id: referral.uuid,
        uuid: referral.uuid,
        name: `${referral.patient_first_name} ${referral.patient_last_name}`,
        age: calculateAge(referral.patient_dob),
        nhsId: referral.nhs_number,
        source: "Doctor",
        study: referral.trial.name,
        trials: referral.trial.name,
        date: new Date(referral.created_at).toLocaleDateString(),
        status: formatStatus(referral.status) as "Pending" | "Approved" | "Rejected" | "In Progress",
        department: referral.trial.hospital.name,
        formData: {
          doctorName: referral.doctor_name,
          nhsEmail: referral.nhs_email,
          practice: referral.organization_name,
          patientInitials: `${referral.patient_first_name} ${referral.patient_last_name}`,
          condition: referral.trial.name,
          nhsNumber: referral.nhs_number,
          age: calculateAge(referral.patient_dob),
          email: referral.email,
          phone: referral.phone,
          notes: referral.reason_for_referral,
          medicalHistory: referral.medical_history,
          medications: referral.current_medications,
          patient_dob: referral.patient_dob
        }
      })),
      ...patientApplications.map(application => {
        const age = calculateAge(application.date_of_birth);
        return {
          id: application.uuid,
          uuid: application.uuid,
          name: `${application.first_name} ${application.last_name}`,
          age: age,
          nhsId: application.nhs_number || application.uuid,
          source: "Patient",
          study: application.trial.name,
          trials: application.trial.name,
          date: new Date(application.created_at).toLocaleDateString(),
          status: formatStatus(application.status) as "Pending" | "Approved" | "Rejected" | "In Progress",
          department: application.hospital.name,
          formData: {
            name: `${application.first_name} ${application.last_name}`,
            email: application.email,
            phone: application.phone_number,
            age: age,
            postcode: application.postcode,
            hospital: application.hospital.name,
            medicalHistory: application.medical_history,
            medications: application.current_medications,
            familyHistory: application.allergies,
            date_of_birth: application.date_of_birth,
            nhsNumber: application.nhs_number,
            gender: application.gender,
            ethnicity: application.ethnicity,
            smokingStatus: application.smoking_status,
            alcoholConsumption: application.alcohol_consumption,
            physicalActivityLevel: application.physical_activity_level,
            pregnancyStatus: application.pregnancy_status,
            willingToTravel: application.willing_to_travel,
            dataSharingConsent: application.data_sharing_consent
          }
        };
      })
    ];
  };

  // Transform API data into PendingRequest format
  useEffect(() => {
    if (!doctorReferrals?.results && !patientApplications?.results) {
      return;
    }

    const transformedRequests = transformRequests(
      doctorReferrals?.results || [],
      patientApplications?.results || []
    );

    setRequests(transformedRequests);
    setFilteredRequests(transformedRequests);
  }, [doctorReferrals, patientApplications]);

  // Filter requests based on filters
  const filteredByAll = useMemo(() => {
    if (!requests.length) return [];
    
    let result = [...requests];
    
    // Only apply filters if any are selected
    if (selectedStatuses.length > 0 || selectedDepartments.length > 0 || selectedSources.length > 0) {
      if (selectedStatuses.length > 0) {
        result = result.filter(request => 
          selectedStatuses.some(status => status === request.status)
        );
      }
      
      if (selectedDepartments.length > 0) {
        result = result.filter(request => 
          selectedDepartments.includes(request.department)
        );
      }
      
      if (selectedSources.length > 0) {
        result = result.filter(request => 
          selectedSources.includes(request.source)
        );
      }
    }
    
    return result;
  }, [requests, selectedStatuses, selectedDepartments, selectedSources]);

  // Update filtered requests when filters change
  useEffect(() => {
    setFilteredRequests(filteredByAll);
  }, [filteredByAll]);

  const handleCategoryChange = (categoryId: string) => {
    setSelectedStatuses((prev) =>
      prev.includes(categoryId)
        ? prev.filter((id) => id !== categoryId)
        : [...prev, categoryId]
    );
  };

  const handleDepartmentFilterChange = (departmentId: string) => {
    setSelectedDepartments((prev) =>
      prev.includes(departmentId)
        ? prev.filter((id) => id !== departmentId)
        : [...prev, departmentId]
    );
  };

  const handleSourceFilterChange = (source: string) => {
    setSelectedSources((prev) =>
      prev.includes(source)
        ? prev.filter((s) => s !== source)
        : [...prev, source]
    );
  };
  
  const handleDetailView = (request: PendingRequest) => {
    setSelectedRequest(request);
  };

  const handleBackToList = async () => {
    setSelectedRequest(null);
    setSelectedStatuses([]);
    setSelectedDepartments([]);
    setSelectedSources([]);
    
    // Refetch data and reset filters
    await refetchAllData();
  };

  const handleUpdateStatus = async (id: string, newStatus: "Pending" | "Approved" | "Rejected" | "In Progress", comment: string) => {
    try {
      // In a real app, this would make an API call to update the status
      const updatedRequests = requests.map(request => {
        if (request.id === id) {
          return {
            ...request,
            status: newStatus
          };
        }
        return request;
      });
      
      // Update both the full requests list and the filtered list
      setRequests(updatedRequests);
      setFilteredRequests(updatedRequests);
      
      // Update selected request if it's the one being modified
      if (selectedRequest && selectedRequest.id === id) {
        setSelectedRequest({
          ...selectedRequest,
          status: newStatus
        });
      }
      
      // Here we would also log the status change and comment
      console.log(`Request ${id} status changed to ${newStatus}. Comment: ${comment}`);

      // Refetch the data to ensure we have the latest state
      await refetchAllData();
    } catch (error) {
      console.error('Error updating status:', error);
    }
  };

  const handleAssignUsers = async (id: string, userIds: string[]) => {
    try {
      // In a real app, this would make an API call to assign users
      console.log(`Request ${id} assigned to users: ${userIds.join(', ')}`);
      
      // Refetch the data to ensure we have the latest state
      await refetchAllData();
    } catch (error) {
      console.error('Error assigning users:', error);
      // You might want to show an error message to the user here
    }
  };

  // Source categories for filtering
  const sourceCategories = useMemo(() => [
    { id: "Doctor", label: "Doctor", count: requests.filter(r => r.source === "Doctor").length },
    { id: "Patient", label: "Patient", count: requests.filter(r => r.source === "Patient").length }
  ], [requests]);
  
  // Status categories for filtering
  const statusCategories = useMemo(() => [
    { id: "Pending", label: "Pending", count: requests.filter(r => r.status === "Pending").length },
    { id: "Approved", label: "Approved", count: requests.filter(r => r.status === "Approved").length },
    { id: "Rejected", label: "Rejected", count: requests.filter(r => r.status === "Rejected").length },
    { id: "In Progress", label: "In Progress", count: requests.filter(r => r.status === "In Progress").length }
  ], [requests]);

  // Department categories for filtering
  const departmentCategories = useMemo(() => [
    { id: "Cardiology", label: "Cardiology", count: requests.filter(r => r.department === "Cardiology").length },
    { id: "Neurology", label: "Neurology", count: requests.filter(r => r.department === "Neurology").length },
    { id: "Oncology", label: "Oncology", count: requests.filter(r => r.department === "Oncology").length },
    { id: "Orthopedics", label: "Orthopedics", count: requests.filter(r => r.department === "Orthopedics").length }
  ], [requests]);

  // Define table columns
  const columns: Column<PendingRequest>[] = [
    {
      key: 'name',
      header: "Name",
      sortable: true
    },
    {
      key: 'age',
      header: 'Age',
      sortable: true
    },
    {
      key: 'nhsId',
      header: 'NHS ID',
      sortable: true
    },
    {
      key: 'source',
      header: 'Source',
      sortable: true,
      render: (value: unknown) => {
        const sourceValue = typeof value === 'string' ? value : '';
        return (
          <span 
            style={{
              backgroundColor: sourceValue === 'Doctor' ? '#dbeafe' : '#3F72AF',
              color: sourceValue === 'Doctor' ? '#2563eb' : '#F9F7F7',
              padding: '4px 8px',
              borderRadius: '4px',
              fontSize: '12px',
              fontWeight: '500'
            }}
          >
            {sourceValue}
          </span>
        );
      }
    },
    {
      key: 'study',
      header: 'Study',
      sortable: true
    },
    {
      key: 'department',
      header: 'Hospital',
      sortable: true
    },
    {
      key: 'date',
      header: 'Date',
      sortable: true
    },
    {
      key: 'status',
      header: 'Status',
      sortable: true,
      render: (value: unknown) => {
        const statusValue = typeof value === 'string' ? value : '';
        let color;
        let bgColor;
        
        switch(statusValue) {
          case 'Pending':
            color = '#D97706';
            bgColor = '#FEF3C7';
            break;
          case 'Approved':
            color = '#059669';
            bgColor = '#D1FAE5';
            break;
          case 'Rejected':
            color = '#DC2626';
            bgColor = '#FEE2E2';
            break;
          case 'In Progress':
            color = '#2563EB';
            bgColor = '#DBEAFE';
            break;
          default:
            color = '#6B7280';
            bgColor = '#F3F4F6';
        }
        
        return (
          <span style={{
            backgroundColor: bgColor,
            color: color,
            padding: '5px 10px',
            borderRadius: '15px',
            fontWeight: '500',
            fontSize: '0.875rem'
          }}>
            {statusValue}
          </span>
        );
      }
    }
  ];

  // Define table actions
  const tableActions: Action<PendingRequest>[] = [
    {
      icon: <Eye size={18} />,
      tooltipText: "View request details",
      onClick: (request) => handleDetailView(request)
    }
  ];

  if (isUserLoading || isReferralsLoading || isApplicationsLoading) {
    return <div className="loading">Loading requests...</div>;
  }

  // Add error handling for when data is not available
  if (!doctorReferrals?.results && !patientApplications?.results) {
    return <div className="error">Failed to load requests. Please try again later.</div>;
  }

  return (
    <div>
      {!selectedRequest ? (
        <div className="patient-details-container">
          <div className="patient-details-header">
            <h1 className="page-title">Pending Requests</h1>
          </div>

          <div style={{
            display: 'flex',
            justifyContent: 'space-between',
            alignItems: 'center',
            marginBottom: '20px',
          }}>
            <h3 style={{ margin: 0, fontSize: '18px', fontWeight: 600 }}>
              Manage and respond to patient and doctor requests.
            </h3>
            <div style={{ width: '300px' }}>
              <DataTableSearch
                data={filteredRequests}
                onFilter={setFilteredRequests}
                placeholder="Search requests..."
              />
            </div>
          </div>

          <div className="filter-section mb-4">
            <div className="row">
              <div className="col-md-3">
                <div className="filter-group">
                  <h6 className="sidebar__subtitle mb-2">Source</h6>
                  <div className="d-flex flex-wrap gap-2">
                    {sourceCategories.map((category) => (
                      <button
                        key={category.id}
                        className={`filter-tag ${selectedSources.includes(category.id) ? 'active' : ''}`}
                        onClick={() => handleSourceFilterChange(category.id)}
                        style={{
                          padding: '6px 12px',
                          borderRadius: '20px',
                          border: '1px solid #e5e7eb',
                          background: selectedSources.includes(category.id) ? '#37B7C3' : '#fff',
                          color: selectedSources.includes(category.id) ? '#fff' : '#6b7280',
                          fontSize: '12px',
                          fontWeight: '500',
                          cursor: 'pointer',
                          marginBottom: '8px',
                          display: 'flex',
                          alignItems: 'center',
                          gap: '6px'
                        }}
                      >
                        {category.label}
                        <span style={{
                          background: selectedSources.includes(category.id) ? 'rgba(255,255,255,0.2)' : '#f3f4f6',
                          borderRadius: '10px',
                          padding: '2px 6px',
                          fontSize: '11px'
                        }}>
                          {category.count}
                        </span>
                      </button>
                    ))}
                  </div>
                </div>
              </div>
              <div className="col-md-3">
                <div className="filter-group">
                  <h6 className="sidebar__subtitle mb-2">Status</h6>
                  <div className="d-flex flex-wrap gap-2">
                    {statusCategories.map((category) => (
                      <button
                        key={category.id}
                        className={`filter-tag ${selectedStatuses.includes(category.id) ? 'active' : ''}`}
                        onClick={() => handleCategoryChange(category.id)}
                        style={{
                          padding: '6px 12px',
                          borderRadius: '20px',
                          border: '1px solid #e5e7eb',
                          background: selectedStatuses.includes(category.id) ? '#37B7C3' : '#fff',
                          color: selectedStatuses.includes(category.id) ? '#fff' : '#6b7280',
                          fontSize: '12px',
                          fontWeight: '500',
                          cursor: 'pointer',
                          marginBottom: '8px',
                          display: 'flex',
                          alignItems: 'center',
                          gap: '6px'
                        }}
                      >
                        {category.label}
                        <span style={{
                          background: selectedStatuses.includes(category.id) ? 'rgba(255,255,255,0.2)' : '#f3f4f6',
                          borderRadius: '10px',
                          padding: '2px 6px',
                          fontSize: '11px'
                        }}>
                          {category.count}
                        </span>
                      </button>
                    ))}
                  </div>
                </div>
              </div>
              <div className="col-md-3">
                <div className="filter-group">
                  <h6 className="sidebar__subtitle mb-2">Departments</h6>
                  <div className="d-flex flex-wrap gap-2">
                    {departmentCategories.map((category) => (
                      <button
                        key={category.id}
                        className={`filter-tag ${selectedDepartments.includes(category.id) ? 'active' : ''}`}
                        onClick={() => handleDepartmentFilterChange(category.id)}
                        style={{
                          padding: '6px 12px',
                          borderRadius: '20px',
                          border: '1px solid #e5e7eb',
                          background: selectedDepartments.includes(category.id) ? '#37B7C3' : '#fff',
                          color: selectedDepartments.includes(category.id) ? '#fff' : '#6b7280',
                          fontSize: '12px',
                          fontWeight: '500',
                          cursor: 'pointer',
                          marginBottom: '8px',
                          display: 'flex',
                          alignItems: 'center',
                          gap: '6px'
                        }}
                      >
                        {category.label}
                        <span style={{
                          background: selectedDepartments.includes(category.id) ? 'rgba(255,255,255,0.2)' : '#f3f4f6',
                          borderRadius: '10px',
                          padding: '2px 6px',
                          fontSize: '11px'
                        }}>
                          {category.count}
                        </span>
                      </button>
                    ))}
                  </div>
                </div>
              </div>
              <div className="col-md-3">
                <div className="filter-group">
                  <h6 className="sidebar__subtitle mb-2">Tags</h6>
                  <div className="d-flex flex-wrap gap-2">
                    {['New', 'Featured', 'Popular', 'Trending'].map((tag) => (
                      <span 
                        key={tag} 
                        className="tag-item"
                        style={{
                          padding: '6px 12px',
                          backgroundColor: '#f3f4f6',
                          borderRadius: '20px',
                          fontSize: '12px',
                          color: '#4b5563',
                          fontWeight: '500',
                          cursor: 'pointer',
                          transition: 'all 0.2s ease'
                        }}
                      >
                        {tag}
                      </span>
                    ))}
                  </div>
                </div>
              </div>
            </div>
          </div>

          <DataTable<PendingRequest>
            data={filteredRequests}
            columns={columns}
            actions={tableActions}
            noDataMessage="No pending requests available"
            defaultItemsPerPage={10}
          />
        </div>
      ) : (
        <RequestDetail 
          request={selectedRequest}
          onBack={handleBackToList}
          onUpdateStatus={handleUpdateStatus}
          onAssignUsers={handleAssignUsers}
          currentUser={currentUser as CurrentUser}
        />
      )}
    </div>
  );
};

export default PendingRequests;

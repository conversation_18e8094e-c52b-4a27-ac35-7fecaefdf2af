import React from "react";
import { motion } from "framer-motion";

const ActivitiesSection: React.FC = () => {
  return (
    <motion.div
      className="patclin-tab-content"
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      transition={{ duration: 0.5 }}
    >
      <h2 className="patclin-section-title">Recommended Activities</h2>
      <div className="patclin-empty-state">
        <p>You have no recommended activities at this time.</p>
      </div>
    </motion.div>
  );
};

export default ActivitiesSection;

import React, { useState, useEffect } from "react";
import { X } from "lucide-react";
import { type CreateSymptomData } from "@/services/api/symptom.service";
import { useCreateSymptomMutation, useMarkSymptomAsReviewedMutation } from "@/hooks/symptom.query";
import { useCurrentUserQuery } from "@/hooks/user.query";

interface AddSymptomModalProps {
  isOpen: boolean;
  onClose: () => void;
  patientUuid: string;
  onSuccess: () => void;
}

const AddSymptomModal: React.FC<AddSymptomModalProps> = ({
  isOpen,
  onClose,
  patientUuid,
  onSuccess,
}) => {
  console.log('Modal received patientUuid:', patientUuid); // Debug log

  const { data: currentUser } = useCurrentUserQuery();
  const isPatient = currentUser?.user_type === "patient";
  const markAsReviewedMutation = useMarkSymptomAsReviewedMutation();

  const [formData, setFormData] = useState<CreateSymptomData>({
    patient_uuid: patientUuid,
    name: "",
    description: "",
    start_date: new Date().toISOString().split("T")[0],
    start_time: new Date().toTimeString().slice(0, 5),
    severity: "mild",
    hospitalization_required: false,
    status: "ongoing_treatment",
    relatedness: "unrelated",
    category: "nan",
    patient_comment: "",
    resolved_date: "",
  });

  const [reviewComment, setReviewComment] = useState<string>("");

  const resetForm = () => {
    setFormData({
      patient_uuid: patientUuid,
      name: "",
      description: "",
      start_date: new Date().toISOString().split("T")[0],
      start_time: new Date().toTimeString().slice(0, 5),
      severity: "mild",
      hospitalization_required: false,
      status: "ongoing_treatment",
      relatedness: "unrelated",
      category: "nan",
      patient_comment: "",
    });
    setReviewComment("");
  };

  // Update formData when patientUuid changes
  useEffect(() => {
    setFormData(prev => ({
      ...prev,
      patient_uuid: patientUuid
    }));
  }, [patientUuid]);

  const { mutate: createSymptom, isPending, error } = useCreateSymptomMutation(patientUuid);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    const dataToSubmit = {
      ...formData,
      patient_uuid: patientUuid,
      // Set default values for patient users
      ...(isPatient && {
        category: "nan",
        patient_comment: "",
        severity: "mild", // Default severity for patients
        relatedness: "unrelated" // Default relatedness for patients
      }),
      // Include review comment in patient_comment if provided
      ...(reviewComment && reviewComment.trim() !== "" && { patient_comment: reviewComment }),
      // Only include resolved_date if status is "resolved"
      ...(formData.status === "resolved" ? { resolved_date: formData.resolved_date } : { resolved_date: undefined })
    };

    console.log('Submitting symptom data:', dataToSubmit); // Debug log

    try {
      // Use the mutation with proper error handling
      createSymptom(dataToSubmit, {
        onSuccess: async (createdSymptom) => {
          // If a review comment was added and the user is not a patient, mark as reviewed
          if (!isPatient && reviewComment && reviewComment.trim() !== "") {
            try {
              await markAsReviewedMutation.mutateAsync(createdSymptom.uuid);
              console.log('Symptom marked as reviewed successfully');
            } catch (error) {
              console.error('Error marking symptom as reviewed:', error);
            }
          }

          resetForm(); // Reset form after successful submission
          onSuccess();
        },
        onError: (error) => {
          console.error('Error creating symptom:', error);
        }
      });
    } catch (error) {
      console.error('Error creating symptom:', error);
    }
  };

  const handleChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>
  ) => {
    const { name, value, type } = e.target;
    setFormData((prev) => ({
      ...prev,
      [name]: type === "checkbox" ? (e.target as HTMLInputElement).checked : value,
    }));
  };

  if (!isOpen) return null;

  return (
    <div className="modal-overlay">
      <div className="modal-content">
        <div className="modal-header">
          <h3>Add New Symptom</h3>
          <button className="close-button" onClick={onClose}>
            <X size={20} />
          </button>
        </div>

        <form onSubmit={handleSubmit}>
          <div className="row g-3">
            <div className="col-md-6">
              <div className="form-group">
                <label htmlFor="name">Symptom Name</label>
                <input
                  type="text"
                  className="form-control"
                  id="name"
                  name="name"
                  value={formData.name}
                  onChange={handleChange}
                  required
                />
              </div>
            </div>
            <div className="col-md-6">
              <div className="form-group">
                <label htmlFor="status">Status</label>
                <select
                  id="status"
                  name="status"
                  value={formData.status}
                  onChange={handleChange}
                  required
                  className="form-control"
                >
                  <option value="resolved">Resolved</option>
                  <option value="recovered">Recovered with sequelae</option>
                  <option value="ongoing_treatment">Ongoing / Continuing treatment</option>
                  <option value="condition_worsening">Condition worsening</option>
                  <option value="unknown">Unknown</option>
                </select>
              </div>
            </div>
            <div className="col-12">
              <div className="form-group">
                <label htmlFor="description">Description</label>
                <textarea
                  className="form-control"
                  id="description"
                  name="description"
                  value={formData.description}
                  onChange={handleChange}
                  required
                  rows={4}
                />
              </div>
            </div>
            <div className="col-md-6">
              <div className="form-group">
                <label htmlFor="start_date">Start Date</label>
                <input
                  type="date"
                  className="form-control"
                  id="start_date"
                  name="start_date"
                  value={formData.start_date}
                  onChange={handleChange}
                  required
                />
              </div>
            </div>
            <div className="col-md-6">
              <div className="form-group">
                <label htmlFor="start_time">Start Time</label>
                <input
                  type="time"
                  className="form-control"
                  id="start_time"
                  name="start_time"
                  value={formData.start_time}
                  onChange={handleChange}
                  required
                />
              </div>
            </div>
            {!isPatient && (
              <div className="col-md-6">
                <div className="form-group">
                  <label htmlFor="severity">Severity</label>
                  <select
                    className="form-control"
                    id="severity"
                    name="severity"
                    value={formData.severity}
                    onChange={handleChange}
                    required
                  >
                    <option value="mild">Mild</option>
                    <option value="moderate">Moderate</option>
                    <option value="severe">Severe</option>
                    <option value="critical">Critical</option>
                    <option value="life_threatening">Life Threatening</option>
                    <option value="death">Death (Fatal)</option>
                  </select>
                </div>
              </div>
            )}
            <div className="col-md-6">
              <div className="form-group">
                <label className="checkbox-label">
                  <input
                    type="checkbox"
                    name="hospitalization_required"
                    checked={formData.hospitalization_required}
                    onChange={handleChange}
                  />
                  Hospitalization Required
                </label>
              </div>
            </div>
            {formData.status === "resolved" && (
              <div className="col-md-6">
                <div className="form-group">
                  <label htmlFor="resolved_date">Resolved Date</label>
                  <input
                    type="date"
                    className="form-control"
                    id="resolved_date"
                    name="resolved_date"
                    value={formData.resolved_date}
                    onChange={handleChange}
                    required
                  />
                </div>
              </div>
            )}
            {!isPatient && (
              <div className="col-md-6">
                <div className="form-group">
                  <label htmlFor="relatedness">Relatedness</label>
                  <select
                    className="form-control"
                    id="relatedness"
                    name="relatedness"
                    value={formData.relatedness}
                    onChange={handleChange}
                    required
                  >
                    <option value="definitely_related">Definitely related</option>
                    <option value="probably_related">Probably related</option>
                    <option value="possibly_related">Possibly related</option>
                    <option value="unlikely_related">Unlikely</option>
                    <option value="unrelated">Unrelated</option>
                  </select>
                </div>
              </div>
            )}
            {!isPatient && (
              <div className="col-md-6">
                <div className="form-group">
                  <label htmlFor="category">Category</label>
                  <select
                    className="form-control"
                    id="category"
                    name="category"
                    value={formData.category}
                    onChange={handleChange}
                    required
                  >
                    <option value="nan">N/A</option>
                    <option value="sae">SAEs</option>
                    <option value="ae">AEs</option>
                    <option value="ar">ARs</option>
                    <option value="ssar">SSARs</option>
                    <option value="susar">SUSARs</option>
                  </select>
                </div>
              </div>
            )}
            {!isPatient && (
              <div className="col-12">
                <div className="form-group">
                  <label htmlFor="review_comment">Review Comment</label>
                  <textarea
                    className="form-control"
                    required
                    id="review_comment"
                    name="review_comment"
                    value={reviewComment}
                    onChange={(e) => setReviewComment(e.target.value)}
                    rows={3}
                    placeholder="Add a review comment"
                  />
                </div>
              </div>
            )}
          </div>

          {error && <div className="error-message">{error.message}</div>}

          <div className="modal-footer">
            <button
              type="button"
              className="btn btn-secondary"
              onClick={onClose}
              disabled={isPending}
            >
              Cancel
            </button>
            <button type="submit" className="btn btn-primary" disabled={isPending}>
              {isPending ? "Saving..." : "Save Symptom"}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default AddSymptomModal;
import React, { useEffect, useState } from "react";
import { motion } from "framer-motion";
import { useNavigate } from "react-router-dom";
import {
  Plus,
  Activity,
  RefreshCw,
  HelpCircle,
  Calendar,
  Clock,
  MessageCircle
} from "lucide-react";
import NurtifyButton from "@/components/NurtifyButton";
import { useCurrentUserQuery } from "@/hooks/user.query";
import {
  useVisitsByEnrollmentQuery,
  useStudyEnrollmentsByPatientQuery
} from "@/hooks/study.query";
import { useSymptomsQuery } from "@/hooks/symptom.query";
import { usePatientConcomitantMedicationsQuery } from "@/hooks/patient.query";
import { useGetUnsubmittedFormsByPatient } from "@/hooks/form.query";
import "./dashboard.css";

const DashboardSection: React.FC = () => {
  const [showMessage, setShowMessage] = useState(false); // Start with false
  const navigate = useNavigate();
  const { data: currentUser } = useCurrentUserQuery();

  // Get patient UUID for fetching appointments
  const patientUuid = (currentUser as any)?.patient_uuid || "";

  // Fetch enrollments and visits
  const { data: enrollments } = useStudyEnrollmentsByPatientQuery(patientUuid);
  const firstEnrollment = enrollments?.[0];

  const { data: visits } = useVisitsByEnrollmentQuery(
    firstEnrollment?.uuid || "",
    { enabled: !!firstEnrollment?.uuid }
  );

  // Fetch symptoms and medications for diary section
  const { data: symptoms = [] } = useSymptomsQuery(patientUuid);
  const { data: medications } = usePatientConcomitantMedicationsQuery(patientUuid);

  // Fetch studies and forms for bottom sections
  const { data: studies } = useStudyEnrollmentsByPatientQuery(patientUuid);
  const { data: forms } = useGetUnsubmittedFormsByPatient(patientUuid);

  useEffect(() => {
    // Check if message has been shown in this session
    const messageShownKey = `emergencyMessageShown_${patientUuid}`;
    const hasMessageBeenShown = sessionStorage.getItem(messageShownKey);

    // Only show message if it hasn't been shown in this session
    if (!hasMessageBeenShown && currentUser) {
      setShowMessage(true);

      // Mark message as shown for this session
      sessionStorage.setItem(messageShownKey, 'true');

      // Auto-hide after 30 seconds
      const timer = setTimeout(() => {
        setShowMessage(false);
      }, 30000); // 30 seconds

      // Cleanup timeout on component unmount
      return () => clearTimeout(timer);
    }
  }, [currentUser, patientUuid]);

  const handleRefundClick = () => {
    navigate('/patient/conversation?type=refund');
  };

  const handleOtherQueriesClick = () => {
    navigate('/patient/live-chat');
  };

  const handleSeeAllAppointments = () => {
    navigate('/patient/appointments');
  };

  const handleAddSymptom = () => {
    navigate('/patient/symptoms');
  };

  const handleAddMedication = () => {
    navigate('/patient/medication');
  };

  const handleNewMedication = () => {
    navigate('/patient/medication');
  };

  const handleReportSymptoms = () => {
    navigate('/patient/symptoms');
  };

  const handleSeeAllStudies = () => {
    navigate('/patient/studies');
  };

  const handleSeeAllForms = () => {
    navigate('/patient/my-forms');
  };

  // Filter upcoming visits
  const getUpcomingVisits = () => {
    if (!visits) return [];
    const today = new Date();
    today.setHours(0, 0, 0, 0);

    return visits
      .filter((visit: any) => {
        const visitDate = new Date(visit.date);
        visitDate.setHours(0, 0, 0, 0);
        return visitDate >= today && visit.visit_status !== 'Canceled';
      })
      .sort((a: any, b: any) => new Date(a.date).getTime() - new Date(b.date).getTime())
      .slice(0, 2); // Show only first 2 upcoming visits
  };

  const upcomingVisits = getUpcomingVisits();

  // Format date for display
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    const today = new Date();
    const tomorrow = new Date(today);
    tomorrow.setDate(tomorrow.getDate() + 1);

    if (date.toDateString() === today.toDateString()) {
      return "Today";
    } else if (date.toDateString() === tomorrow.toDateString()) {
      return "Tomorrow";
    } else {
      return date.toLocaleDateString('en-US', {
        weekday: 'short',
        month: 'short',
        day: 'numeric'
      });
    }
  };

  // Format time for display - format HH:MM:SS to HH:MM
  const formatTime = (timeString: string) => {
    if (!timeString) return "Time TBD";
    // If time is in HH:MM:SS format, extract HH:MM
    return timeString.substring(0, 5);
  };

  // Get status badge styling based on status value
  const getStatusBadgeClass = (status: string) => {
    const statusLower = status?.toLowerCase() || '';

    if (statusLower.includes('accepted')) {
      return 'status-success'; // Green for accepted
    } else {
      return 'status-danger'; // Red for everything else
    }
  };


  return (
    <motion.div
      className="dashboard-container"
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      transition={{ duration: 0.5 }}
    >
      {/* Emergency Banner Message */}
      {showMessage && (
        <div
          className="dashboard-banner-message"
          style={{
            width: "100%",
            height: "auto",
            backgroundColor: "#FF4267",
            borderRadius: "8px",
            padding: "12px 24px",
            margin: "0 0 20px 0",
            display: "flex",
            justifyContent: "center",
            alignItems: "center",
            color: "white",
            fontWeight: "bold",
            gap: "4px",
            opacity: 1,
          }}
        >
          <p style={{ margin: 0, textAlign: "center", fontSize: "14px", lineHeight: "1.4" }}>
            In an emergency 🚨, call 999 immediately for urgent issues like severe chest pain, breathing difficulty, unconsciousness, or serious injuries.<br />
            For non-emergencies, use NHS 111 (24/7) for advice and referrals to GPs, urgent care, or other services. Contact your GP or pharmacy for minor concerns. Reserve hospitals and A&E for life-threatening conditions. Unsure? Call NHS 111 for guidance.
          </p>
        </div>
      )}

      {/* Welcome Header */}
      <div className="welcome-header">
        <h1 className="welcome-title">Welcome Back, <span className="name-highlight">{currentUser?.first_name || 'Patient'}</span></h1>
      </div>

      {/* Report to Clinical Team Section */}
      <div className="section">
        <h2 className="section-title">Report to Clinical Team</h2>
        <div className="report-cards-grid">
          <div className="report-card new-medication" onClick={handleNewMedication}>
            <div className="card-icon">
              <Plus size={24} />
            </div>
            <h3>New Medication</h3>
          </div>
          <div className="report-card report-symptoms" onClick={handleReportSymptoms}>
            <div className="card-icon">
              <Activity size={24} />
            </div>
            <h3>Report Symptoms</h3>
          </div>
          <div className="report-card request-refund" onClick={handleRefundClick}>
            <div className="card-icon">
              <RefreshCw size={24} />
            </div>
            <h3>Request Refund</h3>
          </div>
          <div className="report-card other-queries" onClick={handleOtherQueriesClick}>
            <div className="card-icon">
              <HelpCircle size={24} />
            </div>
            <h3>Other Queries</h3>
          </div>
        </div>
      </div>

      {/* Main Content Grid */}
      <div className="main-content-grid">
        {/* My Appointments Section */}
        <div className="appointments-section">
          <div className="section-header">
            <h2 className="section-title">My Appointments</h2>
            <button className="see-all-btn" onClick={handleSeeAllAppointments}>See All</button>
          </div>
          <div className="appointments-list">
            {upcomingVisits.length > 0 ? (
              upcomingVisits.map((visit: any) => (
                <div key={visit.uuid} className="appointment-item">
                  <div className="doctor-avatar"></div>
                  <div className="appointment-details">
                    <h4>{visit.name}</h4>
                    <p>{visit.activities && visit.activities.length > 0 ? visit.activities.join(", ") : "No activities"}</p>
                    <div className="appointment-time">
                      <Calendar size={14} />
                      <span>{formatDate(visit.date)}</span>
                      <Clock size={14} />
                      <span>{formatTime(visit.time)}</span>
                    </div>
                  </div>
                  <div className="appointment-action">
                    <MessageCircle size={20} />
                  </div>
                </div>
              ))
            ) : (
              <div className="appointment-item">
                <div className="appointment-details">
                  <h4>No upcoming appointments</h4>
                  <p>You have no scheduled appointments at this time.</p>
                </div>
              </div>
            )}
          </div>
        </div>

        {/* Recommended Activities Section */}
        <div className="activities-section">
          <div className="section-header">
            <h2 className="section-title">Recommended Activities</h2>
            <button className="see-all-btn">See All</button>
          </div>
          <div className="activities-list">
            <div className="activity-item">
              <h4>Title</h4>
              <p>Description</p>
            </div>
            <div className="activity-item">
              <h4>Title</h4>
              <p>Description</p>
            </div>
            <div className="activity-item">
              <h4>Title</h4>
              <p>Description</p>
            </div>
          </div>
        </div>
      </div>

      {/* My Diary Section */}
      <div className="diary-section">
        <div className="diary-header">
          <h2 className="section-title">My Diary</h2>
          <div className="diary-actions">
            <NurtifyButton variant="primary" size="small" onClick={handleAddMedication}>Add Medication</NurtifyButton>
            <NurtifyButton variant="primary" size="small" onClick={handleAddSymptom}>Add Symptom</NurtifyButton>
          </div>
        </div>

        <div className="diary-content">
          {/* Symptoms Table */}
          <div className="diary-table-section">
            <h3>Symptoms</h3>
            <div className="table-container">
              <table className="diary-table">
                <thead>
                  <tr>
                    <th>Description</th>
                    <th>Severity</th>
                    <th>Start date</th>
                    <th>Hospitalized</th>
                  </tr>
                </thead>
                <tbody>
                  {symptoms.slice(0, 3).map((symptom: any) => (
                    <tr key={symptom.uuid}>
                      <td>{symptom.description}</td>
                      <td>{symptom.severity}</td>
                      <td>{formatDate(symptom.start_date)}</td>
                      <td>{symptom.hospitalization_required ? "Yes" : "No"}</td>
                    </tr>
                  ))}
                  {symptoms.length === 0 && (
                    <tr>
                      <td colSpan={4} style={{ textAlign: 'center', color: '#666' }}>
                        No symptoms recorded yet
                      </td>
                    </tr>
                  )}
                </tbody>
              </table>
            </div>
          </div>

          {/* Medications Table */}
          <div className="diary-table-section">
            <h3>Medications</h3>
            <div className="table-container">
              <table className="diary-table">
                <thead>
                  <tr>
                    <th>Medication</th>
                    <th>Indication</th>
                    <th>Dose</th>
                    <th>Schedule</th>
                  </tr>
                </thead>
                <tbody>
                  {medications?.results?.slice(0, 3).map((medication: any) => (
                    <tr key={medication.uuid}>
                      <td>{medication.medication}</td>
                      <td>{medication.indication}</td>
                      <td>{medication.dose} {medication.dose_units_display}</td>
                      <td>{medication.schedule_display}</td>
                    </tr>
                  ))}
                  {(!medications?.results || medications.results.length === 0) && (
                    <tr>
                      <td colSpan={4} style={{ textAlign: 'center', color: '#666' }}>
                        No medications recorded yet
                      </td>
                    </tr>
                  )}
                </tbody>
              </table>
            </div>
          </div>
        </div>
      </div>

      {/* Bottom Sections */}
      <div className="bottom-sections">
        {/* Studies Section */}
        <div className="studies-section">
          <div className="section-header">
            <h2 className="section-title">Studies</h2>
            <button className="see-all-btn" onClick={handleSeeAllStudies}>See All</button>
          </div>
          <div className="table-container">
            <table className="studies-table">
              <thead>
                <tr>
                  <th>Study Name</th>
                  <th>Patient Code</th>
                  <th>Status</th>
                  <th>Action</th>
                </tr>
              </thead>
              <tbody>
                {studies?.slice(0, 3).map((study: any) => (
                  <tr key={study.uuid}>
                    <td>{study.study_name || "N/A"}</td>
                    <td>{study.patient_code || "N/A"}</td>
                    <td><span className={getStatusBadgeClass(study.study_status || "Active")}>{study.study_status || "Active"}</span></td>
                    <td><NurtifyButton variant="primary" size="small" outline>Details</NurtifyButton></td>
                  </tr>
                ))}
                {(!studies || studies.length === 0) && (
                  <tr>
                    <td colSpan={4} style={{ textAlign: 'center', color: '#666' }}>
                      You are not in a study yet
                    </td>
                  </tr>
                )}
              </tbody>
            </table>
          </div>
        </div>

        {/* Forms Section */}
        <div className="forms-section">
          <div className="section-header">
            <h2 className="section-title">Forms</h2>
            <button className="see-all-btn" onClick={handleSeeAllForms}>See All</button>
          </div>
          <div className="table-container">
            <table className="forms-table">
              <thead>
                <tr>
                  <th>Form Name</th>
                  <th>Created</th>
                  <th>Status</th>
                  <th>Action</th>
                </tr>
              </thead>
              <tbody>
                {(Array.isArray(forms) ? forms : (forms as any)?.results || []).slice(0, 3).map((form: any) => (
                  <tr key={form.uuid}>
                    <td>{form.name || "Unnamed Form"}</td>
                    <td>{form.created_at ? new Date(form.created_at).toLocaleDateString() : "N/A"}</td>
                    <td><span className={getStatusBadgeClass("New")}>New</span></td>
                    <td><NurtifyButton variant="primary" size="small" outline>Details</NurtifyButton></td>
                  </tr>
                ))}
                {((!forms || (Array.isArray(forms) ? forms.length === 0 : (!(forms as any)?.results || (forms as any).results.length === 0)))) && (
                  <tr>
                    <td colSpan={4} style={{ textAlign: 'center', color: '#666' }}>
                      You haven't any form yet
                    </td>
                  </tr>
                )}
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </motion.div>
  );
};

export default DashboardSection;

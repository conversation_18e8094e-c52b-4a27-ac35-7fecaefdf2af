import { useLocation, useNavigate } from 'react-router-dom';
import './MobileSidebarIcons.css';


interface MobileSidebarIconsProps {
   
  menuItems: any[];
  basePath?: string; // Add basePath prop
}


const MobileSidebarIcons = ({ menuItems, basePath }: MobileSidebarIconsProps) => {
  const location = useLocation();
  const navigate = useNavigate();

  const handleClick = (item: typeof menuItems[0]) => {
    if (item.onClick) {
      item.onClick();
    } else {
      navigate(`${basePath}${item.path}`);
    }
  };

  return (
    <nav className="mobile-sidebar-icons" role="navigation" aria-label="Mobile navigation">
      <ul className="mobile-nav-list">
        {menuItems.map((item, index) => (
          <li
            key={index}
            className={`mobile-nav-item ${location.pathname === `${basePath}${item.path}` ? 'active' : ''}`}
            onClick={() => handleClick(item)}
            role="button"
            tabIndex={0}
          >
            <div className="mobile-nav-icon">
              {item.icon}
            </div>
          </li>
        ))}
      </ul>
    </nav>
  );
};

export default MobileSidebarIcons; 
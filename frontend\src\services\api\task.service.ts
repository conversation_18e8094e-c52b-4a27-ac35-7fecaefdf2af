import api from "@/services/api";

export interface StudyTask {
  uuid: string;
  name: string;
  description: string;
  study_uuid: string;
  created_at: string;
  updated_at: string;
}

export interface StudyTasksResponse {
  count: number;
  next: string | null;
  previous: string | null;
  results: StudyTask[];
}

export const TaskService = {
  // Get all tasks for a specific study
  getTasksByStudy: async (studyUuid: string): Promise<StudyTasksResponse> => {
    const response = await api.get(`/study/studies/${studyUuid}/tasks/`);
    const data = response.data;
    
    // Handle both array and paginated response formats
    if (Array.isArray(data)) {
      return {
        count: data.length,
        next: null,
        previous: null,
        results: data
      };
    }
    
    return data;
  },

  // Get all tasks (with optional study filter)
  getAllTasks: async (studyUuid?: string): Promise<StudyTasksResponse> => {
    const params = studyUuid ? `?study_uuid=${studyUuid}` : '';
    const response = await api.get(`/study/tasks/${params}`);
    const data = response.data;
    
    // Handle both array and paginated response formats
    if (Array.isArray(data)) {
      return {
        count: data.length,
        next: null,
        previous: null,
        results: data
      };
    }
    
    return data;
  },
}; 
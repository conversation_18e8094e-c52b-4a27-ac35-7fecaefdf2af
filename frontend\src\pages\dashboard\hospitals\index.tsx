import "./hospitals.css";
import { <PERSON>, useNavigate } from "react-router-dom";
import { useState } from "react";
import {
  ArrowUpRight,
  Plus,
  Trash2,
  Building,
  Search,
  UserCog,
} from "lucide-react";
import NoResult from "@/components/common/NoResult";
import { Hospital as HospitalData } from "@/services/api/types.ts";
import {
  useHospitalsQuery,
  useDeleteHospitalMutation,
} from "@/hooks/hospital.query";
import DeleteHospitalModal from "@/components/modal/DeleteHospitalModal";
import DataTable, { DataTableSearch } from "@/components/common/DataTable";

export default function Hospitals() {
  const { data = [] } = useHospitalsQuery(); // Default to empty array
  const deleteHospitalMutation = useDeleteHospitalMutation();
  const navigate = useNavigate();
  const [deletableHospitalID, setDeletableHospitalID] = useState<string>("");
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [filteredData, setFilteredData] = useState<HospitalData[]>([]);

  const handleCloseModal = () => {
    setIsModalOpen(false);
  };

  const handleSubmitModal = async () => {
    try {
      await deleteHospitalMutation.mutateAsync({ uuid: deletableHospitalID });
      setIsModalOpen(false); // Close modal on success
    } catch (error) {
      console.error("Error deleting hospital:", error);
    }
  };

  const HospitalColums: Array<{
    key: keyof HospitalData;
    header: string;
    hidden?: boolean;
  }> = [
    { key: "uuid", header: "ID", hidden: true },
    { key: "name", header: "Hospital Name" },
    { key: "department_count", header: "Number of department" },
    { key: "postcode", header: "Postcode" },
    { key: "country", header: "Country" },
  ];

  const actions = [
    {
      icon: <ArrowUpRight size={20} />,
      onClick: (row: HospitalData) => handleHospitalDetails(row),
      tooltipText: "Edit this item",
    },
    {
      icon: <Trash2 size={20} color="#FF0000" />,
      onClick: (row: HospitalData) => handleHospitalDelete(row),
      tooltipText: "Delete this item",
    },
    {
      icon: <UserCog size={20} color="#FF0000" />,
      onClick: (row: HospitalData) => handleHospitalAdmin(row),
      tooltipText: "Manage Admin",
    },
  ];

  const handleHospitalDetails = (hospital: HospitalData) => {
    navigate(`/org/dashboard/hospital-details/${hospital.uuid}`);
  };

  const handleHospitalDelete = (hospital: HospitalData) => {
    setIsModalOpen(true);
    setDeletableHospitalID(hospital.uuid ?? "");
  };

  const handleHospitalAdmin = (hospital: HospitalData) => {
    navigate(`/org/dashboard/hospital-details/${hospital.uuid}/hospitalAdmin`);
  };

  return (
    <div className="hospitals-container">
      <div className="hospitals-header">
        <div className="hospitals-title">
          <h1>
            <Building size={24} style={{ marginRight: "10px" }} />
            Hospitals
          </h1>
        </div>
        <div className="hospitals-subtitle">
          <h6>Manage and view all hospitals in your network</h6>
        </div>
      </div>
      
      <div className="hospitals-search-container">
        <div className="hospitals-search-box">
          <Search className="search-icon" size={18} />
          <DataTableSearch
            data={data}
            onFilter={setFilteredData}
            placeholder="Search hospitals..."
          />
        </div>
        <Link
          to="/org/dashboard/add-hospital"
          className="hospitals-add-button"
        >
          Add Hospital <Plus size={20} />
        </Link>
      </div>
      
      <div className="hospitals-table-container">
        {data.length > 0 ? (
          <>
            <h4 className="hospitals-table-title">Hospital List</h4>
            <DataTable
              data={data}
              filteredData={filteredData}
              columns={HospitalColums.map(col => ({
                key: col.key,
                header: col.header,
                hidden: col.hidden,
                sortable: true
              }))}
              actions={actions}
              noDataMessage="No hospitals found"
              defaultItemsPerPage={10}
            />
            {isModalOpen && (
              <DeleteHospitalModal
                isOpen={isModalOpen}
                onClose={handleCloseModal}
                onDelete={handleSubmitModal}
              />
            )}
          </>
        ) : (
          <NoResult addPath="/dashboard/add-hospital" title="Hospital" />
        )}
      </div>
    </div>
  );
}

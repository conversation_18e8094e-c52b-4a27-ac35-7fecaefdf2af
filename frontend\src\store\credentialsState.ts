import { create } from 'zustand';

interface CredentialsState {
  email: string | null;
  password: string | null;
  userType: string | null;
  setCredentials: (email: string, password: string, userType: string) => void;
  clearCredentials: () => void;
}

export const useCredentialsStore = create<CredentialsState>((set) => ({
  email: null,
  password: null,
  userType: null,
  setCredentials: (email, password, userType) => set({ email, password, userType }),
  clearCredentials: () => set({ email: null, password: null, userType: null}),
}));

/* Delegation Logs Page Styles - Prefixed with .delegation-logs- */

/* Main Container */
.delegation-logs-container {
  font-family: var(--font-primary);
  color: var(--color-dark-1);
  background-color: var(--color-light-4);
  min-height: 100vh;
  max-width: 1400px;
  margin: 32px auto 0 auto;
  padding: 0 20px;
}

/* Header Section */
.delegation-logs-header {
  background: var(--color-white);
  border-radius: 16px;
  padding: 32px;
  margin: 80px 0 64px 0;
  box-shadow: 0px 4px 20px rgba(0, 0, 0, 0.08);
  border: 1px solid var(--color-light-2);
  animation: delegation-logs-fadeInUp 0.6s ease-out both;
}

.delegation-logs-header-content {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  gap: 24px;
}

.delegation-logs-header-left {
  flex: 1;
}

.delegation-logs-title-section {
  display: flex;
  align-items: center;
  gap: 16px;
  margin-bottom: 32px;
}

.delegation-logs-icon-wrapper {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 56px;
  height: 56px;
  background: linear-gradient(135deg, var(--color-purple-1) 0%, var(--color-blue-1) 100%);
  border-radius: 16px;
  color: white;
  box-shadow: 0px 8px 24px rgba(55, 183, 195, 0.3);
  animation: delegation-logs-pulse 2s infinite;
}

.delegation-logs-title-content h1 {
  font-size: var(--text-30);
  font-weight: 700;
  color: var(--color-dark-1);
  margin: 0 0 8px 0;
  line-height: 1.2;
}

.delegation-logs-title-content p {
  font-size: var(--text-16);
  color: var(--color-light-1);
  margin: 0;
  line-height: 1.5;
}

/* Stats Section */
.delegation-logs-stats {
  display: flex;
  gap: 20px;
  flex-wrap: wrap;
  margin-bottom: 0;
}

.delegation-logs-stat-card {
  display: flex;
  align-items: center;
  gap: 16px;
  background: var(--color-white);
  padding: 20px 24px;
  border-radius: 16px;
  border: 1px solid var(--color-light-2);
  box-shadow: 0px 2px 8px rgba(0, 0, 0, 0.04);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  min-width: 180px;
  max-width: 220px;
  flex: 1;
  position: relative;
  overflow: hidden;
}

.delegation-logs-stat-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, var(--color-purple-1), var(--color-blue-1));
  transform: scaleX(0);
  transition: transform 0.3s ease;
}

.delegation-logs-stat-card:hover {
  transform: translateY(-4px);
  box-shadow: 0px 12px 32px rgba(0, 0, 0, 0.15);
}

.delegation-logs-stat-card:hover::before {
  transform: scaleX(1);
}

.delegation-logs-stat-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 48px;
  height: 48px;
  border-radius: 12px;
  color: white;
  flex-shrink: 0;
}

.delegation-logs-stat-icon.pending {
  background: linear-gradient(135deg, var(--color-orange-1) 0%, var(--color-orange-4) 100%);
}

.delegation-logs-stat-icon.sponsor {
  background: linear-gradient(135deg, var(--color-blue-3) 0%, var(--color-blue-4) 100%);
}

.delegation-logs-stat-icon.total {
  background: linear-gradient(135deg, var(--color-green-4) 0%, var(--color-green-5) 100%);
}

.delegation-logs-stat-content {
  display: flex;
  flex-direction: column;
  flex: 1;
}

.delegation-logs-stat-number {
  font-size: var(--text-24);
  font-weight: 700;
  color: var(--color-dark-1);
  line-height: 1;
  margin-bottom: 4px;
}

.delegation-logs-stat-label {
  font-size: var(--text-14);
  color: var(--color-light-1);
  font-weight: 500;
}

/* Search Section */
.delegation-logs-search-section {
  background: var(--color-white);
  border-radius: 16px;
  padding: 24px;
  margin: 0 0 48px 0;
  box-shadow: 0px 2px 12px rgba(0, 0, 0, 0.06);
  border: 1px solid var(--color-light-2);
  animation: delegation-logs-fadeInUp 0.6s ease-out 0.2s both;
  max-width: 800px;
}

.delegation-logs-search-wrapper {
  position: relative;
  max-width: 600px;
}

.delegation-logs-search-input {
  width: 100%;
  padding: 16px 20px 16px 52px;
  border: 2px solid var(--color-light-2);
  border-radius: 12px;
  font-size: var(--text-16);
  background: var(--color-white);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  font-family: var(--font-primary);
}

.delegation-logs-search-input:focus {
  outline: none;
  border-color: var(--color-purple-1);
  box-shadow: 0px 0px 0px 4px rgba(55, 183, 195, 0.1);
  transform: translateY(-1px);
}

.delegation-logs-search-input::placeholder {
  color: var(--color-light-1);
  font-weight: 400;
}

.delegation-logs-search-icon {
  position: absolute;
  left: 18px;
  top: 50%;
  transform: translateY(-50%);
  color: var(--color-light-1);
  transition: color 0.3s ease;
}

.delegation-logs-search-wrapper:focus-within .delegation-logs-search-icon {
  color: var(--color-purple-1);
}

/* Table Section */
.delegation-logs-table-section {
  background: var(--color-white);
  border-radius: 16px;
  overflow: hidden;
  box-shadow: 0px 2px 12px rgba(0, 0, 0, 0.06);
  border: 1px solid var(--color-light-2);
  animation: delegation-logs-fadeInUp 0.6s ease-out 0.4s both;
  max-width: 100%;
  margin-bottom: 32px;
}

.delegation-logs-table-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24px 28px;
  border-bottom: 1px solid var(--color-light-2);
  background: linear-gradient(135deg, var(--color-light-6) 0%, var(--color-white) 100%);
}

.delegation-logs-table-title {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.delegation-logs-table-title h3 {
  font-size: var(--text-20);
  font-weight: 600;
  color: var(--color-dark-1);
  margin: 0;
  display: flex;
  align-items: center;
  gap: 12px;
}

.delegation-logs-table-title p {
  font-size: var(--text-14);
  color: var(--color-light-1);
  margin: 0;
}

.delegation-logs-table-content {
  padding: 0;
  overflow-x: auto;
  max-width: 100%;
}

/* Ensure DataTable doesn't exceed container width */
.delegation-logs-table-content .data-table {
  max-width: 100%;
  overflow-x: auto;
}

.delegation-logs-table-content table {
  width: 100%;
  max-width: 100%;
  table-layout: fixed;
}

.delegation-logs-table-content th,
.delegation-logs-table-content td {
  max-width: 200px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

/* Status Badges */
.delegation-logs-status-badge {
  display: inline-flex;
  align-items: center;
  gap: 6px;
  padding: 6px 12px;
  border-radius: 20px;
  font-size: var(--text-11);
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  max-width: 150px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.delegation-logs-status-pending-pi {
  background: var(--color-orange-5);
  color: var(--color-orange-1);
}

.delegation-logs-status-pending-sponsor {
  background: var(--color-blue-6);
  color: var(--color-blue-3);
}

.delegation-logs-status-accepted {
  background: var(--color-green-6);
  color: var(--color-green-5);
}

.delegation-logs-status-pi-rejected {
  background: var(--color-red-2);
  color: var(--color-red-1);
}

.delegation-logs-status-sponsor-rejected {
  background: var(--color-red-2);
  color: var(--color-red-1);
}

/* Action Buttons */
.delegation-logs-action-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 36px;
  height: 36px;
  border-radius: 10px;
  border: none;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  font-size: 0;
}

.delegation-logs-action-btn:disabled {
  opacity: 0.4;
  cursor: not-allowed;
  transform: none !important;
}

.delegation-logs-action-btn:not(:disabled):hover {
  transform: translateY(-2px);
  box-shadow: 0px 8px 16px rgba(0, 0, 0, 0.15);
}

.delegation-logs-action-view {
  background: linear-gradient(135deg, var(--color-blue-3) 0%, var(--color-blue-4) 100%);
  color: white;
}

.delegation-logs-action-approve {
  background: linear-gradient(135deg, var(--color-green-4) 0%, var(--color-green-5) 100%);
  color: white;
}

.delegation-logs-action-reject {
  background: linear-gradient(135deg, var(--color-red-1) 0%, var(--color-orange-1) 100%);
  color: white;
}

/* Tooltip */
.delegation-logs-tooltip {
  position: relative;
}

.delegation-logs-tooltip::after {
  content: attr(data-tooltip);
  position: absolute;
  bottom: 100%;
  left: 50%;
  transform: translateX(-50%);
  background: var(--color-dark-8);
  color: white;
  padding: 8px 12px;
  border-radius: 6px;
  font-size: var(--text-12);
  white-space: nowrap;
  z-index: 1000;
  margin-bottom: 8px;
  opacity: 0;
  pointer-events: none;
  transition: opacity 0.3s ease;
}

.delegation-logs-tooltip::before {
  content: '';
  position: absolute;
  bottom: 100%;
  left: 50%;
  transform: translateX(-50%);
  border: 4px solid transparent;
  border-top-color: var(--color-dark-8);
  margin-bottom: 4px;
  opacity: 0;
  pointer-events: none;
  transition: opacity 0.3s ease;
}

.delegation-logs-tooltip:hover::after,
.delegation-logs-tooltip:hover::before {
  opacity: 1;
}

/* Loading States */
.delegation-logs-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
  color: var(--color-light-1);
  background: var(--color-white);
  border-radius: 16px;
  margin: 64px 0;
  max-width: 600px;
  margin-left: auto;
  margin-right: auto;
}

.delegation-logs-loading-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16px;
}

.delegation-logs-spinner {
  width: 40px;
  height: 40px;
  border: 3px solid var(--color-light-2);
  border-top: 3px solid var(--color-purple-1);
  border-radius: 50%;
  animation: delegation-logs-spin 1s linear infinite;
}

.delegation-logs-loading-text {
  font-size: var(--text-16);
  font-weight: 500;
  color: var(--color-dark-1);
}

/* Error States */
.delegation-logs-error {
  background: var(--color-white);
  border-radius: 16px;
  padding: 40px;
  text-align: center;
  box-shadow: 0px 2px 12px rgba(0, 0, 0, 0.06);
  border: 1px solid var(--color-light-2);
  margin: 64px 0;
  max-width: 600px;
  margin-left: auto;
  margin-right: auto;
}

.delegation-logs-error-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16px;
  max-width: 400px;
  margin: 0 auto;
}

.delegation-logs-error-icon {
  width: 64px;
  height: 64px;
  background: linear-gradient(135deg, var(--color-red-1) 0%, var(--color-red-2) 100%);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
}

.delegation-logs-error h3 {
  font-size: var(--text-20);
  font-weight: 600;
  color: var(--color-dark-1);
  margin: 0;
}

.delegation-logs-error p {
  font-size: var(--text-14);
  color: var(--color-light-1);
  margin: 0;
  line-height: 1.5;
}

/* Animations */
@keyframes delegation-logs-fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes delegation-logs-pulse {
  0%, 100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
}

@keyframes delegation-logs-spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Responsive Design */
@media (max-width: 1400px) {
  .delegation-logs-container {
    max-width: 1200px;
  }
}

@media (max-width: 1200px) {
  .delegation-logs-container {
    max-width: 1000px;
  }
  
  .delegation-logs-stats {
    justify-content: center;
  }
  
  .delegation-logs-stat-card {
    min-width: 160px;
    max-width: 200px;
  }
}

@media (max-width: 768px) {
  .delegation-logs-container {
    padding: 0 16px;
  }
  
  .delegation-logs-header {
    padding: 24px;
    margin: 24px 0 48px 0;
  }
  
  .delegation-logs-header-content {
    flex-direction: column;
    align-items: stretch;
  }
  
  .delegation-logs-title-section {
    flex-direction: column;
    align-items: center;
    text-align: center;
    gap: 12px;
    margin-bottom: 24px;
  }
  
  .delegation-logs-title-content h1 {
    font-size: var(--text-24);
  }
  
  .delegation-logs-stats {
    flex-direction: column;
    gap: 16px;
  }
  
  .delegation-logs-stat-card {
    min-width: auto;
    max-width: none;
    justify-content: center;
  }
  
  .delegation-logs-search-section {
    padding: 20px;
    margin-bottom: 32px;
  }
  
  .delegation-logs-search-wrapper {
    max-width: none;
  }
  
  .delegation-logs-table-header {
    flex-direction: column;
    gap: 16px;
    align-items: stretch;
    padding: 20px;
  }
  
  .delegation-logs-table-title h3 {
    font-size: var(--text-18);
    justify-content: center;
  }
}

@media (max-width: 480px) {
  .delegation-logs-container {
    padding: 0 12px;
  }
  
  .delegation-logs-header {
    padding: 20px;
    margin: 20px 0 32px 0;
  }
  
  .delegation-logs-icon-wrapper {
    width: 48px;
    height: 48px;
  }
  
  .delegation-logs-title-content h1 {
    font-size: var(--text-20);
  }
  
  .delegation-logs-search-section {
    padding: 16px;
    margin-bottom: 24px;
  }
  
  .delegation-logs-search-input {
    padding: 14px 16px 14px 44px;
    font-size: var(--text-14);
  }
  
  .delegation-logs-search-icon {
    left: 14px;
  }
  
  .delegation-logs-table-header {
    padding: 16px;
  }
  
  .delegation-logs-stat-number {
    font-size: var(--text-20);
  }
  
  .delegation-logs-action-btn {
    width: 32px;
    height: 32px;
  }
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
  .delegation-logs-container {
    background-color: var(--color-dark-8);
    color: var(--color-white);
  }
  
  .delegation-logs-header,
  .delegation-logs-search-section,
  .delegation-logs-table-section,
  .delegation-logs-loading,
  .delegation-logs-error {
    background: var(--color-dark-4);
    border-color: var(--color-dark-3);
  }
  
  .delegation-logs-search-input {
    background: var(--color-dark-4);
    border-color: var(--color-dark-3);
    color: var(--color-white);
  }
  
  .delegation-logs-table-header {
    background: linear-gradient(135deg, var(--color-dark-3) 0%, var(--color-dark-4) 100%);
  }
}

/* Focus styles for accessibility */
.delegation-logs-search-input:focus,
.delegation-logs-action-btn:focus {
  outline: 2px solid var(--color-purple-1);
  outline-offset: 2px;
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  .delegation-logs-header,
  .delegation-logs-search-section,
  .delegation-logs-table-section,
  .delegation-logs-stat-card,
  .delegation-logs-action-btn,
  .delegation-logs-spinner {
    animation: none;
    transition: none;
  }
  
  .delegation-logs-icon-wrapper {
    animation: none;
  }
}

/* Print styles */
@media print {
  .delegation-logs-search-section,
  .delegation-logs-action-btn {
    display: none;
  }
  
  .delegation-logs-container {
    background: white;
  }
  
  .delegation-logs-header,
  .delegation-logs-table-section {
    box-shadow: none;
    border: 1px solid #ccc;
  }
}

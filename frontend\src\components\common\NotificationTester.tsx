import React from 'react';
import { useSendNotificationMutation } from '@/hooks/notification.query';
import { useCurrentUserQuery } from '@/hooks/user.query';

const NotificationTester: React.FC = () => {
  const { data: currentUser } = useCurrentUserQuery();
  const sendNotificationMutation = useSendNotificationMutation();

  const sendTestNotification = async () => {
    if (!currentUser) {
      console.error('No current user found');
      return;
    }

    try {
      await sendNotificationMutation.mutateAsync({
        recipient_id: currentUser.id,
        notification_type: 'test_notification',
        title: 'Test Notification',
        message: 'This is a test notification to verify the system is working.',
        priority: 'normal',
        data: {
          test: true,
          timestamp: new Date().toISOString()
        },
        send_push: true,
        send_email: false,
        send_sms: false,
        send_in_app: true
      });
      
      console.log('✅ Test notification sent successfully');
    } catch (error) {
      console.error('❌ Error sending test notification:', error);
    }
  };

  // Only show in development mode
  if (import.meta.env.VITE_APP_MODE !== 'development') {
    return null;
  }

  return (
    <div style={{ 
      position: 'fixed', 
      bottom: '20px', 
      right: '20px', 
      zIndex: 9999,
      background: '#37B7C3',
      color: 'white',
      padding: '10px 15px',
      borderRadius: '8px',
      boxShadow: '0 4px 12px rgba(0,0,0,0.15)'
    }}>
      <button
        onClick={sendTestNotification}
        disabled={sendNotificationMutation.isPending}
        style={{
          background: 'white',
          color: '#37B7C3',
          border: 'none',
          padding: '8px 12px',
          borderRadius: '4px',
          cursor: 'pointer',
          fontSize: '12px',
          fontWeight: '600'
        }}
      >
        {sendNotificationMutation.isPending ? 'Sending...' : 'Send Test Notification'}
      </button>
    </div>
  );
};

export default NotificationTester;

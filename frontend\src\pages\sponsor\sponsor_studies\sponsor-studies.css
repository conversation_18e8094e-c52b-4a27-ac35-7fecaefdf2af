/* Sponsor Studies Page Styles - All classes prefixed with 'sponsor-studies-' */

/* Main Container */
.sponsor-studies-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 24px;
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  min-height: 100vh;
}

/* Header Section */
.sponsor-studies-header {
  background: white;
  border-radius: 16px;
  padding: 32px;
  margin-bottom: 32px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  border: 1px solid #e2e8f0;
}

.sponsor-studies-header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 16px;
}

.sponsor-studies-title {
  display: flex;
  align-items: center;
  gap: 12px;
  color: #1e293b;
  font-size: 2rem;
  font-weight: 700;
  margin: 0;
}

.sponsor-studies-create-btn {
  display: flex;
  align-items: center;
  gap: 8px;
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
  color: white;
  border: none;
  border-radius: 12px;
  padding: 12px 24px;
  font-weight: 600;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
}

.sponsor-studies-create-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(59, 130, 246, 0.4);
}

.sponsor-studies-create-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

/* Form Section */
.sponsor-studies-form-section {
  background: white;
  border-radius: 16px;
  padding: 32px;
  margin-bottom: 32px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  border: 1px solid #e2e8f0;
  animation: slideIn 0.3s ease-out;
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateY(-20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.sponsor-studies-form-title {
  display: flex;
  align-items: center;
  gap: 12px;
  color: #1e293b;
  font-size: 1.5rem;
  font-weight: 600;
  margin-bottom: 24px;
  padding-bottom: 16px;
  border-bottom: 2px solid #e2e8f0;
}

/* Alert Messages */
.sponsor-studies-alert {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 16px;
  border-radius: 8px;
  margin-bottom: 16px;
  font-weight: 500;
}

.sponsor-studies-alert-error {
  background: #fef2f2;
  color: #dc2626;
  border: 1px solid #fecaca;
}

.sponsor-studies-alert-success {
  background: #f0fdf4;
  color: #16a34a;
  border: 1px solid #bbf7d0;
}

/* Form Layout */
.sponsor-studies-form {
  display: grid;
  gap: 24px;
}

.sponsor-studies-form-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 24px;
}

.sponsor-studies-form-group {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.sponsor-studies-form-group.full-width {
  grid-column: 1 / -1;
}

.sponsor-studies-label {
  font-weight: 600;
  color: #374151;
  font-size: 14px;
}

.sponsor-studies-required {
  color: #dc2626;
}

/* Tasks Section */
.sponsor-studies-tasks-section {
  border: 2px dashed #e2e8f0;
  border-radius: 12px;
  padding: 24px;
  background: #f8fafc;
}

.sponsor-studies-tasks-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.sponsor-studies-add-task-btn {
  display: flex;
  align-items: center;
  gap: 6px;
  background: #10b981;
  color: white;
  border: none;
  border-radius: 8px;
  padding: 8px 16px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.sponsor-studies-add-task-btn:hover {
  background: #059669;
  transform: translateY(-1px);
}

.sponsor-studies-tasks-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.sponsor-studies-task-item {
  background: white;
  border: 1px solid #e2e8f0;
  border-radius: 12px;
  padding: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
}

.sponsor-studies-task-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.sponsor-studies-task-title {
  color: #1e293b;
  font-weight: 600;
  margin: 0;
}

.sponsor-studies-remove-task-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  background: #ef4444;
  color: white;
  border: none;
  border-radius: 6px;
  padding: 6px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.sponsor-studies-remove-task-btn:hover {
  background: #dc2626;
  transform: scale(1.05);
}

.sponsor-studies-task-fields {
  display: grid;
  grid-template-columns: 1fr 2fr;
  gap: 16px;
}

.sponsor-studies-no-tasks {
  text-align: center;
  padding: 32px;
  color: #64748b;
  font-style: italic;
}

/* Form Actions */
.sponsor-studies-form-actions {
  display: flex;
  gap: 16px;
  justify-content: flex-end;
  padding-top: 24px;
  border-top: 1px solid #e2e8f0;
}

.sponsor-studies-cancel-btn {
  background: #f1f5f9;
  color: #475569;
  border: 1px solid #cbd5e1;
  border-radius: 8px;
  padding: 12px 24px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.sponsor-studies-cancel-btn:hover {
  background: #e2e8f0;
  border-color: #94a3b8;
}

.sponsor-studies-submit-btn {
  display: flex;
  align-items: center;
  gap: 8px;
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
  color: white;
  border: none;
  border-radius: 8px;
  padding: 12px 24px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 4px 12px rgba(16, 185, 129, 0.3);
}

.sponsor-studies-submit-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(16, 185, 129, 0.4);
}

.sponsor-studies-submit-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

/* Loading State */
.sponsor-studies-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 64px;
  color: #64748b;
  font-size: 18px;
  gap: 16px;
}

.sponsor-studies-loading svg {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

/* Empty State */
.sponsor-studies-empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 64px;
  background: white;
  border-radius: 16px;
  border: 2px dashed #e2e8f0;
  color: #64748b;
  text-align: center;
}

.sponsor-studies-empty-state svg {
  margin-bottom: 16px;
  opacity: 0.5;
}

/* Data Table Container */
.sponsor-studies-table-container {
  background: white;
  border-radius: 16px;
  padding: 24px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  border: 1px solid #e2e8f0;
}

/* Visits Section */
.sponsor-studies-visits-section {
  background: white;
  border-radius: 16px;
  padding: 24px;
  margin-top: 24px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  border: 1px solid #e2e8f0;
  animation: slideIn 0.3s ease-out;
}

.sponsor-studies-visits-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
  padding-bottom: 16px;
  border-bottom: 2px solid #e2e8f0;
}

.sponsor-studies-visits-title {
  color: #1e293b;
  font-size: 1.25rem;
  font-weight: 600;
  margin: 0;
}

.sponsor-studies-add-visit-btn {
  display: flex;
  align-items: center;
  gap: 8px;
  background: linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%);
  color: white;
  border: none;
  border-radius: 8px;
  padding: 10px 20px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 4px 12px rgba(139, 92, 246, 0.3);
}

.sponsor-studies-add-visit-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(139, 92, 246, 0.4);
}

.sponsor-studies-visits-list {
  display: grid;
  gap: 16px;
}

.sponsor-studies-visit-card {
  background: #f8fafc;
  border: 1px solid #e2e8f0;
  border-radius: 12px;
  padding: 20px;
  transition: all 0.2s ease;
}

.sponsor-studies-visit-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
  border-color: #cbd5e1;
}

.sponsor-studies-visit-content {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
}

.sponsor-studies-visit-info h3 {
  color: #1e293b;
  font-size: 1.1rem;
  font-weight: 600;
  margin: 0 0 8px 0;
}

.sponsor-studies-visit-info p {
  color: #64748b;
  margin: 4px 0;
  font-size: 14px;
}

.sponsor-studies-visit-actions {
  display: flex;
  gap: 8px;
}

.sponsor-studies-visit-action-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f1f5f9;
  color: #475569;
  border: 1px solid #cbd5e1;
  border-radius: 6px;
  padding: 8px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.sponsor-studies-visit-action-btn:hover {
  background: #e2e8f0;
  border-color: #94a3b8;
  transform: scale(1.05);
}

.sponsor-studies-visit-action-btn.delete:hover {
  background: #fef2f2;
  border-color: #fecaca;
  color: #dc2626;
}

.sponsor-studies-visits-empty {
  text-align: center;
  padding: 40px;
  color: #64748b;
}

.sponsor-studies-visits-empty p {
  margin-bottom: 16px;
  font-style: italic;
}

/* Responsive Design */
@media (max-width: 768px) {
  .sponsor-studies-container {
    padding: 16px;
  }
  
  .sponsor-studies-header {
    padding: 24px;
  }
  
  .sponsor-studies-header-content {
    flex-direction: column;
    align-items: stretch;
  }
  
  .sponsor-studies-form-row {
    grid-template-columns: 1fr;
  }
  
  .sponsor-studies-task-fields {
    grid-template-columns: 1fr;
  }
  
  .sponsor-studies-form-actions {
    flex-direction: column;
  }
  
  .sponsor-studies-visit-content {
    flex-direction: column;
    gap: 16px;
  }
  
  .sponsor-studies-visit-actions {
    align-self: flex-end;
  }
}

@media (max-width: 480px) {
  .sponsor-studies-title {
    font-size: 1.5rem;
  }
  
  .sponsor-studies-form-section {
    padding: 20px;
  }
  
  .sponsor-studies-tasks-section {
    padding: 16px;
  }
}

/* Focus States for Accessibility */
.sponsor-studies-create-btn:focus,
.sponsor-studies-submit-btn:focus,
.sponsor-studies-cancel-btn:focus,
.sponsor-studies-add-task-btn:focus,
.sponsor-studies-add-visit-btn:focus {
  outline: 2px solid #3b82f6;
  outline-offset: 2px;
}

.sponsor-studies-remove-task-btn:focus,
.sponsor-studies-visit-action-btn:focus {
  outline: 2px solid #3b82f6;
  outline-offset: 2px;
}

/* Smooth transitions for interactive elements */
* {
  transition: color 0.2s ease, background-color 0.2s ease, border-color 0.2s ease, transform 0.2s ease, box-shadow 0.2s ease;
}

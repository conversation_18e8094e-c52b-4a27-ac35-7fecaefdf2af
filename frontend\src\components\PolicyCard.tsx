import React from 'react';
import { useNavigate } from 'react-router-dom';
import { Policy } from '@/types/types';

interface PolicyCardProps {
  policy: Policy;
}

const PolicyCard: React.FC<PolicyCardProps> = ({ policy }) => {
  const navigate = useNavigate();

  const handleClick = () => {
    navigate(`/patient/policy-details/${policy.uuid}`);
  };

  return (
    <div className="policy-card">
      <h3 className="policy-card-title">{policy.title}</h3>
      <p className="policy-card-description">{policy.description}</p>
      <button className="policy-card-button" onClick={handleClick}>
        Open
      </button>
    </div>
  );
};

export default PolicyCard;

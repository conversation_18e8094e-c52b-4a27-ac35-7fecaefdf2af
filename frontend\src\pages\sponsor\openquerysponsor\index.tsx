import React, { useState, useEffect } from "react";
import { motion } from "framer-motion";
import {
  AlertCircle,
  Clock,
  Calendar,
  FileText,
  Eye,
  User,
  CheckCircle2,
  ChevronLeft,
  HelpCircle,
  Send
} from "lucide-react";
import { useGetQueryResponses, useCreateQueryResponse, useResolveFormSubmissionQuery } from "@/hooks/form.query";
import { useSponsorPatientsQuery } from "@/hooks/sponsorPatients.query";
import { formatDate } from "@/utils/date";
import { getFormSubmissionQueriesByPatient } from "@/services/api/form.service";
import DataTable, { Column, Action } from "@/components/common/DataTable";
import { useNavigate } from "react-router-dom";
import "./OpenQuerySponsor.css";

interface QueryResponse {
  uuid: string;
  message: string;
  created_at: string;
  responder: {
    first_name: string;
    last_name: string;
  };
  is_clarification: boolean;
}

interface FormSubmissionQuery {
  uuid: string;
  question_number: string;
  description: string;
  priority: 'low' | 'medium' | 'high';
  created_by: {
    identifier: string;
    first_name: string;
    last_name: string;
    email: string;
  };
  created_at: string;
  resolved_at: string | null;
  is_resolved: boolean;
  resolution_notes: string | null;
  assigned_to: string | null;
  responses: QueryResponse[];
  latest_response: QueryResponse | null;
  form_submission: {
    form_name: string;
    uuid: string;
  };
}

interface PaginatedResponse<T> {
  count: number;
  next: string | null;
  previous: string | null;
  results: T[];
}

interface PatientWithQueries {
  patient: {
    patient_uuid: string;
    patient_initials: string;
    patient_study_id: string;
    nhs_number: string;
    site_name: string;
    hospital_name: string;
    study_name: string;
    enrollment_status: string;
    enrollment_date: string;
  };
  queries: FormSubmissionQuery[];
}

// Extended query type with patient information for DataTable
interface QueryWithPatient extends FormSubmissionQuery {
  patient: {
    patient_uuid: string;
    patient_initials: string;
    patient_study_id: string;
    site_name: string;
  };
}

const OpenQuerySponsor: React.FC = () => {
  const navigate = useNavigate();
  const [selectedQuery, setSelectedQuery] = useState<FormSubmissionQuery | null>(null);
  const [queryResponses, setQueryResponses] = useState<Record<string, QueryResponse[]>>({});
  const [showResolveModal, setShowResolveModal] = useState(false);
  const [resolutionNotes, setResolutionNotes] = useState('');
  const [newResponse, setNewResponse] = useState<Record<string, string>>({});
  const [isClarification, setIsClarification] = useState<Record<string, boolean>>({});

  // Fetch all sponsor patients
  const { data: patientsResponse, isLoading: isLoadingPatients } = useSponsorPatientsQuery();

  // Get all patients with their open queries
  const [patientsWithQueries, setPatientsWithQueries] = useState<PatientWithQueries[]>([]);
  const [isLoadingQueries, setIsLoadingQueries] = useState(false);

  // Get query responses for selected query
  const {
    data: responsesData,
    isLoading: isLoadingResponses,
  } = useGetQueryResponses(selectedQuery ? { query_uuid: selectedQuery.uuid } : undefined);

  // Mutations
  const { mutate: createResponse } = useCreateQueryResponse();
  const { mutate: resolveQuery } = useResolveFormSubmissionQuery(selectedQuery?.uuid || '');

  // Fetch queries for all patients
  useEffect(() => {
    const fetchAllPatientQueries = async () => {
      if (!patientsResponse?.results) return;

      setIsLoadingQueries(true);
      const patientsWithQueriesData: PatientWithQueries[] = [];

      for (const patient of patientsResponse.results) {
        try {
          // Use the proper API service function that's configured for the backend
          const data = await getFormSubmissionQueriesByPatient(patient.patient_uuid, { is_resolved: false });
          const queries = data.results || [];
          if (queries.length > 0) {
            patientsWithQueriesData.push({
              patient,
              queries: queries as any // Type assertion to handle the mismatch
            });
          }
        } catch (error) {
          console.error(`Error fetching queries for patient ${patient.patient_uuid}:`, error);
        }
      }

      setPatientsWithQueries(patientsWithQueriesData);
      setIsLoadingQueries(false);
    };

    fetchAllPatientQueries();
  }, [patientsResponse]);

  // Process query responses
  useEffect(() => {
    if (responsesData && selectedQuery) {
      const paginatedResponses = responsesData as unknown as PaginatedResponse<QueryResponse>;
      const responses = paginatedResponses?.results || [];

      setQueryResponses((prev) => ({
        ...prev,
        [selectedQuery.uuid]: responses,
      }));
    }
  }, [responsesData, selectedQuery]);

  // Flatten all queries from all patients
  const allQueries = patientsWithQueries.flatMap(pwq =>
    pwq.queries.map(query => ({
      ...query,
      patient: pwq.patient
    }))
  ) as QueryWithPatient[];

  // Use all queries since we removed filters
  const filteredQueries = allQueries;

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'high': return '#ef4444';
      case 'medium': return '#f59e0b';
      case 'low': return '#10b981';
      default: return '#6b7280';
    }
  };

  // Define columns for DataTable
  const columns: Column<QueryWithPatient>[] = [
    {
      key: 'patient' as keyof QueryWithPatient,
      header: 'Patient',
      render: (_, row) => (
        <div className="patient-info">
          <div className="patient-details">
            <span className="patient-initials">{row?.patient?.patient_initials || 'N/A'}</span>
            <span className="patient-study-id">({row?.patient?.patient_study_id || 'N/A'})</span>
          </div>
          <div className="patient-site">{row?.patient?.site_name || 'N/A'}</div>
        </div>
      ),
      sortable: true,
    },
    {
      key: 'form_submission' as keyof QueryWithPatient,
      header: 'Form Name',
      render: (value) => {
        const formSubmission = value as any;
        return <span>{formSubmission?.form_name || 'Unknown Form'}</span>;
      },
      sortable: true,
    },
    {
      key: 'description' as keyof QueryWithPatient,
      header: 'Description',
      render: (value) => <span>{(value as string) || 'No description'}</span>,
      sortable: true,
      maxLength: 50,
    },
    {
      key: 'priority' as keyof QueryWithPatient,
      header: 'Priority',
      render: (value) => (
        <span
          className={`priority-badge priority-${value}`}
          style={{ backgroundColor: getPriorityColor(value as string) + '20', color: getPriorityColor(value as string) }}
        >
          <AlertCircle size={14} />
          {(value as string).toUpperCase()}
        </span>
      ),
      sortable: true,
    },
    {
      key: 'created_at' as keyof QueryWithPatient,
      header: 'Created',
      render: (value) => <span>{formatDate(value as string)}</span>,
      sortable: true,
    },
    {
      key: 'created_by' as keyof QueryWithPatient,
      header: 'Created By',
      render: (value) => {
        const creator = value as any;
        return <span>{creator ? `${creator.first_name} ${creator.last_name}` : 'N/A'}</span>;
      },
      sortable: true,
    },
    {
      key: 'is_resolved' as keyof QueryWithPatient,
      header: 'Status',
      render: () => (
        <span className="status-badge pending">
          <Clock size={14} />
          Open
        </span>
      ),
      sortable: true,
    },
  ];

  // Define actions for DataTable
  const actions: Action<QueryWithPatient>[] = [
    {
      icon: <Eye size={16} />,
      onClick: (row) => setSelectedQuery(row),
      tooltipText: 'View Details',
      className: 'view-btn',
    },
    {
      icon: <User size={16} />,
      onClick: (row) => {
        navigate(`/sponsor/study-patients/${row.patient.patient_uuid}`);
      },
      tooltipText: 'View Patient Details',
      className: 'external-btn',
    },
  ];

  const handleSubmitResponse = (queryUuid: string) => {
    if (!newResponse[queryUuid]?.trim()) return;

    createResponse(
      {
        query: queryUuid,
        message: newResponse[queryUuid],
        is_clarification: isClarification[queryUuid] || false,
      },
      {
        onSuccess: () => {
          setNewResponse((prev) => ({ ...prev, [queryUuid]: '' }));
          setIsClarification((prev) => ({ ...prev, [queryUuid]: false }));
          if (selectedQuery) {
            setQueryResponses((prev) => ({
              ...prev,
              [queryUuid]: [
                ...(prev[queryUuid] || []),
                {
                  uuid: Date.now().toString(),
                  message: newResponse[queryUuid],
                  created_at: new Date().toISOString(),
                  responder: {
                    first_name: 'You',
                    last_name: '',
                  },
                  is_clarification: isClarification[queryUuid] || false,
                },
              ],
            }));
          }
        },
      }
    );
  };

  const handleResolveQuery = () => {
    if (!selectedQuery) return;

    resolveQuery(
      { resolution_notes: resolutionNotes },
      {
        onSuccess: () => {
          setSelectedQuery((prev) => (prev ? { ...prev, is_resolved: true } : null));
          setShowResolveModal(false);
          setResolutionNotes('');
          // Refresh the queries data
          window.location.reload();
        },
      }
    );
  };

  const renderResolveModal = () => {
    if (!showResolveModal) return null;

    return (
      <div className="modal-overlay">
        <div className="modal-content">
          <h3>Resolve Query</h3>
          <p>Add any notes about how this query was resolved (optional)</p>
          <textarea
            value={resolutionNotes}
            onChange={(e) => setResolutionNotes(e.target.value)}
            placeholder="Enter resolution notes..."
            rows={4}
          />
          <div className="modal-actions">
            <button
              className="cancel-btn"
              onClick={() => {
                setShowResolveModal(false);
                setResolutionNotes('');
              }}
            >
              Cancel
            </button>
            <button className="resolve-btn" onClick={handleResolveQuery}>
              <CheckCircle2 size={16} />
              Resolve Query
            </button>
          </div>
        </div>
      </div>
    );
  };

  const renderQueryDetail = () => {
    if (!selectedQuery) return null;

    const responses = queryResponses[selectedQuery.uuid] || [];

    const formatQuestionNumber = (questionNumber: string) => {
      const [section, question] = questionNumber.split('-');
      return `Section ${section}, Question ${question}`;
    };

    return (
      <motion.div
        className="query-detail-view"
        initial={{ opacity: 0, x: 20 }}
        animate={{ opacity: 1, x: 0 }}
        transition={{ duration: 0.3 }}
      >
        <button className="back-button" onClick={() => setSelectedQuery(null)}>
          <ChevronLeft size={20} />
          Back to Queries
        </button>

        <div className="query-card">
          <div className="query-header">
            <div className="query-info">
              <h3 className="query-title">
                <AlertCircle size={16} className={`priority-${selectedQuery.priority}`} />
                {formatQuestionNumber(selectedQuery.question_number)}
              </h3>
              <p className="query-description">{selectedQuery.description}</p>
              <div className="query-creator">
                <span className="creator-label">Created by:</span>
                <span className="creator-name">
                  {selectedQuery.created_by.first_name} {selectedQuery.created_by.last_name}
                </span>
              </div>
              <div className="patient-info">
                <span className="patient-label">Patient:</span>
                <span className="patient-name">
                  {(selectedQuery as any).patient?.patient_initials} ({(selectedQuery as any).patient?.patient_study_id})
                </span>
              </div>
            </div>
            <div className="query-meta">
              <span className="query-date">
                <Calendar size={14} />
                {formatDate(selectedQuery.created_at)}
              </span>
              <span className={`status-badge ${selectedQuery.is_resolved ? 'resolved' : 'pending'}`}>
                {selectedQuery.is_resolved ? (
                  <>
                    <CheckCircle2 size={14} />
                    Resolved
                  </>
                ) : (
                  <>
                    <Clock size={14} />
                    Pending
                  </>
                )}
              </span>
              {!selectedQuery.is_resolved && (
                <button
                  className="resolve-query-btn"
                  onClick={() => setShowResolveModal(true)}
                >
                  <CheckCircle2 size={16} />
                  Resolve Query
                </button>
              )}
            </div>
          </div>

          <div className="responses-section">
            {isLoadingResponses ? (
              <div className="loading-responses">Loading responses...</div>
            ) : responses.length === 0 ? (
              <div className="no-responses">No responses yet</div>
            ) : (
              responses.map((response: QueryResponse) => (
                <div key={response.uuid} className="response-item">
                  <div className="response-header">
                    <div className="response-meta">
                      <span className="responder-name">
                        {response.responder.first_name} {response.responder.last_name}
                      </span>
                      {response.is_clarification && (
                        <span className="clarification-badge" title="This is a clarification">
                          <HelpCircle size={14} />
                          Clarification
                        </span>
                      )}
                    </div>
                    <span className="response-date">
                      <Calendar size={14} />
                      {formatDate(response.created_at)}
                    </span>
                  </div>
                  <p className="response-message">{response.message}</p>
                </div>
              ))
            )}
          </div>

          {selectedQuery.is_resolved ? (
            <div className="resolved-query-notice">
              <CheckCircle2 size={20} />
              <p>This query has been resolved and cannot be edited.</p>
              {selectedQuery.resolution_notes && (
                <div className="resolution-notes">
                  <h4>Resolution Notes:</h4>
                  <p>{selectedQuery.resolution_notes}</p>
                </div>
              )}
            </div>
          ) : (
            <div className="response-form">
              <div className="response-input-container">
                <textarea
                  value={newResponse[selectedQuery.uuid] || ''}
                  onChange={(e) =>
                    setNewResponse((prev) => ({
                      ...prev,
                      [selectedQuery.uuid]: e.target.value,
                    }))
                  }
                  placeholder="Type your response..."
                  rows={3}
                />
                <div className="response-options">
                  <label className="clarification-toggle">
                    <input
                      type="checkbox"
                      checked={isClarification[selectedQuery.uuid] || false}
                      onChange={(e) =>
                        setIsClarification((prev) => ({
                          ...prev,
                          [selectedQuery.uuid]: e.target.checked,
                        }))
                      }
                    />
                    <span className="toggle-label">
                      <HelpCircle size={16} />
                      This is a clarification
                    </span>
                  </label>
                </div>
              </div>
              <button
                className="submit-response-btn"
                onClick={() => handleSubmitResponse(selectedQuery.uuid)}
                disabled={!newResponse[selectedQuery.uuid]?.trim()}
              >
                <Send size={16} />
                Send Response
              </button>
            </div>
          )}
        </div>
      </motion.div>
    );
  };

  if (isLoadingPatients || isLoadingQueries) {
    return (
      <motion.div
        className="open-query-sponsor-container"
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
      >
        <div className="loading-container">
          <div className="loading-spinner"></div>
          <p>Loading open queries...</p>
        </div>
      </motion.div>
    );
  }

  return (
    <motion.div
      className="open-query-sponsor-container"
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
    >
      {/* Header Section */}
      <div className="open-query-header">
        <div className="header-content">
          <div className="header-left">
            <h1>Open Queries</h1>
            <p>Manage and view all unresolved queries across your studies</p>
          </div>
          <div className="header-stats">
            <div className="stat-card">
              <span className="stat-number">{allQueries.length}</span>
              <span className="stat-label">Total Open Queries</span>
            </div>
          </div>
        </div>
      </div>



      {/* Queries DataTable */}
      <div className="queries-table-container">
                 {filteredQueries.length === 0 ? (
           <div className="no-queries">
             <FileText size={48} />
             <h3>No Open Queries Found</h3>
             <p>There are no unresolved queries at the moment.</p>
           </div>
         ) : (
          <DataTable<QueryWithPatient>
            data={filteredQueries}
            columns={columns}
            actions={actions}
            noDataMessage="No open queries found"
            globalFilterPlaceholder="Search queries..."
            defaultItemsPerPage={10}
          />
        )}
      </div>

             {/* Summary Footer */}
       {filteredQueries.length > 0 && (
         <div className="queries-summary">
           <p>
             Showing {filteredQueries.length} of {allQueries.length} open queries
           </p>
         </div>
       )}

      {/* Query Detail View */}
      {selectedQuery && renderQueryDetail()}

      {/* Resolve Modal */}
      {renderResolveModal()}
    </motion.div>
  );
};

export default OpenQuerySponsor;
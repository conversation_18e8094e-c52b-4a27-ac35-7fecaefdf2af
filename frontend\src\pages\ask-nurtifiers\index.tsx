import React from "react";
import {
  usePendingConversations,
  useInProgressConversations,
  useForwardedConversations,
  useRejectedConversations,
  useReimbursedConversations,
  useSetInProgress,
  useConversationLogs,
} from "@/hooks/conversation.query";
import LightFooter from "@/shared/LightFooter";
import "./asknurtifiers.css";
import { motion } from "framer-motion";
import { BriefcaseMedical, ChevronDown, ChevronUp } from "lucide-react";
import { useNavigate } from "react-router-dom";
import { Conversation, ConversationLog } from "@/services/api/types";

type TabType = "pending" | "inprogress" | "forwarded" | "rejected" | "reimbursed";

interface ConversationCardProps {
  conversation: Conversation;
  index: number;
  activeTab: TabType;
  processingConversation: string | null;
  onViewDetails: (uuid: string) => void;
  onProcess: (uuid: string) => void;
}

const ConversationCard: React.FC<ConversationCardProps> = ({
  conversation,
  index,
  activeTab,
  processingConversation,
  onViewDetails,
  onProcess,
}) => {
  const [isLogsExpanded, setIsLogsExpanded] = React.useState(false);
  const { data: logs } = useConversationLogs(conversation.uuid);

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString();
  };

  const formatStatus = (status: string) => {
    if (status === "inprogress") {
      return "In Progress";
    }
    return status
      .split('_')
      .map(word => word.charAt(0).toUpperCase() + word.slice(1))
      .join(' ');
  };

  const formatLogDate = (dateString: string) => {
    return new Date(dateString).toLocaleString();
  };

  const renderLogs = (logs: ConversationLog[]) => {
    return (
      <div className="conversation-logs">
        {logs.map((log) => (
          <div key={log.uuid} className="log-item">
            <div className="log-header">
              <span className="log-field">{log.field_changed}</span>
              <span className="log-changes">
                {log.old_value} → {log.new_value}
              </span>
            </div>
            <div className="log-details">
              <span className="log-user">Changed by: {log.changed_by}</span>
              <span className="log-time">{formatLogDate(log.changed_at)}</span>
            </div>
          </div>
        ))}
      </div>
    );
  };

  return (
    <motion.div
      className="ask-card"
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5, delay: index * 0.1 }}
    >
      <div className="ask-card-icon">
        <BriefcaseMedical />
      </div>
      <div className="ask-card-content">
        <div className="ask-card-header">
          <h3 className="ask-card-title">
            {conversation?.patient_details?.first_name} {conversation?.patient_details?.last_name}
          </h3>
          <span className={`ask-card-status ${conversation.status}`}>
            {formatStatus(conversation.status)}
          </span>
        </div>
        <div className="ask-card-details">
          {conversation?.patient_details?.email && (
            <p><strong>Email:</strong> {conversation.patient_details.email}</p>
          )}
          {conversation?.treated_by_details && (
            <p>
              <strong>Treated By:</strong> {conversation.treated_by_details.first_name} {conversation.treated_by_details.last_name}
            </p>
          )}
          {conversation?.last_update_person && (
            <p><strong>Last Update:</strong> {conversation.last_update_person}</p>
          )}
          <p><strong>Submitted:</strong> {formatDate(conversation.created_at)}</p>
        </div>
        <div className="ask-card-actions">
          <button
            className="ask-card-button view"
            onClick={() => onViewDetails(conversation.uuid)}
          >
            View Details
          </button>
          {activeTab === "pending" && (
            <button
              className="ask-card-button respond"
              onClick={() => onProcess(conversation.uuid)}
              disabled={processingConversation === conversation.uuid}
            >
              {processingConversation === conversation.uuid ? "Processing..." : "Process"}
            </button>
          )}
          <button
            className="ask-card-button logs"
            onClick={() => setIsLogsExpanded(!isLogsExpanded)}
          >
            {isLogsExpanded ? (
              <>
                Hide Logs <ChevronUp size={16} />
              </>
            ) : (
              <>
                Show Logs <ChevronDown size={16} />
              </>
            )}
          </button>
        </div>
        {isLogsExpanded && logs && (
          <div className="logs-container">
            {logs.length > 0 ? (
              renderLogs(logs)
            ) : (
              <p className="no-logs">No logs available</p>
            )}
          </div>
        )}
      </div>
    </motion.div>
  );
};

const AskNurtifiers: React.FC = () => {
  const [activeTab, setActiveTab] = React.useState<TabType>("pending");
  const [processingConversation, setProcessingConversation] = React.useState<string | null>(null);

  const navigate = useNavigate();
  const setInProgressMutation = useSetInProgress();

  const formatStatus = (status: string) => {
    if (status === "inprogress") {
      return "In Progress";
    }
    return status
      .split('_')
      .map(word => word.charAt(0).toUpperCase() + word.slice(1))
      .join(' ');
  };

  // Fetch conversations for each status
  const { data: pendingConversations, isLoading: isPendingLoading } = usePendingConversations();
  const { data: inProgressConversations, isLoading: isInProgressLoading } = useInProgressConversations();
  const { data: forwardedConversations, isLoading: isForwardedLoading } = useForwardedConversations();
  const { data: rejectedConversations, isLoading: isRejectedLoading } = useRejectedConversations();
  const { data: reimbursedConversations, isLoading: isReimbursedLoading } = useReimbursedConversations();

  // Get current conversations based on active tab
  const getCurrentConversations = () => {
    switch (activeTab) {
      case "pending":
        return { data: pendingConversations, isLoading: isPendingLoading };
      case "inprogress":
        return { data: inProgressConversations, isLoading: isInProgressLoading };
      case "forwarded":
        return { data: forwardedConversations, isLoading: isForwardedLoading };
      case "rejected":
        return { data: rejectedConversations, isLoading: isRejectedLoading };
      case "reimbursed":
        return { data: reimbursedConversations, isLoading: isReimbursedLoading };
    }
  };

  const { data: currentConversations, isLoading } = getCurrentConversations();

  const handleViewDetails = (uuid: string) => {
    navigate(`/conversation-details/${uuid}`);
  };

  const handleProcess = async (uuid: string) => {
    try {
      setProcessingConversation(uuid);
      await setInProgressMutation.mutateAsync(uuid);
      if (activeTab === "pending") {
        setActiveTab("inprogress");
      }
    } catch (error) {
      console.error("Error setting conversation to in-progress:", error);
    } finally {
      setProcessingConversation(null);
    }
  };

  return (
    <div className="ask-main-content bg-light-4">
      <div className="ask-content-wrapper">
        <div className="ask-dashboard__content">
          <div className="container-fluid px-0">
            {/* Tabs Section */}
            <div className="ask-tabs-wrapper">
              <button
                className={`ask-tab-button ${activeTab === "pending" ? "active" : ""}`}
                onClick={() => setActiveTab("pending")}
              >
                Pending
              </button>
              <button
                className={`ask-tab-button ${activeTab === "inprogress" ? "active" : ""}`}
                onClick={() => setActiveTab("inprogress")}
              >
                In Progress
              </button>
              <button
                className={`ask-tab-button ${activeTab === "forwarded" ? "active" : ""}`}
                onClick={() => setActiveTab("forwarded")}
              >
                Forwarded to Finance
              </button>
              <button
                className={`ask-tab-button ${activeTab === "reimbursed" ? "active" : ""}`}
                onClick={() => setActiveTab("reimbursed")}
              >
                Reimbursed
              </button>
              <button
                className={`ask-tab-button ${activeTab === "rejected" ? "active" : ""}`}
                onClick={() => setActiveTab("rejected")}
              >
                Rejected
              </button>
            </div>

            {/* Header Section */}
            <motion.div
              className="ask-header"
              initial={{ opacity: 0, y: -20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5 }}
            >
              <h1 className="ask-title">
                {activeTab === "forwarded"
                  ? "Forwarded to Finance"
                  : formatStatus(activeTab)} Refunds
              </h1>
              <p className="ask-subtitle">Review {activeTab === "forwarded" ? "forwarded to finance" : activeTab} forms</p>
            </motion.div>

            {/* Cards Grid */}
            <div className="ask-cards-grid">
              {isLoading ? (
                <div className="loading-message">Loading conversations...</div>
              ) : currentConversations?.length ? (
                currentConversations.map((conversation: Conversation, index: number) => (
                  <ConversationCard
                    key={conversation.uuid}
                    conversation={conversation}
                    index={index}
                    activeTab={activeTab}
                    processingConversation={processingConversation}
                    onViewDetails={handleViewDetails}
                    onProcess={handleProcess}
                  />
                ))
              ) : (
                <div className="no-conversations">
                  No {activeTab} refunds found.
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
      <LightFooter />
    </div>
  );
};

export default AskNurtifiers;
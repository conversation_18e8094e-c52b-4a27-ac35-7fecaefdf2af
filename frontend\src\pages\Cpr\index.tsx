import Preloader from "@/components/common/Preloader";
import "./cpr.css";
import LightFooter from "@/shared/LightFooter";
import DashboardSidebar from "@/components/common/DashboardSidebar";
import MobileSidebarIcons from "@/components/common/MobileSidebarIcons";
import { BookUser, UserSearch, Syringe, BriefcaseMedical, HeartPulse, ScanSearch, BookHeart, Plus, Activity, Hospital, SquareActivity } from "lucide-react";
import CprCard from "./CprCard";
import Table from "@/components/Table";

type MenuItem = {
  icon: JSX.Element;
  label: string;
  path: string;
  onClick?: () => void;
};

import { useState } from "react";

export default function Cpr() {
  const [isSidebarCollapsed, setSidebarCollapsed] = useState(() => {
    const stored = localStorage.getItem('sidebarCollapsed') === 'true';
    return stored || window.innerWidth <= 991;
  });

  const menuItems: MenuItem[] = [
    { 
      icon: <BookUser size={20} />, 
      label: 'Patient Details',
      path: '/',
    },
    { 
      icon: <UserSearch size={20} />, 
      label: 'ABG/VBG',
      path: '/',
    },
    { 
      icon: <Syringe size={20} />, 
      label: 'Add medication',
      path: '/',
    },
    { 
      icon: <BriefcaseMedical size={20} />, 
      label: 'Intervention',
      path: '/',
    },
    { 
      icon: <HeartPulse size={20} />, 
      label: 'ECG',
      path: '/',
    },
    { 
      icon: <ScanSearch size={20} />, 
      label: 'Reversible Causes',
      path: '/',
    },
    { 
      icon: <BookHeart size={20} />, 
      label: 'Clinical Notes',
      path: '/',
    }
  ];

  const table1Headers = ["n°", "time", "dose", "route", "action"];
  const table1Data = [
    { "n°": "1", "time": "10:00", "dose": "5mg", "route": "IV", "action": <button>Action</button> },
    { "n°": "2", "time": "10:05", "dose": "10mg", "route": "IM", "action": <button>Action</button> },
    { "n°": "3", "time": "10:10", "dose": "15mg", "route": "IV", "action": <button>Action</button> },
    { "n°": "4", "time": "10:15", "dose": "20mg", "route": "IV", "action": <button>Action</button> },
    { "n°": "5", "time": "10:20", "dose": "25mg", "route": "IM", "action": <button>Action</button> },
    { "n°": "6", "time": "10:25", "dose": "30mg", "route": "IV", "action": <button>Action</button> },
  ];

  const table2Headers = ["n°", "time", "pulse", "Rythm", "action"];
  const table2Data = [
    { "n°": "1", "time": "10:00", "pulse": "80", "Rythm": "Normal", "action": <button>Action</button> },
    { "n°": "2", "time": "10:05", "pulse": "85", "Rythm": "Normal", "action": <button>Action</button> },
    { "n°": "3", "time": "10:10", "pulse": "90", "Rythm": "Irregular", "action": <button>Action</button> },
    { "n°": "4", "time": "10:15", "pulse": "95", "Rythm": "Normal", "action": <button>Action</button> },
    { "n°": "5", "time": "10:20", "pulse": "100", "Rythm": "Irregular", "action": <button>Action</button> },
    { "n°": "6", "time": "10:25", "pulse": "105", "Rythm": "Normal", "action": <button>Action</button> },
  ];

  const table3Headers = ["n°", "time", "dose", "route", "action"];
  const table3Data = [
    { "n°": "1", "time": "10:15", "dose": "20mg", "route": "IV", "action": <button>Action</button> },
    { "n°": "2", "time": "10:20", "dose": "25mg", "route": "IM", "action": <button>Action</button> },
    { "n°": "3", "time": "10:25", "dose": "30mg", "route": "IV", "action": <button>Action</button> },
    { "n°": "4", "time": "10:30", "dose": "35mg", "route": "IV", "action": <button>Action</button> },
    { "n°": "5", "time": "10:35", "dose": "40mg", "route": "IM", "action": <button>Action</button> },
    { "n°": "6", "time": "10:40", "dose": "45mg", "route": "IV", "action": <button>Action</button> },
  ];

  return (
    <div className="main-content bg-light-4">
      <Preloader />
      <div className="content-wrapper js-content-wrapper">
        <div className="dashboard__content bg-light-4">
          <div className="container-fluid px-0">
            <div className="row y-gap-30">

              <div className={isSidebarCollapsed ? "col-xl-1 col-lg-2" : "col-xl-3 col-lg-4"}>
                <DashboardSidebar
                  menuItems={menuItems}
                  isCollapsed={isSidebarCollapsed}
                  onCollapseChange={(collapsed) => {
                    setSidebarCollapsed(collapsed);
                    localStorage.setItem('sidebarCollapsed', collapsed.toString());
                  }}
                />
              </div>

              <div className={isSidebarCollapsed ? "col-xl-11 col-lg-10" : "col-xl-9 col-lg-8"} > 
                <div style={{ marginTop: "30px", paddingBottom: "80px", textAlign: "center" }}>
                  <h1 style={{ fontSize: "32px", fontWeight: "bold", color: "#071952" }}>
                    CPR Time
                  </h1>
                  <div className="cpr-timer-text">00 : 00 : 00</div>
                  <div className="card-grid">
                    <CprCard
                      duration={60}
                      text1="More than 5 minutes from last dose!"
                      text2="Time from last Epinephrine dose: 0:06 minutes."
                      text3="Number of dose given: 0 "
                      buttonText="Epinephrine Given"
                      buttonIconLeft={<Plus size={40} />}
                      buttonIconRight={<Syringe size={40} />}
                      onButtonClick={() => alert("Epinephrine Administered")}
                    />
                    <CprCard
                      duration={120}
                      text1="More than 5 minutes from last check!"
                      text2="Time since last check: 2 minutes"
                      text3="Next check in: 1 minute"
                      buttonText="Check"
                      buttonIconLeft={<Plus size={40} />}
                      buttonIconRight={<HeartPulse size={40} />}
                      onButtonClick={() => alert("Rhythm Checked")}
                    />
                    <CprCard
                      text1="Shockable Rhythm"
                      text2="Time since last shock: 3 minutes"
                      text3="Next shock in: 2 minutes"
                      buttonText="Shock"
                      buttonIconLeft={<Plus size={40} />}
                      buttonIconRight={<Activity size={40} />}
                      onButtonClick={() => alert("Shock Administered")}
                      secondButtonText="Amiodarone"
                      secondButtonIconLeft={<Plus size={40} />}
                      secondButtonIconRight={<Hospital size={35} />}
                      onSecondButtonClick={() => alert("Shock Cancelled")}
                    />
                  </div>
                  <div className="table-container">
                    <div className="table-row">
                      <Table headers={table1Headers} data={table1Data} tableIndex={0} />
                      <Table headers={table2Headers} data={table2Data} tableIndex={1} />
                      <Table headers={table3Headers} data={table3Data} tableIndex={2} />
                    </div>
                  </div>
                  <div className="rosc-button-container">
                    <button className="rosc-button">
                      <SquareActivity size={60} className="icon-left" />
                      <span className="button-text">ROSC Achieved</span>
                      <SquareActivity size={60} className="icon-right" />
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <MobileSidebarIcons menuItems={menuItems} />
        <LightFooter />
      </div>
    </div>
  );
}

import { useState, useMemo } from "react";
import { Search, Filter, Check, X, Clock, User<PERSON>he<PERSON>, AlertCircle } from "lucide-react";
import DataTable from "@/components/common/DataTable";
import { useAllPIAssignmentsQuery, useAcceptPIAssignmentMutation, useRejectPIAssignmentMutation } from "@/hooks/study.query";
import { PIAssignment } from "@/services/api/types";
import "./pi-assignments-tracker.css";

interface PIAssignmentsTrackerProps {
  departmentUuid?: string;
}

export default function PIAssignmentsTracker({ departmentUuid }: PIAssignmentsTrackerProps) {
  const [searchTerm, setSearchTerm] = useState("");
  const [statusFilter, setStatusFilter] = useState<string>("all");
  const [confirmationModal, setConfirmationModal] = useState<{
    isOpen: boolean;
    action: "accept" | "reject" | null;
    assignment: PIAssignment | null;
  }>({ isOpen: false, action: null, assignment: null });
  
  const { data: piAssignments, isLoading } = useAllPIAssignmentsQuery(departmentUuid || "");
  const acceptPIAssignmentMutation = useAcceptPIAssignmentMutation();
  const rejectPIAssignmentMutation = useRejectPIAssignmentMutation();

  // Filter assignments based on search term and status
  const filteredAssignments = useMemo(() => {
    // Ensure piAssignments is an array before filtering
    const assignments = Array.isArray(piAssignments) ? piAssignments : [];
    
    if (assignments.length === 0) return [];

    return assignments.filter((assignment: PIAssignment) => {
      const matchesSearch = 
        assignment.study_name?.toLowerCase().includes(searchTerm.toLowerCase()) ||
        assignment.investigator_name?.toLowerCase().includes(searchTerm.toLowerCase()) ||
        assignment.department_name?.toLowerCase().includes(searchTerm.toLowerCase()) ||
        assignment.hospital_name?.toLowerCase().includes(searchTerm.toLowerCase());

      const matchesStatus = statusFilter === "all" || assignment.status === statusFilter;

      return matchesSearch && matchesStatus;
    });
  }, [piAssignments, searchTerm, statusFilter]);

  const handleAcceptAssignment = (assignment: PIAssignment) => {
    setConfirmationModal({
      isOpen: true,
      action: "accept",
      assignment
    });
  };

  const handleRejectAssignment = (assignment: PIAssignment) => {
    setConfirmationModal({
      isOpen: true,
      action: "reject",
      assignment
    });
  };

  const handleConfirmAction = () => {
    if (!confirmationModal.assignment || !confirmationModal.action) return;

    const { assignment, action } = confirmationModal;

    if (action === "accept") {
      acceptPIAssignmentMutation.mutate(assignment.uuid, {
        onSuccess: () => {
          setConfirmationModal({ isOpen: false, action: null, assignment: null });
        }
      });
    } else if (action === "reject") {
      rejectPIAssignmentMutation.mutate(assignment.uuid, {
        onSuccess: () => {
          setConfirmationModal({ isOpen: false, action: null, assignment: null });
        }
      });
    }
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case "pending":
        return (
          <span className="status-badge pending">
            <Clock size={14} />
            Pending
          </span>
        );
      case "accepted":
        return (
          <span className="status-badge accepted">
            <Check size={14} />
            Accepted
          </span>
        );
      case "rejected":
        return (
          <span className="status-badge rejected">
            <X size={14} />
            Rejected
          </span>
        );
      default:
        return (
          <span className="status-badge unknown">
            <AlertCircle size={14} />
            Unknown
          </span>
        );
    }
  };

  // Define actions for the DataTable
  const actions = useMemo(() => [
    {
      icon: <Check size={16} />,
      tooltipText: "Accept Assignment",
      onClick: (assignment: PIAssignment) => handleAcceptAssignment(assignment),
      className: "action-accept",
      disabled: (assignment: PIAssignment) => assignment.status !== "pending"
    },
    {
      icon: <X size={16} />,
      tooltipText: "Reject Assignment",
      onClick: (assignment: PIAssignment) => handleRejectAssignment(assignment),
      className: "action-reject",
      disabled: (assignment: PIAssignment) => assignment.status !== "pending"
    }
  ], []);

  const columns = useMemo(() => [
    {
      key: "study_name" as keyof PIAssignment,
      header: "Study",
      sortable: true,
      render: (value: unknown): React.ReactNode => {
        return value ? String(value) : "N/A";
      },
    },
    {
      key: "investigator_name" as keyof PIAssignment,
      header: "Principal Investigator",
      sortable: true,
      render: (value: unknown): React.ReactNode => {
        return value ? String(value) : "N/A";
      },
    },
    {
      key: "investigator_email" as keyof PIAssignment,
      header: "Investigator Email",
      sortable: true,
      render: (value: unknown): React.ReactNode => {
        return value ? String(value) : "N/A";
      },
    },
    {
      key: "investigator_speciality" as keyof PIAssignment,
      header: "Speciality",
      sortable: true,
      render: (value: unknown): React.ReactNode => {
        return value ? String(value) : "N/A";
      },
    },
    {
      key: "department_name" as keyof PIAssignment,
      header: "Department",
      sortable: true,
      render: (value: unknown): React.ReactNode => {
        return value ? String(value) : "N/A";
      },
    },
    {
      key: "hospital_name" as keyof PIAssignment,
      header: "Hospital",
      sortable: true,
      render: (value: unknown): React.ReactNode => {
        return value ? String(value) : "N/A";
      },
    },
    {
      key: "status" as keyof PIAssignment,
      header: "Status",
      sortable: true,
      render: (value: unknown): React.ReactNode => {
        return getStatusBadge(String(value));
      },
    },
    {
      key: "assigned_at" as keyof PIAssignment,
      header: "Assigned Date",
      sortable: true,
      render: (value: unknown): React.ReactNode => {
        return value ? new Date(String(value)).toLocaleDateString() : "N/A";
      },
    },
    {
      key: "responded_at" as keyof PIAssignment,
      header: "Response Date",
      sortable: true,
      render: (value: unknown): React.ReactNode => {
        return value ? new Date(String(value)).toLocaleDateString() : "N/A";
      },
    }
  ], []);

  const summaryStats = useMemo(() => {
    // Ensure piAssignments is an array before calculating stats
    const assignments = Array.isArray(piAssignments) ? piAssignments : [];
    
    return {
      total: assignments.length,
      pending: assignments.filter((a: PIAssignment) => a.status === "pending").length,
      accepted: assignments.filter((a: PIAssignment) => a.status === "accepted").length,
      rejected: assignments.filter((a: PIAssignment) => a.status === "rejected").length,
    };
  }, [piAssignments]);

  if (isLoading) {
    return (
      <div className="pi-assignments-tracker">
        <div className="loading-message">Loading PI assignments...</div>
      </div>
    );
  }

  return (
    <div className="pi-assignments-tracker">
      <div className="tracker-header">
        <div className="header-content">
          <h1>
            <UserCheck size={24} style={{ marginRight: "10px" }} />
            PI Assignment Tracker
          </h1>
          <p>Track and manage Principal Investigator assignments across all studies</p>
        </div>
      </div>

      {/* Summary Cards */}
      <div className="summary-cards">
        <div className="summary-card total">
          <div className="card-icon">
            <UserCheck size={24} />
          </div>
          <div className="card-content">
            <h3>{summaryStats.total}</h3>
            <p>Total Assignments</p>
          </div>
        </div>
        <div className="summary-card pending">
          <div className="card-icon">
            <Clock size={24} />
          </div>
          <div className="card-content">
            <h3>{summaryStats.pending}</h3>
            <p>Pending Approval</p>
          </div>
        </div>
        <div className="summary-card accepted">
          <div className="card-icon">
            <Check size={24} />
          </div>
          <div className="card-content">
            <h3>{summaryStats.accepted}</h3>
            <p>Accepted</p>
          </div>
        </div>
        <div className="summary-card rejected">
          <div className="card-icon">
            <X size={24} />
          </div>
          <div className="card-content">
            <h3>{summaryStats.rejected}</h3>
            <p>Rejected</p>
          </div>
        </div>
      </div>

      {/* Filters and Search */}
      <div className="tracker-controls">
        <div className="search-container">
          <Search size={18} className="search-icon" />
          <input
            type="text"
            placeholder="Search by study, investigator, department, or hospital..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="search-input"
          />
        </div>
        <div className="filter-container">
          <Filter size={18} className="filter-icon" />
          <select
            value={statusFilter}
            onChange={(e) => setStatusFilter(e.target.value)}
            className="status-filter"
          >
            <option value="all">All Status</option>
            <option value="pending">Pending</option>
            <option value="accepted">Accepted</option>
            <option value="rejected">Rejected</option>
          </select>
        </div>
      </div>

      {/* Data Table */}
      <div className="tracker-table-container">
        <DataTable
          data={filteredAssignments}
          columns={columns}
          actions={actions}
          noDataMessage="No PI assignments found"
          defaultItemsPerPage={10}
        />
      </div>

      {/* Confirmation Modal */}
      {confirmationModal.isOpen && (
        <div className="modal-overlay">
          <div className="confirmation-modal">
            <div className="modal-header">
              <h2>
                {confirmationModal.action === "accept" ? "Accept" : "Reject"} PI Assignment
              </h2>
              <button
                className="close-btn"
                onClick={() => setConfirmationModal({ isOpen: false, action: null, assignment: null })}
              >
                <X size={20} />
              </button>
            </div>
            <div className="modal-content">
              <p>
                Are you sure you want to{" "}
                <strong>{confirmationModal.action === "accept" ? "accept" : "reject"}</strong> the PI assignment for:
              </p>
              <div className="assignment-details">
                <p><strong>Study:</strong> {confirmationModal.assignment?.study_name}</p>
                <p><strong>Investigator:</strong> {confirmationModal.assignment?.investigator_name}</p>
                <p><strong>Department:</strong> {confirmationModal.assignment?.department_name}</p>
                <p><strong>Hospital:</strong> {confirmationModal.assignment?.hospital_name}</p>
              </div>
            </div>
            <div className="modal-actions">
              <button
                className="cancel-btn"
                onClick={() => setConfirmationModal({ isOpen: false, action: null, assignment: null })}
              >
                Cancel
              </button>
              <button
                className={`confirm-btn ${confirmationModal.action === "accept" ? "accept" : "reject"}`}
                onClick={handleConfirmAction}
                disabled={acceptPIAssignmentMutation.isPending || rejectPIAssignmentMutation.isPending}
              >
                {acceptPIAssignmentMutation.isPending || rejectPIAssignmentMutation.isPending
                  ? "Processing..."
                  : confirmationModal.action === "accept"
                  ? "Accept Assignment"
                  : "Reject Assignment"}
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
} 
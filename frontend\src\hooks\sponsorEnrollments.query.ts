import { useQuery } from "@tanstack/react-query";
import { getSponsorEnrollmentsByDepartment } from "@/services/api/sponsorEnrollments.service";

export const SPONSOR_ENROLLMENTS_KEYS = {
  GET_ALL: "sponsor-enrollments",
};

export const useSponsorEnrollmentsQuery = () => {
  return useQuery({
    queryKey: [SPONSOR_ENROLLMENTS_KEYS.GET_ALL],
    queryFn: getSponsorEnrollmentsByDepartment,
    refetchOnWindowFocus: false,
    staleTime: 1000 * 60 * 5, // 5 minutes
  });
};
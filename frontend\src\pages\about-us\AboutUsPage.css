/* Landing Page Styles */
:root {
  --primary-dark: #071952;
  --primary: #37B7C3;
  --primary-light: #37B7C3;
  --background-light: #EBF4F6;
  --color-purple-1: #37B7C3; /* For compatibility with HeroSixSection */
}




/* Nurses Overview Section */
.nurses-overview-section {
  background: #fff;
  padding: 0 0 60px 0;
}

.nurses-overview-container {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 40px;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 24px;
}

.nurses-overview-content {
  flex: 1 1 0;
  max-width: 48%;
}

.nurses-overview-title {
  font-size: 36px;
  font-weight: 700;
  color: #071952;
  margin-bottom: 24px;
}

.nurses-overview-description {
  font-size: 16px;
  color: #333;
  line-height: 1.7;
}

.nurses-overview-image {
  flex: 1 1 0;
  max-width: 48%;
  display: flex;
  align-items: center;
  justify-content: flex-end;
}

.nurses-image {
  max-width: 100%;
  height: auto;
  display: block;
  border-radius: 0;
  box-shadow: none;
}

/* Responsive styles */
@media (max-width: 992px) {
  .nurses-overview-container {
    flex-direction: column;
    gap: 32px;
    padding: 0 12px;
  }
  .nurses-overview-content,
  .nurses-overview-image {
    max-width: 100%;
  }
  .nurses-overview-image {
    justify-content: center;
  }
  .nurses-overview-title {
    font-size: 28px;
  }
}

@media (max-width: 600px) {
  .nurses-overview-section {
    padding: 40px 0 30px 0;
  }
  .nurses-overview-title {
    font-size: 22px;
    margin-bottom: 16px;
  }
  .nurses-overview-description {
    font-size: 14px;
  }
}

/* Career Section */
.career-section {
  background: white;
  padding: 80px 0;
}

.career-header {
  text-align: center;
  margin-bottom: 60px;
  max-width: 800px;
  margin-left: auto;
  margin-right: auto;
}

.career-title {
  font-size: 36px;
  font-weight: 800;
  color: #090914;
  margin-bottom: 20px;
  line-height: 1.2;
}

.career-subtitle {
  font-size: 16px;
  color: #090914;
  line-height: 1.6;
  max-width: 700px;
  font-weight: 500;
  margin: 0 auto;
}

.career-roles-container {
  max-width: 800px;
  margin: 0 auto;
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.career-role-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 16px;
  padding: 24px;
  border: 1px solid #e9ecef;
  border-radius: 20px;
  flex-wrap: wrap; /* allow wrapping on medium screens */
} 


.career-role-item:hover {
  display: flex;
  justify-content: space-between;
  align-items: center;
  cursor: pointer;
  padding: 24px;
  border: 1px solid #090914;
  border-radius: 20px;
} 

.role-title {
  font-size: 16px;
  font-weight: 400;
  color: #333;
  margin: 0;
  flex: 1 1 auto; /* take available space */
  min-width: 200px; /* keep readable when wrapping */
}

.apply-button {
  display: inline-flex;
  align-items: center;
  gap: 8px;
  background: transparent;
  color: #333;
  text-decoration: none;
  padding: 0;
  font-size: 16px;
  font-weight: 400;
  border: none;
  white-space: nowrap; /* prevent breaking "Apply here" */
}

.apply-button:hover {
  color: #333;
  text-decoration: none;
}

.apply-button svg {
  width: 16px;
  height: 16px;
}

@media (max-width: 768px) {
  .career-section {
    padding: 60px 0;
  }
  
  .career-title {
    font-size: 28px;
  }
  
  .career-subtitle {
    font-size: 15px;
    padding: 0 20px;
  }
  
  .career-role-item {
    flex-direction: column;
    align-items: flex-start;
    width: 100%;
    padding: 16px;
    gap: 14px;
  }
  
  .apply-button {
    align-self: flex-start;
  }
  
  .career-roles-container {
    padding: 0 16px;
  }
  
  .role-title {
    min-width: 0;
    width: 100%;
    word-break: break-word;
  }
}

@media (max-width: 480px) {
  .career-section {
    padding: 50px 0;
  }
  
  .career-header {
    margin-bottom: 40px;
  }
  
  .career-title {
    font-size: 24px;
  }
  
  .career-subtitle {
    font-size: 14px;
  }
  
  .career-role-item {
    padding: 14px 16px;
  }
}

/* Font Face Declaration for Vogun Medium */
@font-face {
  font-family: 'Vogun';
  src: url('/assets/fonts/Vogun-Medium.woff') format('woff');
  font-weight: 500;
  font-style: normal;
  font-display: swap;
}

/* Global Poppins font for all landing page elements */
* {
  font-family: 'Poppins', sans-serif;
}

body {
  font-family: 'Poppins', sans-serif;
}
    
.section-header {
  margin-bottom: 50px;
}

.section-title {
  font-size: 36px;
  font-weight: 700;
  color: var(--primary-dark);
  margin-bottom: 16px;
}

.section-subtitle {
  font-size: 18px;
  color: #6c757d;
  max-width: 700px;
  margin: 0 auto;
}

.text-highlight {
  color: var(--primary);
  font-weight: 700;
}

/* Button Styles */
.btn {
  padding: 12px 24px;
  border-radius: 8px;
  font-weight: 600;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.btn-icon {
  margin-left: 8px;
  width: 16px;
  height: 16px;
}

.btn-primary {
  background-color: var(--primary);
  border-color: var(--primary);
  color: white;
}

.btn-primary:hover {
  background-color: var(--primary-dark);
  border-color: var(--primary-dark);
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.btn-outline-primary {
  color: var(--primary);
  border-color: var(--primary);
  background-color: transparent;
}

.btn-outline-primary:hover {
  background-color: var(--primary);
  color: white;
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.btn-demo, .btn-learn {
  min-width: 160px;
  margin: 0 10px 10px 0;
}

/* Hero Section */
.hero-container-landing {
  width: 100%;
  margin-bottom: 0;
  overflow: hidden;
  position: relative;
}

.hero-split-background {
  width: 100vw;
  height: 100vh;
  /* background-image removed for video background */
  display: flex;
  justify-content: center; /* Add this for horizontal centering */
  min-height: 600px; /* Fallback for short screens */
  overflow: hidden; /* Ensure video doesn't overflow */
}

.hero-bg-video {
  position: absolute;
  inset: 0 !important;
  width: 100% !important;
  height: 100% !important;
  min-width: 100vw !important;
  min-height: 100vh !important;
  z-index: 0 !important;
  object-fit: cover !important;
  pointer-events: none;
  border: none;
  margin: 0;
  padding: 0;
  display: block;
  background: transparent !important;
}

.hero-split-background-home {
  width: 100%;
  height: 100vh;
  background-image: url('/assets/img/home/<USER>');
  background-size: cover;
  background-position: center;
  position: relative;
  display: flex;
  align-items: center;
}

.hero-content {
  max-width: 800px;
  position: relative;
  z-index: 3;
  text-align: center;
  margin: 0 auto;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
}

.hero-badge {
  color: white;
  font-size: 16px;
  font-weight: 700;
  letter-spacing: 1px;
  margin-bottom: 30px;
  display: block;
}

.hero-title {
  font-size: 64px;
  font-family: 'Poppins', sans-serif;
  font-weight: 600; /* SemiBold */
  line-height: 1.2;
  color: white;
  margin-bottom: 25px;
  margin-top: 0;
}

/* Enhanced Hero Styles */
.hero-title-enhanced {
  font-size: 80.8px;
  font-family: 'Poppins', sans-serif;
  font-weight: 800; /* Bold */
  line-height: 1.1;
  color: white;
  margin-bottom: 30px;
  margin-top: 0;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
}

.hero-subtitle-enhanced {
  margin-bottom: 35px;
  margin-top: 0;
}

.typewriter-text {
  font-size: 28px;
  font-family: 'Poppins', sans-serif;
  font-weight: 600;
  color: #f0f0f1;
  line-height: 1.4;
  min-height: 40px;
  display: block;
  margin-bottom: 15px;
}

.cursor {
  animation: blink 1s infinite;
  color: #60FBFF;
  font-weight: 300;
}

@keyframes blink {
  0%, 50% { opacity: 1; }
  51%, 100% { opacity: 0; }
}

.subtitle-static {
  font-size: 20px;
  font-family: 'Poppins', sans-serif;
  font-weight: 400;
  color: rgba(255, 255, 255, 0.9);
  line-height: 1.5;
}

.hero-buttons-enhanced {
  display: flex;
  flex-wrap: wrap;
  margin-top: 20px;
  justify-content: center;
}

.hero-cta-button-enhanced {
  background: linear-gradient(135deg, #37B7C3 0%, #60FBFF 100%);
  color: white;
  border: none;
  border-radius: 50px;
  padding: 20px 50px;
  font-size: 18px;
  font-weight: 700;
  cursor: pointer;
  transition: all 0.4s ease;
  text-transform: uppercase;
  letter-spacing: 1px;
  box-shadow: 0 8px 25px rgba(55, 183, 195, 0.4);
  white-space: nowrap;
  position: relative;
  overflow: hidden;
}

.hero-cta-button-enhanced::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, #60FBFF 0%, #37B7C3 100%);
  transition: left 0.4s ease;
  z-index: -1;
}

.hero-cta-button-enhanced:hover::before {
  left: 0;
}

.hero-cta-button-enhanced:hover {
  transform: translateY(-4px);
  box-shadow: 0 15px 35px rgba(55, 183, 195, 0.6);
  letter-spacing: 1.2px;
}

/* Animated Background Circles */
.hero-background-circles {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  overflow: hidden;
  z-index: 2;
  pointer-events: none;
}

/* Add orbit wrappers for each circle */
.circle-orbit-1, .circle-orbit-2, .circle-orbit-3, .circle-orbit-4, .circle-orbit-5, .circle-orbit-6, .circle-orbit-7 {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  /* Each orbit will have a different animation duration and diameter */
}

.circle-orbit-1 {
  position: absolute;
  top: 10%;
  left: 8%;
  width: 320px; height: 320px;
  animation: orbit-1 18s linear infinite;
}
.circle-orbit-2 {
  position: absolute;
  top: 70%;
  left: 80%;
  width: 220px; height: 220px;
  animation: orbit-2 14s linear infinite;
}
.circle-orbit-3 {
  position: absolute;
  top: 5%;
  right: 12%;
  width: 400px; height: 400px;
  animation: orbit-3 22s linear infinite;
}
.circle-orbit-4 {
  position: absolute;
  bottom: 12%;
  left: 18%;
  width: 150px; height: 150px;
  animation: orbit-4 10s linear infinite;
}
.circle-orbit-5 {
  position: absolute;
  top: 35%;
  right: 20%;
  width: 280px; height: 280px;
  animation: orbit-5 16s linear infinite;
}
.circle-orbit-6 {
  position: absolute;
  bottom: 20%;
  left: 50%;
  width: 360px; height: 360px;
  animation: orbit-6 20s linear infinite;
}
.circle-orbit-7 {
  position: absolute;
  top: 25%;
  right: 30%;
  width: 180px; height: 180px;
  animation: orbit-7 12s linear infinite;
}

/* Center the circle in its orbit and offset it to the edge */
.circle {
  position: absolute;
  top: 0;
  left: 50%;
  transform: translate(-50%, 0); /* Offset to top edge of orbit */
  border-radius: 50%;
  backdrop-filter: blur(3px);
  box-shadow: 0 0 30px rgba(255, 255, 255, 0.2), inset 0 0 20px rgba(255, 255, 255, 0.1);
  animation: float 15s infinite ease-in-out;
  will-change: transform, opacity;
}

.circle-1 {
  width: 150px;
  height: 150px;
  background: rgba(96, 251, 255, 0.2);
  animation-delay: 0s;
  animation-duration: 20s;
}
.circle-2 {
  width: 100px;
  height: 100px;
  background: rgba(55, 183, 195, 0.18);
  animation-delay: -5s;
  animation-duration: 15s;
}
.circle-3 {
  width: 200px;
  height: 200px;
  background: rgba(255, 255, 255, 0.12);
  animation-delay: -10s;
  animation-duration: 25s;
}
.circle-4 {
  width: 80px;
  height: 80px;
  background: rgba(96, 251, 255, 0.25);
  animation-delay: -3s;
  animation-duration: 12s;
}
.circle-5 {
  width: 180px;
  height: 180px;
  background: rgba(55, 183, 195, 0.16);
  animation-delay: -8s;
  animation-duration: 18s;
}
.circle-6 {
  width: 120px;
  height: 120px;
  background: rgba(255, 255, 255, 0.14);
  animation-delay: -12s;
  animation-duration: 22s;
}
.circle-7 {
  width: 60px;
  height: 60px;
  background: rgba(96, 251, 255, 0.3);
  animation-delay: -2s;
  animation-duration: 10s;
}

/* Orbit keyframes for each circle */
@keyframes orbit-1 { 100% { transform: translate(-50%, -50%) rotate(360deg); } }
@keyframes orbit-2 { 100% { transform: translate(-50%, -50%) rotate(360deg); } }
@keyframes orbit-3 { 100% { transform: translate(-50%, -50%) rotate(360deg); } }
@keyframes orbit-4 { 100% { transform: translate(-50%, -50%) rotate(360deg); } }
@keyframes orbit-5 { 100% { transform: translate(-50%, -50%) rotate(360deg); } }
@keyframes orbit-6 { 100% { transform: translate(-50%, -50%) rotate(360deg); } }
@keyframes orbit-7 { 100% { transform: translate(-50%, -50%) rotate(360deg); } }

@keyframes float {
  0% {
    transform: translateY(0px) translateX(0px) scale(1) rotate(0deg);
    opacity: 0.6;
  }
  25% {
    transform: translateY(-40px) translateX(25px) scale(1.1) rotate(90deg);
    opacity: 0.3;
  }
  50% {
    transform: translateY(-60px) translateX(-20px) scale(0.9) rotate(180deg);
    opacity: 0.8;
  }
  75% {
    transform: translateY(-20px) translateX(35px) scale(1.05) rotate(270deg);
    opacity: 0.4;
  }
  100% {
    transform: translateY(0px) translateX(0px) scale(1) rotate(360deg);
    opacity: 0.6;
  }
}

@keyframes rotate360 {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

.text-lighter {
  font-size: 3.5rem;
  margin-bottom: 1.5rem;
  opacity: 0;
  animation: slideUp 1s ease-out 0.5s forwards;
}

.hero-subtitle {
  font-size: 22px;
  font-family: 'Poppins', sans-serif;
  font-weight: 550; /* Medium */
  color: rgb(255, 255, 255);
  margin-bottom: 25px;
  line-height: 1.4;
  margin-top: 0;
}

.hero-description {
  font-size: 16px;
  font-family: 'Poppins', sans-serif;
  font-weight: 400; /* Regular */
  color: rgba(255, 255, 255, 0.9);
  line-height: 1.6;
  margin-bottom: 35px;
  margin-top: 0;
  max-width: 550px;
}

.hero-buttons {
  display: flex;
  flex-wrap: wrap;
  margin-top: 10px;
}

.space-button {
  display: flex;
  gap: 10px;
  position: relative;
  padding: 15px 30px;
  font-size: 14px;
  border: 2px solid white;
  color: rgba(255, 255, 255, 1);
  background: linear-gradient(90deg, #37B7C3 0%, #088395 93%, #37B7C3 100%);
  border-radius: 35px;
  cursor: pointer;
  overflow: hidden;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  font-family: "Arial", sans-serif;
  text-transform: uppercase;
  letter-spacing: 2px;
  z-index: 1;
  transform: translateZ(0);
  backface-visibility: hidden;
  perspective: 1000px;
  will-change: transform;
  text-shadow:
    0 0 10px rgba(255, 255, 255, 0.3),
    0 0 20px rgba(255, 255, 255, 0.1);
  font-weight: 500;
}

.space-button-container {
  position: relative;
  display: inline-block;
  padding: 2px;
  border-radius: 12px;
  background-size: 400% 400%;
  animation: gradientBorder 20s ease-in-out infinite;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  transform: translateZ(0);
  backface-visibility: hidden;
  perspective: 1000px;
  will-change: transform, filter;
  backdrop-filter: blur(5px);
}

.space-button span {
  position: relative;
  z-index: 5;
  mix-blend-mode: normal;
  font-weight: 500;
  background: linear-gradient(to right, #ffffff, #ffffff);
  -webkit-background-clip: text;
  background-clip: text;
  filter: brightness(1.5) contrast(1.8);
  text-shadow:
    0 0 15px rgba(255, 255, 255, 0.6),
    0 0 30px rgba(255, 255, 255, 0.3);
}

.space-button::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: radial-gradient(
      1px 1px at 10% 15%,
      rgba(255, 255, 255, 0.95),
      rgba(0, 0, 0, 0)
    ),
    radial-gradient(
      1.5px 1.5px at 85% 25%,
      rgba(255, 255, 255, 0.95),
      rgba(0, 0, 0, 0)
    ),
    radial-gradient(
      1px 1px at 75% 85%,
      rgba(255, 255, 255, 0.95),
      rgba(0, 0, 0, 0)
    ),
    radial-gradient(
      1.5px 1.5px at 15% 75%,
      rgba(255, 255, 255, 0.95),
      rgba(0, 0, 0, 0)
    ),
    radial-gradient(
      1px 1px at 50% 25%,
      rgba(255, 255, 255, 0.95),
      rgba(0, 0, 0, 0)
    ),
    radial-gradient(
      1.5px 1.5px at 25% 50%,
      rgba(255, 255, 255, 0.95),
      rgba(0, 0, 0, 0)
    ),
    radial-gradient(
      1px 1px at 85% 65%,
      rgba(255, 255, 255, 0.95),
      rgba(0, 0, 0, 0)
    );
  animation: moveParticles1 20s linear infinite;
  opacity: 0.45;
  pointer-events: none;
  will-change: transform;
  z-index: 2;
}

.space-button::after {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: inherit;
  animation: moveParticles2 18s linear infinite;
  transform: rotate(25deg);
  opacity: 0.55;
  pointer-events: none;
  will-change: transform;
  z-index: 3;
}

.space-button:hover {
  transform: translateY(-1px) translateZ(0);
  background: linear-gradient(270deg, #37B7C3 0%, #088395 93%, #37B7C3 100%);
}

.space-button:hover span {
  filter: brightness(1.8) contrast(2);
  text-shadow:
    0 0 20px rgba(255, 255, 255, 0.8),
    0 0 40px rgba(255, 255, 255, 0.4),
    0 0 60px rgba(255, 255, 255, 0.2);
}

.space-button-container:hover {

  backdrop-filter: blur(8px);
  animation: gradientBorder 10s ease-in-out infinite;
}

.space-button:hover::before {
  opacity: 0.65;
}

.space-button:hover::after {
  opacity: 0.75;
}

.space-button:hover .bright-particles {
  opacity: 1;
  filter: blur(0);
}

@keyframes moveParticles1 {
  0% {
    transform: translate(0, 0) rotate(0deg);
  }
  20% {
    transform: translate(-25px, 15px) rotate(90deg);
  }
  40% {
    transform: translate(20px, -20px) rotate(180deg);
  }
  60% {
    transform: translate(-15px, -25px) rotate(270deg);
  }
  80% {
    transform: translate(25px, 20px) rotate(320deg);
  }
  100% {
    transform: translate(0, 0) rotate(360deg);
  }
}

@keyframes moveParticles2 {
  0% {
    transform: translate(0, 0) rotate(20deg);
  }
  25% {
    transform: translate(20px, -15px) rotate(110deg);
  }
  50% {
    transform: translate(-20px, 20px) rotate(200deg);
  }
  75% {
    transform: translate(15px, 15px) rotate(290deg);
  }
  100% {
    transform: translate(0, 0) rotate(380deg);
  }
}

@keyframes gradientBorder {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}

.space-button .bright-particles {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: radial-gradient(
      2px 2px at 15% 25%,
      rgba(255, 255, 255, 1),
      rgba(0, 0, 0, 0)
    ),
    radial-gradient(
      1.8px 1.8px at 85% 15%,
      rgba(255, 255, 255, 0.98),
      rgba(0, 0, 0, 0)
    ),
    radial-gradient(
      2px 2px at 75% 75%,
      rgba(255, 255, 255, 1),
      rgba(0, 0, 0, 0)
    ),
    radial-gradient(
      1.5px 1.5px at 25% 85%,
      rgba(255, 255, 255, 0.95),
      rgba(0, 0, 0, 0)
    ),
    radial-gradient(
      2.2px 2.2px at 65% 35%,
      rgba(255, 255, 255, 1),
      rgba(0, 0, 0, 0)
    );
  animation: moveParticles4 15s linear infinite;
  opacity: 0.85;
  mix-blend-mode: screen;
  pointer-events: none;
  will-change: transform;
  z-index: 4;
  filter: blur(0.3px);
}

@keyframes moveParticles4 {
  0% {
    transform: translate(0, 0) rotate(0deg);
  }
  20% {
    transform: translate(-30px, 20px) rotate(90deg);
  }
  40% {
    transform: translate(25px, -25px) rotate(180deg);
  }
  60% {
    transform: translate(-20px, -30px) rotate(270deg);
  }
  80% {
    transform: translate(30px, 25px) rotate(320deg);
  }
  100% {
    transform: translate(0, 0) rotate(360deg);
  }
}



.hero-cta-button {
  color: #fff;
  background: linear-gradient(90deg, #37B7C3 0%, #8dc7c1 50%, #37B7C3 100%);
  border: none;
  border-radius: 30px;
  padding: 16px 40px;
  font-size: 16px;
  font-weight: 700;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4,0,0.2,1);
  margin-right: 20px;
  text-transform: uppercase;
  letter-spacing: 1px;
  box-shadow: 0 4px 16px rgba(55, 183, 195, 0.18);
  white-space: nowrap;
  position: relative;
  overflow: hidden;
  z-index: 1;
}

.hero-cta-button a {
  /* color: inherit; */
  text-decoration: none;
  display: block;
  width: 100%;
  height: 100%;
}

.hero-cta-button:hover {
  background: white ;
  transform: translateY(-3px) scale(1.04);
  box-shadow: 0 8px 24px rgba(55, 183, 195, 0.25);
  color: #1c6e92;
}

.hero-cta-button:hover a {
  color: #ffffff;
}

.hero-secondary-button {
  background-color: transparent;
  color: white;
  border: 1px solid white;
  border-radius: 30px;
  padding: 16px 40px;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  text-transform: uppercase;
  letter-spacing: 0.8px;
  white-space: nowrap;
}

.hero-secondary-button:hover {
  background-color: rgba(255, 255, 255, 0.1);
  transform: translateY(-3px);
  box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
}

.hero-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

/* Statistics Section */
.statistics-section {
  background-color: #EBF4F6;
  padding: 60px 0;
  margin: 0;
}

.statistics-grid {
  display: flex;
  justify-content: center;
  align-items: center;
  max-width: 1440px;
  margin: 0 auto;
  gap: 0;
}

.stat-item {
  text-align: left;
  padding: 0 40px;
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.stat-number {
  font-size: 50px !important;
  font-weight: 500;
  color: #071952;
  margin: 0;
  line-height: 1;
  margin-bottom: 15px;
  font-family: 'Vogun', 'Poppins', sans-serif;
}

.stat-description {
  font-size: 16px;
  font-weight: 500;
  color: #071952;
  line-height: 1.4;
  font-family: 'Poppins', sans-serif;
  max-width: 180px;
}

.stat-divider {
  width: 2px;
  height: 80px;
  background-color: #071952;
  font-weight: bold;
  opacity: 0.5;
}

@media (max-width: 768px) {
  .statistics-section {
    padding: 40px 0;
  }
  
  .statistics-grid {
    flex-direction: column;
    gap: 30px;
  }
  
  .stat-divider {
    width: 80px;
    height: 1px;
    background-color: #B0BEC5;
    opacity: 0.5;
  }
  
  .stat-item {
    padding: 0 20px;
  }
  
  .stat-number {
    font-size: 48px;
  }
  
  .stat-description {
    font-size: 14px;
    max-width: 150px;
  }
}

@media (max-width: 480px) {
  .stat-number {
    font-size: 40px;
  }
  
  .stat-description {
    font-size: 13px;
  }
}

/* Best Features Section */
.best-features-section {
  background-color: white;
  padding: 80px 0;
}

.features-numbered-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 30px;
  max-width: 1200px;
  margin: 0 auto 60px;
}

.numbered-feature-card {
  background-color: white;
  border-radius: 0; /* Removed border-radius */
  padding: 30px;
  box-shadow: none; /* Removed box-shadow */
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  display: flex;
  align-items: flex-start;
  gap: 20px;
  position: relative;
  overflow: hidden;
}

.numbered-feature-card:hover {
  transform: translateY(-10px);
  box-shadow: none; /* Removed box-shadow */
}

.feature-number {
  font-size: 48px;
  font-weight: 800;
  color: rgba(7, 25, 82, 0.1);
  line-height: 1;
  margin-top: 5px;
}

.numbered-feature-content {
  flex: 1;
}

.numbered-feature-title {
  font-size: 22px;
  font-weight: 700;
  color: var(--primary-dark);
  margin-bottom: 12px;
}

.numbered-feature-description {
  font-size: 16px;
  color: #6c757d;
  line-height: 1.6;
}

/* Pain Points Section */
.pain-points-grid {
  margin-top: 40px;
}

.pain-point-card {
  background-color: white;
  border-radius: 0; /* Removed border-radius */
  padding: 30px 20px;
  height: 100%;
  box-shadow: none; /* Removed box-shadow */
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  text-align: center;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: flex-start;
}

.pain-point-card:hover {
  transform: translateY(-5px);
  box-shadow: none; /* Removed box-shadow */
}

.pain-point-icon {
  font-size: 36px;
  margin-bottom: 16px;
}

.pain-point-title {
  font-size: 18px;
  font-weight: 600;
  color: var(--primary-dark);
  margin: 0 0 12px 0;
}

.pain-point-description {
  font-size: 14px;
  color: #6c757d;
  line-height: 1.5;
  margin: 0;
}

/* About Section */
.about-section {
  background-color: var(--background-light);
  padding: 80px 0;
}

.about-image-container {
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
}

.about-image {
  max-width: 100%;
  height: auto;
  border-radius: 0; /* Removed border-radius */
  box-shadow: none; /* Removed box-shadow */
}

.about-content {
  padding: 20px;
}

.about-title {
  font-size: 36px;
  font-weight: 700;
  color: var(--primary-dark);
  margin-bottom: 24px;
}

.about-description {
  font-size: 16px;
  color: #555;
  line-height: 1.7;
  margin-bottom: 20px;
}

.about-stats {
  display: flex;
  justify-content: space-between;
  margin-top: 40px;
}

.stat-item {
  text-align: center;
  padding: 0 10px;
}

.stat-value {
  font-size: 36px;
  font-weight: 800;
  color: var(--primary);
  margin-bottom: 8px;
}

.stat-label {
  font-size: 16px;
  font-weight: 600;
  color: var(--primary-dark);
}

/* Features Section */
.features-section {
  background-color: white;
  padding: 80px 0;
}

.features-row {
  margin-top: 40px;
}

/* Articles Section */
.articles-section {
  background-color: white;
  padding: 80px 0;
  margin: 40px 0;
}

.articles-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 40px;
}

.articles-title {
  font-size: 36px;
  font-weight: 700;
  color: black;
  line-height: 1.2;
  margin: 0;
}

.articles-highlight {
  color: #37B7C3;
  font-weight: 700;
}

.articles-view-all {
  background-color: var(--primary);
  color: white;
  border: none;
  border-radius: 30px;
  padding: 12px 24px;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
}

.articles-view-all:hover {
  background-color: var(--primary-dark);
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.articles-grid {
  margin-top: 30px;
}

.article-card {
  background-color: white;
  border-radius: 0; /* Removed border-radius */
  box-shadow: none; /* Removed box-shadow */
  overflow: hidden;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.article-card:hover {
  transform: translateY(-5px);
  box-shadow: none; /* Removed box-shadow */
}

.article-image {
  width: 100%;
  height: 300px;
  overflow: hidden;
}

.article-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 15px;
  transition: transform 0.5s ease;
}

.article-card:hover .article-image img {
  transform: scale(1.05);
}

.article-content {
  padding-top: 25px;
  flex: 1;
  display: flex;
  flex-direction: column;
}

.article-title-latest {
  font-size: 18px;
  font-weight: 700;
  color: var(--primary-dark);
  margin-bottom: 15px;
  line-height: 1.4;
}

.article-description {
  font-size: 14px;
  color: #6c757d;
  line-height: 1.6;
  margin-bottom: 20px;
  flex: 1;
}

.article-link {
  color: var(--primary);
  font-weight: 600;
  font-size: 14px;
  text-decoration: none;
  display: inline-block;
  position: relative;
  padding-bottom: 2px;
  align-self: flex-start;
  border: 2px solid #37B7C3;
  border-radius: 20px;
  color: #37B7C3 !important;
  padding: 10px 30px;
  background-color: transparent;
  transition: 
    background-color 0.3s ease,
    color 0.3s ease,
    border-color 0.3s ease,
    box-shadow 0.3s ease;
}

.article-link:hover,
.article-link:focus {
  background-color: #37B7C3;
  color: #fff !important;
  border-color: #37B7C3;
  box-shadow: 0 2px 8px rgba(55, 183, 195, 0.15);
  text-decoration: none;
}

.article-link::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 2px;
  background-color: var(--primary);
  transform: scaleX(0);
  transform-origin: right;
  transition: transform 0.3s ease;
}


@media (max-width: 992px) {
  .articles-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 20px;
  }
  
  .articles-title {
    font-size: 30px;
  }
}

@media (max-width: 768px) {
  .articles-section {
    padding: 60px 0;
    text-align: center;
  }
  
  .articles-title {
    font-size: 26px;
    text-align: center;
  }
  
  .articles-header {
    justify-content: center;
    align-items: center;
  }
  
  .article-card {
    margin-bottom: 30px;
    max-width: 450px;
    margin-left: auto;
    margin-right: auto;
  }
  
  .articles-grid .row {
    display: block;
    justify-content: center;
    text-align: center;
  }
  
  .articles-grid .col-md-4 {
    width: 100%;
    max-width: 100%;
    flex: 0 0 100%;
    margin-bottom: 20px;
    display: flex;
    justify-content: center;
  }
}

/* Participation Section */
.participation-section {
  padding: 60px 0;
  margin: 40px auto;
  border-radius: 0; /* Removed border-radius */
  max-width: 1200px;
  overflow: hidden;
}

.participation-container {
  width: 100%;
  max-width: 1100px;
  margin: 0 auto;
  padding: 0 20px;
}

.participation-row {
  display: flex;
  flex-direction: row;
  align-items: center;
  position: relative;
}

.participation-image-col {
  flex: 0 0 45%;
  max-width: 45%;
  z-index: 2;
}

.participation-content-col {
  flex: 0 0 65%;
  position: relative;
  z-index: 1;
  margin-left: -50px;
  background-color: #EBF4F6;
  border-radius: 15px; /* Removed border-radius */
  padding: 100px 60px 60px 100px;
}

.participation-image-container {
  width: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
}

.participation-image {
  max-width: 100%;
  height: auto;
  border-radius: 15px; /* Removed border-radius */
  box-shadow: none; /* Removed box-shadow */
}

.participation-content {
  padding: 0;
}

.participation-title {
  font-size: 36px;
  font-weight: 700;
  color: black;
  margin-bottom: 24px;
  line-height: 1.2;
}

.participation-highlight {
  color: #37B7C3;
  font-weight: 700;
}

.participation-description {
  font-size: 16px;
  color: #555;
  line-height: 1.7;
  margin-bottom: 20px;
}

.participation-action {
  margin-top: 30px;
}

.participation-button {
  background-color: var(--primary);
  color: white;
  border: none;
  border-radius: 30px;
  padding: 14px 30px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 4px 10px rgba(8, 131, 149, 0.3);
}

.participation-button:hover {
  background-color: var(--primary-dark);
  transform: translateY(-2px);
  box-shadow: none; /* Removed box-shadow */
}

/* Tablet styles */
@media (max-width: 992px) {
  .participation-title {
    font-size: 30px;
  }
  
  .participation-content-col {
    padding: 40px 40px 40px 90px;
  }
}

/* Mobile styles */
@media (max-width: 768px) {
  .participation-section {
    padding: 40px 0;
    margin: 30px auto;
    border-radius: 15px;
    text-align: center;
  }
  
  .participation-container {
    display: flex;
    justify-content: center;
    align-items: center;
  }
  
  .participation-row {
    flex-direction: column;
    align-items: center;
    width: 100%;
    max-width: 450px;
  }
  
  .participation-image-col {
    flex: 0 0 100%;
    max-width: 100%;
    padding-top: 0;
    display: flex;
    justify-content: center;
  }
  
  .participation-image-container {
    max-width: 80%;
    margin-bottom: 0;
  }
  
  .participation-content-col {
    flex: 0 0 100%;
    max-width: 100%;
    margin-left: 0;
    margin-top: -30px;
    padding: 40px 25px;
    border-radius: 0; /* Removed border-radius */
    display: flex;
    justify-content: center;
  }
  
  .participation-content {
    text-align: center;
    max-width: 90%;
  }
  
  .participation-title {
    font-size: 26px;
    text-align: center;
  }
  
  .participation-description {
    text-align: center;
  }
  
  .participation-action {
    display: flex;
    justify-content: center;
  }
}

/* Small mobile styles */
@media (max-width: 480px) {
  .participation-content-col {
    padding: 30px 20px;
  }
  
  .participation-title {
    font-size: 24px;
  }
  
  .participation-description {
    font-size: 15px;
  }
}

/* Seamless Connection Section */
.seamless-connection-section {
  text-align: left;
  background-image: url('/assets/img/landing/seamless-bg.png');
  background-size: cover;
  width: 100%;
  height: 100%;
  background-repeat: no-repeat;
  flex-direction: row;
  justify-content: space-between;
  padding: 80px 0;
  color: white;
  position: relative;
  overflow: hidden;
}

.seamless-connection-section>h2 {
  text-align: center;
}

.seamless-connection-title {
  font-size: 36px;
  font-weight: 700;
  margin-bottom: 20px;
  color: white;
  line-height: 1.2;
}

.seamless-connection-highlight {
  color: #37B7C3;
  font-weight: 700;
}

.seamless-connection-description {
  font-size: 16px;
  color: rgba(255, 255, 255, 0.9);
  max-width: 700px;
  margin: 0 auto 40px;
  line-height: 1.6;
  text-align: center;
}

.mobile-devices-container {
  display: flex;
  flex-direction: row;
  justify-content: center;
  align-items: center;
  gap: 40px;
  position: relative;
  width: 100%;
}

.left-mobile {
  position: relative;
}

.right-mobile {
  position: relative;
}

.left-mobile img, .right-mobile img {
  width: 100%;
  height: 100%;
}

.feature-bubble {
  position: absolute;
  background-color: white;
  border-radius: 0; /* Removed border-radius */
  padding: 8px 15px;
  display: flex;
  align-items: end;
  box-shadow: none; /* Removed box-shadow */
  z-index: 3;
}

.bubble-icon {
  font-size: 18px;
  margin-right: 8px;
}

.bubble-text {
  display: flex;
  align-items: start;
  font-size: 12px;
  font-weight: 500;
  color: #071952;
}

/* Left mobile bubbles */
.feature-bubble-left-1 {
  top: 30%;
  left: 10%;
}

.feature-bubble-left-2 {
  top: 50%;
  left: 10%;
}

.feature-bubble-left-3 {
  top: 65%;
  left: 10%;
}

/* Right mobile bubbles */
.feature-bubble-right-1 {
  top: 15%;
}

.feature-bubble-right-2 {
  top: 40%;
}

.feature-bubble-right-3 {
  top: 65%;
}

.connection-buttons {
  display: flex;
  width: 100vw;
  justify-content: center;
  gap: 20px;
  margin-top: 40px;
}

.app-store-button,
.google-play-button {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 10px 20px;
  border-radius: 30px;
  text-decoration: none;
  transition: all 0.3s ease;
  min-width: 200px;
  background-color: #37B7C3;
  color: white;
  border: none;
}

.google-play-button {
  background-color: transparent;
  border: 1px solid white;
}

.app-button-content {
  display: flex;
  align-items: center;
  gap: 10px;
}

.app-button-icon {
  display: flex;
  align-items: center;
  justify-content: center;
}

.app-button-text {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  line-height: 1.2;
}

.app-button-small {
  font-size: 10px;
  font-weight: 400;
}

.app-button-large {
  font-size: 16px;
  font-weight: 600;
}

.app-store-button:hover {
  background-color: #2da8b4;
  transform: translateY(-3px);
  box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
}

.google-play-button:hover {
  background-color: rgba(255, 255, 255, 0.1);
  transform: translateY(-3px);
  box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
}

@media (max-width: 992px) {
  .seamless-connection-title {
    font-size: 30px;
  }
  
  .mobile-devices-container {
    flex-direction: row;
    align-items: center;
    justify-content: center;
    margin: 0 auto;
    max-width: 650px;
    flex-wrap: wrap;
    gap: 20px;
  }
  
  /* Adjust mobile images */
  .left-mobile, .right-mobile {
    height: auto;
    max-width: 45%;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    position: relative;
    margin: 0;
  }
  
  .left-mobile img, .right-mobile img {
    max-width: 100%;
    margin: 0 auto;
  }
  
  /* Position feature bubbles better on tablet */
  .feature-bubble-left-1,
  .feature-bubble-left-2,
  .feature-bubble-left-3 {
    position: absolute;
    left: 0 !important;
    right: auto !important;
    margin: 0;
  }
  
  .feature-bubble-right-1,
  .feature-bubble-right-2,
  .feature-bubble-right-3 {
    position: absolute;
    right: 0 !important;
    left: auto !important;
    margin: 0;
  }
  
  /* Adjust positions for tablet */
  .feature-bubble-right-1 {
    top: 15% !important;
  }
  
  .feature-bubble-right-2 {
    top: 35% !important;
  }
  
  .feature-bubble-right-3 {
    top: 55% !important;
  }
}

@media (max-width: 768px) {
  .seamless-connection-section {
    padding: 60px 20px;
    text-align: center;
  }
  
  .seamless-connection-title {
    font-size: 26px;
    text-align: center;
  }
  
  .seamless-connection-description {
    text-align: center;
    padding: 0 15px;
  }
  
  .mobile-devices-container {
    max-width: 100%;
    flex-direction: row;
    justify-content: center;
    gap: 10px;
  }
  
  .left-mobile, .right-mobile {
    height: auto;
    max-width: 45%;
    flex: 0 0 45%;
    margin-bottom: 20px;
    position: relative;
    display: flex;
    flex-direction: column;
    align-items: center;
  }
  
  .left-mobile .participation-image-container,
  .right-mobile .participation-image-container,
  .left-mobile > img, 
  .right-mobile > img {
    width: 100%;
    height: auto;
    display: flex;
    justify-content: center;
    align-items: center;
  }
  
  .left-mobile img, 
  .right-mobile img {
    width: 100%;
    height: auto;
    max-height: 400px;
    object-fit: contain;
  }
  
  /* Make feature bubbles more mobile-friendly */
  .feature-bubble {
    position: relative !important;
    max-width: 100%;
    padding: 6px 10px;
    width: auto;
    z-index: 5;
    margin: 8px auto;
    left: auto !important;
    right: auto !important;
    top: auto !important;
  }
  
  .bubble-text {
    font-size: 11px;
  }
  
  /* Reposition left bubbles to be above image */
  .left-mobile {
    display: flex;
    flex-direction: column;
  }
  
  .left-mobile .feature-bubble-left-1,
  .left-mobile .feature-bubble-left-2,
  .left-mobile .feature-bubble-left-3 {
    order: 1;
  }
  
  .left-mobile .participation-image-container {
    order: 2;
  }
  
  /* Reposition right bubbles to be below image */
  .right-mobile {
    display: flex;
    flex-direction: column;
  }
  
  .right-mobile .participation-image-container,
  .right-mobile > img {
    order: 1;
  }
  
  .right-mobile .feature-bubble-right-1,
  .right-mobile .feature-bubble-right-2,
  .right-mobile .feature-bubble-right-3 {
    order: 2;
  }
  
  /* Center the app store buttons */
  .connection-buttons {
    flex-direction: column;
    align-items: center;
    justify-content: center;
    margin-top: 40px;
    width: 100%;
    max-width: 100%;
  }
  
  .app-store-button,
  .google-play-button {
    max-width: 250px;
    margin: 0 auto 15px;
  }
  
  /* Adjust mobile images for better display */
  .left-mobile, .right-mobile {
    margin-bottom: 30px;
    padding: 0;
    width: 100%;
  }
  
  .left-mobile img, .right-mobile img {
    max-width: 80%;
    margin: 0 auto;
  }
}

/* Small mobile devices */
/* Desktop layout */
.mobile-devices-container {
  display: flex;
  justify-content: center;
  position: relative;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 50px;
}

.mobile-phones-image {
  width: 100%;
  height: auto;
  display: block;
}

/* Feature bubbles container styles */
.feature-bubbles-left, 
.feature-bubbles-right {
  position: relative;
  width: 450px;
  display: flex;
  flex-direction: column;
  gap: 25px;
  z-index: 3;
}

.feature-bubbles-left {
  margin-right: 50px;
  display: flex;
  flex-direction: column;
  gap: 25px;
  align-items: flex-end;
}

.feature-bubbles-right {
  margin-left: 50px;
  display: flex;
  flex-direction: column;
  gap: 25px;
  align-items: flex-start;
}

/* Desktop bubble styling */
.feature-bubble {
  position: relative !important;
  background-color: white;
  border-radius: 20px;
  padding: 10px 15px;
  display: flex;
  align-items: center;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
  width: 100%;
  max-width: 100%;
  z-index: 5;
  transition: transform 0.3s ease;
}

.feature-bubble:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.15);
}

.bubble-icon {
  font-size: 18px;
  margin-right: 10px;
}

.bubble-text {
  font-size: 14px;
  font-weight: 500;
  color: #071952;
  line-height: 1.3;
}

@media (max-width: 1200px) {
  .mobile-devices-container {
    padding: 0 30px;
  }
  
  .feature-bubbles-left {
    margin-right: 30px;
  }
  
  .feature-bubbles-right {
    margin-left: 30px;
  }
  
}

@media (max-width: 992px) {
  .mobile-devices-container {
    padding: 0 20px;
  }
  
  .feature-bubbles-left {
    margin-right: 20px;
    width: 80%;
  }
  
  .feature-bubbles-right {
    margin-left: 20px;
    width: 80%;
  }
}

@media (max-width: 768px) {
  .feature-bubbles-left,
  .feature-bubbles-right {
    position: relative;
    width: 100%;
    max-width: 350px;
    left: auto;
    right: auto;
    top: auto;
    transform: none;
    margin: 0 auto;
    display: flex;
    flex-direction: column;
    gap: 15px;
    align-items: center;
  }

  .mobile-devices-container {
    display: flex;
    flex-direction: column !important;
    justify-content: center !important;
    align-items: center !important;
    gap: 20px !important;
    width: 100%;
    max-width: 100%;
    margin: 0 auto !important;
  }
}

@media (max-width: 480px) {
  .seamless-connection-section {
    padding: 50px 15px;
    overflow: visible;
  }
  
  .feature-bubble {
    max-width: 100%;
    width: 100%;
    position: relative !important;
    left: auto !important;
    right: auto !important;
    top: auto !important;
    margin: 8px 0 !important;
    display: flex;
    align-items: center;
    justify-content: flex-start;
    background-color: rgba(255, 255, 255, 0.95);
    border-radius: 8px;
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
  }
  
  .bubble-text {
    font-size: 13px;
    font-weight: 500;
  }
  /* Mobile devices container */
  .mobile-devices-container {
    display: flex;
    flex-direction: column !important;
    align-items: center;
    width: 100%;
    margin: 0 auto;
    padding: 0;
  }
  
  /* Feature bubbles first */
  .feature-bubbles-left {
    order: 1;
    margin-bottom: 15px;
  }
  
  .mobile-phones-image {
    width: 100%;
    max-width: 320px;
    margin: 0 auto;
  }
  
  /* Feature bubbles last */
  .feature-bubbles-right {
    order: 3;
    margin-top: 15px;
  }
  
  /* Bubble container styling */
  .feature-bubbles-container-left,
  .feature-bubbles-container-right {
    width: 100%;
    display: flex;
    flex-direction: column;
    gap: 5px;
  }
  
  /* Connection buttons for mobile */
  .connection-buttons {
    margin-top: 30px;
    width: auto;
  }
  
  /* App store buttons */
  .app-store-button, .google-play-button {
    min-width: 150px;
    padding: 8px 15px;
  }
  
  .app-button-large {
    font-size: 14px;
  }
  
  .app-button-small {
    font-size: 9px;
  }
}

/* Service Section */

@media (max-width: 768px) {
  .service-section {
    padding: 40px 0;
  }
  
  .service-container {
    margin: 0;
    border-radius: 0;
    width: 100vw;
    max-width: 100%;
  }
}

.service-content {
  text-align: center;
  color: white;
  position: relative;
  z-index: 2;
}

.service-subtitle {
  font-size: 18px;
  font-weight: 500;
  margin-bottom: 20px;
  color: white;
}

.service-title {
  font-size: 42px;
  font-weight: 700;
  margin-bottom: 40px;
  color: white;
  line-height: 1.2;
}

.service-buttons {
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  gap: 20px;
  margin-top: 30px;
}

.service-button {
  padding: 14px 28px;
  font-size: 14px;
  font-weight: 600;
  border-radius: 30px;
  cursor: pointer;
  transition: all 0.3s ease;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  border: 2px solid transparent;
}

.waitlist-button {
  background-color: transparent;
  color: white;
  border: 2px solid white;
}

.waitlist-button:hover {
  background-color: rgba(255, 255, 255, 0.1);
  transform: translateY(-3px);
  box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
}

.demo-button {
  background-color: #37B7C3;
  color: white;
  border: none;
}

.demo-button:hover {
  background-color: #2da8b4;
  transform: translateY(-3px);
  box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
}

.sales-button {
  background-color: transparent;
  color: white;
  border: 2px solid white;
}

.sales-button:hover {
  background-color: rgba(255, 255, 255, 0.1);
  transform: translateY(-3px);
  box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
}

@media (max-width: 768px) {
  .left-mobile, .right-mobile {
    height: auto;
    max-width: 45%;
    flex: 0 0 45%;
    margin-bottom: 20px;
    position: relative;
    display: flex;
    flex-direction: column;
    align-items: center;
  }
  
  .left-mobile .participation-image-container,
  .right-mobile .participation-image-container,
  .left-mobile > img, 
  .right-mobile > img {
    width: 100%;
    height: auto;
    display: flex;
    justify-content: center;
    align-items: center;
  }
  
  .left-mobile img, 
  .right-mobile img {
    width: 100%;
    height: 350px;
    object-fit: contain;
  }
  
  /* Ensure both right-mobile and left-mobile images have the same dimensions */
  .right-mobile > img,
  .left-mobile .participation-image-container img {
    height: 350px !important;
    width: auto !important;
    max-width: 100% !important;
    object-fit: contain !important;
  }
  
  .service-buttons {
    flex-direction: column;
    gap: 15px;
    align-items: center;
    justify-content: center;
  }
  
  .service-button {
    width: 100%;
    max-width: 300px;
    margin: 0 auto;
  }
}

.feature-card {
  background-color: white;
  border-radius: 0; /* Removed border-radius */
  padding: 30px;
  height: 100%;
  box-shadow: none; /* Removed box-shadow */
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.feature-card:hover {
  transform: translateY(-5px);
  box-shadow: none; /* Removed box-shadow */
}

.feature-image {
  width: 100%;
  height: 160px;
  margin-bottom: 20px;
  border-radius: 0; /* Removed border-radius */
  overflow: hidden;
  border: 1px solid rgba(0, 0, 0, 0.1);
}

.feature-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.5s ease;
}

.feature-card:hover .feature-image img {
  transform: scale(1.05);
}

.feature-icon {
  font-size: 36px;
  margin-bottom: 16px;
  min-width: 50px;
  text-align: center;
  background-color: var(--background-light);
  width: 60px;
  height: 60px;
  border-radius: 0; /* Removed border-radius */
  display: flex;
  align-items: center;
  justify-content: center;
  margin-top: -50px;
  border: 2px solid white;
  box-shadow: none; /* Removed box-shadow */
}

.feature-content {
  flex: 1;
  text-align: center;
}

.feature-title {
  font-size: 20px;
  font-weight: 600;
  color: var(--primary-dark);
  margin-bottom: 10px;
}

.feature-description {
  font-size: 16px;
  color: #6c757d;
  margin: 0;
  line-height: 1.5;
}

/* Why Choose Nurtify Section */
.why-choose-section {
  background-color: white;
  padding: 80px 0;
  margin: 40px 0;
}

.why-choose-header {
  margin-bottom: 50px;
}

.why-choose-title {
  font-size: 36px;
  font-weight: 700;
  color: black;
  line-height: 1.2;
  margin-bottom: 15px;
}

.why-choose-highlight {
  color: #37B7C3;
  font-weight: 700;
}

.why-choose-subtitle {
  font-size: 18px;
  color: #6c757d;
  margin: 0;
}

.why-choose-grid {
  margin-top: 30px;
}

.why-choose-card {
  background-color: rgba(235, 244, 246, 0.5);
  border-radius: 20px; /* Removed border-radius */
  padding: 25px;
  height: 100%;
  transition: background 0.8s cubic-bezier(0.4, 0, 0.2, 1), transform 0.8s cubic-bezier(0.4, 0, 0.2, 1);
  display: flex;
  flex-direction: column;
  color: black;
  align-items: flex-start;
  position: relative;
}

.why-choose-card-with-image {
  border-radius: 0; /* Removed border-radius */
  height: 100%;
  transition: transform 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  position: relative;
}

.why-choose-card:hover {
  /* Change background to a blue gradient on hover */
  /*
  color: #fff;
  background: linear-gradient(90deg, #37b7c3 0%, #088395 100%);
  */
  cursor: pointer;
  transform: translateY(-10px);
}

.why-choose-icon {
  width: 60px;
  height: 60px;
  min-width: 60px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  font-size: 18px;
  margin-bottom: 15px;
  z-index: 2;
  border-radius: 10px;
}

.why-choose-content {
  flex: 1;
  width: 100%;
  z-index: 2;
}

.why-choose-image {
  width: 100%;
  margin-top: 20px;
  border-radius: 0; /* Removed border-radius */
  overflow: hidden;
  max-height: 200px;
}

.why-choose-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 0; /* Removed border-radius */
}

.why-choose-card-title {
  font-size: 20px;
  font-weight: 700;
  color: black;
  margin-bottom: 10px;
  line-height: 1.3;
}

.why-choose-description {
  font-size: 14px;
  color: black;
  line-height: 1.5;
  margin: 0;
  font-family: 'Poppins', sans-serif;
  color: #090914;
  font-weight: 400;
  line-height: 24px;
  letter-spacing: 0%;
}

.why-choose-card,
.why-choose-card-title,
.why-choose-description {
  transition: all 0.3s ease;
}

.why-choose-card:hover {
  transform: translateY(-8px) scale(1.03);
  box-shadow: 0 8px 24px rgba(0,0,0,0.12);
}


.why-choose-card:hover .why-choose-description {
  color: #444;
}

@media (max-width: 992px) {
  .why-choose-title {
    font-size: 30px;
  }
  
  .why-choose-card {
    margin-bottom: 20px;
  }
}

@media (max-width: 768px) {
  .why-choose-section {
    padding: 60px 0;
    width: 100%;
  }
  
  .why-choose-title {
    font-size: 26px;
  }
  
  .why-choose-card, .why-choose-card-with-image {
    padding: 20px;
    margin-bottom: 30px;
    width: 100%;
  }
  
  .why-choose-card-with-image img {
    width: 100%;
    height: auto;
  }

  .why-choose-card-with-image:hover {
    -webkit-transform: scale(1.3);
    transform: scale(1.3);
    cursor: pointer;
  }
  
  .why-choose-grid .row {
    width: 100%;
    display: block; /* Force rows to become block-level elements */
    margin-bottom: 0; /* Remove row margin since we're using card margin */
  }
  
  .why-choose-grid .row.mt-4 {
    margin-top: 0 !important; /* Remove top margin between rows */
  }
  
  .why-choose-grid .col-md-4, 
  .why-choose-grid .col-md-6 {
    width: 100%;
    max-width: 100%;
    flex: 0 0 100%;
    padding: 0 15px;
    margin-bottom: 0; /* Control spacing through card margin instead */
  }
  
  .why-choose-icon {
    width: 35px;
    height: 35px;
    min-width: 35px;
    font-size: 16px;
  }
  
  .why-choose-card-title {
    font-size: 20px;
  }
  
  .why-choose-description {
    font-size: 13px;
  }
}

.why-choose-card-with-image img {
  transition: transform 0.8s cubic-bezier(0.4, 0, 0.2, 1);
}

.why-choose-card-with-image:hover img {
  transform: translateY(-10px);
}

/* Separator Section */
.separator-section {
  position: relative;
  margin: 0;
  padding: 0;
}

.separator-top {
  height: 80px;
  background-color: white;
}

.separator-content {
  background-color: #071238;
  color: white;
  padding: 80px 0;
}

.separator-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  padding: 20px;
}

.separator-number {
  font-size: 64px;
  font-weight: 800;
  color: #00E5E8;
  margin-bottom: 20px;
  line-height: 1;
}

.separator-text {
  font-size: 18px;
  line-height: 1.5;
  color: white;
}

.separator-bottom {
  background-color: #088395;
  padding: 40px 0;
  text-align: center;
}

.separator-title {
  font-size: 48px;
  font-weight: 700;
  color: white;
  margin: 0;
  text-transform: capitalize;
}

/* Trusted Leaders Section */
.trusted-leaders-section {
  background-image: url('/assets/img/landing/testimony.png');
  background-size: cover;
  background-position: center;
  padding: 80px 0;
  margin: 0;
}

.trusted-leaders-title {
  font-size: 36px;
  font-weight: 700;
  color: black;
  text-align: center;
  margin-bottom: 50px;
}

.testimonial-carousel {
  position: relative;
  margin-bottom: 60px;
  max-width: 800px;
  margin-left: auto;
  margin-right: auto;
}

.carousel-arrow {
  width: 40px;
  height: 40px;
  border-radius: 0; /* Removed border-radius */
  background-color: white;
  border: 1px solid #e0e0e0;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
  z-index: 2;
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
}

.carousel-arrow:hover {
  background-color: var(--primary-light);
  color: white;
  transform: translateY(-50%) scale(1.1);
  box-shadow: none; /* Removed box-shadow */
  border-color: var(--primary-light);
}

.carousel-arrow:active {
  transform: translateY(-50%) scale(0.95);
  box-shadow: none; /* Removed box-shadow */
}

.arrow-icon {
  font-size: 18px;
  line-height: 1;
  font-weight: bold;
}

.carousel-arrow-prev {
  left: -70px;
}

.carousel-arrow-next {
  right: -70px;
}

.arrow-icon {
  font-size: 18px;
  line-height: 1;
}

.testimonial-card-large {
  background-color: white;
  border-radius: 0; /* Removed border-radius */
  padding: 30px;
  box-shadow: none; /* Removed box-shadow */
  max-width: 800px;
  width: 100%;
  transition: transform 0.5s ease;
  transform-style: preserve-3d;
}

/* Flip animations */
.flip-out-left {
  animation: flipOutLeft 0.5s forwards;
}

.flip-in-right {
  animation: flipInRight 0.5s forwards;
}

.flip-out-right {
  animation: flipOutRight 0.5s forwards;
}

.flip-in-left {
  animation: flipInLeft 0.5s forwards;
}

.testimonial-carousel {
  position: relative;
  margin-bottom: 60px;
  max-width: 800px;
  margin-left: auto;
  margin-right: auto;
  perspective: 1500px;
}

/* Create a stacked card effect */
.testimonial-carousel::before,
.testimonial-carousel::after {
  content: '';
  position: absolute;
  width: 95%;
  height: 100%;
  background-color: white;
  border-radius: 0; /* Removed border-radius */
  box-shadow: none; /* Removed box-shadow */
  z-index: -1;
  left: 50%;
  transform: translateX(-50%);
}

.testimonial-carousel::before {
  bottom: -10px;
  width: 90%;
}

.testimonial-carousel::after {
  bottom: -20px;
  width: 85%;
}

.testimonial-card-large {
  background-color: white;
  border-radius: 16px;
  padding: 30px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.05);
  max-width: 800px;
  width: 100%;
  transition: transform 0.5s ease;
  transform-style: preserve-3d;
  position: relative;
  backface-visibility: hidden;
  z-index: 1;
  min-height: 250px;
  display: flex;
  flex-direction: column;
}

.testimonial-header {
  display: flex;
  align-items: center;
  margin-bottom: 20px;
  width: 50%;
  max-width: 100%;
  box-shadow: 2px 4px 8px 0px rgba(0, 0, 0, 0.08);
  border-radius: 50px;
  padding: 15px;
}

@media (max-width: 600px) {
  .testimonial-header {
    width: 100%;
  }
}

.testimonial-avatar {
  width: 60px;
  height: 60px;
  border-radius: 0; /* Removed border-radius */
  overflow: hidden;
  margin-right: 20px;
  border: 2px solid var(--primary-light);
  background-color: #f5f5f5;
  display: flex;
  align-items: center;
  justify-content: center;
}

.testimonial-avatar img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.testimonial-author-info {
  flex: 1;
}

.testimonial-author-name {
  font-size: 18px;
  font-weight: 600;
  color: var(--primary-dark);
  margin: 0 0 5px 0;
}

.testimonial-author-title {
  font-size: 14px;
  color: #6c757d;
  margin: 0;
  line-height: 1.4;
}

.testimonial-body {
  padding-top: 10px;
  flex: 1;
}

.testimonial-quote {
  font-size: 16px;
  color: #555;
  line-height: 1.6;
  font-style: italic;
  margin: 0;
  position: relative;
  padding: 0 10px;
}

/* Book-like page flip animations */
@keyframes flipOutLeft {
  0% {
    transform: rotateY(0deg);
    transform-origin: left;
    box-shadow: none; /* Removed box-shadow */
  }
  25% {
    box-shadow: none; /* Removed box-shadow */
  }
  100% {
    transform: rotateY(-180deg);
    transform-origin: left;
    opacity: 0;
    box-shadow: none; /* Removed box-shadow */
  }
}

@keyframes flipInRight {
  0% {
    transform: rotateY(180deg);
    transform-origin: right;
    opacity: 0;
    box-shadow: none; /* Removed box-shadow */
  }
  75% {
    box-shadow: none; /* Removed box-shadow */
  }
  100% {
    transform: rotateY(0deg);
    transform-origin: right;
    opacity: 1;
    box-shadow: none; /* Removed box-shadow */
  }
}

@keyframes flipOutRight {
  0% {
    transform: rotateY(0deg);
    transform-origin: right;
    box-shadow: none; /* Removed box-shadow */
  }
  25% {
    box-shadow: none; /* Removed box-shadow */
  }
  100% {
    transform: rotateY(180deg);
    transform-origin: right;
    opacity: 0;
    box-shadow: none; /* Removed box-shadow */
  }
}

@keyframes flipInLeft {
  0% {
    transform: rotateY(-180deg);
    transform-origin: left;
    opacity: 0;
    box-shadow: none; /* Removed box-shadow */
  }
  75% {
    box-shadow: none; /* Removed box-shadow */
  }
  100% {
    transform: rotateY(0deg);
    transform-origin: left;
    opacity: 1;
    box-shadow: none; /* Removed box-shadow */
  }
}

/* Animation durations */
.flip-out-left, .flip-out-right {
  animation-duration: 0.3s;
}

.flip-in-left, .flip-in-right {
  animation-duration: 0.4s;
}

.testimonial-header {
  display: flex;
  align-items: center;
  margin-bottom: 20px;
}

.testimonial-avatar {
  width: 60px;
  height: 60px;
  border-radius: 50px; /* Removed border-radius */
  overflow: hidden;
  margin-right: 20px;
  border: 2px solid var(--primary-light);
}

/* === PARTNERS SECTION (Our Partners logos row) === */
.partners-section {
  background: #0896a2;
  padding: 48px 0 36px 0;
  width: 100vw;
  margin-left: 50%;
  transform: translateX(-50%);
  position: relative;
  z-index: 1;
}

.partners-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 32px;
  text-align: center;
}

.partners-title {
  color: #fff;
  font-size: 3rem;
  font-weight: 700;
  margin-bottom: 32px;
  letter-spacing: 0.01em;
}

.partners-section .trusted-logos {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
  gap: 48px;
  flex-wrap: wrap;
  width: 100%;
  position: static;
  height: auto;
  margin: 0 auto;
  left: unset;
  overflow: visible;
}

.partners-section .trusted-logo {
  position: static;
  height: 40px;
  width: auto;
  object-fit: contain;
  filter: brightness(0) invert(1) grayscale(1);
  opacity: 0.95;
  transition: transform 0.2s;
  background: transparent;
  margin: 0;
  display: flex;
  align-items: center;
  animation: none !important;
}

.partners-section .trusted-logo:hover {
  transform: scale(1.07);
  opacity: 1;
}
/* === PARTNERS SECTION (Animated Logos Row) === */
.partners-section .trusted-logos {
  overflow: hidden;
  width: 100%;
  position: relative;
  height: 60px;
  margin: 0 auto;
  display: flex;
  align-items: center;
}

.partners-section .logos-track {
  display: flex;
  align-items: center;
  gap: 48px;
  animation: partners-marquee 18s linear infinite;
  will-change: transform;
}

.partners-section .trusted-logo {
  height: 60px;
  width: auto;
  object-fit: contain;
  filter: brightness(0) invert(1) grayscale(1);
  opacity: 0.95;
  transition: transform 0.2s;
  background: transparent;
  margin: 0;
  display: flex;
  align-items: center;
}

.partners-section .trusted-logo:hover {
  transform: scale(1.07);
  opacity: 1;
}

@keyframes partners-marquee {
  0% {
    transform: translateX(0%);
  }
  100% {
    transform: translateX(-50%);
  }
}

/* Responsive adjustments */
@media (max-width: 900px) {
  .partners-section .logos-track {
    gap: 28px;
  }
  .partners-section .trusted-logo {
    height: 28px;
    width: 28px;
    font-size: 1rem;
  }
}

@media (max-width: 600px) {
  .partners-section .logos-track {
    gap: 16px;
  }
  .partners-section .trusted-logo {
    height: 40px;
    width: 40px;
    font-size: 0.8rem;
  }
}


.avatar-logo {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 40px;
  width: 40px;
  margin-left: 8px;
  margin-right: 0;
  background: none;
  filter: none;
}

.avatar-circle {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 40px;
  width: 40px;
  border-radius: 50%;
  background: linear-gradient(135deg, #0e8a9c 60%, #f500c7 100%);
  color: #fff;
  font-weight: 700;
  font-size: 1.3rem;
  box-shadow: 0 2px 8px rgba(0,0,0,0.10);
  border: 2px solid #fff;
  letter-spacing: 0.02em;
}

@media (max-width: 900px) {
  .partners-section .trusted-logos {
    gap: 28px;
  }
  .partners-title {
    font-size: 1.3rem;
    margin-bottom: 20px;
  }
  .partners-section .trusted-logo, .avatar-logo, .avatar-circle {
    height: 48px;
    width: 48px;
    font-size: 1rem;
  }
}

@media (max-width: 600px) {
  .partners-section {
    padding: 32px 0 20px 0;
  }
  .partners-section .trusted-logos {
    gap: 16px;
    flex-wrap: wrap;
  }
  .partners-title {
    font-size: 1.1rem;
    margin-bottom: 12px;
  }
  .partners-section .trusted-logo, .avatar-logo, .avatar-circle {
    height: 80px;
    width: 80px;
    font-size: 0.8rem;
  }
}

.testimonial-avatar img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.testimonial-author-info {
  flex: 1;
}

.testimonial-author-name {
  font-size: 18px;
  font-weight: 600;
  color: var(--primary-dark);
  margin: 0 0 5px 0;
}

.testimonial-author-title {
  font-size: 14px;
  color: #6c757d;
  margin: 0;
  line-height: 1.4;
}

.testimonial-body {
  padding-top: 10px;
}

.testimonial-quote {
  font-size: 16px;
  color: #555;
  line-height: 1.6;
  font-style: italic;
  margin: 0;
}

.trusted-logos {
  position: relative;
  height: 80px;
  overflow: hidden;
  display: flex;
  align-items: center;
  width: 100vw;
  left: 0;
  margin: 0 auto;
}

.trusted-logo {
  position: absolute;
  top: 0;
  height: 60px;
  width: auto;
  animation: moveLogo 12s linear infinite;
}

.trusted-logo:nth-child(1) { left: 0; animation-delay: 0s; }
.trusted-logo:nth-child(2) { left: 180px; animation-delay: 2.4s; }
.trusted-logo:nth-child(3) { left: 360px; animation-delay: 4.8s; }
.trusted-logo:nth-child(4) { left: 540px; animation-delay: 7.2s; }
.trusted-logo:nth-child(5) { left: 720px; animation-delay: 9.6s; }

@keyframes moveLogo {
  0% {
    transform: translateX(-150px);
    opacity: 0;
  }
  10% {
    opacity: 1;
  }
  90% {
    opacity: 1;
  }
  100% {
    transform: translateX(100vw);
    opacity: 0;
  }
}

@media (max-width: 768px) {
  .trusted-leaders-section {
    padding: 60px 0;
  }
  
  .trusted-leaders-title {
    font-size: 26px;
    margin-bottom: 30px;
  }
  
  .testimonial-carousel {
    flex-direction: column;
  }
  
  .carousel-arrow {
    margin: 15px 0;
  }
  
  .carousel-arrow-prev {
    margin-right: 0;
    order: 2;
  }
  
  .carousel-arrow-next {
    margin-left: 0;
    order: 3;
  }
  
  .testimonial-card-large {
    order: 1;
    padding: 20px;
  }
  
  .trusted-logos {
    gap: 20px;
  }
  
  .trusted-logo {
    height: 20px;
  }
}

/* Testimonials Section */
.testimonials-section {
  background-color: #088395;
  padding: 80px 0;
}

.testimonials-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 30px;
  max-width: 1200px;
  margin: 0 auto 50px;
}

.testimonial-card {
  background-color: white;
  border-radius: 0; /* Removed border-radius */
  padding: 30px;
  box-shadow: none; /* Removed box-shadow */
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  display: flex;
  flex-direction: column;
}

.testimonial-card:hover {
  transform: translateY(-10px);
  box-shadow: none; /* Removed box-shadow */
}

.testimonial-content {
  flex: 1;
  margin-bottom: 20px;
}

.testimonial-quote {
  font-size: 16px;
  color: #555;
  line-height: 1.6;
  font-style: italic;
}

.testimonial-author {
  display: flex;
  align-items: center;
  gap: 15px;
}

.testimonial-image {
  width: 50px;
  height: 50px;
  border-radius: 0; /* Removed border-radius */
  overflow: hidden;
  border: 2px solid var(--primary-light);
}

.testimonial-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.testimonial-info {
  flex: 1;
}

.testimonial-name {
  font-size: 16px;
  font-weight: 600;
  color: var(--primary-dark);
  margin: 0 0 5px 0;
}

.testimonial-role {
  font-size: 14px;
  color: #6c757d;
  margin: 0;
}

.trust-logos {
  margin-top: 50px;
}

.trust-logo-placeholder {
  background-color: white;
  border-radius: 0; /* Removed border-radius */
  height: 100px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 20px;
  border: 1px dashed #dee2e6;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.trust-logo-placeholder:hover {
  transform: translateY(-5px);
  box-shadow: none; /* Removed box-shadow */
}

.placeholder-text {
  color: #555;
  font-weight: 500;
}

/* CTA Section */
.cta-section {
  background-color: var(--primary-dark);
  background-image: linear-gradient(135deg, var(--primary-dark) 0%, #0a2463 100%);
  padding: 80px 0;
  color: white;
}

.cta-content {
  max-width: 700px;
  margin: 0 auto;
}

.cta-title {
  font-size: 36px;
  font-weight: 700;
  margin-bottom: 20px;
  color: white;
}

.cta-subtitle {
  font-size: 18px;
  margin-bottom: 30px;
  color: rgba(255, 255, 255, 0.8);
}

.cta-form-container {
  max-width: 500px;
  margin: 0 auto;
}

.cta-form {
  margin-bottom: 15px;
}

.form-row {
  display: flex;
  flex-wrap: wrap;
  margin: 0 -10px;
}

.form-group {
  flex: 1;
  padding: 0 10px;
  min-width: 200px;
  margin-bottom: 15px;
}

.form-control {
  width: 100%;
  padding: 12px 15px;
  border-radius: 0; /* Removed border-radius */
  border: none;
  font-size: 16px;
}

.cta-note {
  font-size: 14px;
  color: rgba(255, 255, 255, 0.7);
  margin-top: 15px;
}

.cta-buttons {
  display: flex;
  justify-content: center;
  flex-wrap: wrap;
}

.cta-section .btn-primary {
  background-color: var(--primary-light);
  border-color: var(--primary-light);
  width: 100%;
}

.cta-section .btn-primary:hover {
  background-color: var(--primary);
  border-color: var(--primary);
}

.cta-section .btn-outline-primary {
  color: white;
  border-color: white;
}

.cta-section .btn-outline-primary:hover {
  background-color: white;
  color: var(--primary-dark);
}

/* Contact Section */
.contact-section {
  background-color: white;
  padding: 80px 0;
}

.contact-content {
  max-width: 600px;
  margin: 0 auto;
}

.contact-title {
  font-size: 36px;
  font-weight: 700;
  color: var(--primary-dark);
  margin-bottom: 40px;
}

.contact-info {
  display: flex;
  flex-direction: column;
  gap: 30px;
}

.contact-item {
  text-align: center;
}

.contact-label {
  font-size: 18px;
  font-weight: 600;
  color: var(--primary);
  margin-bottom: 10px;
}

.contact-value {
  font-size: 20px;
  color: #333;
}

.social-icons {
  display: flex;
  justify-content: center;
  gap: 15px;
  margin-top: 10px;
}

.social-icon {
  width: 40px;
  height: 40px;
  border-radius: 0; /* Removed border-radius */
  background-color: var(--primary-light);
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

.social-icon:hover {
  background-color: var(--primary);
  transform: translateY(-3px);
}

/* Mobile CTA Bar */
.mobile-cta-bar {
  display: none;
  position: fixed;
  bottom: 0;
  left: 0;
  width: 100%;
  background-color: var(--primary-dark);
  padding: 15px 0;
  box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.2);
  z-index: 1000;
}

.mobile-cta-bar .btn {
  padding: 10px;
  width: 100%;
}

.mobile-cta-bar .btn-primary {
  background-color: var(--primary-light);
  border-color: var(--primary-light);
}

.mobile-cta-bar .btn-primary:hover {
  background-color: var(--primary);
  border-color: var(--primary);
}

.mobile-cta-bar .btn-outline-primary {
  color: white;
  border-color: white;
  background-color: transparent;
}

.mobile-cta-bar .btn-outline-primary:hover {
  background-color: white;
  color: var(--primary-dark);
}

/* Blog Section */
.blog-section {
  background-color: white;
  padding: 0;
  margin-top: -20px;
}

.row {
  display: flex;
  flex-wrap: wrap;
  margin-right: -15px;
  margin-left: -15px;
}

.col-lg-5, .col-lg-6, .col-lg-7 {
  position: relative;
  width: 100%;
  padding-right: 15px;
  padding-left: 15px;
}

@media (min-width: 992px) {
  .col-lg-5 {
    flex: 0 0 41.666667%;
    max-width: 41.666667%;
  }
  
  .col-lg-6 {
    flex: 0 0 50%;
    max-width: 50%;
  }
  
  .col-lg-7 {
    flex: 0 0 58.333333%;
    max-width: 58.333333%;
  }
}

/* Section Spacing */
section:not(:first-child) {
  position: relative;
}

/* section:not(:first-child)::before {
  content: '';
  position: absolute;
  top: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 100px;
  height: 4px;
  background-color: var(--primary-light);
  border-radius: 0; 
  opacity: 0.3;
} */

/* Responsive Styles */
@media (max-width: 992px) {
  .hero-background {
    padding: 60px 0;
  }
  
  .hero-title {
    font-size: 36px;
  }
  
  .hero-tagline {
    font-size: 22px;
  }
  
  .hero-subtitle {
    font-size: 16px;
  }
  
  .section-title {
    font-size: 30px;
  }
  
  .numbered-feature-title {
    font-size: 20px;
  }
  
  .about-title,
  .cta-title,
  .contact-title {
    font-size: 30px;
  }
  
  .stat-value {
    font-size: 30px;
  }
}

@media (max-width: 768px) {
  section {
    /* padding: 60px 0; */
    width: 100%;
  }
  
  .hero-split-background {
    height: auto;
    min-height: 500px;
    padding: 40px 0;
    align-items: flex-start;
    justify-content: center;
  }

  .hero-split-background-home {
    height: 100vh;
    padding: 40px 0;
  }
  
  .hero-background {
    padding: 40px 0 60px;
    text-align: center;
  }
  
  .hero-content {
    height: auto;
    min-height: 300px;
    padding: 0 10px;
    justify-content: center;
    align-items: center;
  }
  
  .hero-buttons {
    justify-content: center;
    flex-direction: column;
    width: 100%;
  }
  
  .hero-cta-button, .hero-secondary-button {
    width: 100%;
    margin-right: 0;
    margin-bottom: 15px;
  }

  .hero-title {
    font-size: 32px;
  }

  /* Enhanced Hero Mobile Styles */
  .hero-title-enhanced {
    font-size: 48px;
    line-height: 1.0;
    text-align: center;
  }

  .typewriter-text {
    font-size: 20px;
    min-height: 28px;
    text-align: center;
  }

  .subtitle-static {
    font-size: 16px;
    text-align: center;
  }

  .hero-buttons-enhanced {
    justify-content: center;
    width: 100%;
  }

  .hero-cta-button-enhanced {
    padding: 16px 40px;
    font-size: 16px;
    width: 100%;
    max-width: 280px;
  }

  .hero-background-circles {
    display: none; /* Hide circles on mobile for performance */
  }
  
  .hero-tagline {
    font-size: 20px;
  }
  
  .section-title {
    font-size: 26px;
  }
  
  .features-numbered-grid {
    grid-template-columns: 1fr;
  }
  
  .numbered-feature-card {
    padding: 20px;
  }
  
  .feature-number {
    font-size: 36px;
  }
  
  .feature-card {
    padding: 20px;
  }
  
  .feature-icon {
    margin-top: -40px;
    width: 50px;
    height: 50px;
    font-size: 28px;
  }
  
  .feature-title {
    font-size: 18px;
  }
  
  .feature-description {
    font-size: 14px;
  }
  
  .testimonials-grid {
    grid-template-columns: 1fr;
  }
  
  .about-stats {
    flex-direction: column;
    gap: 20px;
  }
  
  .stat-item {
    margin-bottom: 20px;
  }
  
  .contact-info {
    gap: 20px;
  }
  
  .mobile-cta-bar {
    display: block;
  }
  
  /* Add padding to the bottom to account for the fixed CTA bar */
  /* body {
    padding-bottom: 70px;
  }
   */
  .pain-point-description {
    display: none; /* Hide descriptions on mobile to save space */
  }
}

/* Accessibility - For users who prefer reduced motion */
@media (prefers-reduced-motion: reduce) {
  .feature-card, 
  .pain-point-card,
  .numbered-feature-card,
  .testimonial-card,
  .trust-logo-placeholder {
    transition: none;
  }
  
  .feature-card:hover,
  .pain-point-card:hover,
  .numbered-feature-card:hover,
  .testimonial-card:hover,
  .trust-logo-placeholder:hover,
  .social-icon:hover {
    transform: none;
  }
  
  .btn:hover {
    transform: none;
  }
}

/* === CONTACT SECTION (Service Section Redesign) === */


@media (max-width: 600px) {
  .contact-section-bg {
    padding: 0;
    width: 100vw;
    margin: 0;
    margin-left: 50%;
    transform: translateX(-50%);
    border-radius: 0;
    background-image: url('/assets/img/landing/service-bg.png');
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
  }
  .contact-section-left {
    padding-right: 0;
    text-align: center;
    margin-bottom: 12px;
  }
  .contact-section-right {
    margin-top: 0;
    padding-top: 0;
    max-width: 100%;
    width: 100%;
    gap: 0;
  }
  .contact-form {
    padding: 8px 0 8px 0;
    border-radius: 8px;
    width: 100%;
    min-width: 0;
  }
  .contact-form-group {
    width: 100%;
    margin-bottom: 12px;
  }
  .contact-item-CTA,
  .contact-form-group textarea,
  .contact-form-group select {
    width: 100% !important;
    min-width: 0;
    font-size: 15px;
    box-sizing: border-box;
  }
  .contact-form-group:last-child {
    margin-bottom: 0;
  }
  .contact-form-group button,
  .contact-form-group .NurtifyButton {
    width: 100% !important;
    min-width: 0;
    margin-top: 12px;
    font-size: 16px;
  }
}

/* Extra small devices */
@media (max-width: 400px) {
  .contact-section-bg {
    margin: 10px 0;
    border-radius: 8px;
  }
  .contact-section-container {
    padding: 4px 0;
    gap: 6px;
    border-radius: 8px !important;
  }
  .contact-section-left {
    font-size: 15px;
    margin-bottom: 8px;
  }
  .contact-form {
    padding: 4px 0 4px 0;
    border-radius: 6px;
  }
  .contact-form-group label {
    font-size: 13px;
  }
  .contact-item-CTA,
  .contact-form-group textarea,
  .contact-form-group select {
    font-size: 13px;
    padding: 8px 6px;
  }
}

/* Core Values Section */
.core-values-section {
  padding: 80px 0;
  position: relative;
  overflow: hidden;
  background-image: 
    url('/assets/img/about-us/Vector.png');
  background-repeat: no-repeat;
  background-position: right top;
  background-size: auto 80%;
}

.core-values-section::before {
  content: "";
  position: absolute;
  right: -120px;
  top: -80px;
  width: 520px;
  height: 520px;
  background: radial-gradient(closest-side, rgba(55,183,195,0.15), rgba(55,183,195,0));
  filter: blur(0.5px);
  pointer-events: none;
}

.core-values-section::after {
  content: "";
  position: absolute;
  left: -160px;
  bottom: -120px;
  width: 520px;
  height: 520px;
  background: radial-gradient(closest-side, rgba(8,131,149,0.12), rgba(8,131,149,0));
  filter: blur(0.5px);
  pointer-events: none;
}

.core-values-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 24px;
  display: grid;
  grid-template-columns: 1.2fr 1fr;
  gap: 48px;
  align-items: center;
}

.core-values-left { width: 100%; }

.core-values-title {
  font-size: 36px;
  font-weight: 800;
  color: #071952;
  margin-bottom: 80px;
  text-align: center;
}

.values-accordion {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.value-item {
  background: #fff;
  border-radius: 12px;
  box-shadow: 0 10px 30px rgba(55, 183, 195, 0.15);
  overflow: hidden;
  transition: box-shadow 0.25s ease, border-color 0.25s ease, transform 0.25s ease;
}

.value-item.active {
  outline: none;
  transform: translateY(-2px);
}

.value-header {
  width: 100%;
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 14px 16px;
  border: none;
  cursor: pointer;
  text-align: left;
  transition: background 0.2s;
  outline: none;
}

.value-item.active .value-header {
  background-color: #EBF4F6;
}

.value-letter {
  width: 30px;
  height: 30px;
  min-width: 30px;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  border-radius: 8px;
  background: linear-gradient(135deg, var(--primary) 0%, #088395 100%);
  color: #fff;
  font-weight: 700;
  font-size: 14px;
}

.value-title {
  flex: 1;
  font-size: 16px;
  font-weight: 600;
  color: #071952;
}

.chevron {
  transition: transform 0.25s ease, color 0.25s ease;
  color: #78909c;
}

.value-item.active .chevron {
  transform: rotate(180deg);
  color: var(--primary);
}

.value-body {
  max-height: 0;
  overflow: hidden;
  padding: 2px 16px;
  color: #333;
  font-size: 14px;
  margin-top: 10px;
  line-height: 1.6;
  transition: max-height 0.3s ease, padding 0.3s ease;
}

.value-item.active .value-body {
  max-height: 240px;
  padding: 0 16px 16px;
}

.core-values-right {
  display: flex;
  justify-content: center;
}

.core-values-image-wrap {
  width: 100%;
  max-width: 520px;
  border-radius: 16px;
  overflow: hidden;
  box-shadow: 0 18px 45px rgba(7, 25, 82, 0.12);
  background: #fff;
}

.core-values-image-wrap img {
  display: block;
  width: 100%;
  height: auto;
}

@media (max-width: 992px) {
  .core-values-container {
    grid-template-columns: 1fr;
    gap: 30px;
  }
  .core-values-title {
    font-size: 28px;
    text-align: center;
  }
  .values-accordion {
    max-width: 700px;
    margin: 0 auto;
  }
  .core-values-right {
    order: 2;
  }
  .core-values-left {
    order: 1;
  }
}

@media (max-width: 600px) {
  .core-values-section {
    padding: 60px 0;
  }
  .core-values-title {
    font-size: 24px;
  }
  .value-title {
    font-size: 15px;
  }
  .value-body {
    font-size: 13px;
  }
}

/* Vision and Mission Section */
.vision-mission-section {
  background: #EBF4F6;
  padding: 80px 0;
}

.vision-mission-container {
  max-width: 1200px;
  margin: 0 auto;
  display: flex;
  align-items: stretch;
  gap: 60px;
  padding: 0 24px;
}

.vision-card {
  flex: 1;
  background: linear-gradient(135deg, #37B7C3 0%, #2da8b4 100%);
  border-radius: 20px;
  padding: 50px 40px;
  color: white;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  text-align: left;
  min-height: 400px;
  box-shadow: 0 10px 30px rgba(55, 183, 195, 0.2);
}

.vision-card:hover{
  transform: translateY(-5px);
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
}

.vision-icon {
  margin-bottom: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.vision-icon svg {
  width: 48px;
  height: 48px;
}

.vision-title {
  font-size: 32px;
  font-weight: 700;
  color: white;
  margin-bottom: 25px;
  line-height: 1.2;
}

.vision-description {
  font-size: 18px;
  line-height: 1.6;
  color: rgba(255, 255, 255, 0.95);
  font-weight: 400;
  margin: 0;
}

.mission-content {
  background-color: white;
  flex: 1;
  border-radius: 20px;
  padding: 50px 40px;
  color: white;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  text-align: left;
  min-height: 400px;
  box-shadow: 0 10px 30px rgba(55, 183, 195, 0.2);
}

.mission-content:hover{
  transform: translateY(-5px);
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
}

.mission-icon {
  margin-bottom: 30px;
  display: flex;
  align-items: center;
  justify-content: flex-start;
}

.mission-icon svg {
  width: 48px;
  height: 48px;
}

.mission-title {
  font-size: 32px;
  font-weight: 700;
  color: #071952;
  margin-bottom: 20px;
  line-height: 1.2;
}

.mission-intro {
  font-size: 18px;
  color: #071952;
  margin-bottom: 25px;
  font-weight: 500;
}

.mission-list {
  list-style: none;
  padding: 0;
  margin: 0;
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.mission-list li {
  font-size: 16px;
  color: #333;
  padding-left: 20px;
  position: relative;
  line-height: 1.5;
  font-weight: 400;
}

.mission-list li::before {
  content: "•";
  color: #37B7C3;
  font-size: 20px;
  position: absolute;
  left: 0;
  top: -2px;
  font-weight: bold;
}

/* Responsive styles for Vision and Mission section */
@media (max-width: 992px) {
  .vision-mission-container {
    flex-direction: column;
    gap: 40px;
    padding: 0 20px;
  }
  
  .vision-card {
    min-height: auto;
    padding: 40px 30px;
  }
  
  .vision-title,
  .mission-title {
    font-size: 28px;
  }
  
  .vision-description,
  .mission-intro {
    font-size: 16px;
  }
  
  .mission-list li {
    font-size: 15px;
  }
}

@media (max-width: 768px) {
  .vision-mission-section {
    padding: 60px 0;
  }
  
  .vision-mission-container {
    padding: 0 15px;
    gap: 30px;
  }
  
  .vision-card {
    padding: 30px 25px;
    text-align: center;
    align-items: center;
  }
  
  .mission-content {
    text-align: center;
    align-items: center;
  }
  
  .mission-icon {
    justify-content: center;
  }
  
  .vision-title,
  .mission-title {
    font-size: 24px;
  }
  
  .vision-description,
  .mission-intro {
    font-size: 15px;
  }
  
  .mission-list {
    text-align: left;
    max-width: 300px;
  }
  
  .mission-list li {
    font-size: 14px;
  }
}

@media (max-width: 480px) {
  .vision-mission-section {
    padding: 50px 0;
  }
  
  .vision-card {
    padding: 25px 20px;
  }
  
  .vision-title,
  .mission-title {
    font-size: 22px;
    margin-bottom: 20px;
  }
  
  .vision-description,
  .mission-intro {
    font-size: 14px;
  }
  
  .mission-list li {
    font-size: 13px;
    gap: 12px;
  }
  
  .mission-icon svg,
  .vision-icon svg {
    width: 40px;
    height: 40px;
  }
}

/* Team Section */
.team-section {
  background: #EBF4F6;
  padding: 80px 0;
}

.team-header {
  text-align: center;
  margin-bottom: 60px;
}

.team-title {
  font-size: 36px;
  font-weight: 800;
  color: #090914;
  margin-bottom: 20px;
  line-height: 1.2;
}

.team-subtitle {
  font-size: 16px;
  color: #090914;
  line-height: 1.6;
  margin: 0 auto 20px;
}

.team-divider {
  width: 60px;
  height: 4px;
  background-color: #FF007F;
  margin: 0 auto;
  border-radius: 2px;
}

.team-grid {
  max-width: 1200px;
  margin: 0 auto;
  display: flex;
  flex-direction: column;
  gap: 40px;
}

.team-row {
  display: flex;
  justify-content: center;
  gap: 40px;
  flex-wrap: wrap;
}

/* Top row with 2 larger cards */
.team-row-large {
  justify-content: center;
  gap: 40px;
}

/* Bottom row with 4 smaller cards */
.team-row-small {
  justify-content: center;
  gap: 20px;
}

.team-member-card {
  background: white;
  border-radius: 20px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  text-align: left;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  position: relative;
}

/* Large cards for top row - horizontal layout */
.team-member-card-large {
  flex: 1;
  min-width: 550px;
  max-width: 580px;
  padding: 10px;
  display: flex;
  align-items: flex-start;
  gap: 20px;
}

@media (max-width: 700px) {
  .team-member-card-large {
    min-width: 240px;
    max-width: 280px;
    padding: 25px;
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    gap: 0;
  }
}

/* Small cards for bottom row - vertical layout */
.team-member-card-small {
  flex: 1;
  min-width: 240px;
  max-width: 280px;
  padding: 25px;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
}

.team-member-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
}

/* Large card image - left side */
.team-member-image-large {
  width: 180px;
  height: 240px;
  border-radius: 15px;
  overflow: hidden;
  flex-shrink: 0;
  background-color: #f5f5f5;
}

.team-member-image-large img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

/* Large card content - right side */
.team-member-content-large {
  flex: 1;
  display: flex;
  flex-direction: column;
}

/* Responsive: Make large card image behave like small card image on small screens */
@media (max-width: 700px) {
  .team-member-image-large {
    width: 100%;
    height: 160px;
    margin-bottom: 15px;
    border-radius: 15px;
    background-color: #f5f5f5;
    flex-shrink: 0;
  }
  .team-member-image-large img {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }
  .team-member-content-large {
    width: 100%;
    flex: none;
    display: flex;
    flex-direction: column;
  }
}

/* Small card image - top */
.team-member-image-small {
  width: 100%;
  height: 240px;
  border-radius: 15px;
  overflow: hidden;
  margin-bottom: 15px;
  background-color: #f5f5f5;
}

.team-member-image-small img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

/* Small card content - bottom */
.team-member-content-small {
  width: 100%;
}

.team-member-name {
  font-size: 18px;
  font-weight: 600;
  color: #090914;
  margin: 0 0 5px 0;
}

.team-member-title {
  font-size: 14px;
  font-weight: 400;
  color: #666;
  margin: 0 0 15px 0;
}

.team-member-description {
  font-size: 14px;
  color: #666;
  line-height: 1.5;
  margin: 0;
}

.linkedin-icon {
  position: absolute;
  top: 20px;
  right: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  border-radius: 50%;
  transition: transform 0.3s ease;
  text-decoration: none;
  z-index: 10;
}

.linkedin-icon:hover {
  transform: scale(1.1);
  text-decoration: none;
}

/* Responsive styles for team section */
@media (max-width: 992px) {
  .team-section {
    padding: 60px 0;
  }
  
  .team-title {
    font-size: 30px;
  }
  
  .team-row {
    gap: 30px;
  }
  
  .team-member-card {
    max-width: 100%;
    min-width: 250px;
  }
}

@media (max-width: 768px) {
  .team-section {
    padding: 50px 0;
  }
  
  .team-title {
    font-size: 26px;
  }
  
  .team-subtitle {
    padding: 0 20px;
  }
  
  .team-row {
    flex-direction: column;
    align-items: center;
    gap: 25px;
  }
  
  .team-member-card {
    max-width: 400px;
    margin: 0 20px;
  }
  
  .team-member-card {
    padding: 30px 25px 25px;
  }
}

@media (max-width: 480px) {
  .team-section {
    padding: 40px 0;
  }
  
  .team-header {
    margin-bottom: 40px;
  }
  
  .team-title {
    font-size: 24px;
  }
  
  .team-subtitle {
    font-size: 15px;
  }
  
  .team-member-card {
    margin: 0 15px;
    padding: 25px 20px 20px;
  }
  
  .team-member-image {
    width: 70px;
    height: 70px;
  }
  
  .team-member-name {
    font-size: 16px;
  }
  
  .team-member-description {
    font-size: 13px;
  }
}

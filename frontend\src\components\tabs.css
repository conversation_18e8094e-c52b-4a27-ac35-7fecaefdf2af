/* frontend/src/components/tabs.css */

.nurtify-tabs-wrapper {
    display: flex;
    gap: 1rem;
    border-bottom: 1px solid #e5e7eb;
    padding-bottom: 0;
    margin-bottom: 30px; /* Keep margin for spacing below tabs */
}

.nurtify-tab-button {
    background: none;
    border: none;
    padding: 0.75rem 1.25rem;
    font-size: 1rem;
    color: #6b7280; /* Default text color */
    position: relative;
    cursor: pointer;
    transition: all 0.3s ease;
    text-align: center;
    border-radius: 8px 8px 0 0; /* Rounded top corners */
}

.nurtify-tab-button:hover {
    color: #374151; /* Darker text on hover */
    background-color: rgba(55, 183, 195, 0.05); /* Light teal background on hover */
}

.nurtify-tab-button.active {
    color: #37B7C3; /* Nurtify teal for active text */
    background-color: rgba(55, 183, 195, 0.08); /* Slightly darker teal background when active */
}

/* Active indicator line */
.nurtify-tab-button.active::after {
    content: '';
    position: absolute;
    bottom: -1px; /* Position just below the button, overlapping the wrapper border */
    left: 0;
    width: 100%;
    height: 3px;
    background-color: #37B7C3; /* Nurtify teal indicator */
}

/* Responsive Adjustments */
@media (max-width: 991px) {
    .nurtify-tabs-wrapper {
        flex-wrap: wrap;
        gap: 0.5rem;
    }

    .nurtify-tab-button {
        flex: 1 0 auto; /* Allow buttons to grow but have a base size */
        min-width: 120px; /* Minimum width for tabs */
        text-align: center;
    }
}

@media (max-width: 767px) {
    .nurtify-tabs-wrapper {
        flex-direction: column; /* Stack tabs vertically */
        gap: 0.5rem;
        border-bottom: none; /* Remove bottom border when stacked */
    }

    .nurtify-tab-button {
        width: 100%;
        text-align: left; /* Align text left when stacked */
        border-radius: 8px; /* Apply rounding to all corners */
    }

    /* Remove bottom indicator line when stacked */
    .nurtify-tab-button.active::after {
        display: none;
    }

    /* Use background color for active state when stacked */
    .nurtify-tab-button.active {
        background-color: rgba(55, 183, 195, 0.15);
    }
}

import React, { useState, useEffect, useCallback } from 'react';
import { getVitalSignHistory } from '../../services/api/vital-signs.service';
import { formatDate } from '../../utils/vitalSignsUtils';
import { VitalSign } from '../../services/api/vital-signs.service';
import './VitalSignsHistory.css';

interface VitalSignHistoryEntry {
  uuid: string;
  field_name: string;
  old_value: string | null;
  new_value: string;
  changed_by: {
    id: number;
    email: string;
    first_name: string;
    last_name: string;
  };
  changed_at: string;
  notes?: string | null;
}

interface VitalSignsHistoryProps {
  patientId: string;
  vitalSigns: VitalSign[];
  isVisible: boolean;
  onClose: () => void;
}


// Main vital sign fields for field selection
const mainVitalSignFields = [
  { key: 'temperature', label: 'Temp (°C)', unit: '°C' },
  { key: 'heart_rate', label: 'HR (bpm)', unit: 'bpm' },
  { key: 'systolic_bp', label: 'SBP (mmHg)', unit: 'mmHg' },
  { key: 'diastolic_bp', label: 'DBP (mmHg)', unit: 'mmHg' },
  { key: 'respiratory_rate', label: 'RR (br/min)', unit: 'br/min' },
  { key: 'oxygen_saturation', label: 'SpO2 (%)', unit: '%' },
  { key: 'blood_sugar', label: 'Blood Glucose (mg/dL)', unit: 'mg/dL' },
  { key: 'height', label: 'Height (cm)', unit: 'cm' },
  { key: 'weight', label: 'Weight (kg)', unit: 'kg' },
];

const VitalSignsHistory: React.FC<VitalSignsHistoryProps> = ({ 
  patientId, 
  vitalSigns, 
  isVisible, 
  onClose 
}) => {
  const [history, setHistory] = useState<VitalSignHistoryEntry[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [filters, setFilters] = useState({
    severity: 'all',
    showToday: false,
    lastHours: '',
    fromDate: '',
    toDate: '',
    changed_by: '',
    start_date: '',
    end_date: ''
  });
  const [selectedRecord, setSelectedRecord] = useState<any | null>(null);
  const [selectedField, setSelectedField] = useState<string | null>(null);


  // Helper: severity logic (stub, adjust as needed)
  const getSeverity = (record: any) => {
    if (typeof record.news_score === 'number') {
      if (record.news_score >= 7) return 'critical';
      if (record.news_score >= 5) return 'high';
      if (record.news_score >= 1) return 'low';
      return 'normal';
    }
    return 'normal';
  };

  // Helper: filter records based on filters
  const getFilteredRecords = () => {
    // Start with ALL records, not just recent ones
    let records = [...vitalSigns];
    const now = new Date();
    
    console.log('🔍 VitalSignsHistory Filtering:', {
      totalRecords: vitalSigns.length,
      fromDate: filters.fromDate,
      toDate: filters.toDate,
      showToday: filters.showToday,
      lastHours: filters.lastHours,
      severity: filters.severity
    });
    
    // Date range filter
    if (filters.fromDate) {
      const from = new Date(filters.fromDate + 'T00:00:00');
      console.log('🔍 From date filter:', from.toISOString());
      records = records.filter(r => {
        const recordDate = new Date(r.recorded_at);
        return recordDate >= from;
      });
      console.log('🔍 After from date filter:', records.length, 'records');
    }
    
    if (filters.toDate) {
      const to = new Date(filters.toDate + 'T23:59:59');
      console.log('🔍 To date filter:', to.toISOString());
      records = records.filter(r => {
        const recordDate = new Date(r.recorded_at);
        return recordDate <= to;
      });
      console.log('🔍 After to date filter:', records.length, 'records');
    }
    
    // Today filter
    if (filters.showToday) {
      const today = new Date();
      today.setHours(0, 0, 0, 0);
      console.log('🔍 Today filter:', today.toISOString());
      records = records.filter(r => {
        const recordDate = new Date(r.recorded_at);
        return recordDate >= today;
      });
      console.log('🔍 After today filter:', records.length, 'records');
    }
    
    // Last X hours filter
    if (filters.lastHours && !isNaN(Number(filters.lastHours))) {
      const cutoff = new Date(now.getTime() - Number(filters.lastHours) * 60 * 60 * 1000);
      console.log('🔍 Last hours filter:', cutoff.toISOString());
      records = records.filter(r => {
        const recordDate = new Date(r.recorded_at);
        return recordDate >= cutoff;
      });
      console.log('🔍 After last hours filter:', records.length, 'records');
    }
    
    // Severity filter
    if (filters.severity !== 'all') {
      records = records.filter(r => getSeverity(r) === filters.severity);
      console.log('🔍 After severity filter:', records.length, 'records');
    }
    
    return records.sort((a, b) => new Date(b.recorded_at).getTime() - new Date(a.recorded_at).getTime());
  };

  // Helper: format summary for a record
  const vitalSignColumns = [
    { key: 'temperature', label: 'Temp', unit: '°C' },
    { key: 'heart_rate', label: 'HR', unit: 'bpm' },
    { key: 'systolic_bp', label: 'SBP', unit: 'mmHg' },
    { key: 'diastolic_bp', label: 'DBP', unit: 'mmHg' },
    { key: 'respiratory_rate', label: 'RR', unit: 'br/min' },
    { key: 'oxygen_saturation', label: 'SpO2', unit: '%' },
    { key: 'blood_sugar', label: 'Blood Glucose', unit: 'mg/dL' },
    { key: 'height', label: 'Height', unit: 'cm' },
    { key: 'weight', label: 'Weight', unit: 'kg' },
  ];
  const formatDateTime = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-GB', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };
  const formatSummary = (record: any) => {
    return [
      formatDateTime(record.recorded_at),
      ...vitalSignColumns.map(col => {
        const value = record[col.key];
        return value !== undefined && value !== null ? `${value} ${col.unit}` : null;
      }).filter(Boolean)
    ].join(' | ');
  };

  const loadHistory = useCallback(async (fieldName: string) => {
    setIsLoading(true);
    setError(null);
    try {
      // Load history for the specific record
      const allHistory: VitalSignHistoryEntry[] = await getVitalSignHistory(selectedRecord.uuid);
      // Only keep entries for the selected field
      const historyData = allHistory
        .filter(h => h.field_name === fieldName)
        .sort((a, b) => new Date(b.changed_at).getTime() - new Date(a.changed_at).getTime());
      setHistory(historyData);
    } catch (err) {
      console.error('Error loading vital signs history:', err);
      setError('Failed to load history data. Please try again.');
    } finally {
      setIsLoading(false);
    }
  }, [selectedRecord]);

  useEffect(() => {
    if (isVisible && patientId && selectedField) {
      loadHistory(selectedField);
    }
  }, [isVisible, patientId, selectedField, filters, loadHistory]);

  const getFieldDisplayName = (fieldName: string): string => {
    const fieldMap: Record<string, string> = {
      'created': 'Record Created',
      'temperature': 'Temperature',
      'temp_unit': 'Temperature Unit',
      'temp_location': 'Temperature Location',
      'heart_rate': 'Heart Rate',
      'systolic_bp': 'Systolic Blood Pressure',
      'diastolic_bp': 'Diastolic Blood Pressure',
      'respiratory_rate': 'Respiratory Rate',
      'oxygen_saturation': 'Oxygen Saturation',
      'consciousness_level': 'Consciousness Level',
      'supplemental_oxygen': 'Supplemental Oxygen',
      'blood_sugar': 'Blood Sugar',
      'blood_sugar_unit': 'Blood Sugar Unit',
      'height': 'Height',
      'height_unit': 'Height Unit',
      'weight': 'Weight',
      'weight_unit': 'Weight Unit',
      'news_score': 'NEWS Score',
      'news_severity': 'NEWS Severity',
      'notes': 'Notes'
    };
    
    return fieldMap[fieldName] || fieldName.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase());
  };

  const getChangeType = (fieldName: string, oldValue: string | null, newValue: string): string => {
    if (fieldName === 'created') return 'created';
    if (oldValue === null) return 'added';
    if (newValue === '') return 'removed';
    return 'updated';
  };

  const getChangeIcon = (changeType: string): string => {
    switch (changeType) {
      case 'created': return '➕';
      case 'added': return '➕';
      case 'updated': return '✏️';
      case 'removed': return '➖';
      default: return '📝';
    }
  };

  const getChangeColor = (changeType: string): string => {
    switch (changeType) {
      case 'created': return '#10b981';
      case 'added': return '#10b981';
      case 'updated': return '#f59e0b';
      case 'removed': return '#ef4444';
      default: return '#6b7280';
    }
  };

  const formatValue = (value: string | null, fieldName: string): string => {
    if (value === null || value === '') return '—';
    
    // Handle boolean values
    if (value === 'true') return 'Yes';
    if (value === 'false') return 'No';
    
    // Handle special field formatting
    switch (fieldName) {
      case 'consciousness_level': {
        const levelMap: Record<string, string> = {
          'A': 'Alert',
          'V': 'Voice',
          'P': 'Pain',
          'U': 'Unresponsive'
        };
        return levelMap[value] || value;
      }
      case 'news_severity':
        return value.replace('-', ' ').replace(/\b\w/g, l => l.toUpperCase());
      default:
        return value;
    }
  };

  if (!isVisible) return null;

  return (
    <div className="vital-signs-history-overlay">
      <div className="vital-signs-history-modal">
        <div className="history-header">
          {selectedField ? (
            <>
              <button className="back-button" onClick={() => setSelectedField(null)} title="Back to field list">←</button>
              <h2>{getFieldDisplayName(selectedField)} History</h2>
            </>
          ) : selectedRecord ? (
            <>
              <button className="back-button" onClick={() => setSelectedRecord(null)} title="Back to record list">←</button>
              <h2>Fields for {formatDateTime(selectedRecord.recorded_at)}</h2>
            </>
          ) : (
          <h2>Vital Signs History</h2>
          )}
          <button className="close-button" onClick={onClose}>×</button>
        </div>

        {!selectedRecord ? (
          <>
            {/* Debug info for VitalSignsHistory */}
            <div style={{ 
              background: '#f0f0f0', 
              padding: '10px', 
              margin: '10px 0', 
              borderRadius: '4px', 
              fontSize: '12px',
              border: '1px solid #ddd'
            }}>
              <strong>VitalSignsHistory Debug:</strong><br/>
              Total records: {vitalSigns.length}<br/>
              Filtered records: {getFilteredRecords().length}<br/>
              From date: {filters.fromDate || 'none'}<br/>
              To date: {filters.toDate || 'none'}<br/>
              Show today: {filters.showToday ? 'yes' : 'no'}<br/>
              Last hours: {filters.lastHours || 'none'}<br/>
              Severity: {filters.severity}<br/>
              {vitalSigns.length > 0 && (
                <>
                  <br/>
                  <strong>Date Range:</strong><br/>
                  Oldest: {new Date(vitalSigns[vitalSigns.length - 1]?.recorded_at).toLocaleDateString()}<br/>
                  Newest: {new Date(vitalSigns[0]?.recorded_at).toLocaleDateString()}
                </>
              )}
            </div>
            
            <div className="vital-signs-filter-bar">
              <label>
                Severity:
                <select value={filters.severity} onChange={e => setFilters(f => ({ ...f, severity: e.target.value }))}>
                  <option value="all">All</option>
                  <option value="critical">Critical</option>
                  <option value="high">High</option>
                  <option value="low">Low</option>
                  <option value="normal">Normal</option>
            </select>
              </label>
              <label>
                <input type="checkbox" checked={filters.showToday} onChange={e => setFilters(f => ({ ...f, showToday: e.target.checked }))} />
                Today's results
              </label>
              <label>
                Last
                <input
                  type="number"
                  min="1"
                  style={{ width: 60, margin: '0 4px' }}
                  value={filters.lastHours}
                  onChange={e => setFilters(f => ({ ...f, lastHours: e.target.value }))}
                  placeholder="hours"
                />
                hours
              </label>
              <label>
                From:
                <input type="date" value={filters.fromDate} onChange={e => setFilters(f => ({ ...f, fromDate: e.target.value }))} />
              </label>
              <label>
                To:
                <input type="date" value={filters.toDate} onChange={e => setFilters(f => ({ ...f, toDate: e.target.value }))} />
              </label>
              <button 
                className="btn btn-small btn-secondary"
                onClick={() => {
                  setFilters({
                    severity: 'all',
                    showToday: false,
                    lastHours: '',
                    fromDate: '',
                    toDate: '',
                    changed_by: '',
                    start_date: '',
                    end_date: ''
                  });
                }}
                style={{ marginLeft: '10px' }}
              >
                Clear Filters
              </button>
            </div>
            <div className="vital-signs-list">
              <h3>Select a Vital Sign Record</h3>
              <ul className="vital-signs-type-list">
                {getFilteredRecords().map(record => (
                  <li key={record.uuid}>
                    <button className="vital-sign-type-btn" onClick={() => setSelectedRecord(record)}>
                      {formatSummary(record)}
                    </button>
                  </li>
                ))}
              </ul>
            </div>
            
            {/* Close button at bottom */}
            <div style={{ 
              display: 'flex', 
              justifyContent: 'center', 
              padding: '20px', 
              borderTop: '1px solid #e5e7eb',
              marginTop: '20px'
            }}>
              <button 
                className="btn btn-secondary"
                onClick={onClose}
                style={{ minWidth: '120px' }}
              >
                Close
              </button>
            </div>
          </>
        ) : !selectedField ? (
          <div className="vital-signs-list">
            <h3>Select a Field</h3>
            <ul className="vital-signs-type-list">
              {mainVitalSignFields.filter(field => selectedRecord && selectedRecord[field.key] !== undefined && selectedRecord[field.key] !== null).map(field => (
                <li key={field.key}>
                  <button className="vital-sign-type-btn" onClick={() => setSelectedField(field.key)}>
                    {field.label} — {selectedRecord[field.key]} {field.unit}
                  </button>
                </li>
              ))}
            </ul>
            
            {/* Close button at bottom */}
            <div style={{ 
              display: 'flex', 
              justifyContent: 'center', 
              padding: '20px', 
              borderTop: '1px solid #e5e7eb',
              marginTop: '20px'
            }}>
              <button 
                className="btn btn-secondary"
                onClick={onClose}
                style={{ minWidth: '120px' }}
              >
                Close
              </button>
            </div>
          </div>
        ) : (
          <>
            <div className="history-filters">
          <div className="filter-group">
            <label>Changed By:</label>
            <input
              type="text"
              placeholder="Search by user..."
              value={filters.changed_by}
              onChange={(e) => setFilters(prev => ({ ...prev, changed_by: e.target.value }))}
            />
          </div>
          <div className="filter-group">
            <label>Date Range:</label>
            <div className="date-range">
              <input
                type="date"
                placeholder="Start date"
                    value={filters.start_date}
                onChange={(e) => setFilters(prev => ({ ...prev, start_date: e.target.value }))}
              />
              <span>to</span>
              <input
                type="date"
                placeholder="End date"
                    value={filters.end_date}
                onChange={(e) => setFilters(prev => ({ ...prev, end_date: e.target.value }))}
              />
            </div>
          </div>
              <button className="btn btn-primary" onClick={() => loadHistory(selectedField)}>
            Refresh
          </button>
        </div>
        <div className="history-content">
          {isLoading ? (
            <div className="history-loading">
              <div className="loading-spinner"></div>
              <p>Loading history...</p>
            </div>
          ) : error ? (
            <div className="history-error">
              <p>{error}</p>
                  <button className="btn btn-primary" onClick={() => loadHistory(selectedField)}>
                Try Again
              </button>
            </div>
          ) : history.length === 0 ? (
            <div className="history-empty">
              <p>No history records found.</p>
            </div>
          ) : (
            <div className="history-timeline">
              {history.map((entry) => {
                const changeType = getChangeType(entry.field_name, entry.old_value, entry.new_value);
                const changeIcon = getChangeIcon(changeType);
                const changeColor = getChangeColor(changeType);
                return (
                  <div key={entry.uuid} className="history-entry">
                    <div className="history-timeline-marker" style={{ backgroundColor: changeColor }}>
                      <span className="change-icon">{changeIcon}</span>
                    </div>
                    <div className="history-entry-content">
                      <div className="history-entry-header">
                        <h4>{getFieldDisplayName(entry.field_name)}</h4>
                        <span className="change-type" style={{ color: changeColor }}>
                          {changeType.charAt(0).toUpperCase() + changeType.slice(1)}
                        </span>
                      </div>
                      <div className="history-entry-details">
                        {entry.old_value !== null && (
                          <div className="value-change">
                            <span className="old-value">
                              From: {formatValue(entry.old_value, entry.field_name)}
                            </span>
                            <span className="arrow">→</span>
                            <span className="new-value">
                              To: {formatValue(entry.new_value, entry.field_name)}
                            </span>
                          </div>
                        )}
                        {entry.old_value === null && (
                          <div className="value-added">
                            <span className="new-value">
                              Set to: {formatValue(entry.new_value, entry.field_name)}
                            </span>
                          </div>
                        )}
                            <div className="history-meta">
                              <div className="history-user">
                                <span className="user-label">Changed by:</span>
                                <span className="user-name">
                                  {entry.changed_by.first_name} {entry.changed_by.last_name}
                                </span>
                                <span className="user-email">({entry.changed_by.email})</span>
                      </div>
                              <div className="history-timestamp">
                                <span className="timestamp-label">Date:</span>
                                <span className="timestamp-value">{formatDate(entry.changed_at)}</span>
                        </div>
                        {entry.notes && (
                          <div className="history-notes">
                                  <span className="notes-label">Notes:</span>
                                  <span className="notes-value">{entry.notes}</span>
                          </div>
                        )}
                            </div>
                      </div>
                    </div>
                  </div>
                );
              })}
            </div>
          )}
        </div>
        
        {/* Close button at bottom for history timeline */}
        <div style={{ 
          display: 'flex', 
          justifyContent: 'center', 
          padding: '20px', 
          borderTop: '1px solid #e5e7eb',
          marginTop: '20px'
        }}>
          <button 
            className="btn btn-secondary"
            onClick={onClose}
            style={{ minWidth: '120px' }}
          >
            Close
          </button>
        </div>
          </>
        )}
      </div>
    </div>
  );
};

export default VitalSignsHistory;

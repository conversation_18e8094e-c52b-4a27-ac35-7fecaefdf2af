import api from '../api';
import { Form, FormSubmission, HolisticFormData, HolisticFormDataResponse, PDFResponse, Tag } from '../../types/types';
import { FormPayload, FormVersionResponse, FormSubmissionQuery, FormSubmissionQueryCreate, FormSubmissionQueryUpdate, FormSubmissionQueryResolve, QueryResponse, QueryResponseCreate, QueryResponseUpdate } from './types';
import { AxiosError } from 'axios';

// Form APIs
export const getForms = async () => {
  const response = await api.get<{ count: number, results: Form[] }>('/form/forms/');
  return response.data;
};

export const getFormByUuid = async (uuid: string) => {
  const response = await api.get<Form>(`/form/forms/${uuid}/`);

  if (response.data.active_version) {
    if (!response.data.active_version.form_structure && response.data.active_version.structure) {
      try {
        if (typeof response.data.active_version.structure === 'string') {
          const structureData = JSON.parse(response.data.active_version.structure);
          response.data.active_version.structure_data = structureData;
        } else {
          response.data.active_version.structure_data = response.data.active_version.structure || {};
        }
      } catch (error) {
        console.error("Error parsing form structure:", error);
        response.data.active_version.structure_data = {};
      }
    }
  }

  return response.data;
};

export const getFormActiveVersionByUuid = async (uuid: string): Promise<FormVersionResponse> => {
  const { data } = await api.get(`/form/forms/${uuid}/active-version/`);

  // Process structure data if needed
  if (data && data.structure) {
    try {
      const structureData = JSON.parse(data.structure);
      data.structure_data = structureData;
    } catch (error) {
      console.error("Error parsing form structure:", error);
      data.structure_data = {};
    }
  }

  return data;
};

export const createForm = async (formData: FormPayload): Promise<Form> => {
  // Format the payload to match Django's expected format
  const payload = {
    name: formData.name,
    description: formData.description,
    categories: formData.categories,
    study: formData.study,
    password: formData.password,
    structure: typeof formData.structure === 'object'
      ? JSON.stringify(formData.structure)
      : formData.structure
  };

  const { data } = await api.post("/form/forms/", payload);
  return data;
};

export const updateForm = async (uuid: string, formData: FormPayload): Promise<Form> => {
  // Format the payload to match the Django API format
  const payload = {
    name: formData.name,
    description: formData.description,
    categories: formData.categories,
    study: formData.study,
    password: formData.password,
    structure: typeof formData.structure === 'object'
      ? JSON.stringify(formData.structure)
      : formData.structure,
    // Add the form_version_uuid if it exists to update the correct version
    form_version_uuid: formData.form_version_uuid
  };

  console.log("Sending form update payload:", payload);
  const response = await api.patch<Form>(`/form/forms/${uuid}/`, payload);
  return response.data;
};

export const deleteForm = async (uuid: string) => {
  const response = await api.delete(`/form/forms/${uuid}/`);
  return response.data;
};

export const getFormsByUser = async (identifier: string) => {
  const response = await api.get<Form[]>(`/form/forms/user/${identifier}/`);
  return response.data;
};

export const getFormsBySponsor = async (sponsorIdentifier: string) => {
  const response = await api.get<{
    count: number;
    results: Form[];
    sponsor: {
      uuid: string;
      name: string;
      email: string;
    };
  }>(`/form/forms/by-sponsor-query/`, {
    params: {
      sponsor_identifier: sponsorIdentifier
    }
  });
  return response.data;
};

export const getFormsByPatient = async (patientUuid: string) => {
  const response = await api.get<Form[]>(`/form/forms/patient/${patientUuid}/`);
  return response.data;
};

export const getFormVersions = async (uuid: string): Promise<FormVersionResponse[]> => {
  const response = await api.get(`/form/forms/${uuid}/versions/`);

  // Process structure data for each version if needed
  if (response.data && Array.isArray(response.data)) {
    response.data.forEach(version => {
      if (version.structure) {
        try {
          version.structure_data = JSON.parse(version.structure);
        } catch (error) {
          console.error("Error parsing version structure:", error);
          version.structure_data = {};
        }
      }
    });
  }

  return response.data;
};

export const acceptForm = async (uuid: string) => {
  const response = await api.patch(`/form/forms/${uuid}/accept/`);
  return response.data;
};

export const rejectForm = async (uuid: string) => {
  const response = await api.patch(`/form/forms/${uuid}/reject/`);
  return response.data;
};

// Form Submissions APIs
export const getSubmissions = async () => {
  const response = await api.get<{ count: number, results: FormSubmission[] }>('/form/submissions/');
  return response.data;
};

export const getSubmissionByUuid = async (uuid: string) => {
  const response = await api.get<FormSubmission>(`/form/submissions/${uuid}/`);
  return response.data;
};

export const createSubmission = async (submissionData: {
  form_uuid: string;
  user: number | { identifier: string; first_name?: string; last_name?: string; email?: string; };
  patient_uuid?: string;
  submission: Record<string, unknown>;
  is_completed?: boolean;
  final_submission?: boolean;
  existing_attachments?: string[]; // Add the new field
  attachments?: {
    image_attachments?: File[];
    video_attachments?: File[];
    document_attachments?: File[];
  };
}) => {
  // TRACKING: Log the incoming existing_attachments at the very start
  console.log('TRACKING: Initial existing_attachments in createSubmission:', {
    exists: submissionData.existing_attachments !== undefined,
    isArray: Array.isArray(submissionData.existing_attachments),
    length: submissionData.existing_attachments ? submissionData.existing_attachments.length : 0,
    value: submissionData.existing_attachments
  });
  // Check if we have any attachments
  const hasAttachments = submissionData.attachments && (
    (submissionData.attachments.image_attachments?.length || 0) > 0 ||
    (submissionData.attachments.video_attachments?.length || 0) > 0 ||
    (submissionData.attachments.document_attachments?.length || 0) > 0
  );

  // Store attachments for later use in case we need the two-step approach
  const attachmentsCopy = hasAttachments ? { ...submissionData.attachments } : undefined;

  // Create a clean submission object with no attachments for the initial create request
  const initialSubmission = {
    ...submissionData,
    attachments: undefined // Remove attachments for the initial creation
  };

  try {
    // Step 1: Create submission without files
    console.log("Creating submission without attachments first...");
    const formData = new FormData();

    formData.append('form_uuid', initialSubmission.form_uuid);

    // Format user object
    let userObj;

    if (typeof initialSubmission.user === 'number') {
      userObj = {
        identifier: initialSubmission.user.toString(),
        first_name: '',
        last_name: '',
        email: '<EMAIL>'
      };
    } else {
      userObj = {
        identifier: initialSubmission.user.identifier || "0",
        first_name: initialSubmission.user.first_name || '',
        last_name: initialSubmission.user.last_name || '',
        email: initialSubmission.user.email || '<EMAIL>'
      };
    }

    if (!userObj.identifier || userObj.identifier === "undefined") {
      userObj.identifier = "0";
    }

    formData.append('user', JSON.stringify(userObj));
    formData.append('submission', JSON.stringify(initialSubmission.submission));
    formData.append('is_completed', initialSubmission.is_completed !== undefined ? initialSubmission.is_completed.toString() : 'true');
    formData.append('final_submission', initialSubmission.final_submission !== undefined ? initialSubmission.final_submission.toString() : 'false');

    if (initialSubmission.patient_uuid) {
      formData.append('patient_uuid', initialSubmission.patient_uuid);
    }

    // Add existing_attachments if provided
    if (initialSubmission.existing_attachments && initialSubmission.existing_attachments.length > 0) {
      console.log(`Including ${initialSubmission.existing_attachments.length} existing attachments:`, initialSubmission.existing_attachments);
      // Append each UUID individually - this is critical for proper backend processing
      initialSubmission.existing_attachments.forEach(uuid => {
        formData.append('existing_attachments', uuid);
      });
      // Also include the JSON stringified version as a fallback
      formData.append('existing_attachments_json', JSON.stringify(initialSubmission.existing_attachments));
    }

    const response = await api.post<FormSubmission>('/form/submissions/', formData, {
      headers: { 'Content-Type': 'multipart/form-data' }
    });

    console.log("Initial submission created successfully:", response.data);

    // If we don't have attachments, return the response
    if (!hasAttachments) {
      return response.data;
    }

    // Step 2: If we have attachments, update the submission to add them
    console.log("Adding attachments via update...");
    const submissionId = response.data.uuid;

    // Create a new FormData for the update
    const updateFormData = new FormData();

    // CRITICAL: Always include the existing_attachments in the update request
    // This is essential for preserving attachments during form updates
    if (initialSubmission.existing_attachments && initialSubmission.existing_attachments.length > 0) {
      console.log(`Including ${initialSubmission.existing_attachments.length} existing attachments in PATCH request:`, initialSubmission.existing_attachments);
      initialSubmission.existing_attachments.forEach(uuid => {
        updateFormData.append('existing_attachments', uuid);
      });
    }

    // Include the id in the update request to ensure we're updating the right submission
    if (response.data.uuid) {
      updateFormData.append('id', String(response.data.uuid));
    }

    // Make sure the form_uuid is included in the update
    updateFormData.append('form_uuid', initialSubmission.form_uuid);

    // CRITICAL: Always include the existing_attachments in the update request
    // This is essential for preserving attachments during form updates
    if (initialSubmission.existing_attachments && initialSubmission.existing_attachments.length > 0) {
      console.log(`Including ${initialSubmission.existing_attachments.length} existing attachments in update:`, initialSubmission.existing_attachments);
      initialSubmission.existing_attachments.forEach(uuid => {
        updateFormData.append('existing_attachments', uuid);
      });
    }

    // Check if the PATCH request contains existing_attachments already
    const patchData = Object.fromEntries(updateFormData.entries());
    console.log("Current PATCH request data keys:", Object.keys(patchData));

    // CRITICAL FIX: Always ensure existing_attachments is included in PATCH requests
    if (initialSubmission.existing_attachments && initialSubmission.existing_attachments.length > 0) {
      console.log("CRITICAL FIX: Ensuring existing_attachments is included in PATCH request");
      // First, check if it's already included
      const hasExistingAttachments = Array.from(updateFormData.keys()).includes('existing_attachments');

      if (!hasExistingAttachments) {
        // If not already included, add each UUID individually
        console.log(`Adding ${initialSubmission.existing_attachments.length} existing attachments to PATCH request`);
        initialSubmission.existing_attachments.forEach(uuid => {
          updateFormData.append('existing_attachments', uuid);
        });
      }
    }

    // Add the attachments
    if (attachmentsCopy?.image_attachments?.length) {
      attachmentsCopy.image_attachments.forEach(file => {
        updateFormData.append('image_attachments', file);
      });
    }

    if (attachmentsCopy?.video_attachments?.length) {
      attachmentsCopy.video_attachments.forEach(file => {
        updateFormData.append('video_attachments', file);
      });
    }

    if (attachmentsCopy?.document_attachments?.length) {
      attachmentsCopy.document_attachments.forEach(file => {
        updateFormData.append('document_attachments', file);
      });
    }

    // Also include existing_attachments in the update if they exist
    if (initialSubmission.existing_attachments && initialSubmission.existing_attachments.length > 0) {
      console.log(`Including ${initialSubmission.existing_attachments.length} existing attachments in update:`, initialSubmission.existing_attachments);
      // Append each UUID individually - this is critical for proper backend processing
      initialSubmission.existing_attachments.forEach(uuid => {
        updateFormData.append('existing_attachments', uuid);
      });
      // Also include the JSON stringified version as a fallback
      updateFormData.append('existing_attachments_json', JSON.stringify(initialSubmission.existing_attachments));
    }

    // Add file type mapping
    const fileTypes: Record<string, string> = {};
    if (attachmentsCopy?.image_attachments) {
      attachmentsCopy.image_attachments.forEach(file => {
        fileTypes[file.name] = 'image';
      });
    }

    if (attachmentsCopy?.video_attachments) {
      attachmentsCopy.video_attachments.forEach(file => {
        fileTypes[file.name] = 'video';
      });
    }

    if (attachmentsCopy?.document_attachments) {
      attachmentsCopy.document_attachments.forEach(file => {
        fileTypes[file.name] = 'document';
      });
    }

    if (Object.keys(fileTypes).length > 0) {
      updateFormData.append('file_types', JSON.stringify(fileTypes));
    }

    // Log the update FormData entries
    console.log("Update FormData entries for attachments:");
    for (const pair of updateFormData.entries()) {
      if (pair[1] instanceof File) {
        console.log(`- ${pair[0]}: ${pair[1].name} (${pair[1].type}, ${Math.round(pair[1].size/1024)}KB)`);
      } else {
        console.log(`- ${pair[0]}: ${pair[1]}`);
      }
    }

    // Update the submission with attachments
    console.log("Sending PATCH request to update submission:", submissionId);
    console.log("FormData keys being sent in PATCH request:", Array.from(updateFormData.keys()));

    // Ensure existing_attachments is included in the PATCH request
    if (initialSubmission.existing_attachments && initialSubmission.existing_attachments.length > 0) {
      console.log("Double-checking existing_attachments is included in PATCH request");
      // If not already present, add it now
      if (!Array.from(updateFormData.keys()).includes('existing_attachments')) {
        console.log("Re-adding existing_attachments to FormData for PATCH request");
        initialSubmission.existing_attachments.forEach(uuid => {
          updateFormData.append('existing_attachments', uuid);
        });

        // Also add as JSON string for backup
        updateFormData.append('existing_attachments_json', JSON.stringify(initialSubmission.existing_attachments));

        console.log("Added existing_attachments to FormData:", initialSubmission.existing_attachments);
      }
    }

    // CRITICAL FIX: Log the FormData to verify existing_attachments is included
    console.log("CRITICAL CHECK - existing_attachments in FormData:");
    if (updateFormData.has('existing_attachments')) {
      console.log("existing_attachments is present in FormData");

      // Get all existing_attachments values
      const existingAttachments = updateFormData.getAll('existing_attachments');
      console.log("existing_attachments values:", existingAttachments);
    } else {
      console.log("existing_attachments is NOT present in FormData!");
    }

    const updateResponse = await api.patch<FormSubmission>(`/form/submissions/${submissionId}/`, updateFormData, {
      headers: { 'Content-Type': 'multipart/form-data' }
    });

    console.log("Attachments added successfully:", updateResponse.data);
    return updateResponse.data;
  } catch (error) {
    const err = error as AxiosError;
    console.error("Form submission error:", err);
    if (err.response) {
      console.error("Error status:", err.response.status);
      console.error("Error data:", err.response.data);
    }
    throw err;
  }
};

export const updateSubmission = async (uuid: string, submissionData: {
  form_uuid?: string;
  user?: number | { identifier: string; first_name?: string; last_name?: string; email?: string; };
  patient_uuid?: string;
  submission?: Record<string, unknown>;
  is_completed?: boolean;
  final_submission?: boolean;
  existing_attachments?: string[]; // Add the new field
  attachments?: {
    image_attachments?: File[];
    video_attachments?: File[];
    document_attachments?: File[];
  };
}) => {
  // CRITICAL LOGGING: Log the incoming existing_attachments at the very start
  console.log('TRACKING: Initial existing_attachments in updateSubmission:', {
    exists: submissionData.existing_attachments !== undefined,
    isArray: Array.isArray(submissionData.existing_attachments),
    length: submissionData.existing_attachments ? submissionData.existing_attachments.length : 0,
    value: submissionData.existing_attachments
  });
  try {
    console.log(`Starting update for submission ${uuid} with data:`, submissionData);
    const formData = new FormData();

    // Add basic submission data if it exists
    if (submissionData.form_uuid) {
      console.log(`Adding form_uuid: ${submissionData.form_uuid}`);
      formData.append('form_uuid', submissionData.form_uuid);
    }

    // Format user object if provided
    if (submissionData.user) {
      let userObj;

      if (typeof submissionData.user === 'number') {
        userObj = {
          identifier: submissionData.user.toString(),
          first_name: '',
          last_name: '',
          email: '<EMAIL>'
        };
      } else {
        userObj = {
          identifier: submissionData.user.identifier || "0",
          first_name: submissionData.user.first_name || '',
          last_name: submissionData.user.last_name || '',
          email: submissionData.user.email || '<EMAIL>'
        };
      }

      // Make sure identifier is valid
      if (!userObj.identifier || userObj.identifier === "undefined") {
        userObj.identifier = "0";
      }

      console.log(`Adding user: ${JSON.stringify(userObj)}`);
      formData.append('user', JSON.stringify(userObj));
    }

    // Add submission data if provided
    if (submissionData.submission) {
      console.log('Adding submission data');
      formData.append('submission', JSON.stringify(submissionData.submission));
    }

    // Add completion status if provided
    if (submissionData.is_completed !== undefined) {
      console.log(`Adding is_completed: ${submissionData.is_completed}`);
      formData.append('is_completed', submissionData.is_completed.toString());
    }

    // Add final submission status if provided
    if (submissionData.final_submission !== undefined) {
      console.log(`Adding final_submission: ${submissionData.final_submission}`);
      formData.append('final_submission', submissionData.final_submission.toString());
    }

    // Add patient UUID if provided
    if (submissionData.patient_uuid) {
      console.log(`Adding patient_uuid: ${submissionData.patient_uuid}`);
      formData.append('patient_uuid', submissionData.patient_uuid);
    }

    // Add existing_attachments as expected by Django REST Framework
    if (submissionData.existing_attachments) {
      console.log("TRACKING: Processing existing_attachments array: ", submissionData.existing_attachments);

      if (submissionData.existing_attachments.length > 0) {
        // Django REST Framework expects list fields to have the same key repeated for each value
        submissionData.existing_attachments.forEach(uuid => {
          formData.append('existing_attachments', uuid);
          console.log(`TRACKING: Added existing_attachment UUID to FormData: ${uuid}`);
        });

        // Also add as a JSON string to make sure it's included in the request
        formData.append('existing_attachments_json', JSON.stringify(submissionData.existing_attachments));
        console.log(`TRACKING: Added existing_attachments_json to FormData: ${JSON.stringify(submissionData.existing_attachments)}`);

        // Log exactly what is being sent
        console.log("TRACKING: Added existing_attachments with values:", submissionData.existing_attachments);
      } else {
        // Send empty array to clear any existing attachments
        console.log("TRACKING: Sending empty existing_attachments array");
        formData.append('existing_attachments', '');
      }
    } else {
      console.log("TRACKING: No existing_attachments field in submissionData");
    }

    // Process file attachments if provided
    if (submissionData.attachments) {
      // Process image attachments
      if (submissionData.attachments.image_attachments && submissionData.attachments.image_attachments.length > 0) {
        console.log(`Adding ${submissionData.attachments.image_attachments.length} image attachments`);
        submissionData.attachments.image_attachments.forEach((file, index) => {
          console.log(`Image ${index+1}: ${file.name} (${file.type}, ${Math.round(file.size/1024)}KB)`);
          formData.append('image_attachments', file);
        });
      }

      // Process video attachments
      if (submissionData.attachments.video_attachments && submissionData.attachments.video_attachments.length > 0) {
        console.log(`Adding ${submissionData.attachments.video_attachments.length} video attachments`);
        submissionData.attachments.video_attachments.forEach((file, index) => {
          console.log(`Video ${index+1}: ${file.name} (${file.type}, ${Math.round(file.size/1024)}KB)`);
          formData.append('video_attachments', file);
        });
      }

      // Process document attachments
      if (submissionData.attachments.document_attachments && submissionData.attachments.document_attachments.length > 0) {
        console.log(`Adding ${submissionData.attachments.document_attachments.length} document attachments`);
        submissionData.attachments.document_attachments.forEach((file, index) => {
          console.log(`Document ${index+1}: ${file.name} (${file.type}, ${Math.round(file.size/1024)}KB)`);
          formData.append('document_attachments', file);
        });
      }

      // Include a type mapping to help the backend identify file types
      const fileTypes: Record<string, string> = {};

      if (submissionData.attachments.image_attachments) {
        submissionData.attachments.image_attachments.forEach((file) => {
          fileTypes[file.name] = 'image';
        });
      }

      if (submissionData.attachments.video_attachments) {
        submissionData.attachments.video_attachments.forEach((file) => {
          fileTypes[file.name] = 'video';
        });
      }

      if (submissionData.attachments.document_attachments) {
        submissionData.attachments.document_attachments.forEach((file) => {
          fileTypes[file.name] = 'document';
        });
      }

      // Add the type mapping if there are files
      if (Object.keys(fileTypes).length > 0) {
        console.log(`Adding file_types mapping: ${JSON.stringify(fileTypes)}`);
        formData.append('file_types', JSON.stringify(fileTypes));
      }
    }

    // Log all FormData entries for debugging
    console.log("FormData entries for submission update:");
    for (const pair of formData.entries()) {
      if (pair[0] === 'submission') {
        console.log(`- ${pair[0]}: [Complex JSON Object]`);
      } else if (pair[1] instanceof File) {
        console.log(`- ${pair[0]}: ${pair[1].name} (${pair[1].type}, ${Math.round(pair[1].size/1024)}KB)`);
      } else {
        console.log(`- ${pair[0]}: ${pair[1]}`);
      }
    }

    // IMPORTANT: Print the raw FormData to check if existing_attachments is present
    console.log("Raw FormData for submission update (key names only):", Array.from(formData.keys()));

    // Check specifically for existing_attachments
    console.log("TRACKING: Checking existing_attachments in FormData before sending request:");
    if (formData.has('existing_attachments')) {
      console.log("TRACKING: existing_attachments is present in FormData");

      // Get all existing_attachments values
      const existingAttachments = formData.getAll('existing_attachments');
      console.log("TRACKING: existing_attachments values:", existingAttachments);
      console.log("TRACKING: Number of existing_attachments entries:", existingAttachments.length);

      // Log each value individually for clarity
      existingAttachments.forEach((value, index) => {
        console.log(`TRACKING: existing_attachments[${index}] = ${value}`);
      });
    } else {
      console.log("TRACKING: existing_attachments is NOT present in FormData!");

      // CRITICAL FIX: If existing_attachments is not in the FormData but is in the submissionData,
      // add it to the FormData
      if (submissionData.existing_attachments && Array.isArray(submissionData.existing_attachments) &&
          submissionData.existing_attachments.length > 0) {
        console.log("TRACKING: CRITICAL FIX: Adding missing existing_attachments to FormData");

        // Add each UUID individually
        submissionData.existing_attachments.forEach(uuid => {
          formData.append('existing_attachments', String(uuid));
          console.log(`TRACKING: Added existing_attachment UUID to FormData: ${uuid}`);
        });

        // Also add as JSON string for backup
        formData.append('existing_attachments_json', JSON.stringify(submissionData.existing_attachments));
        console.log(`TRACKING: Added existing_attachments_json to FormData: ${JSON.stringify(submissionData.existing_attachments)}`);

        console.log("TRACKING: Added existing_attachments to FormData:", submissionData.existing_attachments);

        // Verify the attachments were added
        const verifyAttachments = formData.getAll('existing_attachments');
        console.log("TRACKING: Verification - existing_attachments values after fix:", verifyAttachments);
        console.log("TRACKING: Verification - Number of existing_attachments entries:", verifyAttachments.length);
      } else {
        console.log("TRACKING: No existing_attachments in submissionData to add to FormData");
      }
    }

    const response = await api.patch<FormSubmission>(`/form/submissions/${uuid}/`, formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
    console.log("Form update succeeded:", response.data);
    return response.data;
  } catch (error) {
    const err = error as AxiosError;
    console.error("Form update error:", err);
    if (err.response) {
      console.error("Response status:", err.response.status);
      console.error("Response data:", err.response.data);
    }
    throw err;
  }
};

export const getSubmissionsByPatient = async (patientUuid: string) => {
  const response = await api.get<FormSubmission[]>(`/form/submissions/patient/${patientUuid}/`);
  return response.data;
};

export const getPatientAssignedForms = async (patientUuid: string) => {
  const response = await api.get<{
    submitted_forms: FormSubmission[];
    not_submitted_forms: Form[];
  }>(`/form/submissions/patient/${patientUuid}/assigned-forms/`);
  return response.data;
};

export const updateFormSubmissionByFormAndPatient = async (
  formUuid: string,
  patientUuid: string,
  submissionData: {
    submission?: Record<string, unknown>;
    is_completed?: boolean;
  }
) => {
  const response = await api.patch<FormSubmission>(
    `/form/submissions/update/form/${formUuid}/patient/${patientUuid}/`,
    submissionData
  );
  return response.data;
};

export const deleteFormSubmissionByFormAndPatient = async (formUuid: string, patientUuid: string) => {
  const response = await api.delete(`/form/submissions/delete/form/${formUuid}/patient/${patientUuid}/`);
  return response.data;
};

export const getPendingForms = async (): Promise<Form[]> => {
  const response = await api.get("/form/forms/pending/");
  // Make sure we're returning the results array directly
  return response.data.results || [];
}

export const deleteSubmission = async (uuid: string) => {
  const response = await api.delete(`/form/submissions/${uuid}/`);
  return response.data;
};

export const getSubmissionsByForm = async (formUuid: string) => {
  const response = await api.get<FormSubmission[]>(`/form/submissions/form/${formUuid}/`);
  return response.data;
};

export const finalizeSubmissions = async (data: {
  submission_uuids?: string[];
  submission_uuid?: string
}) => {
  const response = await api.post<{
    finalized: string[];
    already_finalized: string[];
    not_found: string[];
    message: string;
  }>('/form/submissions/finalize/', data);
  return response.data;
};

// Tags APIs
export const getTags = async () => {
  const response = await api.get<Tag[]>('/form/tags/');
  return response.data;
};

export const getTagByUuid = async (uuid: string) => {
  const response = await api.get<Tag>(`/form/tags/${uuid}/`);
  return response.data;
};

export const createTag = async (tagData: { name: string, description?: string }) => {
  const response = await api.post<Tag>('/form/tags/', tagData);
  return response.data;
};

export const updateTag = async (uuid: string, tagData: { name?: string, description?: string }) => {
  const response = await api.put<Tag>(`/form/tags/${uuid}/`, tagData);
  return response.data;
};

export const deleteTag = async (uuid: string) => {
  const response = await api.delete(`/form/tags/${uuid}/`);
  return response.data;
};


export const createHolisticFormSubmission = async (
  formData: HolisticFormData
): Promise<HolisticFormDataResponse> => {
  const { data } = await api.post("/form/holistic-submissions/", formData);
  return data;
};

export const createFormSubmissionQuery = async (queryData: FormSubmissionQueryCreate): Promise<FormSubmissionQuery> => {
  const { data } = await api.post("/form/submission-queries/", queryData);
  return data;
};

export const generateHolisticFormPDF = async (
  uuid: string
): Promise<PDFResponse> => {
  const response = await api.get(`/form/holistic-submissions/${uuid}/generate-pdf/`, {
    responseType: 'blob', // Ensure Axios treats the response as binary data
  });
  return { data: response.data };
};

export const generateDynamicFormPDF = async (
  uuid: string,
  isSubmission: boolean
): Promise<PDFResponse> => {
  const endpoint = isSubmission
    ? `/form/submissions/${uuid}/generate-pdf/`
    : `/form/forms/${uuid}/generate-pdf/`;

  const response = await api.get(endpoint, {
    responseType: 'blob', // Ensure Axios treats the response as binary data
  });

  return { data: response.data };
};

// Form Submission Query APIs
export const getFormSubmissionQueries = async (params?: {
  form_submission_uuid?: string;
  is_resolved?: boolean;
  priority?: 'low' | 'medium' | 'high';
}) => {
  const response = await api.get<FormSubmissionQuery[]>('/form/submission-queries/', { params });
  // Handle both paginated and non-paginated responses
  if (response.data && typeof response.data === 'object' && 'results' in response.data) {
    return response.data.results;
  }
  return response.data;
};

export const getFormSubmissionQueryByUuid = async (uuid: string) => {
  const response = await api.get<FormSubmissionQuery>(`/form/submission-queries/${uuid}/`);
  return response.data;
};

export const updateFormSubmissionQuery = async (uuid: string, queryData: FormSubmissionQueryUpdate) => {
  const response = await api.patch<FormSubmissionQuery>(`/form/submission-queries/${uuid}/`, queryData);
  return response.data;
};

export const resolveFormSubmissionQuery = async (uuid: string, resolutionData: FormSubmissionQueryResolve) => {
  const response = await api.post<FormSubmissionQuery>(`/form/submission-queries/${uuid}/resolve/`, resolutionData);
  return response.data;
};

// Query Response APIs
export const getQueryResponses = async (params?: {
  query_uuid?: string;
}) => {
  const response = await api.get<QueryResponse[]>('/form/query-responses/', { params });
  return response.data;
};

export const getQueryResponseByUuid = async (uuid: string) => {
  const response = await api.get<QueryResponse>(`/form/query-responses/${uuid}/`);
  return response.data;
};

export const createQueryResponse = async (responseData: QueryResponseCreate) => {
  const response = await api.post<QueryResponse>('/form/query-responses/', responseData);
  return response.data;
};

export const updateQueryResponse = async (uuid: string, responseData: QueryResponseUpdate) => {
  const response = await api.patch<QueryResponse>(`/form/query-responses/${uuid}/`, responseData);
  return response.data;
};

export const getFormSubmissionQueriesByPatient = async (patientUuid: string, params?: {
  is_resolved?: boolean;
  priority?: 'low' | 'medium' | 'high';
  assigned_to?: string;
}) => {
  const queryParams = new URLSearchParams();
  if (params?.is_resolved !== undefined) {
    queryParams.append('is_resolved', params.is_resolved.toString());
  }
  if (params?.priority) {
    queryParams.append('priority', params.priority);
  }
  if (params?.assigned_to) {
    queryParams.append('assigned_to', params.assigned_to);
  }

  const queryString = queryParams.toString();
  const url = `/form/submission-queries/patient/${patientUuid}/${queryString ? `?${queryString}` : ''}`;

  // Define PaginatedResponse interface
  interface PaginatedResponse<T> {
    count: number;
    next: string | null;
    previous: string | null;
    results: T[];
  }

  const response = await api.get<PaginatedResponse<FormSubmissionQuery>>(url);
  return response.data;
};

export const resolveQuery = async (queryUuid: string, resolutionNotes?: string) => {
  const response = await api.post(`/form/submission-queries/${queryUuid}/resolve/`, {
    resolution_notes: resolutionNotes
  });
  return response.data;
};
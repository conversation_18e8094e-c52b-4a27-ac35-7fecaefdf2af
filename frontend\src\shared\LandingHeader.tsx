import { useState, useEffect } from "react";
import { Link, useNavigate } from "react-router-dom";
import MobileMenu from "./MobileMenu";
import { Menu } from "lucide-react";
import { useCurrentUserQuery } from "@/hooks/user.query";
import Preloader from "@/components/common/Preloader";
import { useKeycloak } from "@react-keycloak/web";
import { useQueryClient } from "@tanstack/react-query";


// Types
interface MenuItem {
  title: string;
  link: string;
  disabled?: boolean;
}


interface LogoProps {
  isMobile: boolean;
}

interface MobileButtonsProps {
  setActiveMobileMenu: (active: boolean) => void;
}


// No longer needed since we're using Lucide icons directly

// Constants
const MENU_ITEMS: MenuItem[] = [
  { title: "Home", link: "home" },
  ...(import.meta.env.VITE_APP_MODE === "development" ? [{ title: "Ressources", link: "sdk" }] : []),
  { title: "Blog", link: "blog" },
  { title: "Contact us", link: "contact-us" },
  { title: "About us", link: "about-us" },
];


// Styles
const styles = {
  header: (isMobile: boolean) => ({
    color: "white",
    padding: isMobile ? "8px 0" : "12px 0",
    backgroundPosition: "top left",
    backgroundRepeat: "no-repeat",
    zIndex: 1000,
    minHeight: isMobile ? "64px" : "auto",
  }),
  button: {
    base: {
      width: "100%",
      borderRadius: "0px",
      padding: "10px",
    },
    premium: {
      gap: "5px",
      cursor: "pointer",
      backgroundColor: "#37B7C3",
      borderRadius: "20px",
      padding: "16px",
      marginRight: "10px",
      fontSize: "14px",
      color: "white",
      height: "24px",
    },
    regular: {
      backgroundColor: "#E59819",
      gap: "5px",
      cursor: "pointer",
      borderRadius: "20px",
      padding: "16px",
      marginRight: "10px",
      fontSize: "14px",
      color: "white",
      height: "24px",
    },
  },
  profileMenu: {
    container: {
      marginRight: "10px",
      maxWidth: "200px",
      whiteSpace: "nowrap" as const,
      overflow: "hidden",
      textOverflow: "ellipsis",
    },
    dropdown: {
      width: "150px",
    },
  },
  link: {
    textDecoration: "none",
    color: "inherit",
    fontSize: "14px",
    fontWeight: 500,
  },
};

const Logo: React.FC<LogoProps> = ({ isMobile }) => (
  <Link to="/">
    <img
      src={
        "/assets/img/authentication/white-logo.png"
      }
      alt="logo"
      style={{
        width: isMobile ? "120px" : "auto",
        maxHeight: isMobile ? "30px" : "40px",
        objectFit: "contain",
      }}
    />
  </Link>
);

const MobileButtons: React.FC<MobileButtonsProps> = ({
  setActiveMobileMenu,
}) => (
  <div className="d-flex align-items-center" style={{ gap: "15px" }}>
    <button
      className="text-white d-flex align-items-center justify-content-center"
      onClick={() => setActiveMobileMenu(true)}
      style={{
        background: "rgba(255, 255, 255, 0.2)",
        border: "none",
        borderRadius: "8px",
        padding: "8px",
        width: "36px",
        height: "36px",
      }}
    >
      <Menu size={20} />
    </button>
  </div>
);
const DesktopMenu: React.FC = () => {
  const { data: currentUser, isLoading } = useCurrentUserQuery();
  const [isOnProfile, setIsOnProfile] = useState<boolean>(false);
  const navigate = useNavigate();
  const { keycloak } = useKeycloak();
  const queryClient = useQueryClient();

  const handleLogout = () => {
    const redirectUri = `${window.location.origin}/`;
    queryClient.clear();
    keycloak.logout({ redirectUri: redirectUri });
  };

  const basePath = "/org/dashboard";

  return (
    <nav className="menu__nav text-dark-1 d-flex justify-content-between align-items-center w-100">
      <div className="d-flex" style={{ gap: "40px" }}>
        {MENU_ITEMS.map((item, index) => (
          item.disabled ? (
            <div
              key={index}
              className="menu__nav-item px-2"
              style={{
                ...styles.link,
                position: "relative",
                padding: "8px 4px",
                transition: "all 0.3s ease",
                opacity: 0.5,
                cursor: "not-allowed",
              }}
            >
              {item.title}
            </div>
          ) : (
            <Link
              key={index}
              to={`/${item.link}`}
              className="menu__nav-item px-2"
              style={{
                ...styles.link,
                position: "relative",
                padding: "8px 4px",
                transition: "all 0.3s ease",
              }}
              onClick={(e) => {
                e.preventDefault();
                e.stopPropagation();

                // Use window.location for a more direct navigation approach
                window.location.href = `/${item.link}`;
              }}
            >
              {item.title}
              <span
                style={{
                  position: "absolute",
                  bottom: "0",
                  left: "0",
                  width: "0",
                  height: "2px",
                  backgroundColor: "#37B7C3",
                  transition: "width 0.3s ease",
                }}
                className="hover-line"
              />
            </Link>
          )
        ))}
      </div>
      <div className="d-flex align-items-center">
        {currentUser ? (
          <div className="d-flex align-items-center position-relative">
            <div
              className="avatar-circle"
              style={{
                width: "40px",
                height: "40px",
                borderRadius: "50%",
                backgroundColor: "#37B7C3",
                display: "flex",
                alignItems: "center",
                justifyContent: "center",
                color: "white",
                fontWeight: "bold",
                marginRight: "10px",
                cursor: "pointer",
                boxShadow: "0 2px 8px rgba(55, 183, 195, 0.3)",
                border: "2px solid #fff"
              }}
              onClick={() => setIsOnProfile(!isOnProfile)}
            >
              {currentUser.first_name ? currentUser.first_name.charAt(0).toUpperCase() : "U"}
            </div>
            <span
              style={{
                fontWeight: "500",
                cursor: "pointer",
                transition: "color 0.2s ease"
              }}
              onClick={() => setIsOnProfile(!isOnProfile)}
            >
              {currentUser.first_name || "User"}
            </span>

            {isOnProfile && (
              <div
                style={{
                  position: "absolute",
                  top: "50px",
                  right: "0",
                  backgroundColor: "white",
                  borderRadius: "8px",
                  boxShadow: "0 4px 12px rgba(0, 0, 0, 0.1)",
                  width: "200px",
                  zIndex: 100,
                  padding: "10px 0",
                  animation: "fadeIn 0.2s ease-in-out"
                }}
              >
                <div
                  style={{
                    padding: "10px 15px",
                    cursor: "pointer",
                    transition: "background-color 0.2s ease",
                    borderRadius: "4px",
                    margin: "0 5px",
                    color: "#37B7C3",
                    fontWeight: "bold"
                  }}
                  onMouseOver={(e) => e.currentTarget.style.backgroundColor = "#f5f5f5"}
                  onMouseOut={(e) => e.currentTarget.style.backgroundColor = "transparent"}
                  onClick={() => {
                    navigate(basePath);
                    setIsOnProfile(false);
                  }}
                >
                  Dashboard
                </div>
                <div
                  style={{
                    padding: "10px 15px",
                    cursor: "pointer",
                    transition: "background-color 0.2s ease",
                    color: "#e53e3e",
                    borderRadius: "4px",
                    margin: "0 5px"
                  }}
                  onMouseOver={(e) => e.currentTarget.style.backgroundColor = "#fff5f5"}
                  onMouseOut={(e) => e.currentTarget.style.backgroundColor = "transparent"}
                  onClick={() => {
                    handleLogout();
                    setIsOnProfile(false);
                  }}
                >
                  Logout
                </div>
              </div>
            )}
          </div>
        ) : isLoading ? (
          <Preloader />
        ) : (
          <button
            onClick={() => keycloak.login({ redirectUri: `${window.location.origin}/home` })}
            className="btn login-btn"
            style={{
              display: "flex",
              alignItems: "center",
              justifyContent: "center",
              height: "40px",
              minWidth: "100px",
              transition: "all 0.3s ease",
              borderRadius: "35px",
              backgroundColor: "white",
              fontWeight: "500",
              color: "#088395",
              position: "relative",
              overflow: "hidden",
              zIndex: 1,
              border: "none",
              cursor: "pointer"
            }}
          >
            <span style={{ position: "relative", zIndex: 2 }}>Login</span>
          </button>
        )}
      </div>
    </nav>
  );
};

// const ProfileMenu: React.FC<ProfileMenuProps> = ({
//   user,
//   handleLogout,
//   isOnProfile,
//   setIsOnProfile,
//   navigate,
// }) => {
//   const formatName = (
//     firstName: string = "",
//     lastName: string = ""
//   ): string => {
//     const fullName = `${firstName} ${lastName}`.trim();
//     return fullName.length > 20
//       ? `${fullName.substring(0, 17)}...`
//       : fullName || "User";
//   };

//   return (
//     <div className="relative d-flex items-center ml-10">
//       {/* {user?.is_premium ? (
//         <div
//           className="d-flex align-items-center justify-content-center"
//           style={styles.button.premium}
//         >
//           <Instructor />
//           <span>Apply to become Instructor</span>
//         </div>
//       ) : (
//         <div
//           className="d-flex align-items-center justify-content-center"
//           style={styles.button.regular}
//         >
//           <PremiumCrown />
//           <span>Upgrade to premium</span>
//         </div>
//       )} */}

//       <div
//         className="user-name d-flex align-items-center"
//         style={styles.profileMenu.container}
//       >
//         <span>{formatName(user?.first_name, user?.last_name)}</span>
//       </div>

//       <a
//         href="#"
//         data-el-toggle=".js-profile-toggle"
//         onClick={() => setIsOnProfile(!isOnProfile)}
//       >
//         <img
//           className="size-40 rounded-full"
//           src={
//             user?.image
//               ? `https://api.dev.nurtify.co.uk/${user?.image}`
//               : "https://encrypted-tbn0.gstatic.com/images?q=tbn:ANd9GcTni2_UQfY9kvI719Jrf5DInG1KNr0Qny_b5A&s"
//           }
//           alt="Profile"
//         />
//       </a>

//       <div
//         className={`toggle-element js-profile-toggle ${
//           isOnProfile ? "-is-el-visible" : ""
//         }`}
//       >
//         <div
//           className="toggle-bottom -profile bg-white shadow-4 border-light rounded-8 mt-10"
//           style={styles.profileMenu.dropdown}
//         >
//           {PROFILE_MENU_ITEMS.map((item, index) => (
//             <button
//               key={index}
//               className="btn btn-link"
//               style={styles.button.base}
//               onClick={() => navigate(item.href)}
//             >
//               {item.label}
//             </button>
//           ))}
//           <button
//             className="btn btn-link"
//             style={styles.button.base}
//             onClick={handleLogout}
//           >
//             Logout
//           </button>
//         </div>
//       </div>
//     </div>
//   );
// };

// Removed unused MobileIcon component

// Main Component
const LandingHeader = () => {
  const { data: currentUser } = useCurrentUserQuery();
  const [activeMobileMenu, setActiveMobileMenu] = useState(false);
  const [windowWidth, setWindowWidth] = useState(window.innerWidth);
  const [scrolled, setScrolled] = useState(false);
  // const [isOnProfile, setIsOnProfile] = useState(false);
  const { keycloak } = useKeycloak();

  useEffect(() => {
    const handleResize = () => setWindowWidth(window.innerWidth);
    window.addEventListener("resize", handleResize);

    const handleScroll = () => {
      const isScrolled = window.scrollY > 50;
      if (isScrolled !== scrolled) {
        setScrolled(isScrolled);
      }
    };

    window.addEventListener("scroll", handleScroll);

    return () => {
      window.removeEventListener("resize", handleResize);
      window.removeEventListener("scroll", handleScroll);
    };
  }, [scrolled]);

  const isMobile = windowWidth < 768;

  const headerStyle = {
    ...styles.header(isMobile),
    backgroundColor: scrolled ? 'rgba(8, 131, 149, 0.95)' : 'transparent',
    boxShadow: scrolled ? '0 4px 20px rgba(0, 0, 0, 0.1)' : 'none',
    transition: 'all 0.3s ease'
  };

  return (
    <header className="header -base js-header" style={headerStyle}>
      <div className={`header__container py-3 header-banner ${isMobile ? 'mobile-container' : ''}`}>
        {isMobile ? (
          // Mobile Layout
          <div className="d-flex align-items-center justify-content-between w-100">
            <div className="header__logo">
              <Logo isMobile={isMobile} />
            </div>
            <div className="d-flex align-items-center" style={{ gap: "8px" }}>
              {!currentUser && (
                <button
                  onClick={() => keycloak.login({ redirectUri: `${window.location.origin}/home` })}
                  style={{
                    color: "white",
                    fontWeight: 600,
                    fontSize: "12px",
                    textDecoration: "none",
                    background: "rgba(255, 255, 255, 0.2)",
                    padding: "6px 12px",
                    borderRadius: "20px",
                    border: "none",
                    cursor: "pointer",
                    whiteSpace: "nowrap"
                  }}
                >
                  Login
                </button>
              )}
              <MobileButtons setActiveMobileMenu={setActiveMobileMenu} />
            </div>
            <MobileMenu
              activeMobileMenu={activeMobileMenu}
              setActiveMobileMenu={setActiveMobileMenu}
            />
          </div>
        ) : (
          // Desktop Layout
          <div className="row justify-content-between align-items-center">
            <div className="col-auto d-flex align-items-center">
              <div className="header-left px-4">
                <div className="header__logo">
                  <Logo isMobile={isMobile} />
                </div>
              </div>
            </div>
            <div className="col-auto flex-grow-1">
              <div className="header__menu">
                <DesktopMenu />
              </div>
            </div>
          </div>
        )}
      </div>

      <style>
        {`
          @media (min-width: 768px) {
            .menu__nav-item:hover .hover-line {
              width: 100%;
            }
            
            .btn-nurtify:hover {
              transform: translateY(-2px);
              box-shadow: 0 4px 12px rgba(55, 183, 195, 0.3);
            }

            .login-btn::before {
              content: '';
              position: absolute;
              top: 0;
              left: -100%;
              width: 100%;
              height: 100%;
              background: linear-gradient(90deg, transparent, rgba(8, 131, 149, 0.2), transparent);
              transition: left 0.7s ease;
              z-index: 1;
            }
            
            .login-btn:hover {
              transform: translateY(-3px);
              box-shadow: 0 5px 15px rgba(8, 131, 149, 0.3);
            }
            
            .login-btn:hover::before {
              left: 100%;
            }

            .header__container {
              padding: 10px 90px !important;
            }
          }
          
          @media (max-width: 767px) {
            .header__container {
              padding: 8px 15px !important;
            }
            
            .header-left {
              flex: 1;
            }
            
            .header-right {
              gap: 8px !important;
            }
            
            .header-right button {
              white-space: nowrap;
              min-width: auto !important;
              padding: 6px 10px !important;
              font-size: 12px !important;
            }
          }

          @media (max-width: 480px) {
            .header__container {
              padding: 6px 10px !important;
            }
            
            .header-right button {
              padding: 4px 8px !important;
              font-size: 11px !important;
            }
            
            .header__logo img {
              width: 100px !important;
              max-height: 24px !important;
            }
          }

          @media (max-width: 320px) {
            .header__container {
              padding: 4px 8px !important;
            }
            
            .header__logo img {
              width: 90px !important;
              max-height: 20px !important;
            }
            
            .header-right button {
              padding: 3px 6px !important;
              font-size: 10px !important;
            }
          }
        `}
      </style>
    </header>
  );
};

export default LandingHeader;

import React from "react";
import { motion } from "framer-motion";
import ClinicalCard2 from "@/components/ClinicalCard2";
import { AlertCircle, Pill, Thermometer, Stethoscope, DollarSign, HelpCircle } from "lucide-react";

const ReportSection: React.FC = () => {
  const cards = [
    {
      title: "Report Emergency",
      description: "Report an emergency situation",
      link: "/conversation?type=emergency",
      icon: <AlertCircle size={80} color="#e74c3c" />,
    },
    {
      title: "New Medication",
      description: "Report new medication",
      link: "/conversation?type=medication",
      icon: <Pill size={80} color="#3498db" />,
    },
    {
      title: "Report Symptoms",
      description: "Report new symptoms",
      link: "/conversation?type=symptoms",
      icon: <Thermometer size={80} color="#9b59b6" />,
    },
    {
      title: "Changes on Trial Medicine",
      description: "Report changes in trial medicine schedule",
      link: "/conversation?type=trial-changes",
      icon: <Stethoscope size={80} color="#2ecc71" />,
    },
    {
      title: "Request Refund",
      description: "Submit a refund request for expenses",
      link: "/conversation?type=refund",
      icon: <DollarSign size={80} color="#f39c12" />,
    },
    {
      title: "Other Queries",
      description: "Submit other questions or requests",
      link: "/conversation?type=queries",
      icon: <HelpCircle size={80} color="#7f8c8d" />,
    },
  ];

  return (
    <>
      {/* Header Section */}
      <motion.div
        className="patclin-header"
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
      >
        <h1 className="patclin-title">
          <span>Report to Clinical Team</span>
          <span
            className="patclin-wave-emoji"
            role="img"
            aria-label="waving hand"
          >
            👋
          </span>
        </h1>
        <p className="patclin-subtitle">
          Choose a reporting option below.
        </p>
      </motion.div>

      {/* Cards Section */}
      <motion.div
        className="patclin-cards-section"
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5, delay: 0.2 }}
      >
        <div className="patclin-cards-grid">
          {cards.map((card, index) => (
            <motion.div
              key={index}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: 0.1 * index + 0.3 }}
            >
              <ClinicalCard2
                title={card.title}
                description={card.description}
                link={card.link}
                icon={card.icon}
              />
            </motion.div>
          ))}
        </div>
      </motion.div>
    </>
  );
};

export default ReportSection;

import "./addsponsororg.css";
import { useNavigate } from "react-router-dom";
import React, { useState, useEffect, useRef } from "react";
import NurtifyText from "@components/NurtifyText.tsx";
import NurtifyInput from "@components/NurtifyInput.tsx";
import NurtifyTextArea from "@components/NurtifyTextArea.tsx";
import { motion, AnimatePresence } from "framer-motion";
import { Building, MapPin, CheckCircle, Search, Loader2, Info } from "lucide-react";
import { useCreateSponsorOrgMutation } from "@/hooks/sponsorOrg.query";
import AddDepHospModal from "@/components/modal/AddDepHospModal";
import { CountrySelect } from "react-country-state-city";
import "react-country-state-city/dist/react-country-state-city.css";

const SPECIALITY_CHOICES = [
  { value: "pharma", label: "Pharmaceutical Company" },
  { value: "research", label: "Research Institute" },
  { value: "government", label: "Government Body" },
  { value: "private", label: "Private Organization" },
  { value: "other", label: "Other" }
];

type Country = {
  id: number;
  name: string;
  iso2: string;
};

export default function AddSponsorOrg() {
  const createSponsorOrgMutation = useCreateSponsorOrgMutation();
  const [showPopup, setShowPopup] = useState<boolean>(false);
  const [newSponsorOrgUuid, setNewSponsorOrgUuid] = useState<string | null>(null);
  const navigate = useNavigate();
  const [currentStep, setCurrentStep] = useState(1);
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [warnings, setWarnings] = useState<Record<string, string>>({});
  const [warningTimeout, setWarningTimeout] = useState<NodeJS.Timeout | null>(null);
  const [blurredFields, setBlurredFields] = useState<Record<string, boolean>>({});

  const [formData, setFormData] = useState({
    name: "",
    phone_number: "",
    extension: "",
    point_of_contact_Person: "",
    primary_address: "",
    secondary_address: "",
    postcode: "",
    country: "United Kingdom",
    organization_type: "",
    description: "",
  });

  // --- Postcode Autocomplete & Lookup States ---
  const [autocompleteSuggestions, setAutocompleteSuggestions] = useState<string[]>([]);
  const [showPostcodeAutocomplete, setShowPostcodeAutocomplete] = useState(false);
  const [isPostcodeApiLoading, setIsPostcodeApiLoading] = useState(false);
  const [postcodeApiError, setPostcodeApiError] = useState<string | null>(null);
  const autocompletePostcodeRef = useRef<HTMLUListElement>(null);
  const postcodeDebounceTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  const API_BASE_URL = 'https://api.postcodes.io';

  const validateField = (name: string, value: string) => {
    const maxLengths: Record<string, number> = {
      name: 50,
      phone_number: 20,
      extension: 20,
      point_of_contact_Person: 50,
      primary_address: 50,
      secondary_address: 50,
      postcode: 20,
      country: 20,
      organization_type: 50,
      description: 100,
    };

    const optionalFields = ['secondary_address', 'description'];

    if (optionalFields.includes(name)) {
      if (value && value.length > maxLengths[name]) {
        return `${name.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase())} must not exceed ${maxLengths[name]} characters`;
      }
      return '';
    }

    if (!value.trim()) {
      return `${name.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase())} is required`;
    }
    if (value.length > maxLengths[name]) {
      return `${name.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase())} must not exceed ${maxLengths[name]} characters`;
    }
    return '';
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;

    if (name === "phone_number" || name === "extension") {
      const nonNumericChars = /[^0-9]/;
      if (nonNumericChars.test(value)) {
        if (warningTimeout) {
          clearTimeout(warningTimeout);
        }
        setWarnings(prev => ({
          ...prev,
          [name]: "Only numbers are allowed"
        }));
        const timeoutId = setTimeout(() => {
          setWarnings(prev => ({
            ...prev,
            [name]: ""
          }));
        }, 3000);
        setWarningTimeout(timeoutId);
        return;
      }
    }

    setFormData((prev) => ({
      ...prev,
      [name]: value,
    }));

    // Only set error if the field has been blurred
    if (blurredFields[name]) {
      const error = validateField(name, value);
      setErrors(prev => ({
        ...prev,
        [name]: error
      }));
    }

    if ((name === "phone_number" || name === "extension") && value === "") {
      setWarnings(prev => ({
        ...prev,
        [name]: ""
      }));
    }
  };

  const handleBlur = (name: string) => {
    setBlurredFields(prev => ({
      ...prev,
      [name]: true
    }));
    const error = validateField(name, formData[name as keyof typeof formData]);
    setErrors(prev => ({
      ...prev,
      [name]: error
    }));
  };

  const handleSelectChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));

    if (blurredFields[name]) {
      const error = validateField(name, value);
      setErrors(prev => ({
        ...prev,
        [name]: error
      }));
    }
  };

  const [selectedCountry, setSelectedCountry] = useState<Country | null>({
    id: 232,
    name: "United Kingdom",
    iso2: "GB"
  });

  const handleCountrySelectChange = (val: Country | null) => {
    console.log("Selected country:", val);
    setSelectedCountry(val);
    const countryName = val ? val.name : "";
    setFormData((prev) => ({
      ...prev,
      country: countryName
    }));

    if (blurredFields['country']) {
      setErrors(prev => ({
        ...prev,
        country: val ? '' : 'Country is required'
      }));
    }
  };

  const handleCountryBlur = () => {
    setBlurredFields(prev => ({
      ...prev,
      country: true
    }));
    setErrors(prev => ({
      ...prev,
      country: formData.country ? '' : 'Country is required'
    }));
  };

  const fetchPostcodeAutocompleteSuggestions = async (query: string) => {
    setIsPostcodeApiLoading(true);
    setPostcodeApiError(null);
    try {
      const response = await fetch(`${API_BASE_URL}/postcodes/${query}/autocomplete`);
      const data = await response.json();

      if (data.status === 200) {
        setAutocompleteSuggestions(data.result || []);
        setShowPostcodeAutocomplete(true);
      } else {
        setAutocompleteSuggestions([]);
        if (data.status !== 404) {
          setPostcodeApiError(data.error);
        }
      }
    } catch {
      setPostcodeApiError('Failed to fetch postcode suggestions. Please try again.');
      setAutocompleteSuggestions([]);
    } finally {
      setIsPostcodeApiLoading(false);
    }
  };

  const fetchFullAddressDetails = async (pc: string) => {
    setIsPostcodeApiLoading(true);
    setPostcodeApiError(null);
    try {
      const response = await fetch(`${API_BASE_URL}/postcodes/${pc}`);
      const data = await response.json();

      if (data.status === 200 && data.result) {
        const result = data.result;
        setFormData(prev => ({
          ...prev,
          primary_address: result.line_1 || result.admin_county || result.parish || '',
          secondary_address: result.line_2 || result.admin_ward || '',
          country: result.country || 'United Kingdom',
          postcode: result.postcode
        }));
        if (result.country === 'United Kingdom') {
          setSelectedCountry({ id: 232, name: "United Kingdom", iso2: "GB" });
        }
        setShowPostcodeAutocomplete(false);
      } else {
        setPostcodeApiError(data.error || 'Postcode not found or invalid.');
        setFormData(prev => ({
          ...prev,
          primary_address: '',
          secondary_address: '',
        }));
      }
    } catch {
      setPostcodeApiError('Failed to lookup postcode details. Please try again.');
      setFormData(prev => ({
        ...prev,
        primary_address: '',
        secondary_address: '',
      }));
    } finally {
      setIsPostcodeApiLoading(false);
    }
  };

  useEffect(() => {
    if (postcodeDebounceTimeoutRef.current) {
      clearTimeout(postcodeDebounceTimeoutRef.current);
    }

    if (formData.postcode.length > 1) {
      postcodeDebounceTimeoutRef.current = setTimeout(() => {
        fetchPostcodeAutocompleteSuggestions(formData.postcode);
      }, 300);
    } else {
      setAutocompleteSuggestions([]);
      setShowPostcodeAutocomplete(false);
    }

    return () => {
      if (postcodeDebounceTimeoutRef.current) {
        clearTimeout(postcodeDebounceTimeoutRef.current);
      }
    };
  }, [formData.postcode]);

  const handlePostcodeInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData((prev) => ({
      ...prev,
      [name]: value,
    }));

    if (blurredFields[name]) {
      const error = validateField(name, value);
      setErrors(prev => ({
        ...prev,
        [name]: error
      }));
    }
  };

  const handlePostcodeSuggestionSelect = (suggestion: string) => {
    setFormData(prev => ({
      ...prev,
      postcode: suggestion,
    }));
    setShowPostcodeAutocomplete(false);
    fetchFullAddressDetails(suggestion);
  };

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (autocompletePostcodeRef.current && !autocompletePostcodeRef.current.contains(event.target as Node)) {
        setShowPostcodeAutocomplete(false);
      }
    };
    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    const newErrors: Record<string, string> = {};
    Object.keys(formData).forEach(key => {
      const error = validateField(key, formData[key as keyof typeof formData]);
      if (error) newErrors[key] = error;
    });

    setErrors(newErrors);
    // Mark all fields as blurred on submit to show all errors
    setBlurredFields(prev => ({
      ...prev,
      ...Object.keys(formData).reduce((acc, key) => ({ ...acc, [key]: true }), {})
    }));

    if (Object.keys(newErrors).length === 0) {
      try {
        const formattedData = {
          ...formData,
          name: formData.name.trim().replace(/\s+/g, '_')
        };
        const response = await createSponsorOrgMutation.mutateAsync(formattedData);
        setNewSponsorOrgUuid(response.uuid);
        setShowPopup(true);
      } catch (error) {
        console.error("Error creating sponsor org:", error);
      }
    } else {
      const step1Fields = ['name', 'organization_type', 'phone_number', 'extension', 'point_of_contact_Person'];
      const hasStep1Errors = step1Fields.some(field => newErrors[field]);
      setCurrentStep(hasStep1Errors ? 1 : 2);
    }
  };

  const nextStep = () => {
    const currentStepErrors: Record<string, string> = {};
    let fieldsToValidate: (keyof typeof formData)[] = [];

    if (currentStep === 1) {
      fieldsToValidate = ['name', 'organization_type', 'phone_number', 'extension', 'point_of_contact_Person'];
    } else if (currentStep === 2) {
      fieldsToValidate = ['primary_address', 'postcode', 'country'];
    }

    fieldsToValidate.forEach(field => {
      const error = validateField(field, formData[field]);
      if (error) currentStepErrors[field] = error;
    });

    setErrors(prev => ({ ...prev, ...currentStepErrors }));
    // Mark current step fields as blurred to show errors
    setBlurredFields(prev => ({
      ...prev,
      ...fieldsToValidate.reduce((acc, field) => ({ ...acc, [field]: true }), {})
    }));

    if (Object.keys(currentStepErrors).length === 0) {
      setCurrentStep(currentStep + 1);
    }
  };

  const prevStep = () => {
    if (currentStep > 1) {
      setCurrentStep(currentStep - 1);
    }
  };

  const handlePopupYes = () => {
    if (newSponsorOrgUuid) {
      navigate(`/org/dashboard/sponsorOrg-details/${newSponsorOrgUuid}/orgAdmins`);
    }
    setShowPopup(false);
  };

  const handlePopupNo = () => {
    navigate("/org/dashboard/sponsor-orgs", { replace: true });
    setShowPopup(false);
  };

  return (
    <div className="add-sponsor-org-container">
      <div className="add-sponsor-org-header">
        <div className="add-sponsor-org-title">
          <h1>
            <Building size={24} style={{ marginRight: "10px" }} />
            Add New Sponsor Org
          </h1>
        </div>
        <div className="add-sponsor-org-subtitle">
          <h6>Create and manage sponsor org information in your network</h6>
        </div>
      </div>

      <div className="sponsor-org-progress">
        <div
          className={`sponsor-org-step ${currentStep >= 1 ? "active" : ""} ${currentStep > 1 ? "completed" : ""}`}
        >
          <div className="step-indicator">
            {currentStep > 1 ? <CheckCircle size={16} /> : 1}
          </div>
          <div className="step-label">Sponsor Org Info</div>
        </div>
        <div
          className={`sponsor-org-step ${currentStep >= 2 ? "active" : ""}`}
        >
          <div className="step-indicator">2</div>
          <div className="step-label">Address Details</div>
        </div>
      </div>

      <form className="add-sponsor-org-form" onSubmit={handleSubmit}>
        {/* Step 1: Sponsor Org Information */}
        {currentStep === 1 && (
          <motion.div
            className="form-section"
            initial={{ opacity: 0, x: -20 }}
            animate={{ opacity: 1, x: 0 }}
            exit={{ opacity: 0, x: 20 }}
            transition={{ duration: 0.3 }}
          >
            <h3 className="form-section-title">
              <Building size={18} style={{ marginRight: "8px", verticalAlign: "middle" }} />
              Sponsor Org Information
            </h3>
            <div className="row y-gap-30">
              <div className="col-md-6">
                <NurtifyText label="Sponsor Org Name*" />
                <NurtifyInput
                  type="text"
                  name="name"
                  value={formData.name}
                  onChange={handleInputChange}
                  onBlur={() => handleBlur('name')}
                  placeholder="Enter Sponsor Org Name here"
                  maxLength={50}
                />
                {blurredFields.name && errors.name && <div className="field-error">{errors.name}</div>}
              </div>

              <div className="col-md-6">
                <NurtifyText label="Organization Type*" />
                <select
                  name="organization_type"
                  value={formData.organization_type}
                  onChange={handleSelectChange}
                  onBlur={() => handleBlur('organization_type')}
                  className="nurtify-select"
                >
                  <option value="">Select Organization Type</option>
                  {SPECIALITY_CHOICES.map((option) => (
                    <option key={option.value} value={option.value}>
                      {option.label}
                    </option>
                  ))}
                </select>
                {blurredFields.organization_type && errors.organization_type && <div className="field-error">{errors.organization_type}</div>}
              </div>
            </div>

            <div className="row y-gap-30" style={{ marginTop: "20px" }}>
              <div className="col-md-6">
                <NurtifyText label="Phone*" />
                <NurtifyInput
                  type="text"
                  name="phone_number"
                  value={formData.phone_number}
                  onChange={handleInputChange}
                  onBlur={() => handleBlur('phone_number')}
                  placeholder="Enter Phone Number here"
                  maxLength={20}
                />
                {blurredFields.phone_number && errors.phone_number && <div className="field-error">{errors.phone_number}</div>}
                {warnings.phone_number && <div className="field-warning">{warnings.phone_number}</div>}
              </div>
              <div className="col-md-6">
                <NurtifyText label="Extension*" />
                <NurtifyInput
                  type="text"
                  name="extension"
                  value={formData.extension}
                  onChange={handleInputChange}
                  onBlur={() => handleBlur('extension')}
                  placeholder="Enter Extension here"
                  maxLength={20}
                />
                {blurredFields.extension && errors.extension && <div className="field-error">{errors.extension}</div>}
                {warnings.extension && <div className="field-warning">{warnings.extension}</div>}
              </div>
            </div>

            <div className="row y-gap-30" style={{ marginTop: "20px" }}>
              <div className="col-md-6">
                <NurtifyText label="Point of Contact Person*" />
                <NurtifyInput
                  type="text"
                  name="point_of_contact_Person"
                  value={formData.point_of_contact_Person}
                  onChange={handleInputChange}
                  onBlur={() => handleBlur('point_of_contact_Person')}
                  placeholder="Enter Point of Contact Person here"
                  maxLength={50}
                />
                {blurredFields.point_of_contact_Person && errors.point_of_contact_Person && <div className="field-error">{errors.point_of_contact_Person}</div>}
              </div>
            </div>

            <div className="sponsor-org-form-actions">
              <button
                type="button"
                className="sponsor-org-btn-submit"
                onClick={nextStep}
              >
                Next
              </button>
            </div>
          </motion.div>
        )}

        {/* Step 2: Address Information */}
        {currentStep === 2 && (
          <motion.div
            className="form-section"
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            exit={{ opacity: 0, x: -20 }}
            transition={{ duration: 0.3 }}
          >
            <h3 className="form-section-title">
              <MapPin size={18} style={{ marginRight: "8px", verticalAlign: "middle" }} />
              Address Information
            </h3>

            <div className="row y-gap-30">
              <div className="col-md-6">
                <NurtifyText label="Country*" />
                <CountrySelect
                  containerClassName="form-group"
                  inputClassName=""
                  defaultValue={selectedCountry as any}
                  onChange={handleCountrySelectChange as any}
                  onBlur={handleCountryBlur}
                  placeHolder="Select Country"
                  showFlag={true}
                />
                {blurredFields.country && errors.country && <div className="field-error">{errors.country}</div>}
              </div>
              <div className="col-md-6 relative">
                <NurtifyText label="Postcode*" />
                <NurtifyInput
                  type="text"
                  name="postcode"
                  value={formData.postcode}
                  onChange={handlePostcodeInputChange}
                  onFocus={() => formData.postcode.length > 1 && setShowPostcodeAutocomplete(true)}
                  onBlur={() => {
                    handleBlur('postcode');
                    setTimeout(() => {
                      setShowPostcodeAutocomplete(false);
                    }, 200);
                  }}
                  placeholder="Enter Postcode here"
                  maxLength={20}
                />
                {blurredFields.postcode && errors.postcode && <div className="field-error">{errors.postcode}</div>}

                {isPostcodeApiLoading && (
                  <div className="absolute z-20 w-full mt-1 p-2 bg-white rounded-xl shadow-lg flex items-center justify-center text-blue-600 text-sm">
                    <Loader2 className="animate-spin mr-2" size={16} />
                    <span>Searching...</span>
                  </div>
                )}

                <AnimatePresence>
                  {showPostcodeAutocomplete && autocompleteSuggestions.length > 0 && (
                    <motion.ul
                      ref={autocompletePostcodeRef}
                      initial={{ opacity: 0, y: -10 }}
                      animate={{ opacity: 1, y: 0 }}
                      exit={{ opacity: 0, y: -10 }}
                      transition={{ duration: 0.2 }}
                      style={{ listStyleType: 'none', paddingLeft: 0, margin: 0 }}
                      className="absolute z-10 w-full mt-1 bg-white border border-gray-200 rounded-xl shadow-lg max-h-40 overflow-y-auto"
                    >
                      {autocompleteSuggestions.map((suggestion, index) => (
                        <motion.li
                          key={index}
                          whileHover={{ backgroundColor: '#f0f4f8' }}
                          onClick={() => handlePostcodeSuggestionSelect(suggestion)}
                          className="cursor-pointer p-2 border-b border-gray-100 last:border-b-0 text-gray-800 text-sm"
                        >
                          <Search className="inline-block mr-2 text-gray-400" size={14} /> {String(suggestion)}
                        </motion.li>
                      ))}
                    </motion.ul>
                  )}
                </AnimatePresence>

                {postcodeApiError && (
                  <motion.div
                    initial={{ opacity: 0, y: -10 }}
                    animate={{ opacity: 1, y: 0 }}
                    className="mt-2 flex items-center p-2 bg-red-100 text-red-700 rounded-xl text-xs"
                  >
                    <Info className="mr-1" size={14} />
                    <span>{postcodeApiError}</span>
                  </motion.div>
                )}
              </div>
            </div>

            <div className="row y-gap-30" style={{ marginTop: "20px" }}>
              <div className="col-md-6">
                <NurtifyText label="Primary Address*" />
                <NurtifyInput
                  type="text"
                  name="primary_address"
                  value={formData.primary_address}
                  onChange={handleInputChange}
                  onBlur={() => handleBlur('primary_address')}
                  placeholder="Enter Primary Address here"
                  maxLength={50}
                />
                {blurredFields.primary_address && errors.primary_address && <div className="field-error">{errors.primary_address}</div>}
              </div>
              <div className="col-md-6">
                <NurtifyText label="Secondary Address" />
                <NurtifyInput
                  type="text"
                  name="secondary_address"
                  value={formData.secondary_address}
                  onChange={handleInputChange}
                  onBlur={() => handleBlur('secondary_address')}
                  placeholder="Enter Secondary Address here (Optional)"
                  maxLength={50}
                />
                {blurredFields.secondary_address && errors.secondary_address && <div className="field-error">{errors.secondary_address}</div>}
              </div>
            </div>

            <div className="row y-gap-30" style={{ marginTop: "20px" }}>
              <div className="col-md-12">
                <NurtifyText label="Description" />
                <NurtifyTextArea
                  name="description"
                  value={formData.description}
                  onChange={handleInputChange}
                  onBlur={() => handleBlur('description')}
                  placeholder="Enter Description here (Optional)"
                  maxLength={100}
                />
                {blurredFields.description && errors.description && <div className="field-error">{errors.description}</div>}
              </div>
            </div>

            <div className="sponsor-org-form-actions">
              <button
                type="button"
                className="sponsor-org-btn-cancel"
                onClick={prevStep}
              >
                Back
              </button>
              <button
                type="submit"
                className="sponsor-org-btn-submit"
                disabled={createSponsorOrgMutation.isPending}
              >
                {createSponsorOrgMutation.isPending ? "Submitting..." : "Add Sponsor Org"}
              </button>
            </div>
          </motion.div>
        )}
      </form>
      {showPopup && (
        <AddDepHospModal
          type="SponsorOrg"
          isOpen={showPopup}
          onNo={handlePopupNo}
          onYes={handlePopupYes}
        />
      )}
    </div>
  );
}

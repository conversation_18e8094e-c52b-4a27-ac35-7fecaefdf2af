import { useState } from "react";
import { Plus, Edit, Trash2 } from "lucide-react";
import Preloader from "@/components/common/Preloader";
import useSelectedPatientStore from "@/store/SelectedPatientState";
import "./patient-medications.css";
import {
  usePatientConcomitantMedicationsQuery,
  useCreateConcomitantMedicationMutation,
  useUpdateConcomitantMedicationMutation,
  useDeleteConcomitantMedicationMutation
} from "@/hooks/patient.query";
import type { ConcomitantMedication, CreateConcomitantMedicationData } from "@/services/api/types";
import EditMedicationModal from "@/components/modal/EditMedicationModal";
import AddMedicationModal from "@/components/modal/AddMedicationModal";
import DataTable from "@/components/common/DataTable";

// Define type for medication data
type MedicationTableData = {
  uuid: string;
  medication: string;
  indication: string;
  dose: string;
  schedule: string;
  form: string;
  route: string;
  startDate: string;
  endDate: string;
  status: string;
};

export default function PatientMedications() {
  const { selectedPatient, isLoading, error } = useSelectedPatientStore();
  const { data: concomitantMedications, isLoading: isConcomitantMedicationsLoading, refetch: refetchMedications } = usePatientConcomitantMedicationsQuery(selectedPatient?.uuid || "");

  const createConcomitantMedicationMutation = useCreateConcomitantMedicationMutation();
  const updateConcomitantMedicationMutation = useUpdateConcomitantMedicationMutation();
  const deleteConcomitantMedicationMutation = useDeleteConcomitantMedicationMutation();

  const [isAddMedicationModalOpen, setIsAddMedicationModalOpen] = useState(false);
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const [selectedMedication, setSelectedMedication] = useState<ConcomitantMedication | null>(null);

  const handleAddMedication = () => {
    setIsAddMedicationModalOpen(true);
  };

  const handleSubmitMedication = async (newMedication: CreateConcomitantMedicationData) => {
    try {
      const medicationData = { ...newMedication };

      if (medicationData.end_date === "") {
        delete medicationData.end_date;
      }

      await createConcomitantMedicationMutation.mutateAsync(medicationData);
      setIsAddMedicationModalOpen(false);
      refetchMedications();
    } catch (error) {
      console.error("Error creating medication:", error);
    }
  };

  const handleEditMedication = (medication: ConcomitantMedication) => {
    setSelectedMedication(medication);
    setIsEditModalOpen(true);
  };

  const handleSaveEditMedication = async (updatedMedication: {
    medication: string;
    non_drug_therapy?: string;
    indication: string;
    dose: number;
    dose_units: string;
    schedule: string;
    dose_form: string;
    route: string;
    start_date: string;
    end_date?: string;
    is_baseline: boolean;
    is_continuing: boolean;
  }) => {
    if (!selectedMedication || !selectedPatient?.uuid) return;

    try {
      const medicationData = { ...updatedMedication };

      if (medicationData.end_date === "") {
        delete medicationData.end_date;
      }

      await updateConcomitantMedicationMutation.mutateAsync({
        uuid: selectedMedication.uuid,
        data: medicationData
      });
      setIsEditModalOpen(false);
      setSelectedMedication(null);
      refetchMedications();
    } catch (error) {
      console.error("Error updating medication:", error);
    }
  };

  const handleDeleteMedication = async (medicationUuid: string) => {
    if (!selectedPatient?.uuid) return;

    if (window.confirm("Are you sure you want to delete this medication?")) {
      try {
        await deleteConcomitantMedicationMutation.mutateAsync({
          uuid: medicationUuid,
          patientUuid: selectedPatient.uuid
        });
        refetchMedications();
      } catch (error) {
        console.error("Error deleting medication:", error);
      }
    }
  };

  if (isLoading) {
    return <Preloader />;
  }

  if (error || !selectedPatient) {
    return <div>Error loading patient details</div>;
  }

  const medicationsData = concomitantMedications?.results || [];

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      month: 'numeric',
      day: 'numeric',
      year: 'numeric'
    });
  };

  // Transform data for DataTable
  const tableData: MedicationTableData[] = medicationsData.map(medication => ({
    medication: medication.medication,
    indication: medication.indication,
    dose: `${medication.dose} ${medication.dose_units_display}`,
    schedule: medication.schedule_display,
    form: medication.dose_form_display,
    route: medication.route_display,
    startDate: formatDate(medication.start_date),
    endDate: medication.end_date ? formatDate(medication.end_date) : '-',
    status: medication.is_continuing ? 'Continue' : 'Discontinued',
    uuid: medication.uuid
  }));

  // Define DataTable columns
  const columns = [
    { key: 'medication', header: 'Medication' },
    { key: 'indication', header: 'Indication' },
    { key: 'dose', header: 'Dose' },
    { key: 'schedule', header: 'Schedule' },
    { key: 'form', header: 'Form' },
    { key: 'route', header: 'Route' },
    { key: 'startDate', header: 'Start Date' },
    { key: 'endDate', header: 'End Date' },
    { key: 'status', header: 'Status' }
  ] as import('@/components/common/DataTable').Column<MedicationTableData>[];

  // Define DataTable actions
  const actions = [
    {
      icon: <Edit size={16} />,

      onClick: (row: MedicationTableData) => {
        const medication = medicationsData.find(med => med.uuid === row.uuid);
        if (medication) {
          handleEditMedication(medication);
        }
      },
      tooltipText: 'Edit medication'
    },
    {
      icon: <Trash2 size={16} />,

      onClick: (row: MedicationTableData) => handleDeleteMedication(row.uuid),
      tooltipText: 'Delete medication'
    }
  ] as import('@/components/common/DataTable').Action<MedicationTableData>[];

  return (
    <div className="medications-page">
      {/* Page Content */}
      <div className="page-content">
        <div className="page-header">
          <h1 className="page-title">Concomitant Medications</h1>
        </div>

        <div className="controls-section">
          <button className="add-medication-btn" onClick={handleAddMedication}>
            <Plus size={20} />
            Add Medication
          </button>
        </div>

        {isConcomitantMedicationsLoading ? (
          <div className="loading-container">
            <Preloader />
          </div>
        ) : (
          <DataTable<MedicationTableData>
            data={tableData}
            columns={columns}
            actions={actions}
            noDataMessage="No concomitant medications recorded for this patient."
            defaultItemsPerPage={5}
          />
        )}
      </div>

      {/* Modals */}
      <AddMedicationModal
        isOpen={isAddMedicationModalOpen}
        onClose={() => setIsAddMedicationModalOpen(false)}
        onSave={handleSubmitMedication}
        patientUuid={selectedPatient?.uuid || ""}
      />

      {selectedMedication && (
        <EditMedicationModal
          isOpen={isEditModalOpen}
          onClose={() => setIsEditModalOpen(false)}
          onSave={handleSaveEditMedication}
          medicationData={selectedMedication}
        />
      )}
    </div>
  );
}

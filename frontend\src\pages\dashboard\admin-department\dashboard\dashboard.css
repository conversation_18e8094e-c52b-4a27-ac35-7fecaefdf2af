/* Analytics Dashboard Content */
.analytics-dashboard-content {
  padding: 24px;
  background-color: #f8f9fa;
}

/* Welcome Header */
.analytics-welcome-header {
  margin-bottom: 32px;
}

.analytics-welcome-title {
  font-size: 32px;
  font-weight: 700;
  color: #1a1a1a;
  margin: 0 0 8px 0;
}

.analytics-user-name {
  color: #37B7C3;
}

.analytics-welcome-subtitle {
  font-size: 16px;
  color: #6b7280;
  margin: 0;
}

/* KPI Cards Row */
.analytics-kpi-cards-row {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 24px;
  margin-bottom: 32px;
}

.analytics-kpi-card {
  background: white;
  border-radius: 12px;
  padding: 24px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  border: 1px solid #e5e7eb;
}

.analytics-kpi-header {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 16px;
}

.analytics-kpi-icon {
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.analytics-patients-icon {
  color: #37B7C3;
}

.analytics-policies-icon {
  color: #37B7C3;
}

.analytics-sponsors-icon {
  color: #37B7C3;
}

.analytics-kpi-title-section {
  flex: 1;
}

.analytics-kpi-title-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}

.analytics-kpi-title {
  font-size: 16px;
  font-weight: 600;
  color: #374151;
}

.analytics-kpi-period {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 14px;
  color: #6b7280;
  cursor: pointer;
}

.analytics-kpi-period-select {

  color: #6b7280;

}

.analytics-kpi-main-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.analytics-kpi-number {
  font-size: 48px;
  font-weight: 700;
  color: #1a1a1a;
}

.analytics-kpi-percentage {
  font-size: 14px;
  font-weight: 600;
  padding: 4px 8px;
  border-radius: 4px;
}

.analytics-kpi-percentage.analytics-positive {
  color: #10b981;
  background-color: #dcfce7;
}

.analytics-kpi-percentage.analytics-negative {
  color: #ef4444;
  background-color: #fee2e2;
}

.analytics-kpi-change {
  margin-bottom: 0;
}

.analytics-change-text {
  font-size: 14px;
  color: #6b7280;
}

.analytics-highlight {
  font-weight: 600;
}

.analytics-kpi-change.analytics-positive .analytics-highlight {
  color: #10b981;
}

.analytics-kpi-change.analytics-negative .analytics-highlight {
  color: #ef4444;
}

/* Middle Section */
.analytics-middle-section {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 24px;
  margin-bottom: 32px;
}

/* Staff Card */
.analytics-staff-card {
  background: white;
  border-radius: 12px;
  padding: 24px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  border: 1px solid #e5e7eb;
}

.analytics-staff-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 20px;
}

.analytics-staff-title-section h3 {
  font-size: 18px;
  font-weight: 600;
  color: #374151;
  margin: 0 0 8px 0;
}

.analytics-staff-period {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 14px;
  color: #6b7280;
  cursor: pointer;
}

.analytics-add-user-btn {
  display: flex;
  align-items: center;
  gap: 8px;
  background-color: #37B7C3;
  color: white;
  border: none;
  border-radius: 8px;
  padding: 10px 16px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s;
}

.analytics-add-user-btn:hover {
  background-color: #2ea4a9;
}

.analytics-staff-main-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.analytics-staff-number {
  font-size: 48px;
  font-weight: 700;
  color: #1a1a1a;
}

.analytics-staff-percentage {
  font-size: 14px;
  font-weight: 600;
  padding: 4px 8px;
  border-radius: 4px;
}

.analytics-staff-percentage.analytics-positive {
  color: #10b981;
  background-color: #dcfce7;
}

.analytics-staff-percentage.analytics-negative {
  color: #ef4444;
  background-color: #fee2e2;
}

.analytics-staff-change {
  margin-bottom: 24px;
}

.analytics-staff-change .analytics-change-text {
  font-size: 14px;
  color: #6b7280;
}

.analytics-staff-change .analytics-highlight {
  font-weight: 600;
}

.analytics-staff-change.analytics-positive .analytics-highlight {
  color: #10b981;
}

.analytics-staff-change.analytics-negative .analytics-highlight {
  color: #ef4444;
}

.analytics-staff-breakdown {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 16px;
}

.analytics-staff-type {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.analytics-staff-type-header {
  display: flex;
  align-items: center;
  gap: 12px;
  font-size: 14px;
  font-weight: 500;
  color: #374151;
}

.analytics-staff-type-icon {
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.analytics-doctors-icon {
  color: #37B7C3;
}

.analytics-nurses-icon {
  color: #37B7C3;
}

.analytics-receptionist-icon {
  color: #37B7C3;
}

.analytics-others-icon {
  color: #37B7C3;
}

.analytics-staff-count {
  margin-left: auto;
  font-weight: 600;
}

.analytics-progress-bar {
  height: 8px;
  background-color: #e5e7eb;
  border-radius: 4px;
  overflow: hidden;
}

.analytics-progress-fill {
  height: 100%;
  border-radius: 4px;
  transition: width 0.3s ease;
}

.analytics-doctors-progress {
  background-color: #37B7C3;
}

.analytics-nurses-progress {
  background-color: #37B7C3;
}

.analytics-receptionist-progress {
  background-color: #37B7C3;
}

.analytics-others-progress {
  background-color: #37B7C3;
}

.analytics-progress-percentage {
  font-size: 12px;
  color: #6b7280;
  text-align: right;
}

/* Studies Card */
.analytics-studies-card {
  background: white;
  border-radius: 12px;
  padding: 24px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  border: 1px solid #e5e7eb;
}

.analytics-studies-card h3 {
  font-size: 18px;
  font-weight: 600;
  color: #374151;
  margin: 0 0 20px 0;
}

.analytics-studies-stats {
  display: flex;
  gap: 12px;
  margin-bottom: 20px;
}

.analytics-stat-item {
  flex: 1;
  padding: 12px 16px;
  border-radius: 8px;
  text-align: center;
}

.analytics-stat-item:first-child {
  background-color: #f3f4f6;
}

.analytics-stat-item:nth-child(2) {
  background-color: #dcfce7;
}

.analytics-stat-item:nth-child(3) {
  background-color: #fee2e2;
}

.analytics-stat-label {
  font-size: 12px;
  color: #6b7280;
  margin-bottom: 4px;
  display: block;
}

.analytics-stat-value {
  font-size: 18px;
  font-weight: 600;
  color: #374151;
}

.analytics-studies-table-container {
  overflow-x: auto;
}

.analytics-studies-table {
  width: 100%;
  border-collapse: collapse;
}

.analytics-studies-table th {
  text-align: left;
  padding: 12px;
  font-size: 14px;
  font-weight: 600;
  color: #6b7280;
  border-bottom: 1px solid #e5e7eb;
}

.analytics-studies-table td {
  padding: 12px;
  font-size: 14px;
  color: #374151;
  border-bottom: 1px solid #f3f4f6;
}

.analytics-studies-table tbody tr:hover {
  background-color: #f9fafb;
}

.analytics-status-badge {
  padding: 4px 12px;
  border-radius: 16px;
  font-size: 12px;
  font-weight: 500;
}

.analytics-status-badge.analytics-pending {
  background-color: #fef3c7;
  color: #d97706;
}

.analytics-action-buttons {
  display: flex;
  gap: 8px;
}

.analytics-action-btn {
  width: 32px;
  height: 32px;
  border: none;
  border-radius: 6px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: background-color 0.2s;
}

.analytics-action-btn.analytics-accept {
  background-color: #dcfce7;
  color: #16a34a;
}

.analytics-action-btn.analytics-accept:hover {
  background-color: #bbf7d0;
}

.analytics-action-btn.analytics-reject {
  background-color: #fee2e2;
  color: #dc2626;
}

.analytics-action-btn.analytics-reject:hover {
  background-color: #fecaca;
}

/* Requests Section */
.analytics-requests-section {
  background: white;
  border-radius: 12px;
  padding: 24px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  border: 1px solid #e5e7eb;
}

.analytics-requests-main {
  display: flex;
  gap: 24px;
}

.analytics-requests-left {
  flex: 1;
}

.analytics-requests-right {
  width: 300px;
  padding: 20px;
  background-color: #f9fafb;
  border-radius: 8px;
}

.analytics-requests-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
}

.analytics-requests-header h3 {
  font-size: 18px;
  font-weight: 600;
  color: #374151;
  margin: 0;
}

.analytics-see-all-btn {
  background-color: #37B7C3;
  color: white;
  border: none;
  border-radius: 8px;
  padding: 8px 16px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s;
}

.analytics-see-all-btn:hover {
  background-color: #2ea4a9;
}

.analytics-requests-stats {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.analytics-total-requests {
  font-size: 16px;
  font-weight: 600;
  color: #374151;
}

.analytics-total-number {
  color: #37B7C3;
}

.analytics-requests-chart-section {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.analytics-chart-bars {
  display: flex;
  flex-direction: column;
  gap: 8px;
  padding: 0;
}

.analytics-chart-bar {
  height: 32px;
  border-radius: 16px;
  position: relative;
  display: flex;
  align-items: center;
  padding-left: 12px;
  width: 100%;
}

.analytics-chart-bar.analytics-accepted {
  background: linear-gradient(90deg, #10b981 0%, #10b981 80%, #dcfce7 80%, #dcfce7 100%);
}

.analytics-chart-bar.analytics-pending {
  background: linear-gradient(90deg, #f59e0b 0%, #f59e0b 60%, #fef3c7 60%, #fef3c7 100%);
}

.analytics-chart-bar.analytics-rejected {
  background: linear-gradient(90deg, #ef4444 0%, #ef4444 40%, #fee2e2 40%, #fee2e2 100%);
}

.analytics-bar-label {
  color: white;
  font-size: 12px;
  font-weight: 600;
  position: absolute;
  left: 12px;
}

.analytics-chart-legend {
  display: flex;
  flex-direction: row;
  gap: 8px;
}

.analytics-legend-item {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 12px;
  color: #6b7280;
}

.analytics-legend-color {
  width: 12px;
  height: 12px;
  border-radius: 50%;
}

.analytics-legend-color.analytics-accepted {
  background-color: #10b981;
}

.analytics-legend-color.analytics-pending {
  background-color: #f59e0b;
}

.analytics-legend-color.analytics-rejected {
  background-color: #ef4444;
}

.analytics-requests-table-container {
  overflow-x: auto;
}

.analytics-requests-table {
  width: 100%;
  border-collapse: collapse;
}

.analytics-requests-table th {
  text-align: left;
  padding: 12px;
  font-size: 14px;
  font-weight: 600;
  color: #6b7280;
  border-bottom: 1px solid #e5e7eb;
}

.analytics-requests-table td {
  padding: 12px;
  font-size: 14px;
  color: #374151;
  border-bottom: 1px solid #f3f4f6;
}

.analytics-requests-table tbody tr:hover {
  background-color: #f9fafb;
}

.analytics-details-btn {
  background-color: transparent;
  color: #37B7C3;
  border: 1px solid #37B7C3;
  border-radius: 20px;
  padding: 6px 16px;
  font-size: 12px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
}

.analytics-details-btn:hover {
  background-color: #37B7C3;
  color: white;
}

/* Responsive Design */
@media (max-width: 1024px) {
  .analytics-middle-section {
    grid-template-columns: 1fr;
  }
  
  .analytics-kpi-cards-row {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 768px) {
  .analytics-dashboard-content {
    padding: 16px;
  }
  
  .analytics-kpi-card,
  .analytics-staff-card,
  .analytics-studies-card,
  .analytics-requests-section {
    padding: 16px;
  }
  
  .analytics-requests-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 16px;
  }
  
  .analytics-requests-stats {
    flex-wrap: wrap;
  }
}

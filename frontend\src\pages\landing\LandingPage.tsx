import React, { useState, useEffect, useRef } from "react";
import { motion, useInView } from "framer-motion";
import Preloader from "@/components/common/Preloader";
import "./LandingPage.css";
import LandingHeader from "@/shared/LandingHeader";
import NurtifyFooter from "@/components/NurtifyFooter";
import ContactUsCTA from "@/components/ContactUsCTA";

// Animated Counter Component
const AnimatedCounter: React.FC<{
  target: number;
  suffix?: string;
  duration?: number;
  delay?: number;
}> = ({ target, suffix = "", duration = 1500, delay = 0 }) => {
  const [count, setCount] = useState(0);
  const countRef = useRef<HTMLDivElement>(null);
  const isInView = useInView(countRef, { once: true });

  useEffect(() => {
    if (!isInView) return;

    const startTime = performance.now() + delay;
    const animate = (currentTime: number) => {
      const elapsed = Math.max(0, currentTime - startTime);
      const progress = Math.min(elapsed / duration, 1);

      // Easing function for smooth animation
      const easeOutQuart = 1 - Math.pow(1 - progress, 4);
      const currentCount = Math.floor(easeOutQuart * target);

      setCount(currentCount);

      if (progress < 1) {
        requestAnimationFrame(animate);
      } else {
        setCount(target);
      }
    };

    requestAnimationFrame(animate);
  }, [isInView, target, duration, delay]);

  return (
    <div ref={countRef} className="stat-number">
      {count}{suffix}
    </div>
  );
};

const LandingPage: React.FC = () => {
  // Typewriter effect state
  const [currentSentenceIndex, setCurrentSentenceIndex] = useState(0);
  const [currentText, setCurrentText] = useState("");
  const [isTyping, setIsTyping] = useState(true);

  const sentences = [
    "Accelerating Recruitment.",
    "Empowering Clinical trial Teams.",
    "Improving Patient Outcomes.",
    "Life science at its best."
  ];

  // Typewriter effect
  useEffect(() => {
    let timeout: NodeJS.Timeout;

    const currentSentence = sentences[currentSentenceIndex];

    if (isTyping) {
      if (currentText.length < currentSentence.length) {
        timeout = setTimeout(() => {
          setCurrentText(currentSentence.substring(0, currentText.length + 1));
        }, 100);
      } else {
        timeout = setTimeout(() => {
          setIsTyping(false);
        }, 2000); // Wait 2 seconds before starting to delete
      }
    } else {
      if (currentText.length > 0) {
        timeout = setTimeout(() => {
          setCurrentText(currentText.substring(0, currentText.length - 1));
        }, 50);
      } else {
        timeout = setTimeout(() => {
          setCurrentSentenceIndex((prev) => (prev + 1) % sentences.length);
          setIsTyping(true);
        }, 500); // Wait 500ms before starting next sentence
      }
    }

    return () => clearTimeout(timeout);
  }, [currentText, currentSentenceIndex, isTyping, sentences]);

  // Define testimonials data
  const testimonials = [
    {
      name: "Dr. Sarah John",
      title: "Clinical Research Director",
      organization: "Parkview Medical Center",
      quote: "Nurtify transformed how we run clinical trials. Recruitment timelines shortened by 45%, data quality improved dramatically, and our team spends less time on paperwork and more time with patients.",
      avatar: "/assets/img/landing/avatar-placeholder.svg"
    },
    {
      name: "Dr. Michael Chen",
      title: "Head of Clinical Operations",
      organization: "Sunrise University Hospital",
      quote: "Since implementing Nurtify, we've seen a 38% increase in patient retention rates. The platform's intuitive design makes it easy for both our staff and trial participants to stay engaged throughout the entire process.",
      avatar: "/assets/img/landing/avatar-placeholder.svg"
    },
    {
      name: "Dr. Emily Rodriguez",
      title: "Research Program Director",
      organization: "Sunrise Medical Center",
      quote: "The real-time data visibility has been a game-changer for our multi-site trials. We can now identify and address issues before they become problems, saving us countless hours and resources.",
      avatar: "/assets/img/landing/avatar-placeholder.svg"
    }
  ];

  // State to track current testimonial
  const [currentTestimonialIndex, setCurrentTestimonialIndex] = useState(0);
  const [isAnimating, setIsAnimating] = useState(false);

  // Auto-rotate testimonials
  React.useEffect(() => {
    const autoRotateInterval = setInterval(() => {
      if (!isAnimating) {
        const currentCard = document.querySelector('.testimonial-card-large');
        if (currentCard) {
          setIsAnimating(true);
          currentCard.classList.add('flip-out-right');

          // Reduced timeout to make transition faster and reduce blank period
          setTimeout(() => {
            const nextIndex = (currentTestimonialIndex + 1) % testimonials.length;
            setCurrentTestimonialIndex(nextIndex);
            currentCard.classList.remove('flip-out-right');
            currentCard.classList.add('flip-in-left');

            // Reduced timeout for smoother transition
            setTimeout(() => {
              currentCard.classList.remove('flip-in-left');
              setIsAnimating(false);
            }, 400);
          }, 300);
        }
      }
    }, 7000); // Change testimonial every 7 seconds

    return () => clearInterval(autoRotateInterval);
  }, [currentTestimonialIndex, isAnimating, testimonials.length]);
  return (
    <div>
      <Preloader />
      <div>
        {/* HEADER */}

        {/* HERO SECTION */}
        <div className="hero-container-landing">
          <LandingHeader />
          <div className="hero-split-background" style={{ position: "relative" }}>
            <video
              className="hero-bg-video"
              src="/assets/img/landing/video.mp4"
              autoPlay
              muted
              loop
              playsInline
              preload="auto"
              aria-label="Hero Section Nurtify"
            />
            {/* Animated Background Circles */}
            {/*          
            <div className="hero-background-circles">
              <div className="circle-orbit-1"><div className="circle circle-1"></div></div>
              <div className="circle-orbit-2"><div className="circle circle-2"></div></div>
              <div className="circle-orbit-3"><div className="circle circle-3"></div></div>
              <div className="circle-orbit-4"><div className="circle circle-4"></div></div>
              <div className="circle-orbit-5"><div className="circle circle-5"></div></div>
              <div className="circle-orbit-6"><div className="circle circle-6"></div></div>
              <div className="circle-orbit-7"><div className="circle circle-7"></div></div>
            </div>
            */}
            <div
              className="header__container px-5"
              style={{
                paddingTop: "120px",
              }}
            >
              <div className="hero-content">
                <motion.h1
                  className="hero-title-enhanced"
                  initial={{ opacity: 0, scale: 0.8 }}
                  animate={{
                    opacity: 1,
                    scale: 1,
                    textShadow: [
                      "0px 0px 0px rgba(55, 183, 195, 0)",
                      "0px 0px 20px rgba(55, 183, 195, 0.3)",
                      "0px 0px 0px rgba(55, 183, 195, 0)"
                    ]
                  }}
                  transition={{
                    duration: 0.8,
                    delay: 0.2,
                    textShadow: {
                      duration: 2,
                      repeat: Infinity,
                      repeatType: "reverse"
                    }
                  }}
                >
                  Transform Clinical Trials<br />
                  with Cloud-Based <br />
                  Innovation
                </motion.h1>

                <motion.div
                  className="hero-subtitle-enhanced"
                  initial={{ opacity: 0, y: -10 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.4, delay: 0.5 }}
                >
                  <span className="typewriter-text">
                    {currentText}
                    <span className="cursor">|</span>
                  </span>
                  <div className="subtitle-static">
                    The Premier Solution for Source Data Management and Real-Time Clinical<br />
                    trial monitoring in the UK.
                  </div>
                </motion.div>

                <motion.a
                  href="https://meetings.hubspot.com/nurtify?embed=true"
                  className="mt-5"
                  target="_blank" rel="noopener noreferrer"
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.6, delay: 0.8 }}
                >
                  <div className="space-button-container">
                    <button className="space-button">

                      <svg width="18" height="18" viewBox="0 0 18 18" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <g clip-path="url(#clip0_1891_5647)">
                          <path d="M7.14724 9.36237C6.79849 9.24612 6.79849 8.75364 7.14724 8.63739L9.19429 7.95574C9.64429 7.8057 10.0531 7.55291 10.3885 7.2174C10.7238 6.8819 10.9764 6.4729 11.1262 6.02282L11.8078 3.97682C11.9241 3.62807 12.4165 3.62807 12.5328 3.97682L13.2144 6.02388C13.3645 6.47387 13.6173 6.88273 13.9528 7.21806C14.2883 7.55338 14.6973 7.80595 15.1473 7.95574L17.1933 8.63739C17.2697 8.66245 17.3362 8.71099 17.3834 8.77609C17.4305 8.84118 17.4559 8.9195 17.4559 8.99988C17.4559 9.08025 17.4305 9.15857 17.3834 9.22367C17.3362 9.28876 17.2697 9.3373 17.1933 9.36237L15.1463 10.044C14.6964 10.1939 14.2876 10.4466 13.9523 10.7819C13.617 11.1172 13.3644 11.526 13.2144 11.9759L12.5328 14.0229C12.5077 14.0993 12.4592 14.1658 12.3941 14.2129C12.329 14.2601 12.2507 14.2855 12.1703 14.2855C12.0899 14.2855 12.0116 14.2601 11.9465 14.2129C11.8814 14.1658 11.8329 14.0993 11.8078 14.0229L11.1262 11.9759C10.9762 11.526 10.7236 11.1172 10.3883 10.7819C10.053 10.4466 9.64417 10.1939 9.19429 10.044L7.14724 9.36237ZM1.75852 13.4449C1.71275 13.4297 1.67293 13.4005 1.6447 13.3614C1.61647 13.3223 1.60128 13.2754 1.60128 13.2272C1.60128 13.1789 1.61647 13.132 1.6447 13.0929C1.67293 13.0538 1.71275 13.0246 1.75852 13.0094L2.98654 12.6005C3.53397 12.4176 3.96304 11.9886 4.14587 11.4411L4.55486 10.2131C4.57001 10.1673 4.59921 10.1275 4.63829 10.0993C4.67737 10.0711 4.72435 10.0559 4.77257 10.0559C4.82077 10.0559 4.86776 10.0711 4.90684 10.0993C4.94592 10.1275 4.97511 10.1673 4.99027 10.2131L5.39926 11.4411C5.48921 11.7111 5.64081 11.9564 5.84204 12.1577C6.04327 12.3589 6.2886 12.5105 6.55859 12.6005L7.78661 13.0094C7.83238 13.0246 7.8722 13.0538 7.90043 13.0929C7.92866 13.132 7.94385 13.1789 7.94385 13.2272C7.94385 13.2754 7.92866 13.3223 7.90043 13.3614C7.8722 13.4005 7.83238 13.4297 7.78661 13.4449L6.55859 13.8538C6.2886 13.9438 6.04327 14.0954 5.84204 14.2966C5.64081 14.4979 5.48921 14.7432 5.39926 15.0132L4.99027 16.2412C4.97511 16.287 4.94592 16.3268 4.90684 16.355C4.86776 16.3832 4.82078 16.3984 4.77257 16.3984C4.72436 16.3984 4.67737 16.3832 4.63829 16.355C4.59921 16.3268 4.57001 16.287 4.55486 16.2412L4.14587 15.0132C4.05592 14.7432 3.90432 14.4979 3.70309 14.2966C3.50186 14.0954 3.25653 13.9438 2.98654 13.8538L1.75852 13.4449ZM0.649917 5.97421C0.619826 5.96377 0.593732 5.94423 0.575265 5.91828C0.556798 5.89233 0.546875 5.86127 0.546875 5.82942C0.546875 5.79757 0.556798 5.76652 0.575265 5.74057C0.593732 5.71462 0.619826 5.69507 0.649917 5.68464L1.46789 5.41198C1.83355 5.29045 2.11995 5.00405 2.24149 4.63839L2.51414 3.82041C2.52458 3.79032 2.54413 3.76423 2.57007 3.74576C2.59602 3.72729 2.62708 3.71737 2.65893 3.71737C2.69078 3.71737 2.72183 3.72729 2.74778 3.74576C2.77373 3.76423 2.79328 3.79032 2.80371 3.82041L3.07637 4.63839C3.13632 4.81859 3.23745 4.98233 3.37173 5.11662C3.50602 5.2509 3.66976 5.35203 3.84996 5.41198L4.66794 5.68464C4.69803 5.69507 4.72412 5.71462 4.74259 5.74057C4.76106 5.76652 4.77098 5.79757 4.77098 5.82942C4.77098 5.86127 4.76106 5.89233 4.74259 5.91828C4.72412 5.94423 4.69803 5.96377 4.66794 5.97421L3.84996 6.24687C3.66976 6.30681 3.50602 6.40794 3.37173 6.54223C3.23745 6.67651 3.13632 6.84026 3.07637 7.02046L2.80371 7.83738C2.79328 7.86747 2.77373 7.89356 2.74778 7.91203C2.72183 7.9305 2.69078 7.94042 2.65893 7.94042C2.62708 7.94042 2.59602 7.9305 2.57007 7.91203C2.54413 7.89356 2.52458 7.86747 2.51414 7.83738L2.24149 7.0194C2.11995 6.65374 1.83355 6.36734 1.46789 6.24581L0.650974 5.97421L0.649917 5.97421Z" fill="white" />
                        </g>
                        <defs>
                          <clipPath id="clip0_1891_5647">
                            <rect width="16.9091" height="16.9091" fill="white" transform="translate(0.546875 17.4551) rotate(-90)" />
                          </clipPath>
                        </defs>
                      </svg>
                      <span>Book a demo</span>
                    </button>
                  </div>
                </motion.a>
              </div>
            </div>
          </div>
        </div>
        {/* STATISTICS SECTION */}
        <section className="statistics-section">
          <div className="container">
            <div className="statistics-grid">
              <motion.div
                className="stat-item"
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{ once: true }}
                transition={{ duration: 0.5, delay: 0.1 }}
              >
                <AnimatedCounter target={50} suffix="%" duration={2500} delay={100} />
                <div className="stat-description">Automated Tasks</div>
              </motion.div>

              <div className="stat-divider"></div>

              <motion.div
                className="stat-item"
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{ once: true }}
                transition={{ duration: 0.5, delay: 0.2 }}
              >
                <AnimatedCounter target={50} suffix="+" duration={2500} delay={200} />
                <div className="stat-description">CRF Nurses consulted</div>
              </motion.div>

              <div className="stat-divider"></div>

              <motion.div
                className="stat-item"
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{ once: true }}
                transition={{ duration: 0.5, delay: 0.3 }}
              >
                <AnimatedCounter target={5} suffix="+" duration={2000} delay={300} />
                <div className="stat-description">Years Experience</div>
              </motion.div>

              <div className="stat-divider"></div>

              <motion.div
                className="stat-item"
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{ once: true }}
                transition={{ duration: 0.5, delay: 0.4 }}
              >
                <AnimatedCounter target={15} suffix="+" duration={2200} delay={400} />
                <div className="stat-description">Team Members</div>
              </motion.div>
            </div>
          </div>
        </section>

        {/* NURSES OVERVIEW SECTION */}
        <section className="nurses-overview-section">
          <div className="container nurses-overview-container">
            <div className="nurses-overview-content">
              <h2 className="nurses-overview-title">Nurtify Overview</h2>
              <p className="nurses-overview-description">
                Nurtify is a leader in life science innovation in the UK, specializing in cloud-based solutions for clinical research and trials.
                <br /><br />
                Our mission, encapsulated in Nurturing Unified Research Through Integrity, Future-readiness, and Yielded Governance.
                <br /><br />
                At Nurtify, we are proud to contribute to the advancement of life sciences in the UK ane the NHS long term plan by providing innovative technology that supports groundbreaking clinical research.
              </p>
            </div>
            <div className="nurses-overview-image">
              <img
                src="/assets/img/landing/nurses.png"
                alt="Nurtify Nurses"
                className="nurses-image"
                style={{ maxWidth: "100%", height: "auto" }}
              />
            </div>
          </div>
        </section>
        {/* WHY CHOOSE NURTIFY SECTION */}
        <section className="why-choose-section">
          <div className="container">
            <div className="why-choose-header text-center">
              <h2 className="why-choose-title">
                Empower Your Clinical Trials with<br />
                <span className="why-choose-highlight">Cutting-Edge Technology</span>
              </h2>
              <p className="why-choose-subtitle">
                Nurtify offers a robust suite of features tailored for clinical trial management

              </p>
            </div>

            <div className="why-choose-grid">
              <div className="row mt-4">
                <div className="col-md-4">
                  <motion.div
                    className="why-choose-card  "
                    initial={{ opacity: 0, y: 20 }}
                    whileInView={{ opacity: 1, y: 0 }}
                    viewport={{ once: true }}
                    transition={{ duration: 0.5, delay: 0.2 }}
                  >
                    <div className="why-choose-icon">
                      <img src="/assets/img/landing/icons/RealTime.svg" alt="Real-Time Trial Visibility" width="72" height="72" />
                    </div>
                    <div className="why-choose-content">
                      <h3 className="why-choose-card-title">
                        Real-Time Trial Visibility
                      </h3>
                      <p className="why-choose-description">
                        Capture and manage structured source data in real-time, ensuring accuracy, efficiency, and compliance with clinical research standards. <br />
                        Your dashboard gives you complete visibility into trial                         performance at every stage.
                      </p>
                    </div>
                  </motion.div>
                </div>

                <div className="col-md-4">
                  <motion.div
                    className="why-choose-card "
                    initial={{ opacity: 0, y: 20 }}
                    whileInView={{ opacity: 1, y: 0 }}
                    viewport={{ once: true }}
                    transition={{ duration: 0.5, delay: 0.3 }}
                  >
                    <div className="why-choose-icon">
                      <img src="/assets/img/landing/icons/Robust.svg" alt="Robust Source Data" width="72" height="72" />
                    </div>
                    <div className="why-choose-content">
                      <h3 className="why-choose-card-title">
                        Cloud-Based Platform
                      </h3>
                      <p className="why-choose-description">
                        Access your clinical trial data from anywhere, anytime, with our secure cloud infrastructure, ensuring seamless collaboration and scalability. <br />
                        Protect sensitive research data with strict security measures, GDPR compliance, and alignment with the UK NHS Compliance framework, ensuring your data is always secure and compliant.
                      </p>
                    </div>
                  </motion.div>
                </div>
                <div className="col-md-4">
                  <motion.div
                    className="why-choose-card "
                    initial={{ opacity: 0, y: 20 }}
                    whileInView={{ opacity: 1, y: 0 }}
                    viewport={{ once: true }}
                    transition={{ duration: 0.5, delay: 0.4 }}
                  >
                    <div className="why-choose-icon">
                      <img src="/assets/img/landing/icons/FullyDigital.svg" alt="Fully Digital Workflows" width="72" height="72" />
                    </div>
                    <div className="why-choose-content">
                      <h3 className="why-choose-card-title">
                        Fully Digital Workflows
                      </h3>
                      <p className="why-choose-description">
                        Our platform eliminates paper-based administrative
                        burdens by digitizing every process, streamlined for
                        maximum efficiency. <br />

                        Streamline clinical workflows with intuitive dashboards
                        for structured data entry, real-time trial monitoring, and
                        comprehensive reporting, allowing clinicians to focus on patient care.
                      </p>
                    </div>
                  </motion.div>
                </div>
              </div>
              <div className="row mt-4">
                <div className="col-md-4">
                  <motion.div
                    className="why-choose-card "
                    initial={{ opacity: 0, y: 20 }}
                    whileInView={{ opacity: 1, y: 0 }}
                    viewport={{ once: true }}
                    transition={{ duration: 0.5, delay: 0.5 }}
                  >
                    <div className="why-choose-icon">
                      <img src="/assets/img/landing/icons/ParticipantEngagement.svg" alt="Participant Engagement" width="72" height="72" />
                    </div>
                    <div className="why-choose-content">
                      <h3 className="why-choose-card-title">
                        Participant Engagement
                      </h3>
                      <p className="why-choose-description">
                        Empower trial participants with a user-friendly portal for appointment scheduling, health tracking, and direct communication with research teams, boosting engagement and adherence. <br />
                        Dramatically reduce dropout rates by keeping participants informed and connected throughout the trial
                      </p>
                    </div>
                  </motion.div>
                </div>
                <div className="col-md-4">
                  <motion.div
                    className="why-choose-card "
                    initial={{ opacity: 0, y: 20 }}
                    whileInView={{ opacity: 1, y: 0 }}
                    viewport={{ once: true }}
                    transition={{ duration: 0.5, delay: 0.6 }}
                  >
                    <div className="why-choose-icon">
                      <img src="/assets/img/landing/icons/UnifiedPatient.svg" alt="Unified Patient Profiles" width="72" height="72" />
                    </div>
                    <div className="why-choose-content">
                      <h3 className="why-choose-card-title">
                        Collaborative Source Data Management
                      </h3>
                      <p className="why-choose-description">
                        Nurtify comprehensive platform connects patients, clinicians, sponsors and research teams, streamlining all aspects of source data management while reducing administrative burdens, enhancing patient engagement, and ensuring ALCOA Compliance.                      </p>
                    </div>
                  </motion.div>
                </div>

                <div className="col-md-4">
                  <motion.div
                    className="why-choose-card "
                    initial={{ opacity: 0, y: 20 }}
                    whileInView={{ opacity: 1, y: 0 }}
                    viewport={{ once: true }}
                    transition={{ duration: 0.5, delay: 0.7 }}
                  >
                    <div className="why-choose-icon">
                      <img src="/assets/img/landing/icons/AutomatedWorksheet.svg" alt="Automated Worksheet Generation" width="72" height="72" />
                    </div>
                    <div className="why-choose-content">
                      <h3 className="why-choose-card-title">
                        Automated Worksheet Generation
                      </h3>
                      <p className="why-choose-description">
                        Reclaim hours with smart worksheets that auto-populate
                        based on protocol requirements. Our templates adapt to
                        protocol changes, ensuring your documentation is always
                        in compliance. <br />
                        With structured data entry and real-time monitoring, minimize errors and ensure high-quality research outcomes and faster trial timelines.

                      </p>
                    </div>
                  </motion.div>
                </div>

              </div>
            </div>
          </div>
        </section>

        {/* SEAMLESS CONNECTION SECTION */}
        <section className="seamless-connection-section">
          <motion.h2
            className="seamless-connection-title"
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.6 }}
          >
            Seamless Connection Between Patients
            <br />
            and Research Teams
          </motion.h2>
          <motion.p
            className="seamless-connection-description"
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.6, delay: 0.1 }}
          >
            Our intuitive mobile app bridges the gap between patients and
            research teams, creating a continuous feedback loop that
            improves compliance and outcomes.
          </motion.p>

          <div className="seamless-connection-content">

            <div className="mobile-devices-container">
              {/* Left side feature bubbles */}
              <div className="feature-bubbles-left">
                <motion.div
                  className="feature-bubble feature-bubble-left-1"
                  initial={{ opacity: 0, scale: 0.8 }}
                  whileInView={{ opacity: 1, scale: 1 }}
                  viewport={{ once: true }}
                  transition={{ duration: 0.5, delay: 0.4 }}
                >
                  <span className="bubble-icon">
                    <svg
                      width="32"
                      height="32"
                      viewBox="0 0 32 32"
                      fill="none"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <rect width="32" height="32" rx="8" fill="#37B7C3" />
                      <path
                        d="M21 6V8M16 6V8M11 6V8"
                        stroke="white"
                        stroke-width="1.5"
                        stroke-linecap="round"
                        stroke-linejoin="round"
                      />
                      <path
                        d="M7.5 14C7.5 10.7002 7.5 9.05025 8.52513 8.02513C9.55025 7 11.2002 7 14.5 7H17.5C20.7998 7 22.4497 7 23.4749 8.02513C24.5 9.05025 24.5 10.7002 24.5 14V19C24.5 22.2998 24.5 23.9497 23.4749 24.9749C22.4497 26 20.7998 26 17.5 26H14.5C11.2002 26 9.55025 26 8.52513 24.9749C7.5 23.9497 7.5 22.2998 7.5 19V14Z"
                        stroke="white"
                        stroke-width="1.5"
                        stroke-linecap="round"
                        stroke-linejoin="round"
                      />
                      <path
                        d="M17.5 20H21"
                        stroke="white"
                        stroke-width="1.5"
                        stroke-linecap="round"
                      />
                      <path
                        d="M17.5 13H21"
                        stroke="white"
                        stroke-width="1.5"
                        stroke-linecap="round"
                      />
                      <path
                        d="M11 14C11 14 11.5 14 12 15C12 15 13.5882 12.5 15 12"
                        stroke="white"
                        stroke-width="1.5"
                        stroke-linecap="round"
                        stroke-linejoin="round"
                      />
                      <path
                        d="M11 21C11 21 11.5 21 12 22C12 22 13.5882 19.5 15 19"
                        stroke="white"
                        stroke-width="1.5"
                        stroke-linecap="round"
                        stroke-linejoin="round"
                      />
                    </svg>
                  </span>
                  <span className="bubble-text">Easy form completion</span>
                </motion.div>
                <motion.div
                  className="feature-bubble feature-bubble-left-2"
                  initial={{ opacity: 0, scale: 0.8 }}
                  whileInView={{ opacity: 1, scale: 1 }}
                  viewport={{ once: true }}
                  transition={{ duration: 0.5, delay: 0.6 }}
                >
                  <span className="bubble-icon">
                    <svg
                      width="32"
                      height="32"
                      viewBox="0 0 32 32"
                      fill="none"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <rect width="32" height="32" rx="8" fill="#37B7C3" />
                      <path
                        d="M21 6V8M16 6V8M11 6V8"
                        stroke="white"
                        stroke-width="1.5"
                        stroke-linecap="round"
                        stroke-linejoin="round"
                      />
                      <path
                        d="M7.5 14C7.5 10.7002 7.5 9.05025 8.52513 8.02513C9.55025 7 11.2002 7 14.5 7H17.5C20.7998 7 22.4497 7 23.4749 8.02513C24.5 9.05025 24.5 10.7002 24.5 14V19C24.5 22.2998 24.5 23.9497 23.4749 24.9749C22.4497 26 20.7998 26 17.5 26H14.5C11.2002 26 9.55025 26 8.52513 24.9749C7.5 23.9497 7.5 22.2998 7.5 19V14Z"
                        stroke="white"
                        stroke-width="1.5"
                        stroke-linecap="round"
                        stroke-linejoin="round"
                      />
                      <path
                        d="M17.5 20H21"
                        stroke="white"
                        stroke-width="1.5"
                        stroke-linecap="round"
                      />
                      <path
                        d="M17.5 13H21"
                        stroke="white"
                        stroke-width="1.5"
                        stroke-linecap="round"
                      />
                      <path
                        d="M11 14C11 14 11.5 14 12 15C12 15 13.5882 12.5 15 12"
                        stroke="white"
                        stroke-width="1.5"
                        stroke-linecap="round"
                        stroke-linejoin="round"
                      />
                      <path
                        d="M11 21C11 21 11.5 21 12 22C12 22 13.5882 19.5 15 19"
                        stroke="white"
                        stroke-width="1.5"
                        stroke-linecap="round"
                        stroke-linejoin="round"
                      />
                    </svg>
                  </span>
                  <span className="bubble-text">Appointment reminders</span>
                </motion.div>
                <motion.div
                  className="feature-bubble feature-bubble-left-3"
                  initial={{ opacity: 0, scale: 0.8 }}
                  whileInView={{ opacity: 1, scale: 1 }}
                  viewport={{ once: true }}
                  transition={{ duration: 0.5, delay: 0.8 }}
                >
                  <span className="bubble-icon">
                    <svg
                      width="32"
                      height="32"
                      viewBox="0 0 32 32"
                      fill="none"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <rect width="32" height="32" rx="8" fill="#37B7C3" />
                      <path
                        d="M21 6V8M16 6V8M11 6V8"
                        stroke="white"
                        stroke-width="1.5"
                        stroke-linecap="round"
                        stroke-linejoin="round"
                      />
                      <path
                        d="M7.5 14C7.5 10.7002 7.5 9.05025 8.52513 8.02513C9.55025 7 11.2002 7 14.5 7H17.5C20.7998 7 22.4497 7 23.4749 8.02513C24.5 9.05025 24.5 10.7002 24.5 14V19C24.5 22.2998 24.5 23.9497 23.4749 24.9749C22.4497 26 20.7998 26 17.5 26H14.5C11.2002 26 9.55025 26 8.52513 24.9749C7.5 23.9497 7.5 22.2998 7.5 19V14Z"
                        stroke="white"
                        stroke-width="1.5"
                        stroke-linecap="round"
                        stroke-linejoin="round"
                      />
                      <path
                        d="M17.5 20H21"
                        stroke="white"
                        stroke-width="1.5"
                        stroke-linecap="round"
                      />
                      <path
                        d="M17.5 13H21"
                        stroke="white"
                        stroke-width="1.5"
                        stroke-linecap="round"
                      />
                      <path
                        d="M11 14C11 14 11.5 14 12 15C12 15 13.5882 12.5 15 12"
                        stroke="white"
                        stroke-width="1.5"
                        stroke-linecap="round"
                        stroke-linejoin="round"
                      />
                      <path
                        d="M11 21C11 21 11.5 21 12 22C12 22 13.5882 19.5 15 19"
                        stroke="white"
                        stroke-width="1.5"
                        stroke-linecap="round"
                        stroke-linejoin="round"
                      />
                    </svg>
                  </span>
                  <span className="bubble-text">
                    Secure messaging with research staff
                  </span>
                </motion.div>
              </div>

              {/* Combined mobile phones image */}
              <motion.div
                className="combined-mobile-image"
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{ once: true }}
                transition={{ duration: 0.7, delay: 0.2 }}
              >
                <img
                  src="/assets/img/landing/mobiles-images.png"
                  alt="Patient and doctor mobile communication"
                  className="mobile-phones-image"
                />
              </motion.div>

              {/* Right side feature bubbles */}
              <div className="feature-bubbles-right">
                <motion.div
                  className="feature-bubble feature-bubble-right-1"
                  initial={{ opacity: 0, scale: 0.8 }}
                  whileInView={{ opacity: 1, scale: 1 }}
                  viewport={{ once: true }}
                  transition={{ duration: 0.5, delay: 0.4 }}
                >
                  <span className="bubble-icon">
                    <svg
                      width="32"
                      height="32"
                      viewBox="0 0 32 32"
                      fill="none"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <rect width="32" height="32" rx="8" fill="#37B7C3" />
                      <path
                        d="M21 6V8M16 6V8M11 6V8"
                        stroke="white"
                        stroke-width="1.5"
                        stroke-linecap="round"
                        stroke-linejoin="round"
                      />
                      <path
                        d="M7.5 14C7.5 10.7002 7.5 9.05025 8.52513 8.02513C9.55025 7 11.2002 7 14.5 7H17.5C20.7998 7 22.4497 7 23.4749 8.02513C24.5 9.05025 24.5 10.7002 24.5 14V19C24.5 22.2998 24.5 23.9497 23.4749 24.9749C22.4497 26 20.7998 26 17.5 26H14.5C11.2002 26 9.55025 26 8.52513 24.9749C7.5 23.9497 7.5 22.2998 7.5 19V14Z"
                        stroke="white"
                        stroke-width="1.5"
                        stroke-linecap="round"
                        stroke-linejoin="round"
                      />
                      <path
                        d="M17.5 20H21"
                        stroke="white"
                        stroke-width="1.5"
                        stroke-linecap="round"
                      />
                      <path
                        d="M17.5 13H21"
                        stroke="white"
                        stroke-width="1.5"
                        stroke-linecap="round"
                      />
                      <path
                        d="M11 14C11 14 11.5 14 12 15C12 15 13.5882 12.5 15 12"
                        stroke="white"
                        stroke-width="1.5"
                        stroke-linecap="round"
                        stroke-linejoin="round"
                      />
                      <path
                        d="M11 21C11 21 11.5 21 12 22C12 22 13.5882 19.5 15 19"
                        stroke="white"
                        stroke-width="1.5"
                        stroke-linecap="round"
                        stroke-linejoin="round"
                      />
                    </svg>
                  </span>
                  <span className="bubble-text">
                    Real-time symptom reporting
                  </span>
                </motion.div>
                <motion.div
                  className="feature-bubble feature-bubble-right-2"
                  initial={{ opacity: 0, scale: 0.8 }}
                  whileInView={{ opacity: 1, scale: 1 }}
                  viewport={{ once: true }}
                  transition={{ duration: 0.5, delay: 0.6 }}
                >
                  <span className="bubble-icon">
                    <svg
                      width="32"
                      height="32"
                      viewBox="0 0 32 32"
                      fill="none"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <rect width="32" height="32" rx="8" fill="#37B7C3" />
                      <path
                        d="M21 6V8M16 6V8M11 6V8"
                        stroke="white"
                        stroke-width="1.5"
                        stroke-linecap="round"
                        stroke-linejoin="round"
                      />
                      <path
                        d="M7.5 14C7.5 10.7002 7.5 9.05025 8.52513 8.02513C9.55025 7 11.2002 7 14.5 7H17.5C20.7998 7 22.4497 7 23.4749 8.02513C24.5 9.05025 24.5 10.7002 24.5 14V19C24.5 22.2998 24.5 23.9497 23.4749 24.9749C22.4497 26 20.7998 26 17.5 26H14.5C11.2002 26 9.55025 26 8.52513 24.9749C7.5 23.9497 7.5 22.2998 7.5 19V14Z"
                        stroke="white"
                        stroke-width="1.5"
                        stroke-linecap="round"
                        stroke-linejoin="round"
                      />
                      <path
                        d="M17.5 20H21"
                        stroke="white"
                        stroke-width="1.5"
                        stroke-linecap="round"
                      />
                      <path
                        d="M17.5 13H21"
                        stroke="white"
                        stroke-width="1.5"
                        stroke-linecap="round"
                      />
                      <path
                        d="M11 14C11 14 11.5 14 12 15C12 15 13.5882 12.5 15 12"
                        stroke="white"
                        stroke-width="1.5"
                        stroke-linecap="round"
                        stroke-linejoin="round"
                      />
                      <path
                        d="M11 21C11 21 11.5 21 12 22C12 22 13.5882 19.5 15 19"
                        stroke="white"
                        stroke-width="1.5"
                        stroke-linecap="round"
                        stroke-linejoin="round"
                      />
                    </svg>
                  </span>
                  <span className="bubble-text">
                    Medication adherence tracking
                  </span>
                </motion.div>
                <motion.div
                  className="feature-bubble feature-bubble-right-3"
                  initial={{ opacity: 0, scale: 0.8 }}
                  whileInView={{ opacity: 1, scale: 1 }}
                  viewport={{ once: true }}
                  transition={{ duration: 0.5, delay: 0.8 }}
                >
                  <span className="bubble-icon">
                    <svg
                      width="32"
                      height="32"
                      viewBox="0 0 32 32"
                      fill="none"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <rect width="32" height="32" rx="8" fill="#37B7C3" />
                      <path
                        d="M21 6V8M16 6V8M11 6V8"
                        stroke="white"
                        stroke-width="1.5"
                        stroke-linecap="round"
                        stroke-linejoin="round"
                      />
                      <path
                        d="M7.5 14C7.5 10.7002 7.5 9.05025 8.52513 8.02513C9.55025 7 11.2002 7 14.5 7H17.5C20.7998 7 22.4497 7 23.4749 8.02513C24.5 9.05025 24.5 10.7002 24.5 14V19C24.5 22.2998 24.5 23.9497 23.4749 24.9749C22.4497 26 20.7998 26 17.5 26H14.5C11.2002 26 9.55025 26 8.52513 24.9749C7.5 23.9497 7.5 22.2998 7.5 19V14Z"
                        stroke="white"
                        stroke-width="1.5"
                        stroke-linecap="round"
                        stroke-linejoin="round"
                      />
                      <path
                        d="M17.5 20H21"
                        stroke="white"
                        stroke-width="1.5"
                        stroke-linecap="round"
                      />
                      <path
                        d="M17.5 13H21"
                        stroke="white"
                        stroke-width="1.5"
                        stroke-linecap="round"
                      />
                      <path
                        d="M11 14C11 14 11.5 14 12 15C12 15 13.5882 12.5 15 12"
                        stroke="white"
                        stroke-width="1.5"
                        stroke-linecap="round"
                        stroke-linejoin="round"
                      />
                      <path
                        d="M11 21C11 21 11.5 21 12 22C12 22 13.5882 19.5 15 19"
                        stroke="white"
                        stroke-width="1.5"
                        stroke-linecap="round"
                        stroke-linejoin="round"
                      />
                    </svg>
                  </span>
                  <span className="bubble-text">
                    Visit scheduling and reminders
                  </span>
                </motion.div>
              </div>
            </div>



            <div className="connection-buttons">
              <motion.a
                href="https://apps.apple.com/bj/developer/apple/id284417353?see-all=i-phone-apps"
                className="app-store-button"
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{ once: true }}
                transition={{ duration: 0.5, delay: 1.0 }}
              >
                <div className="app-button-content">
                  <span className="app-button-icon">
                    <svg
                      width="19"
                      height="23"
                      viewBox="0 0 19 23"
                      fill="none"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <path
                        d="M18.6563 16.7009C18.308 17.817 17.7589 18.933 17.0089 20.0491C15.8571 21.7991 14.7098 22.6741 13.567 22.6741C13.1295 22.6741 12.5045 22.5313 11.692 22.2455C10.9241 21.9598 10.25 21.817 9.66964 21.817C9.125 21.817 8.49107 21.9643 7.76786 22.2589C7.04464 22.5625 6.45536 22.7143 6 22.7143C4.64286 22.7143 3.29911 21.558 1.96875 19.2455C0.65625 16.9152 0 14.6696 0 12.5089C0 10.4732 0.504464 8.80357 1.51339 7.5C2.52232 6.21428 3.79018 5.57143 5.31696 5.57143C5.95982 5.57143 6.75 5.70536 7.6875 5.97321C8.61607 6.24107 9.23214 6.375 9.53571 6.375C9.9375 6.375 10.5759 6.22321 11.4509 5.91964C12.3616 5.61607 13.1339 5.46428 13.7679 5.46428C14.8304 5.46428 15.7813 5.75446 16.6205 6.33482C17.0848 6.65625 17.5491 7.10268 18.0134 7.67411C17.308 8.27232 16.7991 8.79911 16.4866 9.25446C15.9063 10.0937 15.6161 11.0179 15.6161 12.0268C15.6161 13.1339 15.9241 14.1295 16.5402 15.0134C17.1563 15.8973 17.8616 16.4598 18.6563 16.7009ZM13.6205 0.991071C13.6205 1.53571 13.4911 2.14286 13.2321 2.8125C12.9643 3.48214 12.5491 4.09821 11.9866 4.66071C11.5045 5.14286 11.0223 5.46428 10.5402 5.625C10.2098 5.72321 9.74554 5.79911 9.14732 5.85268C9.17411 4.52232 9.52232 3.375 10.192 2.41071C10.8527 1.45536 11.9688 0.794641 13.5402 0.42857C13.5491 0.455356 13.558 0.504463 13.567 0.575892C13.5848 0.647321 13.5982 0.696428 13.6071 0.723214C13.6071 0.758928 13.6071 0.803571 13.6071 0.857142C13.6161 0.910713 13.6205 0.955356 13.6205 0.991071Z"
                        fill="white"
                      />
                    </svg>{" "}
                  </span>
                  <div className="app-button-text">
                    <span className="app-button-small">Download on the</span>
                    <span className="app-button-large">App Store</span>
                  </div>
                </div>
              </motion.a>
              <motion.a
                href="https://play.google.com/store/games?hl=en&pli=1"
                className="google-play-button"
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{ once: true }}
                transition={{ duration: 0.5, delay: 1.2 }}
              >
                <div className="app-button-content">
                  <span className="app-button-icon">
                    <svg
                      width="21"
                      height="21"
                      viewBox="0 0 21 21"
                      fill="none"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <path
                        d="M10.2857 8.95536H19.9955C20.1027 9.55357 20.1563 10.125 20.1563 10.6696C20.1563 12.6071 19.75 14.3393 18.9375 15.8661C18.125 17.3839 16.9643 18.5714 15.4554 19.4286C13.9554 20.2857 12.2321 20.7143 10.2857 20.7143C8.88393 20.7143 7.54911 20.442 6.28125 19.8973C5.01339 19.3616 3.91964 18.6339 3 17.7143C2.08036 16.7946 1.34821 15.7009 0.803571 14.433C0.267857 13.1652 0 11.8304 0 10.4286C0 9.02679 0.267857 7.69196 0.803571 6.42411C1.34821 5.15625 2.08036 4.0625 3 3.14286C3.91964 2.22321 5.01339 1.49554 6.28125 0.959821C7.54911 0.415178 8.88393 0.142856 10.2857 0.142856C12.9643 0.142856 15.2634 1.04018 17.183 2.83482L14.3839 5.52678C13.2857 4.46428 11.9196 3.93303 10.2857 3.93303C9.13393 3.93303 8.06696 4.22321 7.08482 4.80357C6.11161 5.38393 5.33929 6.17411 4.76786 7.17411C4.19643 8.16518 3.91071 9.25 3.91071 10.4286C3.91071 11.6071 4.19643 12.6964 4.76786 13.6964C5.33929 14.6875 6.11161 15.4732 7.08482 16.0536C8.06696 16.6339 9.13393 16.9241 10.2857 16.9241C11.0625 16.9241 11.7768 16.817 12.4286 16.6027C13.0804 16.3884 13.6161 16.1205 14.0357 15.7991C14.4554 15.4777 14.8214 15.1116 15.1339 14.7009C15.4464 14.2902 15.6741 13.9018 15.817 13.5357C15.9688 13.1696 16.0714 12.8214 16.125 12.4911H10.2857V8.95536Z"
                        fill="white"
                      />
                    </svg>
                  </span>
                  <div className="app-button-text">
                    <span className="app-button-small">Get it on</span>
                    <span className="app-button-large">Google Play</span>
                  </div>
                </div>
              </motion.a>
            </div>
          </div>
        </section>

        {/* ARTICLES SECTION */}
        <section className="articles-section">
          <div className="container">
            <div className="articles-header">
              <h2 className="articles-title">
                Latest Articles From
                <br />
                <span className="articles-highlight">
                  Clinical Research
                </span>{" "}
                Experts
              </h2>
              <a href="../blog">
                <button className="articles-view-all">
                  View All Articles
                </button>
              </a>
            </div>

            <div className="articles-grid">
              <div className="row">
                <div className="col-md-4">
                  <motion.div
                    className="article-card"
                    initial={{ opacity: 0, y: 20 }}
                    whileInView={{ opacity: 1, y: 0 }}
                    viewport={{ once: true }}
                    transition={{ duration: 0.5, delay: 0.1 }}
                  >
                    <div className="article-image">
                      <img
                        src="/assets/img/landing/Image_one.png"
                        alt="Decentralized Trials"
                      />
                    </div>
                    <div className="article-content">
                      <h3 className="article-title-latest" style={{ color: "black" }}>
                        The Future of Decentralized Trials: What's Working Now]
                      </h3>
                      <p className="article-description">
                        A comprehensive guide to understanding the complexities of clinical trials and care.
                      </p>
                      <a href="../blog/1-clinical-trials" className="article-link">
                        Read More
                      </a>
                    </div>
                  </motion.div>
                </div>

                <div className="col-md-4">
                  <motion.div
                    className="article-card"
                    initial={{ opacity: 0, y: 20 }}
                    whileInView={{ opacity: 1, y: 0 }}
                    viewport={{ once: true }}
                    transition={{ duration: 0.5, delay: 0.2 }}
                  >
                    <div className="article-image">
                      <img
                        src="/assets/img/landing/Image_second.png"
                        alt="Recruitment Time"
                      />
                    </div>
                    <div className="article-content">
                      <h3 className="article-title-latest" style={{ color: "black" }}>
                        Advanced Diabetes: A Deep Dive into DKA and HHS
                      </h3>
                      <p className="article-description">
                        Understanding the pathophysiology, diagnosis, and management of
                        diabetic ketoacidosis and hyperosmolar hyperglycemic state.
                      </p>
                      <a href="../blog/2-advanced-diabetes-a-deep-dive-into-dka-and-hhs" className="article-link">
                        Read More
                      </a>
                    </div>
                  </motion.div>
                </div>

                <div className="col-md-4">
                  <motion.div
                    className="article-card"
                    initial={{ opacity: 0, y: 20 }}
                    whileInView={{ opacity: 1, y: 0 }}
                    viewport={{ once: true }}
                    transition={{ duration: 0.5, delay: 0.3 }}
                  >
                    <div className="article-image">
                      <img
                        src="/assets/img/landing/Image_third.png"
                        alt="Patient-Centered Design"
                      />
                    </div>
                    <div className="article-content">
                      <h3 className="article-title-latest" style={{ color: "black" }}>
                        Patient-Centered Design: The Key to Trial Retention
                      </h3>
                      <p className="article-description">
                        Exploring the latest research and innovations in critical care nursing to improve patient outcomes and quality of care.
                      </p>
                      <a href="../blog/4-advances-in-critical-care-nursing-evidence-based-practices" className="article-link">
                        Read More
                      </a>
                    </div>
                  </motion.div>
                </div>
              </div>
            </div>
          </div>
        </section>

        {/* TRUSTED BY INDUSTRY LEADERS SECTION */}
        <section className="trusted-leaders-section">
          <div className="container">
            <motion.h2
              className="trusted-leaders-title"
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.6 }}
            >
              Trusted by Industry Leaders
            </motion.h2>

            <div className="testimonial-carousel">
              <button
                className="carousel-arrow carousel-arrow-prev"
                onClick={() => {
                  const currentCard = document.querySelector('.testimonial-card-large');
                  currentCard?.classList.add('flip-out-left');

                  // Reduced timeout for faster transition
                  setTimeout(() => {
                    const prevIndex = (currentTestimonialIndex - 1 + testimonials.length) % testimonials.length;
                    setCurrentTestimonialIndex(prevIndex);
                    currentCard?.classList.remove('flip-out-left');
                    currentCard?.classList.add('flip-in-right');

                    // Reduced timeout for smoother transition
                    setTimeout(() => {
                      currentCard?.classList.remove('flip-in-right');
                    }, 400);
                  }, 300);
                }}
                aria-label="Previous testimonial"
              >
                <span className="arrow-icon">&#8249;</span>
              </button>

              <motion.div
                className="testimonial-card-large"
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{ once: true }}
                transition={{ duration: 0.5 }}
              >
                <div className="testimonial-header">
                  <div className="testimonial-avatar">
                    <img
                      src={testimonials[currentTestimonialIndex].avatar || "/assets/img/landing/avatar-placeholder.png"}
                      alt={testimonials[currentTestimonialIndex].name}
                    />
                  </div>
                  <div className="testimonial-author-info">
                    <h3 className="testimonial-author-name" style={{ color: "black" }}>{testimonials[currentTestimonialIndex].name}</h3>
                    <p className="testimonial-author-title">
                      {testimonials[currentTestimonialIndex].title}<br />
                      {testimonials[currentTestimonialIndex].organization}
                    </p>
                  </div>
                </div>
                <div className="testimonial-body">
                  <p className="testimonial-quote">
                    "{testimonials[currentTestimonialIndex].quote}"
                  </p>
                </div>
              </motion.div>

              <button
                className="carousel-arrow carousel-arrow-next"
                onClick={() => {
                  const currentCard = document.querySelector('.testimonial-card-large');
                  currentCard?.classList.add('flip-out-right');

                  // Reduced timeout for faster transition
                  setTimeout(() => {
                    const nextIndex = (currentTestimonialIndex + 1) % testimonials.length;
                    setCurrentTestimonialIndex(nextIndex);
                    currentCard?.classList.remove('flip-out-right');
                    currentCard?.classList.add('flip-in-left');

                    // Reduced timeout for smoother transition
                    setTimeout(() => {
                      currentCard?.classList.remove('flip-in-left');
                    }, 400);
                  }, 300);
                }}
                aria-label="Next testimonial"
              >
                <span className="arrow-icon">&#8250;</span>
              </button>
            </div>
          </div>
        </section>
        <section className="partners-section">
          <div className="partners-container">
            <h2 className="partners-title">Our Partners</h2>
            <div className="trusted-logos">
              <div className="logos-track">
                <img src="/assets/img/landing/companies/forbes-logo.png" alt="Forbes" className="trusted-logo" />
                <img src="/assets/img/landing/companies/bca-logo.png" alt="BCA" className="trusted-logo" />
                <img src="/assets/img/landing/companies/gopay-logo.png" alt="Gopay" className="trusted-logo" />
                <img src="/assets/img/landing/companies/gopay-logo.png" alt="Gopay" className="trusted-logo" />
                <img src="/assets/img/landing/companies/paypal-logo.png" alt="PayPal" className="trusted-logo" />
                <img src="/assets/img/landing/companies/youtube-logo.png" alt="YouTube" className="trusted-logo" />
                {/* Repeat for seamless animation */}
                <img src="/assets/img/landing/companies/forbes-logo.png" alt="Forbes" className="trusted-logo" />
                <img src="/assets/img/landing/companies/bca-logo.png" alt="BCA" className="trusted-logo" />
                <img src="/assets/img/landing/companies/gopay-logo.png" alt="Gopay" className="trusted-logo" />
                <img src="/assets/img/landing/companies/gopay-logo.png" alt="Gopay" className="trusted-logo" />
                <img src="/assets/img/landing/companies/paypal-logo.png" alt="PayPal" className="trusted-logo" />
                <img src="/assets/img/landing/companies/youtube-logo.png" alt="YouTube" className="trusted-logo" />
              </div>
            </div>
          </div>
        </section>
        {/* PATIENT PARTICIPATION SECTION */}
        <section className="participation-section">
          <div className="participation-container">
            <div className="participation-row">
              <div className="participation-image-col">
                <motion.div
                  className="participation-image-container"
                  initial={{ opacity: 0, x: -20 }}
                  whileInView={{ opacity: 1, x: 0 }}
                  viewport={{ once: true }}
                  transition={{ duration: 0.6 }}
                >
                  <img
                    src="/assets/img/landing/patient_with_nurse.png"
                    alt="Doctor consulting with patient"
                    className="participation-image"
                  />
                </motion.div>
              </div>
              <div className="participation-content-col">
                <motion.div
                  className="participation-content"
                  initial={{ opacity: 0, y: 20 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  viewport={{ once: true }}
                  transition={{ duration: 0.6 }}
                >
                  <h2 className="participation-title">
                    Interested in{" "}<br />
                    <span className="participation-highlight">
                      Participating or <br /> Referring
                    </span>{" "}
                    a Patient?
                  </h2>
                  <p className="participation-description">
                    Whether you're considering joining a study or referring a
                    patient to one of our research centers, we're here to help.
                  </p>
                  <p className="participation-description" style={{ fontWeight: "bold" }}>
                    Get in touch today to explore opportunities and take the
                    next step in advancing medical research.
                  </p>
                  <div className="participation-action">
                    <a href="../contact-us">
                      <button className="participation-button">
                        Get in touch
                      </button></a>
                  </div>
                </motion.div>
              </div>
            </div>
          </div>
        </section>

        {/* SERVICE SECTION */}
        <ContactUsCTA />
        <NurtifyFooter />
      </div>
    </div>
  );
};

export default LandingPage;

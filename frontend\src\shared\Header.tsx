import { useState, useEffect } from "react";
import { Link, useNavigate } from "react-router-dom";
import MobileMenu from "./MobileMenu";
import { Menu } from "lucide-react";
import { useCurrentUserQuery } from "@/hooks/user.query";
import Preloader from "@/components/common/Preloader";
import NotificationBell from "@/components/common/NotificationBell";
import SystemNotificationBell from "@/components/common/SystemNotificationBell";
import NotificationTester from "@/components/common/NotificationTester";
import { useKeycloak } from "@react-keycloak/web";
import { useQueryClient } from "@tanstack/react-query";


// Types
interface MenuItem {
  title: string;
  link: string;
  disabled?: boolean;
}


interface LogoProps {
  isMobile: boolean;
}

interface MobileButtonsProps {
  setActiveMobileMenu: (active: boolean) => void;
}


// No longer needed since we're using Lucide icons directly

// Constants
const MENU_ITEMS: MenuItem[] = [
  { title: "Home", link: "home" },
  ...(import.meta.env.VITE_APP_MODE === "development" ? [{ title: "Ressources", link: "sdk" }] : []),
  { title: "Blog", link: "blog" },
  { title: "Contact Us", link: "contact-us" },
  { title: "About Us", link: "about-us" },
];


// Styles
const styles = {
  header: (isMobile: boolean) => ({
    backgroundImage: isMobile
      ? "none"
      : "url('/assets/img/authentication/shape.png')",
    backgroundSize: "auto",
    backgroundColor: isMobile ? "#37B7C3" : "white",
    padding: isMobile ? "8px 0" : "12px 0",
    backgroundPosition: "top left",
    backgroundRepeat: "no-repeat",
    position: "fixed" as const,
    top: 0,
    left: 0,
    borderBottom: "1px solid #E5E5E5",
    boxShadow: "0 2px 10px rgba(0, 0, 0, 0.05)",
    zIndex: 1000,
    width: "100%",
    minHeight: isMobile ? "60px" : "auto",
  }),
  button: {
    base: {
      width: "100%",
      borderRadius: "0px",
      padding: "10px",
    },
    premium: {
      gap: "5px",
      cursor: "pointer",
      backgroundColor: "#37B7C3",
      borderRadius: "20px",
      padding: "16px",
      marginRight: "10px",
      fontSize: "14px",
      color: "white",
      height: "24px",
    },
    regular: {
      backgroundColor: "#E59819",
      gap: "5px",
      cursor: "pointer",
      borderRadius: "20px",
      padding: "16px",
      marginRight: "10px",
      fontSize: "14px",
      color: "white",
      height: "24px",
    },
  },
  profileMenu: {
    container: {
      marginRight: "10px",
      maxWidth: "200px",
      whiteSpace: "nowrap" as const,
      overflow: "hidden",
      textOverflow: "ellipsis",
    },
    dropdown: {
      width: "150px",
    },
  },
  link: {
    textDecoration: "none",
    color: "inherit",
    fontSize: "14px",
    fontWeight: 500,
  },
};

// // SVG Components
// const PremiumCrown = () => (
//   <svg
//     width="16"
//     height="16"
//     viewBox="0 0 28 22"
//     fill="none"
//     xmlns="http://www.w3.org/2000/svg"
//     style={{ marginRight: "4px" }}
//   >
//     <path
//       d="M13.9847 15.4328C10.0077 15.4328 6.03066 15.4328 2.05365 15.4406C1.78125 15.4406 1.69564 15.3706 1.64895 15.0982C1.37655 13.2694 1.08858 11.4406 0.808403 9.61184C0.543788 7.91535 0.286956 6.21107 0.0223409 4.5068C-0.0554871 4.03209 0.0690377 3.6819 0.411481 3.464C0.753924 3.23832 1.15863 3.26945 1.54777 3.55738C3.22885 4.8103 4.90994 6.07099 6.59102 7.32391C7.43156 7.95426 8.14758 7.82974 8.73129 6.95037C10.1244 4.85699 11.5253 2.77139 12.9184 0.678016C13.5255 -0.224704 14.4594 -0.224704 15.0665 0.670234C16.4674 2.75583 17.8605 4.84921 19.2536 6.94259C19.8529 7.83752 20.5534 7.95426 21.4172 7.30834C23.0905 6.05543 24.7639 4.80252 26.4372 3.55738C26.9508 3.17606 27.5501 3.25388 27.8614 3.74415C28.0015 3.96983 28.0248 4.21108 27.9781 4.46789C27.6279 6.70134 27.2933 8.92701 26.943 11.1605C26.7407 12.4912 26.5228 13.8219 26.3282 15.1527C26.2893 15.4017 26.1881 15.4406 25.9702 15.4406C23.8299 15.4328 21.6896 15.4328 19.5494 15.4328C17.6971 15.4328 15.837 15.4328 13.9847 15.4328Z"
//       fill="white"
//     />
//     <path
//       d="M14.0087 17.4795C17.9857 17.4795 21.9627 17.4872 25.9397 17.4717C26.2432 17.4717 26.3055 17.5573 26.3055 17.8452C26.2821 18.6001 26.2977 19.3549 26.2977 20.1176C26.2977 21.0359 25.7685 21.5651 24.8501 21.5651C17.6199 21.5651 10.3819 21.5651 3.15166 21.5651C2.2333 21.5651 1.70407 21.0437 1.70407 20.1254C1.70407 19.3472 1.71963 18.569 1.69628 17.7985C1.6885 17.5339 1.77411 17.4795 2.02316 17.4795C6.01573 17.4795 10.0083 17.4795 14.0087 17.4795Z"
//       fill="white"
//     />
//   </svg>
// );

// const Instructor = () => (
//   <svg
//     width="16"
//     height="16"
//     viewBox="0 0 26 26"
//     fill="none"
//     xmlns="http://www.w3.org/2000/svg"
//   >
//     <path
//       d="M25.2383 24.4766H21.8867V17.0117C21.8867 16.1717 21.2033 15.4883 20.3633 15.4883H19.2308C18.9323 14.1863 18.3382 13.0684 17.491 12.2286C16.9578 11.7 16.3379 11.2961 15.6504 11.0244C17.124 9.75289 18.0857 7.55488 18.0857 5.56898C18.0857 3.76685 17.5844 2.35244 16.5958 1.3652C15.7015 0.472063 14.4581 0 13 0C11.542 0 10.2985 0.472063 9.40418 1.3652C8.41552 2.35244 7.91421 3.76685 7.91421 5.56898C7.91421 7.55483 8.87595 9.75289 10.3496 11.0244C9.6621 11.2961 9.04216 11.7 8.50896 12.2286C7.66177 13.0684 7.06768 14.1864 6.76929 15.4883H5.63672C4.7967 15.4883 4.11328 16.1717 4.11328 17.0117V24.4766H0.761719C0.341047 24.4766 0 24.8176 0 25.2383C0 25.659 0.341047 26 0.761719 26H25.2383C25.659 26 26 25.659 26 25.2383C26 24.8176 25.659 24.4766 25.2383 24.4766Z"
//       fill="white"
//     />
//   </svg>
// );

// Components
const Logo: React.FC<LogoProps> = ({ isMobile }) => (
  <Link to="/">
    <img
      src={
        isMobile
          ? "/assets/img/authentication/white-logo.png"
          : "/assets/img/general/logo.svg"
      }
      alt="logo"
      style={{
        width: isMobile ? "120px" : "auto",
        maxHeight: isMobile ? "30px" : "40px",
        objectFit: "contain",
      }}
    />
  </Link>
);

const MobileButtons: React.FC<MobileButtonsProps> = ({
  setActiveMobileMenu,
}) => (
  <div className="d-flex align-items-center" style={{ gap: "15px" }}>
    <button
      className="text-white d-flex align-items-center justify-content-center"
      onClick={() => setActiveMobileMenu(true)}
      style={{
        background: "rgba(255, 255, 255, 0.2)",
        border: "none",
        borderRadius: "8px",
        padding: "8px",
        width: "36px",
        height: "36px",
      }}
    >
      <Menu size={20} />
    </button>
  </div>
);
const DesktopMenu: React.FC = () => {
  const { data: currentUser, isLoading } = useCurrentUserQuery();
  const { keycloak } = useKeycloak();
  const [isOnProfile, setIsOnProfile] = useState<boolean>(false);
  const navigate = useNavigate();
  const queryClient = useQueryClient();

  const handleLogout = () => {
    const redirectUri = `${window.location.origin}/`;
    queryClient.clear();
    keycloak.logout({ redirectUri: redirectUri });
  };

  const basePath = "/org/dashboard";

  const handleNavigation = (link: string) => {
    if ((link === "home" || link === "patients" ) && currentUser?.is_superuser) {
      window.location.href = `${basePath}/analytics`;
    } else {
      window.location.href = `/${link}`;
    }
  };

  return (
    <nav className="menu__nav text-dark-1 d-flex justify-content-between align-items-center w-100">
      <div className="d-flex" style={{ gap: "20px" }}>
        {MENU_ITEMS.map((item, index) => (
          item.disabled ? (
            <div
              key={index}
              className="menu__nav-item px-2"
              style={{
                ...styles.link,
                position: "relative",
                padding: "8px 4px",
                transition: "all 0.3s ease",
                opacity: 0.5,
                cursor: "not-allowed",
              }}
            >
              {item.title}
            </div>
          ) : (
            <Link
              key={index}
              to={`/${item.link}`}
              className="menu__nav-item px-2"
              style={{
                ...styles.link,
                position: "relative",
                padding: "8px 4px",
                transition: "all 0.3s ease",
              }}
              onClick={(e) => {
                e.preventDefault();
                e.stopPropagation();
                handleNavigation(item.link);
              }}
            >
              {item.title}
              <span
                style={{
                  position: "absolute",
                  bottom: "0",
                  left: "0",
                  width: "0",
                  height: "2px",
                  backgroundColor: "#37B7C3",
                  transition: "width 0.3s ease",
                }}
                className="hover-line"
              />
            </Link>
          )
        ))}
      </div>
      <div className="d-flex align-items-center">
        {currentUser ? (
          <div className="d-flex align-items-center position-relative">
            {currentUser.user_type === 'organization' && (
              <>
                <SystemNotificationBell />
                <NotificationBell />
              </>
            )}
            <div
              className="avatar-circle"
              style={{
                width: "40px",
                height: "40px",
                borderRadius: "50%",
                backgroundColor: "#37B7C3",
                display: "flex",
                alignItems: "center",
                justifyContent: "center",
                color: "white",
                fontWeight: "bold",
                marginRight: "10px",
                cursor: "pointer",
                boxShadow: "0 2px 8px rgba(55, 183, 195, 0.3)",
                border: "2px solid #fff"
              }}
              onClick={() => setIsOnProfile(!isOnProfile)}
            >
              {currentUser.first_name ? currentUser.first_name.charAt(0).toUpperCase() : "U"}
            </div>
            <span
              style={{
                fontWeight: "500",
                cursor: "pointer",
                transition: "color 0.2s ease"
              }}
              onClick={() => setIsOnProfile(!isOnProfile)}
            >
              {currentUser.first_name || "User"}
            </span>

            {isOnProfile && (
              <div
                style={{
                  position: "absolute",
                  top: "50px",
                  right: "0",
                  backgroundColor: "white",
                  borderRadius: "8px",
                  boxShadow: "0 4px 12px rgba(0, 0, 0, 0.1)",
                  width: "200px",
                  zIndex: 100,
                  padding: "10px 0",
                  animation: "fadeIn 0.2s ease-in-out"
                }}
              >
                <div
                  style={{
                    padding: "10px 15px",
                    cursor: "pointer",
                    transition: "background-color 0.2s ease",
                    borderRadius: "4px",
                    margin: "0 5px"
                  }}
                  onMouseOver={(e) => e.currentTarget.style.backgroundColor = "#f5f5f5"}
                  onMouseOut={(e) => e.currentTarget.style.backgroundColor = "transparent"}
                  onClick={() => {
                    navigate(basePath);
                    setIsOnProfile(false);
                  }}
                >
                  Dashboard
                </div>
                <div
                  style={{
                    padding: "10px 15px",
                    cursor: "pointer",
                    transition: "background-color 0.2s ease",
                    color: "#e53e3e",
                    borderRadius: "4px",
                    margin: "0 5px"
                  }}
                  onMouseOver={(e) => e.currentTarget.style.backgroundColor = "#fff5f5"}
                  onMouseOut={(e) => e.currentTarget.style.backgroundColor = "transparent"}
                  onClick={() => {
                    handleLogout();
                    setIsOnProfile(false);
                  }}
                >
                  Logout
                </div>
              </div>
            )}
          </div>
        ) : isLoading ? (
          <Preloader />
        ) : (
          <button
            onClick={() => keycloak.login({ redirectUri: `${window.location.origin}/home` })}
            className="button text-white btn-nurtify"
            style={{
              display: "flex",
              alignItems: "center",
              justifyContent: "center",
              height: "40px",
              minWidth: "100px",
              transition: "all 0.3s ease",
              borderRadius: "8px",
              backgroundColor: "#37B7C3",
              boxShadow: "0 2px 8px rgba(55, 183, 195, 0.3)",
              fontWeight: "500",
              border: "none",
              cursor: "pointer"
            }}
            onMouseOver={(e) => e.currentTarget.style.backgroundColor = "#2aa0ac"}
            onMouseOut={(e) => e.currentTarget.style.backgroundColor = "#37B7C3"}
          >
            Login
          </button>
        )}
      </div>
    </nav>
  );
};

// const ProfileMenu: React.FC<ProfileMenuProps> = ({
//   user,
//   handleLogout,
//   isOnProfile,
//   setIsOnProfile,
//   navigate,
// }) => {
//   const formatName = (
//     firstName: string = "",
//     lastName: string = ""
//   ): string => {
//     const fullName = `${firstName} ${lastName}`.trim();
//     return fullName.length > 20
//       ? `${fullName.substring(0, 17)}...`
//       : fullName || "User";
//   };

//   return (
//     <div className="relative d-flex items-center ml-10">
//       {/* {user?.is_premium ? (
//         <div
//           className="d-flex align-items-center justify-content-center"
//           style={styles.button.premium}
//         >
//           <Instructor />
//           <span>Apply to become Instructor</span>
//         </div>
//       ) : (
//         <div
//           className="d-flex align-items-center justify-content-center"
//           style={styles.button.regular}
//         >
//           <PremiumCrown />
//           <span>Upgrade to premium</span>
//         </div>
//       )} */}

//       <div
//         className="user-name d-flex align-items-center"
//         style={styles.profileMenu.container}
//       >
//         <span>{formatName(user?.first_name, user?.last_name)}</span>
//       </div>

//       <a
//         href="#"
//         data-el-toggle=".js-profile-toggle"
//         onClick={() => setIsOnProfile(!isOnProfile)}
//       >
//         <img
//           className="size-40 rounded-full"
//           src={
//             user?.image
//               ? `https://api.dev.nurtify.co.uk/${user?.image}`
//               : "https://encrypted-tbn0.gstatic.com/images?q=tbn:ANd9GcTni2_UQfY9kvI719Jrf5DInG1KNr0Qny_b5A&s"
//           }
//           alt="Profile"
//         />
//       </a>

//       <div
//         className={`toggle-element js-profile-toggle ${
//           isOnProfile ? "-is-el-visible" : ""
//         }`}
//       >
//         <div
//           className="toggle-bottom -profile bg-white shadow-4 border-light rounded-8 mt-10"
//           style={styles.profileMenu.dropdown}
//         >
//           {PROFILE_MENU_ITEMS.map((item, index) => (
//             <button
//               key={index}
//               className="btn btn-link"
//               style={styles.button.base}
//               onClick={() => navigate(item.href)}
//             >
//               {item.label}
//             </button>
//           ))}
//           <button
//             className="btn btn-link"
//             style={styles.button.base}
//             onClick={handleLogout}
//           >
//             Logout
//           </button>
//         </div>
//       </div>
//     </div>
//   );
// };

// Removed unused MobileIcon component

// Main Component
const Header = () => {
  const { data: currentUser } = useCurrentUserQuery();
  const [activeMobileMenu, setActiveMobileMenu] = useState(false);
  const [windowWidth, setWindowWidth] = useState(window.innerWidth);
  // const [isOnProfile, setIsOnProfile] = useState(false);
  const { keycloak } = useKeycloak();

  useEffect(() => {
    const handleResize = () => setWindowWidth(window.innerWidth);
    window.addEventListener("resize", handleResize);
    return () => window.removeEventListener("resize", handleResize);
  }, []);

  const isMobile = windowWidth < 768;

  return (
    <header className="header -base js-header" style={styles.header(isMobile)}>
      <div className={`header__container ${isMobile ? 'mobile-container' : ''}`}>
        {isMobile ? (
          // Mobile Layout
          <div className="d-flex align-items-center justify-content-between w-100">
            <div className="header__logo">
              <Logo isMobile={isMobile} />
            </div>
            <div className="d-flex align-items-center" style={{ gap: "8px" }}>
              {!currentUser && (
                <button
                  onClick={() => keycloak.login({ redirectUri: `${window.location.origin}/home` })}
                  style={{
                    color: "white",
                    fontWeight: 600,
                    fontSize: "12px",
                    textDecoration: "none",
                    background: "rgba(255, 255, 255, 0.2)",
                    padding: "6px 12px",
                    borderRadius: "20px",
                    border: "none",
                    cursor: "pointer",
                    whiteSpace: "nowrap"
                  }}
                >
                  Login
                </button>
              )}
              <MobileButtons setActiveMobileMenu={setActiveMobileMenu} />
            </div>
            <MobileMenu
              activeMobileMenu={activeMobileMenu}
              setActiveMobileMenu={setActiveMobileMenu}
            />
          </div>
        ) : (
          // Desktop Layout
          <div className="row justify-content-between align-items-center">
            <div className="col-auto d-flex align-items-center">
              <div className="header-left px-4">
                <div className="header__logo">
                  <Logo isMobile={isMobile} />
                </div>
              </div>
            </div>
            <div className="col-auto flex-grow-1">
              <div className="header__menu">
                <DesktopMenu />
              </div>
            </div>
          </div>
        )}
      </div>

      <style>
        {`
          @media (min-width: 768px) {
            .menu__nav-item:hover .hover-line {
              width: 100%;
            }

            .btn-nurtify:hover {
              transform: translateY(-2px);
              box-shadow: 0 4px 12px rgba(55, 183, 195, 0.3);
            }
          }

          @media (max-width: 767px) {
            .header__container {
              padding: 8px 15px !important;
            }
            
            .header-left {
              flex: 1;
            }
            
            .header-right {
              gap: 8px !important;
            }
            
            .header-right button {
              white-space: nowrap;
              min-width: auto !important;
              padding: 6px 10px !important;
              font-size: 12px !important;
            }
          }

          @media (max-width: 480px) {
            .header__container {
              padding: 6px 10px !important;
            }
            
            .header-right button {
              padding: 4px 8px !important;
              font-size: 11px !important;
            }
          }
        `}
      </style>
      <NotificationTester />
    </header>
  );
};

export default Header;

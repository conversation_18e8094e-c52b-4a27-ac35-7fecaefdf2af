/* Patient Documentation Page */
.documentation-page {
  background-color: #f8fafc;
  min-height: calc(100vh - 80px);
  padding: 40px 0;
  position: relative;
}

.documentation-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
  animation: fadeIn 0.5s ease-in-out;
}

@keyframes fadeIn {
  from { opacity: 0; transform: translateY(20px); }
  to { opacity: 1; transform: translateY(0); }
}

/* Header */
.documentation-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
  position: sticky;
  top: 0;
  z-index: 10;
  background-color: #f8fafc;
  padding: 20px 0;
  border-bottom: 1px solid rgba(55, 183, 195, 0.1);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.03);
  transition: all 0.3s ease;
}

.documentation-title {
  display: flex;
  align-items: center;
  gap: 12px;
}

.documentation-title h1 {
  font-size: 28px;
  font-weight: 600;
  color: var(--color-dark-1);
  margin: 0;
  position: relative;
}

.documentation-title h1::after {
  content: '';
  position: absolute;
  bottom: -6px;
  left: 0;
  width: 40px;
  height: 3px;
  background-color: var(--color-purple-1);
  border-radius: 2px;
  transition: width 0.3s ease;
}

.documentation-title:hover h1::after {
  width: 100%;
}

.selected-forms-counter {
  display: flex;
  align-items: center;
  gap: 10px;
  background-color: #e6f7ff;
  padding: 8px 16px;
  border-radius: 8px;
  border: 1px solid #91d5ff;
  box-shadow: 0 2px 8px rgba(24, 144, 255, 0.1);
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% { box-shadow: 0 0 0 0 rgba(24, 144, 255, 0.4); }
  70% { box-shadow: 0 0 0 10px rgba(24, 144, 255, 0); }
  100% { box-shadow: 0 0 0 0 rgba(24, 144, 255, 0); }
}

.selected-forms-counter span {
  font-weight: 500;
  color: #1890ff;
}

/* Alert Styles */
.patient-status-alerts {
  margin: 24px 0;
  animation: slideIn 0.5s ease-in-out;
}

@keyframes slideIn {
  from { opacity: 0; transform: translateX(-20px); }
  to { opacity: 1; transform: translateX(0); }
}

.alert {
  border-radius: 10px;
  padding: 15px 20px;
  margin-bottom: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  display: flex;
  align-items: center;
  gap: 12px;
  transition: all 0.3s ease;
  transform: translateZ(0);
  backface-visibility: hidden;
  perspective: 1000px;
}

.alert:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.alert-icon {
  width: 36px;
  height: 36px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

.alert:hover .alert-icon {
  transform: scale(1.1);
}

.alert-info {
  background-color: #e6f7ff;
  border: 1px solid #91d5ff;
  color: #1890ff;
  position: relative;
  overflow: hidden;
}

.alert-info::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(to right, rgba(24, 144, 255, 0.05), transparent);
  transform: translateX(-100%);
  transition: transform 0.5s ease;
}

.alert-info:hover::before {
  transform: translateX(100%);
}

.alert-info .alert-icon {
  background-color: rgba(24, 144, 255, 0.1);
}

.alert-warning {
  background-color: #fff7e6;
  border: 1px solid #ffbb96;
  color: #fa8c16;
  position: relative;
  overflow: hidden;
}

.alert-warning::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(to right, rgba(250, 140, 22, 0.05), transparent);
  transform: translateX(-100%);
  transition: transform 0.5s ease;
}

.alert-warning:hover::before {
  transform: translateX(100%);
}

.alert-warning .alert-icon {
  background-color: rgba(250, 140, 22, 0.1);
}

.alert-success {
  background-color: #f6ffed;
  border: 1px solid #b7eb8f;
  color: #52c41a;
  animation: slideInFromTop 0.5s ease;
}

@keyframes slideInFromTop {
  from { opacity: 0; transform: translateY(-20px); }
  to { opacity: 1; transform: translateY(0); }
}

.alert-success .alert-icon {
  background-color: rgba(82, 196, 26, 0.1);
}

.alert-danger {
  background-color: #fff2f0;
  border: 1px solid #ffccc7;
  color: #f5222d;
  animation: shake 0.5s ease;
}

@keyframes shake {
  0%, 100% { transform: translateX(0); }
  10%, 30%, 50%, 70%, 90% { transform: translateX(-5px); }
  20%, 40%, 60%, 80% { transform: translateX(5px); }
}

.alert-danger .alert-icon {
  background-color: rgba(245, 34, 45, 0.1);
}

/* Forms Grid */
.forms-grid {
  margin-top: 30px;
  min-height: 300px;
  position: relative;
}

.forms-grid-content {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 24px;
}

.form-card-wrapper {
  height: 100%;
  opacity: 0;
  transform: translateY(20px);
  animation: cardAppear 0.5s ease forwards;
}

@keyframes cardAppear {
  to { opacity: 1; transform: translateY(0); }
}

/* Staggered animation for cards */
.form-card-wrapper:nth-child(1) { animation-delay: 0.1s; }
.form-card-wrapper:nth-child(2) { animation-delay: 0.15s; }
.form-card-wrapper:nth-child(3) { animation-delay: 0.2s; }
.form-card-wrapper:nth-child(4) { animation-delay: 0.25s; }
.form-card-wrapper:nth-child(5) { animation-delay: 0.3s; }
.form-card-wrapper:nth-child(6) { animation-delay: 0.35s; }
.form-card-wrapper:nth-child(7) { animation-delay: 0.4s; }
.form-card-wrapper:nth-child(8) { animation-delay: 0.45s; }

/* Loading State */
.loading-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 0;
  background-color: white;
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.05);
  animation: fadeIn 0.5s ease;
}

.spinner {
  width: 40px;
  height: 40px;
  border: 3px solid rgba(55, 183, 195, 0.1);
  border-radius: 50%;
  border-top-color: var(--color-purple-1);
  animation: spin 1s ease-in-out infinite;
  margin-bottom: 16px;
  box-shadow: 0 0 10px rgba(55, 183, 195, 0.2);
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

.loading-state p {
  font-size: 16px;
  color: var(--color-light-1);
  margin: 0;
  position: relative;
}

.loading-state p::after {
  content: '...';
  position: absolute;
  animation: ellipsis 1.5s infinite;
  width: 24px;
  text-align: left;
}

@keyframes ellipsis {
  0% { content: '.'; }
  33% { content: '..'; }
  66% { content: '...'; }
}

/* Empty State */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 0;
  background-color: rgba(55, 183, 195, 0.05);
  border-radius: 12px;
  border: 1px dashed rgba(55, 183, 195, 0.2);
  text-align: center;
  animation: fadeIn 0.5s ease;
  transition: all 0.3s ease;
}

.empty-state:hover {
  background-color: rgba(55, 183, 195, 0.08);
  border-color: rgba(55, 183, 195, 0.3);
  transform: scale(1.01);
}

.empty-state-icon {
  margin-bottom: 16px;
  transition: all 0.3s ease;
}

.empty-state:hover .empty-state-icon {
  transform: scale(1.1) rotate(5deg);
}

.empty-state h3 {
  font-size: 18px;
  font-weight: 600;
  color: var(--color-dark-1);
  margin: 0 0 8px 0;
}

.empty-state p {
  font-size: 14px;
  color: var(--color-light-1);
  max-width: 400px;
  margin: 0 auto;
  line-height: 1.6;
}

/* Floating Action Button */
.floating-action-button {
  position: fixed;
  bottom: 30px;
  right: 30px;
  z-index: 100;
  filter: drop-shadow(0 4px 20px rgba(0, 0, 0, 0.15));
  transition: all 0.3s ease;
}

.floating-action-button:hover {
  filter: drop-shadow(0 8px 30px rgba(0, 0, 0, 0.2));
  transform: translateY(-3px);
}

.add-button {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12px;
  background: linear-gradient(135deg, var(--color-purple-1), #2b8d97);
  color: white;
  border: none;
  border-radius: 12px;
  padding: 14px 28px;
  font-size: 16px;
  font-weight: bold;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
  letter-spacing: 0.5px;
  text-transform: uppercase;
  box-shadow: 0 4px 15px rgba(55, 183, 195, 0.3);
}

.add-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(to right, rgba(255, 255, 255, 0.1), transparent);
  transform: translateX(-100%);
  transition: transform 0.5s ease;
}

.add-button:hover::before {
  transform: translateX(100%);
}

.add-button::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 3px;
  background: rgba(255, 255, 255, 0.5);
  transform: scaleX(0);
  transform-origin: right;
  transition: transform 0.5s ease;
}

.add-button:hover::after {
  transform: scaleX(1);
  transform-origin: left;
}

.add-button:hover {
  background: linear-gradient(135deg, #37b7c3, #1a6e76);
  box-shadow: 0 6px 20px rgba(55, 183, 195, 0.4);
}

.add-button:active {
  transform: translateY(2px);
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2) inset;
}

.add-button.disabled {
  background: linear-gradient(135deg, #cccccc, #999999);
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
  opacity: 0.7;
}

.add-button svg {
  filter: drop-shadow(0 2px 3px rgba(0, 0, 0, 0.2));
  transition: all 0.3s ease;
}

.add-button:hover svg {
  transform: rotate(90deg);
}

/* Keyboard Shortcuts Tooltip */
.documentation-container::after {
  content: 'Keyboard shortcuts: Enter to preview, Space to select';
  position: fixed;
  bottom: 10px;
  left: 50%;
  transform: translateX(-50%);
  background-color: rgba(0, 0, 0, 0.7);
  color: white;
  padding: 8px 16px;
  border-radius: 4px;
  font-size: 12px;
  opacity: 0;
  transition: opacity 0.3s ease;
  pointer-events: none;
  z-index: 1000;
}

.documentation-container:hover::after {
  opacity: 1;
}

/* Responsive Adjustments */
@media (max-width: 1200px) {
  .forms-grid-content {
    grid-template-columns: repeat(3, 1fr);
  }
}

@media (max-width: 992px) {
  .forms-grid-content {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (max-width: 768px) {
  .documentation-page {
    padding: 20px 0;
  }
  
  .documentation-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 16px;
  }
  
  .documentation-title h1 {
    font-size: 24px;
  }
  
  .forms-grid-content {
    grid-template-columns: 1fr;
  }
  
  .add-button {
    padding: 10px 16px;
    font-size: 14px;
  }
}

import React, { useState } from 'react';
import { 
  X, 
  Heart, 
  Thermometer, 
  Activity, 
  Droplets, 
  Scale, 
  Ruler, 
  Eye, 
  Wind,
  Brain,
  Save
} from 'lucide-react';
import { CreateVitalSignData } from '../../services/api/vital-signs.service';
import './AddVitalSignModal.css';

interface AddVitalSignModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSubmit: (data: CreateVitalSignData) => void;
  patientId: string;
  loading?: boolean;
}

const AddVitalSignModal: React.FC<AddVitalSignModalProps> = ({
  isOpen,
  onClose,
  onSubmit,
  patientId,
  loading = false
}) => {
  const [formData, setFormData] = useState<CreateVitalSignData>({
    patient_id: patientId,
    temperature: undefined,
    temp_unit: 'C',
    temp_location: 'oral',
    heart_rate: undefined,
    systolic_bp: undefined,
    diastolic_bp: undefined,
    respiratory_rate: undefined,
    oxygen_saturation: undefined,
    consciousness_level: 'A',
    supplemental_oxygen: false,
    blood_sugar: undefined,
    blood_sugar_unit: 'mg/dL',
    height: undefined,
    height_unit: 'cm',
    weight: undefined,
    weight_unit: 'kg',
    notes: ''
  });

  const [errors, setErrors] = useState<Record<string, string>>({});

  const handleInputChange = (field: keyof CreateVitalSignData, value: any) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
    
    // Clear error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({
        ...prev,
        [field]: ''
      }));
    }
  };

  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {};

    // At least one vital sign should be recorded
    const hasVitalSigns = formData.temperature !== undefined ||
                         formData.heart_rate !== undefined ||
                         formData.systolic_bp !== undefined ||
                         formData.diastolic_bp !== undefined ||
                         formData.respiratory_rate !== undefined ||
                         formData.oxygen_saturation !== undefined ||
                         formData.blood_sugar !== undefined ||
                         formData.height !== undefined ||
                         formData.weight !== undefined;

    if (!hasVitalSigns) {
      newErrors.general = 'At least one vital sign must be recorded';
    }

    // Validate temperature
    if (formData.temperature !== undefined) {
      if (formData.temperature < 20 || formData.temperature > 45) {
        newErrors.temperature = 'Temperature must be between 20-45°C';
      }
    }

    // Validate heart rate
    if (formData.heart_rate !== undefined) {
      if (formData.heart_rate < 30 || formData.heart_rate > 300) {
        newErrors.heart_rate = 'Heart rate must be between 30-300 bpm';
      }
    }

    // Validate blood pressure
    if (formData.systolic_bp !== undefined) {
      if (formData.systolic_bp < 50 || formData.systolic_bp > 300) {
        newErrors.systolic_bp = 'Systolic BP must be between 50-300 mmHg';
      }
    }

    if (formData.diastolic_bp !== undefined) {
      if (formData.diastolic_bp < 30 || formData.diastolic_bp > 200) {
        newErrors.diastolic_bp = 'Diastolic BP must be between 30-200 mmHg';
      }
    }

    if (formData.systolic_bp !== undefined && formData.diastolic_bp !== undefined) {
      if (formData.systolic_bp <= formData.diastolic_bp) {
        newErrors.diastolic_bp = 'Diastolic BP must be lower than systolic BP';
      }
    }

    // Validate respiratory rate
    if (formData.respiratory_rate !== undefined) {
      if (formData.respiratory_rate < 5 || formData.respiratory_rate > 60) {
        newErrors.respiratory_rate = 'Respiratory rate must be between 5-60/min';
      }
    }

    // Validate oxygen saturation
    if (formData.oxygen_saturation !== undefined) {
      if (formData.oxygen_saturation < 70 || formData.oxygen_saturation > 100) {
        newErrors.oxygen_saturation = 'Oxygen saturation must be between 70-100%';
      }
    }

    // Validate blood sugar
    if (formData.blood_sugar !== undefined) {
      if (formData.blood_sugar < 0.1 || formData.blood_sugar > 1000) {
        newErrors.blood_sugar = 'Blood sugar must be between 0.1-1000';
      }
    }

    // Validate height
    if (formData.height !== undefined) {
      if (formData.height < 20 || formData.height > 300) {
        newErrors.height = 'Height must be between 20-300 cm';
      }
    }

    // Validate weight
    if (formData.weight !== undefined) {
      if (formData.weight < 0.1 || formData.weight > 500) {
        newErrors.weight = 'Weight must be between 0.1-500 kg';
      }
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    if (validateForm()) {
      // Filter out undefined values
      const cleanData = Object.fromEntries(
        Object.entries(formData).filter(([, value]) => value !== undefined && value !== '')
      ) as CreateVitalSignData;
      
      onSubmit(cleanData);
    }
  };

  const handleClose = () => {
    setFormData({
      patient_id: patientId,
      temperature: undefined,
      temp_unit: 'C',
      temp_location: 'oral',
      heart_rate: undefined,
      systolic_bp: undefined,
      diastolic_bp: undefined,
      respiratory_rate: undefined,
      oxygen_saturation: undefined,
      consciousness_level: 'A',
      supplemental_oxygen: false,
      blood_sugar: undefined,
      blood_sugar_unit: 'mg/dL',
      height: undefined,
      height_unit: 'cm',
      weight: undefined,
      weight_unit: 'kg',
      notes: ''
    });
    setErrors({});
    onClose();
  };

  if (!isOpen) return null;

  return (
    <div className="modal-overlay" onClick={handleClose}>
      <div className="modal-content" onClick={(e) => e.stopPropagation()}>
        <div className="vital-modal-header">
          <div className="vital-modal-title">
            <Activity className="vital-modal-icon" />
            <h2>Add New Vital Signs</h2>
          </div>
          <button className="vital-modal-close" onClick={handleClose}>
            <X size={20} />
          </button>
        </div>

        <form onSubmit={handleSubmit} className="vital-sign-form">
          {errors.general && (
            <div className="error-message general-error">{errors.general}</div>
          )}

          <div className="form-section">
            <h3><Thermometer size={18} /> Temperature</h3>
            <div className="form-row">
              <div className="form-group">
                <label htmlFor="temperature"><Thermometer size={16} /> Temperature</label>
                <input
                  type="number"
                  id="temperature"
                  step="0.1"
                  placeholder="37.2"
                  value={formData.temperature || ''}
                  onChange={(e) => handleInputChange('temperature', e.target.value ? parseFloat(e.target.value) : undefined)}
                  className={errors.temperature ? 'error' : ''}
                />
                {errors.temperature && <span className="error-text">{errors.temperature}</span>}
              </div>
              <div className="form-group">
                <label htmlFor="temp_unit">Unit</label>
                <select
                  id="temp_unit"
                  value={formData.temp_unit}
                  onChange={(e) => handleInputChange('temp_unit', e.target.value as 'C' | 'F')}
                >
                  <option value="C">°C</option>
                  <option value="F">°F</option>
                </select>
              </div>
              <div className="form-group">
                <label htmlFor="temp_location">Location</label>
                <select
                  id="temp_location"
                  value={formData.temp_location}
                  onChange={(e) => handleInputChange('temp_location', e.target.value)}
                >
                  <option value="oral">Oral</option>
                  <option value="axillary">Axillary</option>
                  <option value="rectal">Rectal</option>
                  <option value="tympanic">Tympanic</option>
                  <option value="temporal">Temporal</option>
                  <option value="other">Other</option>
                </select>
              </div>
            </div>
          </div>

          <div className="form-section">
            <h3><Heart size={18} /> Cardiovascular</h3>
            <div className="form-row">
              <div className="form-group">
                <label htmlFor="heart_rate"><Heart size={16} /> Heart Rate (bpm)</label>
                <input
                  type="number"
                  id="heart_rate"
                  placeholder="72"
                  value={formData.heart_rate || ''}
                  onChange={(e) => handleInputChange('heart_rate', e.target.value ? parseInt(e.target.value) : undefined)}
                  className={errors.heart_rate ? 'error' : ''}
                />
                {errors.heart_rate && <span className="error-text">{errors.heart_rate}</span>}
              </div>
              <div className="form-group">
                <label htmlFor="systolic_bp">Systolic BP (mmHg)</label>
                <input
                  type="number"
                  id="systolic_bp"
                  placeholder="120"
                  value={formData.systolic_bp || ''}
                  onChange={(e) => handleInputChange('systolic_bp', e.target.value ? parseInt(e.target.value) : undefined)}
                  className={errors.systolic_bp ? 'error' : ''}
                />
                {errors.systolic_bp && <span className="error-text">{errors.systolic_bp}</span>}
              </div>
              <div className="form-group">
                <label htmlFor="diastolic_bp">Diastolic BP (mmHg)</label>
                <input
                  type="number"
                  id="diastolic_bp"
                  placeholder="80"
                  value={formData.diastolic_bp || ''}
                  onChange={(e) => handleInputChange('diastolic_bp', e.target.value ? parseInt(e.target.value) : undefined)}
                  className={errors.diastolic_bp ? 'error' : ''}
                />
                {errors.diastolic_bp && <span className="error-text">{errors.diastolic_bp}</span>}
              </div>
            </div>
          </div>

          <div className="form-section">
            <h3><Wind size={18} /> Respiratory</h3>
            <div className="form-row">
              <div className="form-group">
                <label htmlFor="respiratory_rate"><Wind size={16} /> Respiratory Rate (/min)</label>
                <input
                  type="number"
                  id="respiratory_rate"
                  placeholder="16"
                  value={formData.respiratory_rate || ''}
                  onChange={(e) => handleInputChange('respiratory_rate', e.target.value ? parseInt(e.target.value) : undefined)}
                  className={errors.respiratory_rate ? 'error' : ''}
                />
                {errors.respiratory_rate && <span className="error-text">{errors.respiratory_rate}</span>}
              </div>
              <div className="form-group">
                <label htmlFor="oxygen_saturation">Oxygen Saturation (%)</label>
                <input
                  type="number"
                  id="oxygen_saturation"
                  placeholder="98"
                  value={formData.oxygen_saturation || ''}
                  onChange={(e) => handleInputChange('oxygen_saturation', e.target.value ? parseInt(e.target.value) : undefined)}
                  className={errors.oxygen_saturation ? 'error' : ''}
                />
                {errors.oxygen_saturation && <span className="error-text">{errors.oxygen_saturation}</span>}
              </div>
              <div className="form-group">
                <label htmlFor="supplemental_oxygen">Supplemental Oxygen</label>
                <select
                  id="supplemental_oxygen"
                  value={formData.supplemental_oxygen ? 'true' : 'false'}
                  onChange={(e) => handleInputChange('supplemental_oxygen', e.target.value === 'true')}
                >
                  <option value="false">No</option>
                  <option value="true">Yes</option>
                </select>
              </div>
            </div>
          </div>

          <div className="form-section">
            <h3><Scale size={18} /> Other Measurements</h3>
            <div className="form-row">
              <div className="form-group">
                <label htmlFor="blood_sugar"><Droplets size={16} /> Blood Sugar</label>
                <div className="blood-sugar-input-group">
                  <input
                    type="number"
                    id="blood_sugar"
                    step="0.1"
                    placeholder="95"
                    value={formData.blood_sugar || ''}
                    onChange={(e) => handleInputChange('blood_sugar', e.target.value ? parseFloat(e.target.value) : undefined)}
                    className={errors.blood_sugar ? 'error' : ''}
                  />
                  <select
                    value={formData.blood_sugar_unit}
                    onChange={(e) => handleInputChange('blood_sugar_unit', e.target.value as 'mg/dL' | 'mmol/L')}
                  >
                    <option value="mg/dL">mg/dL</option>
                    <option value="mmol/L">mmol/L</option>
                  </select>
                </div>
                {errors.blood_sugar && <span className="error-text">{errors.blood_sugar}</span>}
              </div>
              <div className="form-group">
                <label htmlFor="height"><Ruler size={16} /> Height</label>
                <div className="height-input-group">
                  <input
                    type="number"
                    id="height"
                    step="0.1"
                    placeholder="170"
                    value={formData.height || ''}
                    onChange={(e) => handleInputChange('height', e.target.value ? parseFloat(e.target.value) : undefined)}
                    className={errors.height ? 'error' : ''}
                  />
                  <select
                    value={formData.height_unit}
                    onChange={(e) => handleInputChange('height_unit', e.target.value as 'cm' | 'in')}
                  >
                    <option value="cm">cm</option>
                    <option value="in">in</option>
                  </select>
                </div>
                {errors.height && <span className="error-text">{errors.height}</span>}
              </div>
              <div className="form-group">
                <label htmlFor="weight"><Scale size={16} /> Weight</label>
                <div className="weight-input-group">
                  <input
                    type="number"
                    id="weight"
                    step="0.1"
                    placeholder="70"
                    value={formData.weight || ''}
                    onChange={(e) => handleInputChange('weight', e.target.value ? parseFloat(e.target.value) : undefined)}
                    className={errors.weight ? 'error' : ''}
                  />
                  <select
                    value={formData.weight_unit}
                    onChange={(e) => handleInputChange('weight_unit', e.target.value as 'kg' | 'lbs')}
                  >
                    <option value="kg">kg</option>
                    <option value="lbs">lbs</option>
                  </select>
                </div>
                {errors.weight && <span className="error-text">{errors.weight}</span>}
              </div>
            </div>
          </div>

          <div className="form-section">
            <h3><Brain size={18} /> Consciousness</h3>
            <div className="form-row">
              <div className="form-group">
                <label htmlFor="consciousness_level"><Brain size={16} /> Consciousness Level</label>
                <select
                  id="consciousness_level"
                  value={formData.consciousness_level}
                  onChange={(e) => handleInputChange('consciousness_level', e.target.value as 'A' | 'V' | 'P' | 'U')}
                >
                  <option value="A">Alert</option>
                  <option value="V">Voice</option>
                  <option value="P">Pain</option>
                  <option value="U">Unresponsive</option>
                </select>
              </div>
            </div>
          </div>

          <div className="form-section">
            <div className="form-group">
              <label htmlFor="notes"><Eye size={16} /> Notes</label>
              <textarea
                id="notes"
                placeholder="Additional notes about the vital signs..."
                value={formData.notes}
                onChange={(e) => handleInputChange('notes', e.target.value)}
                rows={3}
              />
            </div>
          </div>

          <div className="modal-actions">
            <button type="button" className="btn btn-secondary" onClick={handleClose}>
              <X size={16} />
              Cancel
            </button>
            <button type="submit" className="btn btn-primary" disabled={loading}>
              <Save size={16} />
              {loading ? 'Saving...' : 'Save Vital Signs'}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default AddVitalSignModal;

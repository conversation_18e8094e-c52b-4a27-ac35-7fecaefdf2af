import { useState, useEffect } from "react";
import "./styles.css";

interface NurtifyCheckBoxProps {
    id?: string;
    label?: string;
    name?: string;
    value?: string;
    checked?: boolean;
    onChange?: (event: React.ChangeEvent<HTMLInputElement>) => void;
    required?: boolean;
    description?: string;
    border?: boolean;
}

const NurtifyCheckBox: React.FC<NurtifyCheckBoxProps> = ({ label, name, value, onChange, checked = false, description, border = true }) => {
    const [isChecked, setIsChecked] = useState(checked);
    
    // Update internal state when checked prop changes
    useEffect(() => {
        setIsChecked(checked);
    }, [checked]);
    
    const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        e.stopPropagation();
        const newValue = e.target.checked;
        setIsChecked(newValue);
        if (onChange) {
            onChange(e);
        }
    };

    return (
        <div
            className={`nurtify-radio custom-checkbox mb-1 ${
                isChecked ? "selected" : ""
            } ${border && ""}`}
            style={{ borderRadius: '4px' }}
        >
            <input
                type="checkbox"
                id={value}
                name={name}
                value={value}
                checked={isChecked}
                onChange={handleChange}
                style={{ borderRadius: '2px', appearance: 'none' }}
            />
            <label htmlFor={value}>{label}</label>
            {description && <small className="text-muted">{description}</small>}
        </div>
    );
};

export default NurtifyCheckBox;

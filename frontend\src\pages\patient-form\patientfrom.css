/* Global Variables */
:root {
  --primary-color: #37B7C3;
  --primary-dark: #2ea2b0;
  --primary-light: #e0f7f9;
  --primary-bg: rgba(55, 183, 195, 0.05);
  --primary-border: rgba(55, 183, 195, 0.2);
  --text-dark: #2c3e50;
  --text-medium: #4a5568;
  --text-light: #6b7280;
  --border-light: #e5e7eb;
  --success-color: #4caf50;
  --warning-color: #ff9800;
  --error-color: #f44336;
  --shadow-sm: 0 2px 5px rgba(0, 0, 0, 0.05);
  --shadow-md: 0 4px 10px rgba(0, 0, 0, 0.08);
  --shadow-lg: 0 8px 20px rgba(0, 0, 0, 0.12);
  --transition-fast: all 0.2s ease;
  --transition-normal: all 0.3s ease;
  --border-radius-sm: 6px;
  --border-radius-md: 10px;
  --border-radius-lg: 16px;
}

/* Back <PERSON><PERSON> Styl<PERSON> */
.back-to-forms-button {
  display: flex;
  align-items: center;
  gap: 10px;
  padding: 10px 18px;
  background-color: white;
  border: 1px solid var(--border-light);
  border-radius: var(--border-radius-md);
  cursor: pointer;
  transition: var(--transition-fast);
  font-weight: 500;
  color: var(--text-medium);
  box-shadow: var(--shadow-sm);
}

.back-to-forms-button:hover {
  background-color: var(--primary-light);
  border-color: var(--primary-border);
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(55, 183, 195, 0.15);
  color: var(--primary-color);
}

.back-to-forms-button:active {
  transform: translateY(0);
}

/* Form Card Styling */
.form-card {
  cursor: pointer;
  transition: var(--transition-normal);
  border-radius: var(--border-radius-lg);
  border: 1px solid var(--border-light);
  background-color: white;
  box-shadow: var(--shadow-sm);
  overflow: hidden;
  position: relative;
}

.form-card:hover {
  transform: translateY(-4px);
  box-shadow: var(--shadow-md);
  border-color: var(--primary-border);
}

.form-card::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 0;
  height: 3px;
  background: linear-gradient(to right, var(--primary-color), var(--primary-dark));
  transition: var(--transition-normal);
}

.form-card:hover::after {
  width: 100%;
}

.form-card.selected {
  border-color: var(--primary-color);
  background-color: var(--primary-bg);
  box-shadow: 0 4px 12px rgba(55, 183, 195, 0.2);
}

.form-card.selected::after {
  width: 100%;
  height: 4px;
}

/* Form Actions Styling */
.form-actions {
  position: absolute;
  top: 15px;
  right: 15px;
  z-index: 2;
  display: flex;
  gap: 8px;
}

.btn-edit-form {
  background: linear-gradient(to right, var(--primary-color), var(--primary-dark));
  color: white;
  border: none;
  border-radius: var(--border-radius-md);
  padding: 6px 12px;
  font-weight: 500;
  font-size: 14px;
  cursor: pointer;
  transition: var(--transition-fast);
  box-shadow: var(--shadow-sm);
  display: flex;
  align-items: center;
  gap: 6px;
}

.btn-edit-form:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(55, 183, 195, 0.3);
  background: linear-gradient(to right, var(--primary-dark), #268891);
}

.btn-edit-form:active {
  transform: translateY(0);
  box-shadow: 0 2px 4px rgba(55, 183, 195, 0.2);
}

/* Form View Container */
.form-view-container {
  position: relative;
  background: white;
  border-radius: var(--border-radius-lg);
  box-shadow: var(--shadow-lg);
  margin-top: 30px;
  transition: var(--transition-normal);
  overflow: hidden;
  padding: 30px;
  border: 1px solid rgba(0, 0, 0, 0.05);
}

.form-view-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30px;
  padding-bottom: 20px;
  border-bottom: 1px solid var(--border-light);
  position: relative;
}

.form-view-header::after {
  content: '';
  position: absolute;
  bottom: -1px;
  left: 0;
  width: 100px;
  height: 3px;
  background: linear-gradient(to right, var(--primary-color), var(--primary-dark));
  border-radius: 3px;
}

.form-view-header h2 {
  font-size: 22px;
  line-height: 1.3;
  font-weight: 600;
  margin: 0;
  color: var(--text-dark);
  position: relative;
  padding-left: 15px;
}

.form-view-header h2::before {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  height: 100%;
  width: 4px;
  background-color: var(--primary-color);
  border-radius: 4px;
}

.form-view-content {
  padding: 30px;
  background-color: #f9fafb;
  border-radius: var(--border-radius-md);
  box-shadow: inset 0 0 10px rgba(0, 0, 0, 0.03);
}

/* Loading State */
.loading-indicator {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px;
}

.loading-indicator .spinner {
  width: 50px;
  height: 50px;
  border: 4px solid rgba(55, 183, 195, 0.2);
  border-radius: 50%;
  border-top-color: var(--primary-color);
  animation: spin 1s ease-in-out infinite;
  margin-bottom: 20px;
  box-shadow: 0 0 15px rgba(55, 183, 195, 0.3);
}

.loading-indicator p {
  color: var(--text-medium);
  font-size: 16px;
  font-weight: 500;
  margin-top: 10px;
  animation: pulse 2s infinite;
}

@keyframes spin {
  to { transform: rotate(360deg); }
}

@keyframes pulse {
  0% { opacity: 0.6; }
  50% { opacity: 1; }
  100% { opacity: 0.6; }
}

/* Error Message */
.error-message {
  display: flex;
  align-items: center;
  gap: 12px;
  background-color: #fee2e2;
  color: #b91c1c;
  padding: 16px 20px;
  border-radius: var(--border-radius-md);
  margin-bottom: 20px;
  border-left: 4px solid #f44336;
  box-shadow: var(--shadow-sm);
  animation: fadeIn 0.3s ease-out;
}

.error-message svg {
  flex-shrink: 0;
  color: #f44336;
}

@keyframes fadeIn {
  from { opacity: 0; transform: translateY(-10px); }
  to { opacity: 1; transform: translateY(0); }
}

/* Empty Forms Container */
.empty-forms-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 80px 0;
  text-align: center;
  color: var(--text-light);
  background-color: #f9fafb;
  border-radius: var(--border-radius-lg);
  border: 1px dashed var(--border-light);
  margin: 20px 0;
}

.empty-forms-container svg {
  color: #d1d5db;
  margin-bottom: 25px;
  opacity: 0.7;
  filter: drop-shadow(0 2px 5px rgba(0, 0, 0, 0.1));
  animation: float 3s ease-in-out infinite;
}

@keyframes float {
  0% { transform: translateY(0px); }
  50% { transform: translateY(-10px); }
  100% { transform: translateY(0px); }
}

.empty-forms-container h4 {
  margin-top: 16px;
  margin-bottom: 12px;
  color: var(--text-dark);
  font-size: 20px;
  font-weight: 600;
}

.empty-forms-container p {
  color: var(--text-light);
  max-width: 350px;
  line-height: 1.6;
  font-size: 15px;
}

/* Button Styling */
.btn-submit-forms {
  transition: var(--transition-normal);
  background: linear-gradient(to right, var(--primary-color), var(--primary-dark)) !important;
  position: relative;
  overflow: hidden;
}

.btn-submit-forms::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: var(--transition-normal);
}

.btn-submit-forms:hover::before {
  left: 100%;
}

.btn-submit-forms:hover {
  background: linear-gradient(to right, var(--primary-dark), #268891) !important;
  transform: translateY(-3px);
  box-shadow: 0 6px 15px rgba(55, 183, 195, 0.4);
}

.btn-submit-forms:active {
  transform: translateY(-1px);
  box-shadow: 0 3px 8px rgba(55, 183, 195, 0.3);
}

.btn-submit-forms:disabled {
  background: linear-gradient(to right, #b3b3b3, #9e9e9e) !important;
  cursor: not-allowed;
  box-shadow: none;
  transform: none;
}

/* Enhanced Submit Button Styles */
.submission-controls {
  display: flex;
  align-items: center;
  gap: 12px;
}

.btn-submit-forms {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 10px 16px;
  border-radius: 6px;
  border: none;
  font-weight: 500;
  color: white;
  background-color: #4a6cf7;
  transition: all 0.3s ease;
  min-width: 180px;
}

.btn-submit-forms:hover:not(:disabled) {
  background-color: #3451b2;
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.btn-submit-forms:disabled {
  background-color: #a0a0a0;
  cursor: not-allowed;
}

.btn-submit-forms.active {
  animation: pulse 2s infinite;
}

.btn-submit-forms.submitting {
  background-color: #4a6cf7;
  opacity: 0.8;
}

.submit-icon {
  margin-right: 8px;
  transition: transform 0.3s ease;
}

.btn-submit-forms:hover:not(:disabled) .submit-icon {
  transform: translateX(3px);
}

/* Select All Forms */
.select-all-container {
  display: flex;
  align-items: center;
  margin-left: 10px;
}

/* Tooltip styles */
.tooltip-container {
  position: relative;
  display: inline-block;
}

.help-icon {
  cursor: pointer;
  color: #4a6cf7;
  transition: color 0.3s ease;
}

.help-icon:hover {
  color: #3451b2;
}

.tooltip-content {
  position: absolute;
  top: -10px;
  right: 25px;
  transform: translateY(-100%);
  background-color: #333;
  color: #fff;
  padding: 10px 15px;
  border-radius: 6px;
  width: 250px;
  z-index: 100;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
  font-size: 0.85rem;
}

.tooltip-content:after {
  content: '';
  position: absolute;
  bottom: 10px;
  right: -8px;
  width: 0;
  height: 0;
  border-left: 8px solid #333;
  border-top: 8px solid transparent;
  border-bottom: 8px solid transparent;
}

/* Confirmation Dialog */
.confirmation-dialog-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.confirmation-dialog {
  background-color: white;
  border-radius: 8px;
  padding: 24px;
  width: 90%;
  max-width: 500px;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
  animation: fadeInScale 0.3s ease;
}

.confirmation-dialog h3 {
  margin-top: 0;
  color: #333;
  font-weight: 600;
}

.dialog-buttons {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  margin-top: 24px;
}

.btn-cancel {
  padding: 8px 16px;
  border-radius: 4px;
  border: 1px solid #ccc;
  background-color: white;
  cursor: pointer;
}

.btn-confirm {
  padding: 8px 16px;
  border-radius: 4px;
  border: none;
  background-color: #4a6cf7;
  color: white;
  font-weight: 500;
  cursor: pointer;
}

/* Animations */
@keyframes pulse {
  0% {
    box-shadow: 0 0 0 0 rgba(74, 108, 247, 0.4);
  }
  70% {
    box-shadow: 0 0 0 6px rgba(74, 108, 247, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(74, 108, 247, 0);
  }
}

@keyframes fadeInScale {
  from {
    opacity: 0;
    transform: scale(0.9);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

/* Responsive Adjustments */
@media (max-width: 768px) {
  .form-view-header {
    flex-direction: column;
    gap: 20px;
    align-items: flex-start;
  }
  
  .form-view-content {
    padding: 20px 15px;
  }
  
  .tabs-container {
    overflow-x: auto;
    padding-bottom: 5px;
  }
  
  .tab-buttons {
    width: max-content;
    min-width: 100%;
  }
  
  .form-card {
    margin-bottom: 15px;
  }
  
  .formpreview-info-grid {
    grid-template-columns: 1fr;
  }
}

/* Patient Form Styles - Tabs */
.tabs-container {
  margin-bottom: 30px;
  position: relative;
}

.tabs-container::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 1px;
  background-color: var(--border-light);
}

.tab-buttons {
  display: flex;
  gap: 12px;
  padding-bottom: 15px;
  position: relative;
  z-index: 1;
}

.tab-button {
  display: flex;
  align-items: center;
  gap: 10px;
  padding: 12px 20px;
  border: none;
  background: white;
  border-radius: var(--border-radius-md);
  font-weight: 500;
  color: var(--text-light);
  cursor: pointer;
  transition: var(--transition-fast);
  box-shadow: var(--shadow-sm);
  border: 1px solid var(--border-light);
  position: relative;
  overflow: hidden;
}

.tab-button::before {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 0;
  height: 3px;
  background: linear-gradient(to right, var(--primary-color), var(--primary-dark));
  transition: var(--transition-normal);
}

.tab-button.active {
  background-color: white;
  color: var(--primary-color);
  border-color: var(--primary-border);
  box-shadow: 0 4px 10px rgba(55, 183, 195, 0.15);
  font-weight: 600;
}

.tab-button.active::before {
  width: 100%;
}

.tab-button:hover {
  background-color: var(--primary-light);
  transform: translateY(-2px);
}

.tab-button.active:hover {
  background-color: white;
}

.tab-button svg {
  transition: var(--transition-fast);
}

.tab-button:hover svg {
  transform: scale(1.1);
  color: var(--primary-color);
}

/* Form Selection Styles */
.form-card.selectable {
  position: relative;
  border: 2px solid transparent;
  transition: var(--transition-normal);
}

.form-card.selectable:hover {
  border-color: var(--primary-color);
  transform: translateY(-4px);
  box-shadow: 0 6px 15px rgba(55, 183, 195, 0.2);
}

.form-card.selectable.selected {
  border-color: var(--primary-color);
  background-color: var(--primary-bg);
  box-shadow: 0 5px 15px rgba(55, 183, 195, 0.25);
}

.form-checkbox {
  position: absolute;
  top: 15px;
  right: 15px;
  z-index: 2;
  background-color: white;
  border-radius: 50%;
  padding: 3px;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
  transition: var(--transition-fast);
}

.form-checkbox:hover {
  transform: scale(1.1);
  box-shadow: 0 3px 8px rgba(55, 183, 195, 0.2);
}

.form-checkbox input[type="checkbox"] {
  width: 20px;
  height: 20px;
  cursor: pointer;
  accent-color: var(--primary-color);
  border-radius: 4px;
  transition: var(--transition-fast);
}

.form-card.selected .form-checkbox {
  background-color: var(--primary-light);
}

/* Submission Message Styles */
.submission-result {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 25px;
  padding: 16px 20px;
  border-radius: var(--border-radius-md);
  animation: slideIn 0.4s ease;
  box-shadow: var(--shadow-sm);
}

.submission-result.success {
  background-color: rgba(0, 200, 83, 0.1);
  border-left: 4px solid #00c853;
  color: #00913c;
}

.submission-result.error {
  background-color: rgba(255, 76, 76, 0.1);
  border-left: 4px solid #ff4c4c;
  color: #d32f2f;
}

.submission-result svg {
  flex-shrink: 0;
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateY(-15px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Submitted Forms Tab Styles */
.form-card.submitted {
  cursor: pointer;
  background-color: #f8f9fa;
  border-left: 4px solid #8bc34a;
  transition: var(--transition-normal);
  position: relative;
  overflow: hidden;
}

.form-card.submitted::before {
  content: '';
  position: absolute;
  top: 0;
  right: 0;
  width: 0;
  height: 0;
  border-style: solid;
  border-width: 0 40px 40px 0;
  border-color: transparent #8bc34a transparent transparent;
  z-index: 1;
}

.form-card.submitted::after {
  content: '✓';
  position: absolute;
  top: 5px;
  right: 9px;
  color: white;
  font-size: 14px;
  font-weight: bold;
  z-index: 2;
}

.form-card.submitted:hover {
  transform: translateY(-3px);
  box-shadow: 0 6px 12px rgba(139, 195, 74, 0.2);
  border-color: #7cb342;
}

/* Form Display Fixes */
.row.y-gap-30 {
  margin-bottom: 30px;
}

.col-xl-12, .col-lg-12, .col-md-12 {
  transition: var(--transition-fast);
}

.col-xl-12:hover, .col-lg-12:hover, .col-md-12:hover {
  z-index: 10;
}

/* Form Submission Preview Styles */
.formpreview-main {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #f0f5f9;
  padding: 30px;
  z-index: 1000;
  overflow-y: auto;
  animation: fadeInPreview 0.4s ease-out;
}

@keyframes fadeInPreview {
  from { opacity: 0; }
  to { opacity: 1; }
}

.formpreview-content {
  max-width: 1200px;
  margin: 20px auto;
  padding: 0;
}

.formpreview-container {
  background-color: white;
  border-radius: var(--border-radius-lg);
  box-shadow: var(--shadow-lg);
  overflow: hidden;
  border: 1px solid rgba(0, 0, 0, 0.05);
}

.formpreview-header {
  display: flex;
  align-items: center;
  padding: 25px 30px;
  background: linear-gradient(to right, #f9f9f9, #f3f4f6);
  border-bottom: 1px solid var(--border-light);
  position: relative;
}

.formpreview-header::after {
  content: '';
  position: absolute;
  bottom: -1px;
  left: 0;
  width: 100px;
  height: 3px;
  background: linear-gradient(to right, var(--primary-color), var(--primary-dark));
  border-radius: 3px;
}

.formpreview-back-button {
  display: flex;
  align-items: center;
  gap: 10px;
  padding: 10px 18px;
  background-color: white;
  border: 1px solid var(--border-light);
  border-radius: var(--border-radius-md);
  cursor: pointer;
  transition: var(--transition-fast);
  margin-right: 25px;
  font-weight: 500;
  color: var(--text-medium);
  box-shadow: var(--shadow-sm);
}

.formpreview-back-button:hover {
  background-color: var(--primary-light);
  border-color: var(--primary-border);
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(55, 183, 195, 0.15);
  color: var(--primary-color);
}

.formpreview-back-button:active {
  transform: translateY(0);
}

.formpreview-header h1 {
  font-size: 24px;
  margin: 0;
  font-weight: 600;
  color: var(--text-dark);
  position: relative;
}

.formpreview-card {
  padding: 30px;
}

.formpreview-info {
  background-color: #f9fafb;
  padding: 25px;
  border-radius: var(--border-radius-md);
  margin-bottom: 25px;
  box-shadow: var(--shadow-sm);
  border: 1px solid var(--border-light);
  position: relative;
  overflow: hidden;
}

.formpreview-info::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 4px;
  height: 100%;
  background: linear-gradient(to bottom, var(--primary-color), var(--primary-dark));
}

.formpreview-info h2 {
  margin-top: 0;
  font-size: 20px;
  font-weight: 600;
  margin-bottom: 20px;
  color: var(--text-dark);
  position: relative;
  padding-bottom: 10px;
}

.formpreview-info h2::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 60px;
  height: 3px;
  background: linear-gradient(to right, var(--primary-color), var(--primary-dark));
  border-radius: 3px;
}

.formpreview-info-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
  gap: 20px;
}

.formpreview-info-item {
  display: flex;
  flex-direction: column;
  background-color: white;
  padding: 15px;
  border-radius: var(--border-radius-sm);
  box-shadow: var(--shadow-sm);
  transition: var(--transition-fast);
  border: 1px solid var(--border-light);
}

.formpreview-info-item:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
  border-color: var(--primary-border);
}

.formpreview-info-item label {
  font-weight: 500;
  color: var(--text-light);
  margin-bottom: 8px;
  font-size: 14px;
  display: flex;
  align-items: center;
}

.formpreview-info-item span {
  color: var(--text-dark);
  font-weight: 600;
  font-size: 16px;
}

.submission-status {
  display: inline-block;
  padding: 5px 12px;
  border-radius: 20px;
  font-size: 14px;
  font-weight: 600;
  background-color: #e0f7f9;
  color: var(--primary-color);
}

.formpreview-comments-section {
  background-color: #e0f7f9;
  border-radius: var(--border-radius-md);
  padding: 20px 25px;
  margin-bottom: 25px;
  box-shadow: var(--shadow-sm);
  border: 1px solid rgba(55, 183, 195, 0.2);
  position: relative;
}

.formpreview-comments-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 4px;
  height: 100%;
  background: linear-gradient(to bottom, var(--primary-color), var(--primary-dark));
}

.formpreview-comments-section h2 {
  font-size: 20px;
  font-weight: 600;
  margin-top: 0;
  margin-bottom: 15px;
  color: var(--text-dark);
  position: relative;
  padding-bottom: 10px;
}

.formpreview-comments-section h2::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 60px;
  height: 3px;
  background: linear-gradient(to right, var(--primary-color), var(--primary-dark));
  border-radius: 3px;
}

.formpreview-comment {
  margin: 0;
  white-space: pre-wrap;
  line-height: 1.6;
  color: var(--text-medium);
  font-size: 15px;
  background-color: rgba(255, 255, 255, 0.7);
  padding: 15px;
  border-radius: var(--border-radius-sm);
}

.formpreview-attachment-section {
  background-color: #f9fafb;
  border-radius: var(--border-radius-md);
  padding: 25px;
  margin-bottom: 25px;
  box-shadow: var(--shadow-sm);
  border: 1px solid var(--border-light);
}

.formpreview-attachment-section h2 {
  font-size: 20px;
  font-weight: 600;
  margin-top: 0;
  margin-bottom: 20px;
  color: var(--text-dark);
  position: relative;
  padding-bottom: 10px;
}

.formpreview-attachment-section h2::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 60px;
  height: 3px;
  background: linear-gradient(to right, var(--primary-color), var(--primary-dark));
  border-radius: 3px;
}

.formpreview-attachments {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 15px;
}

.formpreview-attachment-item {
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  overflow: hidden;
  background-color: white;
}

.formpreview-image-container img {
  width: 100%;
  max-height: 200px;
  object-fit: cover;
}

.formpreview-pdf-container {
  height: 200px;
  overflow: hidden;
}

.formpreview-file-link {
  padding: 10px;
  text-align: center;
}

.formpreview-file-link a {
  color: #37B7C3;
  text-decoration: none;
  word-break: break-all;
}

.formpreview-file-link a:hover {
  text-decoration: underline;
}

.formpreview-form-data {
  background-color: white;
  border-radius: var(--border-radius-md);
  padding: 25px;
  box-shadow: var(--shadow-sm);
  border: 1px solid var(--border-light);
}

.formpreview-form-data h2 {
  font-size: 22px;
  font-weight: 600;
  margin-top: 0;
  margin-bottom: 25px;
  color: var(--text-dark);
  position: relative;
  padding-bottom: 12px;
}

.formpreview-form-data h2::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 80px;
  height: 3px;
  background: linear-gradient(to right, var(--primary-color), var(--primary-dark));
  border-radius: 3px;
}

.formpreview-section {
  margin-bottom: 35px;
  border-bottom: 1px solid var(--border-light);
  padding-bottom: 25px;
  animation: fadeIn 0.5s ease-out;
}

.formpreview-section:last-child {
  border-bottom: none;
  margin-bottom: 0;
  padding-bottom: 0;
}

.formpreview-section-name {
  font-size: 18px;
  font-weight: 600;
  margin: 0 0 12px 0;
  color: var(--text-dark);
  position: relative;
  padding-left: 15px;
}

.formpreview-section-name::before {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  height: 100%;
  width: 4px;
  background-color: var(--primary-color);
  border-radius: 4px;
}

.formpreview-section-description {
  color: var(--text-light);
  margin: 0 0 20px 0;
  line-height: 1.6;
  font-size: 15px;
  padding-left: 15px;
}

.formpreview-question-item {
  padding: 20px;
  border-top: 1px dashed var(--border-light);
  margin-top: 15px;
  background-color: #f9fafb;
  border-radius: var(--border-radius-md);
  transition: var(--transition-fast);
  box-shadow: var(--shadow-sm);
}

.formpreview-question-item:hover {
  background-color: white;
  box-shadow: var(--shadow-md);
  transform: translateY(-2px);
}

.formpreview-question-item:first-child {
  border-top: none;
  margin-top: 0;
}

.formpreview-question-header {
  display: flex;
  gap: 15px;
  margin-bottom: 15px;
}

.question-icon {
  flex-shrink: 0;
}

.question-content {
  flex-grow: 1;
}

.formpreview-question-header h4 {
  margin: 0 0 8px 0;
  font-size: 16px;
  font-weight: 600;
  color: var(--text-dark);
  display: flex;
  align-items: center;
}

.question-required {
  color: #f44336;
  margin-left: 5px;
  font-weight: bold;
}

.question-type {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.badge {
  display: inline-block;
  padding: 4px 10px;
  border-radius: 20px;
  font-size: 12px;
  font-weight: 500;
  background-color: #e0f7f9;
  color: var(--primary-color);
}

.badge-non-clinical {
  background-color: #fff3cd;
  color: #856404;
}

.badge-query {
  background-color: #e6f7ff;
  color: #0c63e4;
}

/* Raise Query Button */
.question-header-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.raise-query-button {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 6px 12px;
  background-color: #e6f7ff;
  color: #0c63e4;
  border: 1px solid #bae0ff;
  border-radius: var(--border-radius-sm);
  font-size: 13px;
  font-weight: 500;
  cursor: pointer;
  transition: var(--transition-fast);
}

.raise-query-button:hover {
  background-color: #bae0ff;
  border-color: #0c63e4;
  transform: translateY(-2px);
  box-shadow: 0 2px 5px rgba(12, 99, 228, 0.2);
}

.raise-query-button:active {
  transform: translateY(0);
}

.formpreview-answer {
  margin-top: 15px;
  padding-left: 55px;
}

/* Answer content styling */
.answer-content {
  background-color: white;
  padding: 15px;
  border-radius: var(--border-radius-sm);
  margin: 0;
  color: var(--text-dark);
  white-space: pre-wrap;
  word-break: break-word;
  border: 1px solid var(--border-light);
  box-shadow: var(--shadow-sm);
  line-height: 1.6;
}

.no-answer {
  color: var(--text-light);
  font-style: italic;
  padding: 10px 15px;
  background-color: rgba(0, 0, 0, 0.03);
  border-radius: var(--border-radius-sm);
  display: inline-block;
}

/* Selected option styling */
.selected-option {
  display: flex;
  align-items: center;
  gap: 10px;
  padding: 10px 15px;
  background-color: white;
  border-radius: var(--border-radius-sm);
  margin-bottom: 8px;
  border: 1px solid var(--border-light);
  box-shadow: var(--shadow-sm);
  transition: var(--transition-fast);
}

.selected-option:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
  border-color: var(--primary-border);
}

.option-icon {
  color: var(--primary-color);
}

.option-text {
  font-weight: 500;
  color: var(--text-dark);
}

/* Boolean styling */
.boolean-yes {
  color: #4caf50;
  font-weight: 600;
  background-color: rgba(76, 175, 80, 0.1);
  padding: 5px 12px;
  border-radius: 20px;
}

.boolean-no {
  color: #f44336;
  font-weight: 600;
  background-color: rgba(244, 67, 54, 0.1);
  padding: 5px 12px;
  border-radius: 20px;
}

/* Multiple textboxes styling */
.question-answer-multiple-textboxes {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
  gap: 10px;
}

.textbox-item {
  background-color: white;
  padding: 12px 15px;
  border-radius: var(--border-radius-sm);
  border: 1px solid var(--border-light);
  box-shadow: var(--shadow-sm);
  display: flex;
  flex-direction: column;
  gap: 5px;
}

.textbox-label {
  font-weight: 500;
  color: var(--text-light);
  font-size: 13px;
}

.textbox-value {
  font-weight: 500;
  color: var(--text-dark);
}

/* Range styling */
.range-value {
  padding: 15px;
  background-color: white;
  border-radius: var(--border-radius-sm);
  border: 1px solid var(--border-light);
  box-shadow: var(--shadow-sm);
}

.range-bar {
  height: 10px;
  background-color: #e5e7eb;
  border-radius: 5px;
  margin-bottom: 10px;
  overflow: hidden;
}

.range-fill {
  height: 100%;
  background: linear-gradient(to right, var(--primary-color), var(--primary-dark));
  border-radius: 5px;
}

.range-labels {
  display: flex;
  justify-content: space-between;
  font-size: 14px;
}

.range-min, .range-max {
  color: var(--text-light);
}

.range-selected {
  font-weight: 600;
  color: var(--primary-color);
}

/* Table styling */
.table-responsive {
  overflow-x: auto;
  margin-bottom: 10px;
  border-radius: var(--border-radius-sm);
  box-shadow: var(--shadow-sm);
}

.answer-table {
  width: 100%;
  border-collapse: separate;
  border-spacing: 0;
  border: 1px solid var(--border-light);
  border-radius: var(--border-radius-sm);
  overflow: hidden;
}

.answer-table th {
  background: linear-gradient(to right, var(--primary-color), var(--primary-dark));
  color: white;
  font-weight: 600;
  text-align: left;
  padding: 12px 15px;
  font-size: 14px;
}

.answer-table td {
  padding: 12px 15px;
  border-top: 1px solid var(--border-light);
  color: var(--text-dark);
}

.answer-table tr:nth-child(even) {
  background-color: #f9fafb;
}

.answer-table tr:hover td {
  background-color: var(--primary-bg);
}

/* Signature styling */
.question-answer-signature {
  background-color: white;
  padding: 15px;
  border-radius: var(--border-radius-sm);
  border: 1px solid var(--border-light);
  box-shadow: var(--shadow-sm);
}

.signature-name, .signature-date {
  margin-bottom: 10px;
  display: flex;
  align-items: center;
}

.signature-label {
  font-weight: 500;
  color: var(--text-light);
  width: 80px;
  font-size: 14px;
}

.signature-value {
  font-weight: 500;
  color: var(--text-dark);
}

/* File attachment styling */
.attachment-files-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.attachment-file-item {
  margin-bottom: 16px;
  padding: 15px;
  background-color: white;
  border-radius: var(--border-radius-sm);
  border: 1px solid var(--border-light);
  transition: var(--transition-fast);
  box-shadow: var(--shadow-sm);
  position: relative;
}

.attachment-file-item:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
  border-color: var(--primary-border);
}

.file-type-icon {
  display: inline-block;
  margin-right: 10px;
  font-size: 18px;
}

.file-name.file-link {
  color: var(--primary-color);
  text-decoration: none;
  font-weight: 500;
  display: inline-block;
  margin-right: 8px;
  transition: var(--transition-fast);
}

.file-name.file-link:hover {
  color: var(--primary-dark);
  text-decoration: underline;
}

.file-size {
  color: var(--text-light);
  font-size: 13px;
}

.file-preview {
  margin-top: 15px;
  text-align: center;
}

.image-preview {
  max-width: 100%;
  max-height: 250px;
  border-radius: var(--border-radius-sm);
  margin-top: 10px;
  cursor: pointer;
  box-shadow: var(--shadow-md);
  transition: var(--transition-normal);
  display: inline-block;
}

.image-preview:hover {
  transform: scale(1.03);
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.15);
}

.no-attachments {
  color: var(--text-light);
  font-style: italic;
  padding: 10px 15px;
  background-color: rgba(0, 0, 0, 0.03);
  border-radius: var(--border-radius-sm);
  display: inline-block;
}

@media (max-width: 768px) {
  .formpreview-info-grid {
    grid-template-columns: 1fr;
  }
  
  .formpreview-attachments {
    grid-template-columns: 1fr;
  }
  
  .formpreview-header {
    flex-direction: column;
    align-items: flex-start;
  }
  
  .formpreview-back-button {
    margin-bottom: 10px;
    margin-right: 0;
  }
}

/* Question comment preview styles */
.formpreview-question-comment {
  margin: 0.5rem 0 0.5rem 0;
  padding: 0.75rem 1rem;
  background: #f1f5f9;
  border-left: 4px solid #37b7c3;
  border-radius: 0.375rem;
  color: #334155;
  font-size: 0.97em;
  display: flex;
  align-items: center;
  gap: 0.5em;
}
.formpreview-comment-label {
  font-weight: 600;
  color: #0369a1;
  margin-right: 0.5em;
}

/* Query Dialog Styles */
.query-dialog-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.query-dialog {
  background: white;
  border-radius: 8px;
  padding: 24px;
  width: 90%;
  max-width: 600px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  animation: dialogSlideIn 0.3s ease-out;
}

.query-dialog-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.query-dialog-header h3 {
  margin: 0;
  font-size: 1.5rem;
  color: #1a202c;
}

.close-button {
  background: none;
  border: none;
  font-size: 24px;
  color: #64748b;
  cursor: pointer;
  padding: 4px;
  line-height: 1;
  border-radius: 4px;
  transition: all 0.2s;
}

.close-button:hover {
  background-color: #f1f5f9;
  color: #1a202c;
}

.form-group {
  margin-bottom: 20px;
}

.form-group label {
  display: block;
  margin-bottom: 8px;
  font-weight: 500;
  color: #4a5568;
}

.form-group textarea {
  width: 100%;
  padding: 12px;
  border: 1px solid #e2e8f0;
  border-radius: 6px;
  font-size: 14px;
  resize: vertical;
  transition: border-color 0.2s;
}

.form-group textarea:focus {
  outline: none;
  border-color: #3182ce;
  box-shadow: 0 0 0 3px rgba(49, 130, 206, 0.1);
}

.form-group textarea.error {
  border-color: #ef4444;
}

.error-message {
  color: #ef4444;
  font-size: 12px;
  margin-top: 4px;
  display: block;
}

.priority-selector {
  display: flex;
  gap: 12px;
  margin-top: 8px;
}

.priority-option {
  flex: 1;
  padding: 8px 16px;
  border: 2px solid;
  border-radius: 6px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
}

.priority-option:hover {
  transform: translateY(-1px);
}

.priority-option.selected {
  font-weight: 600;
}

.error-banner,
.success-banner {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px;
  border-radius: 6px;
  margin-bottom: 20px;
  font-size: 14px;
}

.error-banner {
  background-color: #fee2e2;
  color: #ef4444;
  border: 1px solid #fecaca;
}

.success-banner {
  background-color: #dcfce7;
  color: #10b981;
  border: 1px solid #bbf7d0;
}

.query-dialog-actions {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  margin-top: 24px;
}

.btn-primary,
.btn-secondary {
  padding: 10px 20px;
  border-radius: 6px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
  display: flex;
  align-items: center;
  gap: 8px;
}

.btn-primary {
  background-color: #3182ce;
  color: white;
  border: none;
}

.btn-primary:hover:not(:disabled) {
  background-color: #2c5282;
}

.btn-primary:disabled {
  background-color: #a0aec0;
  cursor: not-allowed;
}

.btn-secondary {
  background-color: #f7fafc;
  color: #4a5568;
  border: 1px solid #e2e8f0;
}

.btn-secondary:hover:not(:disabled) {
  background-color: #edf2f7;
}

.btn-secondary:disabled {
  opacity: 0.7;
  cursor: not-allowed;
}

.loading-spinner {
  width: 16px;
  height: 16px;
  border: 2px solid #ffffff;
  border-top-color: transparent;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

@keyframes dialogSlideIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Add styles for tracking changes */
.changes-tracked {
  border-left: 3px solid #4a90e2;
  padding-left: 10px;
  margin-left: -13px;
}

.changes-tracked .question-label::after {
  content: " (Modified)";
  color: #4a90e2;
  font-size: 0.9em;
  font-style: italic;
}

/* Query Section Styles */
.question-queries {
  margin-top: 20px;
  padding: 15px;
  background-color: #f8fafc;
  border-radius: var(--border-radius-md);
  border: 1px solid #e2e8f0;
}

.question-queries h5 {
  font-size: 16px;
  font-weight: 600;
  margin-top: 0;
  margin-bottom: 15px;
  color: var(--text-dark);
  padding-bottom: 8px;
  border-bottom: 1px solid #e2e8f0;
}

.query-item {
  margin-bottom: 15px;
  border-radius: var(--border-radius-sm);
  overflow: hidden;
  box-shadow: var(--shadow-sm);
  border: 1px solid #e2e8f0;
  background-color: white;
}

.query-item:last-child {
  margin-bottom: 0;
}

.query-item.open {
  border-left: 3px solid #0c63e4;
}

.query-item.answered {
  border-left: 3px solid #f59e0b;
}

.query-item.resolved {
  border-left: 3px solid #10b981;
}

.query-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 15px;
  background-color: #f8fafc;
  border-bottom: 1px solid #e2e8f0;
}

.query-meta {
  display: flex;
  align-items: center;
  gap: 12px;
  flex-wrap: wrap;
}

.query-status {
  display: flex;
  align-items: center;
  gap: 5px;
  font-weight: 600;
  font-size: 13px;
  padding: 4px 10px;
  border-radius: 20px;
}

.query-status.open {
  background-color: #e6f7ff;
  color: #0c63e4;
}

.query-status.answered {
  background-color: #fff7ed;
  color: #f59e0b;
}

.query-status.resolved {
  background-color: #ecfdf5;
  color: #10b981;
}

.query-priority {
  font-size: 13px;
  font-weight: 500;
  color: var(--text-medium);
}

.query-date {
  font-size: 13px;
  color: var(--text-light);
}

.resolve-query-button {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 6px 12px;
  background-color: #ecfdf5;
  color: #10b981;
  border: 1px solid #a7f3d0;
  border-radius: var(--border-radius-sm);
  font-size: 13px;
  font-weight: 500;
  cursor: pointer;
  transition: var(--transition-fast);
}

.resolve-query-button:hover {
  background-color: #a7f3d0;
  border-color: #10b981;
  transform: translateY(-2px);
  box-shadow: 0 2px 5px rgba(16, 185, 129, 0.2);
}

.resolve-query-button:active {
  transform: translateY(0);
}

.query-content {
  padding: 15px;
}

.query-text {
  margin: 0 0 15px 0;
  line-height: 1.6;
  color: var(--text-dark);
}

.query-reply {
  margin-top: 15px;
  padding: 12px;
  background-color: #f8fafc;
  border-radius: var(--border-radius-sm);
  border: 1px solid #e2e8f0;
}

.reply-label {
  font-weight: 600;
  color: var(--text-medium);
  display: block;
  margin-bottom: 5px;
}

.reply-text {
  margin: 0 0 10px 0;
  line-height: 1.6;
  color: var(--text-dark);
}

.reply-meta {
  font-size: 13px;
  color: var(--text-light);
  display: block;
  text-align: right;
}

.query-reply-form {
  margin-top: 15px;
}

.query-reply-form textarea {
  width: 100%;
  padding: 12px;
  border: 1px solid #e2e8f0;
  border-radius: var(--border-radius-sm);
  font-size: 14px;
  resize: vertical;
  margin-bottom: 10px;
  transition: border-color 0.2s;
}

.query-reply-form textarea:focus {
  outline: none;
  border-color: #3182ce;
  box-shadow: 0 0 0 3px rgba(49, 130, 206, 0.1);
}

.submit-reply-button {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 8px 16px;
  background-color: #3182ce;
  color: white;
  border: none;
  border-radius: var(--border-radius-sm);
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: var(--transition-fast);
  margin-left: auto;
}

.submit-reply-button:hover {
  background-color: #2c5282;
  transform: translateY(-2px);
  box-shadow: 0 2px 5px rgba(49, 130, 206, 0.2);
}

.submit-reply-button:active {
  transform: translateY(0);
}

.btn-delete-form {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 16px;
  background-color: #dc3545;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  transition: background-color 0.2s;
}

.btn-delete-form:hover {
  background-color: #c82333;
}

.btn-delete-form:active {
  background-color: #bd2130;
}

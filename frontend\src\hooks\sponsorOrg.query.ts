import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { SponsorOrg_KEYS } from './keys';
import { SponsorOrg } from "@/services/api/types";
import { getAllSponsorOrgs, getSponsorOrgById, createSponsorOrg, updateSponsorOrg, deleteSponsorOrg } from "@/services/api/sponsorOrg.service";

export const useSponsorOrgsQuery = () => {
    return useQuery<SponsorOrg[], Error>({
        queryKey: [SponsorOrg_KEYS.GET_ALL],
        queryFn: getAllSponsorOrgs,
        refetchOnWindowFocus: false,
        staleTime: 1000 * 60 * 5,
    });
};
export const useSponsorOrgQuery = (uuid: string) => {
    return useQuery<SponsorOrg, Error>({
        queryKey: [SponsorOrg_KEYS.GET_BY_ID, uuid],
        queryFn: () => getSponsorOrgById(uuid),
        enabled: !!uuid,  // Only fetch if id is provided
        refetchOnWindowFocus: false,  // Prevent refetch when switching tabs/windows
        refetchOnReconnect: false,    // Prevent refetch when reconnecting to the internet
        staleTime: 1000 * 60 * 5,      // Cache data for 5 minutes before marking it stale
    });
};

export const useCreateSponsorOrgMutation = () => {
    const queryClient = useQueryClient();

    return useMutation({
        mutationFn: createSponsorOrg,
        onSuccess: () => {
            queryClient.invalidateQueries({ queryKey: [SponsorOrg_KEYS.GET_ALL] });
        },
    });
};

export const useUpdateSponsorOrgMutation = () => {
    const queryClient = useQueryClient();

    return useMutation({
        mutationFn: ({ uuid, data }: { uuid: string; data: Partial<SponsorOrg> }) => 
            updateSponsorOrg(uuid, data),
        onSuccess: (_, { uuid }) => {
            queryClient.invalidateQueries({ queryKey: [SponsorOrg_KEYS.GET_ALL] });
            queryClient.invalidateQueries({ queryKey: [SponsorOrg_KEYS.GET_BY_ID, uuid] });
        },
    });
};

export const useDeleteSponsorOrgMutation = () => {
    const queryClient = useQueryClient();

    return useMutation({
        mutationFn: ({ uuid}: { uuid: string }) => 
            deleteSponsorOrg(uuid),
        onSuccess: () => {
            queryClient.invalidateQueries({ queryKey: [SponsorOrg_KEYS.GET_ALL] });
            
        },
    });

};
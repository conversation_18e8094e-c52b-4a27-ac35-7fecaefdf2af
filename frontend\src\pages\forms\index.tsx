import Preloader from "@/components/common/Preloader";
import "./forms.css";
import { useState } from "react";
import FormCard from "@/components/FormCard";
import LightFooter from "@/shared/LightFooter";
import FilterSidebar from "@/components/FilterSidebar";

export default function Forms() {
  const [selectedCategories, setSelectedCategories] = useState<string[]>([]);
  const [activeTab, setActiveTab] = useState<"all" | "favorites">("all");

  const categories = [
    { id: "business", label: "Business Forms", count: 24 },
    { id: "education", label: "Education Forms", count: 16 },
    { id: "healthcare", label: "Healthcare Forms", count: 18 },
    { id: "general", label: "General Forms", count: 32 },
    { id: "hr", label: "HR Forms", count: 12 },
    { id: "legal", label: "Legal Forms", count: 20 },
    { id: "finance", label: "Finance Forms", count: 15 },
  ];

  const handleCategoryChange = (categoryId: string) => {
    setSelectedCategories((prev) =>
      prev.includes(categoryId)
        ? prev.filter((id) => id !== categoryId)
        : [...prev, categoryId]
    );
  };

  return (
    <div className="main-content bg-light-4">
      <Preloader />
      <div className="content-wrapper js-content-wrapper">
        <div className="dashboard__content bg-light-4">
          <div className="container-fluid px-0">
            <div className="row y-gap-30">
              {/* Enhanced Sidebar */}
              <div className="col-xl-3 col-lg-4">
                <FilterSidebar
                  title="Filter Forms"
                  categories={categories}
                  selectedCategories={selectedCategories}
                  onCategoryChange={handleCategoryChange}
                />
              </div>

              <div className="col-xl-9 col-lg-8">
                <div className="tabs-wrapper">
                  <button
                    className={`tab-button ${
                      activeTab === "all" ? "active" : ""
                    }`}
                    onClick={() => setActiveTab("all")}
                  >
                    All Templates
                  </button>
                  <button
                    className={`tab-button ${
                      activeTab === "favorites" ? "active" : ""
                    }`}
                    onClick={() => setActiveTab("favorites")}
                  >
                    My Favorites
                  </button>
                </div>
                {activeTab === "all" && (
                  <div style={{ marginTop: "30px" }}>
                    <div className="row y-gap-30 mt-30" style={{ marginBottom: "30px" }}>
                      <div className="col-xl-3 col-lg-6 col-md-6">
                        <FormCard label="Holistic Form" link="/holistic-form" />
                      </div>
                      <div className="col-xl-3 col-lg-6 col-md-6">
                        <FormCard label="Contact Form" />
                      </div>
                      <div className="col-xl-3 col-lg-6 col-md-6">
                        <FormCard label="Registration Form" />
                      </div>
                      <div className="col-xl-3 col-lg-6 col-md-6">
                        <FormCard label="Survey Form" />
                      </div>
                    </div>
                    <div className="row y-gap-30 mt-30" style={{ marginBottom: "30px" }}>
                      <div className="col-xl-3 col-lg-6 col-md-6">
                        <FormCard label="Feedback Form" />
                      </div>
                      <div className="col-xl-3 col-lg-6 col-md-6">
                        <FormCard label="Application Form" />
                      </div>
                      <div className="col-xl-3 col-lg-6 col-md-6">
                        <FormCard label="Evaluation Form" />
                      </div>
                      <div className="col-xl-3 col-lg-6 col-md-6">
                        <FormCard label="Assessment Form" />
                      </div>
                    </div>
                    <div className="row y-gap-30 mt-30" style={{ marginBottom: "30px" }}>
                      <div className="col-xl-3 col-lg-6 col-md-6">
                        <FormCard label="Complaint Form" />
                      </div>
                      <div className="col-xl-3 col-lg-6 col-md-6">
                        <FormCard label="Request Form" />
                      </div>
                      <div className="col-xl-3 col-lg-6 col-md-6">
                        <FormCard label="Enrollment Form" />
                      </div>
                      <div className="col-xl-3 col-lg-6 col-md-6">
                        <FormCard label="Subscription Form" />
                      </div>
                    </div>
                    <div className="row y-gap-30 mt-30" style={{ marginBottom: "30px" }}>
                      <div className="col-xl-3 col-lg-6 col-md-6">
                        <FormCard label="Order Form" />
                      </div>
                      <div className="col-xl-3 col-lg-6 col-md-6">
                        <FormCard label="Booking Form" />
                      </div>
                      <div className="col-xl-3 col-lg-6 col-md-6">
                        <FormCard label="Inquiry Form" />
                      </div>
                      <div className="col-xl-3 col-lg-6 col-md-6">
                        <FormCard label="Consent Form" />
                      </div>
                    </div>
                  </div>
                )}
                {activeTab === "favorites" && (
                  <div style={{ marginTop: "30px" }}>
                    <div className="row y-gap-30 mt-30" style={{ marginBottom: "30px" }}>
                      <div className="col-xl-3 col-lg-6 col-md-6">
                        <FormCard label="Favorite 1" />
                      </div>
                      <div className="col-xl-3 col-lg-6 col-md-6">
                        <FormCard label="Favorite 2" />
                      </div>
                      <div className="col-xl-3 col-lg-6 col-md-6">
                        <FormCard label="Favorite 3" />
                      </div>
                      <div className="col-xl-3 col-lg-6 col-md-6">
                        <FormCard label="Favorite 4" />
                      </div>
                    </div>
                    <div className="row y-gap-30 mt-30" style={{ marginBottom: "30px" }}>
                      <div className="col-xl-3 col-lg-6 col-md-6">
                        <FormCard label="Favorite 5" />
                      </div>
                      <div className="col-xl-3 col-lg-6 col-md-6">
                        <FormCard label="Favorite 6" />
                      </div>
                      <div className="col-xl-3 col-lg-6 col-md-6">
                        <FormCard label="Favorite 7" />
                      </div>
                      <div className="col-xl-3 col-lg-6 col-md-6">
                        <FormCard label="Favorite 8" />
                      </div>
                    </div>
                    <div className="row y-gap-30 mt-30" style={{ marginBottom: "30px" }}>
                      <div className="col-xl-3 col-lg-6 col-md-6">
                        <FormCard label="Favorite 13" />
                      </div>
                      <div className="col-xl-3 col-lg-6 col-md-6">
                        <FormCard label="Favorite 14" />
                      </div>
                      <div className="col-xl-3 col-lg-6 col-md-6">
                        <FormCard label="Favorite 15" />
                      </div>
                      <div className="col-xl-3 col-lg-6 col-md-6">
                        <FormCard label="Favorite 16" />
                      </div>
                    </div>
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>
        <LightFooter />
      </div>
    </div>
  );
}

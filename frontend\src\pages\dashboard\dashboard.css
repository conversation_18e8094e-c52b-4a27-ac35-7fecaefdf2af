/* Dashboard Main Content */
.dashboard-main-content {
  padding: 24px;
  background-color: #f8f9fa;
}

/* Welcome Header */
.welcome-header {
  margin-bottom: 32px;
}

.welcome-title {
  font-size: 32px;
  font-weight: 700;
  color: #1a1a1a;
  margin: 0 0 8px 0;
}

.user-name {
  color: #37B7C3;
}

.welcome-subtitle {
  font-size: 16px;
  color: #6b7280;
  margin: 0;
}

/* KPI Cards Row */
.kpi-cards-row {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 24px;
  margin-bottom: 32px;
}

.kpi-card {
  background: white;
  border-radius: 12px;
  padding: 24px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  border: 1px solid #e5e7eb;
}

.kpi-header {
  display: flex;
  align-items: flex-start;
  gap: 16px;
  margin-bottom: 20px;
}

.kpi-icon {
  width: 48px;
  height: 48px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
}

.patients-icon {
  background-color: #37B7C3;
}

.policies-icon {
  background-color: #37B7C3;
}

.sponsors-icon {
  background-color: #37B7C3;
}

.kpi-info {
  flex: 1;
}

.kpi-title {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.kpi-title span {
  font-size: 16px;
  font-weight: 600;
  color: #374151;
}

.kpi-period {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 14px;
  color: #6b7280;
  cursor: pointer;
}

.kpi-number {
  font-size: 48px;
  font-weight: 700;
  color: #1a1a1a;
  margin-bottom: 12px;
}

.kpi-change {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.change-text {
  font-size: 14px;
  color: #6b7280;
}

.change-percentage {
  font-size: 14px;
  font-weight: 600;
}

.kpi-change.positive .change-percentage {
  color: #10b981;
}

.kpi-change.negative .change-percentage {
  color: #ef4444;
}

/* Middle Section */
.middle-section {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 24px;
  margin-bottom: 32px;
}

/* Staff Card */
.staff-card {
  background: white;
  border-radius: 12px;
  padding: 24px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  border: 1px solid #e5e7eb;
}

.staff-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 20px;
}

.staff-title-section h3 {
  font-size: 18px;
  font-weight: 600;
  color: #374151;
  margin: 0 0 8px 0;
}

.staff-period {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 14px;
  color: #6b7280;
  cursor: pointer;
}

.add-user-btn {
  display: flex;
  align-items: center;
  gap: 8px;
  background-color: #37B7C3;
  color: white;
  border: none;
  border-radius: 8px;
  padding: 10px 16px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s;
}

.add-user-btn:hover {
  background-color: #2ea4a9;
}

.staff-number {
  font-size: 48px;
  font-weight: 700;
  color: #1a1a1a;
  margin-bottom: 12px;
}

.staff-change {
  display: flex;
  flex-direction: column;
  gap: 4px;
  margin-bottom: 24px;
}

.staff-change.positive .change-percentage {
  color: #10b981;
}

.staff-breakdown {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.staff-type {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.staff-type-header {
  display: flex;
  align-items: center;
  gap: 12px;
  font-size: 14px;
  font-weight: 500;
  color: #374151;
}

.staff-type-icon {
  width: 24px;
  height: 24px;
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
}

.doctors-icon {
  background-color: #37B7C3;
}

.nurses-icon {
  background-color: #37B7C3;
}

.receptionist-icon {
  background-color: #37B7C3;
}

.others-icon {
  background-color: #37B7C3;
}

.staff-count {
  margin-left: auto;
  font-weight: 600;
}

.progress-bar {
  height: 8px;
  background-color: #e5e7eb;
  border-radius: 4px;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  border-radius: 4px;
  transition: width 0.3s ease;
}

.doctors-progress {
  background-color: #37B7C3;
}

.nurses-progress {
  background-color: #37B7C3;
}

.receptionist-progress {
  background-color: #37B7C3;
}

.others-progress {
  background-color: #37B7C3;
}

.progress-percentage {
  font-size: 12px;
  color: #6b7280;
  text-align: right;
}

/* Studies Card */
.studies-card {
  background: white;
  border-radius: 12px;
  padding: 24px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  border: 1px solid #e5e7eb;
}

.studies-card h3 {
  font-size: 18px;
  font-weight: 600;
  color: #374151;
  margin: 0 0 20px 0;
}

.studies-stats {
  display: flex;
  justify-content: space-between;
  margin-bottom: 20px;
  padding: 16px;
  background-color: #f9fafb;
  border-radius: 8px;
}

.stat-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
}

.stat-label {
  font-size: 12px;
  color: #6b7280;
  margin-bottom: 4px;
}

.stat-value {
  font-size: 18px;
  font-weight: 600;
  color: #374151;
}

.studies-table-container {
  overflow-x: auto;
}

.studies-table {
  width: 100%;
  border-collapse: collapse;
}

.studies-table th {
  text-align: left;
  padding: 12px;
  font-size: 14px;
  font-weight: 600;
  color: #6b7280;
  border-bottom: 1px solid #e5e7eb;
}

.studies-table td {
  padding: 12px;
  font-size: 14px;
  color: #374151;
  border-bottom: 1px solid #f3f4f6;
}

.studies-table tbody tr:hover {
  background-color: #f9fafb;
}

.status-badge {
  padding: 4px 12px;
  border-radius: 16px;
  font-size: 12px;
  font-weight: 500;
}

.status-badge.pending {
  background-color: #fef3c7;
  color: #d97706;
}

.action-buttons {
  display: flex;
  gap: 8px;
}

.action-btn {
  width: 32px;
  height: 32px;
  border: none;
  border-radius: 6px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: background-color 0.2s;
}

.action-btn.accept {
  background-color: #dcfce7;
  color: #16a34a;
}

.action-btn.accept:hover {
  background-color: #bbf7d0;
}

.action-btn.reject {
  background-color: #fee2e2;
  color: #dc2626;
}

.action-btn.reject:hover {
  background-color: #fecaca;
}

/* Requests Section */
.requests-section {
  background: white;
  border-radius: 12px;
  padding: 24px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  border: 1px solid #e5e7eb;
}

.requests-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
}

.requests-header h3 {
  font-size: 18px;
  font-weight: 600;
  color: #374151;
  margin: 0;
}

.requests-stats {
  display: flex;
  align-items: center;
  gap: 24px;
}

.total-requests {
  font-size: 14px;
  color: #6b7280;
}

.requests-chart {
  display: flex;
  align-items: end;
  gap: 4px;
  height: 40px;
}

.chart-bar {
  width: 12px;
  border-radius: 2px;
}

.chart-bar.accepted {
  background-color: #10b981;
}

.chart-bar.pending {
  background-color: #f59e0b;
}

.chart-bar.rejected {
  background-color: #ef4444;
}

.chart-legend {
  display: flex;
  gap: 16px;
}

.legend-item {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 12px;
  color: #6b7280;
}

.legend-color {
  width: 8px;
  height: 8px;
  border-radius: 50%;
}

.legend-color.accepted {
  background-color: #10b981;
}

.legend-color.pending {
  background-color: #f59e0b;
}

.legend-color.rejected {
  background-color: #ef4444;
}

.requests-table-container {
  overflow-x: auto;
}

.requests-table {
  width: 100%;
  border-collapse: collapse;
}

.requests-table th {
  text-align: left;
  padding: 12px;
  font-size: 14px;
  font-weight: 600;
  color: #6b7280;
  border-bottom: 1px solid #e5e7eb;
}

.requests-table td {
  padding: 12px;
  font-size: 14px;
  color: #374151;
  border-bottom: 1px solid #f3f4f6;
}

.requests-table tbody tr:hover {
  background-color: #f9fafb;
}

.details-btn {
  display: flex;
  align-items: center;
  gap: 6px;
  background-color: #dbeafe;
  color: #2563eb;
  border: none;
  border-radius: 6px;
  padding: 6px 12px;
  font-size: 12px;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s;
}

.details-btn:hover {
  background-color: #bfdbfe;
}

/* Legacy styles for compatibility */
.dashboard__content {
  padding: 0;
  height: calc(100vh - 50px);
  display: flex;
  flex-direction: column;
  overflow: hidden;
  transition: all 0.3s ease;
}

.dashboard__content > .container-fluid {
  flex-grow: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.dashboard__content .row {
  display: flex;
  flex-grow: 1;
  height: 100%; 
  overflow: hidden;
  transition: width 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.dashboard__content > .container-fluid > .row > .col-xl-10.col-lg-9 {
  height: 100%;
  overflow-y: auto;
  padding: 0;
}

.dashboard__content.sidebar-collapsed > .container-fluid > .row > .col-xl-11.col-lg-10 {
  padding-left: 24px;
  transition: padding-left 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.dashboard__content > .container-fluid > .row > .col-xl-2.col-lg-3 {
  height: 100%;
  overflow: hidden !important;
}

.bg-light-4 {
  background-color: #f5f7fa;
}

/* Responsive Design */
@media (max-width: 1024px) {
  .middle-section {
    grid-template-columns: 1fr;
  }
  
  .kpi-cards-row {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 768px) {
  .dashboard-main-content {
    padding: 16px;
  }
  
  .kpi-card,
  .staff-card,
  .studies-card,
  .requests-section {
    padding: 16px;
  }
  
  .requests-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 16px;
  }
  
  .requests-stats {
    flex-wrap: wrap;
  }
}

@media (max-width: 991px) {
  .dashboard__content > .container-fluid > .row > .col-xl-2.col-lg-3 {
    display: none;
  }

  .dashboard__content .row:has(.sidebar-dsbh.mobile) .col-lg-9,
  .dashboard__content .row:has(.sidebar-dsbh.mobile) .col-xl-10 {
    width: 100%;
    flex: 0 0 100%;
    max-width: 100%;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    height: 100%; 
    overflow-y: auto;
    padding: 0;
  }
}

/* Modal Styles */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.modal-content {
  background: white;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  max-width: 500px;
  width: 100%;
}

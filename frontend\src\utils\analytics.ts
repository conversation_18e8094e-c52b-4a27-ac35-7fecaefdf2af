// Google Analytics and SEO tracking utilities

declare global {
  interface Window {
    gtag: (...args: any[]) => void;
    dataLayer: any[];
  }
}

// Initialize Google Analytics
export const initGA = (measurementId: string) => {
  if (typeof window === 'undefined') return;

  // Load Google Analytics script
  const script = document.createElement('script');
  script.async = true;
  script.src = `https://www.googletagmanager.com/gtag/js?id=${measurementId}`;
  document.head.appendChild(script);

  // Initialize dataLayer and gtag
  window.dataLayer = window.dataLayer || [];
  window.gtag = function gtag(...args: any[]) {
    window.dataLayer.push(args);
  };

  window.gtag('js', new Date());
  window.gtag('config', measurementId, {
    page_title: document.title,
    page_location: window.location.href,
  });
};

// Track page views
export const trackPageView = (url: string, title: string) => {
  if (typeof window === 'undefined' || !window.gtag) return;

  window.gtag('config', import.meta.env.VITE_GA_MEASUREMENT_ID, {
    page_title: title,
    page_location: url,
  });
};

// Track custom events for SEO
export const trackSEOEvent = (eventName: string, parameters: Record<string, any> = {}) => {
  if (typeof window === 'undefined' || !window.gtag) return;

  window.gtag('event', eventName, {
    event_category: 'SEO',
    ...parameters,
  });
};

// Track form submissions
export const trackFormSubmission = (formName: string, formType: 'contact' | 'referral' | 'patient' | 'business') => {
  trackSEOEvent('form_submission', {
    form_name: formName,
    form_type: formType,
  });
};

// Track CTA clicks
export const trackCTAClick = (ctaName: string, location: string) => {
  trackSEOEvent('cta_click', {
    cta_name: ctaName,
    cta_location: location,
  });
};

// Track clinical trial engagement
export const trackClinicalTrialEvent = (action: 'view_study' | 'join_study' | 'contact_doctor', studyId?: string) => {
  trackSEOEvent('clinical_trial_engagement', {
    action,
    study_id: studyId,
  });
};

// Track search functionality
export const trackSiteSearch = (searchTerm: string, resultCount: number) => {
  trackSEOEvent('site_search', {
    search_term: searchTerm,
    result_count: resultCount,
  });
};

// Monitor page performance for SEO
export const trackPagePerformance = () => {
  if (typeof window === 'undefined' || !window.performance) return;

  // Wait for page to fully load
  window.addEventListener('load', () => {
    const navigation = performance.getEntriesByType('navigation')[0] as PerformanceNavigationTiming;
    const paint = performance.getEntriesByType('paint');
    
    const metrics = {
      page_load_time: Math.round(navigation.loadEventEnd - navigation.loadEventStart),
      dom_content_loaded: Math.round(navigation.domContentLoadedEventEnd - navigation.domContentLoadedEventStart),
      first_contentful_paint: paint.find(p => p.name === 'first-contentful-paint')?.startTime || 0,
      largest_contentful_paint: 0, // Will be updated by observer
    };

    // Track LCP (Largest Contentful Paint)
    if ('PerformanceObserver' in window) {
      const observer = new PerformanceObserver((list) => {
        const entries = list.getEntries();
        const lastEntry = entries[entries.length - 1];
        metrics.largest_contentful_paint = Math.round(lastEntry.startTime);
        
        trackSEOEvent('page_performance', metrics);
      });
      
      observer.observe({ entryTypes: ['largest-contentful-paint'] });
    }
  });
};

// JSON-LD structured data helper
export const addStructuredData = (data: object) => {
  if (typeof document === 'undefined') return;

  const script = document.createElement('script');
  script.type = 'application/ld+json';
  script.text = JSON.stringify(data);
  document.head.appendChild(script);
};

// Clean up old structured data (useful for SPA routing)
export const removeStructuredData = () => {
  if (typeof document === 'undefined') return;

  const scripts = document.querySelectorAll('script[type="application/ld+json"]');
  scripts.forEach(script => {
    if (script.parentNode) {
      script.parentNode.removeChild(script);
    }
  });
};

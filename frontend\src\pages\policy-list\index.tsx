import Preloader from "@/components/common/Preloader";
import "./policyList.css";
import LightFooter from "@/shared/LightFooter";
import FilterSidebar from "@/components/FilterSidebar";
import { ArrowUpRight, RefreshCw } from "lucide-react";
import PdfDownloadButton from "@/pages/policy-list/PdfDownloadButton.tsx";
import { format } from "date-fns";
import { usePoliciesQuery } from "@/hooks/policy.query.ts";
import { useStudiesQuery } from "@/hooks/study.query";
import { useState } from "react";
import { Policy } from "@/types/types";
import { Study } from "@/store/scheduleEventState";
import DataTable, { Column } from '@/components/common/DataTable';

export default function PolicyList() {
    const { data, isLoading, isError, error, refetch } = usePoliciesQuery();
    const { data: studies } = useStudiesQuery();
    const [selectedStudy, setSelectedStudy] = useState<string>("");

    // Transform studies into filter format
    const studyFilters = studies?.map((study: Study) => ({
        id: study.uuid,
        label: study.name,
        count: data?.results?.filter(policy => policy.study?.uuid === study.uuid).length || 0
    })) || [];

    const handleStudyChange = (studyId: string) => {
        setSelectedStudy(studyId === selectedStudy ? "" : studyId);
    };

    const handleRedirect = (policyUuid: string) => {
        window.location.href = `/policy-details/${policyUuid}`;
    };

    // Filter policies based only on study
    const filteredPolicies = data?.results?.filter(policy => {
        const matchesStudy = selectedStudy === "" || policy.study?.uuid === selectedStudy;
        return matchesStudy;
    });

    if (isLoading) {
        return <Preloader />;
    }

    if (isError) return (
        <div className="main-content bg-light-4">
            <div className="content-wrapper js-content-wrapper">
                <div className="dashboard__content bg-light-4">
                    <div className="container-fluid px-0">
                        <div className="error-container">
                            <h2>Error Loading Policies</h2>
                            <p>{(error as Error).message}</p>
                            <button className="refresh-button" onClick={() => refetch()}>
                                <RefreshCw size={16} /> Try Again
                            </button>
                        </div>
                    </div>
                </div>
                <LightFooter />
            </div>
        </div>
    );

    const policyColumns: Array<Column<Policy> | { key: string; header: string; render: (value: unknown, row?: Policy) => React.ReactNode }> = [
        {
            key: 'title',
            header: 'Policy Name',
            sortable: true,
        },
        {
            key: 'created_at',
            header: 'Date Created',
            sortable: true,
            render: (value: unknown) => typeof value === 'string' ? format(new Date(value), 'dd/MM/yyyy') : '',
        },
        {
            key: 'actions',
            header: 'Action',
            render: (_value: unknown, row?: Policy) => {
                if (!row) return null;
                const attachUrl = row.attach_content || '';
                const fileName = attachUrl && typeof attachUrl === 'string'
                    ? attachUrl.split('/').pop() || row.title
                    : row.title;
                return (
                    <div className="policy-actions">
                        <button
                            onClick={() => handleRedirect(row.uuid)}
                            className="arrowUp"
                            title="View policy details"
                        >
                            <ArrowUpRight size={18} />
                        </button>
                        {attachUrl && typeof attachUrl === 'string' ? (
                            <PdfDownloadButton
                                fileUrl={attachUrl}
                                fileName={fileName}
                            />
                        ) : (
                            <span>No file</span>
                        )}
                    </div>
                );
            },
        },
    ];

    return (
        <div className="main-content bg-light-4">
            <div className="content-wrapper js-content-wrapper">
                <div className="dashboard__content bg-light-4" style={{ backgroundColor: "white" }}>
                    <div className="container-fluid px-0">
                        <div className="row y-gap-30">
                            <div className="col-xl-3 col-lg-4">
                                <FilterSidebar
                                    title="Filter by Study"
                                    categories={studyFilters}
                                    selectedCategories={selectedStudy ? [selectedStudy] : []}
                                    onCategoryChange={handleStudyChange}
                                />
                            </div>

                            <div className="col-xl-9 col-lg-8">
                                <div className="policy-title"><h1>Policy List</h1></div>
                                {/* <div className="policy-title"><h6>We're on a mission to deliver engaging, curated courses at a reasonable price.</h6></div> */}

                                {/* Search bar removed */}

                                <div style={{ marginTop: "30px" }}>
                                    {isError && (
                                        <div className="error-message">
                                            Error loading policies. Please try again.
                                        </div>
                                    )}

                                    <div className="policy-table-container">
                                        {filteredPolicies?.length === 0 ? (
                                            <div className="no-results">
                                                <p>No policies found.</p>
                                            </div>
                                        ) : (
                                            <DataTable<Policy>
                                                data={filteredPolicies || []}
                                                columns={policyColumns as Column<Policy>[]}
                                                noDataMessage="No policies found."
                                                defaultItemsPerPage={10}
                                            />
                                        )}
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <br /><br /><br />
                </div>
            </div>
            <LightFooter />
        </div>
    );
}

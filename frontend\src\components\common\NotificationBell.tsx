import { useState, useEffect, useRef } from 'react';
import { MessageSquare } from 'lucide-react';
import { useNavigate } from 'react-router-dom';
import { useQuery, useQueryClient } from '@tanstack/react-query';
import api from '@/services/api'; // Use your custom api instance

interface Notification {
  uuid: string;
  chat_uuid: string;
  chat_subject: string;
  message: {
    content: string;
    sender: {
      name: string;
      role: string;
    };
    created_at: string;
  };
  is_read: boolean;
}

const NotificationBell = () => {
  const [isOpen, setIsOpen] = useState(false);
  const [notifications, setNotifications] = useState<Notification[]>([]);
  const dropdownRef = useRef<HTMLDivElement>(null);
  const navigate = useNavigate();
  const queryClient = useQueryClient();

  // Fetch unread count
  const { data: unreadCount = 0 } = useQuery({
    queryKey: ['notifications', 'unread-count'],
    queryFn: async () => {
      try {
        const response = await api.get('/live-chat/notifications/unread_count/'); // Use api and correct path
        return response.data.count ?? 0;
      } catch (error) {
        console.error('Error fetching unread count:', error);
        return 0;
      }
    },
    refetchInterval: 30000, // Refetch every 30 seconds
  });

  // Fetch notifications
  const { data: notificationsData } = useQuery({
    queryKey: ['notifications'],
    queryFn: async () => {
      const response = await api.get('/live-chat/notifications/'); // Use api and correct path
      return response.data;
    },
    refetchInterval: 30000, // Refetch every 30 seconds
  });

  useEffect(() => {
    if (notificationsData) {
      setNotifications(notificationsData);
    }
  }, [notificationsData]);

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setIsOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  // WebSocket connection for real-time notifications
  useEffect(() => {
    // Get the base URL and convert to WebSocket protocol
    const baseUrl = window.location.origin;
    const wsProtocol = baseUrl.startsWith('https') ? 'wss' : 'ws';
    const wsBaseUrl = baseUrl.replace(/^https?/, wsProtocol);
    const ws = new WebSocket(`${wsBaseUrl}/ws/notifications/`);

    ws.onopen = () => {
      console.log('WebSocket connection established');
    };

    ws.onerror = (error) => {
      console.error('WebSocket error:', error);
    };

    ws.onclose = (event) => {
      console.log('WebSocket connection closed:', event.code, event.reason);
      // Attempt to reconnect after a delay
      setTimeout(() => {
        console.log('Attempting to reconnect WebSocket...');
        // The cleanup function will handle the old connection
      }, 5000);
    };

    ws.onmessage = (event) => {
      try {
        const data = JSON.parse(event.data);
        if (data.type === 'notification') {
          // Add new notification to the list
          setNotifications(prev => [data.notification, ...prev]);
          // Refetch unread count
          queryClient.invalidateQueries({ queryKey: ['notifications', 'unread-count'] });
        }
      } catch (error) {
        console.error('Error parsing WebSocket message:', error);
      }
    };

    return () => {
      if (ws.readyState === WebSocket.OPEN) {
        ws.close();
      }
    };
  }, [queryClient]);

  const handleNotificationClick = async (notification: Notification) => {
    // Mark as read
    await api.post(`/live-chat/notifications/${notification.uuid}/mark_as_read/`); // Use api and correct path

    // Navigate to chat
    navigate(`/org/dashboard/live-chat/${notification.chat_uuid}`);

    // Close dropdown
    setIsOpen(false);

    // Refetch notifications and unread count
    queryClient.invalidateQueries({ queryKey: ['notifications'] });
    queryClient.invalidateQueries({ queryKey: ['notifications', 'unread-count'] });
  };

  const formatTime = (dateString: string) => {
    const date = new Date(dateString);
    const now = new Date();
    const diff = now.getTime() - date.getTime();

    // Less than 1 hour
    if (diff < 3600000) {
      const minutes = Math.floor(diff / 60000);
      return `${minutes}m ago`;
    }
    // Less than 24 hours
    if (diff < 86400000) {
      const hours = Math.floor(diff / 3600000);
      return `${hours}h ago`;
    }
    // More than 24 hours
    return date.toLocaleDateString();
  };

  return (
    <div className="notification-bell" ref={dropdownRef}>
      <div
        className="menu-icon-wrapper"
        onClick={() => navigate('/org/dashboard/live-chat')}
        tabIndex={0}
        role="button"
        aria-label="Go to chat"
        onKeyDown={(e) => {
          if (e.key === 'Enter' || e.key === ' ') navigate('/org/dashboard/live-chat');
        }}
        style={{ position: 'relative', cursor: 'pointer', margin: '0 10px' }}
      >
        <MessageSquare size={24} />
        {unreadCount > 0 && (
          <span className="notification-badge">{unreadCount}</span>
        )}
      </div>

      {isOpen && (
        <div className="notification-dropdown">
          <div className="notification-dropdown-header" style={{ fontWeight: 600, fontSize: '16px', padding: '12px 16px', borderBottom: '1px solid #f0f0f0', background: '#fff' }}>
            Messages
          </div>
          <div className="notification-header">
            <h3>Notifications</h3>
            {notifications.length > 0 && (
              <button
                className="mark-all-read"
                onClick={async () => {
                  await api.post('/live-chat/notifications/mark_all_as_read/'); // Use api and correct path
                  queryClient.invalidateQueries({ queryKey: ['notifications'] });
                  queryClient.invalidateQueries({ queryKey: ['notifications', 'unread-count'] });
                }}
              >
                Mark all as read
              </button>
            )}
          </div>

          <div className="notification-list">
            {notifications.length === 0 ? (
              <div className="no-notifications">
                No notifications
              </div>
            ) : (
              notifications.map((notification) => (
                <div
                  key={notification.uuid}
                  className={`notification-item ${!notification.is_read ? 'unread' : ''}`}
                  onClick={() => handleNotificationClick(notification)}
                >
                  <div className="notification-content">
                    <div className="notification-title">
                      {notification.chat_subject}
                    </div>
                    <div className="notification-message">
                      {notification.message.content}
                    </div>
                    <div className="notification-meta">
                      <span className="notification-sender">
                        {notification.message.sender.name}
                      </span>
                      <span className="notification-time">
                        {formatTime(notification.message.created_at)}
                      </span>
                    </div>
                  </div>
                </div>
              ))
            )}
          </div>
        </div>
      )}
    </div>
  );
};

export default NotificationBell;

import { ReactNode, useEffect } from 'react';
import { Navigate, useLocation } from 'react-router-dom';
import { useCurrentUserQuery } from '@/hooks/user.query';
import Preloader from './Preloader';
import inactivityTimer from '@/utils/inactivityTimer';
import { useKeycloak } from '@react-keycloak/web';

interface ProtectedRouteProps {
  children: ReactNode;
}

const ProtectedRoute = ({ children }: ProtectedRouteProps) => {
  const { keycloak } = useKeycloak();
  const { data: currentUser } = useCurrentUserQuery();
  const location = useLocation();
  const basePathOrg = "/org/dashboard";
  const basePathPatient = "/patient";
  const basePathSponsor = "/sponsor";
  const hospitalUuid = !currentUser?.is_superuser ? (currentUser?.hospital?.uuid || null) : null;

  // Define allowed paths for superuser
  const superuserAllowedPaths = [
    `${basePathOrg}/analytics`,
    `${basePathOrg}/hospitals`,
    `${basePathOrg}/add-hospital`,
    `${basePathOrg}/hospital-details`,
    `${basePathOrg}/sponsors`,
    `${basePathOrg}/sponsorOrg-details`,
    `${basePathOrg}/profile`,
    `${basePathOrg}/add-sponsor`,
    `${basePathOrg}/pending-request`,
    `${basePathOrg}/live-chat`
  ];

  // Start the inactivity timer when the user is authenticated
  useEffect(() => {
    // Only start the timer if the user is authenticated
    if (currentUser && keycloak) {
      // Start tracking inactivity and pass keycloak instance
      inactivityTimer.start(keycloak);

      // Clean up when component unmounts
      return () => {
        inactivityTimer.stop();
      };
    }
  }, [currentUser, keycloak]);

  // If not authenticated, trigger Keycloak login
  if (!keycloak.authenticated) {
    keycloak.login();
    return <Preloader />;
  }
  if (!currentUser) {
    return <Preloader />;
  }
  
  

  // Check if the current path is under /patient-clinical
  const isPatientRoute = location.pathname.startsWith(basePathPatient);
  const isSponsorRoute = location.pathname.startsWith(basePathSponsor);
  const isOrgRoute = location.pathname.includes('org');

  // Redirect superusers if they try to access any non-org path
  if (currentUser?.is_superuser && !isOrgRoute) {
    return <Navigate to={`${basePathOrg}/analytics`} replace />;
  }

  if (currentUser?.user_type === "patient") {
    if (!isPatientRoute) {
      return <Navigate to={basePathPatient} replace />;
    }
    return <>{children}</>;
  } else if (currentUser?.user_type === "sponsor") {
    // Redirect sponsor users to a sponsor dashboard or homepage
    if (!isSponsorRoute) {
      return <Navigate to={basePathSponsor} replace />;
    }
    return <>{children}</>;
  } else {
    // Non-patient, non-sponsor users (staff, admin, superuser)
    if (currentUser.is_superuser) {
      // Check if the current path is allowed for superuser
      const isAllowedPath = superuserAllowedPaths.some(allowedPath => 
        location.pathname.startsWith(allowedPath)
      );

      if (!isAllowedPath) {
        return <Navigate to={`${basePathOrg}/analytics`} replace />;
      }
    }

    if (location.pathname === basePathOrg) {
      if (currentUser.is_superuser) {
        return <Navigate to={`${basePathOrg}/analytics`} replace />;
      } else if (currentUser.is_hospital_admin && currentUser.hospital?.uuid) {
        return <Navigate to={`${basePathOrg}/hospital-dashboard`} replace />;
      } else if (currentUser.is_admin && currentUser.department?.uuid) {
        return <Navigate to={`${basePathOrg}/admin-department/${currentUser.department.uuid}/`} replace />;
      } else if (currentUser.is_staff && currentUser.department?.uuid) {
        return <Navigate to={`${basePathOrg}/my-hospital/${hospitalUuid}/`} replace />;
      } else {
        return <Navigate to={`${basePathOrg}/policy`} replace />;
      }
    }
  
    return <>{children}</>;
  }
};

export default ProtectedRoute;

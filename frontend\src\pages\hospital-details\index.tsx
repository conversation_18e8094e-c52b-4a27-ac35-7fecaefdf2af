import "./hospital-details.css";
import { useEffect, useState } from "react";
import { useParams } from "react-router-dom";

import HospitalDetails from "./HospitalDetails";
import HospitalAdmin from "./HospitalAdmin";

import { User } from "lucide-react";

type TabName = "hospitalDetails" | "hospitalAdmin";

export default function HospitalDetailsAndAdmins() {
  const { uuid, tab } = useParams<{ uuid: string; tab?: TabName }>();
  const [activeTab, setActiveTab] = useState<TabName>(
    tab || "hospitalDetails"
  );

  useEffect(() => {
    if (tab && (tab === "hospitalDetails" || tab === "hospitalAdmin")) {
      setActiveTab(tab);
    }
  }, [tab]);


  return (
    <div className="hospital-details__content">
      <div className="hospital-card">
        <div className="hospital-details-header">
          <div className="hospital-details-title">
            <h1>
              <User size={24} style={{ marginRight: "10px" }} />
              Hospital Details and Admin
            </h1>
          </div>
        </div> 
        <div className="hospital-tabs-wrapper">
          <button
            className={`hospital-tab-button ${
              activeTab === "hospitalDetails" ? "active" : ""
            }`}
            onClick={() => setActiveTab("hospitalDetails")}
          >
            Hospital Details
          </button>
          <button
            className={`hospital-tab-button ${
              activeTab === "hospitalAdmin" ? "active" : ""
            }`}
            onClick={() => setActiveTab("hospitalAdmin")}
          >
            Hospital Administrator
          </button>
        </div>
        
        <div className="hospital-form-section">
            {activeTab === "hospitalDetails" && (
               <HospitalDetails uuid={uuid!} />
            )}

            {activeTab === "hospitalAdmin" && (
              <HospitalAdmin uuid={uuid!} />
              
            )}
        </div>
      </div>
    </div>
  );
}


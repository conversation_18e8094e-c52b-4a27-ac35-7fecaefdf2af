import api from "@/services/api";
import type {
  SystemNotification,
  NotificationType,
  NotificationPreferences,
  PushToken,
  NotificationTemplate,
  NotificationPaginatedResponse,
  UnreadCountResponse,
  MarkAllReadResponse,
  SendNotificationRequest,
  SendBatchNotificationRequest,
  RegisterDeviceRequest,
  UnregisterDeviceRequest,
  UpdatePreferencesRequest
} from "./notification.types";

// User Notifications
export const getUserNotifications = async (page?: number): Promise<NotificationPaginatedResponse<SystemNotification>> => {
  const url = page ? `/notification/notifications/?page=${page}` : '/notification/notifications/';
  const response = await api.get(url);
  return response.data;
};

export const getRecentNotifications = async (): Promise<SystemNotification[]> => {
  const response = await api.get('/notification/notifications/recent/');
  return response.data;
};

export const getUnreadCount = async (): Promise<UnreadCountResponse> => {
  const response = await api.get('/notification/notifications/unread_count/');
  return response.data;
};

export const getNotificationsByType = async (type: string): Promise<NotificationPaginatedResponse<SystemNotification>> => {
  const response = await api.get(`/notification/notifications/by_type/?type=${type}`);
  return response.data;
};

export const markNotificationAsRead = async (uuid: string): Promise<{ status: string }> => {
  const response = await api.post(`/notification/notifications/${uuid}/mark_read/`);
  return response.data;
};

export const markAllNotificationsAsRead = async (): Promise<MarkAllReadResponse> => {
  const response = await api.post('/notification/notifications/mark_all_read/');
  return response.data;
};

// Notification Types
export const getNotificationTypes = async (): Promise<NotificationType[]> => {
  const response = await api.get('/notification/types/');
  return response.data;
};

// User Preferences
export const getUserPreferences = async (): Promise<NotificationPreferences> => {
  const response = await api.get('/notification/preferences/');
  return response.data;
};

export const updateUserPreferences = async (preferences: NotificationPreferences): Promise<NotificationPreferences> => {
  const response = await api.put('/notification/preferences/', preferences);
  return response.data;
};

export const updateSpecificPreferences = async (preferences: UpdatePreferencesRequest): Promise<NotificationPreferences> => {
  const response = await api.post('/notification/preferences/update_preferences/', preferences);
  return response.data;
};

// Push Notification Tokens
export const getUserPushTokens = async (): Promise<PushToken[]> => {
  const response = await api.get('/notification/push-tokens/');
  return response.data;
};

export const registerDevice = async (deviceData: RegisterDeviceRequest): Promise<PushToken> => {
  const response = await api.post('/notification/push-tokens/register_device/', deviceData);
  return response.data;
};

export const unregisterDevice = async (deviceData: UnregisterDeviceRequest): Promise<{ status: string }> => {
  const response = await api.post('/notification/push-tokens/unregister_device/', deviceData);
  return response.data;
};

// Notification Templates
export const getNotificationTemplates = async (): Promise<NotificationTemplate[]> => {
  const response = await api.get('/notification/templates/');
  return response.data;
};

// Admin Endpoints (Admin Users Only)
export const sendNotificationToUser = async (notificationData: SendNotificationRequest): Promise<SystemNotification> => {
  const response = await api.post('/notification/admin/send_notification/', notificationData);
  return response.data;
};

export const sendBatchNotification = async (notificationData: SendBatchNotificationRequest): Promise<{ status: string }> => {
  const response = await api.post('/notification/admin/send_batch_notification/', notificationData);
  return response.data;
};

// WebSocket Connection Helper
export const getNotificationWebSocketUrl = (token: string): string => {
  // Use the same pattern as live chat WebSocket - use API base URL
  const apiBaseUrl = import.meta.env.VITE_API_BASE_URL || 'http://localhost:8000/api';
  const wsProtocol = window.location.protocol === 'https:' ? 'wss' : 'ws';
  const wsHost = new URL(apiBaseUrl).host;

  return `${wsProtocol}://${wsHost}/socket/notifications/?token=${token}`;
};

// Utility Functions
export const formatNotificationTime = (dateString: string): string => {
  const date = new Date(dateString);
  const now = new Date();
  const diff = now.getTime() - date.getTime();

  // Less than 1 minute
  if (diff < 60000) {
    return 'Just now';
  }
  // Less than 1 hour
  if (diff < 3600000) {
    const minutes = Math.floor(diff / 60000);
    return `${minutes}m ago`;
  }
  // Less than 24 hours
  if (diff < 86400000) {
    const hours = Math.floor(diff / 3600000);
    return `${hours}h ago`;
  }
  // Less than 7 days
  if (diff < 604800000) {
    const days = Math.floor(diff / 86400000);
    return `${days}d ago`;
  }
  // More than 7 days
  return date.toLocaleDateString();
};

export const getPriorityColor = (priority: string): string => {
  switch (priority) {
    case 'urgent':
      return '#FF2D55';
    case 'high':
      return '#FF9500';
    case 'normal':
      return '#37B7C3';
    case 'low':
      return '#8E8E93';
    default:
      return '#37B7C3';
  }
};

export const getPriorityIcon = (priority: string): string => {
  switch (priority) {
    case 'urgent':
      return '🚨';
    case 'high':
      return '⚠️';
    case 'normal':
      return '📢';
    case 'low':
      return 'ℹ️';
    default:
      return '📢';
  }
};

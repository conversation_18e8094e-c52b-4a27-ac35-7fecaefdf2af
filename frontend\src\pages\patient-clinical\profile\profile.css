.profile-container {
  padding: 20px;
  background-color: #f8f9fa;
  min-height: 100vh;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
}

/* Loading and Error States */
.profile-loading,
.profile-error {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 400px;
  color: #666666;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid #f3f3f3;
  border-top: 4px solid #4ECDC4;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 16px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Profile Header */
.profile-header {
  background: white;
  border-radius: 12px;
  padding: 32px;
  margin-bottom: 24px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.profile-header-content {
  display: flex;
  align-items: center;
  gap: 24px;
}

.profile-avatar {
  color: #4ECDC4;
  flex-shrink: 0;
}

.profile-header-info {
  flex: 1;
}

.profile-name {
  font-size: 28px;
  font-weight: 600;
  color: #333333;
  margin: 0 0 8px 0;
}

.profile-role {
  font-size: 16px;
  color: #666666;
  margin: 0 0 12px 0;
}

.profile-status {
  margin-bottom: 0;
}

.status-badge {
  padding: 4px 12px;
  border-radius: 16px;
  font-size: 12px;
  font-weight: 500;
  display: inline-block;
}

.status-active {
  background-color: #28a745;
  color: white;
}

.status-inactive {
  background-color: #dc3545;
  color: white;
}

.profile-actions {
  flex-shrink: 0;
}

.edit-actions {
  display: flex;
  gap: 12px;
}

.edit-actions .nurtify-button {
  display: flex;
  align-items: center;
  gap: 6px;
}

.profile-actions .nurtify-button {
  display: flex;
  align-items: center;
  gap: 6px;
}

/* Profile Content */
.profile-content {
  display: flex;
  flex-direction: column;
}

.profile-sections {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.profile-section {
  background: white;
  border-radius: 12px;
  padding: 24px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.section-header {
  margin-bottom: 20px;
}

.section-title {
  font-size: 20px;
  font-weight: 600;
  color: #333333;
  margin: 0;
  display: flex;
  align-items: center;
  gap: 8px;
}

.section-title::before {
  content: '';
  width: 4px;
  height: 20px;
  background-color: #4ECDC4;
  border-radius: 2px;
}

.section-content {
  padding: 0;
}

/* Info Grid */
.info-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 20px;
}

.info-item {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.info-label {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
  font-weight: 500;
  color: #666666;
}

.info-label svg {
  color: #4ECDC4;
  flex-shrink: 0;
}

.info-value {
  font-size: 16px;
  color: #333333;
  font-weight: 400;
  padding: 8px 0;
  min-height: 24px;
  display: flex;
  align-items: center;
}

/* Form Inputs */
.profile-input,
.profile-select {
  width: 100%;
  padding: 10px 12px;
  border: 1px solid #ddd;
  border-radius: 6px;
  font-size: 14px;
  color: #333333;
  background-color: white;
  transition: border-color 0.2s ease;
}

.profile-input:focus,
.profile-select:focus {
  outline: none;
  border-color: #4ECDC4;
  box-shadow: 0 0 0 2px rgba(78, 205, 196, 0.1);
}

.profile-input::placeholder {
  color: #999999;
}

.profile-select {
  cursor: pointer;
}

/* Responsive Design */
@media (max-width: 768px) {
  .profile-container {
    padding: 16px;
  }

  .profile-header {
    padding: 20px;
  }

  .profile-header-content {
    flex-direction: column;
    text-align: center;
    gap: 16px;
  }

  .profile-actions {
    width: 100%;
  }

  .edit-actions {
    justify-content: center;
    flex-wrap: wrap;
  }

  .profile-section {
    padding: 20px;
  }

  .info-grid {
    grid-template-columns: 1fr;
    gap: 16px;
  }

  .profile-name {
    font-size: 24px;
  }
}

@media (max-width: 480px) {
  .profile-header-content {
    gap: 12px;
  }

  .edit-actions {
    flex-direction: column;
    width: 100%;
  }

  .edit-actions .nurtify-button {
    width: 100%;
    justify-content: center;
  }

  .profile-actions .nurtify-button {
    width: 100%;
    justify-content: center;
  }
}

/* Animation for smooth transitions */
.profile-section {
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.profile-section:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.info-item {
  transition: all 0.2s ease;
}

.profile-input,
.profile-select {
  transition: all 0.2s ease;
}

.profile-input:hover,
.profile-select:hover {
  border-color: #bbb;
}

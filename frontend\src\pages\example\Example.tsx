import Preloader from "@/components/common/Preloader";
import useExampleQuery from "@/hooks/example.query";
import LightFooter from "@/shared/LightFooter";

const Example = () => {
  const { data, isLoading } = useExampleQuery();

  if (isLoading) {
    return <Preloader />;
  }
  
  return (
    <div className="main-content bg-light-4">
      <div className="content-wrapper js-content-wrapper">
          <div className="dashboard__content bg-light-4">
              <div className="container-fluid px-0">
                  <div className="row y-gap-30">
                      <div className="col-12">
                          <div className="row">
                              <div className="col-12">
                                  <div className="card">
                                      <div className="card-body">
                                          <h5 className="card-title">Example</h5>
                                          <p className="card-text">
                                              {data?.message}
                                          </p>
                                      </div>
                                  </div>
                              </div>

                          </div>
                      </div>
                  </div>
              </div>
          </div>
        
        <LightFooter />
      </div>
    </div>
  );
};

export default Example;

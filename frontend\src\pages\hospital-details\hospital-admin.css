.delete-btn {
  background: none;
  border: none;
  color: #ef4444;
  cursor: pointer;
  font-size: 16px;
}

.delete-btn:hover {
  color: #dc2626;
}

.user-management {
    padding: 20px;
    max-width: 800px;
    margin: 0 auto;
    font-family: 'Arial', sans-serif;
  }
  
  .team-members-section {
    background: #fff;
    border-radius: 8px;
    padding: 20px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  }
  
  
  
  .users-list {
    display: flex;
    flex-direction: column;
  }
  
  .user-row {
    display: flex;
    align-items: center;
    padding: 10px 0;
    border-bottom: 1px solid #eee;
  }
  
  .user-info {
    flex: 2;
    display: flex;
    /* align-items: center; */
    gap: 12px;
  }
  
  .user-initials {
    width: 32px;
    height: 32px;
    background-color: #e0e7ff;
    color: #6366f1;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 14px;
    font-weight: 600;
  }
  
  .user-name {
    font-size: 14px;
    font-weight: 500;
    margin: 0;
  }
  
  .user-email {
    font-size: 12px;
    color: #666;
    margin: 2px 0 0;
  }
  
  .user-status {
    flex: 1;
  }
  
  .status {
    font-size: 12px;
    padding: 4px 8px;
    border-radius: 12px;
    font-weight: 500;
  }
  
  .status.active {
    background-color: #d1fae5;
    color: #10b981;
  }
  
  .status.inactive {
    background-color: #e5e7eb;
    color: #6b7280;
  }
  
  .status.invited {
    background-color: #fef3c7;
    color: #d97706;
  }
  
  .user-role {
    flex: 1;
    position: relative;
  }
  
  .role-btn {
    background: none;
    border: none;
    font-size: 14px;
    color: #333;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 4px;
  }
  
  .dropdown-arrow {
    font-size: 10px;
  }
  
  .dropdown-menu {
    position: absolute;
    top: 100%;
    right: 0;
    background: #fff;
    border-radius: 4px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
    z-index: 10;
    min-width: 150px;
  }
  
  .dropdown-menu button {
    display: block;
    width: 100%;
    padding: 8px 12px;
    background: none;
    border: none;
    text-align: left;
    font-size: 14px;
    color: #333;
    cursor: pointer;
  }
  
  .dropdown-menu button:hover {
    background-color: #f5f5f5;
  }
  
  .user-actions {
    flex: 0.5;
    display: flex;
    justify-content: flex-end;
  }
  
  .delete-btn {
    background: none;
    border: none;
    color: #ef4444;
    cursor: pointer;
    font-size: 16px;
  }
  
  .delete-btn:hover {
    color: #dc2626;
  }
  /* #here */
  .user-management {
    padding: 20px;
    max-width: 1200px;
    margin: 0 auto;
  }
  
  .team-members-section {
    background: white;
    border-radius: 12px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
    overflow: hidden;
  }
  
 
  .add-member-btn {
    padding: 10px 20px;
    background: #37B7C3;
    color: white;
    border: none;
    border-radius: 6px;
    cursor: pointer;
    font-weight: 500;
    transition: background 0.2s;
  }
  
  .add-member-btn:hover {
    background: #2d919a;
  }
  
  .add-member-btn.active {
    background: #dc3545;
  }
  
  .add-member-btn.active:hover {
    background: #b02a37;
  }
  
  .add-member-section {
    padding: 20px;
    background: #f8f9fa;
    border-bottom: 1px solid #e9ecef;
    transition: all 0.3s ease;
  }
  
  .add-mode-toggle {
    display: flex;
    gap: 12px;
    margin-bottom: 16px;
  }
  
  .toggle-btn {
    padding: 8px 16px;
    border: 1px solid #dee2e6;
    border-radius: 6px;
    background: white;
    cursor: pointer;
    font-weight: 500;
    color: #495057;
    transition: all 0.2s;
  }
  
  .toggle-btn:hover {
    background: #f1f3f5;
  }
  
  .toggle-btn.active {
    background: #007bff;
    color: white;
    border-color: #007bff;
  }
  
  .new-admin-form {
    display: flex;
    gap: 12px;
    align-items: center;
  }
  
  .existing-admin-form {
    position: relative;
  }
  
  .enhanced-input {
    width: 100%;
    padding: 10px 12px;
    border: 1px solid #ced4da;
    border-radius: 6px;
    font-size: 14px;
    transition: border-color 0.2s;
  }
  
  .enhanced-input:focus {
    outline: none;
    border-color: #007bff;
    box-shadow: 0 0 0 3px rgba(0, 123, 255, 0.1);
  }
  
  .action-btn {
    padding: 10px 20px;
    border: none;
    border-radius: 6px;
    cursor: pointer;
    font-weight: 500;
    transition: all 0.2s;
  }
  
  .action-btn.primary {
    background: #37B7C3;
    color: white;
  }
  
  .action-btn.primary:hover {
    background: #2d919a;
  }
  
  .action-btn:disabled {
    background: #ced4da;
    cursor: not-allowed;
  }
  
  .search-results-above {
    position: absolute;
    bottom: 100%;
    left: 0;
    right: 0;
    background: white;
    border: 1px solid #ced4da;
    border-radius: 6px;
    max-height: 240px;
    overflow-y: auto;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    z-index: 10;
    margin-bottom: 8px;
  }
  
  .search-result-item {
    padding: 10px 12px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    cursor: pointer;
    border-bottom: 1px solid #f0f0f0;
  }
  
  .search-result-item:last-child {
    border-bottom: none;
  }
  
  .search-result-item:hover {
    background: #f8f9fa;
  }
  
  .user-info-result {
    display: flex;
    align-items: center;
    gap: 8px;
  }
  
  .user-info-result .initials {
    width: 32px;
    height: 32px;
    background: #e9ecef;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 12px;
    font-weight: 500;
  }
  
  .user-info-result .name {
    font-weight: 500;
    color: #1a1a1a;
  }
  
  .user-info-result .email {
    display: block;
    color: #6c757d;
    font-size: 12px;
  }
  
  .add-btn {
    padding: 6px 12px;
    background: #28a745;
    color: white;
    border: none;
    border-radius: 4px;
    font-size: 12px;
    cursor: pointer;
  }
  
  .add-btn:hover {
    background: #218838;
  }
  
  .no-results {
    padding: 12px;
    color: #6c757d;
    text-align: center;
    font-size: 14px;
  }
  
  .users-list {
    padding: 20px;
  }
  
  .user-row {
    padding: 12px 0;
    border-bottom: 1px solid #f0f0f0;
  }

  .user-actions {
    display: flex;
    align-items: center;
    gap: 8px; /* Adds space between buttons */
  }
  
  .resend-btn {
    background-color: transparent;
    border: 1px solid #666;
    border-radius: 4px;
    padding: 4px 8px;
    font-size: 12px;
    color: #666;
    cursor: pointer;
    transition: all 0.2s ease;
    white-space: nowrap;
  }
  
  .resend-btn:hover {
    background-color: #f5f5f5;
    border-color: #444;
    color: #444;
  }
  
  .resend-btn:active {
    background-color: #e0e0e0;
  }
  
  .resend-btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
  }
  
  /* Ensure delete-btn matches the style if needed */
  .delete-btn {
    padding: 4px 8px; /* Match padding with resend-btn if desired */
    font-size: 12px;  /* Match font size if desired */
  }
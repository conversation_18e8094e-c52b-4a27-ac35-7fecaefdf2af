import React, { useState, useEffect } from 'react';
import { useParams } from 'react-router-dom';
import Preloader from "@/components/common/Preloader";
import LightFooter from "@/shared/LightFooter";
import { useGetFormByUuid, useAcceptForm, useRejectForm } from '@/hooks/form.query';
import { ArrowLeft, Tag, User, Calendar, List, CheckSquare, FileText, 
         Type, ToggleLeft, Grid, ChevronDown, FilePlus, Signature, 
         Sliders, Table as TableIcon, AlertTriangle, Edit, CheckCircle, XCircle } from 'lucide-react';
import './formpreview.css';
import NurtifyRadio from '@/components/NurtifyRadio';
import NurtifyCheckBox from '@/components/NurtifyCheckBox';
import NurtifyInput from '@/components/NurtifyInput';
import NurtifyTextArea from '@/components/NurtifyTextArea';
import NurtifyToggle from '@/components/NurtifyToggle';
import NurtifySelect from '@/components/NurtifySelect';
import NurtifyRange from '@/components/NurtifyRange';
import { GeneratePdfButton } from '../patient-form/GeneratePdfButton';

// Interface for section and question structure
interface Question {
  id: number;
  type: string;
  questionName?: string;
  required?: boolean;
  nonClinical?: boolean;
  answers?: string[];
  options?: string[];
  range?: { min: string; max: string; currentValue: string };
  expression?: string;
  table?: { columns?: string[] };
}

interface Section {
  id: number;
  name: string;
  description?: string;
  questions: Question[];
}

interface FormStructure {
  sections: Section[];
}

const FormPreview: React.FC = () => {
  const { formUuid } = useParams<{ formUuid: string }>();
  const [sections, setSections] = useState<Section[]>([]);
  const [showConfirmDialog, setShowConfirmDialog] = useState(false);
  const [actionType, setActionType] = useState<'accept' | 'reject' | null>(null);
  
  const { data: formData, isLoading, error } = useGetFormByUuid(formUuid || '');
  const acceptFormMutation = useAcceptForm();
  const rejectFormMutation = useRejectForm();

  useEffect(() => {
    if (formData && formData.active_version) {
      console.log('Form data received:', formData);
      let formStructure: FormStructure | null = null;
      
      try {
        if (formData.active_version.form_structure) {
          console.log('Using form_structure:', formData.active_version.form_structure);
          if (typeof formData.active_version.form_structure === 'string') {
            formStructure = JSON.parse(formData.active_version.form_structure);
          } else {
            formStructure = formData.active_version.form_structure as unknown as FormStructure;
          }
        } 
        else if (formData.active_version.structure) {
          console.log('Falling back to structure field');
          if (typeof formData.active_version.structure === 'string') {
            formStructure = JSON.parse(formData.active_version.structure);
          } else {
            formStructure = formData.active_version.structure;
          }
        }
        
        // Add detailed debugging to identify the exact issue
        console.log('Form structure before validation:', formStructure);
        
        // Ensure we have valid sections data before setting state
        if (formStructure && Array.isArray(formStructure.sections) && formStructure.sections.length > 0) {
          console.log('Valid sections found:', formStructure.sections);
          setSections(formStructure.sections);
        } else {
          console.error('No valid sections found in form structure:', formStructure);
          setSections([]);
        }
      } catch (e) {
        console.error("Error processing form structure:", e);
        setSections([]);
      }
    }
  }, [formData]);

  // Get formatted creation date
  const formatDate = (dateString?: string) => {
    if (!dateString) return '';
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric', 
      month: 'long', 
      day: 'numeric'
    });
  };

  // Helper function to get question type display name
  const getQuestionTypeDisplay = (type?: string) => {
    if (!type) return 'Unknown';
    
    const typeMap: {[key: string]: string} = {
      'short-text': 'Short Text',
      'long-text': 'Long Text',
      'single-choice': 'Single Choice',
      'multiple-choice': 'Multiple Choice',
      'boolean': 'Yes/No',
      'multiple-text-boxes': 'Multiple Inputs',
      'dropdown': 'Dropdown',
      'multi-select-dropdown': 'Multi-select Dropdown',
      'attach-file': 'File Attachment',
      'signature': 'Signature',
      'range': 'Range Slider',
      'table': 'Table',
      'Expression': 'Expression'
    };
    
    return typeMap[type] || type.charAt(0).toUpperCase() + type.slice(1).replace(/-/g, ' ');
  };

  // Get question icon based on type
  const getQuestionIcon = (type?: string) => {
    switch(type) {
      case 'single-choice':
        return <CheckSquare size={20} />;
      case 'multiple-choice':
        return <List size={20} />;
      case 'boolean':
        return <ToggleLeft size={20} />;
      case 'short-text':
        return <Type size={20} />;
      case 'long-text':
        return <FileText size={20} />;
      case 'multiple-text-boxes':
        return <Grid size={20} />;
      case 'dropdown':
      case 'multi-select-dropdown':
        return <ChevronDown size={20} />;
      case 'attach-file':
        return <FilePlus size={20} />;
      case 'signature':
        return <Signature size={20} />;
      case 'range':
        return <Sliders size={20} />;
      case 'table':
        return <TableIcon size={20} />;
      case 'Expression':
        return <FileText size={20} />;
      default:
        return <List size={20} />;
    }
  };

  const renderQuestionPreview = (question: Question) => {
    console.log('Rendering question:', question);
    
    switch(question.type) {
      case 'short-text':
        return (
          <div className="question-preview-content">
            <NurtifyInput
              type="text"
              placeholder="Sample text input"
              disabled={true}
            />
          </div>
        );
        
      case 'long-text':
        return (
          <div className="question-preview-content">
            <NurtifyTextArea
              rows={3}
              placeholder="Sample long text..."
              disabled={true}
            />
          </div>
        );
        
      case 'single-choice':
        return (
          <div className="question-preview-content">
            {question.answers && question.answers.map((answer, index) => (
              <div key={index} className="question-preview-option">
                <NurtifyRadio
                  label={answer}
                  name={`preview-radio-${question.id}`}
                  value={answer}
                  checked={index === 0}
                />
              </div>
            ))}
            {(!question.answers || question.answers.length === 0) && (
              <div className="question-preview-no-options">
                <p>No answer options provided</p>
              </div>
            )}
          </div>
        );
        
      case 'multiple-choice':
        return (
          <div className="question-preview-content">
            {question.answers && question.answers.map((answer, index) => (
              <div key={index} className="question-preview-option">
                <NurtifyCheckBox
                  label={answer}
                  id={`preview-checkbox-${question.id}-${index}`}
                  value={answer}
                  checked={index === 0}
                  
                />
              </div>
            ))}
          </div>
        );
        
      case 'boolean':
        return (
          <div className="question-preview-content">
            <div className="question-preview-toggle">
              <NurtifyToggle 
                id={`preview-toggle-${question.id}`} 
                name={`preview-toggle-${question.id}`}
                labels={["Yes", "No"]}
                
              />
            </div>
          </div>
        );
        
      case 'dropdown':
        return (
          <div className="question-preview-content">
            <NurtifySelect
              name={`preview-select-${question.id}`}
              options={question.options 
                ? question.options.map(opt => ({ value: opt, label: opt }))
                : [{ value: "sample", label: "Sample Option" }]
              }
              disabled={true}
            />
          </div>
        );
        
      case 'multi-select-dropdown':
        return (
          <div className="question-preview-content">
            <div className="preview-multi-select">
              {question.options && question.options.slice(0, 2).map((opt, idx) => (
                <div key={idx} className="preview-selected-option">
                  {opt}
                </div>
              ))}
            </div>
          </div>
        );
        
      case 'range':
        return (
          <div className="question-preview-content">
            <div className="range-preview-container">
              <NurtifyRange 
                min={question.range?.min ? parseInt(question.range.min) : 0}
                max={question.range?.max ? parseInt(question.range.max) : 100}
                
              />
              <div className="range-values">
                <span>{question.range?.min || '0'}</span>
                <span>{question.range?.max || '100'}</span>
              </div>
            </div>
          </div>
        );
        
      case 'table':
        return (
          <div className="question-preview-content">
            <div className="table-preview">
              <table>
                <thead>
                  <tr>
                    {question.table?.columns 
                      ? question.table.columns.map((col, idx) => <th key={idx}>{col}</th>)
                      : [1, 2].map((idx) => <th key={idx}>Column {idx}</th>)
                    }
                  </tr>
                </thead>
                <tbody>
                  <tr>
                    {question.table?.columns 
                      ? question.table.columns.map((_, idx) => <td key={idx}>Sample</td>)
                      : [1, 2].map((idx) => <td key={idx}>Sample</td>)
                    }
                  </tr>
                </tbody>
              </table>
            </div>
          </div>
        );
        
      case 'attach-file':
        return (
          <div className="question-preview-content">
            <div className="file-attachment-preview">
              <FilePlus size={24} />
              <span>Click to attach file</span>
            </div>
          </div>
        );
        
      case 'signature':
        return (
          <div className="question-preview-content">
            <div className="signature-preview">
              <div className="signature-placeholder">
                <Signature size={24} />
                <span>Sign here</span>
              </div>
              <div className="signature-fields">
                <NurtifyInput 
                  type="text" 
                  placeholder="Full Name" 
                  disabled={true} 
                />
                <NurtifyInput 
                  type="text" 
                  placeholder="Date" 
                  disabled={true} 
                />
              </div>
            </div>
          </div>
        );
        
      case 'multiple-text-boxes':
        return (
          <div className="question-preview-content">
            <div className="multiple-text-boxes-preview">
              {question.options ? (
                question.options.map((opt, idx) => (
                  <div key={idx} className="text-box-item">
                    <label>{opt}</label>
                    <NurtifyInput 
                      type="text" 
                      placeholder="Value" 
                      disabled={true} 
                    />
                  </div>
                ))
              ) : (
                <>
                  <div className="text-box-item">
                    <label>Parameter 1</label>
                    <NurtifyInput 
                      type="text" 
                      placeholder="Value" 
                      disabled={true} 
                    />
                  </div>
                  <div className="text-box-item">
                    <label>Parameter 2</label>
                    <NurtifyInput 
                      type="text" 
                      placeholder="Value" 
                      disabled={true} 
                    />
                  </div>
                </>
              )}
            </div>
          </div>
        );
          
      case 'Expression':
        return (
          <div className="question-preview-content">
            <div className="expression-preview">
              <FileText size={20} />
              <span>Expression: {question.expression || "Sample expression"}</span>
            </div>
          </div>
        );
          
      default:
        return (
          <div className="question-preview-content">
            <p className="unknown-question-type">Unknown question type: {question.type}</p>
          </div>
        );
    }
  };

  const handleAcceptForm = () => {
    setActionType('accept');
    setShowConfirmDialog(true);
  };

  const handleRejectForm = () => {
    setActionType('reject');
    setShowConfirmDialog(true);
  };

  const confirmAction = async () => {
    if (!formUuid || !actionType) return;

    try {
      if (actionType === 'accept') {
        await acceptFormMutation.mutateAsync(formUuid);
        alert('Form accepted successfully!');
      } else if (actionType === 'reject') {
        await rejectFormMutation.mutateAsync(formUuid);
        alert('Form rejected successfully!');
      }
      setShowConfirmDialog(false);
      setActionType(null);
    } catch (error) {
      console.error('Error processing form action:', error);
      alert('Error processing form action. Please try again.');
    }
  };

  const cancelAction = () => {
    setShowConfirmDialog(false);
    setActionType(null);
  };

  return (
    <div className="main-content bg-light-4">
      <Preloader />
      <div className="content-wrapper js-content-wrapper">
        <div className="dashboard__content bg-light-4">
          <div className="container-fluid px-0">
            {isLoading ? (
              <div className="form-preview-loading text-center py-5">
                <div className="spinner-border text-primary" role="status">
                  <span className="visually-hidden">Loading...</span>
                </div>
                <p className="mt-2">Loading form preview...</p>
              </div>
            ) : error ? (
              <div className="form-preview-error text-center py-5">
                <AlertTriangle size={48} color="#E53935" />
                <p className="mt-2">Error loading form: {(error as Error).message}</p>
                <button onClick={() => window.history.back()} className="btn-nurtify mt-3">
                  <ArrowLeft size={16} className="mr-2" /> Go Back
                </button>
              </div>
            ) : (
              <div className="form-preview-container">
                <div className="form-preview-header">
                  <div className="d-flex justify-content-between">
                    <button onClick={() => window.history.back()} className="form-preview-back-btn">
                      <ArrowLeft size={16} /> Go Back
                    </button>
                    <div className="form-preview-back-btn">
                      <GeneratePdfButton uuid={formUuid || ''}  />                  
                    </div>
                  </div>
                  
                  <h1 className="form-preview-title">{formData?.name || "Untitled Form"}</h1>
                  <div className="form-preview-meta">
                    {formData?.active_version?.description && (
                      <div className="form-preview-description">
                        {formData.active_version.description}
                      </div>
                    )}
                    
                    <div className="form-preview-details">
                      <div className="form-preview-detail">
                        <User size={16} />
                        <span>Created by: {formData?.user?.first_name || ""} {formData?.user?.last_name || formData?.user?.identifier || "Unknown"}</span>
                      </div>
                      <div className="form-preview-detail">
                        <Calendar size={16} />
                        <span>Created on: {formatDate(formData?.created_at)}</span>
                      </div>
                      <div className="form-preview-detail">
                        <Edit size={16} />
                        <span>Version: {formData?.active_version?.version || "1.0"}</span>
                      </div>
                      {formData?.active_version?.categories && formData.active_version.categories.length > 0 && (
                        <div className="form-preview-tags">
                          <Tag size={16} />
                          <div className="tags-list">
                            {Array.isArray(formData.active_version.categories) ? 
                              formData.active_version.categories.map((category, i) => (
                                <span key={i} className="form-preview-tag">{category}</span>
                              )) : 
                              <span className="form-preview-tag">{formData.active_version.categories}</span>
                            }
                          </div>
                        </div>
                      )}
                    </div>
                  </div>
                </div>

                <div className="form-preview-content">
                  <h2 className="form-preview-sections-title">Form Structure</h2>
                  
                  {sections.length > 0 ? (
                    <div className="form-preview-sections">
                      {sections.map((section) => (
                        <div key={section.id} className="form-preview-section">
                          <h3 className="section-title">{section.name || `Section ${section.id}`}</h3>
                          {section.description && (
                            <p className="section-description">{section.description}</p>
                          )}
                          
                          <div className="form-preview-questions">
                            {section.questions && section.questions.length > 0 ? (
                              section.questions.map((question) => (
                                question.type !== 'Expression' && (
                                  <div key={question.id} className="form-preview-question">
                                    <div className="question-header">
                                      <div className="question-icon">
                                        {getQuestionIcon(question.type)}
                                      </div>
                                      <div className="question-content">
                                        <h4 className="question-name">
                                          {question.questionName || `Question ${question.id}`}
                                          {question.required && <span className="question-required">*</span>}
                                        </h4>
                                        <div className="question-type">
                                          <span className="badge">{getQuestionTypeDisplay(question.type)}</span>
                                          {question.nonClinical && <span className="badge badge-non-clinical">Non-Clinical</span>}
                                        </div>
                                      </div>
                                    </div>
                                    
                                    <div className="question-preview">
                                      {renderQuestionPreview(question)}
                                    </div>
                                  </div>
                                )
                              ))
                            ) : (
                              <p className="no-questions">No questions in this section</p>
                            )}
                          </div>
                        </div>
                      ))}
                    </div>
                  ) : (
                    <div className="no-sections">
                      <p>No sections found in this form or form structure is invalid.</p>
                    </div>
                  )}
                </div>
                
                {/* Accept/Reject Actions */}
                <div className="form-preview-actions">
                  <button 
                    className="btn-nurtify-lighter"
                    onClick={() => window.history.back()}
                  >
                    <ArrowLeft size={16} /> Back to Forms
                  </button>
                  <div className="action-buttons">
                    <button 
                      className="btn-accept"
                      onClick={handleAcceptForm}
                      disabled={acceptFormMutation.isPending || rejectFormMutation.isPending}
                    >
                      <CheckCircle size={16} />
                      {acceptFormMutation.isPending ? 'Accepting...' : 'Accept Form'}
                    </button>
                    <button 
                      className="btn-reject"
                      onClick={handleRejectForm}
                      disabled={acceptFormMutation.isPending || rejectFormMutation.isPending}
                    >
                      <XCircle size={16} />
                      {rejectFormMutation.isPending ? 'Rejecting...' : 'Reject Form'}
                    </button>
                  </div>
                </div>

                {/* Confirmation Dialog */}
                {showConfirmDialog && (
                  <div className="confirmation-dialog-backdrop">
                    <div className="confirmation-dialog">
                      <h3>Confirm {actionType === 'accept' ? 'Acceptance' : 'Rejection'}</h3>
                      <p>
                        Are you sure you want to {actionType} this form? 
                        This action cannot be undone.
                      </p>
                      <div className="confirmation-dialog-actions">
                        <button 
                          className="btn-cancel"
                          onClick={cancelAction}
                        >
                          Cancel
                        </button>
                        <button 
                          className={`btn-confirm ${actionType === 'accept' ? 'btn-confirm-accept' : 'btn-confirm-reject'}`}
                          onClick={confirmAction}
                        >
                          {actionType === 'accept' ? 'Accept' : 'Reject'}
                        </button>
                      </div>
                    </div>
                  </div>
                )}
              </div>
            )}
          </div>
        </div>
        <LightFooter />
      </div>
    </div>
  );
};

export default FormPreview;

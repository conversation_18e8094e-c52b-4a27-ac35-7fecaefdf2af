import { Facebook, Twitter, Linkedin } from 'lucide-react';

interface SocialsProps {
  componentsClass?: string;
  textSize?: string;
}

export default function Socials({ componentsClass }: SocialsProps) {
  // Temporary social media links until data file is created
  const socialMediaLinks = [
    {
      href: "https://www.facebook.com/nurtify",
      icon: Facebook
    },
    {
      href: "https://x.com/nurtifynews", 
      icon: Twitter
    },
    {
      href: "https://www.linkedin.com/company/nurtifyltd",
      icon: Linkedin
    }
  ];

  return (
    <>
      {socialMediaLinks.map((link, index) => (
        <a
          key={index}
          className={componentsClass || ""}
          href={link.href}
          target='__blank'
        >
          <link.icon size={20} className="text-white" />
        </a>
      ))}
    </>
  );
}

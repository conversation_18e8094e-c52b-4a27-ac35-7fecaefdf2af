import { useQuery } from '@tanstack/react-query';
import { EXAMPLE_KEYS } from './keys';

interface HelloResponse {
  message: string;
}

const useExampleQuery = () => {
  const { data, isLoading, error } = useQuery<HelloResponse, Error>({
    queryKey: [EXAMPLE_KEYS.GET_EXAMPLE],
    queryFn: async () => {
      const response = {
        data: {
          message: 'Hello World',
        },
      };
      return response.data;
    }
  });

  return { data, isLoading, error };
};

export default useExampleQuery;
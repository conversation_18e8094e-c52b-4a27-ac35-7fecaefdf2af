import React, { useState } from "react";
import { usePatientAndStaffPoliciesQuery } from '@/hooks/policy.query';
import PolicyCard from '@/components/PolicyCard';
import { Policy } from '@/types/types';
import "./resources.css";

const ResourcesSection: React.FC = () => {
  const { data: policiesData, isLoading, error } = usePatientAndStaffPoliciesQuery();
  const policies = policiesData?.policies || [];
  const [searchTerm, setSearchTerm] = useState("");

  // Filter policies based on search term
  const filteredPolicies = policies.filter((policy: Policy) =>
    policy.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
    policy.description.toLowerCase().includes(searchTerm.toLowerCase())
  );

  return (
    <div className="patclin-tab-content">
      <h2 className="patclin-section-title">Health Resources</h2>

      {/* Search Bar */}
      <div className="search-container">
        <input
          type="text"
          placeholder="Search policies..."
          value={searchTerm}
          onChange={(e) => setSearchTerm(e.target.value)}
          className="search-input"
        />
      </div>

      {/* Policies Section */}
      <div className="resources-container">
        <h3 className="resources-title">
          Patient <span className="text-highlight">Policies</span>
        </h3>

        <p className="resources-subtitle">
          Important policies and guidelines for your care
        </p>

        {isLoading && (
          <div className="loading-state">
            <p>Loading policies...</p>
          </div>
        )}

        {error && (
          <div className="error-state">
            <p>Error loading policies. Please try again later.</p>
          </div>
        )}

        {!isLoading && !error && filteredPolicies.length > 0 && (
          <div className="policies-grid">
            {filteredPolicies.map((policy: Policy) => (
              <PolicyCard key={policy.uuid} policy={policy} />
            ))}
          </div>
        )}

        {!isLoading && !error && filteredPolicies.length === 0 && searchTerm && (
          <div className="no-results-state">
            <p>No policies found matching "{searchTerm}"</p>
          </div>
        )}

        {!isLoading && !error && policies.length === 0 && (
          <div className="no-policies-state">
            <p>No policies available for patients at this time.</p>
          </div>
        )}
      </div>
    </div>
  );
};

export default ResourcesSection;

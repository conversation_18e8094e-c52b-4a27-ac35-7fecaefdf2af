import React, { useRef, useEffect } from 'react';
import './otp-input.css'; // We'll create this CSS file next

interface NurtifyOtpInputProps {
  length: number;
  value: string[];
  onChange: (otp: string[]) => void;
  disabled?: boolean;
}

const NurtifyOtpInput: React.FC<NurtifyOtpInputProps> = ({
  length,
  value,
  onChange,
  disabled = false,
}) => {
  const inputRefs = useRef<(HTMLInputElement | null)[]>([]);

  useEffect(() => {
    // Initialize refs array
    inputRefs.current = inputRefs.current.slice(0, length);
  }, [length]);

  const handleInputChange = (
    e: React.ChangeEvent<HTMLInputElement>,
    index: number
  ) => {
    const inputValue = e.target.value;
    // Allow only single digits
    if (/^\d?$/.test(inputValue)) {
      const newOtp = [...value];
      newOtp[index] = inputValue;
      onChange(newOtp);

      // Move focus to the next input if a digit is entered
      if (inputValue.length === 1 && index < length - 1) {
        inputRefs.current[index + 1]?.focus();
      }
    }
  };

  const handleKeyDown = (
    e: React.KeyboardEvent<HTMLInputElement>,
    index: number
  ) => {
    // Move focus to the previous input on backspace if the current input is empty
    if (e.key === 'Backspace' && !value[index] && index > 0) {
      inputRefs.current[index - 1]?.focus();
    }
  };

  const handlePaste = (e: React.ClipboardEvent<HTMLInputElement>) => {
    e.preventDefault();
    const pastedData = e.clipboardData.getData('text');
    // Allow only digits in pasted data
    if (/^\d+$/.test(pastedData)) {
      const digits = pastedData.slice(0, length).split('');
      const newOtp = [...value];

      digits.forEach((digit, index) => {
        if (index < length) {
          newOtp[index] = digit;
        }
      });

      onChange(newOtp);

      // Focus on the next empty input or the last input after pasting
      const nextFocusIndex = Math.min(digits.length, length - 1);
       if (inputRefs.current[nextFocusIndex]) {
         inputRefs.current[nextFocusIndex]?.focus();
       }
    }
  };

  return (
    <div className="nurtify-otp-container">
      {[...Array(length)].map((_, index) => (
        <input
          key={index}
          className="nurtify-otp-input"
          type="text" // Use text to allow single character input and pasting
          pattern="\d{1}" // Pattern for validation (though mostly handled by JS)
          inputMode="numeric" // Hint for mobile keyboards
          value={value[index] || ''} // Ensure value is controlled
          maxLength={1}
          ref={(el) => (inputRefs.current[index] = el)}
          onChange={(e) => handleInputChange(e, index)}
          onKeyDown={(e) => handleKeyDown(e, index)}
          onPaste={index === 0 ? handlePaste : undefined} // Handle paste only on the first input
          disabled={disabled}
          aria-label={`OTP digit ${index + 1}`}
           // Basic filtering on input event for non-numeric characters (optional enhancement)
           onInput={(e) => {
             e.currentTarget.value = e.currentTarget.value.replace(/[^0-9]/g, '');
           }}
        />
      ))}
    </div>
  );
};

export default NurtifyOtpInput;

import { X, Save, Calendar } from "lucide-react";
import { useState, useEffect } from "react";
import { useOrganizationUsersQuery } from "@/hooks/user.query";
import { useUpdateVisitMutation } from "@/hooks/patient.query";
import { User } from "@/services/api/types";
import { toast } from "sonner";
import "./EditPatientVisitModal.css";

interface VisitFormData {
  registration_status: "Not Arrived" | "In Hospital" | "Discharged";
  location: string;
  nurse_identifier: string;
}

interface EditPatientVisitModalProps {
  isOpen: boolean;
  onClose: () => void;
  patientUuid?: string;
  visitUuid?: string;
  onSave?: (data: VisitFormData) => void;
  visitData: {
    name: string;
    visit_status: string;
    registration_status: string;
    date: string;
    time: string;
    location?: string;
    nurse_identifier?: string;
    leading_team?: string;
    activities?: string[];
    comments?: string;
  };
}

const EditPatientVisitModal: React.FC<EditPatientVisitModalProps> = ({
  isOpen,
  onClose,
  patientUuid,
  visitUuid,
  onSave,
  visitData,
}) => {
  const [formData, setFormData] = useState<VisitFormData>({
    registration_status: visitData.registration_status as "Not Arrived" | "In Hospital" | "Discharged",
    location: visitData.location || "",
    nurse_identifier: visitData.nurse_identifier || "",
  });

  const { data: organizationUsers, isLoading: isLoadingUsers } = useOrganizationUsersQuery();
  const updateVisitMutation = useUpdateVisitMutation();

  // Update form data when modal opens or visitData changes
  useEffect(() => {
    if (isOpen) {
      setFormData({
        registration_status: visitData.registration_status as "Not Arrived" | "In Hospital" | "Discharged",
        location: visitData.location || "",
        nurse_identifier: visitData.nurse_identifier || "",
      });
      console.log("Modal props:", { patientUuid, visitUuid, visitData });
    }
  }, [isOpen, patientUuid, visitUuid, visitData]);

  if (!isOpen) return null;

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!patientUuid || !visitUuid) {
      toast.error("Missing patient or visit information");
      console.error("Missing props:", { patientUuid, visitUuid });
      return;
    }
    try {
      // If onSave prop is provided, use it instead of direct mutation
      if (onSave) {
        onSave(formData);
      } else {
        await updateVisitMutation.mutateAsync({
          patientUuid,
          visitUuid,
          data: formData,
        });
      }
      toast.success("Visit updated successfully");
      onClose();
    } catch (error) {
      toast.error("Failed to update visit");
      console.error("Error updating visit:", error);
    }
  };

  const handleChange = (
    e: React.ChangeEvent<HTMLSelectElement | HTMLInputElement>
  ) => {
    const { name, value } = e.target;
    setFormData((prev) => ({
      ...prev,
      [name]: value,
    }));
  };

  return (
    <div className="edit-patient-visit-modal-overlay">
      <div className="edit-patient-visit-modal">
        <div className="edit-patient-visit-modal-header">
          <h2 className="edit-patient-visit-modal-title">
            <Calendar size={20} className="modal-icon" /> Edit Visit: {visitData.name}
          </h2>
          <button
            type="button"
            className="edit-patient-visit-modal-close"
            onClick={onClose}
            aria-label="Close"
          >
            <X size={20} />
          </button>
        </div>

        <form onSubmit={handleSubmit}>
          <div className="edit-patient-visit-modal-body">
            <div className="form-group">
              <label htmlFor="registration_status">Registration status</label>
              <select
                id="registration_status"
                name="registration_status"
                className="form-select"
                value={formData.registration_status}
                onChange={handleChange}
                required
                disabled={updateVisitMutation.isPending}
              >
                <option value="">select registration status</option>
                <option value="Not Arrived">Not Arrived</option>
                <option value="In Hospital">In Hospital</option>
                <option value="Discharged">Discharged</option>
              </select>
            </div>

            <div className="form-group">
              <label htmlFor="location">Location</label>
              <input
                type="text"
                id="location"
                name="location"
                className="form-control"
                value={formData.location}
                onChange={handleChange}
                required
                placeholder="Enter location"
                disabled={updateVisitMutation.isPending}
              />
            </div>

            <div className="form-group">
              <label htmlFor="nurse_identifier">Staff Member</label>
              <select
                id="nurse_identifier"
                name="nurse_identifier"
                className="form-select"
                value={formData.nurse_identifier}
                onChange={handleChange}
                required
                disabled={isLoadingUsers || updateVisitMutation.isPending}
              >
                <option value="">Select a staff member</option>
                {isLoadingUsers ? (
                  <option value="" disabled>Loading staff members...</option>
                ) : (
                  organizationUsers?.map((user: User) => (
                    <option key={user.identifier} value={user.identifier}>
                      {user.first_name} {user.last_name} {user.speciality ? `(${user.speciality})` : ''}
                    </option>
                  ))
                )}
              </select>
              {isLoadingUsers && <span className="loading-text">Loading staff members...</span>}
            </div>
          </div>

          <div className="edit-patient-visit-modal-footer">
            <button
              type="button"
              className="cancel-btn"
              onClick={onClose}
              disabled={updateVisitMutation.isPending}
            >
              <X size={16} /> Cancel
            </button>
            <button
              type="submit"
              className="save-btn"
              disabled={updateVisitMutation.isPending}
            >
              {updateVisitMutation.isPending ? (
                "Saving..."
              ) : (
                <>
                  <Save size={16} /> Save Changes
                </>
              )}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default EditPatientVisitModal;

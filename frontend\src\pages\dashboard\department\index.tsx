import "./department.css";
import { Link, useNavigate } from "react-router-dom";
import { useState, useMemo } from "react";
import { ArrowUpRight, Plus, Trash2, UserCog, Building } from "lucide-react";
import NoResult from "@/components/common/NoResult";
import { Department as DepartmentData } from "@/services/api/types.ts";
import { useDepartmentsByHospitalQuery, useDeleteDepartmentMutation } from "@/hooks/department.query";
import { useCurrentUserQuery } from '@/hooks/user.query';
import DeleteDepartmentModal from "@/components/modal/DeleteDepartmentModal";
import DataTable from "@/components/common/DataTable";

export default function Department() {
  const { data: currentUser } = useCurrentUserQuery();
  const hospitalUuid = currentUser?.hospital?.uuid || "";
  const { data = [] } = useDepartmentsByHospitalQuery(hospitalUuid, currentUser?.is_admin); // Default to empty array
  const deleteDepartmentMutation = useDeleteDepartmentMutation();
  const navigate = useNavigate();

  const [deletableDepartmentID, setDeletableDepartmentID] = useState<string>("");
  const [isModalOpen, setIsModalOpen] = useState(false);

  const DepartmentColums = useMemo(() => [
    { key: "uuid", header: "ID", hidden: true },
    { key: "name", header: "Department Name" },
    {
      key: "hospital",
      header: "Hospital Name",
      render: (value: DepartmentData["hospital"]) => {
        if (typeof value === "object" && value !== null && "name" in value) {
          return value.name;
        }
        return value !== undefined && value !== null ? String(value) : "-";
      },
    },
    { key: "postcode", header: "Postcode" },
    { key: "country", header: "Country" },
  ], []);

  const actions = useMemo(() => [
    {
      icon: <ArrowUpRight size={20} />,
      onClick: (row: DepartmentData) => handleDepartmentDetails(row),
      tooltipText:"Edit this item",
    },
    {
      icon: <Trash2 size={20} color="#FF0000" />,
      onClick: (row: DepartmentData) => handleDepartmentDelete(row),
      tooltipText: "Delete this item",
    },
    {
      icon: <UserCog size={20} color="#FF0000" />,
      onClick: (row: DepartmentData) => handleDepartmentAdmins(row),
      tooltipText: "Manage Admins",
    },
  ], []);

  const handleCloseModal = () => {
    setIsModalOpen(false);
  };

  const handleSubmitModal = async () => {
    try {
      await deleteDepartmentMutation.mutateAsync({ uuid: deletableDepartmentID });
      setIsModalOpen(false); // Close modal on success
    } catch (error) {
      console.error("Error deleting department:", error);
    }
  };

  const handleDepartmentDetails = (department: DepartmentData) => {
    navigate(`/org/dashboard/department-details/${department.uuid}`);
  };

  const handleDepartmentDelete = (department: DepartmentData) => {
    setIsModalOpen(true);
    setDeletableDepartmentID(department.uuid ?? "");
  };

  const handleDepartmentAdmins = (department: DepartmentData) => {
    navigate(`/org/dashboard/department-details/${department.uuid}/departmentAdmins`);
  };

  return (
    <div className="department-container">
      <div className="departments-header">
        <div className="departments-title">
          <h1>
            <Building size={24} style={{ marginRight: "10px" }} />
            Departments
          </h1>
        </div>
        <div className="departments-subtitle">
          <h6>Manage and view all departments in your network</h6>
        </div>
      </div>

      <div className="department-search-container">
        <Link
          to="/org/dashboard/add-department"
          className="department-add-button"
        >
          Add Department <Plus size={20} />
        </Link>
      </div>

      <div className="department-table-container">
        {data.length > 0 ? (
          <>
            <h4 className="department-table-title">Department List</h4>
            <DataTable
              data={data}
              columns={DepartmentColums as import('@/components/common/DataTable').Column<DepartmentData>[]}
              actions={actions}
              noDataMessage="No departments found"
              defaultItemsPerPage={10}
            />
            {isModalOpen && (
              <DeleteDepartmentModal
                isOpen={isModalOpen}
                onClose={handleCloseModal}
                onDelete={handleSubmitModal}
              />
            )}
          </>
        ) : (
          <NoResult addPath="/dashboard/add-department" title="Department" />
        )}
      </div>
    </div>
  );
}

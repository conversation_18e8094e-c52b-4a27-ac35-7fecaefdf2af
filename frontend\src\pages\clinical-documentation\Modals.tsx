import { useState, FC } from "react";
import AddPatientFormModal from "../../components/modal/AddPatientFormModal";
import SubmitSuccessModal from "../../components/modal/SubmitSuccessModal";

interface ModalsProps {
  isOpen: boolean;
  setIsModalOpen: (isOpen: boolean) => void;
}

const Modals: FC<ModalsProps> = ({ isOpen, setIsModalOpen }) => {
  const [switchModal, setSwitchModal] = useState(false);

  return (
    <div>
      {!switchModal && (
        <AddPatientFormModal
          isOpen={isOpen}
          setSwitchModal={setSwitchModal}
          setIsModalOpen={setIsModalOpen}
          switchModal={switchModal}
        />
      )}
      {switchModal && (
        <SubmitSuccessModal
          isOpen={switchModal}
          setNextModel={setIsModalOpen} 
        />
      )}
    </div>
  );
};

export default Modals;

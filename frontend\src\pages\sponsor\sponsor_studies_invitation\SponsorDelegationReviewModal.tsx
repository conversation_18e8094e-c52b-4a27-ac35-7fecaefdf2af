import { useState } from "react";
import { X, Loader2, CheckCircle, XCircle } from "lucide-react";
import "./sponsor-studies-invitation.css";

interface DelegationLogListItem {
  uuid: string;
  study_name: string;
  department_name: string;
  team_member_name: string;
  pi_name: string;
  study_task: string;
  status: string;
  pi_notes: string | null;
  sponsor_notes: string | null;
  created_at: string;
  updated_at: string;
}

interface SponsorDelegationReviewModalProps {
  isOpen: boolean;
  delegation: DelegationLogListItem | null;
  action: 'approve' | 'reject' | null;
  onClose: () => void;
  onConfirm: (notes: string) => void;
  isLoading: boolean;
}

export default function SponsorDelegationReviewModal({
  isOpen,
  delegation,
  action,
  onClose,
  onConfirm,
  isLoading
}: SponsorDelegationReviewModalProps) {
  const [notes, setNotes] = useState("");

  if (!isOpen || !delegation || !action) return null;

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (notes.trim()) {
      onConfirm(notes.trim());
    }
  };

  const handleClose = () => {
    setNotes("");
    onClose();
  };

  const isApprove = action === 'approve';
  const actionIcon = isApprove ? <CheckCircle size={20} /> : <XCircle size={20} />;
  const actionTitle = isApprove ? "Sponsor Approve Application" : "Sponsor Reject Application";
  const actionButtonText = isApprove ? "Approve" : "Reject";

  return (
    <div className="ssi-modal-overlay">
      <div className="ssi-modal ssi-review-modal">
        <div className="ssi-modal-header">
          <div className="ssi-review-header">
            <div className={`ssi-review-icon ${isApprove ? 'approve' : 'reject'}`}>
              {actionIcon}
            </div>
            <h2 className="ssi-modal-title">{actionTitle}</h2>
          </div>
          <button className="ssi-modal-close" onClick={handleClose} disabled={isLoading}>
            <X size={20} />
          </button>
        </div>
        
        <form onSubmit={handleSubmit}>
          <div className="ssi-modal-body">
            <div className="ssi-review-form">
              <p className="ssi-review-description">
                {isApprove 
                  ? "As a sponsor, please review the application details below and provide final approval notes."
                  : "As a sponsor, please review the application details below and provide rejection notes."
                }
              </p>
              
              <div className="ssi-review-details">
                <div className="ssi-review-detail-grid">
                  <div className="ssi-review-detail-item">
                    <span className="ssi-review-detail-label">Study:</span>
                    <span className="ssi-review-detail-value">{delegation.study_name}</span>
                  </div>
                  <div className="ssi-review-detail-item">
                    <span className="ssi-review-detail-label">Team Member:</span>
                    <span className="ssi-review-detail-value">{delegation.team_member_name}</span>
                  </div>
                  <div className="ssi-review-detail-item">
                    <span className="ssi-review-detail-label">Department:</span>
                    <span className="ssi-review-detail-value">{delegation.department_name}</span>
                  </div>
                  <div className="ssi-review-detail-item">
                    <span className="ssi-review-detail-label">Principal Investigator:</span>
                    <span className="ssi-review-detail-value">{delegation.pi_name}</span>
                  </div>
                  <div className="ssi-review-detail-item">
                    <span className="ssi-review-detail-label">Study Task:</span>
                    <span className="ssi-review-detail-value">{delegation.study_task}</span>
                  </div>
                  <div className="ssi-review-detail-item">
                    <span className="ssi-review-detail-label">PI Notes:</span>
                    <span className="ssi-review-detail-value">{delegation.pi_notes || "No PI notes provided"}</span>
                  </div>
                  <div className="ssi-review-detail-item">
                    <span className="ssi-review-detail-label">Requested Date:</span>
                    <span className="ssi-review-detail-value">
                      {new Date(delegation.created_at).toLocaleDateString()}
                    </span>
                  </div>
                </div>
              </div>
              
              <div className="ssi-form-group">
                <label htmlFor="sponsor-notes" className="ssi-form-label">
                  {isApprove ? "Sponsor Approval Notes:" : "Sponsor Rejection Notes:"}
                </label>
                <textarea
                  id="sponsor-notes"
                  className="ssi-textarea"
                  value={notes}
                  onChange={(e) => setNotes(e.target.value)}
                  placeholder={isApprove 
                    ? "Provide sponsor approval notes (e.g., 'Final approval granted. Welcome to the study team!')"
                    : "Provide sponsor rejection notes (e.g., 'Rejected - Study team is already at capacity')"
                  }
                  rows={4}
                  required
                  disabled={isLoading}
                />
                <p className="ssi-form-help">
                  These notes will be shared with the team member and department.
                </p>
              </div>
            </div>
          </div>
          
          <div className="ssi-modal-footer">
            <button 
              type="button"
              className="ssi-btn ssi-btn-secondary"
              onClick={handleClose}
              disabled={isLoading}
            >
              Cancel
            </button>
            <button 
              type="submit"
              className={`ssi-btn ${isApprove ? 'ssi-btn-success' : 'ssi-btn-danger'}`}
              disabled={isLoading || !notes.trim()}
            >
              {isLoading ? (
                <>
                  <Loader2 size={16} className="ssi-spin" />
                  Processing...
                </>
              ) : (
                actionButtonText
              )}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
}

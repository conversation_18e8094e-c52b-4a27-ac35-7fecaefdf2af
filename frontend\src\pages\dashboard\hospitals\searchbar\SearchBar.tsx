import React, { useState } from "react";
import "./SearchBar.css";

interface SearchBarProps {
  placeholder?: string;
  searchButtonText?: string;
  onSearch?: (value: string) => void;
  onReset?: () => void;
  inputId?: string;
  disabled?: boolean;
}

const SearchBar: React.FC<SearchBarProps> = ({
  placeholder = "Type to search...",
  searchButtonText = "Search",
  onSearch,
  onReset,
  inputId = "search",
  disabled = false,
}) => {
  const [inputValue, setInputValue] = useState("");

  const handleChange = (value: string) => {
    setInputValue(value);
    /* fetchData(value); */
  };

  const handleSubmit = (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    if (onSearch && inputValue) {
      onSearch(inputValue);
    }
  };

  const handleReset = (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    setInputValue("");
    if (onReset) {
      onReset();
    }
  };

  const suggestionList = Array.from({ length: 10 }, (_, index) => `Item ${index}`);

  return (
    <div className="search-bar-container">
      <form className="form" onSubmit={handleSubmit} onReset={handleReset}>
        <label htmlFor={inputId}>
          <input
            required
            autoComplete="off"
            placeholder={placeholder}
            id={inputId}
            type="text"
            value={inputValue}
            onChange={(e) => handleChange(e.target.value)}
            disabled={disabled}
          />
          <div className="icon">
            <svg
              strokeWidth={2}
              stroke="currentColor"
              viewBox="0 0 24 24"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
              className="swap-on"
            >
              <path
                d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"
                strokeLinejoin="round"
                strokeLinecap="round"
              />
            </svg>
            <svg
              strokeWidth={2}
              stroke="currentColor"
              viewBox="0 0 24 24"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
              className="swap-off"
            >
              <path
                d="M10 19l-7-7m0 0l7-7m-7 7h18"
                strokeLinejoin="round"
                strokeLinecap="round"
              />
            </svg>
          </div>
          <button type="reset" className="close-btn">
            <svg
              viewBox="0 0 20 20"
              className="h-5 w-5"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                clipRule="evenodd"
                d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z"
                fillRule="evenodd"
              />
            </svg>
          </button>
          <button type="submit" className="hospitals-search-button" disabled={disabled}>
            {searchButtonText}
          </button>
        </label>
      </form>
      {inputValue && (
        <ul className="suggestion-list">
          {suggestionList.map((item, index) => (
            <li key={index}>{item}</li>
          ))}
        </ul>
      )}
    </div>
  );
};

export default SearchBar;
import { useEffect } from 'react';
import { useLocation } from 'react-router-dom';
import { Helmet } from 'react-helmet-async';
import { trackPageView } from '@/utils/analytics';
import { getSEOConfig } from '@/config/seoConfig';

const RouteTracker: React.FC = () => {
  const location = useLocation();

  useEffect(() => {
    // Track page view on route change
    const url = `${window.location.origin}${location.pathname}${location.search}`;
    const title = document.title;
    
    trackPageView(url, title);
  }, [location]);

  // Define protected routes that should have noindex
  const protectedRoutes = [
    '/home',
    '/clinical',
    '/patient',
    '/sponsor', 
    '/org/dashboard',
    '/dashboard',
    '/templates',
    '/my-patients',
    '/patients'
  ];

  // Check if current route is protected
  const isProtectedRoute = protectedRoutes.some(route => 
    location.pathname.startsWith(route)
  );

  // Get SEO config for current route
  const seoConfig = getSEOConfig(location.pathname);
  const canonicalUrl = `https://nurtify.co.uk${location.pathname}`;

  return (
    <Helmet>
      {/* Dynamic robots meta based on route type */}
      <meta 
        name="robots" 
        content={isProtectedRoute ? "noindex, nofollow" : "index, follow"} 
      />
      
      {/* Canonical URL */}
      <link rel="canonical" href={canonicalUrl} />
      
      {/* Update title and description dynamically */}
      <title>{seoConfig.title}</title>
      <meta name="description" content={seoConfig.description} />
      <meta name="keywords" content={seoConfig.keywords} />
      
      {/* Open Graph */}
      <meta property="og:url" content={canonicalUrl} />
      <meta property="og:title" content={seoConfig.title} />
      <meta property="og:description" content={seoConfig.description} />
      
      {/* Twitter */}
      <meta name="twitter:url" content={canonicalUrl} />
      <meta name="twitter:title" content={seoConfig.title} />
      <meta name="twitter:description" content={seoConfig.description} />
    </Helmet>
  );
};

export default RouteTracker;

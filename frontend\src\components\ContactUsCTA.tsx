import NurtifyButton from "./NurtifyButton";
import { useState, ChangeEvent, FormEvent } from "react";
import { useSubmitResearchOpportunity } from '@/hooks/contact.query';
import { toast } from "sonner";

// Define the interface to match the ResearchOpportunity model
interface ResearchOpportunityForm {
    name: string;
    email: string;
    user_type: string;
    message: string;
    consent: boolean;
}

const ContactUsCTA = () => {
    const submitResearchOpportunity = useSubmitResearchOpportunity();

    // Set the initial state to match the ResearchOpportunityForm interface
    const [researchOpportunityForm, setResearchOpportunityForm] = useState<ResearchOpportunityForm>({
        name: '',
        email: '',
        user_type: '',
        message: '',
        consent: false
    });

    const validateEmail = (email: string): boolean => {
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        return emailRegex.test(email);
    };

    const handleResearchOpportunityChange = (
        e: ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>
    ): void => {
        const { name, value, type } = e.target;
        if (type === 'checkbox') {
            const checked = (e.target as HTMLInputElement).checked;
            setResearchOpportunityForm({ ...researchOpportunityForm, [name]: checked });
        } else {
            setResearchOpportunityForm({ ...researchOpportunityForm, [name]: value });
        }
    };

    const handleSubmit = async (e: FormEvent<HTMLFormElement>): Promise<void> => {
        e.preventDefault();

        // Basic validation for required fields
        if (!researchOpportunityForm.name || !researchOpportunityForm.email || !researchOpportunityForm.user_type || !researchOpportunityForm.message) {
            toast.error('Please fill in all required fields.');
            return;
        }

        if (!validateEmail(researchOpportunityForm.email)) {
            toast.error('Please enter a valid email address.');
            return;
        }

        // Create the payload for the backend API
        const payload = {
            name: researchOpportunityForm.name,
            email: researchOpportunityForm.email,
            user_type: researchOpportunityForm.user_type,
            message: researchOpportunityForm.message,
        };

        try {
            await submitResearchOpportunity.mutateAsync(payload);
            

            // Reset the form after successful submission
            setResearchOpportunityForm({
                name: '',
                email: '',
                user_type: '',
                message: '',
                consent: false
            });

        } catch (error) {
            
            console.error('Submission error:', error);
        }
    };

    return (
        <section className="service-section contact-section-bg">
            <div className="contact-section-container">
                <div className="contact-section-left">
                    <h4 className="service-subtitle">Take The Next Step</h4>
                    <h2 className="service-title">
                        Contact Us About
                        <br />
                        Research Opportunities
                    </h2>
                </div>
                <div className="contact-section-right">
                    <form className="contact-form" onSubmit={handleSubmit}>
                        <div className="contact-form-group">
                            <label htmlFor="name">Name</label>
                            <input 
                                id="name" 
                                name="name"
                                placeholder="Text" 
                                type="text" 
                                className="contact-item-CTA"
                                value={researchOpportunityForm.name}
                                onChange={handleResearchOpportunityChange} 
                            />
                        </div>
                        <div className="contact-form-group">
                            <label htmlFor="email">Email</label>
                            <input 
                                id="email" 
                                name="email"
                                placeholder="Text" 
                                type="email" 
                                className="contact-item-CTA"
                                value={researchOpportunityForm.email}
                                onChange={handleResearchOpportunityChange} 
                            />
                        </div>
                        <div className="contact-form-group">
                            <label htmlFor="user_type">Please tell us about you</label>
                            <select 
                                id="user_type" 
                                name="user_type"
                                className="contact-item-CTA"
                                value={researchOpportunityForm.user_type}
                                onChange={handleResearchOpportunityChange}
                            >
                                <option value="" disabled>
                                    Select
                                </option>
                                <option value="patient">Patient interested in participating in a study</option>
                                <option value="sponsor">Sponsor seeking partnership</option>
                                <option value="nhs">NHS Clinical Research Facility representative</option>
                            </select>
                        </div>
                        <div className="contact-form-group">
                            <label htmlFor="message">Message</label>
                            <textarea 
                                id="message" 
                                name="message"
                                placeholder="Text" 
                                className="contact-item-CTA"
                                value={researchOpportunityForm.message}
                                onChange={handleResearchOpportunityChange}
                            />
                        </div>
                        <div className="contact-form-group">
                            <NurtifyButton 
                                type="submit" 
                                style={{ width: "50%", marginTop: "15px" }}
                                onMouseEnter={(e) => {
                                    e.currentTarget.style.backgroundColor = '#2A9BAB';
                                    e.currentTarget.style.transform = 'translateY(-2px)';
                                }}
                                onMouseLeave={(e) => {
                                    e.currentTarget.style.backgroundColor = '#37B7C3';
                                    e.currentTarget.style.transform = 'translateY(0)';
                                }}
                                disabled={submitResearchOpportunity.isPending}
                            >
                                {submitResearchOpportunity.isPending ? 'Submitting...' : 'Send Message'}
                            </NurtifyButton>
                        </div>
                    </form>
                </div>
            </div>
        </section>
    );
};

export default ContactUsCTA;
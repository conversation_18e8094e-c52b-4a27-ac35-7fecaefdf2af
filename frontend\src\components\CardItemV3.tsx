import { motion } from "framer-motion";
import { useEffect, useState } from "react";
import { useNavigate } from "react-router-dom";

interface CardItemV3Props {
  icon: string;
  color?: string;
  title: string;
  style?: React.CSSProperties;
  navigateTo?: string;
}

const CardItemV3: React.FC<CardItemV3Props> = ({
  icon,
  color = "#FFFFFF",
  title,
  style = {},
  navigateTo,
}) => {
  const navigate = useNavigate();
  const [isMobile, setIsMobile] = useState<boolean>(window.innerWidth < 768);
  const handleResize = () => {
    setIsMobile(window.innerWidth < 768);
  };

  useEffect(() => {
    window.addEventListener("resize", handleResize);
    return () => {
      window.removeEventListener("resize", handleResize);
    };
  }, []);

  return (
    <div
      className="d-flex flex-column align-items-center flex-wrap"
      style={{
        gap: "10px",
        width: isMobile ? "100%" : "134px",
        textAlign: "center",
        fontWeight: "bold",
        color: "black",
        fontSize: "17px",
      }}
      onClick={() => {
        if (navigateTo) {
          navigate(navigateTo);
        }
      }}
    >
      <motion.div
        className="card-item card-item--style-1 border-0"
        style={{
          ...style,
          gap: "10px",
          border: "1px solid #E4E4E7",
          borderRadius: "5px",
          backgroundColor: color,
          padding: style.padding || "15px", // Use padding from style prop or a smaller default
        }}
        initial={{ opacity: 0, y: 20 }}
        whileInView={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6 }}
        viewport={{ once: true, amount: 0.5 }}
      >
        <motion.div
          className="card-item__image"
          initial={{ scale: 0.8 }}
          whileInView={{ scale: 1 }}
          transition={{ duration: 0.4, delay: 0.2 }}
        >
          <img 
            src={icon} 
            alt={`${title} icon`} 
            style={{ 
              maxWidth: "100%", 
              maxHeight: "100%", 
              objectFit: "contain", 
              width: "auto",
              height: "auto" 
            }} 
          />
        </motion.div>
      </motion.div>
      <motion.div>{title}</motion.div>
    </div>
  );
};

export default CardItemV3;

import frailtyImage from "./static/images/added/frailty.jpg";
import frailtyScoreImg from "./static/images/frailty_score_img.png";
import useHolisticFormStore from "@/store/holisticFormState";
import NurtifyRadio from "@/components/NurtifyRadio";

const frailtyScoreOptions = [
  { value: "1", label: "Very Fit" },
  { value: "2", label: "Well" },
  { value: "3", label: "Managing Well" },
  { value: "4", label: "Vulnerable" },
  { value: "5", label: "Mildly Frail" },
  { value: "6", label: "Moderately Frail" },
  { value: "7", label: "Severely Frail" },
  { value: "8", label: "Very Severely Frail" },
  { value: "9", label: "Terminally Ill" },
];

const Frailty = () => {
  const { assessment, setAssessment } = useHolisticFormStore();
  const frailtyScore = assessment?.frailty;

  const handleFrailtyScoreChange = (value: string) => {
    setAssessment({
      ...assessment,
      frailty: parseInt(value),
    });
  };

  return (
    <div className="block align-items-start flex-column flex-md-row p-4 mt-5">
      <div id="division-32" className="w-100">
        {/*Frailty Section Title  */}
        <div className="inlineBlock mb-4 headinqQuestion">
          <img
            src={frailtyImage}
            className="imageEtiquette"
            alt="old patient in wheelchair"
          />
          <span className="mt-2 pt-2 fs-2 text-start etiquetteHeadingForms">
            Frailty Score
          </span>
        </div>

        <div className="d-flex flex-wrap align-items-start">
          <div className="list-group me-4 mt-3">
            <span className="headinqQuestion">Select Frailty Score</span>
            {frailtyScoreOptions.map((option) => (
              <NurtifyRadio
                key={option.value}
                name="frailtyScore"
                label={option.label}
                value={option.value}
                checked={frailtyScore === parseInt(option.value)}
                onChange={() => handleFrailtyScoreChange(option.value)}
              />
            ))}
          </div>
          <div className="col-12 col-md-9 mt-3 border border-5">
            <img
              style={{ width: "100%", height: "auto" }}
              src={frailtyScoreImg}
              alt=""
            />
          </div>
        </div>
      </div>
    </div>
  );
};

export default Frailty;

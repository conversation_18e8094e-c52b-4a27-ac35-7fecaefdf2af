import { useState, ChangeEvent, FormEvent } from "react";
import NurtifyCheckBox from "@/components/NurtifyCheckBox";
import NurtifyComboBox from "@/components/NurtifyComboBox";
import NurtifyInput from "@/components/NurtifyInput";
import NurtifySelect from "@/components/NurtifySelect";
import NurtifyTextArea from "@/components/NurtifyTextArea";
import NurtifyText from "@/components/NurtifyText";
import NurtifyDateInput from "@/components/NurtifyDateInput";
import { useHospitalsQuery } from '@/hooks/hospital.query';
import { 
  useTrialsByHospitalQuery, 
  useSubmitPatientApplication, 
  useSubmitDoctorReferral, 
  useActiveTrialsQuery,
  useSubmitSupportInquiry,
  useSubmitBusinessInquiries 
} from '@/hooks/contact.query';
import NurtifyAttachFileBox from "@/components/NurtifyAttachFileBox";
import { useEffect } from "react";
import { useNavigate, useParams } from "react-router-dom";
import Preloader from "@/components/common/Preloader";
import DarkFooter from "@/shared/DarkFooter";
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faTimes } from '@fortawesome/free-solid-svg-icons';
import { faFilePdf, faFileWord, faFileAlt, faFileImage } from '@fortawesome/free-solid-svg-icons';
import { toast } from "sonner";

type ContactCategory = 'business' | 'patient' | 'doctor' | 'support';

interface BusinessForm {
  organization_name: string;
  contact_person: string;
  email: string;
  phone: string;
  message: string;
  consent: boolean;
}

interface PatientForm {
  // Hospital & Trial Selection
  preferredHospital: string;
  trialOfInterest: string;
  // Personal Information
  firstName: string;
  lastName: string;
  dateOfBirth: string;
  genderIdentity: string;
  ethnicBackground: string;
  postcode: string;
  nhsNumber: string;
  // Contact Details
  email: string;
  phone: string;
  // Medical History
  pastMedicalHistory: string[];
  currentMedications: string[];
  allergies: string[];
  // Lifestyle Factors
  smokingStatus: string;
  alcoholConsumption: string;
  physicalActivityLevel: string;
  // Eligibility Screening
  pregnancyStatus: string;
  willingnessToTravel: string;
  // Consent
  consent: boolean;
  // Form Errors
  errors: {
    [key: string]: string;
  };
  submitError?: string;
}

interface DoctorForm {
  // Doctor/HCP Information
  fullName: string;
  nhsEmail: string;
  gmcNumber: string;
  practiceOrganization: string;
  contactPhone: string;
  // Patient Information
  patientFirstName: string;
  patientLastName: string;
  patientDob: string;
  nhsNumber: string;
  ethnicBackground: string;
  postcode: string;
  patientPhone: string;
  previousMedicalHistory: string[];
  patientEmail: string;
  currentMedications: string[];
  // Trial-Specific Details
  trialOfInterest: string;
  reasonForReferral: string;
  patientSuitabilityNotes: string;
  // Consent & Compliance
  willingnessToTravel: string;
  // Consent
  consent: boolean;
  // Form Errors
  errors: {
    [key: string]: string;
  };
  submitError?: string;
  attachedDocuments: File[];
}

interface SupportForm {
  name: string;
  email: string;
  issue_type: string;
  phone: string;
  uploaded_file?: File;
  message: string;
  consent: boolean;
}


interface PatientApplication {
  trial_uuid: string;
  hospital_uuid: string;
  first_name: string;
  last_name: string;
  date_of_birth: string;
  gender: string;
  ethnicity: string;
  postcode: string;
  nhs_number: string;
  email: string;
  phone_number: string;
  medical_history: string;
  current_medications: string;
  allergies: string;
  smoking_status: string;
  alcohol_consumption: string;
  physical_activity_level: string;
  pregnancy_status: boolean;
  willing_to_travel: boolean;
  data_sharing_consent: boolean;
  status: 'pending' | 'approved' | 'rejected';
  trial: {
    uuid: string;
    name: string;
    description: string;
    hospital: {
      uuid: string;
      name: string;
    };
    is_active: boolean;
    created_at: string;
    updated_at: string;
  };
}

interface DoctorReferral {
  trial_uuid: string;
  doctor_name: string;
  nhs_email: string;
  gmc_number: string;
  organization_name: string;
  contact_phone: string;
  patient_first_name: string;
  patient_last_name: string;
  patient_dob: string;
  nhs_number: string;
  ethnicity: string;
  postcode: string;
  phone: string;
  phone_number: string;
  email: string;
  medical_history: string;
  current_medications: string;
  reason_for_referral: string;
  suitability_notes: string;
  willing_to_travel: boolean;
  data_sharing_consent: boolean;
  attached_documents?: File;
  trial: {
    uuid: string;
    name: string;
    description: string;
    hospital: {
      uuid: string;
      name: string;
    };
    is_active: boolean;
    created_at: string;
    updated_at: string;
  };
  status: 'pending' | 'approved' | 'rejected';
  first_name: string;
  last_name: string;
  date_of_birth: string;
}

interface ApiError {
  response?: {
    status: number;
    data?: any;
  };
}

export default function ContactDetailsPage(): JSX.Element {
  const navigate = useNavigate();
  const { category } = useParams<{ category: ContactCategory }>();
  const categories: ContactCategory[] = ['business', 'patient', 'doctor', 'support'];

  useEffect(() => {
    if (!category || !categories.includes(category)) {
      navigate('/contact-us');
    } else {
      setActiveCategory(category);
    }
  }, [category, categories, navigate]);

  const [activeCategory, setActiveCategory] = useState<ContactCategory>('business');

  const [businessForm, setBusinessForm] = useState<BusinessForm>({
    organization_name: '',
    contact_person: '',
    email: '',
    phone: '',
    message: '',
    consent: false
  });

  const [patientForm, setPatientForm] = useState<PatientForm>({
    // Hospital & Trial Selection
    preferredHospital: '',
    trialOfInterest: '',
    // Personal Information
    firstName: '',
    lastName: '',
    dateOfBirth: '',
    genderIdentity: '',
    ethnicBackground: '',
    postcode: '',
    nhsNumber: '',
    // Contact Details
    email: '',
    phone: '',
    // Medical History
    pastMedicalHistory: [],
    currentMedications: [],
    allergies: [],
    // Lifestyle Factors
    smokingStatus: '',
    alcoholConsumption: '',
    physicalActivityLevel: '',
    // Eligibility Screening
    pregnancyStatus: '',
    willingnessToTravel: '',
    // Consent
    consent: false,
    // Form Errors
    errors: {},
    submitError: undefined
  });

  const [doctorForm, setDoctorForm] = useState<DoctorForm>({
    // Doctor/HCP Information
    fullName: '',
    nhsEmail: '',
    gmcNumber: '',
    practiceOrganization: '',
    contactPhone: '',
    // Patient Information
    patientFirstName: '',
    patientLastName: '',
    patientDob: '',
    nhsNumber: '',
    ethnicBackground: '',
    postcode: '',
    patientPhone: '',
    previousMedicalHistory: [],
    patientEmail: '',
    currentMedications: [],
    // Trial-Specific Details
    trialOfInterest: '',
    reasonForReferral: '',
    patientSuitabilityNotes: '',
    // Consent & Compliance
    willingnessToTravel: '',
    // Consent
    consent: false,
    // Form Errors
    errors: {},
    submitError: undefined,
    attachedDocuments: [],
  });

  const [supportForm, setSupportForm] = useState<SupportForm>({
    name: '',
    email: '',
    issue_type: '',
    phone: '',
    uploaded_file: undefined,
    message: '',
    consent: false
  });


  // Fetch hospitals from API
  const { data: hospitals, isLoading: hospitalsLoading, error: hospitalsError } = useHospitalsQuery();

  // Fetch trials when a hospital is selected
  const { data: trials, isLoading: trialsLoading } = useTrialsByHospitalQuery(patientForm.preferredHospital);

  // Fetch active trials
  const { data: activeTrials, isLoading: activeTrialsLoading } = useActiveTrialsQuery();

  const submitPatientApplication = useSubmitPatientApplication();
  const submitDoctorReferral = useSubmitDoctorReferral();
  const submitSupportInquiry = useSubmitSupportInquiry();
  const submitBusinessInquiries = useSubmitBusinessInquiries();

  // Transform hospitals to ComboBox options
  const hospitalOptions = hospitals?.map(h => ({ value: h.uuid || h.name || '', label: h.name || '' })) || [];

  // Transform trials to ComboBox options
  const trialOptions = trials?.map(t => ({ value: t.uuid || '', label: t.name || '' })) || [];

  // Transform active trials to ComboBox options
  const activeTrialOptions = activeTrials?.map(t => ({ value: t.uuid || '', label: t.name || '' })) || [];

  const handleBusinessChange = (
    e: ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>
  ): void => {
    const { name, value, type } = e.target;
    if (type === 'checkbox') {
      const checked = (e.target as HTMLInputElement).checked;
      setBusinessForm({ ...businessForm, [name]: checked });
    } else {
      setBusinessForm({ ...businessForm, [name]: value });
    }
  };

  const handlePatientChange = (
    e: ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>
  ): void => {
    const { name, value, type } = e.target;
    if (type === 'checkbox') {
      const checked = (e.target as HTMLInputElement).checked;
      setPatientForm({ ...patientForm, [name]: checked });
    } else {
      setPatientForm({ ...patientForm, [name]: value });
    }
  };

  const handleDoctorChange = (
    e: ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>
  ): void => {
    const { name, value, type } = e.target;
    if (type === 'checkbox') {
      const checked = (e.target as HTMLInputElement).checked;
      setDoctorForm({ ...doctorForm, [name]: checked });
    } else {
      setDoctorForm({ ...doctorForm, [name]: value });
    }
  };

  const handleSupportChange = (
    e: ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>
  ): void => {
    const { name, value, type } = e.target;
    if (type === 'checkbox') {
      const checked = (e.target as HTMLInputElement).checked;
      setSupportForm({ ...supportForm, [name]: checked });
    } else {
      setSupportForm({ ...supportForm, [name]: value });
    }
  };

  const handleSupportFileChange = (files: FileList | null): void => {
    if (files && files.length > 0) {
      // Take only the first file since support form expects single file
      setSupportForm(prev => ({
        ...prev,
        uploaded_file: files[0]
      }));
    } else {
      setSupportForm(prev => ({
        ...prev,
        uploaded_file: undefined
      }));
    }
  };

  const getFileIcon = (fileName: string) => {
    const ext = fileName.split('.').pop()?.toLowerCase();
    switch (ext) {
      case 'pdf':
        return faFilePdf;
      case 'doc':
      case 'docx':
        return faFileWord;
      case 'jpg':
      case 'jpeg':
      case 'png':
      case 'gif':
      case 'bmp':
      case 'webp':
        return faFileImage;
      default:
        return faFileAlt;
    }
  };

  const handleRemoveFile = () => {
    setSupportForm(prevForm => ({ ...prevForm, uploaded_file: undefined }));
  };

  const validateEmail = (email: string): boolean => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  };

  const validateDate = (date: string): boolean => {
    const dateRegex = /^\d{4}-\d{2}-\d{2}$/;
    if (!dateRegex.test(date)) return false;

    const d = new Date(date);
    return d instanceof Date && !isNaN(d.getTime());
  };

  const validatePatientForm = (): boolean => {
    const errors: { [key: string]: string } = {};

    // Basic Information Validation
    if (!patientForm.firstName) {
      errors.firstName = 'First name is required';
    } else if (patientForm.firstName.length > 100) {
      errors.firstName = 'First name must not exceed 100 characters';
    }

    if (!patientForm.lastName) {
      errors.lastName = 'Last name is required';
    } else if (patientForm.lastName.length > 100) {
      errors.lastName = 'Last name must not exceed 100 characters';
    }

    if (!patientForm.dateOfBirth) {
      errors.dateOfBirth = 'Date of birth is required';
    } else if (!validateDate(patientForm.dateOfBirth)) {
      errors.dateOfBirth = 'Date must be in YYYY-MM-DD format';
    }

    if (!patientForm.genderIdentity) {
      errors.genderIdentity = 'Gender is required';
    } else if (!['Male', 'Female', 'Prefer_not_to_disclose'].includes(patientForm.genderIdentity)) {
      errors.genderIdentity = 'Invalid gender selection';
    }

    if (!patientForm.ethnicBackground) {
      errors.ethnicBackground = 'Ethnic background is required';
    }

    // NHS Number Validation
    if (!patientForm.nhsNumber) {
      errors.nhsNumber = 'NHS number is required';
    } else if (!/^\d{10}$/.test(patientForm.nhsNumber)) {
      errors.nhsNumber = 'NHS number must be exactly 10 digits';
    }

    // Phone Number Validation
    if (patientForm.phone && patientForm.phone.length > 20) {
      errors.phone = 'Phone number must not exceed 20 characters';
    }

    // Email Validation
    if (!patientForm.email) {
      errors.email = 'Email is required';
    } else if (!validateEmail(patientForm.email)) {
      errors.email = 'Invalid email format';
    }

    // Medical Information Validation
    if (patientForm.pastMedicalHistory.length === 0) {
      errors.pastMedicalHistory = 'Medical history is required';
    }

    if (patientForm.currentMedications.length === 0) {
      errors.currentMedications = 'Current medications are required';
    }

    // Consent Validation
    if (!patientForm.consent) {
      errors.consent = 'You must consent to proceed';
    }

    if (!patientForm.willingnessToTravel) {
      errors.willingnessToTravel = 'Please indicate willingness to travel';
    }

    setPatientForm(prev => ({ ...prev, errors }));
    return Object.keys(errors).length === 0;
  };

  const validateDoctorForm = (): boolean => {
    const errors: { [key: string]: string } = {};

    // Doctor Information Validation
    if (!doctorForm.fullName) {
      errors.fullName = 'Doctor name is required';
    } else if (doctorForm.fullName.length > 200) {
      errors.fullName = 'Doctor name must not exceed 200 characters';
    }

    if (!doctorForm.nhsEmail) {
      errors.nhsEmail = 'Email is required';
    } else if (!validateEmail(doctorForm.nhsEmail)) {
      errors.nhsEmail = 'Invalid email format';
    }

    if (!doctorForm.gmcNumber) {
      errors.gmcNumber = 'GMC number is required';
    } else if (doctorForm.gmcNumber.length > 20) {
      errors.gmcNumber = 'GMC number must not exceed 20 characters';
    }

    if (!doctorForm.practiceOrganization) {
      errors.practiceOrganization = 'Organization name is required';
    } else if (doctorForm.practiceOrganization.length > 200) {
      errors.practiceOrganization = 'Organization name must not exceed 200 characters';
    }

    if (!doctorForm.contactPhone) {
      errors.contactPhone = 'Contact phone is required';
    } else if (doctorForm.contactPhone.length > 20) {
      errors.contactPhone = 'Contact phone must not exceed 20 characters';
    }

    // Patient Information Validation
    if (!doctorForm.patientFirstName) {
      errors.patientFirstName = 'Patient first name is required';
    } else if (doctorForm.patientFirstName.length > 100) {
      errors.patientFirstName = 'Patient first name must not exceed 100 characters';
    }

    if (!doctorForm.patientLastName) {
      errors.patientLastName = 'Patient last name is required';
    } else if (doctorForm.patientLastName.length > 100) {
      errors.patientLastName = 'Patient last name must not exceed 100 characters';
    }

    if (!doctorForm.patientDob) {
      errors.patientDob = 'Patient date of birth is required';
    } else if (!validateDate(doctorForm.patientDob)) {
      errors.patientDob = 'Date must be in YYYY-MM-DD format';
    }

    if (!doctorForm.nhsNumber) {
      errors.nhsNumber = 'NHS number is required';
    } else if (!/^\d{10}$/.test(doctorForm.nhsNumber)) {
      errors.nhsNumber = 'NHS number must be exactly 10 digits';
    }

    if (!doctorForm.ethnicBackground) {
      errors.ethnicBackground = 'Ethnic background is required';
    }

    if (!doctorForm.postcode) {
      errors.postcode = 'Postcode is required';
    } else if (doctorForm.postcode.length > 10) {
      errors.postcode = 'Postcode must not exceed 10 characters';
    }

    if (!doctorForm.patientPhone) {
      errors.patientPhone = 'Patient phone is required';
    } else if (doctorForm.patientPhone.length > 20) {
      errors.patientPhone = 'Patient phone must not exceed 20 characters';
    }

    if (!doctorForm.patientEmail) {
      errors.patientEmail = 'Patient email is required';
    } else if (!validateEmail(doctorForm.patientEmail)) {
      errors.patientEmail = 'Invalid email format';
    }

    // Medical Information Validation
    if (doctorForm.previousMedicalHistory.length === 0) {
      errors.previousMedicalHistory = 'Medical history is required';
    }

    if (doctorForm.currentMedications.length === 0) {
      errors.currentMedications = 'Current medications are required';
    }

    // Trial-Specific Details Validation
    if (!doctorForm.trialOfInterest) {
      errors.trialOfInterest = 'Trial of interest is required';
    }

    if (!doctorForm.reasonForReferral) {
      errors.reasonForReferral = 'Reason for referral is required';
    }

    if (!doctorForm.patientSuitabilityNotes) {
      errors.patientSuitabilityNotes = 'Patient suitability notes are required';
    }

    // Consent Validation
    if (!doctorForm.consent) {
      errors.consent = 'You must consent to proceed';
    }

    if (!doctorForm.willingnessToTravel) {
      errors.willingnessToTravel = 'Please indicate willingness to travel';
    }

    setDoctorForm(prev => ({ ...prev, errors }));
    return Object.keys(errors).length === 0;
  };

  // Update the handleSubmit function to handle a single File
  const handleSubmit = async (e: FormEvent<HTMLFormElement>): Promise<void> => {
    e.preventDefault();
    let isValid = true;

    if (activeCategory === 'patient') {
      if (!validatePatientForm()) {
        return;
      }

      // Clear any previous submit errors
      setPatientForm(prev => ({ ...prev, submitError: undefined }));

      // Convert form data to match the API's expected format
      const applicationData: PatientApplication = {
        trial_uuid: patientForm.trialOfInterest,
        hospital_uuid: patientForm.preferredHospital,
        first_name: patientForm.firstName,
        last_name: patientForm.lastName,
        date_of_birth: patientForm.dateOfBirth,
        gender: patientForm.genderIdentity,
        ethnicity: patientForm.ethnicBackground,
        postcode: patientForm.postcode,
        nhs_number: patientForm.nhsNumber,
        email: patientForm.email,
        phone_number: patientForm.phone,
        medical_history: patientForm.pastMedicalHistory.join(', '),
        current_medications: patientForm.currentMedications.join(', '),
        allergies: patientForm.allergies.join(', '),
        smoking_status: patientForm.smokingStatus,
        alcohol_consumption: patientForm.alcoholConsumption,
        physical_activity_level: patientForm.physicalActivityLevel,
        pregnancy_status: patientForm.pregnancyStatus === 'yes',
        willing_to_travel: patientForm.willingnessToTravel === 'yes',
        data_sharing_consent: patientForm.consent,
        status: 'pending' as const,
        trial: {
          uuid: patientForm.trialOfInterest,
          name: '',
          description: '',
          hospital: {
            uuid: patientForm.preferredHospital,
            name: ''
          },
          is_active: true,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        }
      };

      try {
        await submitPatientApplication.mutateAsync(applicationData);
        alert('Application submitted successfully!');

        // Reset the form with all fields cleared
        setPatientForm({
          // Hospital & Trial Selection
          preferredHospital: '',
          trialOfInterest: '',
          // Personal Information
          firstName: '',
          lastName: '',
          dateOfBirth: '', // Reset date field
          genderIdentity: '',
          ethnicBackground: '',
          postcode: '',
          nhsNumber: '',
          // Contact Details
          email: '',
          phone: '',
          // Medical History
          pastMedicalHistory: [],
          currentMedications: [],
          allergies: [],
          // Lifestyle Factors
          smokingStatus: '',
          alcoholConsumption: '',
          physicalActivityLevel: '',
          // Eligibility Screening
          pregnancyStatus: '',
          willingnessToTravel: '',
          // Consent
          consent: false,
          // Form Errors
          errors: {},
          submitError: undefined
        });

        // Force a re-render of the date input
        const dateInput = document.querySelector('input[name="dateOfBirth"]') as HTMLInputElement;
        if (dateInput) {
          dateInput.value = '';
          // Trigger change event to update the calendar
          dateInput.dispatchEvent(new Event('change', { bubbles: true }));
        }
      } catch (error) {
        const err = error as ApiError;
        console.error('Failed to submit application:', err);
        if (err?.response?.status === 400) {
          setPatientForm(prev => ({
            ...prev,
            submitError: 'Please fill in all required fields correctly before submitting the form.'
          }));
        } else {
          alert('Failed to submit application. Please try again.');
        }
      }
    } else if (activeCategory === 'doctor') {
      if (!validateDoctorForm()) {
        return;
      }

      // Clear any previous submit errors
      setDoctorForm(prev => ({ ...prev, submitError: undefined }));

      try {
        // Create DoctorReferral object
        const referralData: DoctorReferral = {
          trial_uuid: doctorForm.trialOfInterest,
          doctor_name: doctorForm.fullName,
          nhs_email: doctorForm.nhsEmail,
          gmc_number: doctorForm.gmcNumber,
          organization_name: doctorForm.practiceOrganization,
          contact_phone: doctorForm.contactPhone,
          patient_first_name: doctorForm.patientFirstName,
          patient_last_name: doctorForm.patientLastName,
          patient_dob: doctorForm.patientDob,
          nhs_number: doctorForm.nhsNumber,
          ethnicity: doctorForm.ethnicBackground,
          postcode: doctorForm.postcode,
          phone: doctorForm.patientPhone,
          phone_number: doctorForm.patientPhone,
          email: doctorForm.patientEmail,
          medical_history: doctorForm.previousMedicalHistory.join(', '),
          current_medications: doctorForm.currentMedications.join(', '),
          reason_for_referral: doctorForm.reasonForReferral,
          suitability_notes: doctorForm.patientSuitabilityNotes,
          willing_to_travel: doctorForm.willingnessToTravel === 'yes',
          data_sharing_consent: doctorForm.consent,
          attached_documents: doctorForm.attachedDocuments[0], // Take the first file if available
          trial: {
            uuid: doctorForm.trialOfInterest,
            name: '',
            description: '',
            hospital: {
              uuid: '',
              name: ''
            },
            is_active: true,
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString()
          },
          status: 'pending',
          first_name: doctorForm.patientFirstName,
          last_name: doctorForm.patientLastName,
          date_of_birth: doctorForm.patientDob
        };

        // Submit the referral data
        await submitDoctorReferral.mutateAsync(referralData);
        alert('Referral submitted successfully!');

        // Reset the form with all fields cleared
        setDoctorForm({
          fullName: '',
          nhsEmail: '',
          gmcNumber: '',
          practiceOrganization: '',
          contactPhone: '',
          patientFirstName: '',
          patientLastName: '',
          patientDob: '',
          nhsNumber: '',
          ethnicBackground: '',
          postcode: '',
          patientPhone: '',
          previousMedicalHistory: [],
          patientEmail: '',
          currentMedications: [],
          trialOfInterest: '',
          reasonForReferral: '',
          patientSuitabilityNotes: '',
          willingnessToTravel: '',
          consent: false,
          errors: {},
          submitError: undefined,
          attachedDocuments: []
        });

        // Force a re-render of the date input
        const dateInput = document.querySelector('input[name="patientDob"]') as HTMLInputElement;
        if (dateInput) {
          dateInput.value = '';
          dateInput.dispatchEvent(new Event('change', { bubbles: true }));
        }
      } catch (error) {
        const err = error as ApiError;
        console.error('Failed to submit referral:', err);
        if (err?.response?.status === 400) {
          setDoctorForm(prev => ({
            ...prev,
            submitError: 'Please fill in all required fields correctly before submitting the form.'
          }));
        } else {
          alert('Failed to submit referral. Please try again.');
        }
      }
    } else if (activeCategory === 'business') {
      // Basic Business Form Validation
      if (!businessForm.organization_name || !businessForm.contact_person || !businessForm.email || !businessForm.message || !businessForm.consent) {
        toast.error('Please fill in all required fields and agree to the consent.');
        isValid = false;
      } else if (!validateEmail(businessForm.email)) {
        toast.error('Please enter a valid email address.');
        isValid = false;
      }

      if (isValid) {
        // Create a new object that matches the backend interface expected by submitBusinessInquiries
        const payload: Omit<BusinessForm, 'consent'> = { // Consent is handled by backend, or you can send it if needed
            organization_name: businessForm.organization_name,
            contact_person: businessForm.contact_person,
            email: businessForm.email,
            phone: businessForm.phone,
            message: businessForm.message,
        };
        submitBusinessInquiries.mutate(payload);
        // Reset the form with all fields cleared
        setBusinessForm({
          organization_name: '',
          contact_person: '',
          email: '',
          phone: '',
          message: '',
          consent: false
        });
      }
    } else if (activeCategory === 'support') {
      // Basic Support Form Validation
      if (!supportForm.name || !supportForm.email || !supportForm.issue_type || !supportForm.message || !supportForm.consent) {
        toast.error('Please fill in all required fields and agree to the consent.');
        isValid = false;
      } else if (!validateEmail(supportForm.email)) {
        toast.error('Please enter a valid email address.');
        isValid = false;
      } else if (supportForm.issue_type === "") { // Ensure a category is selected
        toast.error('Please select an issue category.');
        isValid = false;
      }

      if (isValid) {
        // For FormData, you generally send all simple fields as strings, and File objects as themselves.
        // The `uploaded_file` in the SupportForm interface directly maps to the `uploaded_file` in Django.
        const payload: SupportForm = {
          name: supportForm.name,
          email: supportForm.email,
          issue_type: supportForm.issue_type,
          phone: supportForm.phone,
          message: supportForm.message,
          consent: supportForm.consent,
          // Only include uploaded_file if it exists
          ...(supportForm.uploaded_file && { uploaded_file: supportForm.uploaded_file }),
        };
        submitSupportInquiry.mutate(payload);

        // Reset the form with all fields cleared
        setSupportForm({
          name: '',
          email: '',
          issue_type: '',
          phone: '',
          uploaded_file: undefined,
          message: '',
          consent: false
        });
      }
    }

    // ... handle other form types if needed ...
  };

  const renderForm = () => {
    switch (activeCategory) {
      case 'business':
        return (
          <div>
            {/* Form Fields */}
            {/* 1. Business Inquiries */}
            <div
              className="mb-4"
              style={{
                display: 'flex',
                justifyContent: 'space-between',
                alignItems: 'center',
                borderBottom: '1px solid #eee',
                paddingBottom: '10px',
              }}
            >
              <h5 style={{ fontSize: '18px', color: '#000', marginBottom: '0' }}>
                Business Inquiries
              </h5>
              <button
                type="button"
                className="patient-search-modal-close-button"
                onClick={() => navigate('/contact-us')}
                aria-label="Close modal"
                style={{ flexShrink: 0 }}
              >
                <FontAwesomeIcon icon={faTimes} />
              </button>
            </div>

            {/* Form Fields */}
            <div className="mb-4">
              <NurtifyText label="Organization Name" />
              <NurtifyInput
                type="text"
                name="organization_name"
                placeholder="Enter organization name"
                value={businessForm.organization_name}
                onChange={handleBusinessChange}
              />
            </div>

            <div className="mb-4">
              <NurtifyText label="Contact Person" />
              <NurtifyInput
                type="text"
                name="contact_person"
                placeholder="Enter contact person's name"
                value={businessForm.contact_person}
                onChange={handleBusinessChange}
              />
            </div>

            <div className="mb-4">
              <NurtifyText label="Email" />
              <NurtifyInput
                type="email"
                name="email"
                placeholder="Enter email address"
                value={businessForm.email}
                onChange={handleBusinessChange}
              />
            </div>

            <div className="mb-4">
              <NurtifyText label="Phone" />
              <NurtifyInput
                type="tel"
                name="phone"
                placeholder="Enter phone number"
                value={businessForm.phone}
                onChange={handleBusinessChange}
              />
            </div>

            <div className="mb-4">
              <NurtifyText label="Message" />
              <NurtifyTextArea
                name="message"
                placeholder="Tell us about your needs and how we can help"
                value={businessForm.message}
                onChange={handleBusinessChange}
                rows={4}
              />
            </div>

            <div className="mb-4">
              <NurtifyCheckBox
                label="I consent to being contacted by Nurtify and agree to secure data handling."
                name="consent"
                value="consent" // The actual value is `checked`
                checked={businessForm.consent}
                onChange={handleBusinessChange}
              />
            </div>

            <div style={{ textAlign: 'center', marginTop: '40px' }}>
              <button
                type="submit"
                style={{
                  backgroundColor: '#37B7C3',
                  color: 'white',
                  border: 'none',
                  borderRadius: '25px',
                  padding: '14px 40px',
                  fontSize: '16px',
                  fontWeight: 600,
                  cursor: 'pointer',
                  transition: 'all 0.3s ease',
                  boxShadow: '0 4px 12px rgba(55, 183, 195, 0.3)',
                }}
                onMouseEnter={(e) => {
                  e.currentTarget.style.backgroundColor = '#2A9BAB';
                  e.currentTarget.style.transform = 'translateY(-2px)';
                }}
                onMouseLeave={(e) => {
                  e.currentTarget.style.backgroundColor = '#37B7C3';
                  e.currentTarget.style.transform = 'translateY(0)';
                }}
                disabled={submitBusinessInquiries.isPending}
              >
                {submitBusinessInquiries.isPending ? 'Submitting...' : 'SEND MESSAGE'}
              </button>
            </div>
          </div>
        );

      case 'patient':
        return (
          <div>
            {/* Form Fields */}
            {/* 1. Hospital & Trial Selection */}
            <div 
              className="mb-4" 
              style={{ 
                display: 'flex', 
                justifyContent: 'space-between', 
                alignItems: 'center', 
                borderBottom: '1px solid #eee', /* Move border here */
                paddingBottom: '10px' /* Adjust padding as needed */
              }}
            >
              <h5 style={{ fontSize: '18px', color: '#000', marginBottom: '0' }}> {/* Remove border-bottom from h5 */}
                Hospital & Trial Selection
              </h5>
              <button
                type="button"
                className="patient-search-modal-close-button"
                onClick={() => navigate('/contact-us')}
                aria-label="Close modal"
                style={{ flexShrink: 0 }}
              >
                <FontAwesomeIcon icon={faTimes} />
              </button>
            </div>

            <div className="mb-4">
              <NurtifyText label="Preferred Hospital" />
              <NurtifyComboBox
                options={hospitalOptions.length > 0 ? hospitalOptions : [
                  { value: "london-general", label: "London General Hospital" },
                  { value: "manchester-hospital", label: "Manchester Hospital" },
                  { value: "other-hospitals", label: "Other Hospitals" }
                ]}
                selectedValues={patientForm.preferredHospital ? [patientForm.preferredHospital] : []}
                onChange={(values) => setPatientForm({ ...patientForm, preferredHospital: values[0] || '' })}
              />
              {hospitalsLoading && <div style={{ color: '#888' }}>Loading hospitals...</div>}
              {hospitalsError && <div style={{ color: 'red' }}>Could not load hospitals</div>}
            </div>

            <div className="mb-4">
              <NurtifyText label="Trial of Interest" />
              <NurtifyComboBox
                options={trialOptions.length > 0 ? trialOptions : [
                  { value: "no-trials", label: "No trials available" }
                ]}
                selectedValues={patientForm.trialOfInterest ? [patientForm.trialOfInterest] : []}
                onChange={(values) => setPatientForm({ ...patientForm, trialOfInterest: values[0] || '' })}
              />
              {trialsLoading && <div style={{ color: '#888' }}>Loading trials...</div>}
            </div>

            {/* 2. Personal Information */}
            <div className="mb-4">
              <h5 style={{ fontSize: '18px', color: '#000', marginBottom: '15px', borderBottom: '1px solid #eee', paddingBottom: '10px' }}>
                Personal Information
              </h5>
            </div>

            <div className="row gx-2">
              <div className="col-12 col-md-6 mb-4">
                <NurtifyText label="First Name" />
                <NurtifyInput
                  type="text"
                  name="firstName"
                  placeholder="Enter your first name"
                  value={patientForm.firstName}
                  onChange={handlePatientChange}
                  maxLength={100}
                />
                {patientForm.errors.firstName && (
                  <div style={{ color: 'red', fontSize: '12px', marginTop: '5px' }}>
                    {patientForm.errors.firstName}
                  </div>
                )}
              </div>

              <div className="col-12 col-md-6 mb-4">
                <NurtifyText label="Last Name" />
                <NurtifyInput
                  type="text"
                  name="lastName"
                  placeholder="Enter your last name"
                  value={patientForm.lastName}
                  onChange={handlePatientChange}
                  maxLength={100}
                />
                {patientForm.errors.lastName && (
                  <div style={{ color: 'red', fontSize: '12px', marginTop: '5px' }}>
                    {patientForm.errors.lastName}
                  </div>
                )}
              </div>
            </div>

            <div className="mb-4">
              <NurtifyText label="Date of Birth" />
              <NurtifyDateInput
                name="dateOfBirth"
                value={patientForm.dateOfBirth}
                onChange={(event) => {
                  handlePatientChange({
                    target: {
                      name: event.target.name,
                      value: event.target.value,
                      type: "text"
                    }
                  } as ChangeEvent<HTMLInputElement>);
                }}
                placeholder="YYYY-MM-DD"
              />
              {patientForm.errors.dateOfBirth && (
                <div style={{ color: 'red', fontSize: '12px', marginTop: '5px' }}>
                  {patientForm.errors.dateOfBirth}
                </div>
              )}
            </div>

            <div className="mb-4">
              <NurtifyText label="Gender Identity" />
              <NurtifySelect
                name="genderIdentity"
                value={patientForm.genderIdentity}
                onChange={handlePatientChange}
                options={[
                  { value: "", label: "Select gender" },
                  { value: "Male", label: "Male" },
                  { value: "Female", label: "Female" },
                  { value: "Prefer_not_to_disclose", label: "Prefer not to disclose" }
                ]}
              />
              {patientForm.errors.genderIdentity && (
                <div style={{ color: 'red', fontSize: '12px', marginTop: '5px' }}>
                  {patientForm.errors.genderIdentity}
                </div>
              )}
            </div>

            <div className="mb-4">
              <NurtifyText label="Ethnic Background" />
              <NurtifySelect
                name="ethnicBackground"
                value={patientForm.ethnicBackground}
                onChange={handlePatientChange}
                options={[
                  { value: "", label: "Select ethnic background" },
                  { value: "White_British", label: "White (English/Welsh/Scottish/Northern Irish/British)" },
                  { value: "White_Irish", label: "White (Irish)" },
                  { value: "White_Gypsy", label: "White (Gypsy or Irish Traveller)" },
                  { value: "White_Other", label: "White (Other)" },
                  { value: "Asian_Indian", label: "Asian/Asian British (Indian)" },
                  { value: "Asian_Pakistani", label: "Asian/Asian British (Pakistani)" },
                  { value: "Asian_Bangladeshi", label: "Asian/Asian British (Bangladeshi)" },
                  { value: "Asian_Chinese", label: "Asian/Asian British (Chinese)" },
                  { value: "Asian_Other", label: "Asian/Asian British (Other)" },
                  { value: "Black_African", label: "Black/Black British (African)" },
                  { value: "Black_Caribbean", label: "Black/Black British (Caribbean)" },
                  { value: "Black_Other", label: "Black/Black British (Other)" },
                  { value: "Mixed", label: "Mixed/Multiple Ethnic Groups" },
                  { value: "Other", label: "Other Ethnic Group" },
                  { value: "Prefer_Not_to_Say", label: "Prefer Not to Say" }
                ]}
              />
              {patientForm.errors.ethnicBackground && (
                <div style={{ color: 'red', fontSize: '12px', marginTop: '5px' }}>
                  {patientForm.errors.ethnicBackground}
                </div>
              )}
            </div>

            <div className="mb-4">
              <NurtifyText label="Postcode" />
              <NurtifyInput
                type="text"
                name="postcode"
                placeholder="Enter your postcode"
                value={patientForm.postcode}
                onChange={handlePatientChange}
              />
              {patientForm.errors.postcode && (
                <div style={{ color: 'red', fontSize: '12px', marginTop: '5px' }}>
                  {patientForm.errors.postcode}
                </div>
              )}
            </div>

            <div className="mb-4">
              <NurtifyText label="NHS Number" />
              <NurtifyInput
                type="text"
                name="nhsNumber"
                placeholder="Enter your NHS number"
                value={patientForm.nhsNumber}
                onChange={handlePatientChange}
              />
              {patientForm.errors.nhsNumber && (
                <div style={{ color: 'red', fontSize: '12px', marginTop: '5px' }}>
                  {patientForm.errors.nhsNumber}
                </div>
              )}
            </div>

            {/* 3. Contact Details */}
            <div className="mb-4">
              <h5 style={{ fontSize: '18px', color: '#000', marginBottom: '15px', borderBottom: '1px solid #eee', paddingBottom: '10px' }}>
                Contact Details
              </h5>
            </div>

            <div className="mb-4">
              <NurtifyText label="Email" />
              <NurtifyInput
                type="email"
                name="email"
                placeholder="Enter your email address"
                value={patientForm.email}
                onChange={handlePatientChange}
                pattern="[a-z0-9._%+-]+@[a-z0-9.-]+\.[a-z]{2,}$"
              />
              {patientForm.errors.email && (
                <div style={{ color: 'red', fontSize: '12px', marginTop: '5px' }}>
                  {patientForm.errors.email}
                </div>
              )}
            </div>

            <div className="mb-4">
              <NurtifyText label="Phone Number" />
              <NurtifyInput
                type="tel"
                name="phone"
                placeholder="Enter your phone number"
                value={patientForm.phone}
                onChange={handlePatientChange}
                maxLength={20}
              />
              {patientForm.errors.phone && (
                <div style={{ color: 'red', fontSize: '12px', marginTop: '5px' }}>
                  {patientForm.errors.phone}
                </div>
              )}
            </div>

            {/* 4. Medical History */}
            <div className="mb-4">
              <h5 style={{ fontSize: '18px', color: '#000', marginBottom: '15px', borderBottom: '1px solid #eee', paddingBottom: '10px' }}>
                Medical History
              </h5>
            </div>

            <div className="mb-4">
              <NurtifyText label="Past Medical History" />
              <NurtifyComboBox
                options={[
                  { value: "diabetes", label: "Diabetes" },
                  { value: "hypertension", label: "Hypertension" },
                  { value: "asthma", label: "Asthma" },
                  { value: "heart-disease", label: "Heart Disease" },
                  { value: "cancer", label: "Cancer" },
                  { value: "arthritis", label: "Arthritis" },
                  { value: "depression", label: "Depression" }
                ]}
                selectedValues={patientForm.pastMedicalHistory}
                onChange={(values) => setPatientForm({ ...patientForm, pastMedicalHistory: values })}
              />
              {patientForm.errors.pastMedicalHistory && (
                <div style={{ color: 'red', fontSize: '12px', marginTop: '5px' }}>
                  {patientForm.errors.pastMedicalHistory}
                </div>
              )}
            </div>

            <div className="mb-4">
              <NurtifyText label="Current Medications" />
              <NurtifyComboBox
                options={[
                  { value: "insulin", label: "Insulin" },
                  { value: "metformin", label: "Metformin" },
                  { value: "statins", label: "Statins" },
                  { value: "aspirin", label: "Aspirin" },
                  { value: "beta-blockers", label: "Beta Blockers" },
                  { value: "antidepressants", label: "Antidepressants" }
                ]}
                selectedValues={patientForm.currentMedications}
                onChange={(values) => setPatientForm({ ...patientForm, currentMedications: values })}
              />
              {patientForm.errors.currentMedications && (
                <div style={{ color: 'red', fontSize: '12px', marginTop: '5px' }}>
                  {patientForm.errors.currentMedications}
                </div>
              )}
            </div>

            <div className="mb-4">
              <NurtifyText label="Allergies" />
              <NurtifyComboBox
                options={[
                  { value: "penicillin", label: "Penicillin" },
                  { value: "sulfa", label: "Sulfa Drugs" },
                  { value: "nsaids", label: "NSAIDs" },
                  { value: "peanuts", label: "Peanuts" },
                  { value: "shellfish", label: "Shellfish" },
                  { value: "latex", label: "Latex" }
                ]}
                selectedValues={patientForm.allergies}
                onChange={(values) => setPatientForm({ ...patientForm, allergies: values })}
              />
            </div>

            {/* 5. Lifestyle Factors */}
            <div className="mb-4">
              <h5 style={{ fontSize: '18px', color: '#000', marginBottom: '15px', borderBottom: '1px solid #eee', paddingBottom: '10px' }}>
                Lifestyle Factors
              </h5>
            </div>

            <div className="mb-4">
              <NurtifyText label="Smoking Status" />
              <NurtifySelect
                name="smokingStatus"
                value={patientForm.smokingStatus}
                onChange={handlePatientChange}
                options={[
                  { value: '', label: 'Select smoking status' },
                  { value: 'Current', label: 'Current' },
                  { value: 'Former', label: 'Former' },
                  { value: 'Never', label: 'Never' }
                ]}
              />
            </div>

            <div className="mb-4">
              <NurtifyText label="Alcohol Consumption" />
              <NurtifySelect
                name="alcoholConsumption"
                value={patientForm.alcoholConsumption}
                onChange={handlePatientChange}
                options={[
                  { value: '', label: 'Select alcohol consumption' },
                  { value: 'None', label: 'None' },
                  { value: 'Low', label: 'Low' },
                  { value: 'Moderate', label: 'Moderate' },
                  { value: 'High', label: 'High' }
                ]}
              />
            </div>

            <div className="mb-4">
              <NurtifyText label="Physical Activity Level" />
              <NurtifySelect
                name="physicalActivityLevel"
                value={patientForm.physicalActivityLevel}
                onChange={handlePatientChange}
                options={[
                  { value: '', label: 'Select physical activity level' },
                  { value: 'Low', label: 'Low' },
                  { value: 'Moderate', label: 'Moderate' },
                  { value: 'High', label: 'High' }
                ]}
              />
            </div>

            {/* 6. Eligibility Screening */}
            <div className="mb-4">
              <h5 style={{ fontSize: '18px', color: '#000', marginBottom: '15px', borderBottom: '1px solid #eee', paddingBottom: '10px' }}>
                Eligibility Screening
              </h5>
            </div>

            <div className="mb-4">
              <NurtifyText label="Pregnancy Status (if applicable)" />
              <NurtifySelect
                name="pregnancyStatus"
                value={patientForm.pregnancyStatus}
                onChange={handlePatientChange}
                options={[
                  { value: '', label: 'Select pregnancy status' },
                  { value: 'pregnant', label: 'Currently Pregnant' },
                  { value: 'not-pregnant', label: 'Not Pregnant' }
                ]}
              />
            </div>

            <div className="mb-4">
              <NurtifyText label="Willingness to Travel to the Trial Site" />
              <NurtifySelect
                name="willingnessToTravel"
                value={patientForm.willingnessToTravel}
                onChange={handlePatientChange}
                options={[
                  { value: '', label: 'Select willingness to travel' },
                  { value: 'yes', label: 'Yes' },
                  { value: 'no', label: 'No' }
                ]}
              />
            </div>

            {/* 7. Consent & Data Protection */}
            <div className="mb-4">
              <h5 style={{ fontSize: '18px', color: '#000', marginBottom: '15px', borderBottom: '1px solid #eee', paddingBottom: '10px' }}>
                Consent & Data Protection
              </h5>
            </div>

            <div className="mb-4">
              <NurtifyCheckBox
                label="I consent to be contacted and for my information to be securely shared with the selected hospital and researchers."
                name="consent"
                value="consent"
                checked={patientForm.consent}
                onChange={handlePatientChange}
              />
              {patientForm.errors.consent && (
                <div style={{ color: 'red', fontSize: '12px', marginTop: '5px' }}>
                  {patientForm.errors.consent}
                </div>
              )}
            </div>

            <div style={{ textAlign: 'center', marginTop: '40px' }}>
              <button
                type="submit"
                style={{
                  backgroundColor: '#37B7C3',
                  color: 'white',
                  border: 'none',
                  borderRadius: '25px',
                  padding: '14px 40px',
                  fontSize: '16px',
                  fontWeight: 600,
                  cursor: 'pointer',
                  transition: 'all 0.3s ease',
                  boxShadow: '0 4px 12px rgba(55, 183, 195, 0.3)'
                }}
                onMouseEnter={(e) => {
                  e.currentTarget.style.backgroundColor = '#2A9BAB';
                  e.currentTarget.style.transform = 'translateY(-2px)';
                }}
                onMouseLeave={(e) => {
                  e.currentTarget.style.backgroundColor = '#37B7C3';
                  e.currentTarget.style.transform = 'translateY(0)';
                }}
                disabled={submitPatientApplication.isPending}
              >
                {submitPatientApplication.isPending ? 'Submitting...' : 'SEND MY DETAILS'}
              </button>
              {patientForm.submitError && (
                <div
                  style={{
                    color: '#dc3545',
                    fontSize: '14px',
                    marginTop: '10px',
                    textAlign: 'center',
                    backgroundColor: '#f8d7da',
                    padding: '10px',
                    borderRadius: '5px',
                    border: '1px solid #f5c6cb'
                  }}
                >
                  {patientForm.submitError}
                </div>
              )}
            </div>
          </div>
        );

      case 'doctor':
        return (
          <div>
            {/* Form Fields */}
            {/* 1. Doctor/HCP Information */}
            <div 
              className="mb-4" 
              style={{ 
                display: 'flex', 
                justifyContent: 'space-between', 
                alignItems: 'center', 
                borderBottom: '1px solid #eee', /* Move border here */
                paddingBottom: '10px' /* Adjust padding as needed */
              }}
            >
              <h5 style={{ fontSize: '18px', color: '#000', marginBottom: '0' }}> {/* Remove border-bottom from h5 */}
                Doctor/HCP Information
              </h5>
              <button
                type="button"
                className="patient-search-modal-close-button"
                onClick={() => navigate('/contact-us')}
                aria-label="Close modal"
                style={{ flexShrink: 0 }}
              >
                <FontAwesomeIcon icon={faTimes} />
              </button>
            </div>

            <div className="mb-4">
              <NurtifyText label="Full Name" />
              <NurtifyInput
                type="text"
                name="fullName"
                placeholder="Enter your full name"
                value={doctorForm.fullName}
                onChange={handleDoctorChange}
              />
              {doctorForm.errors.fullName && (
                <div style={{ color: 'red', fontSize: '12px', marginTop: '5px' }}>
                  {doctorForm.errors.fullName}
                </div>
              )}
            </div>

            <div className="mb-4">
              <NurtifyText label="NHS Email Address" />
              <NurtifyInput
                type="email"
                name="nhsEmail"
                placeholder="Enter your NHS email"
                value={doctorForm.nhsEmail}
                onChange={handleDoctorChange}
              />
              {doctorForm.errors.nhsEmail && (
                <div style={{ color: 'red', fontSize: '12px', marginTop: '5px' }}>
                  {doctorForm.errors.nhsEmail}
                </div>
              )}
            </div>

            <div className="mb-4">
              <NurtifyText label="GMC/NMC Registration Number" />
              <NurtifyInput
                type="text"
                name="gmcNumber"
                placeholder="Enter your GMC/NMC number"
                value={doctorForm.gmcNumber}
                onChange={handleDoctorChange}
              />
              {doctorForm.errors.gmcNumber && (
                <div style={{ color: 'red', fontSize: '12px', marginTop: '5px' }}>
                  {doctorForm.errors.gmcNumber}
                </div>
              )}
            </div>

            <div className="mb-4">
              <NurtifyText label="Practice/Organization Name" />
              <NurtifyInput
                type="text"
                name="practiceOrganization"
                placeholder="Enter your practice or organization"
                value={doctorForm.practiceOrganization}
                onChange={handleDoctorChange}
              />
              {doctorForm.errors.practiceOrganization && (
                <div style={{ color: 'red', fontSize: '12px', marginTop: '5px' }}>
                  {doctorForm.errors.practiceOrganization}
                </div>
              )}
            </div>

            <div className="mb-4">
              <NurtifyText label="Contact Phone Number" />
              <NurtifyInput
                type="tel"
                name="contactPhone"
                placeholder="Enter your contact phone"
                value={doctorForm.contactPhone}
                onChange={handleDoctorChange}
              />
              {doctorForm.errors.contactPhone && (
                <div style={{ color: 'red', fontSize: '12px', marginTop: '5px' }}>
                  {doctorForm.errors.contactPhone}
                </div>
              )}
            </div>

            {/* 2. Patient Information */}
            <div className="mb-4">
              <h5 style={{ fontSize: '18px', color: '#000', marginBottom: '15px', borderBottom: '1px solid #eee', paddingBottom: '10px' }}>
                Patient Information
              </h5>
            </div>

            <div className="row gx-2">
              <div className="col-12 col-md-6 mb-4">
                <NurtifyText label="First Name" />
                <NurtifyInput
                  type="text"
                  name="patientFirstName"
                  placeholder="Enter patient's first name"
                  value={doctorForm.patientFirstName}
                  onChange={handleDoctorChange}
                />
                {doctorForm.errors.patientFirstName && (
                  <div style={{ color: 'red', fontSize: '12px', marginTop: '5px' }}>
                    {doctorForm.errors.patientFirstName}
                  </div>
                )}
              </div>

              <div className="col-12 col-md-6 mb-4">
                <NurtifyText label="Last Name" />
                <NurtifyInput
                  type="text"
                  name="patientLastName"
                  placeholder="Enter patient's last name"
                  value={doctorForm.patientLastName}
                  onChange={handleDoctorChange}
                />
                {doctorForm.errors.patientLastName && (
                  <div style={{ color: 'red', fontSize: '12px', marginTop: '5px' }}>
                    {doctorForm.errors.patientLastName}
                  </div>
                )}
              </div>
            </div>

            <div className="mb-4">
              <NurtifyText label="Date of Birth" />
              <NurtifyDateInput
                name="patientDob"
                value={doctorForm.patientDob}
                onChange={(event) => {
                  handleDoctorChange({
                    target: {
                      name: event.target.name,
                      value: event.target.value,
                      type: "text"
                    }
                  } as ChangeEvent<HTMLInputElement>);
                }}
                placeholder="YYYY-MM-DD"
              />
              {doctorForm.errors.patientDob && (
                <div style={{ color: 'red', fontSize: '12px', marginTop: '5px' }}>
                  {doctorForm.errors.patientDob}
                </div>
              )}
            </div>

            <div className="mb-4">
              <NurtifyText label="NHS Number" />
              <NurtifyInput
                type="text"
                name="nhsNumber"
                placeholder="Enter NHS number"
                value={doctorForm.nhsNumber}
                onChange={handleDoctorChange}
              />
              {doctorForm.errors.nhsNumber && (
                <div style={{ color: 'red', fontSize: '12px', marginTop: '5px' }}>
                  {doctorForm.errors.nhsNumber}
                </div>
              )}
            </div>

            <div className="mb-4">
              <NurtifyText label="Ethnic Background" />
              <NurtifySelect
                name="ethnicBackground"
                value={doctorForm.ethnicBackground}
                onChange={handleDoctorChange}
                options={[
                  { value: "", label: "Select ethnic background" },
                  { value: "White_British", label: "White (English/Welsh/Scottish/Northern Irish/British)" },
                  { value: "White_Irish", label: "White (Irish)" },
                  { value: "White_Gypsy", label: "White (Gypsy or Irish Traveller)" },
                  { value: "White_Other", label: "White (Other)" },
                  { value: "Asian_Indian", label: "Asian/Asian British (Indian)" },
                  { value: "Asian_Pakistani", label: "Asian/Asian British (Pakistani)" },
                  { value: "Asian_Bangladeshi", label: "Asian/Asian British (Bangladeshi)" },
                  { value: "Asian_Chinese", label: "Asian/Asian British (Chinese)" },
                  { value: "Asian_Other", label: "Asian/Asian British (Other)" },
                  { value: "Black_African", label: "Black/Black British (African)" },
                  { value: "Black_Caribbean", label: "Black/Black British (Caribbean)" },
                  { value: "Black_Other", label: "Black/Black British (Other)" },
                  { value: "Mixed", label: "Mixed/Multiple Ethnic Groups" },
                  { value: "Other", label: "Other Ethnic Group" },
                  { value: "Prefer_Not_to_Say", label: "Prefer Not to Say" }
                ]}
              />
              {doctorForm.errors.ethnicBackground && (
                <div style={{ color: 'red', fontSize: '12px', marginTop: '5px' }}>
                  {doctorForm.errors.ethnicBackground}
                </div>
              )}
            </div>

            <div className="mb-4">
              <NurtifyText label="Postcode" />
              <NurtifyInput
                type="text"
                name="postcode"
                placeholder="Enter patient's postcode"
                value={doctorForm.postcode}
                onChange={handleDoctorChange}
              />
              {doctorForm.errors.postcode && (
                <div style={{ color: 'red', fontSize: '12px', marginTop: '5px' }}>
                  {doctorForm.errors.postcode}
                </div>
              )}
            </div>

            <div className="mb-4">
              <NurtifyText label="Phone" />
              <NurtifyInput
                type="tel"
                name="patientPhone"
                placeholder="Enter patient's phone"
                value={doctorForm.patientPhone}
                onChange={handleDoctorChange}
              />
              {doctorForm.errors.patientPhone && (
                <div style={{ color: 'red', fontSize: '12px', marginTop: '5px' }}>
                  {doctorForm.errors.patientPhone}
                </div>
              )}
            </div>

            <div className="mb-4">
              <NurtifyText label="Previous Medical History" />
              <NurtifyComboBox
                options={[
                  { value: "diabetes", label: "Diabetes" },
                  { value: "hypertension", label: "Hypertension" },
                  { value: "asthma", label: "Asthma" },
                  { value: "heart-disease", label: "Heart Disease" },
                  { value: "cancer", label: "Cancer" },
                  { value: "arthritis", label: "Arthritis" },
                  { value: "depression", label: "Depression" }
                ]}
                selectedValues={doctorForm.previousMedicalHistory}
                onChange={(values) => setDoctorForm({ ...doctorForm, previousMedicalHistory: values })}
              />
              {doctorForm.errors.previousMedicalHistory && (
                <div style={{ color: 'red', fontSize: '12px', marginTop: '5px' }}>
                  {doctorForm.errors.previousMedicalHistory}
                </div>
              )}
            </div>

            <div className="mb-4">
              <NurtifyText label="Email" />
              <NurtifyInput
                type="email"
                name="patientEmail"
                placeholder="Enter patient's email"
                value={doctorForm.patientEmail}
                onChange={handleDoctorChange}
              />
              {doctorForm.errors.patientEmail && (
                <div style={{ color: 'red', fontSize: '12px', marginTop: '5px' }}>
                  {doctorForm.errors.patientEmail}
                </div>
              )}
            </div>

            <div className="mb-4">
              <NurtifyText label="Current Medications" />
              <NurtifyComboBox
                options={[
                  { value: "insulin", label: "Insulin" },
                  { value: "metformin", label: "Metformin" },
                  { value: "statins", label: "Statins" },
                  { value: "aspirin", label: "Aspirin" },
                  { value: "beta-blockers", label: "Beta Blockers" },
                  { value: "antidepressants", label: "Antidepressants" }
                ]}
                selectedValues={doctorForm.currentMedications}
                onChange={(values) => setDoctorForm({ ...doctorForm, currentMedications: values })}
              />
              {doctorForm.errors.currentMedications && (
                <div style={{ color: 'red', fontSize: '12px', marginTop: '5px' }}>
                  {doctorForm.errors.currentMedications}
                </div>
              )}
            </div>

            {/* 3. Trial-Specific Details */}
            <div className="mb-4">
              <h5 style={{ fontSize: '18px', color: '#000', marginBottom: '15px', borderBottom: '1px solid #eee', paddingBottom: '10px' }}>
                Trial-Specific Details
              </h5>
            </div>

            <div className="mb-4">
              <NurtifyText label="Trial of Interest" />
              <NurtifyComboBox
                options={activeTrialOptions.length > 0 ? activeTrialOptions : [
                  { value: "no-trials", label: "No trials available" }
                ]}
                selectedValues={doctorForm.trialOfInterest ? [doctorForm.trialOfInterest] : []}
                onChange={(values) => setDoctorForm({ ...doctorForm, trialOfInterest: values[0] || '' })}
              />
              {activeTrialsLoading && <div style={{ color: '#888' }}>Loading trials...</div>}
            </div>

            <div className="mb-4">
              <NurtifyText label="Reason for Referral" />
              <NurtifyTextArea
                name="reasonForReferral"
                placeholder="Enter reason for referral"
                value={doctorForm.reasonForReferral}
                onChange={handleDoctorChange}
              />
              {doctorForm.errors.reasonForReferral && (
                <div style={{ color: 'red', fontSize: '12px', marginTop: '5px' }}>
                  {doctorForm.errors.reasonForReferral}
                </div>
              )}
            </div>

            <div className="mb-4">
              <NurtifyText label="Patient's Suitability Notes" />
              <NurtifyTextArea
                name="patientSuitabilityNotes"
                placeholder="Enter notes about patient's suitability"
                value={doctorForm.patientSuitabilityNotes}
                onChange={handleDoctorChange}
              />
              {doctorForm.errors.patientSuitabilityNotes && (
                <div style={{ color: 'red', fontSize: '12px', marginTop: '5px' }}>
                  {doctorForm.errors.patientSuitabilityNotes}
                </div>
              )}
            </div>

            <div className="mb-4">
              <NurtifyText label="Attached Documents" />
              <NurtifyAttachFileBox
                onChange={handleFileChange}
                accept=".pdf,.doc,.docx,.jpg,.jpeg,.png,.mp4"
                multiple={true}
              />
              <small className="text-muted">
                Accepted formats: PDF, DOC, DOCX, JPG, JPEG, PNG, MP4. Max file size: 10MB
              </small>
              {doctorForm.attachedDocuments.length > 0 && (
                <div className="mt-2">
                  <p className="mb-2">Selected files:</p>
                  <ul className="list-unstyled">
                    {doctorForm.attachedDocuments.map((file, index) => (
                      <li key={index} className="text-sm">
                        {file.name} ({(file.size / 1024 / 1024).toFixed(2)} MB)
                      </li>
                    ))}
                  </ul>
                </div>
              )}
            </div>

            {/* 4. Consent & Compliance */}
            <div className="mb-4">
              <h5 style={{ fontSize: '18px', color: '#000', marginBottom: '15px', borderBottom: '1px solid #eee', paddingBottom: '10px' }}>
                Consent & Compliance
              </h5>
            </div>

            <div className="mb-4">
              <NurtifyText label="Willingness to Travel to the Trial Site" />
              <NurtifySelect
                name="willingnessToTravel"
                value={doctorForm.willingnessToTravel}
                onChange={handleDoctorChange}
                options={[
                  { value: '', label: 'Select willingness to travel' },
                  { value: 'yes', label: 'Yes' },
                  { value: 'no', label: 'No' }
                ]}
              />
              {doctorForm.errors.willingnessToTravel && (
                <div style={{ color: 'red', fontSize: '12px', marginTop: '5px' }}>
                  {doctorForm.errors.willingnessToTravel}
                </div>
              )}
            </div>

            {/* 5. Consent & Data Protection */}
            <div className="mb-4">
              <h5 style={{ fontSize: '18px', color: '#000', marginBottom: '15px', borderBottom: '1px solid #eee', paddingBottom: '10px' }}>
                Consent & Data Protection
              </h5>
            </div>

            <div className="mb-4">
              <NurtifyCheckBox
                label="I confirm I have patient's consent to share this information with Nurtify and researchers."
                name="consent"
                value="consent"
                checked={doctorForm.consent}
                onChange={handleDoctorChange}
              />
              {doctorForm.errors.consent && (
                <div style={{ color: 'red', fontSize: '12px', marginTop: '5px' }}>
                  {doctorForm.errors.consent}
                </div>
              )}
            </div>

            <div style={{ textAlign: 'center', marginTop: '40px' }}>
              <button
                type="submit"
                style={{
                  backgroundColor: '#37B7C3',
                  color: 'white',
                  border: 'none',
                  borderRadius: '25px',
                  padding: '14px 40px',
                  fontSize: '16px',
                  fontWeight: 600,
                  cursor: 'pointer',
                  transition: 'all 0.3s ease',
                  boxShadow: '0 4px 12px rgba(55, 183, 195, 0.3)'
                }}
                onMouseEnter={(e) => {
                  e.currentTarget.style.backgroundColor = '#2A9BAB';
                  e.currentTarget.style.transform = 'translateY(-2px)';
                }}
                onMouseLeave={(e) => {
                  e.currentTarget.style.backgroundColor = '#37B7C3';
                  e.currentTarget.style.transform = 'translateY(0)';
                }}
                disabled={submitDoctorReferral.isPending}
              >
                {submitDoctorReferral.isPending ? 'Submitting...' : 'REFER PATIENT'}
              </button>
              {doctorForm.submitError && (
                <div
                  style={{
                    color: '#dc3545',
                    fontSize: '14px',
                    marginTop: '10px',
                    textAlign: 'center',
                    backgroundColor: '#f8d7da',
                    padding: '10px',
                    borderRadius: '5px',
                    border: '1px solid #f5c6cb'
                  }}
                >
                  {doctorForm.submitError}
                </div>
              )}
            </div>
          </div>
        );

      case 'support':
        return (
          <div>
            {/* Form Fields */}
            {/* 1. Support Inquiry */}
            <div
              className="mb-4"
              style={{
                display: 'flex',
                justifyContent: 'space-between',
                alignItems: 'center',
                borderBottom: '1px solid #eee',
                paddingBottom: '10px',
              }}
            >
              <h5 style={{ fontSize: '18px', color: '#000', marginBottom: '0' }}>
                Support Inquiry
              </h5>
              <button
                type="button"
                className="patient-search-modal-close-button"
                onClick={() => navigate('/contact-us')}
                aria-label="Close modal"
                style={{ flexShrink: 0 }}
              >
                <FontAwesomeIcon icon={faTimes} />
              </button>
            </div>

            {/* Form Fields */}
            <div className="mb-4">
              <NurtifyText label="Full Name" />
              <NurtifyInput
                type="text"
                name="name"
                placeholder="Enter your full name"
                value={supportForm.name}
                onChange={handleSupportChange}
              />
            </div>

            <div className="mb-4">
              <NurtifyText label="Email" />
              <NurtifyInput
                type="email"
                name="email"
                placeholder="Enter your email address"
                value={supportForm.email}
                onChange={handleSupportChange}
              />
            </div>

            <div className="mb-4">
              <NurtifyText label="Phone" />
              <NurtifyInput
                type="tel"
                name="phone"
                placeholder="Enter your phone number"
                value={supportForm.phone} // Make sure this is linked to state
                onChange={handleSupportChange} // Make sure this is linked to handler
              />
            </div>

            <div className="mb-4">
              <NurtifyText label="Issue Category" />
              <NurtifySelect
                name="issue_type"
                value={supportForm.issue_type}
                onChange={handleSupportChange}
                options={[
                  { value: '', label: 'Select category' },
                  { value: 'technical', label: 'Technical Issue' },
                  { value: 'account', label: 'Account Access' },
                  { value: 'billing', label: 'Billing Question' },
                  { value: 'feature', label: 'Feature Request' },
                  { value: 'other', label: 'Other' },
                ]}
              />
            </div>

            <div className="mb-4">
              <NurtifyText label="Describe Your Issue" />
              <NurtifyTextArea
                name="message"
                placeholder="Provide details about your issue"
                value={supportForm.message}
                onChange={handleSupportChange}
                rows={6}
              />
            </div>

            {/* File Upload Section */}
            <div className="mb-4">
              <NurtifyText label="Upload Screenshot" />
              <NurtifyAttachFileBox
                onChange={handleSupportFileChange}
                accept=".pdf,.doc,.docx,.jpg,.jpeg,.png,.mp4"
                multiple={false}
              />
              <small className="text-muted">
                Accepted formats: PDF, DOC, DOCX, JPG, JPEG, PNG, MP4. Max file size: 10MB
              </small>

              {supportForm.uploaded_file && ( // Correctly reference supportForm.uploaded_file
                <div className="mt-2">
                  <p className="mb-2">Selected file:</p>
                  <ul className="list-unstyled" style={{ paddingLeft: 0 }}>
                    <li
                      className="d-flex align-items-center justify-content-between py-2 px-3 mb-2"
                      style={{
                        border: '1px solid #eee',
                        borderRadius: '5px',
                        backgroundColor: '#f9f9f9',
                        listStyle: 'none',
                      }}
                    >
                      <div style={{ display: 'flex', alignItems: 'center', flexGrow: 1, minWidth: 0 }}>
                        {supportForm.uploaded_file.type.startsWith('image/') ? (
                          <img
                            src={URL.createObjectURL(supportForm.uploaded_file)}
                            alt={supportForm.uploaded_file.name}
                            style={{
                              width: '50px',
                              height: '50px',
                              objectFit: 'cover',
                              marginRight: '10px',
                              borderRadius: '3px',
                            }}
                          />
                        ) : (
                          <FontAwesomeIcon
                            icon={getFileIcon(supportForm.uploaded_file.name)}
                            style={{ fontSize: '24px', color: '#666', marginRight: '10px' }}
                          />
                        )}
                        <span
                          style={{
                            flexGrow: 1,
                            whiteSpace: 'nowrap',
                            overflow: 'hidden',
                            textOverflow: 'ellipsis',
                            fontSize: '14px',
                          }}
                        >
                          {supportForm.uploaded_file.name} (
                          {(supportForm.uploaded_file.size / 1024 / 1024).toFixed(2)} MB)
                        </span>
                      </div>
                      <button
                        type="button"
                        onClick={handleRemoveFile} // Call the new remove function
                        className="btn btn-sm btn-light ml-2"
                        style={{
                          background: 'none',
                          border: 'none',
                          color: '#dc3545',
                          cursor: 'pointer',
                          fontSize: '18px',
                          marginLeft: '10px',
                          padding: '0 5px',
                          lineHeight: '1',
                        }}
                        aria-label={`Remove ${supportForm.uploaded_file.name}`}
                      >
                        <FontAwesomeIcon icon={faTimes} />
                      </button>
                    </li>
                  </ul>
                </div>
              )}
            </div>

            <div className="mb-4">
              <NurtifyCheckBox
                label="I agree to be contacted regarding this query and understand my information will be used for support purposes only."
                name="consent"
                value="consent"
                checked={supportForm.consent}
                onChange={handleSupportChange}
              />
            </div>

            <div style={{ textAlign: 'center', marginTop: '40px' }}>
              <button
                type="submit"
                style={{
                  backgroundColor: '#37B7C3',
                  color: 'white',
                  border: 'none',
                  borderRadius: '25px',
                  padding: '14px 40px',
                  fontSize: '16px',
                  fontWeight: 600,
                  cursor: 'pointer',
                  transition: 'all 0.3s ease',
                  boxShadow: '0 4px 12px rgba(55, 183, 195, 0.3)',
                }}
                onMouseEnter={(e) => {
                  e.currentTarget.style.backgroundColor = '#2A9BAB';
                  e.currentTarget.style.transform = 'translateY(-2px)';
                }}
                onMouseLeave={(e) => {
                  e.currentTarget.style.backgroundColor = '#37B7C3';
                  e.currentTarget.style.transform = 'translateY(0)';
                }}
                disabled={submitSupportInquiry.isPending}
              >
                {submitSupportInquiry.isPending ? 'Submitting...' : 'SUBMIT TICKET'}
              </button>
            </div>
          </div>
        );

      default:
        return null;
    }
  };

  // Update the handleFileChange function to handle a single file
  const handleFileChange = (files: FileList | null): void => {
    if (files && files.length > 0) {
      // Take only the first file
      setDoctorForm(prev => ({
        ...prev,
        attachedDocuments: [files[0]]
      }));
    } else {
      setDoctorForm(prev => ({
        ...prev,
        attachedDocuments: []
      }));
    }
  };

  // Dynamic header content based on category
  const getHeaderContent = () => {
    switch (activeCategory) {
      case 'business':
        return {
          title: 'Partner with Nurtify',
          description: 'Join us in revolutionizing clinical research. Whether you\'re looking to collaborate, invest, or explore partnership opportunities, we\'d love to hear from you.'
        };
      case 'patient':
        return {
          title: 'Join a Clinical Study',
          description: 'Interested in participating in groundbreaking clinical research? Share your information and we\'ll connect you with relevant studies in your area.'
        };
      case 'doctor':
        return {
          title: 'Refer a Patient to Nurtify',
          description: 'As a healthcare professional, you can help your patients access cutting-edge clinical trials. Use our secure referral system to connect patients with relevant research opportunities.'
        };
      case 'support':
        return {
          title: 'Have a Question?\nWe\'re Here to Help.',
          description: 'Whether you\'re facing technical issues or have general questions about our platform, our support team is ready to assist you.'
        };
      default:
        return {
          title: 'Have a Question?\nWe\'re Here to Help.',
          description: 'Whether you\'re a patient looking to join a clinical study, a healthcare professional wanting to refer a patient, or have questions about our research work - we\'re here to help.'
        };
    }
  };

  const headerContent = getHeaderContent();

  return (
    <div className="main-content-contact-us">
      <Preloader />
      <div className="content-wrapper js-content-wrapper overflow-hidden">
        {/* Header Section */}
        <section className="contact-header-section" style={{ display: "flex", alignItems: "center", justifyContent: "center", position: "relative" }}>
          <img
            src="/assets/img/contact-us/ContactUsHeader.png"
            alt="Contact Us Header"
            style={{ width: "100%", height: "100%", objectFit: "cover", minHeight: "320px", maxHeight: "100vh", display: "block" }}
          />
          <div
            className="contact-header-content"
            style={{
              position: "absolute",
              top: "50%",
              left: "50%",
              transform: "translate(-50%, -50%)",
              maxWidth: "90%",
              zIndex: 1,
              color: "white",
              display: "flex",
              flexDirection: "column",
              alignItems: "center",
              textAlign: "center",
              width: "50%",
            }}
          >
            <h1 className="responsive-heading" style={{ textAlign: "center", width: "100%" }}>
              {headerContent.title}
            </h1>
            <p className="responsive-desc" style={{ textAlign: "center", width: "100%" }}>
              {headerContent.description}
            </p>
          </div>
        </section>

        {/* Form Section */}
        <section style={{ backgroundColor: '#f8f9fa', minHeight: '100vh', paddingTop: '60px', paddingBottom: '60px' }}>
          <div className="container">
            <div className="row justify-content-center">
              <div className="col-12 col-lg-10 col-xl-8">
                <div
                  style={{
                    marginTop:"-180px",
                    backgroundColor: 'white',
                    borderRadius: '20px',
                    padding: '40px',
                    boxShadow: '0 10px 30px rgba(0,0,0,0.1)',
                    border: '1px solid rgba(0,0,0,0.08)'
                  }}
                >
                  <form onSubmit={handleSubmit}>
                    {renderForm()}
                  </form>
                </div>
              </div>
            </div>
          </div>
        </section>
        <DarkFooter />
      </div>
    </div>
  );
}

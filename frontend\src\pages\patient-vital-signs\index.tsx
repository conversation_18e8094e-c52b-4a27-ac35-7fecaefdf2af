import React, { useState } from 'react';
import { 
  Heart, 
  Thermometer, 
  Activity, 
  Droplets, 
  Wind, 
  Scale, 
  Ruler, 
  Plus,
  Edit3,
  Calendar,
  TrendingUp,
  AlertCircle,
  BarChart3
} from 'lucide-react';
import { CreateVitalSignData, UpdateVitalSignData, VitalSign } from '../../services/api/vital-signs.service';
import { 
  getVitalSignColor, 
  formatVitalSignValue, 
  formatDate,
  getTrendIcon,
  getTrendColor,
  getNewsSeverityColor
} from '../../utils/vitalSignsUtils';
import { usePatientVitalSigns, useCreateVitalSign, useUpdateVitalSign } from '../../hooks/vital-signs.query';
import useSelectedPatientStore from '../../store/SelectedPatientState';
import AddVitalSignModal from '../../components/modal/AddVitalSignModal';
import UpdateVitalSignModal from '../../components/modal/UpdateVitalSignModal';
import VitalSignsChart from '../../components/vital-signs/VitalSignsChart';
import VitalSignsHistory from '../../components/vital-signs/VitalSignsHistory';
import IndividualVitalSignHistory from '../../components/vital-signs/IndividualVitalSignHistory';
import './patient-vital-signs.css';

const PatientVitalSigns: React.FC = () => {
  const { selectedPatient } = useSelectedPatientStore();
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [isUpdateModalOpen, setIsUpdateModalOpen] = useState(false);
  const [selectedVitalSign, setSelectedVitalSign] = useState<VitalSign | null>(null);
  const [showDetailedHistory, setShowDetailedHistory] = useState(false);
  const [showIndividualHistory, setShowIndividualHistory] = useState(false);
  const [selectedField, setSelectedField] = useState<{ name: string; displayName: string } | null>(null);
  const [showAllRecords, setShowAllRecords] = useState(false);
  
  // Filter state (move to top to avoid conditional hook calls)
  const [severity, setSeverity] = useState('all');
  const [showToday, setShowToday] = useState(false);
  const [lastHours, setLastHours] = useState('');
  const [fromDate, setFromDate] = useState('');
  const [toDate, setToDate] = useState('');
  
  // Don't fetch data if no patient is selected
  const { summary, vitalSigns, chartData, isLoading, isError, refetch } = usePatientVitalSigns(selectedPatient?.uuid || '');
  const createVitalSignMutation = useCreateVitalSign();
  const updateVitalSignMutation = useUpdateVitalSign();

  // Show message if no patient is selected
  if (!selectedPatient) {
    return (
      <div className="vital-signs-container">
        <div className="no-patient-selected">
          <p>Please select a patient to view vital signs.</p>
        </div>
      </div>
    );
  }

  const handleAddVitalSign = async (data: CreateVitalSignData) => {
    try {
      await createVitalSignMutation.mutateAsync(data);
      setIsModalOpen(false);
    } catch (err) {
      console.error('Error creating vital sign:', err);
      // Error handling is managed by React Query
    }
  };

  const handleUpdateVitalSign = async (data: UpdateVitalSignData) => {
    if (!selectedVitalSign) return;
    
    try {
      await updateVitalSignMutation.mutateAsync({
        uuid: selectedVitalSign.uuid,
        data
      });
      setIsUpdateModalOpen(false);
      setSelectedVitalSign(null);
    } catch (err) {
      console.error('Error updating vital sign:', err);
      // Error handling is managed by React Query
    }
  };

  const handleViewIndividualHistory = (fieldName: string, fieldDisplayName: string) => {
    setSelectedField({ name: fieldName, displayName: fieldDisplayName });
    setShowIndividualHistory(true);
  };

  const getVitalSignIcon = (type: string) => {
    switch (type) {
      case 'heart_rate':
        return <Heart className="icon heart-rate" />;
      case 'temperature':
        return <Thermometer className="icon temperature" />;
      case 'systolic_bp':
      case 'diastolic_bp':
        return <Activity className="icon blood-pressure" />;
      case 'oxygen_saturation':
        return <Droplets className="icon oxygen-saturation" />;
      case 'respiratory_rate':
        return <Wind className="icon respiratory-rate" />;
      case 'weight':
        return <Scale className="icon weight" />;
      case 'height':
        return <Ruler className="icon height" />;
      case 'blood_sugar':
        return <Activity className="icon blood-pressure" />;
      default:
        return <Activity className="icon" />;
    }
  };

  const renderVitalSignCard = (title: string, value: number | undefined, unit?: string, type?: string, trend?: string) => {
    const color = type && value !== undefined ? getVitalSignColor(type, value, unit) : '#6b7280';
    const formattedValue = formatVitalSignValue(value, type || '', unit);
    
    return (
      <div className="vital-sign-card" style={{ borderLeft: `4px solid ${color}` }}>
        <div className="vital-sign-header">
          <div style={{ display: 'flex', alignItems: 'center', gap: '0.5rem' }}>
            {type && getVitalSignIcon(type)}
            <h3>{title}</h3>
          </div>
          {type && (
            <button 
              className="history-btn"
              onClick={() => handleViewIndividualHistory(type, title)}
              title={`View ${title} history`}
            >
              <BarChart3 size={16} />
            </button>
          )}
        </div>
        <div className="vital-sign-value" style={{ color }}>
          {formattedValue}
        </div>
        {trend && (
          <div className="vital-sign-trend" style={{ color: getTrendColor(trend) }}>
            {getTrendIcon(trend)} {trend}
          </div>
        )}
      </div>
    );
  };

  const renderLatestVitalSigns = () => {
    if (!summary) return null;

    // Use the latest vital sign record if summary doesn't have the new fields
    const latestVitalSign = vitalSigns[0];

    return (
      <div className="vital-signs-grid">
        {renderVitalSignCard(
          'Blood Pressure',
          summary.latest_systolic_bp,
          undefined,
          'systolic_bp',
          summary.blood_pressure_trend
        )}
        {summary.latest_diastolic_bp && (
          <div className="vital-sign-card" style={{ borderLeft: '4px solid #6b7280' }}>
            <div className="vital-sign-header">
              <div style={{ display: 'flex', alignItems: 'center', gap: '0.5rem' }}>
                <Activity className="icon blood-pressure" />
                <h3>Diastolic BP</h3>
              </div>
              <button 
                className="history-btn"
                onClick={() => handleViewIndividualHistory('diastolic_bp', 'Diastolic Blood Pressure')}
                title="View Diastolic Blood Pressure history"
              >
                <BarChart3 size={16} />
              </button>
            </div>
            <div className="vital-sign-value">{formatVitalSignValue(summary.latest_diastolic_bp, 'diastolic_bp')}</div>
          </div>
        )}
        {renderVitalSignCard(
          'Heart Rate',
          summary.latest_heart_rate,
          undefined,
          'heart_rate',
          summary.heart_rate_trend
        )}
        {renderVitalSignCard(
          'Temperature',
          summary.latest_temperature,
          summary.latest_temp_unit || 'C',
          'temperature',
          summary.temperature_trend
        )}
        {renderVitalSignCard(
          'Respiratory Rate',
          summary.latest_respiratory_rate,
          undefined,
          'respiratory_rate'
        )}
        {renderVitalSignCard(
          'Oxygen Saturation',
          summary.latest_oxygen_saturation,
          undefined,
          'oxygen_saturation'
        )}
        {renderVitalSignCard(
          'Blood Sugar',
          latestVitalSign?.blood_sugar || summary.latest_blood_sugar,
          latestVitalSign?.blood_sugar_unit || summary.latest_blood_sugar_unit || 'mg/dL',
          'blood_sugar'
        )}
        {renderVitalSignCard(
          'Height',
          latestVitalSign?.height || summary.latest_height,
          latestVitalSign?.height_unit || summary.latest_height_unit || 'cm',
          'height'
        )}
        {renderVitalSignCard(
          'Weight',
          latestVitalSign?.weight || summary.latest_weight,
          latestVitalSign?.weight_unit || summary.latest_weight_unit || 'kg',
          'weight'
        )}
        <div 
          className="vital-sign-card news-score-card" 
          style={{ 
            borderLeft: `4px solid ${getNewsSeverityColor(summary.latest_news_severity)}`,
            background: `${getNewsSeverityColor(summary.latest_news_severity)}10`
          }}
        >
          <div className="vital-sign-header">
            <div style={{ display: 'flex', alignItems: 'center', gap: '0.5rem' }}>
              <AlertCircle className="icon" style={{ color: getNewsSeverityColor(summary.latest_news_severity) }} />
              <h3>NEWS Score</h3>
            </div>
          </div>
          <div className="vital-sign-value" style={{ color: getNewsSeverityColor(summary.latest_news_severity) }}>
            {summary.latest_news_score}
          </div>
          <div className="news-severity" style={{ color: getNewsSeverityColor(summary.latest_news_severity) }}>
            {summary.latest_news_severity} Risk
          </div>
        </div>
      </div>
    );
  };



  // Helper to determine severity (stub: you can adjust logic as needed)
  const getSeverity = (record: any) => {
    // Example: use NEWS score if available, or implement your own logic
    if (typeof record.news_score === 'number') {
      if (record.news_score >= 7) return 'critical';
      if (record.news_score >= 5) return 'high';
      if (record.news_score >= 1) return 'low';
      return 'normal';
    }
    return 'normal';
  };

  // Helper to filter records based on filters
  const getFilteredRecords = () => {
    // Start with ALL records, not just recent ones
    let records = [...vitalSigns];
    const now = new Date();
    
    // Date range filter
    if (fromDate) {
      const from = new Date(fromDate + 'T00:00:00');
      records = records.filter(r => {
        const recordDate = new Date(r.recorded_at);
        return recordDate >= from;
      });
    }
    
    if (toDate) {
      const to = new Date(toDate + 'T23:59:59');
      records = records.filter(r => {
        const recordDate = new Date(r.recorded_at);
        return recordDate <= to;
      });
    }
    
    // Today filter
    if (showToday) {
      const today = new Date();
      today.setHours(0, 0, 0, 0);
      records = records.filter(r => {
        const recordDate = new Date(r.recorded_at);
        return recordDate >= today;
      });
    }
    
    // Last X hours filter
    if (lastHours && !isNaN(Number(lastHours))) {
      const cutoff = new Date(now.getTime() - Number(lastHours) * 60 * 60 * 1000);
      records = records.filter(r => {
        const recordDate = new Date(r.recorded_at);
        return recordDate >= cutoff;
      });
    }
    
    // Severity filter
    if (severity !== 'all') {
      records = records.filter(r => getSeverity(r) === severity);
    }
    
    // Sort newest to oldest
    return records.sort((a, b) => new Date(b.recorded_at).getTime() - new Date(a.recorded_at).getTime());
  };

  // Define which fields to show as columns
  const vitalSignColumns = [
    { key: 'temperature', label: 'Temp', unit: '°C' },
    { key: 'heart_rate', label: 'HR', unit: 'bpm' },
    { key: 'systolic_bp', label: 'SBP', unit: 'mmHg' },
    { key: 'diastolic_bp', label: 'DBP', unit: 'mmHg' },
    { key: 'respiratory_rate', label: 'RR', unit: 'br/min' },
    { key: 'oxygen_saturation', label: 'SpO2', unit: '%' },
    { key: 'blood_sugar', label: 'Blood Glucose', unit: 'mg/dL' },
    { key: 'height', label: 'Height', unit: 'cm' },
    { key: 'weight', label: 'Weight', unit: 'kg' },
  ];

  // Helper to format date and time
  const formatDateTime = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-GB', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const renderRecentHistory = () => {
    const recentRecords = getFilteredRecords();
    
    return (
      <div className="recent-history-table-wrapper">
        <h3>Recent History (Last 48 Hours)</h3>
        <div className="vital-signs-filter-bar">
          <label>
            Severity:
            <select value={severity} onChange={e => setSeverity(e.target.value)}>
              <option value="all">All</option>
              <option value="critical">Critical</option>
              <option value="high">High</option>
              <option value="low">Low</option>
              <option value="normal">Normal</option>
            </select>
          </label>
          <label>
            <input type="checkbox" checked={showToday} onChange={e => setShowToday(e.target.checked)} />
            Today's results
          </label>
          <label>
            Last
            <input
              type="number"
              min="1"
              style={{ width: 60, margin: '0 4px' }}
              value={lastHours}
              onChange={e => setLastHours(e.target.value)}
              placeholder="hours"
            />
            hours
          </label>
          <label>
            From:
            <input type="date" value={fromDate} onChange={e => setFromDate(e.target.value)} />
          </label>
          <label>
            To:
            <input type="date" value={toDate} onChange={e => setToDate(e.target.value)} />
          </label>
          <button 
            className="btn btn-small btn-secondary"
            onClick={() => {
              setSeverity('all');
              setShowToday(false);
              setLastHours('');
              setFromDate('');
              setToDate('');
            }}
            style={{ marginLeft: '10px' }}
          >
            Clear Filters
          </button>

        </div>
        {recentRecords.length === 0 ? (
          <div className="no-data">
            <p>No vital signs match the selected filters.</p>
          </div>
        ) : (
          <table className="vital-signs-history-table">
            <thead>
              <tr>
                <th>Vital Sign</th>
                {recentRecords.map(record => (
                  <th key={record.uuid}>{formatDateTime(record.recorded_at)}</th>
                ))}
              </tr>
            </thead>
            <tbody>
              {vitalSignColumns.map(col => (
                <tr key={col.key}>
                  <td>{col.label} ({col.unit})</td>
                  {recentRecords.map(record => {
                    const value = record[col.key as keyof typeof record];
                    return (
                      <td key={record.uuid}>
                        {value !== undefined && value !== null ? `${value} ${col.unit}` : <span style={{ color: '#aaa' }}>—</span>}
                      </td>
                    );
                  })}
                </tr>
              ))}
            </tbody>
          </table>
        )}
      </div>
    );
  };

  if (isLoading) {
    return (
      <div className="vital-signs-container">
        <div className="loading">
          <div className="loading-spinner"></div>
          <p>Loading vital signs...</p>
        </div>
      </div>
    );
  }

  if (isError) {
    return (
      <div className="vital-signs-container">
        <div className="error-message">
          <p>Failed to load vital signs data. Please try again.</p>
          <button className="btn btn-primary" onClick={() => refetch()}>
            Try Again
          </button>
        </div>
      </div>
    );
  }



  // Create fallback chart data from vital signs if chart data is not available
  const fallbackChartData = vitalSigns.length > 0 && !chartData?.data ? vitalSigns.map(vitalSign => ({
    recorded_at: vitalSign.recorded_at,
    temperature: vitalSign.temperature,
    heart_rate: vitalSign.heart_rate,
    systolic_bp: vitalSign.systolic_bp,
    diastolic_bp: vitalSign.diastolic_bp,
    respiratory_rate: vitalSign.respiratory_rate,
    oxygen_saturation: vitalSign.oxygen_saturation,
    blood_sugar: vitalSign.blood_sugar,
    height: vitalSign.height,
    weight: vitalSign.weight,
    news_score: vitalSign.news_score,
    temperature_color: vitalSign.temperature ? (vitalSign.temperature >= 36.1 && vitalSign.temperature <= 38.0 ? 'green' : vitalSign.temperature > 38.0 ? 'red' : 'blue') : 'gray',
    heart_rate_color: vitalSign.heart_rate ? (vitalSign.heart_rate >= 51 && vitalSign.heart_rate <= 90 ? 'green' : vitalSign.heart_rate > 90 ? 'red' : 'blue') : 'gray',
    blood_pressure_color: vitalSign.systolic_bp ? (vitalSign.systolic_bp >= 90 && vitalSign.systolic_bp <= 140 ? 'green' : vitalSign.systolic_bp > 140 ? 'red' : 'blue') : 'gray',
    respiratory_rate_color: vitalSign.respiratory_rate ? (vitalSign.respiratory_rate >= 12 && vitalSign.respiratory_rate <= 20 ? 'green' : vitalSign.respiratory_rate > 20 ? 'red' : 'blue') : 'gray',
    oxygen_saturation_color: vitalSign.oxygen_saturation ? (vitalSign.oxygen_saturation >= 97 ? 'green' : vitalSign.oxygen_saturation < 95 ? 'red' : 'orange') : 'gray',
    blood_sugar_color: 'gray',
    height_color: 'gray',
    weight_color: 'gray',
    news_score_color: vitalSign.news_score <= 4 ? 'green' : vitalSign.news_score <= 6 ? 'orange' : 'red'
  })) : null;

  const chartDataToUse = chartData?.data || fallbackChartData;

  return (
    <div className="vital-signs-container">
      <div className="vital-signs-header">
        <h1>Vital Signs</h1>
        <p>Monitor and record patient vital signs</p>
        {summary && (
          <div className="summary-info">
            <span>Total Records: {summary.total_records}</span>
            <span>Last Updated: {formatDate(summary.latest_recorded_at)}</span>
          </div>
        )}
      </div>
      
      <div className="vital-signs-content">
        {renderLatestVitalSigns()}
        
        {/* Chart Section */}
        {chartDataToUse && chartDataToUse.length > 0 ? (
          <div className="chart-section">
            <VitalSignsChart 
              data={chartDataToUse} 
              isLoading={isLoading}
            />
          </div>
        ) : (
          <div className="chart-section">
            <div className="chart-no-data">
              <h3>Vital Signs Trends</h3>
              <p>Chart data is not available. This could be because:</p>
              <ul>
                <li>No vital signs have been recorded yet</li>
                <li>Chart data is still loading</li>
                <li>There was an issue fetching chart data</li>
              </ul>
              <button 
                className="btn btn-secondary" 
                onClick={() => refetch()}
              >
                Refresh Data
              </button>
            </div>
          </div>
        )}
        
        {renderRecentHistory()}
        
        {/* All Records Section */}
        <div className="all-records-section">
          <div className="section-header">
            <h3>All Vital Signs Records</h3>
            <button 
              className="btn btn-secondary"
              onClick={() => setShowAllRecords(!showAllRecords)}
            >
              <TrendingUp size={20} />
              {showAllRecords ? 'Hide' : 'Show'} All Records
            </button>
          </div>
          
          {showAllRecords && (
            <div className="all-records-list">
              {vitalSigns.length === 0 ? (
                <div className="no-data">
                  <p>No vital signs records found.</p>
                </div>
              ) : (
                <div className="records-table-wrapper">
                  <table className="records-table">
                    <thead>
                      <tr>
                        <th>Date & Time</th>
                        <th>Temperature</th>
                        <th>Heart Rate</th>
                        <th>Blood Pressure</th>
                        <th>Respiratory Rate</th>
                        <th>Oxygen Saturation</th>
                        <th>Blood Sugar</th>
                        <th>Height</th>
                        <th>Weight</th>
                        <th>NEWS Score</th>
                        <th>Actions</th>
                      </tr>
                    </thead>
                    <tbody>
                      {vitalSigns.map((record) => (
                        <tr key={record.uuid}>
                          <td>{formatDate(record.recorded_at)}</td>
                          <td>
                            {record.temperature !== undefined && record.temperature !== null 
                              ? `${record.temperature} ${record.temp_unit || '°C'}` 
                              : '—'}
                          </td>
                          <td>
                            {record.heart_rate !== undefined && record.heart_rate !== null 
                              ? `${record.heart_rate} bpm` 
                              : '—'}
                          </td>
                          <td>
                            {record.systolic_bp !== undefined && record.systolic_bp !== null 
                              ? `${record.systolic_bp}/${record.diastolic_bp || '—'} mmHg` 
                              : '—'}
                          </td>
                          <td>
                            {record.respiratory_rate !== undefined && record.respiratory_rate !== null 
                              ? `${record.respiratory_rate} br/min` 
                              : '—'}
                          </td>
                          <td>
                            {record.oxygen_saturation !== undefined && record.oxygen_saturation !== null 
                              ? `${record.oxygen_saturation}%` 
                              : '—'}
                          </td>
                          <td>
                            {record.blood_sugar !== undefined && record.blood_sugar !== null 
                              ? `${record.blood_sugar} ${record.blood_sugar_unit || 'mg/dL'}` 
                              : '—'}
                          </td>
                          <td>
                            {record.height !== undefined && record.height !== null 
                              ? `${record.height} ${record.height_unit || 'cm'}` 
                              : '—'}
                          </td>
                          <td>
                            {record.weight !== undefined && record.weight !== null 
                              ? `${record.weight} ${record.weight_unit || 'kg'}` 
                              : '—'}
                          </td>
                          <td>
                            {record.news_score !== undefined && record.news_score !== null 
                              ? record.news_score 
                              : '—'}
                          </td>
                          <td>
                            <button 
                              className="btn btn-small btn-secondary"
                              onClick={() => {
                                setSelectedVitalSign(record);
                                setIsUpdateModalOpen(true);
                              }}
                              title="Edit this record"
                            >
                              <Edit3 size={16} />
                              Edit
                            </button>
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              )}
            </div>
          )}
        </div>
        
        <div className="vital-signs-actions">
          <button 
            className="btn btn-primary" 
            onClick={() => setIsModalOpen(true)}
          >
            <Plus size={20} />
            Add New Vital Signs
          </button>
          <button 
            className="btn btn-secondary"
            onClick={() => setShowDetailedHistory(!showDetailedHistory)}
          >
            <Calendar size={20} />
            {showDetailedHistory ? 'Hide' : 'View'} Full History
          </button>
        </div>
      </div>

      <AddVitalSignModal
        isOpen={isModalOpen}
        onClose={() => setIsModalOpen(false)}
        onSubmit={handleAddVitalSign}
        patientId={selectedPatient?.uuid || ''}
        loading={createVitalSignMutation.isPending}
      />

      <UpdateVitalSignModal
        isOpen={isUpdateModalOpen}
        onClose={() => {
          setIsUpdateModalOpen(false);
          setSelectedVitalSign(null);
        }}
        onSubmit={handleUpdateVitalSign}
        vitalSign={selectedVitalSign}
        loading={updateVitalSignMutation.isPending}
      />

      <VitalSignsHistory
        patientId={selectedPatient?.uuid || ''}
        vitalSigns={vitalSigns}
        isVisible={showDetailedHistory}
        onClose={() => setShowDetailedHistory(false)}
      />

      {selectedField && (
        <IndividualVitalSignHistory
          patientId={selectedPatient?.uuid || ''}
          fieldName={selectedField.name}
          fieldDisplayName={selectedField.displayName}
          isVisible={showIndividualHistory}
          onClose={() => {
            setShowIndividualHistory(false);
            setSelectedField(null);
          }}
        />
      )}
    </div>
  );
};

export default PatientVitalSigns;

import { X } from "lucide-react";
import "./EditPatientVisitModal.css"; // Reusing the same CSS
import { useRegistrationStatusLogsQuery } from "@/hooks/patient.query";
import { format, parseISO } from "date-fns";
import NurtifyAccordion from "@/components/NurtifyAccordion";

interface VisitDetailsModalProps {
  isOpen: boolean;
  onClose: () => void;
  visitData: {
    patientName: string;
    room: string;
    registrationStatus: string;
    staffMember: string;
    leading_team: string;
    activities: string[];
    comments: string;
    time: string;
  };
  visitUuid: string;
}

const VisitDetailsModal: React.FC<VisitDetailsModalProps> = ({
  isOpen,
  onClose,
  visitData,
  visitUuid,
}) => {
  // Format registration status for display
  const formatRegistrationStatus = (status: string) => {
    // Status values are already in the correct format from backend
    return status;
  };

  // Format date for display
  const formatDate = (dateString: string) => {
    try {
      const date = parseISO(dateString);
      return format(date, "dd MMM yyyy 'at' HH:mm");
    } catch (error) {
      console.error("Error formatting date:", error);
      return dateString;
    }
  };

  // Fetch registration status logs
  const {
    data: registrationLogs,
    isLoading: isLogsLoading,
    error: logsError
  } = useRegistrationStatusLogsQuery(visitUuid);

  if (!isOpen) return null;

  return (
    <div className="edit-patient-visit-modal-overlay">
      <div className="edit-patient-visit-modal">
        <div className="edit-patient-visit-modal-header">
          <h2 className="edit-patient-visit-modal-title">
            Visit Details
          </h2>
          <button
            type="button"
            className="edit-patient-visit-modal-close"
            onClick={onClose}
            aria-label="Close"
          >
            <X size={20} />
          </button>
        </div>

        <div className="edit-patient-visit-modal-body">
          <div className="visit-details-container">
            <div className="visit-detail-item">
              <strong>Patient Name:</strong>
              <span>{visitData.patientName}</span>
            </div>
            <div className="visit-detail-item">
              <strong>Visit start time:</strong>
              <span>{visitData.time || "Not specified"}</span>
            </div>
            <div className="visit-detail-item">
              <strong>Leading Team:</strong>
              <span>{visitData.leading_team}</span>
            </div>
            <div className="visit-detail-item">
              <strong>Staff Member:</strong>
              <span>{visitData.staffMember}</span>
            </div>
            <div className="visit-detail-item">
              <strong>Activities:</strong>
              <span>{visitData.activities.join(", ")}</span>
            </div>
            <div className="visit-detail-item">
              <strong>Comments:</strong>
              <span>{visitData.comments}</span>
            </div>
          </div>

          {/* Registration Status Logs Section */}
          <div className="registration-logs-section" style={{ marginTop: "20px" }}>
            <h3 style={{ fontSize: "18px", marginBottom: "15px" }}>Registration Status Logs</h3>

            {isLogsLoading && (
              <div className="loading-logs" style={{ textAlign: "center", padding: "15px" }}>
                Loading registration status logs...
              </div>
            )}

            {logsError && (
              <div className="error-logs" style={{ color: "red", padding: "15px" }}>
                Error loading registration status logs. Please try again later.
              </div>
            )}

            {!isLogsLoading && !logsError && registrationLogs && registrationLogs.length === 0 && (
              <div className="empty-logs" style={{ padding: "15px", backgroundColor: "#f8f9fa", borderRadius: "5px" }}>
                No registration status changes recorded for this visit.
              </div>
            )}

            {!isLogsLoading && !logsError && registrationLogs && registrationLogs.length > 0 && (
              <div className="logs-list">
                <NurtifyAccordion
                  items={
                    [...registrationLogs]
                      .sort((a, b) => new Date(b.updated_at).getTime() - new Date(a.updated_at).getTime())
                      .map((log, index) => ({
                        id: `log-${index}`,
                        question: `${formatRegistrationStatus(log.new_status)} - ${formatDate(log.updated_at)}`,
                        answer: `**Updated At:** ${formatDate(log.updated_at)}\n**New Status:** ${formatRegistrationStatus(log.new_status)}\n**Updated By:** ${log.updated_by_details.first_name} ${log.updated_by_details.last_name}`
                      }))
                  }
                />
              </div>
            )}
          </div>
        </div>

        <div className="edit-patient-visit-modal-footer">
          <button
            type="button"
            className="cancel-btn"
            onClick={onClose}
          >
            <X size={16} /> Close
          </button>
        </div>
      </div>
    </div>
  );
};

export default VisitDetailsModal;

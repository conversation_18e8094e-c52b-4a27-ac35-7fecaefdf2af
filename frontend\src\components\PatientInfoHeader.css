.patient-info-header {
  display: flex;
  align-items: center;
  gap: 32px;
  background: #fff;
  border-radius: 10px;
  box-shadow: 0 8px 32px rgba(55,183,195,0.08);
  border: 1.5px solid #e3e9f6;
  border-top: 3px solid #37b7c3;
  position: relative;
  padding: 20px 32px 20px 20px;
  margin-bottom: 32px;
  margin-top: 0;
  min-height: 72px;
  z-index: 1100;
  overflow-x: auto;
  transition: box-shadow 0.2s, border-color 0.18s;
}

.patient-avatar {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 50px; height: 50px;
  min-width: 50px; min-height: 50px;
  border-radius: 50%;
  background: linear-gradient(135deg, #e9f7fa 0%, #ecf1fd 100%);
  border: 2px solid #e3e9f6;
  font-size: 20px;
  color: #418bbd;
  font-weight: 700;
  margin-right: 24px;
}

.patient-info-fields {
  display: flex;
  gap: 28px;
  flex-wrap: wrap;
}

.patient-info-item {
  display: flex;
  flex-direction: column;
  justify-content: center;
  min-width: 140px;
  margin-right: 8px;
}

.label {
  font-size: 13px;
  color: #373068;
  font-weight: 700;
  letter-spacing: 0.6px;
  text-transform: uppercase;
  margin-bottom: 5px;
  margin-left: 2px;
}

.value {
  font-size: 18px;
  font-weight: 600;
  color: #193d6a;
  padding: 11px 18px;
  background: #f7fafc;
  border-radius: 8px;
  border-bottom: 2.5px solid #37b7c3;
  min-height: 32px;
  box-shadow: 0 1px 2.5px rgba(55,183,195, 0.05);
  display: flex;
  align-items: center;
}

.value:empty::after {
  content: "N/A";
  color: #babfdc;
}

@media (max-width: 900px) {
  .patient-info-header {
    flex-direction: column;
    align-items: flex-start;
    padding: 12px 5vw 14px 14px;
    gap: 14px;
    min-height: 48px;
  }
  .patient-info-fields {
    gap: 14px;
    flex-wrap: wrap;
  }
  .patient-avatar {
    width: 36px; height: 36px;
    font-size: 14px;
    margin-bottom: 6px;
    margin-right: 7px;
  }
  .patient-info-item {
    margin-right: 5px; min-width: 82px;
  }
  .value {
    font-size: 15px; padding: 7px 9px; min-height: 24px;
  }
}

.sponsor-policy-details-container {
    min-height: 100vh;
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
    padding: 0;
}

/* Header Section */
.policy-header {
    background: white;
    border-bottom: 1px solid #e2e8f0;
    padding: 24px 32px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.back-button {
    display: inline-flex;
    align-items: center;
    gap: 6px;
    background: transparent;
    border: none;
    color: #6b7280;
    font-size: 14px;
    font-weight: 400;
    cursor: pointer;
    padding: 0;
    margin-bottom: 24px;
    text-decoration: none;
    transition: color 0.2s ease;
    position: relative;
}

.back-button::before {
    content: '';
    position: absolute;
    bottom: -2px;
    left: 0;
    width: 0;
    height: 1px;
    background-color: #6b7280;
    transition: width 0.3s ease;
}

.back-button:hover {
    color: #374151;
}

.back-button:hover::before {
    width: 100%;
}

.back-button svg {
    transition: transform 0.2s ease;
}

.back-button:hover svg {
    transform: translateX(-2px);
}

.policy-header-info {
    max-width: 1200px;
    margin: 0 auto;
}

.policy-category {
    display: inline-flex;
    align-items: center;
    gap: 6px;
    background: #dbeafe;
    color: #1e40af;
    padding: 4px 12px;
    border-radius: 20px;
    font-size: 12px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    margin-bottom: 12px;
}

.policy-title {
    font-size: 32px;
    font-weight: 700;
    color: #1e293b;
    margin: 0 0 20px 0;
    line-height: 1.2;
}

.policy-meta {
    display: flex;
    flex-wrap: wrap;
    gap: 24px;
    align-items: center;
}

.meta-item {
    display: flex;
    align-items: center;
    gap: 8px;
    color: #64748b;
    font-size: 14px;
    font-weight: 500;
}

.meta-item svg {
    color: #94a3b8;
}

.status-meta {
    background: #f8fafc;
    padding: 6px 12px;
    border-radius: 20px;
    border: 1px solid #e2e8f0;
}

.status-icon {
    margin-right: 4px;
}

.status-active {
    color: #059669;
}

.status-pending {
    color: #d97706;
}

.status-default {
    color: #6b7280;
}

/* Content Section */
.policy-content {
    max-width: 1200px;
    margin: 0 auto;
    padding: 32px;
}

.policy-description {
    background: white;
    border-radius: 12px;
    padding: 24px;
    margin-bottom: 24px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    border: 1px solid #e2e8f0;
}

.policy-description h3 {
    font-size: 18px;
    font-weight: 600;
    color: #1e293b;
    margin: 0 0 12px 0;
    display: flex;
    align-items: center;
    gap: 8px;
}

.policy-description p {
    color: #475569;
    font-size: 15px;
    line-height: 1.6;
    margin: 0;
}

/* Document Section */
.document-section {
    background: white;
    border-radius: 12px;
    padding: 24px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    border: 1px solid #e2e8f0;
}

.document-section h3 {
    font-size: 18px;
    font-weight: 600;
    color: #1e293b;
    margin: 0 0 20px 0;
    display: flex;
    align-items: center;
    gap: 8px;
}

.document-viewer {
    border-radius: 8px;
    overflow: hidden;
    background: #f8fafc;
    min-height: 600px;
    border: 1px solid #e2e8f0;
}

/* Image Viewer */
.image-viewer {
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 20px;
    background: #f8fafc;
}

.policy-image {
    max-width: 100%;
    max-height: 80vh;
    border-radius: 8px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

/* No Document / Unsupported Format */
.no-document,
.unsupported-format {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 60px 40px;
    text-align: center;
    color: #64748b;
    background: #f8fafc;
    min-height: 400px;
}

.no-document svg,
.unsupported-format svg {
    color: #cbd5e1;
    margin-bottom: 16px;
}

.no-document h4,
.unsupported-format h4 {
    font-size: 18px;
    font-weight: 600;
    color: #475569;
    margin: 0 0 8px 0;
}

.no-document p,
.unsupported-format p {
    font-size: 14px;
    color: #64748b;
    margin: 0 0 20px 0;
    max-width: 300px;
}

.download-link {
    display: inline-flex;
    align-items: center;
    gap: 8px;
    background: #3b82f6;
    color: white;
    padding: 10px 20px;
    border-radius: 6px;
    text-decoration: none;
    font-weight: 500;
    font-size: 14px;
    transition: background-color 0.2s ease;
}

.download-link:hover {
    background: #2563eb;
}

/* Responsive Design */
@media (max-width: 768px) {
    .policy-header {
        padding: 20px;
    }

    .policy-title {
        font-size: 24px;
    }

    .policy-meta {
        flex-direction: column;
        align-items: flex-start;
        gap: 12px;
    }

    .policy-content {
        padding: 20px;
    }

    .policy-description,
    .document-section {
        padding: 20px;
    }

    .document-viewer {
        min-height: 400px;
    }

    .no-document,
    .unsupported-format {
        padding: 40px 20px;
        min-height: 300px;
    }
}

@media (max-width: 480px) {
    .policy-header {
        padding: 16px;
    }

    .policy-title {
        font-size: 20px;
    }

    .policy-content {
        padding: 16px;
    }

    .policy-description,
    .document-section {
        padding: 16px;
    }

    .back-button {
        font-size: 13px;
        padding: 6px 10px;
    }

    .meta-item {
        font-size: 13px;
    }
}

/* PDF Viewer Enhancements */
.document-viewer .pdf-viewer {
    border: none;
    border-radius: 8px;
    overflow: hidden;
}

/* Loading and Error States */
.policy-loading {
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 400px;
}

.policy-error {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    min-height: 400px;
    color: #dc2626;
    text-align: center;
    padding: 40px;
}

.policy-error h3 {
    font-size: 18px;
    font-weight: 600;
    margin: 0 0 8px 0;
}

.policy-error p {
    font-size: 14px;
    margin: 0;
    color: #7f1d1d;
}

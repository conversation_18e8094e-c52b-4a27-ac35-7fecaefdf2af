
import { ChangeEvent, useState, useEffect } from "react";
import NurtifyCheckBox from "@/components/NurtifyCheckBox";
import NurtifyComboBox from "@/components/NurtifyComboBox";
import NurtifyInput from "@/components/NurtifyInput";
import NurtifyRadio from "@/components/NurtifyRadio";
import NurtifyRange from "@/components/NurtifyRange";
import NurtifySelect from "@/components/NurtifySelect";
import NurtifySwitch from "@/components/NurtifySwitch";
import NurtifyTextArea from "@/components/NurtifyTextArea";
import NurtifyAttachFileBox from "@/components/NurtifyAttachFileBox";
import NurtifySignBox from "@/components/NurtifySignBox";
import NurtifyDateInput from "@/components/NurtifyDateInput";
import NurtifyText from "@/components/NurtifyText";
import NurtifyToggle from "@/components/NurtifyToggle";
import NurtifyMultiInput from "@/components/NurtifyMultiInput";
import NurtifyAccordion from "@/components/NurtifyAccordion";
import NurtifyOtpInput from "@/components/NurtifyOtpInput";
import NurtifyTabs from "@/components/NurtifyTabs";
import NurtifyButton from "@/components/NurtifyButton";
import DataTable, { Column, Action } from "@/components/common/DataTable"; // Import DataTable and its types
import NurtifyModal from "@/components/NurtifyModal";
import NurtifyCarousel from "@/components/NurtifyCarousel";
import { Search, Edit, Trash } from "lucide-react";
import { toast } from "sonner";
import FilterBar from "@/components/FilterBar";
import NurtifyFilter, { NurtifyFilterItem } from "@/components/NurtifyFilter";

interface ElementsProps {
  activeCategory: string;
  searchQuery: string;
}

interface ComponentData {
  id: string;
  name: string;
  category: string;
  description: string;
  component: React.ReactNode;
  keywords?: string[]; // Optional keywords for better search
}

const Elements: React.FC<ElementsProps> = ({ activeCategory, searchQuery }) => {
  // State management
  const [selectedValue, setSelectedValue] = useState<string>("");
  const [selectedInterests, setSelectedInterests] = useState<string[]>([]);
  const [, setNotificationsEnabled] = useState<boolean>(false);
  const [nameEmail, setNameEmail] = useState<string[]>(["", ""]);
  const [rangeValue, setRangeValue] = useState<number>(50);
  const [sdkOtp, setSdkOtp] = useState<string[]>(Array(4).fill(""));
  const [sdkActiveTab, setSdkActiveTab] = useState<string>('tab1');
  const [isSdkModalOpen, setIsSdkModalOpen] = useState<boolean>(false); // State for SDK Modal example
  const [filteredComponents, setFilteredComponents] = useState<ComponentData[]>([]);

  // Sample data for DataTable (matching DataTable.tsx props)
  type SdkUser = { id: number; name: string; role: string; status: string; uuid: string };
  const sdkDataTableColumns: Column<SdkUser>[] = [
    { key: 'id', header: 'ID', sortable: true },
    { key: 'name', header: 'Name', sortable: true },
  ];
  const sdkDataTableData: SdkUser[] = [
    { id: 1, name: 'Jane Cooper', role: 'Admin', status: 'Active', uuid: 'uuid-1' },
    { id: 2, name: 'Cody Fisher', role: 'User', status: 'Inactive', uuid: 'uuid-2' },
    { id: 3, name: 'Esther Howard', role: 'User', status: 'Active', uuid: 'uuid-3' },
    { id: 4, name: 'Kristin Watson', role: 'Moderator', status: 'Active', uuid: 'uuid-4' },
    { id: 5, name: 'Guy Hawkins', role: 'User', status: 'Active', uuid: 'uuid-5' },
    { id: 6, name: 'Robert Fox', role: 'Admin', status: 'Inactive', uuid: 'uuid-6' },
  ];
  // Sample actions for DataTable
  const sdkDataTableActions: Action<SdkUser>[] = [
    {
      icon: <Edit size={16} />,
      onClick: (row) => toast(`Editing user: ${row.name}`),
      tooltipText: "Edit User",
    },
    {
      icon: <Trash size={16} />,
      onClick: (row) => toast.error(`Deleting user: ${row.name}`),
      tooltipText: "Delete User",
    },
  ];

  //filters
  const [selectedSources, setSelectedSources] = useState<string[]>(["cb1"]);
  const [selectedRadio, setSelectedRadio] = useState<string>("rb1");
  const [minimized, setMinimized] = useState(false);

  const filters: NurtifyFilterItem[] = [
    {
      label: "Source",
      type: "checkbox",
      options: [
        { label: "Check Box 1", value: "cb1" },
        { label: "Check Box 2", value: "cb2" },
        { label: "Check Box 3", value: "cb3" },
      ],
      value: selectedSources,
      onChange: (v) => setSelectedSources(v as string[]),
    },
    {
      label: "Type",
      type: "radio",
      options: [
        { label: "Radio Button 1", value: "rb1" },
        { label: "Radio Button 2", value: "rb2" },
        { label: "Radio Button 3", value: "rb3" },
      ],
      value: selectedRadio,
      onChange: (v) => setSelectedRadio(v as string),
    },
  ];


  // Sample data for Accordion
  const accordionItems = [
    { id: 'sdk-acc-1', question: 'What is the first item?', answer: 'This is the detailed answer for the first accordion item. It can contain multiple lines.' },
    { id: 'sdk-acc-2', question: 'How does the second item work?', answer: 'The second item expands to show this content when clicked. \nIt supports line breaks.' },
    { id: 'sdk-acc-3', question: 'Is there a third item?', answer: 'Yes, this is the content for the third and final item in this example.' },
  ];

  // Event handlers
  const handleToggle = (e: React.ChangeEvent<HTMLInputElement>) => {
    setNotificationsEnabled(e.target.checked);
  };

  const handleSelectChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    setSelectedValue(e.target.value);
  };

  const handleInterestsChange = (newValues: string[]) => {
    setSelectedInterests(newValues);
  };

  const handleCheckboxChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    console.log(e.target.checked);
  };

  const handleRadioChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSelectedValue(e.target.value);
  };

  const handleMultiInputChange = (
    index: number,
    e: React.ChangeEvent<HTMLInputElement>
  ) => {
    const newValues = [...nameEmail];
    newValues[index] = e.target.value;
    setNameEmail(newValues);
  };

  // Handle range slider change correctly
  const handleRangeChange = (e: ChangeEvent<HTMLInputElement>) => {
    setRangeValue(Number(e.target.value));
  };

  // Options data
  const options = [
    { value: "option1", label: "Option 1" },
    { value: "option2", label: "Option 2" },
    { value: "option3", label: "Option 3" },
  ];

  const interestsOptions = [
    { label: "Technology", value: "technology" },
    { label: "Health", value: "health" },
    { label: "Finance", value: "finance" },
    { label: "Education", value: "education" },
    { label: "Entertainment", value: "entertainment" },
    { label: "Sports", value: "sports" },
    { label: "Travel", value: "travel" },
    { label: "Art", value: "art" },
    { label: "Science", value: "science" },
  ];

  // Component data
  const components: ComponentData[] = [
    {
      id: "text-input",
      name: "Text Input",
      category: "inputs",
      description: "Standard text input field for collecting single-line text data.",
      component: (
        <div className="component-demo">
          <NurtifyText label="Text Input" />
          <NurtifyInput type="text" placeholder="Enter text here" />
        </div>
      ),
    },
    {
      id: "text-input-with-icon",
      name: "Text Input with Icon",
      category: "inputs",
      description: "Text input field with an optional icon on the left.",
      component: (
        <div className="component-demo">
          <NurtifyText label="Search Input" />
          <NurtifyInput
            type="text"
            placeholder="Search..."
            leftIcon={<Search size={18} className="text-gray-400" />} // Add Search icon
          />
        </div>
      ),
    },
    {
      id: "date-input",
      name: "Date Input",
      category: "inputs",
      description: "Date picker component for selecting dates.",
      component: (
        <div className="component-demo">
          <NurtifyText label="Date Input" />
          <NurtifyDateInput />
        </div>
      ),
    },
    {
      id: "textarea",
      name: "Text Area",
      category: "inputs",
      description: "Multi-line text input for longer form responses.",
      component: (
        <div className="component-demo">
          <NurtifyText label="Text Area" />
          <NurtifyTextArea placeholder="Enter longer text here..." />
        </div>
      ),
    },
    {
      id: "otp-input",
      name: "OTP Input",
      category: "inputs",
      description: "Input field specifically designed for entering One-Time Passwords.",
      keywords: ["otp", "verification", "code", "pin"],
      component: (
        <div className="component-demo">
          <NurtifyText label="Enter Verification Code" />
          <NurtifyOtpInput
            length={4}
            value={sdkOtp}
            onChange={setSdkOtp}
          />
        </div>
      ),
    },
    {
      id: "multi-input",
      name: "Multi Input",
      category: "inputs",
      description: "Multiple input fields grouped together for related information.",
      component: (
        <div className="component-demo">
          <NurtifyText label="Multi Input Example" />
          <NurtifyMultiInput
            inputs={[
              { name: "firstName", label: "Full Name" },
              { name: "lastName", label: "Email Address" },
              { name: "phone", label: "Phone Number" },
            ]}
            values={nameEmail}
            onChange={handleMultiInputChange}
          />
        </div>
      ),
    },
    {
      id: "checkbox",
      name: "Checkbox",
      category: "selection",
      description: "Checkbox component for binary selections.",
      component: (
        <div className="component-demo">
          <NurtifyText label="Checkbox Option" />
          <NurtifyCheckBox
            label="I agree to the terms and conditions"
            value="checkbox-1"
            onChange={(event: any) =>
              handleCheckboxChange(
                event as React.ChangeEvent<HTMLInputElement>
              )
            }
          />
        </div>
      ),
    },
    {
      id: "radio",
      name: "Radio Button",
      category: "selection",
      description: "Radio button component for selecting one option from a list.",
      component: (
        <div className="component-demo">
          <NurtifyText label="Radio Selection" />
          <NurtifyRadio
            label="Basic plan $10/month"
            name="plan"
            value="radio-1"
            onChange={(event: any) =>
              handleRadioChange(
                event as React.ChangeEvent<HTMLInputElement>
              )
            }
          />
        </div>
      ),
    },
    {
      id: "select",
      name: "Select Dropdown",
      category: "selection",
      description: "Dropdown menu for selecting from a list of options.",
      component: (
        <div className="component-demo">
          <NurtifyText label="Select Dropdown" />
          <NurtifySelect
            name="exampleSelect"
            value={selectedValue}
            onChange={handleSelectChange}
            options={options}
          />
        </div>
      ),
    },
    {
      id: "combobox",
      name: "Combo Box",
      category: "selection",
      description: "Advanced selection component with search and multi-select capabilities.",
      component: (
        <div className="component-demo">
          <NurtifyText label="Combo Box" />
          <NurtifyComboBox
            options={interestsOptions}
            selectedValues={selectedInterests}
            onChange={handleInterestsChange}
          />
        </div>
      ),
    },
    {
      id: "switch",
      name: "Switch",
      category: "interactive",
      description: "Toggle switch for enabling or disabling features.",
      component: (
        <div className="component-demo">
          <NurtifyText label="Switch" />
          <NurtifySwitch
            name="notifications"
            value="notifications"
            onChange={(event: any) => handleToggle(event as React.ChangeEvent<HTMLInputElement>)}
          />
        </div>
      ),
    },
    {
      id: "toggle",
      name: "Toggle",
      category: "interactive",
      description: "Alternative toggle component with different visual style.",
      component: (
        <div className="component-demo">
          <NurtifyText label="Toggle" />
          <NurtifyToggle
            name="notifications"
            value="notifications"
            onChange={(event: any) => handleToggle(event as React.ChangeEvent<HTMLInputElement>)}
          />
        </div>
      ),
    },
    {
      id: "range",
      name: "Range Slider",
      category: "interactive",
      description: "Slider component for selecting a value from a range.",
      component: (
        <div className="component-demo">
          <NurtifyText label="Range Slider" />
          <NurtifyRange
            min={0}
            max={100}
            value={rangeValue}
            onChange={handleRangeChange} // Use the dedicated handler instead of inline function
          />
          <div className="mt-2">Selected value: {rangeValue}</div>
        </div>
      ),
    },
    {
      id: "file-attachment",
      name: "File Attachment",
      category: "file",
      description: "Component for uploading and attaching files.",
      component: (
        <div className="component-demo">
          <NurtifyText label="File Attachment" />
          <NurtifyAttachFileBox />
        </div>
      ),
    },
    {
      id: "signature",
      name: "Signature Box",
      category: "file",
      description: "Component for capturing digital signatures.",
      component: (
        <div className="component-demo">
          <NurtifyText label="Signature Box" />
          <NurtifySignBox />
        </div>
      ),
    },
    {
      id: "accordion",
      name: "Accordion",
      category: "interactive",
      description: "Collapsible content sections for displaying information concisely.",
      component: (
        <div className="component-demo">
          <NurtifyText label="Accordion Example" />
          <NurtifyAccordion items={accordionItems} />
        </div>
      ),
    },
    {
      id: "data-table",
      name: "Data Table",
      category: "interactive", // Or 'data display'?
      description: "Component for displaying tabular data with sorting, pagination, and actions.",
      keywords: ["table", "grid", "data", "rows", "columns", "sort", "pagination"],
      component: (
        <div className="component-demo">
          <NurtifyText label="Example Data Table" />
          {/* Use the actual DataTable component */}
          <DataTable<SdkUser>
            columns={sdkDataTableColumns}
            data={sdkDataTableData}
            actions={sdkDataTableActions}
            defaultItemsPerPage={5} // Example: Show 5 items per page
          />
        </div>
      ),
    },
    {
      id: "modal-dialog",
      name: "Modal / Dialog",
      category: "interactive",
      description: "Popup window for displaying information or forms.",
      keywords: ["modal", "dialog", "popup", "overlay"],
      component: (
        <div className="component-demo">
          <NurtifyText label="Modal Example" />
          <button
            className="px-3 py-1.5 text-sm text-white rounded btn btn-nurtify"
            onClick={() => setIsSdkModalOpen(true)}
          >
            Open Modal
          </button>
          <NurtifyModal
            isOpen={isSdkModalOpen}
            onClose={() => setIsSdkModalOpen(false)}
            title="Example Modal Title"
            size="md" // Example size
          >
            {/* Content inside the modal */}
            <p>This is the content area of the modal. You can put any React node here, like forms, text, or other components.</p>
            <div className="flex justify-end mt-4 space-x-2">
              <button
                className="px-3 py-1.5 text-sm border border-gray-300 rounded hover:bg-gray-100"
                onClick={() => setIsSdkModalOpen(false)}
              >
                Cancel
              </button>
              <button
                className="px-3 py-1.5 text-sm text-white rounded btn btn-nurtify"
                onClick={() => {
                  toast.success("Action Confirmed!");
                  setIsSdkModalOpen(false);
                }}
              >
                Confirm Action
              </button>
            </div>
          </NurtifyModal>
        </div>
      ),
    },
    {
      id: "carousel",
      name: "Carousel",
      category: "interactive",
      description: "Component for displaying items in a rotating slideshow.",
      keywords: ["carousel", "slider", "slideshow"],
      component: (
        <div className="component-demo">
          <NurtifyText label="Example Carousel" />
          <NurtifyCarousel
            items={[
              <div key="1" className="p-4 bg-blue-100 rounded">Slide 1 Content</div>,
              <div key="2" className="p-4 bg-green-100 rounded">Slide 2 Content</div>,
              <div key="3" className="p-4 bg-yellow-100 rounded">Slide 3 Content</div>,
            ]}
          />
        </div>
      ),
    },
    {
      id: "tabs",
      name: "Tabs",
      category: "interactive", // Or 'navigation'?
      description: "Component for switching between different content sections.",
      keywords: ["tabs", "navigation", "sections"],
      component: (
        <div className="component-demo">
          <NurtifyText label="Example Tabs" />
          <NurtifyTabs
            tabs={[
              { id: 'tab1', label: 'Tab One' },
              { id: 'tab2', label: 'Tab Two' },
              { id: 'tab3', label: 'Tab Three' },
            ]}
            activeTab={sdkActiveTab}
            onTabChange={setSdkActiveTab}
          />
          {/* Display content based on active tab */}
          <div className="mt-4 p-4 border rounded bg-gray-50">
            {sdkActiveTab === 'tab1' && <p>Content for Tab One</p>}
            {sdkActiveTab === 'tab2' && <p>Content for Tab Two</p>}
            {sdkActiveTab === 'tab3' && <p>Content for Tab Three</p>}
          </div>
        </div>
      ),
    },
    {
      id: "toast-notifications",
      name: "Toast Notifications",
      category: "interactive", // Or maybe a 'feedback' or 'notifications' category?
      description: "Display temporary messages using Sonner toasts.",
      keywords: ["sonner", "toast", "notification", "message", "alert"],
      component: (
        <div className="component-demo d-flex flex-column gap-2">
          {/* Apply exact Nurtify button style provided by user */}
          <button
            className="px-3 py-1.5 text-sm text-white rounded btn btn-nurtify"
            onClick={() => toast("Event has been created.")}
          >
            Default
          </button>
          <button
            className="px-3 py-1.5 text-sm text-white rounded btn btn-nurtify" // Use exact class
            onClick={() => toast.success("Success! Profile updated.")}
          >
            Success
          </button>
          <button
            className="px-3 py-1.5 text-sm text-white rounded btn btn-nurtify" // Use exact class
            onClick={() => toast.error("Error! Could not save changes.")}
          >
            Error
          </button>
          <button
            className="px-3 py-1.5 text-sm text-white rounded btn btn-nurtify" // Use exact class
            onClick={() => toast.warning("Warning! Please check your input.")}
          >
            Warning
          </button>
          <button
            className="px-3 py-1.5 text-sm text-white rounded btn btn-nurtify" // Use exact class
            onClick={() => toast.info("Info: System maintenance scheduled.")}
          >
            Info
          </button>
        </div>
      ),
    },
    {
      id: "buttons",
      name: "Buttons",
      category: "buttons",
      description: "Examples of common button styles used throughout the application.",
      keywords: ["button", "action", "submit", "cancel", "edit", "delete"],
      component: (
        <div className="component-demo">
          {/* Primary Buttons Section */}
          <div className="button-demo-section">
            <div className="button-demo-title">PRIMARY BUTTONS</div>
            <div className="button-demo-row">
              <div className="button-size-group">
                <div className="button-size-label">MEDIUM</div>
                <NurtifyButton variant="primary" size="medium" onClick={() => toast.info("Primary Medium Hover")}>
                  Button
                </NurtifyButton>
                <NurtifyButton variant="primary" size="medium" onClick={() => toast.info("Primary Medium Hover")}>
                  Hover
                </NurtifyButton>
                <NurtifyButton variant="primary" size="medium" disabled>
                  Button
                </NurtifyButton>
              </div>
              <div className="button-size-group">
                <div className="button-size-label">SMALL</div>
                <NurtifyButton variant="primary" size="small" onClick={() => toast.info("Primary Small clicked")}>
                  Button
                </NurtifyButton>
                <NurtifyButton variant="primary" size="small" onClick={() => toast.info("Primary Small clicked")}>
                  Hover
                </NurtifyButton>
                <NurtifyButton variant="primary" size="small" disabled>
                  Button
                </NurtifyButton>
              </div>
              <div className="button-size-group">
                <div className="button-size-label">REGULAR</div>
                <NurtifyButton variant="primary" size="regular" onClick={() => toast.info("Primary Regular Hover")}>
                  Button
                </NurtifyButton>
                <NurtifyButton variant="primary" size="regular" onClick={() => toast.info("Primary Regular Hover")}>
                  Hover
                </NurtifyButton>
                <NurtifyButton variant="primary" size="regular" disabled>
                  Button
                </NurtifyButton>
              </div>

            </div>
          </div>

          {/* Outline Buttons Section */}
          <div className="button-demo-section">
            <div className="button-demo-title">OUTLINE BUTTONS</div>
            <div className="button-demo-row">
              <div className="button-size-group">
                <div className="button-size-label">MEDIUM</div>
                <NurtifyButton variant="primary" size="medium" outline onClick={() => toast.info("Outline Medium clicked")}>
                  Button
                </NurtifyButton>
                <NurtifyButton variant="primary" size="medium" outline onClick={() => toast.info("Outline Medium Hover")}>
                  Hover
                </NurtifyButton>
                <NurtifyButton variant="primary" size="medium" outline disabled>
                  Button
                </NurtifyButton>
              </div>
              <div className="button-size-group">
                <div className="button-size-label">SMALL</div>
                <NurtifyButton variant="primary" size="small" outline onClick={() => toast.info("Outline Small clicked")}>
                  Button
                </NurtifyButton>
                <NurtifyButton variant="primary" size="small" outline onClick={() => toast.info("Outline Small Hover")}>
                  Hover
                </NurtifyButton>
                <NurtifyButton variant="primary" size="small" outline disabled>
                  Button
                </NurtifyButton>
              </div>
              <div className="button-size-group">
                <div className="button-size-label">REGULAR</div>
                <NurtifyButton variant="primary" size="regular" outline onClick={() => toast.info("Outline Regular clicked")}>
                  Button
                </NurtifyButton>
                <NurtifyButton variant="primary" size="regular" outline onClick={() => toast.info("Outline Regular Hover")}>
                  Hover
                </NurtifyButton>
                <NurtifyButton variant="primary" size="regular" outline disabled>
                  Button
                </NurtifyButton>
              </div>

            </div>
          </div>

          {/* Button Variants Section */}
          <div className="button-demo-section">
            <div className="button-demo-title">BUTTON VARIANTS</div>
            <div
              className="button-variants-grid"
              style={{
                display: "grid",
                gridTemplateColumns: "repeat(auto-fit, minmax(140px, 1fr))",
                gap: "12px",
                alignItems: "center"
              }}
            >
              <NurtifyButton variant="primary" size="medium" onClick={() => toast.info("Primary clicked")}>
                Primary
              </NurtifyButton>
              <NurtifyButton variant="primary" size="medium" onClick={() => toast.info("Hover clicked")} style={{ background: '#088395' }}>
                Hover
              </NurtifyButton>
              <NurtifyButton variant="primary" size="medium" disabled>
                Disable
              </NurtifyButton>
              <NurtifyButton variant="success" size="medium" onClick={() => toast.success("Success clicked")}>
                Success
              </NurtifyButton>
              <NurtifyButton variant="danger" size="medium" onClick={() => toast.error("Danger clicked")}>
                Danger
              </NurtifyButton>
              <NurtifyButton variant="warning" size="medium" onClick={() => toast.warning("Warning clicked")}>
                Warning
              </NurtifyButton>
              <NurtifyButton variant="info" size="medium" onClick={() => toast.info("Info clicked")}>
                Info
              </NurtifyButton>
              <NurtifyButton variant="light" size="medium" onClick={() => toast.info("Light clicked")}>
                Light
              </NurtifyButton>
              <NurtifyButton variant="dark" size="medium" onClick={() => toast.info("Dark clicked")}>
                Dark
              </NurtifyButton>
              <NurtifyButton variant="link" size="medium" onClick={() => toast.info("Link clicked")}>
                Link
              </NurtifyButton>
            </div>
          </div>
        </div>
      ),
    },
    {
      id: "filter-bar",
      name: "Filters",
      category: "interactive",
      description: "A flexible filter bar with search, tag, and study selection.",
      keywords: ["filter", "search", "tags", "studies", "bar", "ui", "interactive"],
      component: (
        <>
          <FilterBar
            totalItems={10}
            onSearch={() => { }}
            tags={[]}
            selectedTags={[""]}
            onTagToggle={() => { }}
            studies={[]}
            selectedStudies={[""]}
            onStudyToggle={() => { }}
          />

          <div style={{
            display: "flex",
            gap: 32, 
            flexDirection:"column"
          }}>
            {/* Sidebar Example */}
            <div>
              <h6>Sidebar (minimizable)</h6>
              <NurtifyFilter
                layout="sidebar"
                filters={filters}
                minimizable
                minimized={minimized}
                onMinimize={() => setMinimized((m) => !m)}
              />
            </div>
            {/* Vertical Example */}
            <div>
              <h6>Vertical</h6>
              <NurtifyFilter layout="vertical" filters={filters} />
            </div>
            {/* Horizontal Example */}
            <div>
              <h6>Horizontal</h6>
              <NurtifyFilter layout="horizontal" filters={filters} />
            </div>
          </div></>


      ),
    },
  ];

  // Filter components based on activeCategory and searchQuery
  useEffect(() => {
    let filtered = [...components];

    // Filter by category
    if (activeCategory !== "all") {
      filtered = filtered.filter(component => component.category === activeCategory);
    }

    // Filter by search query
    if (searchQuery.trim() !== "") {
      const query = searchQuery.toLowerCase();
      filtered = filtered.filter(component =>
        component.name.toLowerCase().includes(query) ||
        component.description.toLowerCase().includes(query) ||
        (component.keywords && component.keywords.some(keyword => keyword.toLowerCase().includes(query))) // Include keywords in search
      );
    }

    setFilteredComponents(filtered);
  }, [activeCategory, searchQuery]);

  return (
    <div className="sdk-components">
      {filteredComponents.length > 0 ? (
        <div className="component-grid">
          {filteredComponents.map((component) => (
            <div key={component.id} className="component-card">
              <div className="component-header">
                <h3 className="component-title">{component.name}</h3>
                <span className="component-category">{component.category}</span>
              </div>
              <p className="component-description">{component.description}</p>
              <div className="component-content">
                {component.component}
              </div>
            </div>
          ))}
        </div>
      ) : (
        <div className="no-results">
          <div className="no-results-icon">
            <Search size={48} />
          </div>
          <h3 className="no-results-text">No components found</h3>
          <p className="no-results-subtext">Try adjusting your search or filter criteria</p>
        </div>
      )}
    </div>
  );
};

export default Elements;

import { useRef, useEffect, ChangeEvent } from "react";
import "./styles.css";

interface NurtifyRadioProps {
  id?: string;
  label: string;
  name: string;
  value: string;
  onChange?: (event: ChangeEvent<HTMLInputElement>) => void;
  checked?: boolean;
  onClick?: () => void;
  required?: boolean;
}

const NurtifyRadio: React.FC<NurtifyRadioProps> = ({ label, name, value, onChange, checked, onClick }) => {
  const inputRef = useRef<HTMLInputElement>(null);

  useEffect(() => {
    if (inputRef.current) {
      inputRef.current.checked = checked || false;
    }
  }, [checked]);

  const handleChange = (e: ChangeEvent<HTMLInputElement>) => {
    if (onChange) {
      onChange(e);
    }
  };

  const handleDivClick = () => {
    if (onChange) {
      onChange({ target: { value } } as unknown as ChangeEvent<HTMLInputElement>);
    }
    if (onClick) {
      onClick();
    }
  };

  const isSelected = checked;

  return (
    <div
      className={`nurtify-radio custom-radio ${isSelected ? "selected" : ""} mb-1`}
      onClick={handleDivClick}
    >
      <input
        ref={inputRef}
        type="radio"
        id={value}
        name={name}
        value={value}
        onChange={handleChange}
        onClick={(e: React.MouseEvent) => e.stopPropagation()}
      />
      <label htmlFor={value}>{label}</label>
    </div>
  );
};

export default NurtifyRadio;

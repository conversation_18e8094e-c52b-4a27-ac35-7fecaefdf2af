import api from "@/services/api.ts";

export interface DepartmentEnrollment {
  department_name: string;
  hospital_name: string;
  enrollment_count: number;
}

export interface SponsorEnrollmentsResponse {
  count: number;
  results: DepartmentEnrollment[];
}

export const getSponsorEnrollmentsByDepartment = async () => {
  try {
    const response = await api.get('study/studies/sponsor-patients/');
    const data = response.data;

    // Aggregate enrollments by department
    const departmentEnrollments = new Map<string, DepartmentEnrollment>();

    if (data.results) {
      data.results.forEach((patient: any) => {
        const key = `${patient.site_name}-${patient.hospital_name}`;

        if (departmentEnrollments.has(key)) {
          departmentEnrollments.get(key)!.enrollment_count += 1;
        } else {
          departmentEnrollments.set(key, {
            department_name: patient.site_name,
            hospital_name: patient.hospital_name,
            enrollment_count: 1
          });
        }
      });
    }

    // Convert to array and sort by enrollment count (descending)
    const results = Array.from(departmentEnrollments.values())
      .sort((a, b) => b.enrollment_count - a.enrollment_count);

    return {
      count: results.length,
      results: results
    };
  } catch (error) {
    console.error("Error fetching sponsor enrollments by department:", error);
    throw error;
  }
};
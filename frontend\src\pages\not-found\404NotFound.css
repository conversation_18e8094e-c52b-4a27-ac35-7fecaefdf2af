.not-found-container {
  min-height: 100vh;
  width: 100%;
  position: relative;
  margin: 0;
  padding: 0;
}

.not-found-background {
  background-image: url('/assets/img/404NotFounds.svg');
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
  min-height: 100vh;
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0;
  padding: 0;
}

.not-found-content {
  display: flex;
  align-items: center;
  max-width: 1200px;
  width: 100%;
  padding: 0 4rem;
  gap: 4rem;
  justify-content: center;
}

.not-found-left {
  flex: 1;
  display: flex;
  justify-content: flex-start;
  padding-left: 6vw;
}

.error-text {
  color: #333;
  min-width: 320px;
  max-width: 400px;
  margin-right: 2vw;
}

.error-404 {
  font-size: 4rem;
  font-weight: bold;
  color: #1e3a8a;
  margin: 0 0 1rem 0;
  line-height: 1;
}

.page-not-found {
  font-family: 'Poppins', sans-serif;
  font-weight: 400;
  font-style: normal;
  font-size: 32px;
  line-height: 140%;
  letter-spacing: 0;
  color: #071952;
  margin: 0 0 2rem 0;
  background: none;
}

.go-back-button {
  width: 163px;
  height: 48px;
  gap: 10px;
  opacity: 1;
  padding: 12px 48px;
  border-radius: 35px;
  background: #37B7C3;
  color: white;
  border: none;
  font-size: 1rem;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.3s ease;
  display: inline-flex;
  align-items: center;
  justify-content: center;
}

.go-back-button:hover {
  background: #2ea0ab;
}

@media (max-width: 1024px) {
  .not-found-content {
    gap: 2rem;
    padding: 0 1rem;
  }
  .error-text {
    min-width: 220px;
    max-width: 100%;
    margin-right: 0;
  }
  .not-found-left {
    padding-left: 2vw;
  }
}

@media (max-width: 768px) {
  .not-found-content {
    flex-direction: column;
    text-align: center;
    gap: 2rem;
    padding: 0 2rem;
    justify-content: center;
    align-items: center;
  }
  .not-found-left {
    justify-content: center;
    width: 100%;
    padding-left: 0;
  }
  .error-404 {
    font-size: 3rem;
  }
  .page-not-found {
    font-size: 1.2rem;
  }
}

@media (max-width: 480px) {
  .not-found-content {
    padding: 0 0.5rem;
  }
  .error-text {
    min-width: 0;
    padding: 0 0.5rem;
  }
  .go-back-button {
    width: 100%;
    padding: 12px 0;
  }
}
import { create } from "zustand";

// Define types
export interface Test {
  id: string;
  name: string;
  description?: string;
}

export interface User {
  id: string;
  uuid?: string;
  first_name: string;
  last_name: string;
  email: string;
  identifier?: string;
}

export interface VisitTemplate {
  id: string;
  uuid?: string;
  name: string;
  day: number;
  visit_number: number;
  allowed_earlier_days: number;
  allowed_later_days: number;
  activities: string[];
  reminder_email: string;
  study_uuid: string;
  study?: number;
  patient?: number;
  date?: string;
  comments?: string;
  commentDateTime?: string;
  earliestDays?: number;
  latestDays?: number;
}

export interface Visit {
  id: string;
  uuid?: string;
  name: string;
  dayNumber: number;
  windowDays: number;
  reminderDays: number[];
  tests: Test[];
  study_uuid?: string;
  number?: number;
  study?: number;
  patient?: number;
  date?: string;
  comments?: string;
  commentDateTime?: string;
  earliestDays?: number;
  latestDays?: number;
  status?: "planned" | "on-going" | "completed" | "cancelled";
  isUnscheduled?: boolean; // Replacing isUnplanned
  reason?: string; // Reason for unscheduled visit
  referredBy?: string;
  referredDateTime?: string;
  subjectId?: string;
}

export interface Study {
  id: string;
  uuid?: string;
  name: string;
  full_title?: string;
  description?: string;
  team_email?: string;
  dateAdded?: string;
  visits: (Visit | VisitTemplate)[];
  user_identifier?: string;
  patients?: number[];
  visit_number?: number;
  enrollment_count?: number;
  leading_team_identifier?: string;
  iras?: string;
  team_identifiers?: string[];
  team?: User[];
  sponsor?: {
    identifier: string;
    first_name: string;
    email: string;
    last_name: string;
  };
  leading_team?: string;
  other?: null;
  created_at?: string;
  policies?: any[];
  departments?: Array<{
    uuid: string;
    name: string;
    hospital_name: string;
  }>;
}

export interface Patient {
  id: string;
  name: string;
  email?: string;
  phone?: string;
  nhs_number?: number;
}

export interface ScheduledVisit {
  id: string;
  visitId: string;
  scheduledDate: string;
  status: "scheduled" | "completed" | "cancelled";
}

export interface PatientEnrollment {
  study_uuid: string;
  id: string;
  patient_nhs_number: string;
  patient_uuid: string; 
  first_visit: string;
  referred_by: string;
  referred_date: string;
  patient_code:string;
}

// Define initial values
export const initialStudy: Study = {
  id: "",
  name: "",
  full_title: "",
  description: "",
  team_email: "",
  dateAdded: new Date().toISOString(),
  visits: [],
  leading_team_identifier: "",
  iras: "",
};

export const initialVisit: VisitTemplate = {
  study_uuid: "",
  id: "",
  name: "",
  day: 0,
  visit_number: 0,
  allowed_earlier_days: 0,
  allowed_later_days: 0,
  activities: [],
  reminder_email: "<EMAIL>",
  comments: "",
};

export const initialVisitTemplate: VisitTemplate = {
  study_uuid: "",
  id: "",
  name: "",
  day: 0,
  visit_number: 0,
  allowed_earlier_days: 0,
  allowed_later_days: 0,
  activities: [],
  reminder_email: "", // Will be set dynamically based on current study
  comments: "",
};

export const initialPatient: Patient = {
  id: "",
  name: "",
  email: "",
  phone: "",
  nhs_number: 0,
};

export const initialEnrollment: PatientEnrollment = {
  study_uuid: "",
  id: "",
  patient_nhs_number: "",
  patient_uuid: "",
  first_visit: "",
  referred_by: "",
  referred_date: "",
  patient_code:""
};

// Define available tests
export const availableTests: Test[] = [
  {
    id: "test1",
    name: "Local Blood Test",
    description: "Local laboratory blood analysis",
  },
  {
    id: "test2",
    name: "Central Lab Test",
    description: "Samples sent to central laboratory",
  },
  {
    id: "test3",
    name: "Walking Test",
    description: "Assessment of walking ability",
  },
  {
    id: "test4",
    name: "Radiology Procedure",
    description: "Imaging procedures",
  },
  {
    id: "test5",
    name: "Dosing",
    description: "Administration of study medication",
  },
  { id: "test6", name: "Consenting", description: "Patient consent process" },
  {
    id: "test7",
    name: "Screening",
    description: "Initial eligibility assessment",
  },
  {
    id: "test8",
    name: "Hospitalization",
    description: "Inpatient stay required",
  },
  {
    id: "test9",
    name: "Baseline",
    description: "Baseline measurements and assessments",
  },
];

// Define store state type
interface ScheduleEventState {
  studies: Study[];
  // Study methods
  currentStudy: Study | null;

  // Enrollment methods
  availableTests: Test[];
  setCurrentStudy: (study: Study | null) => void;
}

// Create the store
export const useScheduleEventStore = create<ScheduleEventState>((set) => ({
  currentPhase: 1,
  studies: [],
  patients: [],
  enrollments: [],
  currentStudy: null,
  availableTests: availableTests,
  setCurrentStudy: (study) => set({ currentStudy: study }),
}));

export default useScheduleEventStore;

/* Apply Study Modal Styles - Prefixed with .dept-studies-modal- */

/* Modal Overlay */
.dept-studies-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.6);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: 20px;
}

@keyframes dept-studies-modal-fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

/* Main Modal Container */
.dept-studies-modal-container {
  background: var(--color-white);
  border-radius: 20px;
  box-shadow: 0 25px 80px rgba(0, 0, 0, 0.25);
  width: 100%;
  max-width: 800px;
  max-height: 90vh;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  animation: dept-studies-modal-slideUp 0.4s cubic-bezier(0.34, 1.56, 0.64, 1);
  border: 1px solid var(--color-light-2);
}

@keyframes dept-studies-modal-slideUp {
  from {
    opacity: 0;
    transform: translateY(40px) scale(0.9);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

/* Modal Header */
.dept-studies-modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 32px 36px;
  border-bottom: 1px solid var(--color-light-2);
  background: linear-gradient(135deg, var(--color-purple-1) 0%, var(--color-blue-1) 100%);
  color: white;
  position: relative;
}

.dept-studies-modal-header::before {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
}

.dept-studies-modal-header h2 {
  margin: 0;
  font-size: var(--text-26);
  font-weight: 700;
  font-family: var(--font-primary);
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  display: flex;
  align-items: center;
  gap: 12px;
}

.dept-studies-modal-header-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 10px;
  backdrop-filter: blur(10px);
}

.dept-studies-modal-close-button {
  background: none;
  border: none;
  color: white;
  cursor: pointer;
  padding: 12px;
  border-radius: 10px;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  display: flex;
  align-items: center;
  justify-content: center;
}

.dept-studies-modal-close-button:hover:not(:disabled) {
  background-color: rgba(255, 255, 255, 0.15);
  transform: scale(1.05);
}

.dept-studies-modal-close-button:active {
  transform: scale(0.95);
}

.dept-studies-modal-close-button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* Modal Content */
.dept-studies-modal-content {
  flex: 1;
  overflow-y: auto;
  padding: 36px;
  background: var(--color-white);
}

.dept-studies-modal-content::-webkit-scrollbar {
  width: 8px;
}

.dept-studies-modal-content::-webkit-scrollbar-track {
  background: var(--color-light-3);
  border-radius: 4px;
}

.dept-studies-modal-content::-webkit-scrollbar-thumb {
  background: var(--color-light-1);
  border-radius: 4px;
}

.dept-studies-modal-content::-webkit-scrollbar-thumb:hover {
  background: var(--color-purple-1);
}

/* Description */
.dept-studies-modal-description {
  font-size: var(--text-16);
  color: var(--color-dark-1);
  margin-bottom: 32px;
  line-height: 1.6;
  padding: 20px 24px;
  background: linear-gradient(135deg, var(--color-light-6) 0%, var(--color-white) 100%);
  border-radius: 12px;
  border-left: 4px solid var(--color-purple-1);
  font-weight: 500;
}

/* Study Info Section */
.dept-studies-modal-study-info {
  background: var(--color-white);
  border: 2px solid var(--color-light-2);
  border-radius: 16px;
  padding: 28px;
  margin-bottom: 32px;
  position: relative;
  overflow: hidden;
}

.dept-studies-modal-study-info::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, var(--color-blue-3), var(--color-blue-4));
}

.dept-studies-modal-info-item {
  display: flex;
  align-items: flex-start;
  gap: 16px;
  margin-bottom: 20px;
  padding: 16px 0;
  border-bottom: 1px solid var(--color-light-3);
}

.dept-studies-modal-info-item:last-child {
  border-bottom: none;
  margin-bottom: 0;
}

.dept-studies-modal-info-label {
  font-weight: 600;
  color: var(--color-dark-1);
  min-width: 120px;
  font-size: var(--text-14);
  display: flex;
  align-items: center;
  gap: 8px;
}

.dept-studies-modal-info-label::before {
  content: '';
  width: 6px;
  height: 6px;
  background: var(--color-purple-1);
  border-radius: 50%;
  flex-shrink: 0;
}

.dept-studies-modal-info-value {
  flex: 1;
  color: var(--color-dark-1);
  font-size: var(--text-14);
  line-height: 1.5;
}

/* Tasks Section */
.dept-studies-modal-tasks-section {
  margin-bottom: 32px;
}

.dept-studies-modal-tasks-header {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 24px;
  padding-bottom: 16px;
  border-bottom: 2px solid var(--color-light-2);
}

.dept-studies-modal-tasks-header h3 {
  margin: 0;
  font-size: var(--text-20);
  font-weight: 700;
  color: var(--color-dark-1);
  font-family: var(--font-primary);
}

.dept-studies-modal-tasks-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  background: linear-gradient(135deg, var(--color-purple-1) 0%, var(--color-blue-1) 100%);
  border-radius: 8px;
  color: white;
}

/* Loading State */
.dept-studies-modal-loading {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12px;
  padding: 40px;
  color: var(--color-light-1);
  background: linear-gradient(135deg, var(--color-light-6) 0%, var(--color-white) 100%);
  border-radius: 12px;
  border: 2px dashed var(--color-light-2);
}

.dept-studies-modal-spinner {
  width: 20px;
  height: 20px;
  border: 2px solid var(--color-light-2);
  border-top: 2px solid var(--color-purple-1);
  border-radius: 50%;
  animation: dept-studies-modal-spin 1s linear infinite;
}

.dept-studies-modal-loading-text {
  font-size: var(--text-15);
  font-weight: 500;
  color: var(--color-dark-1);
}

/* Error State */
.dept-studies-modal-error {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 20px 24px;
  background: linear-gradient(135deg, var(--color-error-1) 0%, var(--color-red-2) 100%);
  border: 2px solid var(--color-error-2);
  border-radius: 12px;
  color: var(--color-error-2);
  font-weight: 500;
  font-size: var(--text-14);
}

.dept-studies-modal-error-icon {
  flex-shrink: 0;
}

/* Empty State */
.dept-studies-modal-empty {
  text-align: center;
  padding: 40px 20px;
  background: linear-gradient(135deg, var(--color-light-6) 0%, var(--color-white) 100%);
  border-radius: 12px;
  border: 2px dashed var(--color-light-2);
}

.dept-studies-modal-empty-icon {
  width: 64px;
  height: 64px;
  margin: 0 auto 16px;
  background: var(--color-light-2);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--color-light-1);
}

.dept-studies-modal-empty p {
  margin: 0;
  color: var(--color-light-1);
  font-size: var(--text-15);
  font-weight: 500;
}

/* Tasks List */
.dept-studies-modal-tasks-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.dept-studies-modal-task-item {
  display: flex;
  align-items: flex-start;
  gap: 16px;
  padding: 20px;
  background: var(--color-white);
  border: 2px solid var(--color-light-2);
  border-radius: 12px;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
}

.dept-studies-modal-task-item::before {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  width: 4px;
  background: linear-gradient(135deg, var(--color-purple-1) 0%, var(--color-blue-1) 100%);
  transform: scaleY(0);
  transition: transform 0.3s ease;
}

.dept-studies-modal-task-item:hover {
  border-color: var(--color-purple-1);
  box-shadow: 0 8px 24px rgba(55, 183, 195, 0.15);
  transform: translateY(-2px);
}

.dept-studies-modal-task-item:hover::before {
  transform: scaleY(1);
}

.dept-studies-modal-task-item.selected {
  border-color: var(--color-purple-1);
  background: linear-gradient(135deg, rgba(55, 183, 195, 0.05) 0%, rgba(55, 183, 195, 0.02) 100%);
  box-shadow: 0 4px 16px rgba(55, 183, 195, 0.2);
}

.dept-studies-modal-task-item.selected::before {
  transform: scaleY(1);
}

.dept-studies-modal-task-checkbox {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;
  flex-shrink: 0;
  margin-top: 2px;
  color: var(--color-purple-1);
  transition: all 0.3s ease;
}

.dept-studies-modal-task-content {
  flex: 1;
}

.dept-studies-modal-task-name {
  font-size: var(--text-16);
  font-weight: 600;
  color: var(--color-dark-1);
  margin: 0 0 8px 0;
  line-height: 1.3;
}

.dept-studies-modal-task-description {
  font-size: var(--text-14);
  color: var(--color-light-1);
  margin: 0;
  line-height: 1.5;
}

/* Summary Text */
.dept-studies-modal-summary {
  padding: 24px;
  background: linear-gradient(135deg, var(--color-light-6) 0%, var(--color-white) 100%);
  border-radius: 12px;
  border: 1px solid var(--color-light-2);
  margin-bottom: 32px;
  font-size: var(--text-14);
  line-height: 1.6;
  color: var(--color-dark-1);
}

.dept-studies-modal-summary-highlight {
  display: inline-flex;
  align-items: center;
  gap: 6px;
  background: linear-gradient(135deg, var(--color-purple-1) 0%, var(--color-blue-1) 100%);
  color: white;
  padding: 4px 12px;
  border-radius: 16px;
  font-weight: 600;
  font-size: var(--text-13);
  margin-left: 8px;
}

/* Modal Footer */
.dept-studies-modal-footer {
  padding: 28px 36px;
  border-top: 1px solid var(--color-light-2);
  background: linear-gradient(135deg, var(--color-light-6) 0%, var(--color-white) 100%);
}

.dept-studies-modal-footer-buttons {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 16px;
}

/* Button Styles */
.dept-studies-modal-btn {
  padding: 14px 28px;
  border-radius: 12px;
  font-weight: 600;
  cursor: pointer;
  border: none;
  display: flex;
  align-items: center;
  gap: 10px;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  font-size: var(--text-14);
  font-family: var(--font-primary);
  position: relative;
  overflow: hidden;
  min-width: 120px;
  justify-content: center;
}

.dept-studies-modal-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s;
}

.dept-studies-modal-btn:hover:not(:disabled)::before {
  left: 100%;
}

.dept-studies-modal-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none !important;
  box-shadow: none !important;
}

.dept-studies-modal-btn-secondary {
  background: linear-gradient(135deg, var(--color-light-1) 0%, var(--color-dark-3) 100%);
  color: white;
  box-shadow: 0 4px 16px rgba(111, 122, 153, 0.3);
}

.dept-studies-modal-btn-secondary:hover:not(:disabled) {
  background: linear-gradient(135deg, var(--color-dark-3) 0%, var(--color-dark-4) 100%);
  transform: translateY(-2px);
  box-shadow: 0 8px 24px rgba(111, 122, 153, 0.4);
}

.dept-studies-modal-btn-primary {
  background: linear-gradient(135deg, var(--color-purple-1) 0%, var(--color-blue-1) 100%);
  color: white;
  box-shadow: 0 4px 16px rgba(55, 183, 195, 0.3);
}

.dept-studies-modal-btn-primary:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 8px 24px rgba(55, 183, 195, 0.4);
}

.dept-studies-modal-btn:active:not(:disabled) {
  transform: translateY(0);
}

/* Loading State in Button */
.dept-studies-modal-btn-loading {
  display: inline-flex;
  align-items: center;
  gap: 8px;
}

.dept-studies-modal-btn-spinner {
  width: 16px;
  height: 16px;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-top: 2px solid white;
  border-radius: 50%;
  animation: dept-studies-modal-spin 1s linear infinite;
}

/* Animations */
@keyframes dept-studies-modal-spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Responsive Design */
@media (max-width: 1024px) {
  .dept-studies-modal-container {
    max-width: 95vw;
    margin: 10px;
  }
}

@media (max-width: 768px) {
  .dept-studies-modal-container {
    max-height: 95vh;
    border-radius: 16px;
  }

  .dept-studies-modal-header,
  .dept-studies-modal-content,
  .dept-studies-modal-footer {
    padding: 24px 28px;
  }

  .dept-studies-modal-header h2 {
    font-size: var(--text-22);
  }

  .dept-studies-modal-study-info {
    padding: 24px;
  }

  .dept-studies-modal-info-item {
    flex-direction: column;
    gap: 8px;
    align-items: flex-start;
  }

  .dept-studies-modal-info-label {
    min-width: auto;
  }

  .dept-studies-modal-footer-buttons {
    flex-direction: column;
    gap: 12px;
  }

  .dept-studies-modal-btn {
    width: 100%;
  }

  .dept-studies-modal-task-item {
    padding: 16px;
  }
}

@media (max-width: 480px) {
  .dept-studies-modal-header,
  .dept-studies-modal-content,
  .dept-studies-modal-footer {
    padding: 20px 24px;
  }

  .dept-studies-modal-header h2 {
    font-size: var(--text-20);
  }

  .dept-studies-modal-study-info {
    padding: 20px;
  }

  .dept-studies-modal-btn {
    padding: 12px 20px;
    font-size: var(--text-13);
  }

  .dept-studies-modal-task-name {
    font-size: var(--text-15);
  }

  .dept-studies-modal-task-description {
    font-size: var(--text-13);
  }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .dept-studies-modal-container {
    border: 3px solid var(--color-dark-1);
  }

  .dept-studies-modal-task-item {
    border-width: 3px;
  }

  .dept-studies-modal-task-item:focus {
    border-width: 4px;
  }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  .dept-studies-modal-container,
  .dept-studies-modal-overlay,
  .dept-studies-modal-btn,
  .dept-studies-modal-task-item,
  .dept-studies-modal-spinner,
  .dept-studies-modal-btn-spinner {
    animation: none;
    transition: none;
  }
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
  .dept-studies-modal-container,
  .dept-studies-modal-content,
  .dept-studies-modal-study-info,
  .dept-studies-modal-task-item {
    background: var(--color-dark-4);
    border-color: var(--color-dark-3);
    color: var(--color-white);
  }

  .dept-studies-modal-footer {
    background: linear-gradient(135deg, var(--color-dark-3) 0%, var(--color-dark-4) 100%);
  }

  .dept-studies-modal-description,
  .dept-studies-modal-summary,
  .dept-studies-modal-loading,
  .dept-studies-modal-empty {
    background: linear-gradient(135deg, var(--color-dark-3) 0%, var(--color-dark-4) 100%);
  }

  .dept-studies-modal-info-value,
  .dept-studies-modal-task-name {
    color: var(--color-white);
  }

  .dept-studies-modal-task-description {
    color: var(--color-light-3);
  }
}

/* Focus styles for accessibility */
.dept-studies-modal-close-button:focus,
.dept-studies-modal-btn:focus,
.dept-studies-modal-task-item:focus {
  outline: 2px solid var(--color-purple-1);
  outline-offset: 2px;
}

/* Print styles */
@media print {
  .dept-studies-modal-overlay {
    position: static;
    background: none;
    backdrop-filter: none;
  }

  .dept-studies-modal-container {
    box-shadow: none;
    border: 1px solid #ccc;
    max-height: none;
  }

  .dept-studies-modal-footer,
  .dept-studies-modal-close-button {
    display: none;
  }
}

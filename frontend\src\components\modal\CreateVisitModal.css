.edit-visit-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  overflow-y: auto;
  padding: 20px;
}

.edit-visit-modal {
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.2);
  width: 600px;
  max-width: 90%;
  max-height: 85vh;
  display: flex;
  flex-direction: column;
  position: relative;
}

.edit-visit-modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  border-bottom: 1px solid #e9ecef;
  position: sticky;
  top: 0;
  background-color: white;
  border-radius: 8px 8px 0 0;
  z-index: 10;
}

.edit-visit-modal-title {
  font-size: 18px;
  font-weight: 600;
  color: #333;
  margin: 0;
  display: flex;
  align-items: center;
}

.edit-visit-modal-title .modal-icon {
  margin-right: 8px;
  color: #37b7c3;
}

.edit-visit-modal-close {
  background: none;
  border: none;
  color: #6c757d;
  cursor: pointer;
  padding: 4px;
  transition: color 0.2s;
}

.edit-visit-modal-close:hover {
  color: #dc3545;
}

.edit-visit-modal-body {
  padding: 20px;
  overflow-y: auto;
  max-height: calc(85vh - 130px); /* Adjust for header and footer height */
}

.edit-visit-modal-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  padding: 16px 20px;
  border-top: 1px solid #e9ecef;
  position: sticky;
  bottom: 0;
  background-color: white;
  border-radius: 0 0 8px 8px;
  z-index: 10;
}

.cancel-btn, .save-btn {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 8px 16px;
  border-radius: 4px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
}

.cancel-btn {
  background-color: #f8f9fa;
  color: #6c757d;
  border: 1px solid #ced4da;
}

.cancel-btn:hover {
  background-color: #e9ecef;
}

.save-btn {
  background-color: #37b7c3;
  color: white;
  border: none;
}

.save-btn:hover {
  background-color: #2c9aa5;
}

/* Form section styling */
.form-section {
  margin-bottom: 24px;
  border: 1px solid #e9ecef;
  border-radius: 6px;
  padding: 16px;
  background-color: #f8f9fa;
}

.form-section:last-child {
  margin-bottom: 0;
}

.form-section-title {
  font-size: 16px;
  font-weight: 600;
  color: #37b7c3;
  margin-top: 0;
  margin-bottom: 16px;
  display: flex;
  align-items: center;
  gap: 8px;
}

.form-group {
  margin-bottom: 16px;
}

.form-group:last-child {
  margin-bottom: 0;
}

.form-group label {
  display: block;
  margin-bottom: 6px;
  font-size: 14px;
  font-weight: 500;
  color: #495057;
}

.form-group small {
  display: block;
  font-size: 12px;
  color: #6c757d;
  margin-top: 4px;
}

/* Activities selection grid */
.activities-selection {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 10px;
}

.activity-checkbox {
  margin-bottom: 8px;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .edit-visit-modal {
    width: 95%;
  }
  
  .form-row {
    flex-direction: column;
  }
  
  .activities-selection {
    grid-template-columns: 1fr;
  }
}
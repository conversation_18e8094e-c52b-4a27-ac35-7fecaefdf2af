import Socials from "@/components/common/Socials";
import FooterLinks from "@/components/common/FooterLinks";
import DarkFooter from "./DarkFooter";

export default function Footer() {

  return (
    <footer className="footer -type-1 -green-links" style={{ backgroundColor: "#37B7C3" }}>
      <div className="container">
        {/* Social Media Section */}
        <div className="footer-header">
          <div className="row justify-between items-center">
            <div className="col-12 col-md-auto text-center text-md-left mb-20 mb-md-0">
              <div className="footer-header-socials">
                <div className="footer-header-socials__title text-white mb-10 mb-md-0">
                  Follow us on social media
                </div>
                <div className="footer-header-socials__list justify-center justify-md-start">
                  <Socials />
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Links Section */}
        <div className="footer-columns">
          <div className="row y-gap-30">
            <FooterLinks
              allClasses={"text-17 fw-500 text-white uppercase mb-25 text-center text-md-left"}
            />
          </div>
        </div>
      </div>
      <DarkFooter />
    </footer>
  );
}

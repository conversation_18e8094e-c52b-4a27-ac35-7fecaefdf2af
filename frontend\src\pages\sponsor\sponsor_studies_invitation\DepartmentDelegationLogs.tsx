import { useState, useMemo, useCallback } from "react";
import { Search, Clock, CheckCircle, Users, Filter, Eye, List, FileText, AlertCircle } from "lucide-react";
import DataTable from "@/components/common/DataTable";
import { useDelegationLogsQuery } from "@/hooks/delegation.query";
import DelegationDocumentsModal from "./DelegationDocumentsModal";
import TasksModal from "../../dashboard/delegation-logs/TasksModal";
import { SelectedTask } from "@/services/api/delegation.service";
import "./sponsor-studies-invitation.css";

interface DelegationLogListItem {
  uuid: string;
  study_name: string;
  department_name: string;
  team_member_name: string;
  pi_name: string;
  study_task: string;
  selected_tasks?: SelectedTask[];
  status: string;
  pi_notes: string | null;
  sponsor_notes: string | null;
  created_at: string;
  updated_at: string;
}

export default function DepartmentDelegationLogs(): JSX.Element {
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedDepartment, setSelectedDepartment] = useState<string>("");
  const [showDocumentsModal, setShowDocumentsModal] = useState(false);
  const [selectedDelegationForDocuments, setSelectedDelegationForDocuments] = useState<DelegationLogListItem | null>(null);
  const [showTasksModal, setShowTasksModal] = useState(false);
  const [selectedTasks, setSelectedTasks] = useState<SelectedTask[]>([]);
  const [selectedStudyName, setSelectedStudyName] = useState("");
  const [selectedTeamMemberName, setSelectedTeamMemberName] = useState("");
  
  const { data: delegationLogsResponse, isLoading, isError } = useDelegationLogsQuery();

  // Extract delegation logs from the API response
  const delegationLogs = useMemo(() => {
    if (!delegationLogsResponse) return [];
    return delegationLogsResponse.results || delegationLogsResponse || [];
  }, [delegationLogsResponse]);

  // Get unique departments for filter
  const availableDepartments = useMemo(() => {
    if (!delegationLogs) return [];
    const departments = [...new Set(delegationLogs.map(log => log.department_name))];
    return departments.sort();
  }, [delegationLogs]);

  // Filter delegation logs by selected department
  const filteredByDepartment = useMemo(() => {
    if (!delegationLogs) return [];
    if (!selectedDepartment) return delegationLogs;
    
    return delegationLogs.filter(log => 
      log.department_name === selectedDepartment
    );
  }, [delegationLogs, selectedDepartment]);

  // Filter by search term
  const filteredDelegationLogs = useMemo(() => {
    if (!filteredByDepartment) return [];
    
    return filteredByDepartment.filter((log: DelegationLogListItem) => {
      const matchesSearch = 
        log.study_name?.toLowerCase().includes(searchTerm.toLowerCase()) ||
        log.team_member_name?.toLowerCase().includes(searchTerm.toLowerCase()) ||
        log.pi_name?.toLowerCase().includes(searchTerm.toLowerCase()) ||
        log.study_task?.toLowerCase().includes(searchTerm.toLowerCase()) ||
        log.status?.toLowerCase().includes(searchTerm.toLowerCase());
      
      return matchesSearch;
    });
  }, [filteredByDepartment, searchTerm]);



  const handleViewDocuments = useCallback((delegation: DelegationLogListItem) => {
    setSelectedDelegationForDocuments(delegation);
    setShowDocumentsModal(true);
  }, []);

  const handleCloseDocumentsModal = () => {
    setShowDocumentsModal(false);
    setSelectedDelegationForDocuments(null);
  };

  const handleShowTasks = useCallback((delegation: DelegationLogListItem) => {
    setSelectedTasks(delegation.selected_tasks || []);
    setSelectedStudyName(delegation.study_name);
    setSelectedTeamMemberName(delegation.team_member_name);
    setShowTasksModal(true);
  }, []);

  const handleCloseTasksModal = () => {
    setShowTasksModal(false);
    setSelectedTasks([]);
    setSelectedStudyName("");
    setSelectedTeamMemberName("");
  };

  // Define columns for the DataTable
  const columns = useMemo(() => [
    {
      key: "study_name" as keyof DelegationLogListItem,
      header: "Study Name",
      sortable: true,
      render: (value: unknown): React.ReactNode => {
        return value ? String(value) : "N/A";
      },
    },
    {
      key: "team_member_name" as keyof DelegationLogListItem,
      header: "Applicant",
      sortable: true,
      render: (value: unknown): React.ReactNode => {
        return value ? String(value) : "N/A";
      },
    },
    {
      key: "pi_name" as keyof DelegationLogListItem,
      header: "Principal Investigator",
      sortable: true,
      render: (value: unknown): React.ReactNode => {
        return value ? String(value) : "N/A";
      },
    },
    {
      key: "study_task" as keyof DelegationLogListItem,
      header: "Study Task",
      sortable: true,
      render: (value: unknown): React.ReactNode => {
        const task = value ? String(value) : "";
        return task.length > 50 
          ? `${task.substring(0, 50)}...` 
          : task || "N/A";
      },
    },
    {
      key: "status" as keyof DelegationLogListItem,
      header: "Status",
      sortable: true,
      render: (value: unknown): React.ReactNode => {
        const status = value ? String(value) : "";
        switch (status) {
          case 'pending_pi':
            return <span>Pending PI Review</span>;
          case 'pending_sponsor':
            return <span>Pending Sponsor Review</span>;
          case 'accepted':
            return <span>Accepted</span>;
          case 'pi_rejected':
            return <span>PI Rejected</span>;
          case 'sponsor_rejected':
            return <span>Sponsor Rejected</span>;
          default:
            return <span>{status}</span>;
        }
      },
    },
    {
      key: "created_at" as keyof DelegationLogListItem,
      header: "Application Date",
      sortable: true,
      render: (value: unknown): React.ReactNode => {
        return value ? new Date(String(value)).toLocaleDateString() : "N/A";
      },
    },
  ], []);

  // Define actions for the DataTable
  const actions = useMemo(() => [
    {
      icon: <List size={16} />,
      tooltipText: "View Selected Tasks",
      onClick: (delegation: DelegationLogListItem) => handleShowTasks(delegation),
      disabled: () => false
    },
    {
      icon: <Eye size={16} />,
      tooltipText: "View Documents & Review",
      onClick: (delegation: DelegationLogListItem) => handleViewDocuments(delegation),
    }
  ], [handleViewDocuments, handleShowTasks]);

  if (isLoading) {
    return (
      <div>
        <div>Loading department applications...</div>
      </div>
    );
  }

  if (isError) {
    return (
      <div>
        <div>
          <h3>Error Loading Applications</h3>
          <p>There was an error loading the applications. Please try again later.</p>
        </div>
      </div>
    );
  }

  return (
    <div className="ssi-delegation-logs">
      {/* Enhanced Header */}
      <div className="ssi-card">
        <div className="ssi-card-header">
          <div>
            <h2 className="ssi-card-title">
              <Users size={24} />
              Department Applications Review
            </h2>
            <p className="ssi-card-subtitle">Review and manage applications from department members to join studies</p>
          </div>
        </div>
      </div>

      {/* Enhanced Summary Stats */}
      <div className="ssi-stats-grid">
        <div className="ssi-stat-card-alt">
          <div className="ssi-stat-card-alt-header">
            <div className="ssi-stat-card-alt-icon">
              <Clock size={20} />
            </div>
            <div className="ssi-stat-card-alt-content">
              <h3>{filteredDelegationLogs.filter(log => log.status === 'pending_pi').length}</h3>
              <p>Pending PI Review</p>
            </div>
          </div>
        </div>
        <div className="ssi-stat-card-alt">
          <div className="ssi-stat-card-alt-header">
            <div className="ssi-stat-card-alt-icon">
              <AlertCircle size={20} />
            </div>
            <div className="ssi-stat-card-alt-content">
              <h3>{filteredDelegationLogs.filter(log => log.status === 'pending_sponsor').length}</h3>
              <p>Pending Sponsor Review</p>
            </div>
          </div>
        </div>
        <div className="ssi-stat-card-alt">
          <div className="ssi-stat-card-alt-header">
            <div className="ssi-stat-card-alt-icon">
              <CheckCircle size={20} />
            </div>
            <div className="ssi-stat-card-alt-content">
              <h3>{filteredDelegationLogs.filter(log => log.status === 'accepted').length}</h3>
              <p>Approved Applications</p>
            </div>
          </div>
        </div>
        <div className="ssi-stat-card-alt">
          <div className="ssi-stat-card-alt-header">
            <div className="ssi-stat-card-alt-icon">
              <FileText size={20} />
            </div>
            <div className="ssi-stat-card-alt-content">
              <h3>{delegationLogsResponse?.count || 0}</h3>
              <p>Total Applications</p>
            </div>
          </div>
        </div>
      </div>

      {/* Enhanced Search and Controls */}
      <div className="ssi-controls">
        <div className="ssi-controls-header">
          <h3>Filter Applications</h3>
          <p>Search and filter applications to find specific submissions</p>
        </div>
        <div className="ssi-controls-row">
          <div className="ssi-search-group">
            <div className="ssi-search-wrapper">
              <Search size={18} className="ssi-search-icon" />
              <input
                type="text"
                className="ssi-search-input"
                placeholder="Search by study name, applicant, PI, or status..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
              />
              {searchTerm && (
                <button
                  className="ssi-search-clear"
                  onClick={() => setSearchTerm("")}
                  title="Clear search"
                >
                  ×
                </button>
              )}
            </div>
          </div>
          <div className="ssi-filter-group">
            <div className="ssi-filter-wrapper">
              <Filter size={16} />
              <select
                className="ssi-filter-select"
                value={selectedDepartment}
                onChange={(e) => setSelectedDepartment(e.target.value)}
              >
                <option value="">All Departments</option>
                {availableDepartments.map((department) => (
                  <option key={department} value={department}>
                    {department}
                  </option>
                ))}
              </select>
            </div>
          </div>
        </div>
      </div>

      {/* Enhanced Applications Table */}
      <div className="ssi-card">
        {isLoading ? (
          <div className="ssi-loading">
            <div className="ssi-loading-spinner"></div>
            <div className="ssi-loading-text">Loading Applications</div>
            <div className="ssi-loading-subtext">Please wait while we fetch department applications...</div>
          </div>
        ) : isError ? (
          <div className="ssi-empty-state">
            <div className="ssi-empty-icon">
              <AlertCircle size={48} />
            </div>
            <h3 className="ssi-empty-title">Error Loading Applications</h3>
            <p className="ssi-empty-description">
              There was an error loading the applications. Please try again later.
            </p>
          </div>
        ) : filteredDelegationLogs.length > 0 ? (
          <DataTable
            data={filteredDelegationLogs}
            columns={columns}
            actions={actions}
            noDataMessage="No applications found matching your criteria"
            defaultItemsPerPage={10}
            itemsPerPageOptions={[5, 10, 25, 50]}
            globalFilterPlaceholder="Search applications..."
          />
        ) : (
          <div className="ssi-empty-state">
            <div className="ssi-empty-icon">
              <FileText size={48} />
            </div>
            <h3 className="ssi-empty-title">No Applications Found</h3>
            <p className="ssi-empty-description">
              {searchTerm || selectedDepartment
                ? "No applications match your current filters. Try adjusting your search criteria."
                : "No department applications have been submitted yet. Applications will appear here once departments submit them."
              }
            </p>
          </div>
        )}
      </div>

      {/* Documents Modal */}
      {selectedDelegationForDocuments && (
        <DelegationDocumentsModal
          isOpen={showDocumentsModal}
          onClose={handleCloseDocumentsModal}
          delegationUuid={selectedDelegationForDocuments.uuid}
          delegationData={{
            study_name: selectedDelegationForDocuments.study_name,
            team_member_name: selectedDelegationForDocuments.team_member_name,
            department_name: selectedDelegationForDocuments.department_name,
            pi_name: selectedDelegationForDocuments.pi_name,
            study_task: selectedDelegationForDocuments.study_task,
            status: selectedDelegationForDocuments.status
          }}
        />
      )}

      {/* Tasks Modal */}
      <TasksModal
        isOpen={showTasksModal}
        tasks={selectedTasks}
        studyName={selectedStudyName}
        teamMemberName={selectedTeamMemberName}
        onClose={handleCloseTasksModal}
      />
    </div>
  );
}

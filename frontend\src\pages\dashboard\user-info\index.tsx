import React, { useState } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { useUpdateUserMutation, useDeleteUserMutation,useCurrentUserQuery } from '@/hooks/user.query';
import { User } from '@/types/types';
import NurtifyText from '@components/NurtifyText';
import NurtifyInput from '@components/NurtifyInput';
import { motion } from 'framer-motion';
import { User as UserIcon, Mail } from 'lucide-react';
import './userInfo.css';

export default function UserInfo() {
    const { uuid } = useParams<{ uuid: string }>();
    console.log(uuid);
    const navigate = useNavigate();
    const updateUserMutation = useUpdateUserMutation();
    const deleteUserMutation = useDeleteUserMutation();
    const currentUser = useCurrentUserQuery();
    console.log("current user " + currentUser.data?.department?.name);

    const [formData, setFormData] = useState<Partial<User>>({
        email: '',
        first_name: '',
    });

    const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        const { name, value } = e.target;
        setFormData({ ...formData, [name]: value });
    };

    const handleSubmit = (e: React.FormEvent) => {
        e.preventDefault();
        if (uuid) {
            updateUserMutation.mutate({ uuid, data: formData, }, {
                onSuccess: () => {
                    navigate('/org/dashboard/users');
                },
             });
        }
    };

    const handleDelete = () => {
        if (uuid) {
            deleteUserMutation.mutate(uuid, {
                onSuccess: () => {
                    navigate('/org/dashboard/users');
                },
            });
        }
    };

    return (
        <div className="user-info-container">
            <div className="user-info-header">
                <div className="user-info-title">
                    <h1>
                        <UserIcon size={24} style={{ marginRight: '10px' }} />
                        User Details
                    </h1>
                </div>
                <div className="user-info-subtitle">
                    <h6>Update or delete user information</h6>
                </div>
            </div>

            <form className="user-info-form" onSubmit={handleSubmit}>
                <motion.div
                    className="user-form-section"
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: 0.2, duration: 0.4 }}
                >
                    <h3 className="user-form-section-title">
                        <Mail size={18} style={{ marginRight: '8px', verticalAlign: 'middle' }} />
                        User Information
                    </h3>
                    <div className="row y-gap-30">
                        <div className="col-md-6">
                            <NurtifyText label="Confirm Email*" />
                            <NurtifyInput
                                type="email"
                                name="email"
                                value={formData.email || ''}
                                onChange={handleChange}
                                placeholder="Enter Email here"
                                required
                            />
                        </div>
                        <div className="col-md-6">
                        <NurtifyText label="Department" />
                            <NurtifyInput
                                type="text"
                                name="first_name"
                                value={currentUser.data?.department?.name || ''}
                                onChange={handleChange}
                                placeholder="Enter name here"
                                required
                                disabled
                            />
                        </div>
                        <div className="row y-gap-30">
                        <div className="col-md-6">

                            <NurtifyText label="Job Title" />
                            <NurtifyInput
                                type="text"
                                name="speciality"
                                value={formData.speciality || ''}
                                placeholder="Enter speciality here"
                                onChange={handleChange}

                            />
                        </div>
                        </div>

                    </div>
                </motion.div>

                <div className="user-form-actions">
                    <button
                        type="submit"
                        className="user-btn-submit"
                        disabled={updateUserMutation.isPending}
                    >
                        {updateUserMutation.isPending ? 'Updating...' : 'Update User'}
                    </button>
                    <button
                        type="button"
                        className="user-btn-delete"
                        onClick={handleDelete}
                        disabled={deleteUserMutation.isPending}
                        style={{ marginTop: '10px', backgroundColor: 'red', color: 'white' }}
                    >
                        {deleteUserMutation.isPending ? 'Deleting...' : 'Delete User'}
                    </button>
                </div>
            </form>
        </div>
    );
}
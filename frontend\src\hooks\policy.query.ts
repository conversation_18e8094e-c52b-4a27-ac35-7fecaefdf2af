import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { Policy } from "@/types/types";
import {
    getAllPolicies,
    createPolicy,
    getPolicyByUuid,
    deletePolicyByUuid,
    updatePolicy,
    getPoliciesByStudy,
    getActivePoliciesByStudy,
    getActivePolicies,
    getSupersededPolicies,
    getPatientAndStaffPolicies
    
} from "@/services/api/policy.service.ts";
import { POLICY_KEYS } from './keys';



interface PoliciesResponse {
    results: Policy[];

}

/*export const usePoliciesQuery = () => {
    return useQuery<PoliciesResponse, Error>({
        queryKey: [POLICY_KEYS.GET_ALL],
        queryFn: getAllPolicies,
    });
};*/

export const usePoliciesQuery = (searchTerm?: string) => {
    return useQuery<PoliciesResponse, Error>({
        queryKey: [POLICY_KEYS.GET_ALL, searchTerm],
        queryFn: () => getAllPolicies(searchTerm),
        staleTime: 1000 * 60 * 5, // 5 minutes
        refetchOnWindowFocus: false,
    });
};


export const usePolicyQuery = (uuid: string) => {
    return useQuery<Policy, Error>({
        queryKey: [POLICY_KEYS.GET_BY_UUID, uuid],  // Met à jour la clé de la requête
        queryFn: () => getPolicyByUuid(uuid),     // Utilise la fonction avec uuid
        enabled: !!uuid,                          // Assure-toi que le uuid est défini
    });
};


export const useCreatePolicyMutation = () => {
    const queryClient = useQueryClient();

    return useMutation({
        mutationFn: createPolicy,
        onSuccess: () => {
            // Invalidate and refetch policies list
            queryClient.invalidateQueries({ queryKey: [POLICY_KEYS.CREATE] });
        },
    });
};

//update policy
export const useUpdatePolicyMutation = () => {
    const queryClient = useQueryClient();

    return useMutation({
        mutationFn: ({ uuid, data }: { uuid: string; data: Partial<Policy> }) =>
            updatePolicy(uuid, data),
        onSuccess: (_, { uuid }) => {
            // Invalidate hospital list and the updated hospital details
            queryClient.invalidateQueries({ queryKey: [POLICY_KEYS.GET_ALL] });
            queryClient.invalidateQueries({ queryKey: [POLICY_KEYS.GET_BY_UUID, uuid] });
        },
    });
};




export const useDeletePolicyMutation = () => {
    const queryClient = useQueryClient();

    return useMutation({
        mutationFn: deletePolicyByUuid,
        onSuccess: () => {
            // Invalide et recharge la liste des policies après suppression
            queryClient.invalidateQueries({ queryKey: [POLICY_KEYS.GET_ALL] });
        },
    });
};

// New query hooks for study-based endpoints
export const usePoliciesByStudyQuery = (studyUuid: string) => {
    return useQuery({
        queryKey: [POLICY_KEYS.GET_BY_STUDY, studyUuid],
        queryFn: () => getPoliciesByStudy(studyUuid),
        enabled: !!studyUuid,
        staleTime: 1000 * 60 * 5, // 5 minutes
        refetchOnWindowFocus: false,
    });
};

export const useActivePoliciesByStudyQuery = (studyUuid: string) => {
    return useQuery({
        queryKey: [POLICY_KEYS.GET_ACTIVE_BY_STUDY, studyUuid],
        queryFn: () => getActivePoliciesByStudy(studyUuid),
        enabled: !!studyUuid,
        staleTime: 1000 * 60 * 5, // 5 minutes
        refetchOnWindowFocus: false,
    });
};

export const useActivePoliciesQuery = () => {
    return useQuery<Policy[], Error>({
        queryKey: [POLICY_KEYS.GET_ACTIVE],
        queryFn: getActivePolicies,
        staleTime: 1000 * 60 * 5, // 5 minutes
        refetchOnWindowFocus: false,
    });
};

export const useSupersededPoliciesQuery = () => {
    return useQuery<Policy[], Error>({
        queryKey: [POLICY_KEYS.GET_SUPERSEDED],
        queryFn: getSupersededPolicies,
        staleTime: 1000 * 60 * 5, // 5 minutes
        refetchOnWindowFocus: false,
    });
};

export const usePatientAndStaffPoliciesQuery = () => {
    return useQuery({
        queryKey: [POLICY_KEYS.GET_PATIENT_AND_STAFF],
        queryFn: getPatientAndStaffPolicies,
        staleTime: 1000 * 60 * 5, // 5 minutes
        refetchOnWindowFocus: false,
    });
};



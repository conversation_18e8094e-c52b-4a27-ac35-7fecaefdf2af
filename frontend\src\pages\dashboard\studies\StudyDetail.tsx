import { useState } from "react";
import { use<PERSON>ara<PERSON>, useNavigate } from "react-router-dom";
import { Trash2, ChevronLeft } from "lucide-react";
import {
  <PERSON><PERSON><PERSON>,
  Pie,
  Cell,
  ResponsiveContainer,
  Legend,
  Tooltip,
} from "recharts";
import Preloader from "@/components/common/Preloader";
import Wrapper from "@/components/common/Wrapper";
import LightFooter from "@/shared/LightFooter";
import { usePatientsQuery } from "@/hooks/patient.query";
import {
  useStudyQuery,
  useCreateVisitMutation,
  useCreateVisitTemplateMutation,
  useUpdateVisitMutation,
  useUpdateStudyMutation,
  usePatientsPerStatusQuery,
} from "@/hooks/study.query";
import type { Patient } from "@/services/api/types";
import type { Visit as ApiVisit, Visit } from "@/store/scheduleEventState";
import VisitModal from "@/components/modal/VisitModal";
import UnscheduledVisitModal from "@/components/modal/UnscheduledVisitModal";
import "./study-detail.css";

// Define interfaces for our data structures
interface LocalVisit {
  uuid: string;
  name: string;
  study_uuid: string;
  study: number;
  activities: string[];
  number: number;
  patient: number;
  date: string;
  status?:
    | "Accepted to participate"
    | "Contacted"
    | "PIS Sent"
    | "Unable to contact"
    | "To be called by doctor"
    | "Decline participation"
    | "Booked for screening"
    | "Not eligible"
    | "Screen Failure"
    | "Enrolled in study"
    | "Withdrawn"
    | "Completed"
    | "Other";
  isUnscheduled?: boolean;
  referredBy?: string;
  referredDateTime?: string;
  commentDateTime?: string;
  comments?: string;
}

// Helper function to convert between local status and API status
const mapLocalStatusToApiStatus = (
  localStatus?:
    | "Accepted to participate"
    | "Contacted"
    | "PIS Sent"
    | "Unable to contact"
    | "To be called by doctor"
    | "Decline participation"
    | "Booked for screening"
    | "Screen Failure"
    | "Enrolled in study"
    | "Withdrawn"
    | "Other"
    | "Completed"
    | "Not eligible"
): "planned" | "completed" | "on-going" | "cancelled" | undefined => {
  switch (localStatus) {
    case "Accepted to participate":
    case "Contacted":
    case "PIS Sent":
    case "To be called by doctor":
    case "Booked for screening":
      return "planned";
    case "Enrolled in study":
      return "completed";
    case "Unable to contact":
    case "Decline participation":
    case "Screen Failure":
    case "Withdrawn":
      return "cancelled";
    case "Completed":
      return "completed";
    case "Not eligible":
      return "cancelled";
    case "Other":
      return "on-going";
    default:
      return undefined;
  }
};

// Helper function to convert local visit to API visit
const mapLocalVisitToApiVisit = (
  localVisit: Partial<LocalVisit>
): Partial<ApiVisit> => {
  // Create a new object without the status property
  const { status, ...rest } = localVisit;

  // Create the API visit object with the mapped status
  const apiVisit: Partial<ApiVisit> = {
    ...rest,
    status: status ? mapLocalStatusToApiStatus(status) : undefined,
  };

  return apiVisit;
};

export default function StudyDetail() {
  const { studyId } = useParams<{ studyId: string }>();
  const navigate = useNavigate();
  // Get current user for patient ID
  const { data: study, isLoading: isLoadingStudy } = useStudyQuery(
    studyId || ""
  );
  const { data: patients, isLoading: isLoadingPatients } = usePatientsQuery();
  const { data: patientStatusData } = usePatientsPerStatusQuery(studyId || "");
  console.log(patientStatusData);

  const createVisitMutation = useCreateVisitMutation();
  const createVisitTemplateMutation = useCreateVisitTemplateMutation();
  const updateVisitMutation = useUpdateVisitMutation();
  const updateStudyMutation = useUpdateStudyMutation();

  const [isVisitModalOpen, setIsVisitModalOpen] = useState(false);
  const [isUnscheduledVisitModalOpen, setIsUnscheduledVisitModalOpen] =
    useState(false);
  const [currentVisitData] = useState<Partial<LocalVisit>>({});
  const [isEditingVisit] = useState(false);

  // const [isEnrollModalOpen, setIsEnrollModalOpen] = useState(false);
  // const [selectedPatientId, setSelectedPatientId] = useState("");

  // Filter visits based on search term and filters

  // Handle adding a new visit
  // const handleAddVisit = () => {
  //   setCurrentVisitData({
  //     name: "",
  //     study_uuid: studyId,
  //     study: study?.id,
  //     activities: [],
  //     number: 0,
  //     patient: currentUser?.id ? currentUser.id : 0,
  //     date: new Date().toISOString().split("T")[0],
  //   });
  //   setIsEditingVisit(false);
  //   setIsVisitModalOpen(true);
  // };

  // Handle adding an unscheduled visit

  // Handle editing a visit

  // Handle saving a visit
  const handleSaveVisit = (visitData: Partial<LocalVisit>) => {
    // Convert local visit to API visit
    const apiVisitData = mapLocalVisitToApiVisit(visitData);

    if (isEditingVisit && visitData.uuid) {
      // Update existing visit template
      updateVisitMutation.mutate({
        uuid: visitData.uuid,
        data: apiVisitData,
      });
    } else {
      // Create a new visit template using the dedicated template mutation
      createVisitTemplateMutation.mutate(apiVisitData);
    }
    setIsVisitModalOpen(false);
  };

  // Handle saving an unscheduled visit
  const handleSaveUnscheduledVisit = (visitData: Partial<Visit>) => {
    createVisitMutation.mutate(visitData);
    setIsUnscheduledVisitModalOpen(false);
  };

  // Handle deleting a visit

  // Handle changing visit status

  // Handle enrolling a patient
  // const handleEnrollPatient = () => {
  //   if (!selectedPatientId || !study) return;

  //   // Check if patient is already enrolled
  //   const isAlreadyEnrolled =
  //     study.patients_nhs_number?.includes(selectedPatientId);

  //   if (isAlreadyEnrolled) {
  //     alert("This patient is already enrolled in this study.");
  //     return;
  //   }

  //   // Add patient to study
  //   const updatedPatients = [
  //     ...(study.patients_nhs_number || []),
  //     selectedPatientId,
  //   ];

  //   updateStudyMutation.mutate({
  //     uuid: study.uuid,
  //     data: {
  //       ...study,
  //       patients_nhs_number: updatedPatients,
  //     },
  //   });

  //   setIsEnrollModalOpen(false);
  //   setSelectedPatientId("");
  // };

  // Handle removing a patient
  const handleRemovePatient = (nhsNumber: string) => {
    if (!study) return;

    if (
      confirm("Are you sure you want to remove this patient from the study?")
    ) {
      const updatedPatients = (study.patients_nhs_number || []).filter(
        (id: string) => id !== nhsNumber
      );

      updateStudyMutation.mutate({
        uuid: study.uuid,
        data: {
          ...study,
          patients_nhs_number: updatedPatients,
        },
      });
    }
  };

  // Get patient name by NHS number
  const getPatientName = (nhsNumber: string) => {
    const patient = patients?.find((p: Patient) => p.nhs_number === nhsNumber);
    return patient
      ? `${patient.first_name} ${patient.last_name}`
      : "Unknown Patient";
  };

  // Get patient name by ID

  // Get status badge class

  return (
    <>
      <Wrapper>
        <Preloader />

        <div
          className="content-wrapper js-content-wrapper overflow-hidden"
          style={{ paddingBottom: "88px" }}
        >
          <div className="dashboard__content bg-light-4">
            <div className="row">
              <div className="col-12">
                <div className="study-detail-container">
                  <div className="study-detail-header">
                    <h1>{isLoadingStudy ? "Loading study..." : study?.name}</h1>
                    <div className="study-detail-actions">
                      <button
                        className="back-btn"
                        onClick={() => navigate("/org/dashboard/studies")}
                      >
                        <ChevronLeft size={16} /> Back to Studies
                      </button>
                    </div>
                  </div>
                  <div className="row">
                    {isLoadingStudy ? (
                      <div className="loading-studies">
                        Loading study details...
                      </div>
                    ) : (
                      <div className="study-detail-content">
                        <div className="study-info-container">
                          <h2>Study Information</h2>
                          <div className="study-info">
                            <div className="info-item">
                              <span className="info-label">Study</span>
                              <span className="info-value">{study?.name}</span>
                            </div>
                            {study?.full_title && (
                              <div className="info-item">
                                <span className="info-label">Full Title</span>
                                <span className="info-value">{study.full_title}</span>
                              </div>
                            )}
                            <div className="info-item">
                              <span className="info-label">Created By</span>
                              <span className="info-value">
                                {study?.user?.first_name || "Unknown"}
                              </span>
                            </div>
                            {study?.team_email && (
                              <div className="info-item">
                                <span className="info-label">Team Email</span>
                                <span className="info-value">{study.team_email}</span>
                              </div>
                            )}
                            {study?.iras && (
                              <div className="info-item">
                                <span className="info-label">IRAS Number</span>
                                <span className="info-value">{study.iras}</span>
                              </div>
                            )}
                            {study?.sponsor && (
                              <div className="info-item">
                                <span className="info-label">Sponsor</span>
                                <span className="info-value">
                                  {`${study.sponsor.first_name} ${study.sponsor.last_name}`}
                                </span>
                              </div>
                            )}
                            {study?.departments && study.departments.length > 0 && (
                              <div className="info-item">
                                <span className="info-label">Departments</span>
                                <div className="departments-list">
                                  {study.departments.map((dept: { uuid: string; name: string; hospital_name: string }) => (
                                    <div key={dept.uuid} className="department-item">
                                      <span className="department-name">{dept.name}</span>
                                      <span className="hospital-name">{dept.hospital_name}</span>
                                    </div>
                                  ))}
                                </div>
                              </div>
                            )}
                            <div className="info-item">
                              <span className="info-label">Description</span>
                              {study?.description ? (
                                <div className="study-description">
                                  {study.description}
                                </div>
                              ) : (
                                <span className="info-value">
                                  No description provided
                                </span>
                              )}
                            </div>
                            <div>
                              {isLoadingPatients ? (
                                <div className="loading-patients">
                                  Loading patients...
                                </div>
                              ) : study?.patients_nhs_number &&
                                study.patients_nhs_number.length > 0 ? (
                                <div className="patients-list">
                                  {study.patients_nhs_number.map(
                                    (nhsNumber: string) => (
                                      <div
                                        key={nhsNumber}
                                        className="patient-item"
                                      >
                                        <div className="patient-info">
                                          <div className="patient-name">
                                            {getPatientName(nhsNumber)}
                                          </div>
                                          <div className="patient-id">
                                            NHS: {nhsNumber}
                                          </div>
                                        </div>
                                        <div className="patient-actions">
                                          <button
                                            className="remove-patient-btn"
                                            onClick={() =>
                                              handleRemovePatient(nhsNumber)
                                            }
                                          >
                                            <Trash2 size={16} />
                                          </button>
                                        </div>
                                      </div>
                                    )
                                  )}
                                </div>
                              ) : (
                                <div className="no-patients">
                                  No patients enrolled yet
                                </div>
                              )}
                            </div>
                          </div>
                        </div>
                      </div>
                    )}
                    <div className="patient-status-chart">
                      <h3>Patient Status Overview</h3>
                      <div className="chart-container">
                        <ResponsiveContainer width="100%" height={250}>
                          <PieChart>
                            <Pie
                              data={patientStatusData}
                              cx="50%"
                              cy="50%"
                              labelLine={true}
                              outerRadius={90}
                              innerRadius={30}
                              fill="#8884d8"
                              dataKey="value"
                              paddingAngle={2}
                            >
                              <Cell fill="var(--color-purple-1)" />
                              <Cell fill="var(--color-green-4)" />
                              <Cell fill="var(--color-orange-4)" />
                            </Pie>
                            <Tooltip
                              formatter={(value) => [
                                `${value} patients`,
                                "Count",
                              ]}
                              contentStyle={{
                                backgroundColor: "white",
                                border: "1px solid var(--color-light-2)",
                                borderRadius: "8px",
                                padding: "10px",
                                boxShadow: "0 2px 8px rgba(0, 0, 0, 0.1)",
                              }}
                              itemStyle={{
                                color: "var(--color-dark-1)",
                                fontSize: "14px",
                              }}
                              labelStyle={{
                                color: "var(--color-dark-1)",
                                fontWeight: "600",
                                marginBottom: "5px",
                              }}
                            />
                            <Legend
                              layout="horizontal"
                              verticalAlign="bottom"
                              align="center"
                              wrapperStyle={{
                                paddingTop: "10px",
                              }}
                              formatter={(value) => (
                                <span
                                  style={{
                                    color: "var(--color-dark-1)",
                                    fontSize: "14px",
                                    fontWeight: "500",
                                  }}
                                >
                                  {value}
                                </span>
                              )}
                            />
                          </PieChart>
                        </ResponsiveContainer>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <LightFooter />
        </div>
      </Wrapper>

      {/* Visit Modal - Using the VisitModal component */}
      {isVisitModalOpen && (
        <VisitModal
          isOpen={isVisitModalOpen}
          onClose={() => setIsVisitModalOpen(false)}
          onSave={(visitData) => {
            // Convert from Visit to LocalVisit format
            const localVisitData = visitData as unknown as Partial<LocalVisit>;
            handleSaveVisit(localVisitData);
          }}
          initialData={currentVisitData as unknown as Partial<Visit>}
          availableTests={[]}
          isEditing={isEditingVisit}
        />
      )}

      {/* Unscheduled Visit Modal */}
      {isUnscheduledVisitModalOpen && (
        <UnscheduledVisitModal
          isOpen={isUnscheduledVisitModalOpen}
          onClose={() => setIsUnscheduledVisitModalOpen(false)}
          onSave={handleSaveUnscheduledVisit}
          availableTests={[]}
          studyId={studyId || ""}
        />
      )}

      {/* Enroll Patient Modal */}
      {/* {isEnrollModalOpen && (
        <div className="visit-modal-overlay">
          <div className="visit-modal">
            <div className="visit-modal-header">
              <h2 className="visit-modal-title">Enroll Patient</h2>
              <button
                className="visit-modal-close"
                onClick={() => setIsEnrollModalOpen(false)}
              >
                <X size={20} />
              </button>
            </div>
            <div className="visit-modal-body">
              <form
                onSubmit={(e) => {
                  e.preventDefault();
                  handleEnrollPatient();
                }}
              >
                <div className="form-group">
                  <label htmlFor="patientSelect">Select Patient</label>
                  <select
                    id="patientSelect"
                    value={selectedPatientId}
                    onChange={(e) => setSelectedPatientId(e.target.value)}
                    required
                  >
                    <option value="">Select a patient</option>
                    {patients?.map((patient: Patient) => (
                      <option
                        key={patient.nhs_number}
                        value={patient.nhs_number}
                      >
                        {patient.first_name} {patient.last_name} (NHS:{" "}
                        {patient.nhs_number})
                      </option>
                    ))}
                  </select>
                </div>

                <div className="form-actions">
                  <button
                    type="button"
                    className="cancel-btn"
                    onClick={() => setIsEnrollModalOpen(false)}
                  >
                    <X size={16} /> Cancel
                  </button>
                  <button
                    type="submit"
                    className="save-btn"
                    disabled={!selectedPatientId}
                  >
                    <Users size={16} /> Enroll Patient
                  </button>
                </div>
              </form>
            </div>
          </div>
        </div>
      )} */}
    </>
  );
}

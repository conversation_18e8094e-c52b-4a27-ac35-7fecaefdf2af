import { useNavigate } from "react-router-dom";
import { useState, useRef } from "react";
import Preloader from "@/components/common/Preloader";
import NurtifyModal from "@/components/NurtifyModal";
import useSelectedPatientStore from "@/store/SelectedPatientState";
import {
  usePatientMedicalHistoryQuery,
  useCreateMedicalHistoryMutation,
  useUpdateMedicalHistoryMutation,
  useEditMedicalHistoryConditionMutation,
  useDeleteMedicalHistoryConditionMutation,
  usePatientAllergiesQuery,
  useUpdatePatientProfilePictureMutation
} from "@/hooks/patient.query";
import { usePatientWarnings } from "@/hooks/usePatientWarnings";
import type { MedicalHistoryConditionCreate, MedicalHistoryCondition, PatientWarning } from "@/services/api/types";
import { AlertTriangle, Edit, Camera, User } from "lucide-react";
import "./patient-details.css";
import React from "react";

export default function PatientDetails() {
  const navigate = useNavigate();
  const { selectedPatient, isLoading, error, clearSelectedPatient } = useSelectedPatientStore();
  const { data: medicalHistory, isLoading: isMedicalHistoryLoading } = usePatientMedicalHistoryQuery(selectedPatient?.uuid || "");

  // Patient Allergies functionality
  const { data: patientAllergies, isLoading: isAllergiesLoading } = usePatientAllergiesQuery(selectedPatient?.nhs_number || "");

  // Patient Warnings functionality
  const {
    warnings,
    isLoading: isWarningsLoading
  } = usePatientWarnings(selectedPatient?.uuid || "");

  const createMedicalHistoryMutation = useCreateMedicalHistoryMutation(selectedPatient?.uuid || "");

  const existingMedicalHistoryUuid = medicalHistory?.results?.[0]?.uuid;

  const updateMedicalHistoryMutation = useUpdateMedicalHistoryMutation(
    selectedPatient?.uuid || "",
    existingMedicalHistoryUuid || ""
  );

  const [isAddingRecord, setIsAddingRecord] = useState(false);
  const [editingCondition, setEditingCondition] = useState<{
    uuid: string;
    condition: string;
    start_date: string;
    status: 'current' | 'resolved';
    resolved_date?: string;
  } | null>(null);

  const [deleteConditionUuid, setDeleteConditionUuid] = useState<string>("");

  const editConditionMutation = useEditMedicalHistoryConditionMutation(
    selectedPatient?.uuid || "",
    existingMedicalHistoryUuid || "",
    editingCondition?.uuid || ""
  );

  const deleteConditionMutation = useDeleteMedicalHistoryConditionMutation(
    selectedPatient?.uuid || "",
    existingMedicalHistoryUuid || "",
    deleteConditionUuid
  );

  // Profile Picture functionality
  const fileInputRef = useRef<HTMLInputElement>(null);
  const updateProfilePictureMutation = useUpdatePatientProfilePictureMutation(selectedPatient?.uuid || "");

  const [newCondition, setNewCondition] = useState<MedicalHistoryConditionCreate>({
    condition: "",
    start_date: "",
    status: "current",
    resolved_date: ""
  });

  const handleEditCondition = (condition: MedicalHistoryCondition) => {
    setEditingCondition({
      uuid: condition.uuid,
      condition: condition.condition,
      start_date: condition.start_date,
      status: condition.status === 'active' ? 'current' : 'resolved',
      resolved_date: condition.resolved_date
    });
  };

  const handleCancelEdit = () => {
    setEditingCondition(null);
  };

  const handleSaveEdit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!editingCondition || !selectedPatient?.uuid || !existingMedicalHistoryUuid) return;

    try {
      const conditionData = {
        condition: editingCondition.condition,
        start_date: editingCondition.start_date,
        status: editingCondition.status,
        ...(editingCondition.status === 'resolved' && editingCondition.resolved_date
          ? { resolved_date: editingCondition.resolved_date }
          : {})
      };

      await editConditionMutation.mutateAsync(conditionData);
      setEditingCondition(null);
    } catch (error) {
      console.error("Error updating condition:", error);
    }
  };

  const handleDeleteCondition = async (conditionUuid: string) => {
    if (!selectedPatient?.uuid || !existingMedicalHistoryUuid) return;

    if (window.confirm("Are you sure you want to delete this condition?")) {
      setDeleteConditionUuid(conditionUuid);
      try {
        await deleteConditionMutation.mutateAsync();
        setDeleteConditionUuid("");
      } catch (error) {
        console.error("Error deleting condition:", error);
        setDeleteConditionUuid("");
      }
    }
  };

  // Profile Picture handlers
  const handleProfilePictureClick = () => {
    fileInputRef.current?.click();
  };

  const handleProfilePictureChange = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file || !selectedPatient?.uuid) return;

    // Validate file type
    if (!file.type.startsWith('image/')) {
      alert('Please select an image file');
      return;
    }

    // Validate file size (max 5MB)
    if (file.size > 5 * 1024 * 1024) {
      alert('Image size must be less than 5MB');
      return;
    }

    try {
      const result = await updateProfilePictureMutation.mutateAsync(file);
      // Update the store with the new profile picture data
      clearSelectedPatient();
      if (selectedPatient) {
        const updatedPatient = {
          ...selectedPatient,
          profile_picture: result.profile_picture,
          profile_picture_url: result.profile_picture_url
        };
        useSelectedPatientStore.getState().setSelectedPatient(updatedPatient);
      }
      // Clear the input
      if (fileInputRef.current) {
        fileInputRef.current.value = '';
      }
    } catch (error) {
      console.error("Error updating profile picture:", error);
      alert('Failed to update profile picture. Please try again.');
    }
  };

  const handleAddRecord = () => {
    setIsAddingRecord(true);
  };

  const handleCancel = () => {
    setIsAddingRecord(false);
    setNewCondition({
      condition: "",
      start_date: "",
      status: "current",
      resolved_date: ""
    });
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    try {
      const conditionData = {
        condition: newCondition.condition,
        start_date: newCondition.start_date,
        status: newCondition.status,
        ...(newCondition.status === 'resolved' && newCondition.resolved_date
          ? { resolved_date: newCondition.resolved_date }
          : {})
      };

      // If there's an existing record, use PATCH, otherwise use POST
      if (existingMedicalHistoryUuid) {
        console.log('Updating existing record with UUID:', existingMedicalHistoryUuid);
        await updateMedicalHistoryMutation.mutateAsync({
          conditions_data: [conditionData]
        });
      } else {
        console.log('Creating new record');
        await createMedicalHistoryMutation.mutateAsync({
          conditions_data: [conditionData]
        });
      }

      setIsAddingRecord(false);
      setNewCondition({
        condition: "",
        start_date: "",
        status: "current",
        resolved_date: ""
      });
    } catch (error) {
      console.error("Error creating/updating medical history:", error);
      // You might want to show an error message to the user here
    }
  };

  const renderMedicalHistorySection = () => {
    if (isMedicalHistoryLoading) {
      return <Preloader />;
    }

    if (!medicalHistory?.results || medicalHistory.results.length === 0) {
      return (
        <div className="no-medical-history">
          <p>No medical history records found for this patient.</p>
        </div>
      );
    }

    return (
      <div className="medical-history-list">
        {medicalHistory.results.map((history) => (
          <div key={history.uuid} className="medical-history-item mb-4 p-4 border rounded">
            {history.conditions.length > 0 ? (
              <div className="conditions-list">
                {history.conditions.map((condition) => (
                  <div key={condition.uuid} className="condition-item mb-3">
                    {editingCondition?.uuid === condition.uuid ? (
                      <form onSubmit={handleSaveEdit} className="edit-form">
                        <div className="row g-3">
                          <div className="col-md-4">
                            <div className="form-group">
                              <label htmlFor="edit-condition">Condition</label>
                              <input
                                type="text"
                                className="form-control"
                                id="edit-condition"
                                value={editingCondition.condition}
                                onChange={(e) => setEditingCondition(prev => ({ ...prev!, condition: e.target.value }))}
                                required
                              />
                            </div>
                          </div>
                          <div className="col-md-4">
                            <div className="form-group">
                              <label htmlFor="edit-start-date">Start Date</label>
                              <input
                                type="date"
                                className="form-control"
                                id="edit-start-date"
                                value={editingCondition.start_date}
                                onChange={(e) => setEditingCondition(prev => ({ ...prev!, start_date: e.target.value }))}
                                required
                              />
                            </div>
                          </div>
                          <div className="col-md-4">
                            <div className="form-group">
                              <label htmlFor="edit-status">Status</label>
                              <select
                                className="form-control"
                                id="edit-status"
                                value={editingCondition.status}
                                onChange={(e) => setEditingCondition(prev => ({
                                  ...prev!,
                                  status: e.target.value as 'current' | 'resolved',
                                  resolved_date: e.target.value === 'current' ? '' : prev?.resolved_date
                                }))}
                                required
                              >
                                <option value="current">Current</option>
                                <option value="resolved">Resolved</option>
                              </select>
                            </div>
                          </div>
                          {editingCondition.status === 'resolved' && (
                            <div className="col-md-4">
                              <div className="form-group">
                                <label htmlFor="edit-resolved-date">Resolved Date</label>
                                <input
                                  type="date"
                                  className="form-control"
                                  id="edit-resolved-date"
                                  value={editingCondition.resolved_date || ''}
                                  onChange={(e) => setEditingCondition(prev => ({ ...prev!, resolved_date: e.target.value }))}
                                  required={editingCondition.status === 'resolved'}
                                />
                              </div>
                            </div>
                          )}
                        </div>
                        <div className="mt-3">
                          <button type="submit" className="button -md btn-nurtify text-white me-2">
                            Save Changes
                          </button>
                          <button type="button" className="button -md btn-nurtify-lighter" onClick={handleCancelEdit}>
                            Cancel
                          </button>
                        </div>
                      </form>
                    ) : (
                      <>
                        <div className="row">
                          <div className="col-md-6">
                            <div className="medical-history-info">
                              <h3 className="condition-title">{condition.condition}</h3>
                              <p className="diagnosis-date">
                                Started: {new Date(condition.start_date).toLocaleDateString()}
                              </p>
                            </div>
                          </div>
                          <div className="col-md-6">
                            <div className="medical-history-details">
                              <p>
                                <strong>Status:</strong>{' '}
                                <span className={`status-badge status-${condition.status.toLowerCase()}`}>
                                  {condition.status}
                                </span>
                              </p>
                              {condition.status === 'resolved' && condition.resolved_date && (
                                <p>
                                  <strong>Resolved:</strong>{' '}
                                  {new Date(condition.resolved_date).toLocaleDateString()}
                                </p>
                              )}
                            </div>
                          </div>
                        </div>
                        <div className="condition-actions mt-3">
                          <button
                            className="button -sm btn-nurtify-lighter me-2"
                            onClick={() => handleEditCondition(condition)}
                          >
                            Edit
                          </button>
                          <button
                            className="button -sm btn-danger"
                            onClick={() => handleDeleteCondition(condition.uuid)}
                          >
                            Delete
                          </button>
                        </div>
                      </>
                    )}
                  </div>
                ))}
              </div>
            ) : (
              <div className="no-conditions">
                <p>No conditions recorded for this medical history entry.</p>
              </div>
            )}
            <div className="medical-history-meta mt-3 pt-3 border-top">
              <small className="text-muted">
                Last updated: {new Date(history.updated_at).toLocaleString()}
              </small>
            </div>
          </div>
        ))}
      </div>
    );
  };

  if (isLoading) {
    return <Preloader />;
  }

  if (error || !selectedPatient) {
    return <div>Error loading patient details</div>;
  }

  const patient = selectedPatient;

  // Get active warnings for summary
   
  const activeWarnings = warnings.filter((warning: any) => warning.is_active);

  return (
    <div className="content-wrapper js-content-wrapper">
      <div className="dashboard__content bg-light-4">
        <div className="container-fluid py-6 px-6">
          <div className="patient-details-container">
            <div className="patient-details-header">
              <h1 className="page-title">Patient Details</h1>
              <div className="patient-actions">
                <button
                  className="button -md btn-nurtify-lighter"
                  onClick={() => {clearSelectedPatient();navigate('/patients')}}
                >
                  Patient List
                </button>
                <button
                  className="button -md btn-nurtify text-white"
                  onClick={() => navigate(`/org/dashboard/patient-board/edit-patient`)}
                >
                  Update Patient
                </button>
              </div>
            </div>

            {/* Patient Profile Picture */}
            <div className="patient-profile-picture-section">
              <div className="profile-picture-container">
                <div 
                  className="profile-picture-wrapper"
                  onClick={handleProfilePictureClick}
                  title="Click to update profile picture"
                >
                  {patient.profile_picture_url ? (
                    <img 
                      src={(() => {
                        if (patient.profile_picture_url.startsWith('http')) {
                          return patient.profile_picture_url;
                        }
                        // Handle relative URLs
                        const baseUrl = import.meta.env.VITE_API_URL || 'http://localhost:8000';
                        // Remove leading slash if present to avoid double slashes
                        const cleanUrl = patient.profile_picture_url.startsWith('/') 
                          ? patient.profile_picture_url.substring(1) 
                          : patient.profile_picture_url;
                        return `${baseUrl}/${cleanUrl}`;
                      })()}
                      alt={`${patient.first_name} ${patient.last_name}`}
                      className="profile-picture"
                      onError={(e) => {
                        console.error('Failed to load profile picture:', patient.profile_picture_url);
                        // Fallback to default avatar if image fails to load
                        (e.target as HTMLImageElement).style.display = 'none';
                        (e.target as HTMLImageElement).nextElementSibling?.classList.remove('hidden');
                      }}
                      onLoad={() => {
                        console.log('Profile picture loaded successfully:', patient.profile_picture_url);
                      }}
                    />
                  ) : null}
                  <div className={`profile-picture-placeholder ${patient.profile_picture_url ? 'hidden' : ''}`}>
                    <User size={48} />
                  </div>
                  <div className="profile-picture-overlay">
                    <Camera size={24} />
                    <span>Update Photo</span>
                  </div>
                </div>
                <input
                  ref={fileInputRef}
                  type="file"
                  accept="image/*"
                  onChange={handleProfilePictureChange}
                  style={{ display: 'none' }}
                />
                {updateProfilePictureMutation.isPending && (
                  <div className="profile-picture-loading">
                    <div className="loading-spinner"></div>
                    <span>Updating...</span>
                  </div>
                )}
              </div>
              {/* Debug info - remove in production */}
              {import.meta.env.DEV && (
                <div style={{ fontSize: '12px', color: '#666', marginTop: '8px', textAlign: 'center' }}>
                  Click on the image above to update the patient's profile picture
                </div>
              )}
            </div>

            {/* Patient Allergies and Warnings Summary Cards */}
            <div className="patient-summary-cards">
              <div className="row g-3">
                {/* Allergies Summary Card */}
                <div className="col-md-6">
                  <div className="summary-card allergies-card">
                    <div className="summary-card-header">
                      <h3 className="summary-card-title">
                        <AlertTriangle size={20} />
                        Allergies
                      </h3>
                      <button
                        className="summary-card-edit-btn"
                        onClick={() => navigate(`/org/dashboard/patient-board/edit-patient`)}
                        title="Edit Allergies"
                      >
                        <Edit size={16} />
                      </button>
                    </div>
                    <div className="summary-card-content">
                      {isAllergiesLoading ? (
                        <div className="summary-loading">Loading...</div>
                      ) : patientAllergies && patientAllergies.length > 0 ? (
                        <ul className="summary-list">
                          {patientAllergies.map((allergy, index) => (
                            <li key={allergy.uuid || index} className="summary-list-item">
                              {allergy.name}
                            </li>
                          ))}
                        </ul>
                      ) : (
                        <div className="summary-empty">No known allergies</div>
                      )}
                    </div>
                  </div>
                </div>

                {/* Active Warnings Summary Card */}
                <div className="col-md-6">
                  <div className="summary-card warnings-card">
                    <div className="summary-card-header">
                      <h3 className="summary-card-title">
                        <AlertTriangle size={20} />
                        Active Warnings
                      </h3>
                      <button
                        className="summary-card-edit-btn"
                        onClick={() => navigate(`/org/dashboard/patient-board/edit-patient`)}
                        title="Edit Warnings"
                      >
                        <Edit size={16} />
                      </button>
                    </div>
                    <div className="summary-card-content">
                      {isWarningsLoading ? (
                        <div className="summary-loading">Loading...</div>
                      ) : activeWarnings.length > 0 ? (
                        <ul className="summary-list">
                          {activeWarnings.map((warning: PatientWarning) => (
                            <li
                              key={warning.uuid}
                              className="summary-list-item"
                              title={`Severity: ${warning.severity_display}\n${warning.description}`}
                            >
                              {warning.description}
                            </li>
                          ))}
                        </ul>
                      ) : (
                        <div className="summary-empty">No active warnings</div>
                      )}
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* Patient Details Card */}
            <div className="patient-details-card">
              <div className="row g-5">
                {/* Left Column */}
                <div className="col-md-6">
                  <div className="patient-info-section">
                    {/* First Name */}
                    <div className="patient-info-item">
                      <span className="info-label">First Name</span>
                      <div className="info-value">{patient.first_name}</div>
                    </div>

                    {/* Last Name */}
                    <div className="patient-info-item">
                      <span className="info-label">Last Name</span>
                      <div className="info-value">{patient.last_name}</div>
                    </div>

                    {/* NHS Number */}
                    <div className="patient-info-item">
                      <span className="info-label">NHS Number</span>
                      <div className="info-value">{patient.nhs_number}</div>
                    </div>

                    {/* Medical Record Number */}
                    <div className="patient-info-item">
                      <span className="info-label">Medical Record Number</span>
                      <div className="info-value">{patient.medical_record_number || "N/A"}</div>
                    </div>

                    <div className="patient-info-item">
                      <span className="info-label">Ethnic Background</span>
                      <div className="info-value">{patient.ethnic_background || "N/A"}</div>
                    </div>
                  </div>
                </div>

                {/* Right Column */}
                <div className="col-md-6">
                  <div className="patient-info-section">
                    {/* Date of Birth */}
                    <div className="patient-info-item">
                      <span className="info-label">Date of Birth</span>
                      <div className="info-value">{patient.date_of_birth}</div>
                    </div>

                    {/* Gender */}
                    <div className="patient-info-item">
                      <span className="info-label">Gender</span>
                      <div className="info-value">
                        {patient.gender === "Male" ? "Male" :
                         patient.gender === "Female" ? "Female" :
                         patient.gender === "Prefer_not_to_disclose" ? "Prefer not to disclose" :
                         patient.gender === "M" ? "Male" :
                         patient.gender === "F" ? "Female" :
                         patient.gender === "O" ? "Other" : "Undisclosed"}
                      </div>
                    </div>

                    {/* Email */}
                    <div className="patient-info-item">
                      <span className="info-label">Email</span>
                      <div className="info-value">{patient.email || "N/A"}</div>
                    </div>

                    {/* Phone */}
                    <div className="patient-info-item">
                      <span className="info-label">Phone</span>
                      <div className="info-value">{patient.phone_number || "N/A"}</div>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* Medical History Section */}
            <div className="patient-details-card mt-4">
              <div className="d-flex justify-content-between align-items-center mb-4">
                <h2 className="section-title mb-0">Medical History</h2>
                <button
                  className="button -md btn-nurtify text-white"
                  onClick={handleAddRecord}
                >
                  Add Record
                </button>
              </div>
              {renderMedicalHistorySection()}
            </div>
          </div>
        </div>
      </div>

      {/* Add Medical History Modal */}
      <NurtifyModal
        isOpen={isAddingRecord}
        onClose={handleCancel}
        title="Add New Medical Condition"
        size="lg"
      >
        <form onSubmit={handleSubmit} className="medical-history-modal-form">
          <div className="row g-4">
            <div className="col-md-6">
              <div className="form-group">
                <label htmlFor="modal-condition" className="form-label">
                  Condition <span className="text-danger">*</span>
                </label>
                <input
                  type="text"
                  className="form-control"
                  id="modal-condition"
                  value={newCondition.condition}
                  onChange={(e) => setNewCondition(prev => ({ ...prev, condition: e.target.value }))}
                  placeholder="Enter medical condition"
                  required
                />
              </div>
            </div>
            <div className="col-md-6">
              <div className="form-group">
                <label htmlFor="modal-start-date" className="form-label">
                  Start Date <span className="text-danger">*</span>
                </label>
                <input
                  type="date"
                  className="form-control"
                  id="modal-start-date"
                  value={newCondition.start_date}
                  onChange={(e) => setNewCondition(prev => ({ ...prev, start_date: e.target.value }))}
                  required
                />
              </div>
            </div>
            <div className="col-md-6">
              <div className="form-group">
                <label htmlFor="modal-status" className="form-label">
                  Status <span className="text-danger">*</span>
                </label>
                <select
                  className="form-control"
                  id="modal-status"
                  value={newCondition.status}
                  onChange={(e) => {
                    const newStatus = e.target.value as 'current' | 'resolved';
                    setNewCondition(prev => ({
                      ...prev,
                      status: newStatus,
                      resolved_date: newStatus === 'current' ? '' : prev.resolved_date
                    }));
                  }}
                  required
                >
                  <option value="current">Current</option>
                  <option value="resolved">Resolved</option>
                </select>
              </div>
            </div>
            {newCondition.status === 'resolved' && (
              <div className="col-md-6">
                <div className="form-group">
                  <label htmlFor="modal-resolved-date" className="form-label">
                    Resolved Date <span className="text-danger">*</span>
                  </label>
                  <input
                    type="date"
                    className="form-control"
                    id="modal-resolved-date"
                    value={newCondition.resolved_date}
                    onChange={(e) => setNewCondition(prev => ({ ...prev, resolved_date: e.target.value }))}
                    required={newCondition.status === 'resolved'}
                  />
                </div>
              </div>
            )}
          </div>

          <div className="modal-form-actions mt-4 pt-3 border-top d-flex justify-content-end gap-3">
            <button
              type="button"
              className="button -md btn-nurtify-lighter"
              onClick={handleCancel}
            >
              Cancel
            </button>
            <button
              type="submit"
              className="button -md btn-nurtify text-white"
              disabled={createMedicalHistoryMutation.isPending || updateMedicalHistoryMutation.isPending}
            >
              {(createMedicalHistoryMutation.isPending || updateMedicalHistoryMutation.isPending) ? "Saving..." : "Save Condition"}
            </button>
          </div>
        </form>
      </NurtifyModal>
    </div>
  );
}

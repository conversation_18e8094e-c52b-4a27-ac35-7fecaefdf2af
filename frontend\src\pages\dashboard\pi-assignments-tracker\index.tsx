import Preloader from "@/components/common/Preloader";
import Wrapper from "@/components/common/Wrapper";
import PIAssignmentsTracker from "@/components/PIAssignmentsTracker";
import { useCurrentUserQuery } from "@/hooks/user.query";

export default function PIAssignmentsTrackerPage(): JSX.Element {
  const { data: currentUser } = useCurrentUserQuery();
  return (
    <>
      <Wrapper>
        <Preloader />
        <div className="content-wrapper js-content-wrapper overflow-hidden" style={{paddingBottom: "88px"}}>
          <div className="dashboard__content bg-light-4">
            <div className="row y-gap-20">
              <div className="col-12">
                <PIAssignmentsTracker departmentUuid={currentUser?.department?.uuid} />
              </div>
            </div>
          </div>
        </div>
      </Wrapper>
    </>
  );
} 
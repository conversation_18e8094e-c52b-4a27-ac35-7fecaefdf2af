/* Sponsor Orgs List Page Styles */
.sponsor-orgs-container {
    background-color: #ffffff;
    border-radius: 0; /* Changed */
    box-shadow: none; /* Changed */
    padding: 15px 30px 30px 30px; /* Changed top padding */
    margin-bottom: 30px;
    width: 100%;
}

.sponsor-orgs-header {
    display: flex;
    flex-direction: column;
    margin-bottom: 30px;
    padding-bottom: 15px;
    border-bottom: 1px solid #e5e7eb;
}

.sponsor-orgs-page-title { /* Renamed for clarity to distinguish from table title */
    margin-bottom: 10px;
}

.sponsor-orgs-page-title h1 {
    font-size: 24px;
    font-weight: 600;
    color: #1a1a1a;
    display: flex;
    align-items: center;
    gap: 10px;
    margin: 0;
}

.sponsor-orgs-subtitle {
    color: #4F547B;
    font-size: 16px;
    font-weight: 400;
    margin-top: 5px;
}

.sponsor-orgs-subtitle h6 {
    margin: 0;
    font-weight: 400;
}

.sponsor-orgs-search-container {
    display: flex;
    gap: 20px;
    align-items: center;
    margin-bottom: 30px;
}

.sponsor-orgs-search-box {
    position: relative;
    flex: 1;
    max-width: 550px;
    display: flex;
    align-items: center;
}

.sponsor-orgs-search-box .search-icon {
    position: absolute;
    left: 15px;
    color: #64748b;
    z-index: 1;
}

.sponsor-orgs-search-box .data-table-search {
    width: 100%;
}

.sponsor-orgs-search-box .data-table-search-input {
    width: 100%;
    padding: 12px 15px 12px 45px;
    border: 1px solid #EDEDED !important;
    border-radius: 0; /* Changed */
    outline: none;
    font-size: 14px;
    transition: all 0.3s ease;
    box-shadow: none; /* Changed */
}

.sponsor-orgs-search-box .data-table-search-input:focus {
    border-color: #3EC1C9 !important;
    box-shadow: none; /* Changed */
}

.sponsor-orgs-add-button {
    min-width: 150px;
    height: 48px;
    background-color: #37B7C3;
    color: white !important;
    border: none;
    border-radius: 0; /* Changed */
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    text-decoration: none;
    padding: 0 20px;
    box-shadow: none; /* Changed */
}

.sponsor-orgs-add-button:hover {
    background-color: #2A9A9F;
    transform: none; /* Changed */
    box-shadow: none; /* Changed */
}

.sponsor-orgs-table-container {
    background-color: #ffffff;
    border-radius: 0; /* Changed */
    box-shadow: none; /* Changed */
    overflow: hidden;
    margin-bottom: 30px;
    border: 1px solid #e5e7eb;
    padding: 20px;
}

.sponsor-orgs-table-title {
    font-size: 18px;
    font-weight: 600;
    color: #2B3674;
    margin-bottom: 20px;
}

/* This class seemed specific and potentially redundant or for a different title context.
   If it was for the main page title, .sponsor-orgs-page-title h1 covers it.
   If it was for the table title, .sponsor-orgs-table-title covers it.
   I'll rename it but you might want to verify its usage or consolidate. */
.title-SponsorOrgs {
    font-size: 24px;
    font-weight: 600;
    color: #1a1a1a;
    display: flex;
    align-items: center;
    gap: 10px;
    margin: 0;
    padding-bottom: 15px;
}

/* Responsive Styles */
@media (max-width: 992px) {
    .sponsor-orgs-container {
        padding: 25px;
    }
    
    .sponsor-orgs-search-container {
        flex-direction: column;
        align-items: flex-start;
        gap: 15px;
    }
    
    .sponsor-orgs-search-box {
        width: 100%;
        max-width: none;
    }
    
    .sponsor-orgs-add-button {
        width: 100%;
        justify-content: center;
    }
}

@media (max-width: 768px) {
    .sponsor-orgs-container {
        padding: 20px;
    }
    
    .sponsor-orgs-page-title h1 { /* Adjusted to match renaming */
        font-size: 20px;
    }
    
    .sponsor-orgs-subtitle {
        font-size: 14px;
    }
}

@media (max-width: 576px) {
    .sponsor-orgs-container {
        padding: 15px;
    }
}

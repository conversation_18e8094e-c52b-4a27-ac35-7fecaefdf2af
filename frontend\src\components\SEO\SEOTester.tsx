import React from 'react';
import { useLocation } from 'react-router-dom';
import { getSEOConfig } from '@/config/seoConfig';

const SEOTester: React.FC = () => {
  const location = useLocation();
  
  if (process.env.NODE_ENV !== 'development') {
    return null;
  }

  const protectedRoutes = [
    '/home',
    '/clinical',
    '/patient',
    '/sponsor', 
    '/org/dashboard',
    '/dashboard',
    '/templates',
    '/my-patients',
    '/patients'
  ];

  const isProtectedRoute = protectedRoutes.some(route => 
    location.pathname.startsWith(route)
  );

  const seoConfig = getSEOConfig(location.pathname);

  return (
    <div style={{
      position: 'fixed',
      bottom: '10px',
      right: '10px',
      background: '#000',
      color: '#fff',
      padding: '10px',
      borderRadius: '5px',
      fontSize: '12px',
      maxWidth: '300px',
      zIndex: 9999
    }}>
      <strong>SEO Debug:</strong><br/>
      Route: {location.pathname}<br/>
      Protected: {isProtectedRoute ? 'YES (noindex)' : 'NO (index)'}<br/>
      Title: {seoConfig.title.substring(0, 40)}...<br/>
      Canonical: https://nurtify.co.uk{location.pathname}
    </div>
  );
};

export default SEOTester;

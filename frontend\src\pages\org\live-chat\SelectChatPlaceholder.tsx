import React from 'react';
import './SelectChatPlaceholder.css'; // We'll create this CSS file next
import { MessageSquareText } from 'lucide-react'; // Using an icon

const SelectChatPlaceholder: React.FC = () => {
  return (
    <div className="select-chat-placeholder-container">
      <MessageSquareText size={64} className="placeholder-icon" />
      <h2 className="placeholder-title">Select a Chat</h2>
      <p className="placeholder-text">
        Choose a conversation from the list on the left to start messaging.
      </p>
    </div>
  );
};

export default SelectChatPlaceholder;

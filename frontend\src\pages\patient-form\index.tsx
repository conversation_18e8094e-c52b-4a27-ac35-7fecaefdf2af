import { useState, useEffect } from "react";
import DashboardFormCard from "@/components/DashboardFormCard";
import { 
  useGetFormsByPatient, 
  
  useGetUnsubmittedFormsByPatient, 
  useGetCompletedSubmissionsByPatient, 
  useGetIncompleteSubmissionsByPatient,
  useGetFinalizedSubmissionsByPatient,
  useGetSubmissionByFormAndPatient,
  useCreateSubmission,
  useUpdateSubmission,
  useFinalizeSubmissions,
  useDeleteSubmission,
  useGetSubmissionByUuid
} from "@/hooks/form.query";
import { QueryClient, useQueryClient } from "@tanstack/react-query";
import Preloader from "@/components/common/Preloader";
import useSelectedPatientStore from "@/store/SelectedPatientState";
import { ArrowLeft, AlertCircle, FileText, CheckCircle2, Clock, Plus, Archive, Edit, Trash2 } from "lucide-react";
import RebuildForm, { SavedFormData } from "../form-builder/SurveyFormRenderer";
import { useGetFormByUuid } from "@/hooks/form.query";
import DocumentationSidebar from "@/components/common/DocumentationSidebar";
import FormSubmissionPreview from "./FormSubmissionPreview";
import "./patientfrom.css";
import { useCurrentUserQuery } from "@/hooks/user.query";

// Define the SubmissionData interface to fix type errors
interface SubmissionData {
  formDetails?: {
    name?: string;
    description?: string;
    categories?: string;
    privacy?: string;
    password?: string;
  };
  sections?: Array<{
    id: string | number;
    name?: string;
    description?: string;
    questions?: Array<{
      id: string | number;
      type: string;
      [key: string]: unknown;
    }>;
    [key: string]: unknown;
  }>;
  [key: string]: unknown;
}

export default function PatientForm() {
  const { selectedPatient } = useSelectedPatientStore();
  const patientUuid = selectedPatient?.uuid || "";
  const { data: currentUser } = useCurrentUserQuery();
  
  console.log("Patient UUID in PatientForm:", patientUuid); // Debug log
  
  const [selectedFormUuid, setSelectedFormUuid] = useState<string | null>(null);
  const [processingError, setProcessingError] = useState<string | null>(null);
  const [submissionMode, setSubmissionMode] = useState<"new" | "edit">("new");
  const [currentFormSubmissionUuid, setCurrentFormSubmissionUuid] = useState<string | null>(null);
  const [selectedTab, setSelectedTab] = useState<"draft" | "empty" | "submitted">("empty");
  
  // New state for form selection
  const [selectedFormSubmissions, setSelectedFormSubmissions] = useState<Set<string>>(new Set());
  const [submissionResult, setSubmissionResult] = useState<{success: boolean, message: string} | null>(null);
  
  const [activeTab, setActiveTab] = useState('showAll');
  const [patientsMode, setPatientsMode] = useState(false);
  
  // New state for submission preview
  const [previewSubmission, setPreviewSubmission] = useState<unknown>(null);
  
  // Add new state for tracking changes in submitted forms
  // const [trackedChanges, setTrackedChanges] = useState<Record<string, {
  //   originalAnswers: Record<string, unknown>;
  //   modifiedAnswers: Record<string, unknown>;
  // }>>({});
  
  useGetFormsByPatient(patientUuid);
  
  // Add QueryClient
  const queryClient = useQueryClient();
  
  // Remove the refetch destructuring
  const { data: completedSubmissions, isLoading: isLoadingCompleted } = useGetCompletedSubmissionsByPatient(patientUuid);
  const { data: draftSubmissions, isLoading: isLoadingDrafts } = useGetIncompleteSubmissionsByPatient(patientUuid);
  const { data: emptyForms, isLoading: isLoadingEmptyForms } = useGetUnsubmittedFormsByPatient(patientUuid);
  const { data: submittedForms, isLoading: isLoadingSubmitted } = useGetFinalizedSubmissionsByPatient(patientUuid);
  
  const { data: selectedFormData, isLoading: isLoadingSelectedForm } = useGetFormByUuid(selectedFormUuid || "", {
    enabled: !!selectedFormUuid
  });
  
  const submissionByUuid = useGetSubmissionByUuid(currentFormSubmissionUuid || "");
  const submissionByFormAndPatient = useGetSubmissionByFormAndPatient(selectedFormUuid || "", patientUuid);

  const existingSubmission = (submissionMode === "edit" && currentFormSubmissionUuid)
    ? submissionByUuid.data
    : submissionByFormAndPatient.data;
  const isLoadingSubmission = (submissionMode === "edit" && currentFormSubmissionUuid)
    ? submissionByUuid.isLoading
    : submissionByFormAndPatient.isLoading;
  const submissionExists = (submissionMode === "edit" && currentFormSubmissionUuid)
    ? !!submissionByUuid.data
    : submissionByFormAndPatient.exists;
  
  const createSubmission = useCreateSubmission();
  const updateSubmission = useUpdateSubmission(currentFormSubmissionUuid || "");
  const finalizeSubmissions = useFinalizeSubmissions();
  const deleteSubmission = useDeleteSubmission();

  useEffect(() => {
    if (selectedFormUuid && patientUuid) {
      if (submissionExists && existingSubmission) {
        setSubmissionMode("edit");
        setCurrentFormSubmissionUuid(existingSubmission.uuid);
      } else {
        setSubmissionMode("new");
        setCurrentFormSubmissionUuid(null);
      }
    }
  }, [selectedFormUuid, patientUuid, submissionExists, existingSubmission]);

  useEffect(() => {
    setSelectedFormSubmissions(new Set());
  }, [selectedTab]);

  useEffect(() => {
    console.log("Selected patient updated:", selectedPatient);
  }, [selectedPatient]);

  const handleFilterChange = (filter: string) => {
    setActiveTab(filter);
  };

  const handlePatientsModeToggle = (enabled: boolean) => {
    setPatientsMode(enabled);
  };

  const formatUserName = (user: { first_name?: string; last_name?: string; identifier?: string } | null) => {
    if (!user) return "Unknown User";
    const firstName = user.first_name || "";
    const lastName = user.last_name || "";
    return firstName || lastName ? `${firstName} ${lastName}`.trim() : user.identifier || "Unknown User";
  };

  const handleFormClick = (formUuid: string, mode: "new" | "edit" = "new", submissionUuid?: string) => {
    setSelectedFormUuid(formUuid);
    setSubmissionMode(mode);
    if (mode === "edit" && submissionUuid) {
      setCurrentFormSubmissionUuid(submissionUuid);
    } else {
      setCurrentFormSubmissionUuid(null);
    }
  };

  // Helper function to check if a form is complete (all required questions answered)
  const isFormComplete = (formData: Record<string, unknown>): boolean => {
    if (!formData.sections || !Array.isArray(formData.sections)) {
      console.log("No sections found in form data");
      return false;
    }

    console.log("Checking form completion for", formData.sections.length, "sections");
    
    for (const section of formData.sections) {
      if (section.questions && Array.isArray(section.questions)) {
        console.log("Section", section.id, "has", section.questions.length, "questions");
        
        for (const question of section.questions) {
          // Check if question is required
          if (question.required === true) {
            const answer = question['user-answer'];
            console.log("Required question", question.id, "answer:", answer, "type:", typeof answer);
            
            // Handle different answer types
            if (answer === null || answer === undefined) {
              console.log("Question", question.id, "has null/undefined answer");
              return false;
            }
            
            // Handle string answers
            if (typeof answer === 'string') {
              if (answer.trim() === '') {
                console.log("Question", question.id, "has empty string answer");
                return false;
              }
            }
            
            // Handle array answers
            if (Array.isArray(answer)) {
              if (answer.length === 0) {
                console.log("Question", question.id, "has empty array answer");
                return false;
              }
              // Check if all items in array are empty
              if (answer.every(item => 
                item === null || item === undefined || 
                (typeof item === 'string' && item.trim() === '')
              )) {
                console.log("Question", question.id, "has array with all empty items");
                return false;
              }
            }
            
            // Handle object answers (like file uploads)
            if (typeof answer === 'object' && answer !== null && !Array.isArray(answer)) {
              // For file uploads, check if files exist
              if ('files' in answer && (!answer.files || (Array.isArray(answer.files) && answer.files.length === 0))) {
                console.log("Question", question.id, "has no files uploaded");
                return false;
              }
            }
          }
        }
      }
    }
    
    console.log("Form is complete - all required questions answered");
    return true;
  };

  const handleBackToForms = () => {
    setSelectedFormUuid(null);
    setProcessingError(null);
    setCurrentFormSubmissionUuid(null);
    setSubmissionResult(null); // Clear any submission results when going back
  };

  // Helper function to determine which tab to navigate to after form submission
  const navigateToAppropriateTab = (isCompleted: boolean, isFinalized: boolean = false) => {
    // First, close the form view
    setSelectedFormUuid(null);
    setProcessingError(null);
    setCurrentFormSubmissionUuid(null);
    
    if (isCompleted && isFinalized) {
      setSelectedTab("submitted");
      // Show success message for submitted forms
      if (!submissionResult) {
        setSubmissionResult({
          success: true,
          message: "Form automatically submitted! Navigated to Submitted tab."
        });
      }
    } else if (isCompleted) {
      setSelectedTab("draft");
      // Show success message for completed but not finalized forms
      if (!submissionResult) {
        setSubmissionResult({
          success: true,
          message: "Form completed! Navigated to Drafts tab."
        });
      }
    } else {
      setSelectedTab("draft");
      // Show success message for draft forms
      if (!submissionResult) {
        setSubmissionResult({
          success: true,
          message: "Form draft saved! Navigated to Drafts tab."
        });
      }
    }
  };

  const handleTabChange = (tab: "draft" | "empty" | "submitted") => {
    setSelectedTab(tab);
    setSubmissionResult(null);
  };

  const toggleFormSelection = (submissionUuid: string) => {
    setSelectedFormSubmissions(prev => {
      const newSelection = new Set(prev);
      if (newSelection.has(submissionUuid)) {
        newSelection.delete(submissionUuid);
      } else {
        newSelection.add(submissionUuid);
      }
      return newSelection;
    });
  };

  const refreshData = async (queryClient: QueryClient) => {
    try {
      await queryClient.invalidateQueries(); // Invalidate all queries to refresh data
      console.log("Data refreshed successfully");
    } catch (error) {
      console.error("Error refreshing data:", error);
    }
  };

  // Remove all logic related to selecting forms and handleFinalizeSelectedForms

  // Create a helper function to extract files from FileList or similar structures
  const extractFilesFromAnswer = (answer: unknown): File[] => {
    const files: File[] = [];
    
    if (!answer) return files;
    
    // If answer is a FileList, convert to array of Files
    if (answer instanceof FileList) {
      return Array.from(answer);
    }
    
    // Handle case where answer might be an object with files
    if (typeof answer === 'object' && answer !== null) {
      // If the object has a "files" property that's a FileList
      if ('files' in answer && answer.files instanceof FileList) {
        return Array.from(answer.files);
      }
      
      // Try to find any File objects stored as properties
      Object.values(answer).forEach(value => {
        if (value instanceof File) {
          files.push(value);
        } else if (value instanceof FileList) {
          Array.from(value).forEach(file => files.push(file));
        }
      });
    }
    
    return files;
  };

  // Modify handleFormSubmit to track changes for submitted forms
  const handleFormSubmit = (formData: Record<string, unknown>, isCompleted: boolean = true) => {
    if (!selectedFormUuid || !patientUuid) {
      setProcessingError("Missing form or patient data");
      return;
    }

    // Track changes if this is a submitted form being edited and finalized
    if (selectedTab === "submitted" && existingSubmission && isCompleted) {
      // Add answer_history for all question types
      if (
        formData.sections && Array.isArray(formData.sections) &&
        existingSubmission.submission &&
        typeof existingSubmission.submission === 'object' &&
        Array.isArray((existingSubmission.submission as any).sections)
      ) {
        const oldSections = (existingSubmission.submission as any).sections;
        formData.sections.forEach((section: any) => {
          const oldSection = oldSections.find((s: any) => s.id === section.id);
          if (!oldSection) return;
          section.questions?.forEach((question: any) => {
            const oldQuestion = oldSection.questions?.find((q: any) => q.id === question.id);
            if (!oldQuestion) return;
            // Compare old and new answers (deep equality for arrays/objects)
            const oldAnswer = oldQuestion['user-answer'];
            const newAnswer = question['user-answer'];
            const isChanged = JSON.stringify(oldAnswer) !== JSON.stringify(newAnswer);
            if (isChanged) {
              // Copy previous answer_history if it exists
              const prevHistory = Array.isArray(oldQuestion.answer_history) ? [...oldQuestion.answer_history] : [];
              // Add the old answer to history, with answered-by
              prevHistory.push({
                answer: oldAnswer,
                'answered-at': new Date().toISOString(),
                'answered-by': oldQuestion['answered-by'] || (currentUser ? {
                  email: currentUser.email,
                  last_name: currentUser.last_name,
                  user_type: currentUser.user_type,
                  first_name: currentUser.first_name,
                  identifier: currentUser.identifier
                } : null)
              });
              question.answer_history = prevHistory;
            } else if (Array.isArray(oldQuestion.answer_history)) {
              // If not changed, preserve the full previous history
              question.answer_history = [...oldQuestion.answer_history];
            }
          });
        });
      }
    }

    console.log("Submitting form with patient UUID:", patientUuid);
    console.log("Submission status:", isCompleted ? "COMPLETED" : "DRAFT");

    // Extract file attachments from the form data
    const fileAttachments = {
      image_attachments: [] as File[],
      video_attachments: [] as File[],
      document_attachments: [] as File[]
    };

    // Define section and question types
    interface FormQuestion {
      id: number | string;
      type: string;
      'user-answer'?: unknown;
      'user-answer-metadata'?: Array<{name: string; size: number; type: string}>;
      [key: string]: unknown;
    }
    
    interface FormSection {
      id: number | string;
      questions?: FormQuestion[];
      [key: string]: unknown;
    }
    
    // Process sections to find file attachments
    if (formData.sections && Array.isArray(formData.sections)) {
      (formData.sections as FormSection[]).forEach(section => {
        if (section.questions && Array.isArray(section.questions)) {
          section.questions.forEach((question: FormQuestion) => {
            // Only process attach-file type questions with actual answers
            if (question.type === 'attach-file' && question['user-answer']) {
              const files = extractFilesFromAnswer(question['user-answer']);
              
              if (files.length > 0) {
                console.log(`Found ${files.length} files in question ${question.id}:`, 
                  files.map(f => `${f.name} (${f.type})`));
                  
                // Store additional info in the question's user-answer for displaying in the UI
                question['user-answer-metadata'] = files.map(file => ({
                  name: file.name,
                  size: file.size,
                  type: file.type,
                  question_id: question.id // Add question_id directly when creating metadata
                }));
              }
              
              // Categorize files by type
              files.forEach(file => {
                // Add explicit file type checks
                if (file.type.startsWith('image/')) {
                  console.log(`Adding image file: ${file.name}`);
                  fileAttachments.image_attachments.push(file);
                } else if (file.type.startsWith('video/')) {
                  console.log(`Adding video file: ${file.name}`);
                  fileAttachments.video_attachments.push(file);
                } else {
                  // Any other file type goes to document_attachments
                  console.log(`Adding document file: ${file.name}`);
                  fileAttachments.document_attachments.push(file);
                }
              });
            }
          });
        }
      });
    }

    // Log attachment counts for debugging
    console.log("Attachment counts:", {
      images: fileAttachments.image_attachments.length,
      videos: fileAttachments.video_attachments.length, 
      documents: fileAttachments.document_attachments.length
    });

    // Log the first few files of each type for debugging
    if (fileAttachments.image_attachments.length > 0) {
      console.log("Sample image files:", 
        fileAttachments.image_attachments.slice(0, 2).map(f => ({name: f.name, type: f.type, size: f.size})));
    }
    if (fileAttachments.video_attachments.length > 0) {
      console.log("Sample video files:", 
        fileAttachments.video_attachments.slice(0, 2).map(f => ({name: f.name, type: f.type, size: f.size})));
    }
    if (fileAttachments.document_attachments.length > 0) {
      console.log("Sample document files:", 
        fileAttachments.document_attachments.slice(0, 2).map(f => ({name: f.name, type: f.type, size: f.size})));
    }

    // Create user object for API
    const userObject = {
      identifier: selectedPatient?.uuid || "0",
      first_name: selectedPatient?.first_name || "",
      last_name: selectedPatient?.last_name || "",
      email: selectedPatient?.email || "<EMAIL>"
    };

    if (submissionMode === "edit" && currentFormSubmissionUuid) {
      // Update existing submission
      updateSubmission.mutate(
        {
          form_uuid: selectedFormUuid,  // Add form_uuid for the API
          form: selectedFormUuid,
          user: userObject,
          patient_uuid: patientUuid,
          submission: formData,
          is_completed: isCompleted,
          attachments: fileAttachments
        },
        {
          onSuccess: () => {
            console.log("Form submission updated successfully");
            
            // Check if form is complete and we're in draft tab
            console.log("Form submission details:", {
              selectedTab,
              isCompleted,
              isFormComplete: isFormComplete(formData),
              formDataKeys: Object.keys(formData)
            });
            
            if (selectedTab === "draft" && isCompleted && isFormComplete(formData)) {
              console.log("Form is complete, automatically finalizing...");
              // Automatically finalize the form
              finalizeSubmissions.mutate(
                { submission_uuids: [currentFormSubmissionUuid] },
                {
                  onSuccess: (finalizeResponse) => {
                    console.log("Form automatically finalized:", finalizeResponse);
                    // Refresh data and switch to submitted tab
                    refreshData(queryClient).then(() => {
                      navigateToAppropriateTab(true, true);
                    });
                  },
                  onError: (finalizeError) => {
                    console.error("Error auto-finalizing form:", finalizeError);
                    setSubmissionResult({
                      success: false,
                      message: `Form completed but could not be submitted: ${finalizeError}`
                    });
                    // If finalization fails, stay in drafts tab
                    refreshData(queryClient).then(() => {
                      navigateToAppropriateTab(true, false);
                    });
                  }
                }
              );
            } else if (!isCompleted) {
              // If saving as draft, navigate to draft tab
              console.log("Saving as draft, navigating to draft tab...");
              refreshData(queryClient).then(() => {
                navigateToAppropriateTab(false, false);
              });
            } else {
              // If not complete or not in draft tab, just go back to forms
              handleBackToForms();
            }
          },
          onError: (error: unknown) => {
            console.error("Error updating submission:", error);
            const err = error as { response?: { data?: { detail?: string } }, message?: string };
            const errorMessage = err.response?.data?.detail || err.message || String(error);
            setProcessingError(`Error updating submission: ${errorMessage}`);
          }
        }
      );
    } else {
      // Create new submission
      createSubmission.mutate(
        {
          form_uuid: selectedFormUuid,
          user: userObject,
          patient_uuid: patientUuid,
          submission: formData,
          is_completed: isCompleted,
          attachments: fileAttachments,
          final_submission: false // Explicitly set this to false for initial submissions
          ,
          form: ""
        },
        {
          onSuccess: (response) => {
            console.log(`Form ${isCompleted ? "submission" : "draft"} created successfully`);
            
            // Check if form is complete for new submissions
            console.log("New submission details:", {
              selectedTab,
              isCompleted,
              isFormComplete: isFormComplete(formData),
              responseUuid: response?.uuid
            });
            
            // If the form is completed, automatically finalize it
            if (isCompleted && response?.uuid && isFormComplete(formData)) {
              console.log("Automatically finalizing completed form...");
              finalizeSubmissions.mutate(
                { submission_uuids: [response.uuid] },
                {
                  onSuccess: (finalizeResponse) => {
                    console.log("Form automatically finalized:", finalizeResponse);
                    // Refresh data and switch to submitted tab
                    refreshData(queryClient).then(() => {
                      navigateToAppropriateTab(true, true);
                    });
                  },
                  onError: (finalizeError) => {
                    console.error("Error auto-finalizing form:", finalizeError);
                    setSubmissionResult({
                      success: false,
                      message: `Form completed but could not be submitted: ${finalizeError}`
                    });
                    // If finalization fails, switch to drafts tab since it's a completed but not finalized form
                    refreshData(queryClient).then(() => {
                      navigateToAppropriateTab(true, false);
                    });
                  }
                }
              );
            } else if (!isCompleted) {
              // If form is not completed (draft), switch to drafts tab
              console.log("Saving as draft, navigating to draft tab...");
              refreshData(queryClient).then(() => {
                navigateToAppropriateTab(false, false);
              });
            } else {
              // If completed but not all required questions answered, stay in current tab
              console.log("Form marked as completed but not all required questions answered");
              handleBackToForms();
            }
          },
          onError: (error) => {
            console.error("Error creating submission:", error);
            const err = error as { response?: { data?: { detail?: string } }, message?: string };
            const errorDetails = err.response?.data?.detail || err.message || String(error);
            setProcessingError(`Error creating submission: ${errorDetails}`);
          }
        }
      );
    }
  };

  const processFormData = (): SavedFormData | null => {
    if (!selectedFormData) return null;
    
    try {
      let formStructure;
      
      if (selectedFormData.active_version?.form_structure) {
        if (typeof selectedFormData.active_version.form_structure === 'string') {
          try {
            formStructure = JSON.parse(selectedFormData.active_version.form_structure);
          } catch (parseError) {
            console.error("Failed to parse form_structure string:", parseError);
          }
        } else {
          formStructure = selectedFormData.active_version.form_structure;
        }
      } else if (selectedFormData.active_version?.structure && typeof selectedFormData.active_version.structure === 'string') {
        try {
          formStructure = JSON.parse(selectedFormData.active_version.structure);
        } catch (parseError) {
          console.error("Failed to parse structure string:", parseError);
          
          try {
            formStructure = JSON.parse(JSON.parse(selectedFormData.active_version.structure));
          } catch (doubleParseError) {
            console.error("Failed to parse double-encoded structure string:", doubleParseError);
          }
        }
      } else if (selectedFormData.form_structure) {
        if (typeof selectedFormData.form_structure === 'string') {
          try {
            formStructure = JSON.parse(selectedFormData.form_structure);
          } catch (parseError) {
            console.error("Failed to parse form_structure string:", parseError);
          }
        } else {
          formStructure = selectedFormData.form_structure;
        }
      }
      
      if (!formStructure || !formStructure.sections) {
        setProcessingError("Invalid form structure in the response");
        return null;
      }
      
      // Process existing submission data if available in edit mode
      let existingSubmissionData = {};
      if (submissionMode === "edit" && existingSubmission && existingSubmission.submission) {
        console.log("Processing existing submission data:", existingSubmission.submission);
        existingSubmissionData = existingSubmission.submission;
      }
      
      // Extract form details
      const formName = selectedFormData.name || (selectedFormData.active_version as { name?: string })?.name || "Untitled Form";
      const formDescription = selectedFormData.description || selectedFormData.active_version?.description || "";
      const formPrivacy = selectedFormData.active_version?.privacy || "Public";
      
      // Define a type for category and tag items
      type CategoryOrTagItem = string | { name?: string; [key: string]: unknown };

      // Helper function to extract names
      const extractNames = (items: CategoryOrTagItem[]): string => {
        return items
          .map(item => (typeof item === 'string' ? item : item.name || String(item)))
          .join(", ");
      };

      let categories = "";
      if (Array.isArray(selectedFormData.categories)) {
        categories = extractNames(selectedFormData.categories as CategoryOrTagItem[]);
      } else {
        const formDataAny = selectedFormData as { tags?: unknown[] };
        if (Array.isArray(formDataAny.tags)) {
          categories = extractNames(formDataAny.tags as CategoryOrTagItem[]);
        } else if (Array.isArray(selectedFormData.active_version?.categories)) {
          categories = selectedFormData.active_version.categories.join(", ");
        }
      }
      
      return {
        formDetails: {
          name: formName,
          description: formDescription,
          categories: categories,
          privacy: formPrivacy?.toLowerCase() === "private" ? "Private" : "Public",
          password: selectedFormData.active_version?.password || "",
          study: selectedFormData.active_version?.study || null
        },
        sections: formStructure.sections,
        existingSubmissionData // Include the existing submission data
      };
      
    } catch (e) {
      console.error("Error processing form data:", e);
      setProcessingError(`Error processing form data: ${e instanceof Error ? e.message : "Unknown error"}`);
      return null;
    }
  };

  const staticFormData: SavedFormData = {
    formDetails: {
      name: "Form Not Available",
      description: "This form could not be loaded properly",
      categories: "",
      privacy: "Public",
      password: "",
      study: null
    },
    sections: [
      {
        id: 1, 
        name: "Fallback Section", 
        description: "This is a fallback section", 
        questions: [
          {
            id: 1, 
            type: "short-text", 
            answers: [""], 
            required: false, 
            nonClinical: false, 
            questionName: "This form could not be loaded properly"
          }
        ]
      }
    ]
  };

  const formData = processFormData() || staticFormData;

  // Get the forms to display based on the selected tab
  const getFormsToDisplay = () => {
    // Move declarations outside of switch
    let sortedSubmissions: FormSubmission[] = [];
    let submissionSequences: Map<string, number>;
    let formSubmissionOrder: Map<string, string[]>;
    let allForms: Array<{uuid: string, [key: string]: unknown}> = [];
    let submissionCounts: Map<string, number>;

    switch (selectedTab) {
      case "draft": {
        // Combine draft and completed submissions
        const allDraftAndCompleted = [
          ...(draftSubmissions || []),
          ...(completedSubmissions || [])
        ];
        
        return allDraftAndCompleted.map(submission => {
          // Extract form name from either form object or submission data
          const formUuid = typeof submission.form === 'string' ? submission.form : submission.form?.uuid;
          let formName = typeof submission.form === 'string' ? 'Form' : submission.form?.name || 'Form';
          
          // Add type check for submission.submission
          const submissionData = submission.submission as SubmissionData | undefined;
          if (submissionData?.formDetails?.name) {
            formName = submissionData.formDetails.name;
          }
          
          return {
            uuid: formUuid,
            name: formName,
            user: typeof submission.user === 'number' ? { identifier: 'User' } : submission.user,
            created_at: submission.created_at,
            updated_at: submission.last_updated_at || submission.updated_at || submission.created_at,
            updatedBy: submission.last_updated_by || submission.updated_by || submission.user,
            submission: submission,
            isCompleted: submission.is_completed || false
          };
        }) || [];
      }
      case "submitted":
        // Sort submissions by creation date (oldest first)
        sortedSubmissions = [...(submittedForms || [])].sort((a, b) => {
          const dateA = new Date(a.created_at || '').getTime();
          const dateB = new Date(b.created_at || '').getTime();
          return dateA - dateB;
        });

        // Create a map to track submission sequence numbers for each form
        submissionSequences = new Map<string, number>();
        formSubmissionOrder = new Map<string, string[]>();

        // First pass: collect all submissions for each form in chronological order
        sortedSubmissions.forEach(submission => {
          const formUuid = typeof submission.form === 'string' ? submission.form : submission.form?.uuid;
          if (formUuid) {
            if (!formSubmissionOrder.has(formUuid)) {
              formSubmissionOrder.set(formUuid, []);
            }
            formSubmissionOrder.get(formUuid)?.push(submission.uuid || '');
          }
        });

        // Second pass: assign sequence numbers based on chronological order
        sortedSubmissions.forEach(submission => {
          const formUuid = typeof submission.form === 'string' ? submission.form : submission.form?.uuid;
          if (formUuid) {
            const submissions = formSubmissionOrder.get(formUuid) || [];
            const sequenceNumber = submissions.indexOf(submission.uuid || '') + 1;
            submissionSequences.set(submission.uuid || '', sequenceNumber);
          }
        });

        return sortedSubmissions.map(submission => {
          // Extract form name from either form object or submission data
          const formUuid = typeof submission.form === 'string' ? submission.form : submission.form?.uuid;
          let formName = typeof submission.form === 'string' ? 'Form' : submission.form?.name || 'Form';
          
          // Add type check for submission.submission
          const submissionData = submission.submission as SubmissionData | undefined;
          if (submissionData?.formDetails?.name) {
            formName = submissionData.formDetails.name;
          }
          
          return {
            uuid: formUuid,
            name: formName,
            user: typeof submission.user === 'number' ? { identifier: 'User' } : submission.user,
            created_at: submission.created_at,
            updated_at: submission.last_updated_at || submission.updated_at || submission.created_at,
            updatedBy: submission.last_updated_by || submission.updated_by || submission.user,
            submission: submission,
            submissionCount: submissionSequences.get(submission.uuid || '') || 0
          };
        });
      case "empty":
      default:
        // Get all forms including those that have been submitted
        allForms = emptyForms || [];
        
        // Get submission counts for each form
        submissionCounts = new Map<string, number>();
        submittedForms?.forEach(submission => {
          const formUuid = typeof submission.form === 'string' ? submission.form : submission.form?.uuid;
          if (formUuid) {
            submissionCounts.set(formUuid, (submissionCounts.get(formUuid) || 0) + 1);
          }
        });

        // Add submission count to each form
        return allForms.map(form => ({
          ...form,
          submissionCount: submissionCounts.get(form.uuid) || 0
        }));
    }
  };

  const isLoadingCurrentTab = 
    (selectedTab === "draft" && (isLoadingDrafts || isLoadingCompleted)) || 
    (selectedTab === "empty" && isLoadingEmptyForms) ||
    (selectedTab === "submitted" && isLoadingSubmitted);

  // Define type for form submission
  interface FormSubmission {
    uuid?: string;
    form?: string | { uuid?: string; name?: string };
    user?: number | { first_name?: string; last_name?: string; identifier?: string };
    submission?: SubmissionData;
    created_at?: string;
    updated_at?: string;
    last_updated_at?: string;
    updated_by?: { first_name?: string; last_name?: string; identifier?: string };
    last_updated_by?: { first_name?: string; last_name?: string; identifier?: string };
    is_completed?: boolean;
    is_finalized?: boolean;
  }

  // Handle close preview
  const handleClosePreview = () => {
    setPreviewSubmission(null);
  };

  // If a submission is being previewed, show the preview component
  if (previewSubmission) {
    return <FormSubmissionPreview submission={previewSubmission} onClose={handleClosePreview} />;
  }

  // Add delete handler function
  const handleDeleteSubmission = (formUuid: string) => {
    if (window.confirm('Are you sure you want to delete this draft?')) {
      deleteSubmission.mutate(
        `delete/form/${formUuid}/patient/${patientUuid}`,
        {
          onSuccess: () => {
            console.log("Draft deleted successfully");
            // Refresh the data
            refreshData(queryClient);
          },
          onError: (error) => {
            console.error("Error deleting draft:", error);
            setSubmissionResult({
              success: false,
              message: `Error deleting draft: ${error}`
            });
          }
        }
      );
    }
  };

  if (selectedFormUuid) {
    return (
      <div className="form-view-container">
        <div className="form-view-header">
          <h2>Form View {submissionMode === "edit" ? "(Edit)" : "(New)"}</h2>
          <button 
            className="back-to-forms-button"
            onClick={handleBackToForms}
          >
            <ArrowLeft size={18} />
            Back to Forms List
          </button>
        </div>
        
        <div style={{ display: 'flex' }}>
          <DocumentationSidebar 
            onTabChange={handleFilterChange}
            onPatientsModeToggle={handlePatientsModeToggle}
            currentTab={activeTab}
            currentPatientsMode={patientsMode}
          />
          
          <div className="form-view-content" style={{ flexGrow: 1 }}>
            {isLoadingSelectedForm || isLoadingSubmission ? (
              <div className="loading-indicator">
                <div className="spinner"></div>
                <p>Loading form data...</p>
              </div>
            ) : processingError ? (
              <div className="error-message">
                <AlertCircle size={20} />
                <span>{processingError}</span>
              </div>
            ) : (
              <RebuildForm 
                formData={formData} 
                formUuid={selectedFormUuid}
                activeFilter={activeTab}
                patientsMode={patientsMode}
                onSubmit={handleFormSubmit}
                onSaveDraft={(data) => handleFormSubmit(data, false)}
                isEditMode={submissionMode === "edit"}
              />
            )}
          </div>
        </div>
      </div>
    );
  }

  // Render the list of forms with tabs
  return (
    <div>
      <Preloader />
      <div style={{ marginTop: "30px", paddingBottom: "80px" }}>
        {!patientUuid ? (
          <div className="text-center py-5">No patient selected. Please select a patient first.</div>
        ) : (
          <>
            <div className="tabs-container" style={{ marginBottom: "20px" }}>
              <div className="tab-buttons">
                <button 
                  className={`tab-button ${selectedTab === "empty" ? "active" : ""}`}
                  onClick={() => handleTabChange("empty")}
                >
                  <Plus size={16} />
                  Empty Forms
                </button>
                <button 
                  className={`tab-button ${selectedTab === "draft" ? "active" : ""}`}
                  onClick={() => handleTabChange("draft")}
                >
                  <Clock size={16} />
                  Drafts
                </button>

                <button 
                  className={`tab-button ${selectedTab === "submitted" ? "active" : ""}`}
                  onClick={() => handleTabChange("submitted")}
                >
                  <Archive size={16} />
                  Submitted
                </button>
              </div>
            </div>
            
            {/* Status message display for form submission */}
            {submissionResult && (
              <div className={`submission-result ${submissionResult.success ? 'success' : 'error'}`}>
                {submissionResult.success ? <CheckCircle2 size={18} /> : <AlertCircle size={18} />}
                <span>{submissionResult.message}</span>
              </div>
            )}
            
            {isLoadingCurrentTab ? (
              <div className="loading-indicator">
                <div className="spinner"></div>
                <p>Loading forms...</p>
              </div>
            ) : getFormsToDisplay().length > 0 ? (
              <>
                <div className="row y-gap-30 mt-30" style={{ marginBottom: "30px" }}>
                  {getFormsToDisplay().map((form: any, index: number) => (
                    <div className="col-xl-12 col-lg-12 col-md-12" key={form.uuid || index}>
                      <div 
                        className={`form-card ${selectedTab === "draft" && form.isCompleted ? "selectable" : ""} ${
                          selectedTab === "draft" && form.isCompleted && (form as { submission?: { uuid?: string } }).submission?.uuid && selectedFormSubmissions.has((form as { submission?: { uuid?: string } }).submission?.uuid || '') ? "selected" : ""
                        } ${selectedTab === "submitted" ? "submitted" : ""}`}
                        onClick={() => {
                          if (selectedTab === "draft" && form.isCompleted) {
                            // In draft tab, clicking selects/deselects completed forms
                            const currentForm = form as { submission?: { uuid?: string } };
                            if (currentForm.submission?.uuid) {
                              toggleFormSelection(currentForm.submission.uuid);
                            }
                          } else if (selectedTab === "submitted") {
                            // In submitted tab, clicking opens the form in preview mode
                            if (form.submission) {
                              setPreviewSubmission(form.submission);
                            }
                          } else if (form.uuid) {
                            // In other tabs, clicking opens the form
                            const mode = selectedTab === "empty" ? "new" : "edit";
                            handleFormClick((form as { uuid: string }).uuid, mode);
                          }
                        }}
                      >
                        <DashboardFormCard
                          createdBy={formatUserName(form.user)}
                          title={form.name || "Unnamed Form"}
                          date={new Date(form.created_at).toLocaleDateString()}
                          lastUpdatedAt={form.updated_at ? new Date(form.updated_at).toLocaleDateString() : undefined}
                          lastUpdatedBy={form.updatedBy ? formatUserName(form.updatedBy) : undefined}
                          status={
                            selectedTab === "draft" && form.isCompleted ? "Completed" : 
                            selectedTab === "draft" ? "Draft" : 
                            selectedTab === "submitted" ? "Submitted" : "New"
                          }
                          submissionCount={form.submissionCount}
                        />
                        {selectedTab === "draft" && form.isCompleted && (
                          <div className="form-checkbox">
                            <input 
                              type="checkbox"
                              checked={selectedFormSubmissions.has(form.submission?.uuid ?? '')}
                              onChange={(e) => {
                                e.stopPropagation();
                                if (form.submission?.uuid) {
                                  toggleFormSelection(form.submission.uuid);
                                }
                              }}
                            />
                          </div>
                        )}
                        {selectedTab === "submitted" && (
                          <div className="form-actions">
                            <button 
                              className="btn-edit-form"
                              onClick={(e) => {
                                e.stopPropagation();
                                if (form.uuid && form.submission?.uuid) {
                                  handleFormClick(form.uuid, "edit", form.submission.uuid);
                                }
                              }}
                            >
                              <Edit size={16} />
                              Edit
                            </button>
                          </div>
                        )}
                        {selectedTab === "draft" && (
                          <div className="form-actions">
                            <button 
                              className="btn-delete-form"
                              onClick={(e) => {
                                e.stopPropagation();
                                if (form.uuid) {
                                  handleDeleteSubmission(form.uuid);
                                }
                              }}
                            >
                              <Trash2 size={16} />
                              Delete
                            </button>
                          </div>
                        )}
                      </div>
                    </div>
                  ))}
                </div>
                
              </>
            ) : (
              <div className="empty-forms-container">
                <FileText size={48} className="text-light-3" />
                <h4>No Forms Available</h4>
                <p>
                  {selectedTab === "draft" && "No draft or completed forms found for this patient."}
                  {selectedTab === "empty" && "There are no forms assigned to this patient yet."}
                  {selectedTab === "submitted" && "No submitted forms found for this patient."}
                </p>
              </div>
            )}
          </>
        )}
      </div>
    </div>
  );
}

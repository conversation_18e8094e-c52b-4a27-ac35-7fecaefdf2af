.nurtify-radio {
  display: flex;
  flex-direction: row;
  gap: 5px;
  justify-content: start;
  border-radius: 4px;
  width: 100%;
  padding: 16px;
  color: #090914;
}

.text-14 {
  font-size: var(--text-14) !important;
}


.nurtify-radio:hover {
  background-color: #37b7c31a;
  cursor: pointer;
}

.custom-radio input[type="radio"] {
  display: none;
  padding: 2px;
}

.custom-radio label {
  position: relative;
  padding-left: 25px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
}

.custom-radio label::before {
  content: "";
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 15px;
  height: 15px;
  border: 1px solid #37B7C3;
  border-radius: 50%;
  background: #fff;
}

.custom-radio input[type="radio"]:checked + label::before {
  color: #37B7C3 !important;
  content: "•";
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  font-size: 33px;
  line-height: 0;
  padding-bottom: 8.5px;
}

.custom-checkbox input[type="checkbox"] {
  display: none;
  user-select: none;
}

.nurtify-input input[type="text"],
.nurtify-input input[type="number"],
.nurtify-input input[type="password"],
.nurtify-input input[type="email"],
.nurtify-input input[type="date"],
.nurtify-input input[type="tel"]
 {
  border: none;
  outline: none;
  background-color: transparent;
  padding: 0px;
}


.custom-checkbox label {
  position: relative;
  padding-left: 25px;
  cursor: pointer;
  display: flex;
  user-select: none;
  align-items: center;
  font-size: 14px;
  font-weight: 500;
}

.custom-checkbox label::before {
  content: "";
  position: absolute;
  left: 0;
  top: 47%;
  transform: translateY(-50%);
  width: 16px;
  height: 16px;
  border: 1px solid black;
  border-radius: 16%;
}

.custom-checkbox input[type="checkbox"]:checked + label::before {
  color: black;
  content: "✓";
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
}

.nurtify-input {
  display: flex;
  flex-direction: column;
  justify-content: start;
  background:none;
  border: 1px solid #D2DFE1;
  border-radius: 4px;
  width: auto;
  padding: 10px;
  color: black;
}
.nurtify-select {
  display: flex;
  flex-direction: column;
  justify-content: start;
  border: 1px solid #37B7C3;
  border-radius: 4px;
  width: auto;
  padding: 10px;
  color: black;
}

.nurtify-select select {
  border: none;
  outline: none;
  padding: 8px;
  border-radius: 4px;
  color: black;
  cursor: pointer;
}

.nurtify-select select option:checked {
  background-color: #37B7C3;
  color: white;
}

.nurtify-range {
  -webkit-appearance: none;
  width: 100%;
  height: 8px;
  background: #ddd;
  outline: none;
  border-radius: 5px;
}

.nurtify-range::-webkit-slider-thumb {
  -webkit-appearance: none;
  appearance: none;
  width: 20px;
  height: 20px;
  background: #37B7C3;
  cursor: pointer;
  border-radius: 50%;
}

.nurtify-range::-moz-range-thumb {
  width: 20px;
  height: 20px;
  background: #37B7C3;
  cursor: pointer;
  border-radius: 50%;
}

.nurtify-attach-file-box {
  display: flex;
  flex-direction: column;
  justify-content: start;
  border-radius: 4px;
  height: 60px;
  width: auto;
  padding: 15px;
  color: black;
  background-color: #DFF3F5; /* Add background color */
}

.nurtify-attach-file-box input[type="file"] {
  display: none; /* Hide the default file input */
}

.file-upload-label {
  display: flex;
  align-items: center;
  gap: 20px;
  cursor: pointer;
  color: #07195273;
  font-weight: 700;
}

.upload-icon {
  margin-right: 8px;
}

.nurtify-sign-box {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 1px solid #37B7C38A;
  border-radius: 4px;
  height: 200px;
  padding: 16px;
  color: #07195280;
  font-weight: 700;
  background-color: #DFF3F5;
}

.nurtify-sign-box .icon {
  position: absolute;
  top: 12px;
  right: 12px;
  color: #07195280;
  font-weight: 700;
  cursor: pointer;
}

.nurtify-sign-box .icon-save {
  position: absolute;
  top: 12px;
  right: 50px;
  color: #07195280;
  font-weight: 700;
  cursor: pointer;
}

.nurtify-date-control {
  width: 100%;
  height: 40px;
  border-radius: 4px;
  padding: 0 15px;
  font-size: 15px;
  font-weight: 400;
  color: var(--color-dark);
  transition: all 0.3s;
  cursor: pointer;
}

.nurtify-date-control:focus {
  outline: none;
}

.react-datepicker-wrapper {
  width: 100%;
}

.react-datepicker {
  font-family: inherit;
  border: 1px solid #ddd;
  border-radius: 4px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* === React Datepicker Overrides (Scoped) === */

.nurtify-datepicker-theme .react-datepicker-popper { /* Scope the popper */
  z-index: 50; /* Ensure dropdown appears above other elements */
}

.nurtify-datepicker-theme.react-datepicker { /* Scope the main calendar */
  font-family: inherit; /* Use project font */
  border: 1px solid #e5e7eb; /* Lighter border */
  border-radius: 8px; /* Slightly larger radius */
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1); /* Softer shadow */
  margin: 10px; /* Add margin around the calendar */
}

.nurtify-datepicker-theme .react-datepicker__header { /* Scope the header */
  background-color: #37B7C3; /* Nurtify teal background */
  border-bottom: none;
  padding-top: 12px; /* Adjust padding */
  padding-bottom: 12px;
  border-top-left-radius: 7px; /* Match container radius */
  border-top-right-radius: 7px;
}

.nurtify-datepicker-theme .react-datepicker__current-month,
.nurtify-datepicker-theme .react-datepicker__day-name { /* Scope header text */
  color: white; /* White text */
  font-weight: 500; /* Medium weight */
  text-transform: capitalize;
}

.nurtify-datepicker-theme .react-datepicker__day-name { /* Scope day names */
  margin: 0.166rem 0.4rem; /* Adjust spacing */
}

.nurtify-datepicker-theme .react-datepicker__day { /* Scope general day styles */
  border-radius: 50%; /* Circular days */
  margin: 0.166rem;
  line-height: 1.8rem; /* Adjust line height */
  width: 1.8rem; /* Adjust width */
  transition: background-color 0.2s ease;
}

.nurtify-datepicker-theme .react-datepicker__day--selected { /* Scope selected day */
  background-color: #37B7C3; /* Nurtify teal background */
  color: white; /* White text */
  font-weight: bold;
}

.nurtify-datepicker-theme .react-datepicker__day--selected:hover { /* Scope selected day hover */
  background-color: #31A8B4; /* Darker Nurtify teal on hover */
}

.nurtify-datepicker-theme .react-datepicker__day:hover { /* Scope day hover */
  background-color: #E0F2F7; /* Light Nurtify teal hover */
  border-radius: 50%;
}

.nurtify-datepicker-theme .react-datepicker__day--keyboard-selected { /* Scope keyboard selected */
  background-color: #EDE9FE; /* Light purple for keyboard nav */
  color: #374151; /* Darker text for light background */
}

.nurtify-datepicker-theme .react-datepicker__day--today { /* Scope today */
  font-weight: bold;
  border: 1px solid #37B7C3; /* Nurtify teal border */
  background-color: white; /* Ensure background isn't overridden */
  color: #37B7C3; /* Nurtify teal text */
}
.nurtify-datepicker-theme .react-datepicker__day--today:hover { /* Scope today hover */
  background-color: #E0F2F7; /* Light Nurtify teal hover */
}


.nurtify-datepicker-theme .react-datepicker__day--outside-month { /* Scope outside month */
  color: #9ca3af; /* Dim days outside month */
  cursor: default;
}
.nurtify-datepicker-theme .react-datepicker__day--outside-month:hover { /* Scope outside month hover */
  background-color: transparent;
}


.nurtify-datepicker-theme .react-datepicker__navigation { /* Scope navigation */
  top: 14px; /* Adjust position */
}

.nurtify-datepicker-theme .react-datepicker__navigation--previous { /* Scope prev arrow */
  border-right-color: white; /* White arrow */
}
.nurtify-datepicker-theme .react-datepicker__navigation--previous:hover { /* Scope prev arrow hover */
  border-right-color: #f3f4f6; /* Lighter white on hover */
}

.nurtify-datepicker-theme .react-datepicker__navigation--next { /* Scope next arrow */
  border-left-color: white; /* White arrow */
}
.nurtify-datepicker-theme .react-datepicker__navigation--next:hover { /* Scope next arrow hover */
  border-left-color: #f3f4f6; /* Lighter white on hover */
}


.nurtify-datepicker-theme .react-datepicker__year-read-view--down-arrow,
.nurtify-datepicker-theme .react-datepicker__month-read-view--down-arrow,
.nurtify-datepicker-theme .react-datepicker__month-year-read-view--down-arrow { /* Scope dropdown arrows */
  border-color: white; /* White dropdown arrows */
  border-width: 0.15em 0.15em 0 0;
  top: 0;
}
.nurtify-datepicker-theme .react-datepicker__year-read-view--down-arrow:hover,
.nurtify-datepicker-theme .react-datepicker__month-read-view--down-arrow:hover,
.nurtify-datepicker-theme .react-datepicker__month-year-read-view--down-arrow:hover { /* Scope dropdown arrows hover */
    border-color: #f3f4f6; /* Lighter white on hover */
}


.nurtify-datepicker-theme .react-datepicker__year-dropdown,
.nurtify-datepicker-theme .react-datepicker__month-dropdown,
.nurtify-datepicker-theme .react-datepicker__month-year-dropdown { /* Scope dropdown containers */
  background-color: white;
  border: 1px solid #ddd;
  border-radius: 4px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.nurtify-text{
  color: #071952;
  font-weight: normal;
  font-size: 16px;
  line-height: 24px;
  margin-bottom: 10px;
}

/* Button Styles */
.nurtify-button {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  border: none;
  cursor: pointer;
  font-family: inherit;
  font-weight: 500;
  text-decoration: none;
  transition: all 200ms ease-out;
  border-radius: 35px;
  gap: 10px;
  opacity: 1;
  outline: none;
  position: relative;
  margin: 4px;
}

.nurtify-button:focus {
  outline: 2px solid rgba(55, 183, 195, 0.3);
  outline-offset: 2px;
}

/* Size variants */
.nurtify-button--small {
  height: 48px;
  padding: 12px 32px;
  font-size: 14px;
  min-width: 120px;
}

.nurtify-button--regular {
  height: 48px;
  padding: 12px 40px;
  font-size: 14px;
  min-width: 140px;
}

.nurtify-button--medium {
  height: 48px;
  padding: 12px 48px;
  font-size: 16px;
  min-width: 159px;
}

/* Primary variant */
.nurtify-button--primary {
  background: #37B7C3;
  color: white;
}

.nurtify-button--primary:hover:not(.nurtify-button--disabled) {
  background: #088395;
  transform: translateY(-1px);
}

/* Success variant */
.nurtify-button--success {
  background: #34C759;
  color: white;
}

.nurtify-button--success:hover:not(.nurtify-button--disabled) {
  background: #28a745;
  transform: translateY(-1px);
}

/* Danger variant */
.nurtify-button--danger {
  background: #FF2D55;
  color: white;
}

.nurtify-button--danger:hover:not(.nurtify-button--disabled) {
  background: #dc3545;
  transform: translateY(-1px);
}

/* Warning variant */
.nurtify-button--warning {
  background: #FF9500;
  color: white;
}

.nurtify-button--warning:hover:not(.nurtify-button--disabled) {
  background: #e68900;
  transform: translateY(-1px);
}

/* Info variant */
.nurtify-button--info {
  background: #007AFF;
  color: white;
}

.nurtify-button--info:hover:not(.nurtify-button--disabled) {
  background: #0056cc;
  transform: translateY(-1px);
}

/* Light variant */
.nurtify-button--light {
  background: #EBF4F6;
  color: #333;
}

.nurtify-button--light:hover:not(.nurtify-button--disabled) {
  background: #d4e6ea;
  transform: translateY(-1px);
}

/* Dark variant */
.nurtify-button--dark {
  background: #090914;
  color: white;
}

.nurtify-button--dark:hover:not(.nurtify-button--disabled) {
  background: #1a1a2e;
  transform: translateY(-1px);
}

/* Link variant */
.nurtify-button--link {
  background: transparent;
  color: #37B7C3;
  padding: 12px 8px;
  min-width: auto;
}

.nurtify-button--link:hover:not(.nurtify-button--disabled) {
  color: #088395;
  text-decoration: underline;
}

/* Outline variants */
.nurtify-button--outline.nurtify-button--primary {
  background: transparent;
  color: #37B7C3;
  border: 2px solid #37B7C3;
}

.nurtify-button--outline.nurtify-button--primary:hover:not(.nurtify-button--disabled) {
  background: #37B7C3;
  color: white;
  transform: translateY(-1px);
}

.nurtify-button--outline.nurtify-button--success {
  background: transparent;
  color: #34C759;
  border: 2px solid #34C759;
}

.nurtify-button--outline.nurtify-button--success:hover:not(.nurtify-button--disabled) {
  background: #34C759;
  color: white;
  transform: translateY(-1px);
}

.nurtify-button--outline.nurtify-button--danger {
  background: transparent;
  color: #FF2D55;
  border: 2px solid #FF2D55;
}

.nurtify-button--outline.nurtify-button--danger:hover:not(.nurtify-button--disabled) {
  background: #FF2D55;
  color: white;
  transform: translateY(-1px);
}

.nurtify-button--outline.nurtify-button--warning {
  background: transparent;
  color: #FF9500;
  border: 2px solid #FF9500;
}

.nurtify-button--outline.nurtify-button--warning:hover:not(.nurtify-button--disabled) {
  background: #FF9500;
  color: white;
  transform: translateY(-1px);
}

.nurtify-button--outline.nurtify-button--info {
  background: transparent;
  color: #007AFF;
  border: 2px solid #007AFF;
}

.nurtify-button--outline.nurtify-button--info:hover:not(.nurtify-button--disabled) {
  background: #007AFF;
  color: white;
  transform: translateY(-1px);
}

.nurtify-button--outline.nurtify-button--light {
  background: transparent;
  color: #666;
  border: 2px solid #EBF4F6;
}

.nurtify-button--outline.nurtify-button--light:hover:not(.nurtify-button--disabled) {
  background: #EBF4F6;
  color: #333;
  transform: translateY(-1px);
}

.nurtify-button--outline.nurtify-button--dark {
  background: transparent;
  color: #090914;
  border: 2px solid #090914;
}

.nurtify-button--outline.nurtify-button--dark:hover:not(.nurtify-button--disabled) {
  background: #090914;
  color: white;
  transform: translateY(-1px);
}

/* Disabled state */
.nurtify-button--disabled,
.nurtify-button:disabled {
  background: #AFAFAF !important;
  color: #666 !important;
  cursor: not-allowed !important;
  transform: none !important;
  border-color: #AFAFAF !important;
}

.nurtify-button--outline.nurtify-button--disabled,
.nurtify-button--outline:disabled {
  background: transparent !important;
  color: #AFAFAF !important;
  border-color: #AFAFAF !important;
}

.nurtify-button--link.nurtify-button--disabled,
.nurtify-button--link:disabled {
  background: transparent !important;
  color: #AFAFAF !important;
  text-decoration: none !important;
}

.policy-details-loading,
.policy-details-error {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px;
  text-align: center;
}

.policy-details-error h2 {
  color: #e74c3c;
  margin-bottom: 16px;
}

.policy-details-error p {
  color: #666;
  margin-bottom: 24px;
}

.back-button {
  background: linear-gradient(135deg, #6293c8, #384c6c);
  color: white;
  border: none;
  padding: 10px 20px;
  border-radius: 8px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  transition: all 0.3s ease;
  text-decoration: none;
  display: inline-flex;
  align-items: center;
  gap: 8px;
}

.back-button:hover {
  background: linear-gradient(135deg, #5a87b8, #2d3e5c);
  transform: translateY(-1px);
}

.policy-details-header {
  margin-bottom: 30px;
}

.policy-details-title {
  font-size: 2.5rem;
  font-weight: 700;
  color: #2c3e50;
  margin: 20px 0;
  line-height: 1.2;
}

.policy-details-content {
  display: flex;
  flex-direction: column;
  gap: 30px;
}

.policy-info-section {
  background: #ffffff;
  border-radius: 16px;
  padding: 32px;
  border: 1px solid #e9ecef;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

.policy-meta {
  display: flex;
  flex-wrap: wrap;
  gap: 32px;
  margin-bottom: 32px;
  padding: 24px;
  background: linear-gradient(135deg, #EBF4F6 0%, #f8f9fa 100%);
  border-radius: 12px;
  border-left: 4px solid #37B7C3;
  box-shadow: 0 2px 4px rgba(55, 183, 195, 0.1);
}

.policy-meta-item {
  display: flex;
  flex-direction: column;
  gap: 6px;
  min-width: 200px;
}

.policy-meta-label {
  font-weight: 600;
  color: #6c757d;
  font-size: 0.85rem;
  text-transform: uppercase;
  letter-spacing: 0.8px;
}

.policy-meta-value {
  color: #2c3e50;
  font-size: 1.1rem;
  font-weight: 500;
  line-height: 1.4;
}

.policy-description {
  border-top: 1px solid #e9ecef;
  padding-top: 24px;
}

.policy-description h3 {
  color: #2c3e50;
  font-size: 1.3rem;
  font-weight: 600;
  margin-bottom: 12px;
}

.policy-description p {
  color: #495057;
  line-height: 1.6;
  font-size: 1rem;
}

.policy-document-section {
  background: #ffffff;
  border-radius: 12px;
  padding: 24px;
  border: 1px solid #e9ecef;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.policy-document-section h3 {
  color: #2c3e50;
  font-size: 1.3rem;
  font-weight: 600;
  margin-bottom: 20px;
  display: flex;
  align-items: center;
  gap: 8px;
}

.policy-document-viewer {
  border-radius: 8px;
  overflow: hidden;
  border: 1px solid #dee2e6;
  min-height: 600px;
}

.policy-no-document {
  background: #f8f9fa;
  border-radius: 12px;
  padding: 40px;
  text-align: center;
  border: 2px dashed #dee2e6;
}

.policy-no-document p {
  color: #6c757d;
  font-size: 1.1rem;
  margin: 0;
}

.unsupported-format-message {
  background: #f8f9fa;
  border-radius: 12px;
  padding: 40px;
  text-align: center;
  border: 2px dashed #dee2e6;
}

.unsupported-format-message p {
  color: #6c757d;
  font-size: 1.1rem;
  margin: 0;
}

/* Responsive Design */
@media (max-width: 768px) {
  .policy-details-title {
    font-size: 2rem;
  }

  .policy-meta {
    grid-template-columns: 1fr;
  }

  .policy-info-section,
  .policy-document-section {
    padding: 16px;
  }

  .policy-document-viewer {
    min-height: 400px;
  }
}

@media (max-width: 480px) {
  .policy-details-title {
    font-size: 1.5rem;
  }

  .back-button {
    padding: 8px 16px;
    font-size: 13px;
  }
}

import api from '../api';
import { PatientWarning, PatientWarningCreate, PatientWarningListResponse } from './types';

export const patientWarningService = {
    /**
     * Create a new patient warning
     */
    createWarning: async (data: PatientWarningCreate): Promise<PatientWarning> => {
        const response = await api.post<PatientWarning>('/patient/warnings/', data);
        return response.data;
    },

    /**
     * Get all warnings for a specific patient
     */
    getPatientWarnings: async (patientUuid: string): Promise<PatientWarningListResponse> => {
        const response = await api.get<PatientWarningListResponse>(`/patient/patients/${patientUuid}/warnings/`);
        return response.data;
    },

    /**
     * Update a patient warning
     */
    updateWarning: async (uuid: string, data: Partial<PatientWarningCreate>): Promise<PatientWarning> => {
        const response = await api.patch<PatientWarning>(`/patient/warnings/${uuid}/`, data);
        return response.data;
    },

    /**
     * Delete a patient warning
     */
    deleteWarning: async (uuid: string): Promise<void> => {
        await api.delete(`/patient/warnings/${uuid}/`);
    }
}; 
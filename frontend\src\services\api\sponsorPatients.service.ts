import api from "@/services/api.ts";
import { SponsorPatientsResponse, SponsorPatientsFilters } from "./types.ts";

export const getSponsorPatients = async (filters?: SponsorPatientsFilters) => {
  try {
    const params = new URLSearchParams();
    
    if (filters?.hospital) {
      params.append('hospital', filters.hospital);
    }
    if (filters?.site) {
      params.append('site', filters.site);
    }
    if (filters?.search) {
      params.append('search', filters.search);
    }

    const response = await api.get<SponsorPatientsResponse>(`study/studies/sponsor-patients/${params.toString() ? `?${params.toString()}` : ''}`);
    return response.data;
  } catch (error) {
    console.error("Error fetching sponsor patients:", error);
    throw error;
  }
}; 
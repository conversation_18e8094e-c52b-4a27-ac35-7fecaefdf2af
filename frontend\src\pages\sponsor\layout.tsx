import React from "react";
import { Outlet, useLocation, useNavigate } from "react-router-dom";

import LightFooter from "@/shared/LightFooter";
import "./sponsor.css";
import SponsorSidebar from "@/components/common/SponsorSidebar";

const PatientClinicalLayout: React.FC = () => {
  const location = useLocation();
  const navigate = useNavigate();

  // Get the current tab from the URL path
  const getTabFromPath = (path: string) => {
    if (path === "/sponsor" || path === "/sponsor/") {
      return ""; // Default to Dashboard tab
    }
    const parts = path.split("/");
    return parts[parts.length - 1] || "";
  };

  const currentTab = getTabFromPath(location.pathname);

  const handleTabChange = (tab: string) => {
    navigate(`/sponsor${tab ? `/${tab}` : ""}`);
  };

  return (
    <div className="patclin-dashboard">
      <SponsorSidebar
        onTabChange={handleTabChange}
        currentTab={currentTab}
        notificationCount={0}
      />
      <main className="patclin-content">
        <div className="patclin-container">
          <Outlet />
        </div>
        <LightFooter />
      </main>
    </div>
  );
};

export default PatientClinicalLayout;
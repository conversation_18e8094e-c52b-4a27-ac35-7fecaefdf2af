import React, { useState } from "react";
import { motion } from "framer-motion";
import {
  User,
  Mail,
  Phone,
  Calendar,
  MapPin,
  UserCircle
} from "lucide-react";
import { useCurrentUserQuery } from "@/hooks/user.query";
import "./profile.css";

// Default Avatar SVG Component
const DefaultAvatar: React.FC<{ size?: number; className?: string }> = ({ size = 80, className = "" }) => (
  <svg
    width={size}
    height={size}
    viewBox="0 0 80 80"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    className={className}
  >
    <circle cx="40" cy="40" r="40" fill="#508C9B" />
    <circle cx="40" cy="32" r="12" fill="#EEEEEE" />
    <path
      d="M16 64C16 52.9543 24.9543 44 36 44H44C55.0457 44 64 52.9543 64 64V80H16V64Z"
      fill="#EEEEEE"
    />
  </svg>
);

// Avatar Component with Image Fallback
const PatientAvatar: React.FC<{ user: any; size?: number }> = ({ user, size = 80 }) => {
  const [imageError, setImageError] = useState(false);

  // Helper function to construct full image URL
  const getImageUrl = (imagePath: string | null): string | null => {
    if (!imagePath) return null;

    // If it's already a full URL, return as is
    if (imagePath.startsWith('http://') || imagePath.startsWith('https://')) {
      return imagePath;
    }

    // If it's a relative path, combine with base URL
    const baseURL = import.meta.env.VITE_API_BASE_URL || 'http://localhost:8000/api';
    const staticBaseURL = baseURL.replace('/api', '');
    return `${staticBaseURL}${imagePath}`;
  };

  // Use profile_picture field (the actual field from API) or fallback to image field
  const userImage = user.profile_picture || user.image;
  const fullImageUrl = getImageUrl(userImage);

  // If user has an image and no error occurred, show the image
  if (fullImageUrl && !imageError) {
    return (
      <div className="profile-avatar">
        <img
          src={fullImageUrl}
          alt={`${user.first_name} ${user.last_name}`}
          onError={() => setImageError(true)}
          style={{
            width: size,
            height: size,
            borderRadius: '50%',
            objectFit: 'cover',
            border: '3px solid #508C9B'
          }}
        />
      </div>
    );
  }

  // Fallback to default avatar
  return (
    <div className="profile-avatar">
      <DefaultAvatar size={size} />
    </div>
  );
};

const PatientProfile: React.FC = () => {
  const { data: currentUser, isLoading } = useCurrentUserQuery();

  if (isLoading) {
    return (
      <div className="profile-loading">
        <div className="loading-spinner"></div>
        <p>Loading profile...</p>
      </div>
    );
  }

  if (!currentUser) {
    return (
      <div className="profile-error">
        <p>Unable to load profile information.</p>
      </div>
    );
  }

  return (
    <motion.div
      className="profile-container"
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      transition={{ duration: 0.5 }}
    >
      {/* Profile Header */}
      <div className="profile-header">
        <div className="profile-header-content">
          <PatientAvatar user={currentUser} size={80} />
          <div className="profile-header-info">
            <h1 className="profile-name">
              {currentUser.first_name} {currentUser.last_name}
            </h1>
            <p className="profile-role">Patient</p>
            <div className="profile-status">
              <span className={`status-badge ${currentUser.is_active ? 'status-active' : 'status-inactive'}`}>
                {currentUser.is_active ? 'Active' : 'Inactive'}
              </span>
            </div>
          </div>
        </div>
      </div>

      {/* Profile Content */}
      <div className="profile-content">
        <div className="profile-sections">

          {/* Personal Information Section */}
          <div className="profile-section">
            <div className="section-header">
              <h2 className="section-title">Personal Information</h2>
            </div>
            <div className="section-content">
              <div className="info-grid">

                <div className="info-item">
                  <div className="info-label">
                    <User size={18} />
                    <span>First Name</span>
                  </div>
                  <div className="info-value">{currentUser.first_name || 'Not provided'}</div>
                </div>

                <div className="info-item">
                  <div className="info-label">
                    <User size={18} />
                    <span>Last Name</span>
                  </div>
                  <div className="info-value">{currentUser.last_name || 'Not provided'}</div>
                </div>

                <div className="info-item">
                  <div className="info-label">
                    <Mail size={18} />
                    <span>Email</span>
                  </div>
                  <div className="info-value">{currentUser.email || 'Not provided'}</div>
                </div>

                <div className="info-item">
                  <div className="info-label">
                    <Phone size={18} />
                    <span>Phone Number</span>
                  </div>
                  <div className="info-value">{currentUser.phone_number || 'Not provided'}</div>
                </div>

                <div className="info-item">
                  <div className="info-label">
                    <UserCircle size={18} />
                    <span>Gender</span>
                  </div>
                  <div className="info-value">
                    {currentUser.gender ?
                      currentUser.gender.charAt(0).toUpperCase() + currentUser.gender.slice(1).replace('_', ' ')
                      : 'Not provided'
                    }
                  </div>
                </div>

                <div className="info-item">
                  <div className="info-label">
                    <Calendar size={18} />
                    <span>Member Since</span>
                  </div>
                  <div className="info-value">
                    {new Date(currentUser.created_at).toLocaleDateString('en-US', {
                      year: 'numeric',
                      month: 'long',
                      day: 'numeric'
                    })}
                  </div>
                </div>

              </div>
            </div>
          </div>

          {/* Organization Information Section */}
          {(currentUser.hospital || currentUser.department) && (
            <div className="profile-section">
              <div className="section-header">
                <h2 className="section-title">Organization Information</h2>
              </div>
              <div className="section-content">
                <div className="info-grid">

                  {currentUser.hospital && (
                    <div className="info-item">
                      <div className="info-label">
                        <MapPin size={18} />
                        <span>Hospital</span>
                      </div>
                      <div className="info-value">{currentUser.hospital.name}</div>
                    </div>
                  )}

                  {currentUser.department && (
                    <div className="info-item">
                      <div className="info-label">
                        <MapPin size={18} />
                        <span>Department</span>
                      </div>
                      <div className="info-value">{currentUser.department.name}</div>
                    </div>
                  )}

                </div>
              </div>
            </div>
          )}

          {/* Account Information Section */}
          <div className="profile-section">
            <div className="section-header">
              <h2 className="section-title">Account Information</h2>
            </div>
            <div className="section-content">
              <div className="info-grid">

                <div className="info-item">
                  <div className="info-label">
                    <Calendar size={18} />
                    <span>Last Updated</span>
                  </div>
                  <div className="info-value">
                    {new Date(currentUser.updated_at).toLocaleDateString('en-US', {
                      year: 'numeric',
                      month: 'long',
                      day: 'numeric',
                      hour: '2-digit',
                      minute: '2-digit'
                    })}
                  </div>
                </div>

              </div>
            </div>
          </div>

        </div>
      </div>
    </motion.div>
  );
};

export default PatientProfile;

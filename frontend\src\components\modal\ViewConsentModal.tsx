import React from "react";
import { X, FileText, CheckCircle, XCircle, Calendar, Hash, Users } from "lucide-react";
import { ConsentForm } from "@/types/types";
import "./ViewConsentModal.css";

interface ViewConsentModalProps {
  isOpen: boolean;
  onClose: () => void;
  consent: ConsentForm;
}

const ViewConsentModal: React.FC<ViewConsentModalProps> = ({
  isOpen,
  onClose,
  consent,
}) => {
  if (!isOpen) return null;

  return (
    <div className="modal-overlay">
      <div className="view-consent-modal">
        <div className="modal-header">
          <div className="header-content">
            <h2>
              <FileText size={20} />
              Consent Form Details
            </h2>
            <div className="consent-meta">
              <span className="version-badge">v{consent.version}</span>
              <span className={`status-badge ${consent.is_active ? 'active' : 'inactive'}`}>
                {consent.is_active ? (
                  <>
                    <CheckCircle size={14} />
                    Active
                  </>
                ) : (
                  <>
                    <XCircle size={14} />
                    Inactive
                  </>
                )}
              </span>
            </div>
          </div>
          <button className="close-button" onClick={onClose}>
            <X size={20} />
          </button>
        </div>

        <div className="modal-content">
          {/* Basic Information */}
          <div className="info-section">
            <h3>Basic Information</h3>
            <div className="info-grid">
              <div className="info-item">
                <label>Consent Form Name</label>
                <span className="info-value">{consent.name}</span>
              </div>
              {consent.description && (
                <div className="info-item">
                  <label>Description</label>
                  <span className="info-value">{consent.description}</span>
                </div>
              )}
              <div className="info-item">
                <label>Study ID</label>
                <span className="info-value">{consent.study_id}</span>
              </div>
              <div className="info-item">
                <label>Created</label>
                <span className="info-value">
                  <Calendar size={14} />
                  {new Date(consent.created_at).toLocaleDateString()}
                </span>
              </div>
              <div className="info-item">
                <label>Last Updated</label>
                <span className="info-value">
                  <Calendar size={14} />
                  {new Date(consent.updated_at).toLocaleDateString()}
                </span>
              </div>
              {consent.source_consent_id && (
                <div className="info-item">
                  <label>Source Consent</label>
                  <span className="info-value">
                    <Hash size={14} />
                    {consent.source_consent_id}
                  </span>
                </div>
              )}
            </div>
          </div>

          {/* Questions Section */}
          <div className="questions-section">
            <h3>
              <Users size={18} />
              Consent Questions ({consent.questions?.length || 0})
            </h3>
            
            {consent.questions && consent.questions.length > 0 ? (
              <div className="questions-list">
                {consent.questions.map((question, index) => (
                  <div key={question.uuid} className="question-card">
                    <div className="question-header">
                      <div className="question-number">Q{index + 1}</div>
                      <div className="question-badges">
                        {question.is_required && (
                          <span className="required-badge">Required</span>
                        )}
                        <span className="sequence-badge">Order: {question.sequence}</span>
                      </div>
                    </div>
                    <div className="question-content">
                      <p className="question-text">{question.question_text}</p>
                      <div className="question-meta">
                        <span className="question-id">ID: {question.uuid}</span>
                        <span className="question-date">
                          Created: {new Date(question.created_at).toLocaleDateString()}
                        </span>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <div className="no-questions">
                <p>No questions have been added to this consent form.</p>
              </div>
            )}
          </div>

          {/* Audit Information */}
          <div className="audit-section">
            <h3>Audit Information</h3>
            <div className="audit-info">
              <div className="audit-item">
                <strong>Form UUID:</strong> {consent.uuid}
              </div>
              <div className="audit-item">
                <strong>Sponsor ID:</strong> {consent.sponsor_id}
              </div>
              <div className="audit-item">
                <strong>Immutable:</strong> 
                <span className="immutable-badge">Yes - Cannot be edited after creation</span>
              </div>
              <div className="audit-item">
                <strong>Version Control:</strong> 
                <span className="version-info">Auto-incremental versioning for audit trail</span>
              </div>
            </div>
          </div>
        </div>

        <div className="modal-footer">
          <button className="btn btn-secondary" onClick={onClose}>
            Close
          </button>
        </div>
      </div>
    </div>
  );
};

export default ViewConsentModal;
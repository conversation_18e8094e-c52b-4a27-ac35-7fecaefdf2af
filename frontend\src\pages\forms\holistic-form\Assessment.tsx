// src/Assessment.tsx
import { useState } from "react";
import { ChevronLeft, ChevronRight } from "lucide-react";
import AssessmentOverview from "./assessments/overview";
import Airway from "./assessments/airway";
import Breathing from "./assessments/breathing";
import Circulation from "./assessments/circulation";
import Disability from "./assessments/disability";
import Exposure from "./assessments/exposure";
import Pain from "./assessments/pain";
import SocialHx from "./assessments/social_hx";
import Line from "./assessments/line";
import Frailty from "./assessments/frailty";
import Psychology from "./assessments/psychology";
import Wound from "./assessments/wound";
import PointOfcareTesting from "./assessments/POCT";
import Event from "./assessments/event";
import Injury from "./assessments/injury";
import Properties from "./assessments/properties";
import Medication from "./assessments/medication";
import useHolisticFormStore from "@/store/holisticFormState";
import useHolisticFormTabStore from "@/store/holisticFormTabState";

const buttons = [
  "Overview",
  "Airway",
  "Breathing",
  "Circulation",
  "Disability",
  "Exposure",
  "Pain",
  "Social History",
  "Line",
  "Frailty",
  "Psychology",
  "Wound",
  "POCT",
  "Event",
  "Injury",
  "Properties",
  "Medication",
] as const;

const TOTAL_SUB_TABS = buttons.length; // 17 sub-tabs

const Assessment: React.FC = () => {
  const { assessment } = useHolisticFormStore();
  const { activeAssessmentSubTab, setActiveAssessmentSubTab, goToPrevious, goToNext } =
    useHolisticFormTabStore();
  const [startIndex, setStartIndex] = useState<number>(0);
  const buttonsPerPage = 9;

  const handleTabClick = (tabIndex: number) => {
    setActiveAssessmentSubTab(tabIndex);
  };

  const handlePrevSubTabs = () => {
    setStartIndex((prev) => Math.max(prev - buttonsPerPage, 0));
  };

  const handleNextSubTabs = () => {
    setStartIndex((prev) =>
      Math.min(prev + buttonsPerPage, buttons.length - buttonsPerPage)
    );
    console.log("Assessment Data (example):", assessment.injury);
  };

  const handlePrevious = () => {
    if (activeAssessmentSubTab === 1) {
      // On first sub-tab, go to previous main tab (Background)
      goToPrevious();
    } else {
      // Move to previous sub-tab within Assessment
      setActiveAssessmentSubTab(activeAssessmentSubTab - 1);
      // Adjust startIndex to keep the active sub-tab visible
      if (activeAssessmentSubTab - 1 < startIndex) {
        setStartIndex(Math.max(activeAssessmentSubTab - buttonsPerPage, 0));
      }
    }
  };

  const handleNext = () => {
    if (activeAssessmentSubTab === TOTAL_SUB_TABS) {
      // On last sub-tab, go to next main tab (Recommendation)
      goToNext();
    } else {
      // Move to next sub-tab within Assessment
      setActiveAssessmentSubTab(activeAssessmentSubTab + 1);
      // Adjust startIndex to keep the active sub-tab visible
      if (activeAssessmentSubTab >= startIndex + buttonsPerPage) {
        setStartIndex(
          Math.min(
            activeAssessmentSubTab - buttonsPerPage + 1,
            buttons.length - buttonsPerPage
          )
        );
      }
    }
  };

  return (
    <div className="tabs__pane -tab-item-1">
      <div className="tabs-wrapper">
        <div className="tabs__controls d-flex x-gap-50 y-gap-20 flex-wrap items-center justify-content-center">
          <button
            style={{
              backgroundColor: "#37B7C3",
              borderRadius: "50%",
              width: "40px",
              height: "40px",
              display: "flex",
              justifyContent: "center",
              alignItems: "center",
              fontWeight: "bold",
              color: "white",
              fontSize: "20px",
              marginTop: "5px",
              cursor: "pointer",
              transition: "background-color 0.3s, transform 0.3s",
            }}
            onClick={handlePrevSubTabs}
            disabled={startIndex === 0}
            onMouseEnter={(e) => (e.currentTarget.style.backgroundColor = "#2a9da0")}
            onMouseLeave={(e) => (e.currentTarget.style.backgroundColor = "#37B7C3")}
            onMouseDown={(e) => (e.currentTarget.style.transform = "scale(0.95)")}
            onMouseUp={(e) => (e.currentTarget.style.transform = "scale(1)")}
          >
            <ChevronLeft color="white" size={20} />
          </button>
          {buttons
            .slice(startIndex, startIndex + buttonsPerPage)
            .map((elm, i) => (
              <button
                key={i + startIndex}
                onClick={() => handleTabClick(i + startIndex + 1)}
                className={`tab-button ${
                  activeAssessmentSubTab === i + startIndex + 1 ? "active" : ""
                }`}
                style={{
                  padding: "10px 20px",
                  cursor: "pointer",
                }}
              >
                {elm}
              </button>
            ))}
          <button
            style={{
              backgroundColor: "#37B7C3",
              borderRadius: "50%",
              width: "40px",
              height: "40px",
              display: "flex",
              justifyContent: "center",
              alignItems: "center",
              fontWeight: "bold",
              color: "white",
              marginTop: "5px",
              fontSize: "20px",
              cursor: "pointer",
              transition: "background-color 0.3s, transform 0.3s",
            }}
            onClick={handleNextSubTabs}
            disabled={startIndex + buttonsPerPage >= buttons.length}
            onMouseEnter={(e) => (e.currentTarget.style.backgroundColor = "#2a9da0")}
            onMouseLeave={(e) => (e.currentTarget.style.backgroundColor = "#37B7C3")}
            onMouseDown={(e) => (e.currentTarget.style.transform = "scale(0.95)")}
            onMouseUp={(e) => (e.currentTarget.style.transform = "scale(1)")}
          >
            <ChevronRight color="white" size={20} />
          </button>
        </div>
      </div>

      <div className="col-12 mt-60">
        <div className="tabs__content pt-30">
          {activeAssessmentSubTab === 1 && <AssessmentOverview />}
          {activeAssessmentSubTab === 2 && <Airway />}
          {activeAssessmentSubTab === 3 && <Breathing />}
          {activeAssessmentSubTab === 4 && <Circulation />}
          {activeAssessmentSubTab === 5 && <Disability />}
          {activeAssessmentSubTab === 6 && <Exposure />}
          {activeAssessmentSubTab === 7 && <Pain />}
          {activeAssessmentSubTab === 8 && <SocialHx />}
          {activeAssessmentSubTab === 9 && <Line />}
          {activeAssessmentSubTab === 10 && <Frailty />}
          {activeAssessmentSubTab === 11 && <Psychology />}
          {activeAssessmentSubTab === 12 && <Wound />}
          {activeAssessmentSubTab === 13 && <PointOfcareTesting />}
          {activeAssessmentSubTab === 14 && <Event />}
          {activeAssessmentSubTab === 15 && <Injury />}
          {activeAssessmentSubTab === 16 && <Properties />}
          {activeAssessmentSubTab === 17 && <Medication />}
        </div>

        {/* Main navigation buttons with custom logic */}
        <div className="d-flex justify-content-between gap-3 mt-3">
          <button
            className="button -md btn-nurtify-lighter"
            style={{ width: "100px" }}
            onClick={handlePrevious}
          >
            Prev
          </button>
          <button
            className="button -md btn-nurtify text-white"
            style={{ width: "100px" }}
            onClick={handleNext}
          >
            Next
          </button>
        </div>
      </div>
    </div>
  );
};

export default Assessment;
.sidebar-dsbh-admin {
  width: auto;
  min-width: 280px;
  max-width: 320px;
  height: calc(100vh - 60px);
  background-color: #ffffff;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: sticky;
  top: 20px;
  z-index: 999;
  overflow-y: auto;
  border-radius: 0;
  box-shadow: 2px 0 8px rgba(0, 0, 0, 0.1);
  border-right: 1px solid #e9ecef;
  padding: 20px 20px 40px 20px;
  margin-top: 20px;
}

.sidebar-dsbh-admin.collapsed {
  width: 80px;
  min-width: 80px;
  max-width: 80px;
  padding: 20px 10px 40px 10px;
}

.sidebar-dsbh-admin.mobile {
  position: fixed;
  left: 0;
  transform: translateX(-100%);
  margin-top: 170px;
  height: calc(100vh - 170px);
  width: 80px;
  transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1),
              width 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.sidebar-dsbh-admin.mobile.active {
  transform: translateX(0);
  width: 280px;
}

.sidebar-dsbh-admin.mobile.active.collapsed {
  width: 80px;
}


/* Sidebar Header */
.sidebar-header {
  padding: 0 0 20px 0;
  border-bottom: 1px solid #e9ecef;
  display: flex;
  align-items: center;
  justify-content: flex-end;
  flex-shrink: 0;
}

.minimize-btn {
  display: flex;
  align-items: center;
  gap: 10px;
  padding: 10px 14px;
  background: none;
  border: none;
  color: #37b7c3;
  font-size: 15px;
  font-weight: 500;
  cursor: pointer;
  border-radius: 6px;
  transition: all 0.2s ease;
}

.minimize-btn:hover {
  background-color: #f0f9ff;
  color: #2563eb;
}

.minimize-btn.collapsed {
  justify-content: center;
  padding: 8px;
  margin: 0 auto;
}

.minimize-btn.collapsed .minimize-text {
  display: none;
}

.minimize-text {
  font-size: 13px;
  font-weight: 500;
}

.menuList {
  list-style: none;
  padding: 0;
  margin: 0;
  flex-grow: 1;
  overflow-y: auto;
  overflow-x: hidden;
  scrollbar-width: thin;
  scrollbar-color: rgba(255, 255, 255, 0.3) transparent;
}

.menuList::-webkit-scrollbar {
  width: 6px;
}

.menuList::-webkit-scrollbar-track {
  background: transparent;
  border-radius: 10px;
}

.menuList::-webkit-scrollbar-thumb {
  background-color: rgba(0, 0, 0, 0.2);
  border-radius: 10px;
}

/* Patient Info Section */
.patient-info-section {
  padding: 0 0 20px 0;
  border-bottom: 1px solid #e9ecef;
  margin-bottom: 20px;
  flex-shrink: 0;
}

.patient-header {
  display: flex;
  align-items: center;
  margin-bottom: 12px;
}

.patient-avatar {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  margin-right: 15px;
  overflow: hidden;
  background-color: #f8f9fa;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 2px solid #e9ecef;
}

.patient-avatar img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.avatar-placeholder {
  color: #6c757d;
}

.patient-basic-info {
  flex: 1;
}

.patient-name {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: #212529;
  line-height: 1.2;
}

.patient-demographics {
  margin: 2px 0 0 0;
  color: #6c757d;
  font-size: 14px;
  font-weight: 400;
}

.patient-details {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.detail-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 15px;
}

.detail-label {
  color: #6c757d;
  font-weight: 500;
  min-width: 70px;
  font-size: 15px;
}

.detail-value {
  color: #212529;
  font-weight: 400;
  text-align: right;
  flex: 1;
  margin-left: 8px;
  font-size: 15px;
}

.menuList li {
  margin-top: 15px;
  padding: 12px 18px;
  border-radius: 8px;
  margin-bottom: 6px;
  display: flex;
  align-items: center;
  gap: 14px;
  cursor: pointer;
  color: #495057;
  font-size: 16px;
  transition: all 0.2s ease;
  font-weight: 400;
  position: relative;
  border: 1px solid transparent;
}

.menuList li:hover {
  color: #37b7c3;
  background-color: #f8f9fa;
  border-color: #e9ecef;
  transform: translateX(2px);
}

.menuList li.active {
  background-color: #37b7c3;
  color: white;
  border-color: #37b7c3;
  box-shadow: 0 2px 8px rgba(55, 183, 195, 0.2);
}

.menuList li.active:hover {
  background-color: #2c929e;
  color: white;
  transform: translateX(2px);
}

.menuList li svg {
  flex-shrink: 0;
  width: 20px;
  height: 20px;
}

.menu-label {
  flex: 1;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  font-size: 16px;
}

.sidebar-dsbh-admin.collapsed .menuList li {
  justify-content: center;
  padding: 12px;
}

.sidebar-dsbh-admin.collapsed .menuList li .menu-label {
  display: none;
}

.sidebar-dsbh-admin.collapsed .menuList li svg {
  margin: 0;
}

.overlay {
  display: none;
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 999;
  transition: opacity 0.3s ease;
}

.overlay.active {
  display: block;
}

/* Close Patient Portal Button */
.close-patient-portal {
  margin: 0;
  padding: 12px 18px;
  background-color: transparent;
  border: 1px solid transparent;
  border-radius: 8px;
  color: #dc3545;
  font-size: 16px;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: flex-start;
  gap: 14px;
  font-weight: 400;
  flex-shrink: 0;
  width: 95%;
  position: relative;
}

.close-patient-portal:hover {
  background-color: #f8f9fa;
  color: #721c24;
  border-color: transparent;
  transform: translateX(2px);
}

.sidebar-dsbh-admin.collapsed .close-patient-portal {
  margin: 0;
  padding: 10px 8px;
  justify-content: center;
  gap: 0;
}

.sidebar-dsbh-admin.collapsed .close-patient-portal span {
  display: none;
}

.sidebar-dsbh-admin.collapsed .close-patient-portal svg {
  width: 20px;
  height: 20px;
}

/* Collapsible Menu Items */
.menuList li.has-subitems {
  position: relative;
}

.expand-arrow {
  background: none;
  border: none;
  color: inherit;
  cursor: pointer;
  padding: 4px;
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-left: auto;
  transition: all 0.2s ease;
}

.expand-arrow:hover {
  background-color: rgba(255, 255, 255, 0.1);
}

.menuList li.active .expand-arrow:hover {
  background-color: rgba(255, 255, 255, 0.2);
}

.menuList li:not(.active) .expand-arrow:hover {
  background-color: rgba(55, 183, 195, 0.1);
}

/* Submenu Styles */
.submenu {
  list-style: none;
  padding: 0;
  margin: 0;
  margin-left: 0;
  background-color: #f8f9fa;
  border-radius: 6px;
  margin-top: 4px;
  margin-bottom: 8px;
  overflow: hidden;
}

.submenu li.subitem {
  margin: 0;
  padding: 10px 18px 10px 45px;
  border-radius: 0;
  margin-bottom: 0;
  display: flex;
  align-items: center;
  gap: 12px;
  cursor: pointer;
  color: #6c757d;
  font-size: 15px;
  transition: all 0.2s ease;
  font-weight: 400;
  position: relative;
  border: none;
  border-left: 3px solid transparent;
}

.submenu li.subitem:hover {
  color: #37b7c3;
  background-color: #ffffff;
  border-left-color: #37b7c3;
  transform: translateX(2px);
}

.submenu li.subitem.active {
  background-color: #37b7c3;
  color: white;
  border-left-color: #2c929e;
}

.submenu li.subitem.active:hover {
  background-color: #2c929e;
  color: white;
  transform: translateX(2px);
}

.submenu li.subitem svg {
  flex-shrink: 0;
  width: 18px;
  height: 18px;
}

.submenu li.subitem .menu-label {
  flex: 1;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  font-size: 15px;
}

/* Collapsed state - hide subitems */
.sidebar-dsbh-admin.collapsed .submenu {
  display: none;
}

.sidebar-dsbh-admin.collapsed .expand-arrow {
  display: none;
}

@media (max-width: 991px) {
  .sidebar-dsbh-admin.mobile {
    position: fixed;
    left: 0;
    transform: translateX(-100%);
    margin-top: 90px;
    height: calc(100vh - 90px);
    width: 300px;
    transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    border-radius: 0 16px 16px 0;
  }

  .sidebar-dsbh-admin.mobile.active {
    transform: translateX(0);
  }
}

@media (max-width: 767px) {
  .sidebar-dsbh-admin.mobile.active {
    width: 80%;
  }

  .sidebar-dsbh-admin.mobile.active.collapsed {
    width: 80px;
  }
}

@media (max-width: 480px) {
  .sidebar-dsbh-admin.mobile {
    width: 280px;
  }

  .menuList li {
    padding: 12px 16px;
    font-size: 15px;
  }
}

.compact-patient-details {
  margin-top: 0;
  padding: 0;
  gap: 1px;
}

.compact-patient-details .detail-row {
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  align-items: center;
  font-size: 11px;
  margin-bottom: 1px;
  padding: 0;
}

.compact-patient-details .detail-label {
  color: #666;
  font-weight: 400;
  min-width: 70px;
  margin-right: 6px;
  text-align: left;
}

.compact-patient-details .detail-value {
  color: #333;
  font-weight: 400;
  text-align: left;
  margin-left: 0;
  padding-left: 0;
}

.nhs-link {
  color: #1890ff;
  text-decoration: underline;
  cursor: pointer;
}

.nhs-link:hover {
  color: #1765ad;
}

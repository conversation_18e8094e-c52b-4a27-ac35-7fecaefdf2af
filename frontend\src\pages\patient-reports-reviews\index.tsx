import React, { useState, useMemo, useCallback } from 'react';
import { motion } from 'framer-motion';
import { FileText, PlusCircle, Eye, Clock, CheckCircle2, AlertCircle, FileImage, FileVideo } from 'lucide-react';
import useSelectedPatientStore from '@/store/SelectedPatientState';
import { useReportsByPatientQuery, useCreateReportMutation, useReportTypesQuery, useSubInvestigatorsQuery } from '@/hooks/report.query';
import { useNavigate } from 'react-router-dom';
import { useQueryClient } from '@tanstack/react-query';
import './ReportsReviews.css';
import DataTable, { Column, Action } from "@/components/common/DataTable";
import NurtifyButton from "@/components/NurtifyButton";
import NurtifyFilter, { NurtifyFilterItem } from "@/components/NurtifyFilter";

const ReportsReviews: React.FC = () => {
  const { selectedPatient } = useSelectedPatientStore();
  const [showAddModal, setShowAddModal] = useState(false);
  const [page, setPage] = useState(1);
  const { data: reportsData, isLoading } = useReportsByPatientQuery(selectedPatient?.uuid || '', page);
  const createReportMutation = useCreateReportMutation();
  const { data: reportTypesData, isLoading: isLoadingTypes } = useReportTypesQuery();
  const { data: subInvestigators, isLoading: isLoadingSubInv } = useSubInvestigatorsQuery();
  const navigate = useNavigate();
  const queryClient = useQueryClient();

  // State for filtering
  const [selectedStatus, setSelectedStatus] = useState<string[]>([]);
  const [selectedType, setSelectedType] = useState<string[]>([]);

  // Modal state and handlers
  const [form, setForm] = useState<{
    type: string;
    content_type: 'TEXT' | 'PDF';
    text_content: string;
    exam_date: string;
    attach_content: File | undefined;
    sub_investigator?: string;
  }>({
    type: '',
    content_type: 'TEXT',
    text_content: '',
    exam_date: '',
    attach_content: undefined,
    sub_investigator: '',
  });
  const [formError, setFormError] = useState<string | null>(null);

  // Extract reports from the response
  const allPatientReports = useMemo(() => reportsData?.reports || [], [reportsData]);

  const totalReports = reportsData?.count || 0;
  const pageSize = 10; // Assuming backend default page size is 10
  const totalPages = Math.ceil(totalReports / pageSize);

  // Filter options for status
  const statusOptions = useMemo(() => [
    { label: 'Reviewed', value: 'REVIEWED' },
    { label: 'Not Reviewed', value: 'NOT_REVIEWED' },
  ], []);

  // Filter options for report types, derived from the reports data
  const typeOptions = useMemo(() => {
    const uniqueTypes = new Set(allPatientReports.map(report => report.type));
    return Array.from(uniqueTypes).map(type => ({ label: type, value: type }));
  }, [allPatientReports]);

  // Handler function for filter changes to bridge type mismatch
  const handleFilterChange = useCallback((filterKey: 'status' | 'type', value: string | string[]) => {
    if (Array.isArray(value)) {
      if (filterKey === 'status') {
        setSelectedStatus(value);
      } else if (filterKey === 'type') {
        setSelectedType(value);
      }
    }
  }, []);

  // Define the filters array for the NurtifyFilter component
  const filters: NurtifyFilterItem[] = useMemo(() => [
    {
      label: "Status",
      type: "checkbox",
      options: statusOptions,
      value: selectedStatus,
      onChange: (value: string | string[]) => handleFilterChange('status', value),
    },
    {
      label: "Report Type",
      type: "checkbox",
      options: typeOptions,
      value: selectedType,
      onChange: (value: string | string[]) => handleFilterChange('type', value),
    },
  ], [statusOptions, typeOptions, selectedStatus, selectedType, handleFilterChange]);

  // Filter reports based on selected filters
  const filteredReports = useMemo(() => {
    let filteredData = allPatientReports;

    if (selectedStatus.length > 0) {
      filteredData = filteredData.filter(report => selectedStatus.includes(report.status));
    }

    if (selectedType.length > 0) {
      filteredData = filteredData.filter(report => selectedType.includes(report.type));
    }

    return filteredData;
  }, [allPatientReports, selectedStatus, selectedType]);

  // Show message if no patient is selected
  if (!selectedPatient) {
    return (
      <motion.div
        className="reports-reviews-container"
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ duration: 0.5 }}
      >
        <div className="reports-reviews-header">
          <h2 className="reports-reviews-title">
            <FileText size={28} /> Reports / Reviews
          </h2>
        </div>
        <div className="reports-reviews-empty">
          <FileText size={48} />
          <p>Please select a patient to view their reports.</p>
        </div>
      </motion.div>
    );
  }

  const handleAddReport = () => {
    if (!selectedPatient) return;
    if (!form.type || !form.exam_date || (form.content_type === 'TEXT' && !form.text_content)) {
      setFormError('Please fill all required fields.');
      return;
    }
    const payload: any = {
      type: form.type,
      content_type: form.content_type,
      text_content: form.text_content,
      exam_date: form.exam_date,
      attach_content: form.attach_content,
      patient_uuid: selectedPatient.uuid,
    };
    if (form.sub_investigator !== '') {
      payload.sub_investigator = form.sub_investigator; // uuid
    }
    createReportMutation.mutate(
      payload,
      {
        onSuccess: () => {
          setShowAddModal(false);
          setForm({ type: '', content_type: 'TEXT', text_content: '', exam_date: '', attach_content: undefined, sub_investigator: '' });
          setFormError(null);
          // Invalidate all pages for this patient to ensure data consistency
          queryClient.invalidateQueries({
            queryKey: ['report/getByPatient', selectedPatient.uuid]
          });
          // Navigate to page 1 to show the newly added report
          setPage(1);
        },
        onError: (err: any) => {
          setFormError(err?.message || 'Failed to add report');
        },
      }
    );
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'REVIEWED':
        return <CheckCircle2 size={14} />;
      case 'NOT_REVIEWED':
        return <Clock size={14} />;
      default:
        return <AlertCircle size={14} />;
    }
  };

  const getContentTypeIcon = (contentType: string) => {
    switch (contentType) {
      case 'PDF':
        return <FileText size={14} />;
      case 'TEXT':
        return <FileImage size={14} />;
      default:
        return <FileVideo size={14} />;
    }
  };

  // DataTable columns definition
  const reportColumns: Column<any>[] = [
    {
      key: 'type',
      header: 'Type',
      sortable: true,
    },
    {
      key: 'content_type',
      header: 'Content Type',
      render: (_unused, row) => (
        <span className={`content-type-badge ${row.content_type.toLowerCase()}`}>
          {getContentTypeIcon(row.content_type)}
          {row.content_type}
        </span>
      ),
      sortable: true,
    },
    {
      key: 'status',
      header: 'Status',
      render: (_unused, row) => (
        <span className={`status-badge ${row.status === 'REVIEWED' ? 'reviewed' : 'not-reviewed'}`}>
          {getStatusIcon(row.status)}
          {row.status === 'REVIEWED' ? 'Reviewed' : 'Not Reviewed'}
        </span>
      ),
      sortable: true,
    },
    {
      key: 'exam_date',
      header: 'Exam Date',
      render: (value) => formatDate(value as string),
      sortable: true,
    },
    {
      key: 'uploaded_at',
      header: 'Uploaded At',
      render: (value) => formatDate(value as string),
      sortable: true,
    },
    {
      key: 'sub_investigator_name',
      header: 'Sub Investigator',
      render: (_value, row) =>
        row.sub_investigator_name
          ? `${row.sub_investigator_name} ${row.sub_investigator_last_name}`
          : 'Not assigned',
      sortable: true,
    },
  ];

  // DataTable actions definition
  const actions: Action<any>[] = [
    {
      icon: <Eye size={16} />,
      label: 'View Details',
      onClick: (row) => navigate(`/org/dashboard/patient-board/patient-report/${row.uuid}`),
      tooltipText: 'View Details',
    },
  ];

  return (
    <motion.div
      className="reports-reviews-container"
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      transition={{ duration: 0.5 }}
    >
      <div className="reports-reviews-header">
        <h2 className="reports-reviews-title">
          <FileText size={28} /> Reports / Reviews
          {reportsData?.patient && (
            <span className="patient-name">
              - {reportsData.patient.first_name} {reportsData.patient.last_name}
            </span>
          )}
        </h2>
        <button className="add-report-btn" onClick={() => setShowAddModal(true)}>
          <PlusCircle size={18} /> Add Report
        </button>
      </div>

      {/* Add Report Modal */}
      {showAddModal && (
        <div className="modal-overlay">
          <div className="modal-content">
            <h3>Add Report</h3>
            <label>Type*</label>
            <select
              value={form.type}
              onChange={e => setForm(f => ({ ...f, type: e.target.value }))}
              disabled={isLoadingTypes}
            >
              <option value="">Select type</option>
              {reportTypesData?.report_types?.map((type: string) => (
                <option key={type} value={type}>{type}</option>
              ))}
            </select>
            <label>Content Type*</label>
            <select value={form.content_type} onChange={e => setForm(f => ({ ...f, content_type: e.target.value as 'TEXT' | 'PDF' }))}>
              <option value="TEXT">Text</option>
              <option value="PDF">PDF</option>
            </select>
            {form.content_type === 'TEXT' ? (
              <>
                <label>Text Content*</label>
                <textarea value={form.text_content} onChange={e => setForm(f => ({ ...f, text_content: e.target.value }))} />
              </>
            ) : (
              <>
                <label>Attachment*</label>
                <div className="modal-file-upload-container">
                  <label htmlFor="modal-file-upload" className="modal-file-upload-label">
                    <svg
                      width="30"
                      height="30"
                      viewBox="0 0 24 24"
                      fill="none"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <path
                        d="M20 2H8C6.9 2 6 2.9 6 4V16C6 17.1 6.9 18 8 18H20C21.1 18 22 17.1 22 16V4C22 2.9 21.1 2 20 2ZM20 16H8V4H20V16ZM4 6H2V20C2 21.1 2.9 22 4 22H18V20H4V6ZM12 7H18V9H12V7ZM12 10H18V12H12V10ZM12 13H18V15H12V13ZM9 7H11V15H9V7Z"
                        fill="currentColor"
                      />
                    </svg>
                    {form.attach_content ? `File selected: ${form.attach_content.name}` : 'Click to attach PDF file'}
                  </label>
                  <input
                    type="file"
                    id="modal-file-upload"
                    accept="application/pdf"
                    onChange={e => setForm(f => ({ ...f, attach_content: e.target.files?.[0] }))}
                  />
                </div>
              </>
            )}
            <label>Exam Date*</label>
            <input type="date" value={form.exam_date} onChange={e => setForm(f => ({ ...f, exam_date: e.target.value }))} />
            <label>Sub Investigator</label>
            <select
              value={form.sub_investigator}
              onChange={e => setForm(f => ({ ...f, sub_investigator: e.target.value }))}
              disabled={isLoadingSubInv}
            >
              <option value="">Select sub investigator</option>
              {Array.isArray((subInvestigators as any)?.sub_investigators)
                ? (subInvestigators as any).sub_investigators.map((user: any) => (
                    <option key={user.identifier} value={user.identifier}>
                      {user.first_name} {user.last_name}
                    </option>
                  ))
                : null}
            </select>
            {formError && <div className="form-error">{formError}</div>}
            <div style={{ display: 'flex', justifyContent: 'flex-end', gap: '16px', marginTop: '24px' }}>
              <NurtifyButton size="small" variant="light" onClick={() => setShowAddModal(false)}>
                Cancel
              </NurtifyButton>
              <NurtifyButton size="medium" variant="primary" onClick={handleAddReport} disabled={createReportMutation.isPending}>
                Add
              </NurtifyButton>
            </div>
          </div>
        </div>
      )}

      {/* Reports Table or Empty State */}
      {isLoading ? (
        <div className="reports-reviews-loading">Loading reports...</div>
      ) : allPatientReports.length === 0 ? (
        <div className="reports-reviews-empty">
          <FileText size={48} />
          <p>No reports found for this patient. Click 'Add Report' to create one.</p>
        </div>
      ) : (
        <div className="reports-table-container">
          <div className="reports-count">
            Showing {filteredReports.length} report{filteredReports.length !== 1 ? 's' : ''} for this patient
          </div>
          
          <div style={{ paddingTop: "20px", paddingBottom: "5px", paddingLeft: "10px" }}>
            <NurtifyFilter layout="horizontal" filters={filters} />
            </div>
          
          <DataTable
            data={filteredReports}
            columns={reportColumns}
            actions={actions}
            noDataMessage="No reports found."
            globalFilterPlaceholder="Search reports..."
            defaultItemsPerPage={10}
          />
          {/* Pagination Controls */}
          {totalPages > 1 && (
            <div className="reports-pagination">
              <button
                className="pagination-btn"
                onClick={() => setPage(page - 1)}
                disabled={page === 1}
              >
                Prev
              </button>
              {Array.from({ length: totalPages }, (_, i) => i + 1).map((p) => (
                <button
                  key={p}
                  className={`pagination-btn${p === page ? ' active' : ''}`}
                  onClick={() => setPage(p)}
                  disabled={p === page}
                >
                  {p}
                </button>
              ))}
              <button
                className="pagination-btn"
                onClick={() => setPage(page + 1)}
                disabled={page === totalPages}
              >
                Next
              </button>
            </div>
          )}
        </div>
      )}
    </motion.div>
  );
};

export default ReportsReviews;

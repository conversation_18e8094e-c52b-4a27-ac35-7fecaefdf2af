import React from 'react';
import { useMyApprovedStudiesQuery } from '@/hooks/study.query';
import NurtifySelect from './NurtifySelect';

interface ApprovedStudySelectorProps {
  value: string;
  onChange: (value: string) => void;
}

const ApprovedStudySelector: React.FC<ApprovedStudySelectorProps> = ({ value, onChange }) => {
  const { data: studiesData, isLoading, isError } = useMyApprovedStudiesQuery();

  // Create options for the select component
  const options = React.useMemo(() => {
    const defaultOption = { value: '', label: 'Select a study' };

    if (!studiesData?.studies || isLoading || isError) {
      return [defaultOption];
    }

    return [
      defaultOption,
      ...studiesData.studies.map((study: any) => ({
        value: study.uuid,
        label: `${study.name} - ${study.iras}`
      }))
    ];
  }, [studiesData, isLoading, isError]);

  const handleChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    onChange(e.target.value);
  };

  return (
    <NurtifySelect
      name="approved-study"
      value={value}
      onChange={handleChange}
      options={options}
      disabled={isLoading || isError}
    />
  );
};

export default ApprovedStudySelector; 
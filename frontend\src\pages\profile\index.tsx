import "./profile.css";
import { useState, useEffect, useMemo } from "react";
import { useCurrentUserQuery, useUpdateUserMutation } from "@/hooks/user.query";
import { 
  useUserDocumentsQuery, 
  useUploadDocumentMutation, 
  useDeleteDocumentMutation, 
  useUpdateDocumentMutation,
  useDocumentTypesQuery,
  useExpiredDocumentsQuery,
  useDocumentsExpiringSoonQuery,
  useExpirySummaryQuery
} from "@/hooks/document.query";
import NurtifyInput from "@/components/NurtifyInput";
import NurtifySelect from "@/components/NurtifySelect";
import NurtifyDateInput from "@/components/NurtifyDateInput";
import NurtifyText from "@/components/NurtifyText";
import PasswordInput from "@/components/PasswordInput";
import NurtifyTabs from "@/components/NurtifyTabs"; // Import the new Tabs component
import { User as LucideUser } from "lucide-react";
import { User } from "@/types/types";
import { UserDocument } from "@/services/api/document.service";
import { toast } from "sonner";

//import { CountrySelect, StateSelect, CitySelect, GetCountries } from "react-country-state-city";
import { CountrySelect, StateSelect, CitySelect, GetCountries, GetState } from "react-country-state-city";
import "react-country-state-city/dist/react-country-state-city.css";


type Country = {
  id: number;
  name: string;
  iso2: string;
};

type State = {
  id: number;
  name: string;
};

// Extended user interface to include additional properties from the API
interface ExtendedUser extends Omit<User, 'organization_name'> {
  birth_date?: string;
  primary_address?: string;
  secondary_address?: string;
  city?: string;
  state?: string;
  country?: string | null;
  expary_date?: string;
  organization_name?: string | null;
  is_active?: boolean;
  is_staff?: boolean;
  is_superuser?: boolean;
  is_admin?: boolean;
}

// API payload interface for updating user
interface UserUpdatePayload {
  department?: {
    name: string;
  };
  first_name: string;
  last_name: string;
  phone_number: string;
  gender: string;
  birth_date: string;
  primary_address: string;
  secondary_address: string;
  city: string;
  state: string;
  country: string;
  registration_body: string;
  expary_date: string;
  organization_name: string;
  speciality: string;
  email?: string;
  identifier?: string;
  is_active?: boolean;
  is_staff?: boolean;
  is_superuser?: boolean;
  is_admin?: boolean;
}

// Function to check if all required fields are filled
const isProfileComplete = (user: ExtendedUser | undefined): boolean => {
  if (!user) return false;
  
  return !!(
    user.first_name &&
    user.last_name &&
    user.phone_number &&
    user.birth_date &&
    user.gender &&
    user.primary_address &&
    user.city &&
    user.state &&
    user.country &&
    user.registration_body &&
    user.expary_date &&
    user.organization_name &&
    user.speciality
  );
};

export default function Profile() {
  const [activeTab, setActiveTab] = useState<string>("personal"); // Use string for activeTab ID
  const [isEditing, setIsEditing] = useState(true);
  const [currentPassword, setCurrentPassword] = useState("");
  const [newPassword, setNewPassword] = useState("");
  const [confirmPassword, setConfirmPassword] = useState("");
  const [passwordError, setPasswordError] = useState("");
  const [formError, setFormError] = useState<string>("");
  
  // Document management state
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [documentType, setDocumentType] = useState<string>("");
  const [customDocumentName, setCustomDocumentName] = useState<string>("");
  const [documentExpiryDate, setDocumentExpiryDate] = useState<string>("");
  const [isUpdating, setIsUpdating] = useState<boolean>(false);
  const [updatingDocumentId, setUpdatingDocumentId] = useState<string | null>(null);
  
  // TanStack Query hooks for document management
  const { data: documents, isLoading: documentsLoading, error: documentsError } = useUserDocumentsQuery();
  const { data: documentTypes } = useDocumentTypesQuery();
  const { data: expiredDocuments } = useExpiredDocumentsQuery();
  const { data: expiringSoonDocuments } = useDocumentsExpiringSoonQuery();
  const { data: expirySummary } = useExpirySummaryQuery();
  const uploadDocumentMutation = useUploadDocumentMutation();
  const deleteDocumentMutation = useDeleteDocumentMutation();
  const updateDocumentMutation = useUpdateDocumentMutation();
  
  // Extract documents array from response (handle different API response structures)
  const documentsArray = useMemo(() => {
    return Array.isArray(documents) 
      ? documents 
      : (documents as any)?.results || (documents as any)?.documents || (documents as any)?.data || [];
  }, [documents]);
  
  // Get uploaded document types to disable them in the dropdown
  const uploadedDocumentTypes = documentsArray
    .map((doc: UserDocument) => doc.document_type)
    .filter((type: string) => type !== "Other"); // Exclude "Other" from duplicate prevention
  
  // Debug logging
  useEffect(() => {
    console.log("Documents query state:", {
      documents,
      documentsLoading,
      documentsError,
      documentsCount: documents?.length,
      documentsType: typeof documents,
      documentsKeys: documents ? Object.keys(documents) : null,
      documentsStringified: documents ? JSON.stringify(documents, null, 2) : null,
      documentsArray,
      documentsArrayLength: documentsArray?.length
    });
  }, [documents, documentsLoading, documentsError, documentsArray]);
  
  // Form state variables
  const [firstName, setFirstName] = useState<string>("");
  const [lastName, setLastName] = useState<string>("");
  const [phoneNumber, setPhoneNumber] = useState<string>("");
  const [birthDate, setBirthDate] = useState<string>("");
  const [gender, setGender] = useState<string>("");
  const [primaryAddress, setPrimaryAddress] = useState<string>("");
  const [secondaryAddress, setSecondaryAddress] = useState<string>("");
  const [city, setCity] = useState<string>("");
  const [state, setState] = useState<string>("");
  const [country, setCountry] = useState<string>("");
  const [registrationNumber, setRegistrationNumber] = useState<string>("");
  const [expiryDate, setExpiryDate] = useState<string>("");
  const [organization, setOrganization] = useState<string>("");
  const [speciality, setSpeciality] = useState<string>("");
  
  // Fetch current user data
  const { data: currentUser, isLoading } = useCurrentUserQuery();
  const extendedUser = currentUser as ExtendedUser;
  const updateUserMutation = useUpdateUserMutation();

  const [selectedCountry, setSelectedCountry] = useState<Country | null>(null);
  const [selectedState, setSelectedState] = useState<State | null>(null);
  const [selectedCity, setSelectedCity] = useState<State | null>(null);

  const [stateIsCustom, setStateIsCustom] = useState(false);
  const [cityIsCustom, setCityIsCustom] = useState(false);

  const fetchAndSetCountry = async (country: string, state: string) => {
  try {
    const countries = await GetCountries();

    const matchedCountry = countries.find(
      (c: Country) => c.name.toLowerCase() === country?.toLowerCase()
    );

    if (matchedCountry) {
      setSelectedCountry({ id: matchedCountry.id, name: matchedCountry.name, iso2: matchedCountry.iso2 });
      setCountry(matchedCountry.name);
      
      const states = await GetState(matchedCountry.id);
      const matchedState = states.find(
        (s: State) => s.name.toLowerCase() === state?.toLowerCase()
      );

      if (matchedState) {
        setSelectedState({ id: matchedState.id, name: matchedState.name });
        setStateIsCustom(false);
        setState(matchedState.name);
      } else {
        setSelectedState(null); // No match found in list, treat as custom
        setStateIsCustom(true);
        setState(state || "England");
      }
    } else {
      setSelectedCountry(null); // No match found, treat as custom
      setCountry(country || "United Kingdom");
      setSelectedState(null);
      setStateIsCustom(true);
      setState(state || "England");
    }

  } catch (error) {
    console.error("Error fetching countries:", error);
    setSelectedCountry(null);
   
    setCountry(country || "United Kingdom");
    setSelectedState(null);
    setStateIsCustom(true);
    setState(state || "England");
  }
};
  
  // Initialize form state with user data when it's loaded
  useEffect(() => {
    if (currentUser) {
      setFirstName(currentUser.first_name || "");
      setLastName(currentUser.last_name || "");
      setPhoneNumber(currentUser.phone_number || "");
      setBirthDate(extendedUser?.birth_date || "");
      setGender(currentUser.gender || "");
      setPrimaryAddress(extendedUser?.primary_address || "");
      setCountry(extendedUser?.country?.toString() || "United Kingdom");
      setState(extendedUser?.state || "England");
      setState(extendedUser?.state || "");
      setCountry(extendedUser?.country?.toString() || "");
      setRegistrationNumber(currentUser.registration_body || "");
      setExpiryDate(extendedUser?.expary_date || "");
      setOrganization(extendedUser?.organization_name || "");
      setSpeciality(currentUser.speciality || "");
      
      fetchAndSetCountry(extendedUser?.country?.toString() || "United Kingdom", extendedUser?.state || "England");
      console.log("User data loaded:", currentUser);
    }
  }, [currentUser, extendedUser]);
  
  // Check if profile is complete and redirect if not
  useEffect(() => {
    if (!isLoading && currentUser && !isProfileComplete(extendedUser)) {
      // If user is logged in but profile is incomplete, show message
      setFormError("Please complete your profile information to access other features");
    }
  }, [currentUser, isLoading, extendedUser]);
  // Gender options based on backend choices
  const genderOptions = [
    { value: "Male", label: "Male" },
    { value: "Female", label: "Female" },
    { value: "Prefer_not_to_disclose", label: "Prefer not to disclose" },
  ];
  
  // Speciality options based on backend choices
  const specialityOptions = [
    { value: "Registered Nurse", label: "Registered Nurse" },
    { value: "Senior Registered Nurse", label: "Senior Registered Nurse" },
    { value: "Doctor", label: "Doctor" },
    { value: "SpR Doctor", label: "SpR Doctor" },
    { value: "Consultant", label: "Consultant" },
    { value: "Professor", label: "Professor" },
    { value: "Pharmacist", label: "Pharmacist" },
    { value: "Senior Pharmacist", label: "Senior Pharmacist" },
    { value: "Clinical Research Practitioner", label: "Clinical Research Practitioner" },
    { value: "Advanced Clinical Practitioner", label: "Advanced Clinical Practitioner" },
    { value: "Clinical Research Associate", label: "Clinical Research Associate" },
    { value: "Primary Investigator", label: "Primary Investigator" },
    { value: "Sub-Investigator", label: "Sub-Investigator" },
    { value: "Lab Technician", label: "Lab Technician" },
    { value: "Receptionist", label: "Receptionist" },
    { value: "Data Manager", label: "Data Manager" },
    { value: "Matron", label: "Matron" },
    { value: "Senior Matron", label: "Senior Matron" },
    { value: "Lead Nurse", label: "Lead Nurse" },
    { value: "Ward Manager", label: "Ward Manager" },
    { value: "Charge Nurse", label: "Charge Nurse" },
    { value: "Clinical Practice Educator", label: "Clinical Practice Educator" },
    { value: "Trial Manager", label: "Trial Manager" },
    { value: "CRF Manager", label: "CRF Manager" },
    { value: "Study Coordinator", label: "Study Coordinator" },
    { value: "Portfolio Manager", label: "Portfolio Manager" },
    { value: "Other", label: "Other, please specify" },
  ];

  // Document type options
  const documentTypeOptions = [
    { value: "", label: "Select document type" },
    { value: "CV", label: "CV" },
    { value: "IATA", label: "IATA" },
    { value: "GCP", label: "GCP" },
    { value: "Rave", label: "Rave" },
    { value: "Other", label: "Other" },
  ];

  // Use API document types if available, otherwise fall back to hardcoded options
  const finalDocumentTypeOptions = documentTypes && Array.isArray(documentTypes)
    ? documentTypes.map(type => ({ value: type, label: type }))
    : documentTypeOptions;

  // Filter document type options to disable already uploaded types
  const getFilteredDocumentTypeOptions = () => {
    if (!finalDocumentTypeOptions) return [];
    
    return finalDocumentTypeOptions.map(option => ({
      ...option,
      disabled: uploadedDocumentTypes.includes(option.value) && !isUpdating
    }));
  };
  
  const filteredDocumentTypeOptions = getFilteredDocumentTypeOptions();

  const handleCountrySelectChange = (val: Country | null) => {
  setSelectedCountry(val);
  setCountry(val ? val.name : "");
  }

  const handleStateSelectChange = (val: Country | null) => {
  setSelectedState(val);
  setState(val ? val.name : "");
  }

  const handleCitySelectChange = (val: Country | null) => {
  setSelectedCity(val);
  setCity(val ? val.name : "");
  }

  const handleUpdateClick = () => {
    setIsEditing(true);
  };

  const handleCancelClick = () => {
    setIsEditing(false);
  };

  const handleSaveClick = (e: React.MouseEvent<HTMLButtonElement>) => {
    e.preventDefault(); // Prevent form submission
    setFormError("");
    
    console.log("Save button clicked");
    
    // Validate required fields
    if (!firstName || !lastName || !phoneNumber ||
        !birthDate || !gender || !primaryAddress ||
         !state || !country ||
        !registrationNumber || !expiryDate || 
        !organization || !speciality) {
      console.log("Validation failed - missing required fields");
      setFormError("All fields marked with * are required");
      return;
    }
    
    // Validate birth date - user must be at least 10 years old
    const birthDateObj = new Date(birthDate);
    const today = new Date();
    const minAgeDate = new Date(today.getFullYear() - 10, today.getMonth(), today.getDate());
    
    if (birthDateObj > minAgeDate) {
      console.log("Validation failed - user must be at least 10 years old");
      setFormError("Users must be at least 10 years old or above.");
      return;
    }
    
    // Prepare data for update according to API format
    const userData: UserUpdatePayload = {
      department: currentUser?.department ? {
        name: currentUser.department.name
      } : undefined,
      first_name: firstName,
      last_name: lastName,
      phone_number: phoneNumber,
      gender: gender,
      birth_date: birthDate,
      primary_address: primaryAddress,
      secondary_address: secondaryAddress,
      city: city,
      state: state,
      country: country,
      registration_body: registrationNumber,
      expary_date: expiryDate,
      organization_name: organization,
      speciality: speciality,
      // Keep existing values for fields we don't want to change
      email: currentUser?.email,
      identifier: currentUser?.identifier,
      is_active: extendedUser?.is_active,
      is_staff: extendedUser?.is_staff,
      is_superuser: extendedUser?.is_superuser,
      is_admin: extendedUser?.is_admin,
    };
    
    console.log("User data to update:", userData);
    
    // Call API to update user
    if (currentUser?.identifier) {
      console.log("Calling updateUserMutation with identifier:", currentUser.identifier);
      
      // Use type assertion to satisfy TypeScript
      updateUserMutation.mutate(
        { uuid: currentUser.identifier, data: userData as unknown as Partial<User> },
        {
          onSuccess: (data) => {
            console.log("Update successful:", data);
            setIsEditing(false);
            // Show success message
            //alert("Profile updated successfully");
            toast.success("Success! Profile updated.")
          },
          onError: (error) => {
            console.error("Failed to update profile:", error);
            setFormError("Failed to update profile. Please try again.");
          }
        }
      );
    } else {
      console.error("Cannot update: currentUser.identifier is undefined");
      setFormError("Cannot update profile: User ID is missing");
    }
  };

  const handleChangePassword = (e: React.MouseEvent<HTMLButtonElement>) => {
    e.preventDefault();
    setPasswordError("");

    // Validate passwords
    if (!currentPassword || !newPassword || !confirmPassword) {
      setPasswordError("All fields are required");
      return;
    }

    if (newPassword !== confirmPassword) {
      setPasswordError("New passwords do not match");
      return;
    }

    // Check password criteria
    const hasLetter = /[A-Za-z]/.test(newPassword);
    const hasNumber = /\d/.test(newPassword);
    const hasSpecialChar = /[@$!%*#?&]/.test(newPassword);
    const hasMinLength = newPassword.length >= 8;

    if (!(hasLetter && hasNumber && hasSpecialChar && hasMinLength)) {
      setPasswordError("Password must meet all requirements");
      return;
    }

    // TODO: Call API to change password
    console.log("Changing password...");
    
    // Reset form after successful password change
    setCurrentPassword("");
    setNewPassword("");
    setConfirmPassword("");
    
    // Show success message (in a real app, this would be after API call success)
    alert("Password changed successfully");
  };

  const handleDocumentUpload = (e: React.MouseEvent<HTMLButtonElement>) => {
    e.preventDefault();
    
    // Validate form
    if (!selectedFile) {
      alert("Please select a file to upload");
      return;
    }
    
    if (!documentType) {
      alert("Please select a document type");
      return;
    }
    
    if (documentType === "Other" && !customDocumentName.trim()) {
      alert("Please specify the document name");
      return;
    }
    
    // Validate expiry date if provided
    if (documentExpiryDate) {
      const expiryDate = new Date(documentExpiryDate);
      const today = new Date();
      today.setHours(0, 0, 0, 0); // Reset time to start of day for comparison
      
      if (expiryDate < today) {
        alert("Expiry date cannot be in the past");
        return;
      }
    }
    
    // Validate file size (10MB limit)
    const maxSize = 10 * 1024 * 1024; // 10MB in bytes
    if (selectedFile.size > maxSize) {
      alert("File size must be less than 10MB");
      return;
    }
    
    // Check if document type is already uploaded (exclude "Other" type)
    if (documentType !== "Other" && uploadedDocumentTypes.includes(documentType)) {
      alert(`A ${documentType} document is already uploaded. Please update the existing document instead.`);
      return;
    }
    
    // Prepare payload for API
    const payload = {
      file: selectedFile,
      document_type: documentType,
      ...(documentType === "Other" && { document_name: customDocumentName }),
      ...(documentExpiryDate && { expiry_date: documentExpiryDate })
    };
    
    // Upload document using API
    uploadDocumentMutation.mutate(payload, {
      onSuccess: () => {
        // Reset form
        setSelectedFile(null);
        setDocumentType("");
        setCustomDocumentName("");
        setDocumentExpiryDate("");
        
        // Clear file input
        const fileInput = document.querySelector('input[type="file"]') as HTMLInputElement;
        if (fileInput) {
          fileInput.value = "";
        }
        
        alert("Document uploaded successfully!");
      },
      onError: (error: any) => {
        console.error("Upload failed:", error);
        
        // Handle specific API error messages
        let errorMessage = "Failed to upload document. Please try again.";
        
        if (error.response?.data) {
          const errorData = error.response.data;
          
          // Handle document type already exists error
          if (errorData.document_type && Array.isArray(errorData.document_type)) {
            errorMessage = errorData.document_type[0];
          }
          // Handle file validation errors
          else if (errorData.file && Array.isArray(errorData.file)) {
            errorMessage = errorData.file[0];
          }
          // Handle document name validation errors
          else if (errorData.document_name && Array.isArray(errorData.document_name)) {
            errorMessage = errorData.document_name[0];
          }
          // Handle expiry date validation errors
          else if (errorData.expiry_date && Array.isArray(errorData.expiry_date)) {
            errorMessage = errorData.expiry_date[0];
          }
          // Handle general validation errors
          else if (typeof errorData === 'object') {
            const firstError = Object.values(errorData)[0];
            if (Array.isArray(firstError)) {
              errorMessage = firstError[0];
            }
          }
        }
        
        alert(errorMessage);
      }
    });
  };

  const handleUpdateDocument = (documentId: string) => {
    // Find the document to update
    const documentToUpdate = documentsArray.find((doc: UserDocument) => doc.uuid === documentId);
    if (!documentToUpdate) {
      alert("Document not found");
      return;
    }
    
    // Set form state for update
    setSelectedFile(null);
    setDocumentType(documentToUpdate.document_type);
    setCustomDocumentName(documentToUpdate.document_name || "");
    setDocumentExpiryDate(documentToUpdate.expiry_date || "");
    setIsUpdating(true);
    setUpdatingDocumentId(documentId);
  };

  const handleUpdateSubmit = (e: React.MouseEvent<HTMLButtonElement>) => {
    e.preventDefault();
    
    if (!updatingDocumentId) {
      alert("No document selected for update");
      return;
    }
    
    if (!documentType) {
      alert("Please select a document type");
      return;
    }
    
    if (documentType === "Other" && !customDocumentName.trim()) {
      alert("Please specify the document name");
      return;
    }
    
    // Validate expiry date if provided
    if (documentExpiryDate) {
      const expiryDate = new Date(documentExpiryDate);
      const today = new Date();
      today.setHours(0, 0, 0, 0); // Reset time to start of day for comparison
      
      if (expiryDate < today) {
        alert("Expiry date cannot be in the past");
        return;
      }
    }
    
    // Prepare payload for update
    const payload: any = {
      document_type: documentType,
      ...(documentType === "Other" && { document_name: customDocumentName }),
      ...(documentExpiryDate && { expiry_date: documentExpiryDate })
    };
    
    // Add file if selected
    if (selectedFile) {
      // Validate file size (10MB limit)
      const maxSize = 10 * 1024 * 1024; // 10MB in bytes
      if (selectedFile.size > maxSize) {
        alert("File size must be less than 10MB");
        return;
      }
      payload.file = selectedFile;
    }
    
    // Update document using API
    updateDocumentMutation.mutate(
      { uuid: updatingDocumentId, payload },
      {
        onSuccess: () => {
          // Reset form
          setSelectedFile(null);
          setDocumentType("");
          setCustomDocumentName("");
          setDocumentExpiryDate("");
          setIsUpdating(false);
          setUpdatingDocumentId(null);
          
          // Clear file input
          const fileInput = document.querySelector('input[type="file"]') as HTMLInputElement;
          if (fileInput) {
            fileInput.value = "";
          }
          
          alert("Document updated successfully!");
        },
        onError: (error: any) => {
          console.error("Update failed:", error);
          
          // Handle specific API error messages
          let errorMessage = "Failed to update document. Please try again.";
          
          if (error.response?.data) {
            const errorData = error.response.data;
            
            // Handle file validation errors
            if (errorData.file && Array.isArray(errorData.file)) {
              errorMessage = errorData.file[0];
            }
            // Handle document name validation errors
            else if (errorData.document_name && Array.isArray(errorData.document_name)) {
              errorMessage = errorData.document_name[0];
            }
            // Handle expiry date validation errors
            else if (errorData.expiry_date && Array.isArray(errorData.expiry_date)) {
              errorMessage = errorData.expiry_date[0];
            }
            // Handle general validation errors
            else if (typeof errorData === 'object') {
              const firstError = Object.values(errorData)[0];
              if (Array.isArray(firstError)) {
                errorMessage = firstError[0];
              }
            }
          }
          
          alert(errorMessage);
        }
      }
    );
  };

  const handleCancelUpdate = () => {
    setSelectedFile(null);
    setDocumentType("");
    setCustomDocumentName("");
    setDocumentExpiryDate("");
    setIsUpdating(false);
    setUpdatingDocumentId(null);
    
    // Clear file input
    const fileInput = document.querySelector('input[type="file"]') as HTMLInputElement;
    if (fileInput) {
      fileInput.value = "";
    }
  };

  const handleDeleteDocument = (documentId: string) => {
    if (window.confirm("Are you sure you want to delete this document?")) {
      deleteDocumentMutation.mutate(documentId, {
        onSuccess: () => {
          alert("Document deleted successfully!");
        },
        onError: (error) => {
          console.error("Delete failed:", error);
          alert("Failed to delete document. Please try again.");
        }
      });
    }
  };

  return (
    <div className="profile-dashboard__content">
      <div className="profile-card">
        <div className="profile-header">
          <h1 className="profile-title">
            <LucideUser size={24} style={{ marginRight: "10px", verticalAlign: "middle" }} />
            My Profile
          </h1>
        </div>

        {/* Replace buttons with NurtifyTabs component */}
        <NurtifyTabs
          tabs={[
            { id: "personal", label: "Personal Details" },
            { id: "professional", label: "Professional Details" },
            { id: "documents", label: "My Documents" },
            { id: "password", label: "Password" },
          ]}
          activeTab={activeTab}
          onTabChange={setActiveTab}
          className="profile-tabs-override" // Optional: Add specific class if needed for profile page overrides
        />

        <div className="profile-form-section">
            {formError && (
              <div className="alert alert-danger mb-4" role="alert">
                {formError}
              </div>
            )}
            {activeTab === "personal" && (
              <form>
                <div className="row y-gap-30">
                  <div className="col-md-6">
                    <NurtifyText label="First Name*" />
                    <NurtifyInput 
                      type="text" 
                      value={firstName} 
                      onChange={(e) => setFirstName(e.target.value)}
                      disabled={!isEditing}
                    />
                  </div>
                  <div className="col-md-6">
                    <NurtifyText label="Last Name*" />
                    <NurtifyInput 
                      type="text" 
                      value={lastName} 
                      onChange={(e) => setLastName(e.target.value)}
                      disabled={!isEditing}
                    />
                  </div>
                  <div className="col-md-6">
                    <NurtifyText label="Phone*" />
                    <NurtifyInput 
                      type="text" 
                      value={phoneNumber} 
                      onChange={(e) => setPhoneNumber(e.target.value)}
                      disabled={!isEditing}
                    />
                  </div>
                  <div className="col-md-6">
                    <NurtifyText label="Birthday*" />
                    <NurtifyDateInput 
                      value={birthDate}
                      onChange={(e) => setBirthDate(e.target.value)}
                    />
                  </div>
                  <div className="col-md-6">
                    <NurtifyText label="Gender*" />
                    <NurtifySelect 
                      options={genderOptions} 
                      value={gender} 
                      onChange={(e) => setGender(e.target.value)}
                      disabled={!isEditing}
                    />
                  </div>
                  <div className="col-md-6">
                    <NurtifyText label="Email" />
                    <NurtifyInput 
                      type="email" 
                      value={currentUser?.email || ""} 
                      disabled={true}
                    />
                  </div>
                  <div className="col-md-6">
                    <NurtifyText label="Address Line 1*" />
                    <NurtifyInput 
                      type="text" 
                      value={primaryAddress} 
                      onChange={(e) => setPrimaryAddress(e.target.value)}
                      disabled={!isEditing}
                    />
                  </div>
                  <div className="col-md-6">
                    <NurtifyText label="Address Line 2" />
                    <NurtifyInput 
                      type="text" 
                      value={secondaryAddress} 
                      onChange={(e) => setSecondaryAddress(e.target.value)}
                      disabled={!isEditing}
                    />
                  </div>
                 
            <div className="col-md-6">
                    <NurtifyText label="Country*" />
                    <CountrySelect
                      containerClassName="form-group"
                      inputClassName=""
                      defaultValue={selectedCountry as any}
                      value={selectedCountry as any}
                      onChange={handleCountrySelectChange as any}
                      placeHolder="Select Country"
                      disabled={!isEditing}
                      required
                    />
                  </div>

          

        
        <div className="col-md-6">
          <NurtifyText label="State*" />
          {stateIsCustom ? (
            <NurtifyInput
              type="text"
              value={state}
              onChange={(e) => setState(e.target.value)}
              disabled={!isEditing}
              required
            />
          ) : (
            <StateSelect
              countryid={selectedCountry?.id || 232}
              containerClassName="form-group"
              inputClassName=""
              onChange={(val: any) => {
                if (val && val.name === "Other") {
                  setStateIsCustom(true);
                  setState("");
                  setSelectedState(null);
                } else {
                  handleStateSelectChange(val);
                }
              }}
              defaultValue={selectedState as any}
              placeHolder="Select State"
              disabled={!isEditing || !selectedCountry}
            />
          )}
          {stateIsCustom ? (
            <div className="d-flex align-items-center mt-2 px-1">
              <button type="button" onClick={() => setStateIsCustom(false)} className="btn btn-link p-xl-0 text-decoration-underline text-muted">
                Select from list
              </button>
            </div>
          ) : (
            <div className="d-flex align-items-center mt-0 px-3">
              <button 
                type="button" 
                onClick={() => {
                  setStateIsCustom(true);
                  setState(""); 
                }} 
                className="btn btn-link p-xl-0 text-decoration-underline text-muted"
              >
                Can't find your state? Type it in.
              </button>
            </div>
          )}
        </div>
        
        <div className="col-md-6">
          <NurtifyText label="City" />
          {cityIsCustom ? (
            <NurtifyInput
              type="text"
              value={city}
              onChange={(e) => setCity(e.target.value)}
              disabled={!isEditing}
            />
          ) : (
            <CitySelect
              countryid={selectedCountry?.id || 232}
              stateid={selectedState?.id || 0}
              containerClassName="form-group"
              inputClassName=""
              onChange={(val: any) => {
                if (val && val.name === "Other") {
                  setCityIsCustom(true);
                  setCity("");
                  setSelectedCity(null);
                } else {
                  handleCitySelectChange(val);
                }
              }}
              defaultValue={selectedCity as any}
              disabled={!isEditing || !selectedCountry || !selectedState}
              placeHolder="Select City"
            />
          )}
          {cityIsCustom ? (
            <div className="d-flex align-items-center mt-2 px-1">
              <button type="button" onClick={() => setCityIsCustom(false)} className="btn btn-link p-xl-0 text-decoration-underline text-muted">
                Select from list
              </button>
            </div>
          ) : (
            <div className="d-flex align-items-center mt-0 px-3">
              <button 
                type="button" 
                onClick={() => {
                  setCityIsCustom(true);
                  setCity(""); // Clear the city state when switching to custom input
                }} 
                className="btn btn-link p-xl-0 text-decoration-underline text-muted"
              >
                Can't find your city? Type it in.
              </button>
            </div>
          )}
        </div>
      </div>
                
                <div className="profile-btn-container">
                  {!isEditing && (
                    <button
                      className="profile-btn-custom"
                      onClick={handleUpdateClick}
                    >
                      Update Profile
                    </button>
                  )}
                  {isEditing && (
                    <>
                      <button
                        className="profile-btn-custom profile-btn-secondary"
                        onClick={handleCancelClick}
                      >
                        Cancel
                      </button>
                      <button
                        className="profile-btn-custom"
                        onClick={handleSaveClick}
                        type="button"
                      >
                        Save
                      </button>
                    </>
                  )}
                </div>
              </form>
            )}

            {activeTab === "professional" && (
              <form>
                <div className="row y-gap-30">
                  <div className="col-md-6">
                    <NurtifyText label="Registration Number*" />
                    <NurtifyInput 
                      type="text" 
                      value={registrationNumber} 
                      onChange={(e) => setRegistrationNumber(e.target.value)}
                      disabled={!isEditing} 
                      required 
                    />
                  </div>
                  <div className="col-md-6">
                    <NurtifyText label="Expiry Date*" />
                    <NurtifyDateInput 
                      value={expiryDate} 
                      onChange={(e) => setExpiryDate(e.target.value)}
                    />
                  </div>
                  <div className="col-md-6">
                    <NurtifyText label="Organization*" />
                    <NurtifyInput 
                      type="text" 
                      value={organization} 
                      onChange={(e) => setOrganization(e.target.value)}
                      disabled={!isEditing}
                    />
                  </div>
                  <div className="col-md-6">
                    <NurtifyText label="Department" />
                    <NurtifyInput 
                      type="text" 
                      value={currentUser?.department?.name || ""} 
                      disabled={true}
                    />
                  </div>
                  <div className="col-md-6">
                    <NurtifyText label="Professional Email" />
                    <NurtifyInput 
                      type="email" 
                      value={currentUser?.email || ""} 
                      disabled={true}
                    />
                  </div>
                  <div className="col-md-6">
                    <NurtifyText label="Speciality*" />
                    <NurtifySelect 
                      options={specialityOptions} 
                      value={speciality} 
                      onChange={(e) => setSpeciality(e.target.value)}
                      disabled={!isEditing}
                      required
                    />
                  </div>
                </div>
                <div className="profile-btn-container">
                  {!isEditing && (
                    <button
                      className="profile-btn-custom"
                      onClick={handleUpdateClick}
                    >
                      Update Profile
                    </button>
                  )}
                  {isEditing && (
                    <>
                      <button
                        className="profile-btn-custom profile-btn-secondary"
                        onClick={handleCancelClick}
                      >
                        Cancel
                      </button>
                      <button
                        className="profile-btn-custom"
                        onClick={handleSaveClick}
                        type="button"
                      >
                        Save
                      </button>
                    </>
                  )}
                </div>
              </form>
            )}

            {activeTab === "documents" && (
              <form>
                <div className="row y-gap-30">
                  {/* Document Upload Section */}
                  <div className="col-12">
                    <div className="document-upload-section">
                      <div className="document-upload-title">
                        {isUpdating ? "Update Document" : "Upload New Document"}
                      </div>
                      
                      <div className="row y-gap-20">
                        <div className="col-md-6">
                          <NurtifyText label={isUpdating ? "Select New File (Optional)" : "Select File*"} />
                          <div className="file-input-wrapper">
                            <div className="file-input-custom">
                              <input 
                                type="file" 
                                accept=".pdf,.doc,.docx,.jpg,.jpeg,.png"
                                onChange={(e) => setSelectedFile(e.target.files?.[0] || null)}
                              />
                            </div>
                            <div className="document-file-info">
                              Supported formats: PDF, DOC, DOCX, JPG, JPEG, PNG (Max size: 10MB)
                              {isUpdating && " - Leave empty to keep current file"}
                            </div>
                          </div>
                        </div>
                        
                        <div className="col-md-6">
                          <NurtifyText label="Document Type*" />
                          <NurtifySelect 
                            options={filteredDocumentTypeOptions} 
                            value={documentType} 
                            onChange={(e) => setDocumentType(e.target.value)}
                          />
                          {uploadedDocumentTypes.length > 0 && !isUpdating && (
                            <div className="document-type-hint">
                              <strong>Already uploaded:</strong> {uploadedDocumentTypes.join(', ')}. 
                              Use the "Update" button below to replace these documents. 
                              <br />
                              <em>Note: "Other" document types can be uploaded multiple times.</em>
                            </div>
                          )}
                        </div>
                        
                        {documentType === "Other" && (
                          <div className="col-md-6">
                            <NurtifyText label="Document Name*" />
                            <NurtifyInput 
                              type="text" 
                              value={customDocumentName} 
                              onChange={(e) => setCustomDocumentName(e.target.value)}
                              placeholder="Specify document name"
                            />
                          </div>
                        )}
                        
                        <div className="col-md-6">
                          <NurtifyText label="Expiry Date (Optional)" />
                          <NurtifyDateInput 
                            value={documentExpiryDate}
                            onChange={(e) => setDocumentExpiryDate(e.target.value)}
                          />
                          <div className="document-type-hint">
                            Set expiry date for certifications and time-sensitive documents
                          </div>
                        </div>
                        
                        <div className="col-12">
                          <div className="profile-btn-container">
                            {isUpdating ? (
                              <>
                                <button
                                  className="profile-btn-custom"
                                  type="button"
                                  onClick={handleUpdateSubmit}
                                >
                                  Update Document
                                </button>
                                <button
                                  className="profile-btn-custom profile-btn-secondary"
                                  type="button"
                                  onClick={handleCancelUpdate}
                                >
                                  Cancel Update
                                </button>
                              </>
                            ) : (
                              <button
                                className="profile-btn-custom"
                                type="button"
                                onClick={handleDocumentUpload}
                              >
                                Upload Document
                              </button>
                            )}
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                  
                  {/* Document Management Section */}
                  <div className="col-12">
                    <NurtifyText label="My Documents" />
                    
                    {/* Document Expiry Alerts */}
                    {(expiredDocuments && expiredDocuments.length > 0) || (expiringSoonDocuments && expiringSoonDocuments.length > 0) ? (
                      <div className="document-alerts">
                        {expiredDocuments && expiredDocuments.length > 0 && (
                          <div className="document-alert alert-danger">
                            <div className="document-alert-icon">⚠️</div>
                            <div>
                              <strong>Expired Documents ({expiredDocuments.length})</strong>
                              <p>The following documents have expired and may need to be renewed:</p>
                              <ul style={{ margin: 0, paddingLeft: '20px' }}>
                                {expiredDocuments.map((doc) => (
                                  <li key={doc.uuid}>
                                    {doc.display_name} - Expired on {new Date(doc.expiry_date!).toLocaleDateString()}
                                  </li>
                                ))}
                              </ul>
                            </div>
                          </div>
                        )}
                        
                        {expiringSoonDocuments && expiringSoonDocuments.length > 0 && (
                          <div className="document-alert alert-warning">
                            <div className="document-alert-icon">⏰</div>
                            <div>
                              <strong>Documents Expiring Soon ({expiringSoonDocuments.length})</strong>
                              <p>The following documents will expire within 30 days:</p>
                              <ul style={{ margin: 0, paddingLeft: '20px' }}>
                                {expiringSoonDocuments.map((doc) => (
                                  <li key={doc.uuid}>
                                    {doc.display_name} - Expires on {new Date(doc.expiry_date!).toLocaleDateString()} 
                                    ({doc.days_until_expiry} days remaining)
                                  </li>
                                ))}
                              </ul>
                            </div>
                          </div>
                        )}
                      </div>
                    ) : null}
                    
                    {/* Document Summary */}
                    {expirySummary && (
                      <div className="document-summary-cards">
                        <div className="document-summary-card total">
                          <div className="document-summary-number">{expirySummary.total_documents}</div>
                          <div className="document-summary-label">Total Documents</div>
                        </div>
                        <div className="document-summary-card valid">
                          <div className="document-summary-number">{expirySummary.valid_documents}</div>
                          <div className="document-summary-label">Valid Documents</div>
                        </div>
                        <div className="document-summary-card warning">
                          <div className="document-summary-number">{expirySummary.expiring_soon}</div>
                          <div className="document-summary-label">Expiring Soon</div>
                        </div>
                        <div className="document-summary-card danger">
                          <div className="document-summary-number">{expirySummary.expired_documents}</div>
                          <div className="document-summary-label">Expired</div>
                        </div>
                      </div>
                    )}
                    
                    <div className="document-list">
                      {documentsError ? (
                        <div className="document-alert alert-danger">
                          <div className="document-alert-icon">❌</div>
                          <div>
                            <strong>Error loading documents</strong>
                            <p>{documentsError.message || "Failed to load documents"}</p>
                          </div>
                        </div>
                      ) : documentsLoading ? (
                        <div className="document-alert alert-info">
                          <div className="document-alert-icon">⏳</div>
                          <div>Loading documents...</div>
                        </div>
                      ) : !documentsArray || documentsArray.length === 0 ? (
                        <div className="document-empty-state">
                          <div className="document-empty-state-icon">📄</div>
                          <div className="document-empty-state-title">No documents uploaded yet</div>
                          <div className="document-empty-state-text">
                            Upload your professional documents, certifications, or any other relevant files using the form above.
                          </div>
                        </div>
                      ) : (
                        <div className="document-table-wrapper">
                          <table className="document-table">
                            <thead>
                              <tr>
                                <th>Document Type</th>
                                <th>File Name</th>
                                <th>Upload Date</th>
                                <th>Expiry Date</th>
                                <th>Status</th>
                                <th>File Size</th>
                                <th>Actions</th>
                              </tr>
                            </thead>
                            <tbody>
                              {documentsArray.map((doc: UserDocument) => {
                                const getExpiryStatus = () => {
                                  if (!doc.expiry_date) return { text: "No Expiry", badge: "status-muted" };
                                  if (doc.is_expired) return { text: "Expired", badge: "status-danger" };
                                  if (doc.days_until_expiry && doc.days_until_expiry <= 30) {
                                    return { text: `Expires in ${doc.days_until_expiry} days`, badge: "status-warning" };
                                  }
                                  return { text: "Valid", badge: "status-valid" };
                                };
                                
                                const expiryStatus = getExpiryStatus();
                                
                                return (
                                  <tr key={doc.uuid} className={doc.is_expired ? "table-danger" : ""}>
                                    <td>{doc.display_name}</td>
                                    <td>{doc.file_url.split('/').pop()}</td>
                                    <td>{new Date(doc.uploaded_at).toLocaleDateString()}</td>
                                    <td>
                                      {doc.expiry_date ? (
                                        new Date(doc.expiry_date).toLocaleDateString()
                                      ) : (
                                        <span style={{ color: '#6b7280' }}>-</span>
                                      )}
                                    </td>
                                    <td>
                                      <span className={`document-status-badge ${expiryStatus.badge}`}>
                                        {expiryStatus.text}
                                      </span>
                                    </td>
                                    <td>{(doc.file_size / 1024 / 1024).toFixed(2)} MB</td>
                                    <td>
                                      <div className="document-actions">
                                        <button
                                          type="button"
                                          className={`document-action-btn btn-update ${
                                            updatingDocumentId === doc.uuid ? "updating" : ""
                                          }`}
                                          onClick={() => handleUpdateDocument(doc.uuid)}
                                          disabled={isUpdating && updatingDocumentId !== doc.uuid}
                                        >
                                          {updatingDocumentId === doc.uuid ? "Updating..." : "Update"}
                                        </button>
                                        <button
                                          type="button"
                                          className="document-action-btn btn-delete"
                                          onClick={() => handleDeleteDocument(doc.uuid)}
                                          disabled={isUpdating}
                                        >
                                          Delete
                                        </button>
                                      </div>
                                    </td>
                                  </tr>
                                );
                              })}
                            </tbody>
                          </table>
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              </form>
            )}

            {activeTab === "password" && (
              <form>
                <div className="row y-gap-30" style={{ maxWidth: "800px" }}>
                  <div className="col-12">
                    <NurtifyText label="Current Password" />
                    <PasswordInput 
                      value={currentPassword}
                      onChange={(e) => setCurrentPassword(e.target.value)}
                    />
                  </div>
                  <div className="col-12">
                    <NurtifyText label="New Password" />
                    <PasswordInput 
                      value={newPassword}
                      onChange={(e) => setNewPassword(e.target.value)}
                      showStrengthMeter={true}
                      showRequirements={true}
                    />
                  </div>
                  <div className="col-12">
                    <NurtifyText label="Confirm New Password" />
                    <PasswordInput 
                      value={confirmPassword}
                      onChange={(e) => setConfirmPassword(e.target.value)}
                    />
                  </div>
                  
                  {passwordError && (
                    <div className="col-12">
                      <div className="error-container">
                        <p className="error-message">{passwordError}</p>
                      </div>
                    </div>
                  )}
                </div>
                <div className="profile-btn-container" style={{ justifyContent: "flex-start" }}>
                  <button 
                    className="profile-btn-custom"
                    onClick={handleChangePassword}
                  >
                    Change Password
                  </button>
                </div>
              </form>
            )}
        </div>
      </div>
    </div>
  );
}

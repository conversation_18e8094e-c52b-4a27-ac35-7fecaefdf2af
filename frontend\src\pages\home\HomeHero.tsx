import React from "react";
import { motion } from "framer-motion";
import CardItemV3 from "@/components/CardItemV3";
import HeroSixSection from "@/shared/HeroSixSection";
import "./home.css";
import { useEffect, useRef, useState } from "react";

// Image imports
const techIcon = "/assets/img/home/<USER>";
const eventIcon = "/assets/img/home/<USER>";
const medsIcon = "/assets/img/home/<USER>";
const clinicalDocsIcon = "/assets/img/home/<USER>";
const clinicalResIcon = "/assets/img/home/<USER>";
const askIcon = "/assets/img/home/<USER>";
const gcpIcon = "/assets/img/home/<USER>";
const monitorIcon = "/assets/img/home/<USER>";
const supportIcon = "/assets/img/home/<USER>";
const nurtifyText = "/assets/img/home/<USER>";

// HERO SECTION (from LandingPage)
const HeroSection: React.FC = () => (
  <div className="hero-container-home">
    <div className="hero-split-background-home">
      <div className="hero-padding">
        <div className="hero-content-hero">
          <motion.div
            className="hero-logo-container section-header-align"
            initial={{ opacity: 0, y: -20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
          >
            <img src={nurtifyText} alt="Nurtify" className="hero-logo" />
          </motion.div>
          <motion.div
            className="hero-tagline section-header-align"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.2 }}
          >
            <p style={{ color: "white", fontSize:"20px" }}>
              Transform your clinical research facility with Nurtify! Automate administrative tasks, optimize workflows, and enhance patient involvement to drive impactful research outcomes.
            </p>
          </motion.div>
          <motion.div
            className="hero-buttons"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.6 }}
          >
            <button className="hero-cta-button">Explore our news</button>
          </motion.div>
        </div>
      </div>
    </div>
  </div>
);

// Home-specific sections (restored)
const HeroFeatures: React.FC = () => {
  const ref = useRef<HTMLDivElement>(null);
  const [, setIsMobile] = useState<boolean>(window.innerWidth < 768);

  const handleResize = () => {
    setIsMobile(window.innerWidth < 768);
  };

  useEffect(() => {
    window.addEventListener("resize", handleResize);
    return () => {
      window.removeEventListener("resize", handleResize);
    };
  }, []);

  const features = [
    {
      id: 1,
      color: "white",
      icon: clinicalDocsIcon,
      title: "Patients Portal",
      description: "Access and manage patient clinical records efficiently",
      navigateTo: "/clinical",
      isPopular: true,
    },
    {
      id: 2,
      color: "white",
      icon: clinicalResIcon,
      title: "Studies ",
      description: "Access your study protocol and resources",
      navigateTo: "/department-studies"
    },
    {
      id: 3,
      color: "white",
      icon: medsIcon,
      title: "Prescriptions List",
      description: "Manage and track medication prescriptions",
      navigateTo: "/prescription-list"
    },
    {
      id: 4,
      color: "white",
      icon: eventIcon,
      title: "Study Training",
      description: "Training resources for clinical trials",
      navigateTo: "/development"
    },
    {
      id: 5,
      color: "white",
      icon: askIcon,
      title: "Refund Requests",
      description: "Connect with the patient and provide support",
      navigateTo: "/ask-nurtifiers",
      isNew: true
    },
    {
      id: 6,
      color: "white",
      icon: techIcon,
      title: "Studies schedule",
      description: "List of studies and their schedules",
      navigateTo: "/org/dashboard/studies"
    }
  ];

  return (
    <div className="features-section" ref={ref}>
      <motion.h2
        className="features-title"
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6 }}
      >
        Explore Our <span className="text-highlight">Features</span>
      </motion.h2>

      <motion.p
        className="features-subtitle"
        initial={{ opacity: 0, y: -10 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6, delay: 0.2 }}
      >
        Discover tools and resources designed to enhance your nursing career
      </motion.p>

      <div className="features-grid">
        {features.map((feature, index) => (
          <motion.div
            key={feature.id}
            className="feature-card-container"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.1 * index }}
          >
            <div
              className="feature-card-wrapper"
              onClick={() => {
                if (feature.navigateTo) {
                  window.location.href = feature.navigateTo;
                }
              }}
              style={{ cursor: 'pointer' }}
            >
              <CardItemV3
                color={feature.color}
                icon={feature.icon}
                title={feature.title}
                style={{
                  position: 'relative',
                  marginBottom: '0',
                  height: '130px',
                  width: '130px',
                  padding: '15px',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center'
                }}
              />

              {feature.isNew && (
                <div className="feature-badge new-badge">New</div>
              )}

              {feature.isPopular && (
                <div className="feature-badge popular-badge">Popular</div>
              )}

              <div className="feature-content">
                <p className="feature-description">{feature.description}</p>
              </div>
            </div>
          </motion.div>
        ))}
      </div>
    </div>
  );
};

const BenefitsSection: React.FC = () => {
  const benefits = [
    {
      id: 1,
      icon: monitorIcon,
      title: "Monitor your clinical trials",
      description: "Manage and monitor your clinical trials efficiently"
    },
    {
      id: 2,
      icon: gcpIcon,
      title: "Good clinical practice",
      description: "Good clinical practice and ensure the quality of your clinical practice with high standards."
    },
    {
      id: 3,
      icon: supportIcon,
      title: "Patient Support and involvement",
      description: "Support and involve patients in the clinical trials and studies."
    }
  ];

  return (
    <div className="benefits-section">
      <div className="container">
        <motion.h2
          className="benefits-title"
          initial={{ opacity: 0, y: -20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.6 }}
        >
          Why Choose <span className="text-highlight">Nurtify</span>
        </motion.h2>

        <div className="benefits-grid">
          {benefits.map((benefit, index) => (
            <motion.div
              key={benefit.id}
              className="benefit-card"
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.5, delay: 0.1 * index }}
            >
              <div className="benefit-icon">
                <img src={benefit.icon} alt={benefit.title} width="50" height="50" />
              </div>
              <h3 className="benefit-title">{benefit.title}</h3>
              <p className="benefit-description">{benefit.description}</p>
            </motion.div>
          ))}
        </div>
      </div>
    </div>
  );
};

const HomeHero: React.FC = () => {
  return (
    <section>
      <HeroSection />
      <HeroFeatures />
      <BenefitsSection />
      <div className="news-section">
        <HeroSixSection />
      </div>
    </section>
  );
};

export default HomeHero;

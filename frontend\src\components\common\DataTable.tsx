import { ReactNode, useState, useEffect, useMemo, CSSProperties } from "react";
import "./DataTable.css";
import { ChevronLeft, ChevronRight, GripVertical } from "lucide-react";
import { ChevronUp, ChevronDown } from "lucide-react";

// TanStack Table imports
import {
  PaginationState,
  useReactTable,
  getCoreRowModel,
  ColumnDef,
  getPaginationRowModel,
  getSortedRowModel,
  getFilteredRowModel,
  SortingState,
  flexRender,
} from '@tanstack/react-table';

// DnD Kit imports
import {
  DndContext,
  KeyboardSensor,
  MouseSensor,
  TouchSensor,
  closestCenter,
  type DragEndEvent,
  useSensor,
  useSensors,
} from '@dnd-kit/core';
import { restrictToHorizontalAxis } from '@dnd-kit/modifiers';
import {
  arrayMove,
  SortableContext,
  horizontalListSortingStrategy,
  useSortable,
} from '@dnd-kit/sortable';
import { CSS } from '@dnd-kit/utilities';

// Export the Column type
export type Column<T> = {
  key: keyof T;
  header: string;
  render?: (value: T[keyof T], row?: T) => ReactNode;
  hidden?: boolean;
  sortable?: boolean;
  maxLength?: number; // Add maxLength property for text truncation
};

// Remove the unexported interface Action<T> above this line

// Export the Action type
export interface Action<T> {
  icon?: React.ReactNode;
  label?: string;
  path?: string;
  onClick?: (row: T) => void;
  tooltipText?: string | ((row: T) => string);
  className?: string | ((row: T) => string);
  disabled?: boolean | ((row: T) => boolean);
}

// Remove the duplicate unexported interface Action<T> { ... } - This was the error source

type DataTableProps<T> = {
  data: T[];
  columns: Column<T>[];
  actions?: Action<T>[];
  itemsPerPageOptions?: number[];
  defaultItemsPerPage?: number;
  noDataMessage?: string;
  filteredData?: T[]; // Optional filtered data from external search
  globalFilterPlaceholder?: string; // Placeholder for global filter
  hideItemsPerPageSelector?: boolean;
  hideGlobalFilter?: boolean;
};

// Utility function to truncate text
const truncateText = (text: string, maxLength: number = 20): string => {
  if (text.length <= maxLength) return text;
  return text.substring(0, maxLength) + '...';
};

// Debounced Input Component
function DebouncedInput({
  value: initialValue,
  onChange,
  debounce = 800,
  ...props
}: {
  value: string | number;
  onChange: (value: string | number) => void;
  debounce?: number;
} & Omit<React.InputHTMLAttributes<HTMLInputElement>, 'onChange'>) {
  const [value, setValue] = useState(initialValue);

  useEffect(() => {
    setValue(initialValue);
  }, [initialValue]);

  useEffect(() => {
    const timeout = setTimeout(() => {
      onChange(value);
    }, debounce);

    return () => clearTimeout(timeout);
  }, [value, debounce, onChange]);

  return <input {...props} value={value} onChange={e => setValue(e.target.value)} />;
}

// Draggable Table Header Component with TanStack Table sorting
const DraggableTableHeader = <T,>({
  header,
  column,
}: {
  header: any; // TanStack Table header
  column: Column<T>;
}) => {
  const { attributes, isDragging, listeners, setNodeRef, transform } = useSortable({
    id: String(column.key),
  });

  const style: CSSProperties = {
    opacity: isDragging ? 0.8 : 1,
    position: 'relative',
    transform: CSS.Translate.toString(transform),
    transition: 'width transform 0.2s ease-in-out',
    whiteSpace: 'nowrap',
    zIndex: isDragging ? 1 : 0,
  };

  return (
    <th
      ref={setNodeRef}
      style={style}
      className={`${column.sortable ? "nurtify-datatable-sortable" : ""} ${isDragging ? "nurtify-datatable-dragging" : ""}`}
    >
      <div className="nurtify-datatable-th-content">
        <div
          className={
            header.column.getCanSort()
              ? 'nurtify-datatable-sortable-header'
              : ''
          }
          onClick={header.column.getToggleSortingHandler()}
          title={
            header.column.getCanSort()
              ? header.column.getNextSortingOrder() === 'asc'
                ? 'Sort ascending'
                : header.column.getNextSortingOrder() === 'desc'
                  ? 'Sort descending'
                  : 'Clear sort'
              : undefined
          }
        >
          {flexRender(header.column.columnDef.header, header.getContext())}
          <span className="nurtify-datatable-sort-indicator">
            {header.column.getIsSorted() === 'asc' ? <ChevronUp size={16} /> :
              header.column.getIsSorted() === 'desc' ? <ChevronDown size={16} /> : null}
          </span>
        </div>
        <div className="nurtify-datatable-th-controls">
          <button
            className="nurtify-datatable-drag-handle"
            {...attributes}
            {...listeners}
            onClick={(e) => e.stopPropagation()}
            aria-label={`Drag to reorder ${column.header} column`}
          >
            <GripVertical size={14} />
          </button>
        </div>
      </div>
    </th>
  );
};

// Drag Along Cell Component
const DragAlongCell = <T,>({
  column,
  cellValue,
  displayValue,
  fullValue,
}: {
  column: Column<T>;
  cellValue: ReactNode;
  displayValue: ReactNode;
  fullValue: string;
}) => {
  const { isDragging, setNodeRef, transform } = useSortable({
    id: String(column.key),
  });

  const style: CSSProperties = {
    opacity: isDragging ? 0.8 : 1,
    position: 'relative',
    transform: CSS.Translate.toString(transform),
    transition: 'width transform 0.2s ease-in-out',
    zIndex: isDragging ? 1 : 0,
  };

  return (
    <td
      style={style}
      ref={setNodeRef}
      title={typeof cellValue === 'string' && cellValue.length > (column.maxLength || 20) ? fullValue : undefined}
      className={isDragging ? "data-table-dragging" : ""}
    >
      {displayValue}
    </td>
  );
};

export default function DataTable<T extends { uuid?: number | string }>({
  data,
  columns,
  actions = [],
  itemsPerPageOptions = [5, 10, 25, 50],
  defaultItemsPerPage = 10,
  noDataMessage = "No data available",
  filteredData,
  globalFilterPlaceholder = "Search all columns...",
  hideItemsPerPageSelector = false,
  hideGlobalFilter = false,
}: DataTableProps<T>) {
  // TanStack Table states
  const [pagination, setPagination] = useState<PaginationState>({
    pageIndex: 0,
    pageSize: defaultItemsPerPage,
  });
  const [sorting, setSorting] = useState<SortingState>([]);
  const [globalFilter, setGlobalFilter] = useState('');

  // Column order state for drag and drop
  const [columnOrder, setColumnOrder] = useState<string[]>(() =>
    columns.filter(col => !col.hidden).map(col => String(col.key))
  );

  // Update column order when columns prop changes
  useEffect(() => {
    setColumnOrder(columns.filter(col => !col.hidden).map(col => String(col.key)));
  }, [columns]);

  // Convert our Column type to TanStack ColumnDef
  const tanstackColumns = useMemo<ColumnDef<T>[]>(() => {
    const visibleColumns = columns.filter(col => !col.hidden);
    const orderedCols = columnOrder
      .map(key => visibleColumns.find(col => String(col.key) === key))
      .filter((col): col is Column<T> => col !== undefined);

    return orderedCols.map((col) => ({
      id: String(col.key),
      accessorKey: col.key as string,
      header: col.header,
      enableSorting: col.sortable ?? false,
      cell: ({ getValue, row }) => {
        const value = getValue();
        if (col.render) {
          return col.render(value as T[keyof T], row.original);
        }

        const cellValue = value !== null && value !== undefined ? String(value) : "-";
        const displayValue = typeof cellValue === 'string' && col.maxLength
          ? truncateText(cellValue, col.maxLength)
          : cellValue;

        return (
          <span title={typeof cellValue === 'string' && cellValue.length > (col.maxLength || 20) ? cellValue : undefined}>
            {displayValue}
          </span>
        );
      },
    }));
  }, [columns, columnOrder]);

  // Get ordered columns for drag and drop
  const orderedColumns = useMemo(() => {
    const visibleColumns = columns.filter(col => !col.hidden);
    return columnOrder
      .map(key => visibleColumns.find(col => String(col.key) === key))
      .filter((col): col is Column<T> => col !== undefined);
  }, [columns, columnOrder]);

  // Use the data or filtered data
  const tableData = useMemo(() => filteredData || data, [filteredData, data]);

  // Create TanStack Table instance
  const table = useReactTable({
    data: tableData,
    columns: tanstackColumns,
    state: {
      pagination,
      sorting,
      columnOrder,
      globalFilter,
    },
    onPaginationChange: setPagination,
    onSortingChange: setSorting,
    onColumnOrderChange: setColumnOrder,
    onGlobalFilterChange: setGlobalFilter,
    getCoreRowModel: getCoreRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
    getSortedRowModel: getSortedRowModel(),
    getFilteredRowModel: getFilteredRowModel(),
    manualPagination: false,
    manualSorting: false,
  });

  // Drag and drop sensors
  const sensors = useSensors(
    useSensor(MouseSensor, {}),
    useSensor(TouchSensor, {}),
    useSensor(KeyboardSensor, {})
  );

  // Handle drag end
  const handleDragEnd = (event: DragEndEvent) => {
    const { active, over } = event;
    if (active && over && active.id !== over.id) {
      setColumnOrder(columnOrder => {
        const oldIndex = columnOrder.indexOf(active.id as string);
        const newIndex = columnOrder.indexOf(over.id as string);
        return arrayMove(columnOrder, oldIndex, newIndex);
      });
    }
  };


  // Pagination component using TanStack Table
  const Pagination = () => (
    <div className="data-table-pagination">
      <div className="pagination-info">
        Showing {table.getRowModel().rows.length > 0 ? table.getState().pagination.pageIndex * table.getState().pagination.pageSize + 1 : 0} to{" "}
        {Math.min((table.getState().pagination.pageIndex + 1) * table.getState().pagination.pageSize, tableData.length)} of{" "}
        {tableData.length} entries
        {filteredData && filteredData !== data && ` (filtered from ${data.length} total entries)`}
      </div>
      <div className="pagination-controls">
        <button
          className="pagination-button"
          onClick={() => table.firstPage()}
          disabled={!table.getCanPreviousPage()}
          aria-label="First page"
        >
          First
        </button>
        <button
          className="pagination-button"
          onClick={() => table.previousPage()}
          disabled={!table.getCanPreviousPage()}
          aria-label="Previous page"
        >
          <ChevronLeft size={16} />
        </button>
        <div className="pagination-pages">
          {Array.from({ length: Math.min(5, table.getPageCount()) }, (_, i) => {
            let pageNum;
            const currentPage = table.getState().pagination.pageIndex + 1;
            const totalPages = table.getPageCount();

            if (totalPages <= 5) {
              pageNum = i + 1;
            } else if (currentPage <= 3) {
              pageNum = i + 1;
            } else if (currentPage >= totalPages - 2) {
              pageNum = totalPages - 4 + i;
            } else {
              pageNum = currentPage - 2 + i;
            }
            return (
              <button
                key={pageNum}
                className={`pagination-page ${currentPage === pageNum ? "active" : ""}`}
                onClick={() => table.setPageIndex(pageNum - 1)}
              >
                {pageNum}
              </button>
            );
          })}
        </div>
        <button
          className="pagination-button"
          onClick={() => table.nextPage()}
          disabled={!table.getCanNextPage()}
          aria-label="Next page"
        >
          <ChevronRight size={16} />
        </button>
        <button
          className="pagination-button"
          onClick={() => table.lastPage()}
          disabled={!table.getCanNextPage()}
          aria-label="Last page"
        >
          Last
        </button>
      </div>
    </div>
  );

  // Items per page selector using TanStack Table
  const ItemsPerPageSelector = () => (
    <div className="data-table-items-per-page">
      <label htmlFor="itemsPerPage">Show entries:</label>
      <select
        id="itemsPerPage"
        value={table.getState().pagination.pageSize}
        onChange={e => table.setPageSize(Number(e.target.value))}
        className="data-table-select"
      >
        {itemsPerPageOptions.map((option) => (
          <option key={option} value={option}>
            {option}
          </option>
        ))}
      </select>
    </div>
  );

  // Global Filter Input Component
  const GlobalFilterInput = () => (
    <div className="nurtify-datatable-global-filter">
      <DebouncedInput
        value={globalFilter ?? ''}
        onChange={value => setGlobalFilter(String(value))}
        placeholder={globalFilterPlaceholder}
        className="nurtify-datatable-global-filter-input"
      />
    </div>
  );

  return (
    <DndContext
      collisionDetection={closestCenter}
      modifiers={[restrictToHorizontalAxis]}
      onDragEnd={handleDragEnd}
      sensors={sensors}
    >
      <div className="data-table-container">
        <div className="data-table-top-controls">
          {!hideItemsPerPageSelector && <ItemsPerPageSelector />}
          {!hideGlobalFilter && <GlobalFilterInput />}
        </div>

        <div className="data-table-wrapper">
          <table className="data-table">
            <thead>
              {table.getHeaderGroups().map(headerGroup => (
                <tr key={headerGroup.id}>
                  <SortableContext
                    items={columnOrder}
                    strategy={horizontalListSortingStrategy}
                  >
                    {headerGroup.headers.map(header => {
                      const column = orderedColumns.find(col => String(col.key) === header.id);
                      if (!column) return null;

                      return (
                        <DraggableTableHeader
                          key={header.id}
                          header={header}
                          column={column}
                        />
                      );
                    })}
                  </SortableContext>
                  {actions.length > 0 && <th className="actions-column">Actions</th>}
                </tr>
              ))}
            </thead>
          </table>

          <div className="table-body-container">
            <table className="data-table">
              <tbody>
                {table.getRowModel().rows.length > 0 ? (
                  table.getRowModel().rows.map((row) => (
                    <tr key={`row-${row.original.uuid?.toString() || row.id}`}>
                      <SortableContext
                        items={columnOrder}
                        strategy={horizontalListSortingStrategy}
                      >
                        {orderedColumns.map((column) => {
                          const cellValue = column.render
                            ? column.render(row.original[column.key], row.original)
                            : row.original[column.key] !== null && row.original[column.key] !== undefined
                              ? String(row.original[column.key])
                              : "-";

                          const displayValue = typeof cellValue === 'string' && column.maxLength
                            ? truncateText(cellValue, column.maxLength)
                            : cellValue;

                          const fullValue = typeof cellValue === 'string' ? cellValue : '';

                          return (
                            <DragAlongCell
                              key={String(column.key)}
                              column={column}
                              cellValue={cellValue}
                              displayValue={displayValue}
                              fullValue={fullValue}
                            />
                          );
                        })}
                      </SortableContext>
                      {actions.length > 0 && (
                        <td>
                          <div className="actions-cell">
                          {actions.map((action, index) => (
                            <div key={index} className="action-button-wrapper">
                              <button
                                className={`action-button ${typeof action.className === 'function' ? action.className(row.original) : action.className || ''}`}
                                onClick={() => {
                                  const isDisabled = typeof action.disabled === 'function' ? action.disabled(row.original) : action.disabled;
                                  if (!isDisabled && action.onClick) {
                                    action.onClick(row.original);
                                  }
                                }}
                                title={typeof action.tooltipText === 'function' ? action.tooltipText(row.original) : action.tooltipText}
                                disabled={typeof action.disabled === 'function' ? action.disabled(row.original) : action.disabled}
                              >
                                {action.icon && <span className="action-icon">{action.icon}</span>}
                                {action.label && <span className="action-label">{action.label}</span>}
                              </button>
                              {action.tooltipText && (
                                <span className="tooltip">
                                  {typeof action.tooltipText === 'function' ? action.tooltipText(row.original) : action.tooltipText}
                                </span>
                              )}
                            </div>
                          ))}
                          </div>
                        </td>
                      )}
                    </tr>
                  ))
                ) : (
                  <tr>
                    <td colSpan={orderedColumns.length + (actions.length > 0 ? 1 : 0)} className="no-data">
                      {noDataMessage}
                    </td>
                  </tr>
                )}
              </tbody>
            </table>
          </div>
        </div>

        {tableData.length > 0 && <Pagination />}
      </div>
    </DndContext>
  );
}

// Search component to be used with DataTable
export function DataTableSearch<T>({
  data,
  onFilter,
  placeholder = "Search...",
}: {
  data: T[];
  onFilter: (filtered: T[]) => void;
  placeholder?: string;
  searchFields?: (keyof T)[];
}) {
  const [searchTerm, setSearchTerm] = useState("");

  // Filter data based on search term
  useEffect(() => {
    if (!searchTerm.trim()) {
      onFilter(data);
      return;
    }

    const filtered = data.filter((item) => {
      // Convert item to a record for type safety
      const record = item as Record<string, unknown>;

      return Object.keys(record).some(key => {
        const value = record[key];
        if (value === null || value === undefined) return false;

        // Handle nested objects
        if (typeof value === 'object' && value !== null) {
          const nestedRecord = value as Record<string, unknown>;
          return Object.keys(nestedRecord).some(nestedKey => {
            const nestedValue = nestedRecord[nestedKey];
            return nestedValue !== null &&
                   nestedValue !== undefined &&
                   String(nestedValue).toLowerCase().includes(searchTerm.toLowerCase());
          });
        }

        return String(value).toLowerCase().includes(searchTerm.toLowerCase());
      });
    });

    onFilter(filtered);
  }, [searchTerm, data, onFilter]);

  return (
    <div className="data-table-search">
      <input
        type="text"
        value={searchTerm}
        onChange={(e) => setSearchTerm(e.target.value)}
        placeholder={placeholder}
        className="data-table-search-input"
      />
    </div>
  );
}

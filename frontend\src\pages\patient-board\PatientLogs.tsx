import { usePatientAccessLogsByUuidQuery } from "@/hooks/patient.query";
import useSelectedPatientStore from "@/store/SelectedPatientState";
import { format } from "date-fns";
import DataTable from "@/components/common/DataTable";
import type { PatientAccessLog } from "@/services/api/types";
import { useState, useMemo } from "react";
import NurtifyFilter, { NurtifyFilterItem } from "@/components/NurtifyFilter";

const PatientLogs: React.FC = () => {
  const { selectedPatient } = useSelectedPatientStore();
  const { data: logs = [], isLoading, error } = usePatientAccessLogsByUuidQuery(selectedPatient?.uuid || "");

  // State for filters
  const [selectedAccessType, setSelectedAccessType] = useState<string[]>([]);

  // Function to normalize the access type string for consistent filtering and display
  const normalizeAccessType = (value?: string | null): string => {
    if (typeof value === 'string') {
      return value.charAt(0).toUpperCase() + value.slice(1);
    }
    return '';
  };

  // Memoized filter options with counts based on the raw logs data
  const filterOptionsWithCounts = useMemo(() => {
    const accessTypeCounts = new Map<string, number>();

    logs.forEach(log => {
      const normalizedType = normalizeAccessType(log.access_type);
      if (normalizedType) {
        accessTypeCounts.set(normalizedType, (accessTypeCounts.get(normalizedType) || 0) + 1);
      }
    });

    const accessTypeOptions = Array.from(accessTypeCounts.keys()).map(key => ({
      label: `${key} (${accessTypeCounts.get(key)})`,
      value: key
    }));

    return { accessTypeOptions };
  }, [logs]);

  // Memoized filtered logs based on the selected access type filter
  const filteredLogs = useMemo(() => {
    let filtered = logs;

    if (selectedAccessType.length > 0) {
      filtered = filtered.filter(log =>
        selectedAccessType.includes(normalizeAccessType(log.access_type))
      );
    }
    return filtered;
  }, [logs, selectedAccessType]);

  if (isLoading) {
    return (
      <div className="content-wrapper js-content-wrapper">
        <div className="bg-light-4 px-3 py-5">
          <div className="container-fluid py-6 px-6">
            <div className="loading">Loading patient logs...</div>
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="content-wrapper js-content-wrapper">
        <div className="bg-light-4 px-3 py-5">
          <div className="container-fluid py-6 px-6">
            <div className="error">Error loading patient logs: {(error as Error).message}</div>
          </div>
        </div>
      </div>
    );
  }

  if (!selectedPatient) {
    return (
      <div className="content-wrapper js-content-wrapper">
        <div className="bg-light-4 px-3 py-5">
          <div className="container-fluid py-6 px-6">
            <div className="no-logs">No patient selected to view logs.</div>
          </div>
        </div>
      </div>
    );
  }

  const logsColumns = [
    {
      key: 'accessed_by_name' as keyof PatientAccessLog,
      header: 'Accessed By',
      sortable: true,
    },
    {
      key: 'access_type' as keyof PatientAccessLog,
      header: 'Access Type',
      sortable: true,
      render: (value: any) => normalizeAccessType(value),
    },
    {
      key: 'created_at' as keyof PatientAccessLog,
      header: 'Date & Time',
      sortable: true,
      render: (value: any) => {
        if (typeof value === 'string') {
          return format(new Date(value), 'PPpp');
        }
        return '';
      }
    },
    {
      key: 'ip_address' as keyof PatientAccessLog,
      header: 'IP Address',
      sortable: true,
    }
  ];

  const filters: NurtifyFilterItem[] = [
    {
      label: "Access Type",
      type: "checkbox",
      options: filterOptionsWithCounts.accessTypeOptions,
      value: selectedAccessType,
      onChange: setSelectedAccessType as any,
    },
  ];

  return (
    <div className="content-wrapper js-content-wrapper">
      <div className="bg-light-4 px-3 py-5">
        <div className="container-fluid py-6 px-6">
          <div className="patient-details-container">
            <div className="patient-details-header">
              <h1 className="page-title">Patient Access Logs</h1>
            </div>

            <div style={{
              display: 'flex',
              justifyContent: 'space-between',
              alignItems: 'center',
              marginBottom: '20px',
            }}>
              <h3 style={{ margin: 0, fontSize: '18px', fontWeight: 600 }}>
                View access logs for {selectedPatient.first_name} {selectedPatient.last_name}
              </h3>
            </div>

            {filteredLogs.length > 0 ? (
              <>
                
                  <NurtifyFilter layout="horizontal" filters={filters} />
          
                <DataTable<PatientAccessLog>
                  data={filteredLogs}
                  columns={logsColumns}
                  noDataMessage="No access logs available for this patient"
                  defaultItemsPerPage={10}
                />
              </>
            ) : (
              <div className="no-logs" style={{ textAlign: 'center', marginTop: '50px', fontSize: '1.2em', color: '#666' }}>
                No access logs available for this patient or no logs match the current filter.
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default PatientLogs;

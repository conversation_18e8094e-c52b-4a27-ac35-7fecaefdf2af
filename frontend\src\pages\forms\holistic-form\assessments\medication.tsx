import { useState } from "react";
import MedicationImage from "./static/images/added/medication.png";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { faAdd } from "@fortawesome/free-solid-svg-icons";
import useHolisticFormStore from "@/store/holisticFormState";
import NurtifyInput from "@/components/NurtifyInput";
import NurtifyText from "@/components/NurtifyText";

const Medication = () => {
  const { assessment, setAssessment } = useHolisticFormStore();

  const [addPriorToComingMedicationsFormShowing, setAddPriorToComingMedicationsFormShowing] = useState(false);

  const handleAddPriorToComingMedications = (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    const form = e.currentTarget;
    const formData = new FormData(form);
    const newMedication = {
      d1: formData.get('med_name') as string,
      d2: formData.get('dose') as string,
      d3: formData.get('date') as string,
      d4: formData.get('time') as string,
    };

    setAssessment({
      ...assessment,
      medication: [...assessment.medication, newMedication]
    });

    setAddPriorToComingMedicationsFormShowing(false);
  };

  
  return (
    <>
      <form
        onSubmit={(e) => e.preventDefault()}
        id="division-40"
        className="block align-items-*-start flex-column flex-md-row p-4 mt-5"
      >
        <div className="">
          <div className="inlineBlock mb-4 headinqQuestion">
            <img
              src={MedicationImage}
              className="imageEtiquette"
              alt="patient face image round"
            />
            <span className="mb-2 py-2 fs-2 text-start etiquetteHeadingForms">
              Medication
            </span>
          </div>

          <div className="list-group me-4 mt-3 col-xl-6 col-lg-8 col-md-12">
            <span className="headinqQuestion">
              Any Medications Prior to Coming?
            </span>

            <div className="mt-3 mb-3 d-flex flex-wrap flex-row gap-3">
              <div className="medications d-flex flex-wrap flex-row gap-3 align-items-center justify-content-start">
                {assessment?.medication?.map((med, index) => (
                  <div key={index} className="medication d-flex flex-column align-items-start p-2" style={{backgroundColor: "#112D4E", color: "white", borderRadius: "10px"}}>
                    <div className="d-flex align-items-center gap-2">
                      <NurtifyText className="fw-bold text-white" label="Medication:"/>
                      <NurtifyText label={med.d1} className="text-white" />
                    </div>
                    <div className="d-flex align-items-center gap-2">
                      <NurtifyText className="fw-bold text-white" label="Dose:"/>
                      <NurtifyText label={med.d2} className="text-white" />
                    </div>
                    <div className="d-flex align-items-center gap-2">
                      <NurtifyText className="fw-bold text-white" label="Date:"/>
                      <NurtifyText label={med.d3} className="text-white" />
                    </div>
                    <div className="d-flex align-items-center">
                        <NurtifyText className="fw-bold text-white" label="Time:"/>
                      <NurtifyText label={med.d4} className="text-white" />
                    </div>
                  </div>
                ))}
              </div>
            </div>
            <div className="mt-4 mb-3">
              <button
                onClick={() => {
                  setAddPriorToComingMedicationsFormShowing(true);
                }}
                className="btn-nurtify"
              >
                Add Medications <FontAwesomeIcon icon={faAdd} />
              </button>
            </div>
          </div>
        </div>
      </form>
      {addPriorToComingMedicationsFormShowing && (
        <div className="options-modal">
          <div className="center">
            <form
              onSubmit={handleAddPriorToComingMedications}
              className="d-flex flex-column gap-1"
            >
              <div className="input">
                <NurtifyText label="Medication" />
                <NurtifyInput
                  type="text"
                  name="med_name"
                  className="form-control mb-3"
                />
              </div>
              <div className="input">
                <NurtifyText label="Dose" />
                <NurtifyInput
                  type="text"
                  name="dose"
                  className="form-control mb-3"
                />
              </div>
              <div className="input">
                <NurtifyText label="Date" />
                <input
                  type="date"
                  name="date"
                  className="form-control mb-3"
                />
              </div>
              <div className="input">
                <NurtifyText label="Time" />
                <input
                  type="time"
                  name="time"
                  className="form-control mb-3"
                />
              </div>
              <div className="d-flex btns gap-3 mt-3 flex-row">
                <button
                  onClick={(e) => {
                    e.preventDefault();
                    setAddPriorToComingMedicationsFormShowing(false);
                  }}
                  className="btn btn-secondary"
                >
                  CANCEL
                </button>
                <button type="submit" className="my-primary-btn">
                  save
                </button>
              </div>
            </form>
          </div>
        </div>
      )}
    </>
  );
};

export default Medication;

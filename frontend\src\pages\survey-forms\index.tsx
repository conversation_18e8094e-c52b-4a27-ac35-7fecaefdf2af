import React, { useState, useEffect } from "react";
import Preloader from "@/components/common/Preloader";
import LightFooter from "@/shared/LightFooter";
import { useNavigate, useParams, useSearchParams } from "react-router-dom";
import {
  Plus,
  Copy,
  <PERSON>Up,
  ArrowDown,
  Trash,
  CheckSquare,
  List,
  ToggleLeft,
  Type,
  FileText,
  Grid,
  ChevronDown,
  FilePlus,
  Signature,
  Sliders,
  ChevronUp,
  LayoutPanelTop,
  Table,
  Save,
  Loader,
  CheckCircle,
  AlertCircle
} from "lucide-react";
import NurtifyRadio from "@/components/NurtifyRadio";
import NurtifyCheckBox from "@/components/NurtifyCheckBox";
import NurtifyToggle from "@/components/NurtifyToggle";
import NurtifyInput from "@/components/NurtifyInput";
import NurtifyTextArea from "@/components/NurtifyTextArea";
import NurtifySelect from "@/components/NurtifySelect";
import "./surveyForms.css";
import NurtifyAttachFileBox from "@/components/NurtifyAttachFileBox";
import NurtifySignBox from "@/components/NurtifySignBox";
import NurtifyRange from "@/components/NurtifyRange";
import NurtifySwitch from "@/components/NurtifySwitch";
import NurtifyComboBox from "@/components/NurtifyComboBox";
import { useCreateForm, useGetTags, useGetFormByUuid, useUpdateForm } from "@/hooks/form.query";
import { useCurrentUserQuery } from "@/hooks/user.query";
import { Tag } from "@/types/types";
import axios, { AxiosError } from "axios";
import { getAllStudies } from "@/services/api/study.service";
import { Study } from "@/types/types";

// Interface for paginated API response
interface PaginatedResponse<T> {
  count: number;
  next: string | null;
  previous: string | null;
  results: T[];
}

// Interface for form payload
interface FormPayloadType {
  name: string;
  description: string;
  categories: string[];
  privacy: string;
  study: string | null;
  password?: string;
  structure: { sections: SavedSectionData[] };
  user_identifier: string;
  form_version_uuid?: string; // Optional property for updates
}

const inputTypeOptions = [
  { value: "text", label: "Text" },
  { value: "number", label: "Number" },
  { value: "date", label: "Date" },
  { value: "time", label: "Time" },
  { value: "datetime-local", label: "Date and Time" },
];

const parameterTypeOptions = [
  { value: "", label: "Value of Answer" },
  { value: "blood-pressure", label: "Blood Pressure" },
  { value: "temperature", label: "Temperature" },
  { value: "heart-rate", label: "Heart Rate" },
  { value: "blood-glucose", label: "Blood Glucose" },
  { value: "email", label: "Email" },
  { value: "name", label: "Name" },
  { value: "mrn", label: "MRN" },
  { value: "address", label: "Address" },
  { value: "dob", label: "Date of Birth" },
  { value: "age", label: "Age" },
  { value: "frailty-score", label: "Frailty Score" },
];

export type QuestionType =
  | "single-choice"
  | "multiple-choice"
  | "boolean"
  | "short-text"
  | "long-text"
  | "multiple-text-boxes"
  | "dropdown"
  | "multi-select-dropdown"
  | "attach-file"
  | "signature"
  | "range"
  | "table"
  | "Expression"; 

type Answer = {
  id: number;
  value: string;
};

type Question = {
  id: number;
  type: QuestionType | null;
  answers?: Answer[];
  questionName?: string;
  name?: string; // For expression type questions
};

type Section = {
  id: number;
  questions: Question[];
  showAddQuestionButton: boolean;
  showNewSectionButton: boolean;
  name?: string;
  description?: string;
};

interface SavedQuestionData {
  id: number;
  type: QuestionType | null;
  questionName?: string;
  answers?: string[];
  options?: string[];
  signature?: Record<string, unknown>;
  range?: { min: string; max: string; currentValue: string };
  attachment?: Record<string, unknown>;
  table?: Record<string, unknown>;
  inputType?: string;
  expression?: string;
  required?: boolean;
  nonClinical?: boolean;
}

interface SavedSectionData {
  id: number;
  name: string;
  description: string;
  questions: SavedQuestionData[];
}



/////////////////////////////////////////////////////////////////////////////
// QuestionComponent
/////////////////////////////////////////////////////////////////////////////

interface QuestionComponentProps {
  section: Section;
  question: Question;
  questionKey: string; 
  handleQuestionTypeClick: (
    type: QuestionType,
    sectionId: number,
    questionId: number
  ) => void;
  duplicateQuestion: (sectionId: number, questionId: number) => void;
  moveQuestionUp: (sectionId: number, questionId: number) => void;
  moveQuestionDown: (sectionId: number, questionId: number) => void;
  deleteQuestion: (sectionId: number, questionId: number) => void;
  defaultValues?: {
    questionName?: string;
    answers?: string[];
    options?: string[];
    signature?: Record<string, unknown>;
    range?: { min: string; max: string; currentValue: string };
    attachment?: Record<string, unknown>;
    table?: Record<string, unknown>;
    inputType?: string;
    expression?: string;
    required?: boolean;
    nonClinical?: boolean;
  }
}

const QuestionComponent: React.FC<QuestionComponentProps> = (props) => {
  const {
    section,
    question,
    questionKey,
    handleQuestionTypeClick,
    duplicateQuestion,
    moveQuestionUp,
    moveQuestionDown,
    deleteQuestion,
    defaultValues
  } = props;

  // Initialize state with values based on the question type and defaultValues
  const initTextBoxInputs = () => {
    if (question.answers && question.answers.length > 0) {
      // Use the answers from the question object if available
      return question.answers;
    } else if (defaultValues?.answers && defaultValues.answers.length > 0) {
      return defaultValues.answers.map((answer: string, idx: number) => ({ id: idx + 1, value: answer }))
    }
    return [{ id: 1, value: "" }];
  };

  const initDropdownInputs = () => {
    if (defaultValues?.options && defaultValues.options.length > 0) {
      return defaultValues.options.map((option: string, idx: number) => ({ id: idx + 1, value: option }));
    }
    return [{ id: 1, value: "" }];
  };

  
  const [textBoxInputs, setTextBoxInputs] = useState<Answer[]>(initTextBoxInputs());
  const [dropdownInputs, setDropdownInputs] = useState<Answer[]>(initDropdownInputs());
  const [multiSelectDropdownInputs, setMultiSelectDropdownInputs] = useState<Answer[]>(initDropdownInputs());
  const [inputTypes, setInputTypes] = useState<{ [key: string]: string }>({ [`${questionKey}-0`]: defaultValues?.inputType || "text" });
  const [parameterTypes, setParameterTypes] = useState<{ [key: string]: string }>({ [`${questionKey}-0`]: "" });
  const [signatureInputs, setSignatureInputs] = useState<{ left: string; right: string }>({ left: String(defaultValues?.signature?.left || ""), right: String(defaultValues?.signature?.right || "") });
  const [rangeInputs, setRangeInputs] = useState<{ min: string; max: string }>({ min: defaultValues?.range?.min || "", max: defaultValues?.range?.max || "" });
  const [rangeValue, setRangeValue] = useState<number | null>(defaultValues?.range?.currentValue ? Number(defaultValues.range.currentValue) : null);

  const [booleanAnswers, setBooleanAnswers] = useState<string[] | null>(null);
  const [toggleClicked, setToggleClicked] = useState(false);

  const [requiredSwitch, setRequiredSwitch] = useState(defaultValues?.required ? "true" : "false");
  const [nonClinicalSwitch, setNonClinicalSwitch] = useState(defaultValues?.nonClinical ? "true" : "false");
  const [tableData, setTableData] = useState<string[][]>([["", ""]]);

  const handleTextBoxClick = () => {
    setTextBoxInputs([...textBoxInputs, { id: textBoxInputs.length + 1, value: "" }]);
  };
  const handleDropdownClick = () => {
    setDropdownInputs([...dropdownInputs, { id: dropdownInputs.length + 1, value: "" }]);
  };
  const handleMultiSelectDropdownClick = () => {
    setMultiSelectDropdownInputs([...multiSelectDropdownInputs, { id: multiSelectDropdownInputs.length + 1, value: "" }]);
  };
  const handleInputTypeChange = (index: number, value: string) => {
    setInputTypes({ ...inputTypes, [`${questionKey}-${index}`]: value });
  };
  const handleParameterTypeChange = (index: number, value: string) => {
    setParameterTypes({ ...parameterTypes, [`${questionKey}-${index}`]: value });
  };
  const handleSignatureInputChange = (input: "left" | "right", value: string) => {
    setSignatureInputs({ ...signatureInputs, [input]: value });
  };
  const handleRangeInputChange = (input: "min" | "max", value: string) => {
    setRangeInputs({ ...rangeInputs, [input]: value });
  };

  const addTableColumn = () => {
    setTableData((prev) => {
      if (prev.length > 0) {
        const newRow = [...prev[0], ""];
        return [newRow];
      }
      return [["", ""]];
    });
  };

  const removeTableColumn = () => {
    setTableData((prev) => {
      if (prev.length > 0 && prev[0].length > 1) {
        // Don't allow removing the last column
        const newRow = [...prev[0].slice(0, -1)];
        return [newRow];
      }
      return prev;
    });
  };

  const updateTableCell = (rowIndex: number, colIndex: number, value: string) => {
    setTableData((prev) =>
      prev.map((row, i) =>
        i === rowIndex
          ? row.map((cell, j) => (j === colIndex ? value : cell))
          : row
      )
    );
  };

  const handleBooleanToggleClick = (toggleIndex: number) => {
    const answers = toggleIndex === 0 ? ["True", "False"] : ["Yes", "No"];
    setBooleanAnswers(answers);
    setToggleClicked(true);
  };

  const renderSwitches = () => {
    if (!question.type || question.type === "Expression") return null;
    return (
      <div
        style={{
          bottom: "5px",
          right: "5px",
          display: "flex",
          flexDirection: "row-reverse",
          gap: "5px",
          alignItems: "center",
        }}
      >
        <NurtifySwitch
          name={`required-${questionKey}`}
          value={requiredSwitch}
          onChange={(val) => setRequiredSwitch(val)}
          label="Required"
        />
        <NurtifySwitch
          name={`nonClinical-${questionKey}`}
          value={nonClinicalSwitch}
          onChange={(val) => setNonClinicalSwitch(val)}
          label="Non-Clinical"
        />
        <input type="hidden" id={`questionRequired-${questionKey}`} value={requiredSwitch} />
        <input type="hidden" id={`questionNonClinical-${questionKey}`} value={nonClinicalSwitch} />
      </div>
    );
  };

  const renderQuestionIcons = () => {
    if (!question.type) return null;
    return (
      <div
        className="survey-question-icons"
        style={{
          position: "absolute",
          top: "5px",
          right: "5px",
          display: "flex",
          gap: "10px",
        }}
      >
        <Copy
          size={18}
          className="survey-icon"
          style={{ cursor: "pointer", color: "#2196F3" }}
          onClick={() => duplicateQuestion(section.id, question.id)}
        />
        <ArrowUp
          size={18}
          className="survey-icon"
          style={{ cursor: "pointer", color: "#4CAF50" }}
          onClick={() => moveQuestionUp(section.id, question.id)}
        />
        <ArrowDown
          size={18}
          className="survey-icon"
          style={{ cursor: "pointer", color: "#FF9800" }}
          onClick={() => moveQuestionDown(section.id, question.id)}
        />
        <Trash
          size={18}
          className="survey-icon"
          style={{ cursor: "pointer", color: "#F44336" }}
          onClick={() => deleteQuestion(section.id, question.id)}
        />
      </div>
    );
  };

  const renderQuestionNameInput = () => {
    if (question.type === "Expression") return null;
    return (
      <div className="survey-form-group">
        <label htmlFor={`questionName-${questionKey}`}>Question Name</label>
        <input
          type="text"
          id={`questionName-${questionKey}`}
          name={`questionName-${questionKey}`}
          placeholder="Enter question name"
          className="survey-form-input"
          style={{ border: "none" }}
          defaultValue={question.questionName || defaultValues?.questionName || ""}
        />
      </div>
    );
  };

  const renderAnswerInput = (answer: Answer, index: number) => {
    const idSuffix = `${questionKey}-${index}`;
    const commonProps = {
      id: `questionInput${idSuffix}`,
      name: `questionInput-${questionKey}`,
      placeholder: `Enter answer ${index + 1}`,
      className: "survey-form-input survey-answer-input",
      style: { border: "none" },
      value: answer.value,
    };

    if (question.type === "long-text") {
      return (
        <div className="survey-form-group" key={answer.id}>
          <div className="survey-answer-input-group">
            <NurtifyTextArea
              {...commonProps}
              onChange={(e: React.ChangeEvent<HTMLTextAreaElement>) => {
                const newValue = e.target.value;
                setTextBoxInputs((prev) =>
                  prev.map((input) =>
                    input.id === answer.id ? { ...input, value: newValue } : input
                  )
                );
              }}
            />
          </div>
        </div>
      );
    }

    const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
      const newValue = e.target.value;
      setTextBoxInputs((prev) =>
        prev.map((input) =>
          input.id === answer.id ? { ...input, value: newValue } : input
        )
      );
    };

    return (
      <div className="survey-form-group" key={answer.id}>
        <div className="survey-answer-input-group">
          {question.type === "multiple-choice" ? (
            <>
              <NurtifyCheckBox
                id={`questionInput-checkbox${idSuffix}`}
                name={`questionInput-checkbox${idSuffix}`}
                value={`answer${index + 1}`}
              />
              <input {...commonProps} onChange={handleChange} />
            </>
          ) : question.type === "single-choice" ? (
            <>
              <NurtifyRadio
                id={`questionInput-radio${idSuffix}`}
                name={`questionInput-radio${idSuffix}`}
                value={`answer${index + 1}`}
                label=""
              />
              <input {...commonProps} onChange={handleChange} />
            </>
          ) : question.type === "short-text" ? (
            <NurtifyInput {...commonProps} onChange={handleChange} />
          ) : (
            <NurtifyRadio
              id={`questionInput-radio${idSuffix}`}
              name={`questionInput-radio${idSuffix}`}
              value={`answer${index + 1}`}
              label=""
            />
          )}
        </div>
      </div>
    );
  };

  const renderTextBoxInput = (answer: Answer, index: number) => {
    const idSuffix = `${questionKey}-${index}`;
    return (
      <div className="survey-form-group" key={answer.id}>
        <label htmlFor={`parameterName${idSuffix}`}>Parameter Name</label>
        <div className="survey-answer-input-group">
          <input
            type="text"
            id={`parameterName${idSuffix}`}
            name={`parameterName${idSuffix}`}
            placeholder="Enter parameter name"
            className="survey-form-input survey-answer-input"
            style={{ border: "none" }}
            defaultValue={answer.value}
          />
          <NurtifyInput
            id={`parameterInput${idSuffix}`}
            name={`parameterInput${idSuffix}`}
            placeholder="Enter value"
            className="survey-form-input survey-answer-input"
            style={{ border: "none" }}
            type={inputTypes[`${questionKey}-${index}`] || "text"}
          />
          <NurtifySelect
            name={`inputTypeSelect${idSuffix}`}
            value={inputTypes[`${questionKey}-${index}`] || "text"}
            onChange={(e: React.ChangeEvent<HTMLSelectElement>) => handleInputTypeChange(index, e.target.value)}
            options={inputTypeOptions}
          />
          <NurtifySelect
            name={`parameterTypeSelect${idSuffix}`}
            value={parameterTypes[`${questionKey}-${index}`] || ""}
            onChange={(e: React.ChangeEvent<HTMLSelectElement>) => handleParameterTypeChange(index, e.target.value)}
            options={parameterTypeOptions}
          />
        </div>
      </div>
    );
  };

  const renderDropdownInput = (answer: Answer, index: number) => {
    const idSuffix = `${questionKey}-${index}`;
    return (
      <div className="survey-form-group" key={answer.id}>
        <label htmlFor={`dropdownOption${idSuffix}`}>Dropdown Option</label>
        <div className="survey-answer-input-group">
          <input
            type="text"
            id={`dropdownOption${idSuffix}`}
            name={`dropdownOption${idSuffix}`}
            placeholder="Enter option"
            className="survey-form-input survey-answer-input"
            style={{ border: "none", width: "50%" }}
            value={answer.value}
            onChange={(e) => {
              const newValue = e.target.value;
              setDropdownInputs((prev) =>
                prev.map((input) =>
                  input.id === answer.id ? { ...input, value: newValue } : input
                )
              );
            }}
          />
        </div>
      </div>
    );
  };

  const renderMultiSelectDropdownInput = (answer: Answer, index: number) => {
    const idSuffix = `${questionKey}-${index}`;
    return (
      <div className="survey-form-group" key={answer.id}>
        <label htmlFor={`multiSelectDropdownOption${idSuffix}`}>Multi Select Dropdown Option</label>
        <div className="survey-answer-input-group">
          <input
            type="text"
            id={`multiSelectDropdownOption${idSuffix}`}
            name={`multiSelectDropdownOption${idSuffix}`}
            placeholder="Enter option"
            className="survey-form-input survey-answer-input"
            style={{ border: "none", width: "50%" }}
            value={answer.value}
            onChange={(e) => {
              const newValue = e.target.value;
              setMultiSelectDropdownInputs((prev) =>
                prev.map((input) =>
                  input.id === answer.id ? { ...input, value: newValue } : input
                )
              );
            }}
          />
        </div>
      </div>
    );
  };

  const renderAttachFileInput = () => {
    return (
      <div className="survey-form-group">
        <NurtifyAttachFileBox />
      </div>
    );
  };

  const renderSignatureInput = () => {
    return (
      <div className="survey-form-group">
        <div style={{ width: "50%" }}>
          <NurtifySignBox />
        </div>
        <div className="survey-answer-input-group" style={{ gap: "10px", width: "50%" }}>
          <NurtifyInput
            id={`signatureInputLeft-${questionKey}`}
            name={`signatureInputLeft-${questionKey}`}
            placeholder="Enter full name"
            className="survey-form-input"
            style={{ border: "none", width: "calc(100% - 5px)" }}
            value={signatureInputs.left}
            onChange={(e) => handleSignatureInputChange("left", e.target.value)}
          />
          <NurtifyInput
            id={`signatureInputRight-${questionKey}`}
            name={`signatureInputRight-${questionKey}`}
            placeholder="Enter time"
            className="survey-form-input"
            style={{ border: "none", width: "calc(100% - 5px)" }}
            value={signatureInputs.right}
            onChange={(e) => handleSignatureInputChange("right", e.target.value)}
          />
        </div>
      </div>
    );
  };

  const renderRangeInput = () => {
    return (
      <div className="survey-form-group" style={{ marginTop: "65px" }}>
        <NurtifyRange
          className="nurtify-range"
          onChange={(e: React.ChangeEvent<HTMLInputElement>) => {
            const valueAsNumber = Number(e.target.value);
            setRangeValue(valueAsNumber);
          }}
        />
        <input type="hidden" id={`rangeValue-${questionKey}`} value={rangeValue !== null ? rangeValue : ""} />
        <div style={{ marginBottom: "10px" }}></div>
        <div className="survey-answer-input-group" style={{ gap: "10px", marginTop: "20px", width: "50%" }}>
          <NurtifyInput
            id={`rangeInputMin-${questionKey}`}
            name={`rangeInputMin-${questionKey}`}
            placeholder="Enter min value"
            className="survey-form-input"
            style={{ border: "none", width: "calc(100% - 5px)" }}
            value={rangeInputs.min}
            onChange={(e) => handleRangeInputChange("min", e.target.value)}
          />
          <NurtifyInput
            id={`rangeInputMax-${questionKey}`}
            name={`rangeInputMax-${questionKey}`}
            placeholder="Enter max value"
            className="survey-form-input"
            style={{ border: "none", width: "calc(100% - 5px)" }}
            value={rangeInputs.max}
            onChange={(e) => handleRangeInputChange("max", e.target.value)}
          />
        </div>
      </div>
    );
  };

  const renderTableInput = () => {
    const tableObj = { columns: tableData[0] || [], numberOfColumns: tableData[0] ? tableData[0].length : 0 };
    return (
      <div className="survey-form-group">
        <table style={{ width: "100%", borderCollapse: "collapse", marginBottom: "10px" }}>
          <tbody>
            {tableData.map((row, rowIndex) => (
              <tr key={rowIndex} style={{ border: "1px solid #000" }}>
                {row.map((cell, colIndex) => (
                  <td key={colIndex} style={{ border: "1px solid #000", padding: "5px" }}>
                    <input
                      type="text"
                      value={cell}
                      onChange={(e) => updateTableCell(rowIndex, colIndex, e.target.value)}
                      style={{ width: "100%", border: "none", background: "transparent" }}
                    />
                  </td>
                ))}
              </tr>
            ))}
          </tbody>
        </table>
        <input type="hidden" id={`tableData-${questionKey}`} value={JSON.stringify(tableObj)} />
        <div style={{ display: "flex", gap: "10px" }}>
          <button type="button" onClick={addTableColumn} className="survey-add-textbox-button">
            Add Column <Plus size={20} />
          </button>
          <button type="button" onClick={removeTableColumn} className="survey-add-textbox-button">
            Remove Column <Trash size={20} />
          </button>
        </div>
      </div>
    );
  };

  const renderExpressionInput = () => {
    return (
      <div className="survey-form-group" style={{ marginTop: "40px" }}>
        <NurtifyTextArea
          id={`expression-${questionKey}`}
          name={`expression-${questionKey}`}
          onChange={() => {
            /* No operation needed here */
          }}
          defaultValue={question.name || defaultValues?.expression || ""}
        />
      </div>
    );
  };

  if (!question.type) {
    return (
      <div className="survey-question-form" style={{ position: "relative" }}>
        {renderQuestionIcons()}
        <div className="survey-question-buttons" style={{ marginBottom: "20px", marginTop: "20px" }}>
          {[
            { type: "single-choice", icon: CheckSquare, label: "Single Choice" },
            { type: "multiple-choice", icon: List, label: "Multiple Choice" },
            { type: "boolean", icon: ToggleLeft, label: "Boolean" },
            { type: "short-text", icon: Type, label: "Short Text" },
            { type: "long-text", icon: FileText, label: "Long Text" },
            { type: "multiple-text-boxes", icon: Grid, label: "Multiple Text-Boxes" },
            { type: "dropdown", icon: ChevronDown, label: "Dropdown" },
            { type: "multi-select-dropdown", icon: ChevronDown, label: "Multi Select Dropdown" },
            { type: "attach-file", icon: FilePlus, label: "Attach File" },
            { type: "signature", icon: Signature, label: "Signature" },
            { type: "range", icon: Sliders, label: "Range" },
            { type: "table", icon: Table, label: "Table" },
            { type: "Expression", icon: FileText, label: "Expression" },
          ].map(({ type, icon: Icon, label }) => (
            <button
              key={type}
              className="survey-question-button"
              onClick={() => handleQuestionTypeClick(type as QuestionType, section.id, question.id)}
            >
              <Icon size={20} className="survey-button-icon" />
              {label}
            </button>
          ))}
        </div>
      </div>
    );
  }

  if (question.type === "Expression") {
    return (
      <div className="survey-question-form" style={{ position: "relative" }}>
        {renderQuestionIcons()}
        {renderExpressionInput()}
      </div>
    );
  }

  return (
    <div className="survey-question-form" style={{ position: "relative" }}>
      {renderQuestionIcons()}
      {renderQuestionNameInput()}
      {question.type === "boolean" ? (
        <div className="survey-form-group">
          {!toggleClicked && (
            <div className="survey-answer-input-group" style={{ gap: "20px" }}>
              <div className="nurtify-toggle" onClick={() => handleBooleanToggleClick(0)} style={{ cursor: "pointer" }}>
                <NurtifyToggle
                  id={`questionInput-toggle-0-${questionKey}`}
                  name={`questionInput-toggle-${questionKey}`}
                  labels={["True", "False"]}
                />
              </div>
              <div className="nurtify-toggle" onClick={() => handleBooleanToggleClick(1)} style={{ cursor: "pointer" }}>
                <NurtifyToggle
                  id={`questionInput-toggle-1-${questionKey}`}
                  name={`questionInput-toggle-${questionKey}`}

                  labels={["Yes", "No"]}
                />
              </div>
            </div>
          )}
          {booleanAnswers &&
            booleanAnswers.map((answer, index) => (
              <div className="survey-form-group" key={index}>
                <div className="survey-answer-input-group">
                  <NurtifyRadio
                    name={`questionInput-radio-${questionKey}-${index}`}
                    value={answer}
                    label=""
                  />
                  <input
                    type="text"
                    id={`questionInput-${questionKey}-${index}`}
                    name={`questionInput-${questionKey}-${index}`}
                    value={answer}
                    className="survey-form-input survey-answer-input"
                    style={{ border: "none" }}
                    readOnly
                  />
                </div>
              </div>
            ))}
        </div>
      ) : question.type === "multiple-text-boxes" ? (
        <>
          {textBoxInputs.map((answer, index) => renderTextBoxInput(answer, index))}
          <button type="button" className="survey-add-textbox-button" onClick={handleTextBoxClick}>
            Add Parameter <Plus size={20} />
          </button>
        </>
      ) : question.type === "dropdown" ? (
        <>
          {dropdownInputs.map((answer, index) => renderDropdownInput(answer, index))}
          <button type="button" className="survey-add-textbox-button" onClick={handleDropdownClick}>
            Add Option <Plus size={20} />
          </button>
        </>
      ) : question.type === "multi-select-dropdown" ? (
        <>
          {multiSelectDropdownInputs.map((answer, index) => renderMultiSelectDropdownInput(answer, index))}
          <button type="button" className="survey-add-textbox-button" onClick={handleMultiSelectDropdownClick}>
            Add Option <Plus size={20} />
          </button>
        </>
      ) : question.type === "attach-file" ? (
        renderAttachFileInput()
      ) : question.type === "signature" ? (
        renderSignatureInput()
      ) : question.type === "range" ? (
        renderRangeInput()
      ) : question.type === "table" ? (
        renderTableInput()
      ) : (
        <>
          {(question.type === "short-text" || question.type === "long-text")
            ? renderAnswerInput({ id: 1, value: "" }, 0)
            : textBoxInputs.map((answer, index) => renderAnswerInput(answer, index))}
          {(question.type === "single-choice" || question.type === "multiple-choice") && (
            <button type="button" className="survey-add-textbox-button" onClick={handleTextBoxClick}>
              Add Option <Plus size={20} />
            </button>
          )}
        </>
      )}
      {renderSwitches()}
    </div>
  );
};

export default function SurveyForms() {
  const { formUuid } = useParams<{ formUuid: string }>();
  const [searchParams] = useSearchParams();
  const isEditMode = !!formUuid;
  const isNewVersion = searchParams.get('baseForm') !== null;
  const baseFormUuid = searchParams.get('baseForm');
  const newFormName = searchParams.get('name');
  
  const [formDetails, setFormDetails] = useState({
    name: newFormName || "",
    description: "",
    categories: [] as string[],
    study: null as string | null,
    password: "",
  });
  const [sections, setSections] = useState<Section[]>([]);
  const [isCollapsed, setIsCollapsed] = useState(false);
  const [isSaving, setIsSaving] = useState(false);
  const [isLoading, setIsLoading] = useState(isEditMode || isNewVersion);
  const [saveError, setSaveError] = useState<string | null>(null);
  const [studies, setStudies] = useState<Study[]>([]);
  
  const navigate = useNavigate();
  const { data: formData, isLoading: isLoadingForm } = useGetFormByUuid(formUuid || baseFormUuid || '', { enabled: isEditMode || isNewVersion });
  const createFormMutation = useCreateForm();
  const updateFormMutation = useUpdateForm(formUuid || '');
  
  const { 
    data: tagsData, 
    isLoading: isLoadingTags, 
    error: tagsError 
  } = useGetTags();
  
  const { 
    data: currentUser, 
    isLoading: isLoadingUser 
  } = useCurrentUserQuery();

  // Load studies when component mounts
  useEffect(() => {
    const loadStudies = async () => {
      try {
        const studiesData = await getAllStudies();
        setStudies(studiesData);
      } catch (error) {
        console.error("Error loading studies:", error);
      }
    };
    loadStudies();
  }, []);

  // Load form data when in edit mode or creating new version
  useEffect(() => {
    if ((isEditMode || isNewVersion) && formData && !isLoadingForm) {
      try {
        console.log("Loading form data:", formData);
        
        // Set form details from active_version
        setFormDetails({
          name: isNewVersion ? newFormName || formData.name : formData.name || "",
          description: formData.active_version?.description || "",
          categories: formData.active_version?.categories || [],
          study: formData.active_version?.study || null,
          password: formData.active_version?.password || "",
        });
        
        // Get form structure using multiple strategies to handle different API response formats
        let formStructure = null;
        
        // 1. Check if form_structure field exists and is already parsed
        if (formData.active_version?.form_structure) {
          console.log("Using pre-parsed form_structure");
          if (typeof formData.active_version.form_structure === 'string') {
            try {
              formStructure = JSON.parse(formData.active_version.form_structure);
            } catch (e) {
              console.error("Error parsing form_structure string:", e);
            }
          } else {
            formStructure = formData.active_version.form_structure;
          }
        }
        // 3. Try to parse structure_data field if available
        else if (formData.active_version && 'structure_data' in (formData.active_version as object)) {
          const structureData = (formData.active_version as unknown as Record<string, unknown>).structure_data;
          console.log("Parsing structure field:", structureData);
          try {
            // The structure might be a double-encoded JSON string
            const parsed = typeof structureData === 'string' ? JSON.parse(structureData) : structureData;
            // If parsed is a string, it might be another JSON string
            if (typeof parsed === 'string') {
              try {
                formStructure = JSON.parse(parsed);
                console.log("Successfully parsed double-encoded JSON structure");
              } catch (e) {
                console.error("Error parsing second level JSON:", e);
                formStructure = parsed; // Use the first-level parsed value
              }
            } else {
              formStructure = parsed;
            }
          } catch (error) {
            console.error("Error parsing form structure:", error);
          }
        }
        
        console.log("Final parsed form structure:", formStructure);
        
        if (formStructure && formStructure.sections && Array.isArray(formStructure.sections)) {
          console.log("Found valid sections array in form structure:", formStructure.sections);
          
          const loadedSections: Section[] = formStructure.sections.map((section: SavedSectionData) => {
            console.log("Processing section:", section);
            
            // Ensure all section fields are properly mapped
            return {
              id: section.id,
              name: section.name || "",
              description: section.description || "",
              questions: Array.isArray(section.questions) ? section.questions.map(q => {
                console.log("Processing question:", q);
                
                // Create a properly structured question with all needed properties
                const questionObj: Question = {
                  id: q.id,
                  type: q.type,
                  questionName: q.questionName || "",
                };
                
                // Add answers if they exist
                if (q.answers && Array.isArray(q.answers)) {
                  questionObj.answers = q.answers.map((answer, idx) => ({ 
                    id: idx + 1, 
                    value: answer 
                  }));
                }
                
                // Add expression for Expression type questions
                if (q.type === "Expression" && q.expression) {
                  questionObj.name = q.expression;
                }
                
                return questionObj;
              }) : [],
              showAddQuestionButton: true,
              showNewSectionButton: true,
            };
          });
          
          setSections(loadedSections);
          console.log("Sections loaded successfully:", loadedSections);
        } else {
          console.error("No valid form structure found in the response");
          console.log("Active Version Data:", formData.active_version);
          
          // If no sections found but we have data, try to create a default empty section
          if (formData.name) {
            setSections([{
              id: 1,
              name: "", // Default name
              description: "", // Default description
              questions: [],
              showAddQuestionButton: true,
              showNewSectionButton: true,
            }]);
          }
        }
        
        setIsLoading(false);
      } catch (e) {
        console.error("Error processing form data:", e);
        setSaveError(`Error processing form data: ${e instanceof Error ? e.message : "Unknown error"}`);
        setIsLoading(false);
      }
    }
  }, [isEditMode, isNewVersion, formData, isLoadingForm, newFormName]);

  // Check if data has the paginated structure
  const isPaginatedResponse = (data: unknown): data is PaginatedResponse<Tag> => {
    return typeof data === 'object' && data !== null && 'results' in (data as PaginatedResponse<Tag>) && Array.isArray((data as PaginatedResponse<Tag>).results);
  };


  // Transform tag data from API for dropdown
  const formCategoryOptions = React.useMemo(() => {
    if (!tagsData) {
      return [];
    }
    
    if (isPaginatedResponse(tagsData)) {
      return tagsData.results.map(tag => ({
        value: tag.uuid,
        label: tag.name
      }));
    } else if (Array.isArray(tagsData)) {
      return tagsData.map(tag => ({
        value: tag.uuid,
        label: tag.name
      }));
    }
    
    // Fallback options
    return [
      { value: "clinical", label: "Clinical" },
      { value: "administrative", label: "Administrative" },
      { value: "assessment", label: "Assessment" },
      { value: "general", label: "General" }
    ];
  }, [tagsData]);

  const toggleCollapse = (e: React.MouseEvent<HTMLDivElement>) => {
    e.stopPropagation();
    e.preventDefault();
    setIsCollapsed(!isCollapsed);
  };

  // Handle category select change
  const handleCategoriesChange = (values: string[]) => {
    setFormDetails({ ...formDetails, categories: values });
  };

  const duplicateSection = (sectionId: number) => {
    const index = sections.findIndex((s) => s.id === sectionId);
    if (index === -1) return;
    const sectionToDuplicate = sections[index];
    const newSection = { ...sectionToDuplicate, id: sections.length + 1 };
    const newSections = [...sections];
    newSections.splice(index + 1, 0, newSection);
    setSections(newSections);
  };

  const moveSectionUp = (sectionId: number) => {
    const index = sections.findIndex((s) => s.id === sectionId);
    if (index <= 0) return;
    const newSections = [...sections];
    [newSections[index - 1], newSections[index]] = [newSections[index], newSections[index - 1]];
    setSections(newSections);
  };

  const moveSectionDown = (sectionId: number) => {
    const index = sections.findIndex((s) => s.id === sectionId);
    if (index === -1 || index >= sections.length - 1) return;
    const newSections = [...sections];
    [newSections[index], newSections[index + 1]] = [newSections[index + 1], newSections[index]];
    setSections(newSections);
  };

  const deleteSection = (sectionId: number) => {
    const newSections = sections.filter((s) => s.id !== sectionId);
    setSections(newSections);
  };

  const duplicateQuestion = (sectionId: number, questionId: number) => {
    setSections(
      sections.map((section) => {
        if (section.id === sectionId) {
          const index = section.questions.findIndex((q) => q.id === questionId);
          if (index === -1) return section;
          const questionToDuplicate = section.questions[index];
          const newQuestion = { ...questionToDuplicate, id: section.questions.length + 1 };
          const newQuestions = [...section.questions];
          newQuestions.splice(index + 1, 0, newQuestion);
          return { ...section, questions: newQuestions };
        }
        return section;
      })
    );
  };

  const moveQuestionUp = (sectionId: number, questionId: number) => {
    setSections(
      sections.map((section) => {
        if (section.id === sectionId) {
          const index = section.questions.findIndex((q) => q.id === questionId);
          if (index <= 0) return section;
          const newQuestions = [...section.questions];
          [newQuestions[index - 1], newQuestions[index]] = [newQuestions[index], newQuestions[index - 1]];
          return { ...section, questions: newQuestions };
        }
        return section;
      })
    );
  };

  const moveQuestionDown = (sectionId: number, questionId: number) => {
    setSections(
      sections.map((section) => {
        if (section.id === sectionId) {
          const index = section.questions.findIndex((q) => q.id === questionId);
          if (index === -1 || index >= section.questions.length - 1) return section;
          const newQuestions = [...section.questions];
          [newQuestions[index], newQuestions[index + 1]] = [newQuestions[index + 1], newQuestions[index]];
          return { ...section, questions: newQuestions };
        }
        return section;
      })
    );
  };

  const deleteQuestion = (sectionId: number, questionId: number) => {
    setSections(
      sections.map((section) => {
        if (section.id === sectionId) {
          const newQuestions = section.questions.filter((q) => q.id !== questionId);
          return { ...section, questions: newQuestions };
        }
        return section;
      })
    );
  };

  const addSection = () => {
    if (sections.length > 0) {
      const lastSection = sections[sections.length - 1];
      if (lastSection.questions.length < 1) {
        alert("Please add at least one question to the previous section before adding a new section.");
        return;
      }
    }
    setSections([
      ...sections,
      {
        id: sections.length + 1,
        questions: [],
        showAddQuestionButton: true,
        showNewSectionButton: true,
      },
    ]);
  };

  const addQuestion = (sectionId: number) => {
    setSections(
      sections.map((section) =>
        section.id === sectionId
          ? {
              ...section,
              questions: [
                ...section.questions,
                { id: section.questions.length + 1, type: null },
              ],
              showAddQuestionButton: false,
              showNewSectionButton: true,
            }
          : section
      )
    );
  };

  const handleQuestionTypeClick = (
    type: QuestionType,
    sectionId: number,
    questionId: number
  ) => {
    setSections(
      sections.map((section) => {
        if (section.id === sectionId) {
          return {
            ...section,
            questions: section.questions.map((q) =>
              q.id === questionId ? { ...q, type } : q
            ),
          };
        }
        return section;
      })
    );
  };

  const isAddSectionDisabled = sections.length > 0 && sections[sections.length - 1].questions.length === 0;

  const renderGlobalNewSectionButton = () => (
    <div
      className="survey-button-container"
      style={{ textAlign: "center", marginTop: "20px", marginBottom: "20px" }}
    >
      <button
        type="button"
        className="survey-new-section-button"
        onClick={addSection}
        disabled={isAddSectionDisabled}
        style={{
          backgroundColor: isAddSectionDisabled ? "#96C7CF" : "#3dc6d6",
          cursor: isAddSectionDisabled ? "not-allowed" : "pointer",
          display: "flex",
          alignItems: "center",
          justifyContent: "center",
        }}
      >
        <LayoutPanelTop size={30} className="survey-button-icon" style={{ marginRight: "5px" }} />
        New Section
        <Plus size={30} className="survey-button-icon survey-plus-icon" />
      </button>
    </div>
  );

  const canSaveForm = sections.length > 0 && sections.some((section) => section.questions.length > 0);

  const saveForm = () => {
    if (!canSaveForm || isLoadingUser) return;

    // Clear previous errors
    setSaveError(null);

    const sectionsData: SavedSectionData[] = sections.map((section) => {
      const sectionNameInput = document.getElementById(`sectionName-${section.id}`) as HTMLInputElement | null;
      const sectionDescInput = document.getElementById(`sectionDescription-${section.id}`) as HTMLInputElement | null;
      const sectionName = sectionNameInput?.value || "";
      const sectionDescription = sectionDescInput?.value || "";

      const questionsData: SavedQuestionData[] = section.questions.map((question) => {
        const questionKey = `${section.id}-${question.id}`;
        const questionNameId = `questionName-${questionKey}`;
        const qData: SavedQuestionData = {
          id: question.id,
          type: question.type,
        };

        if (
          question.type && ["short-text", "long-text", "single-choice", "multiple-choice", "boolean"].includes(
            question.type
          )
        ) {
          const questionNameInput = document.getElementById(questionNameId) as HTMLInputElement | null;
          qData.questionName = questionNameInput?.value || "";
          const answerInputs = Array.from(
            document.querySelectorAll<HTMLInputElement>(`[name^="questionInput-${questionKey}"]`)
          );
          qData.answers = answerInputs.map((input) => input.value);
        } else if (question.type === "multiple-text-boxes") {
          const questionNameInput = document.getElementById(questionNameId) as HTMLInputElement | null;
          qData.questionName = questionNameInput?.value || "";
          const optionInputs = Array.from(
            document.querySelectorAll<HTMLInputElement>(`[id^="parameterName${questionKey}-"]`)
          );
          qData.options = optionInputs.map((input) => input.value);
        } else if (question.type === "dropdown") {
          const questionNameInput = document.getElementById(questionNameId) as HTMLInputElement | null;
          qData.questionName = questionNameInput?.value || "";
          const optionInputs = Array.from(
            document.querySelectorAll<HTMLInputElement>(`[id^="dropdownOption${questionKey}-"]`)
          );
          qData.options = optionInputs.map((input) => input.value);
        } else if (question.type === "multi-select-dropdown") {
          const questionNameInput = document.getElementById(questionNameId) as HTMLInputElement | null;
          qData.questionName = questionNameInput?.value || "";
          const optionInputs = Array.from(
            document.querySelectorAll<HTMLInputElement>(`[id^="multiSelectDropdownOption${questionKey}-"]`)
          );
          qData.options = optionInputs.map((input) => input.value);
        } else if (question.type === "signature") {
          const questionNameInput = document.getElementById(questionNameId) as HTMLInputElement | null;
          qData.questionName = questionNameInput?.value || "";
          qData.signature = {};
        } else if (question.type === "range") {
          const questionNameInput = document.getElementById(questionNameId) as HTMLInputElement | null;
          qData.questionName = questionNameInput?.value || "";
          const min = (document.getElementById(`rangeInputMin-${questionKey}`) as HTMLInputElement | null)?.value || "";
          const max = (document.getElementById(`rangeInputMax-${questionKey}`) as HTMLInputElement | null)?.value || "";
          const currentValue = (document.getElementById(`rangeValue-${questionKey}`) as HTMLInputElement | null)?.value || "";
          qData.range = { min, max, currentValue };
        } else if (question.type === "attach-file") {
          const questionNameInput = document.getElementById(questionNameId) as HTMLInputElement | null;
          qData.questionName = questionNameInput?.value || "";
          qData.attachment = {};
        } else if (question.type === "table") {
          const questionNameInput = document.getElementById(questionNameId) as HTMLInputElement | null;
          qData.questionName = questionNameInput?.value || "";
          const tableDataInput = document.getElementById(`tableData-${questionKey}`) as HTMLInputElement | null;
          let tableObj: Record<string, unknown> = {};
          if (tableDataInput) {
            try {
              tableObj = JSON.parse(tableDataInput.value);
            } catch {
              //
            }
          }
          qData.table = tableObj;
        } else if (question.type === "Expression") {
          const expressionInput = document.getElementById(`expression-${questionKey}`) as HTMLTextAreaElement | null;
          qData.inputType = "long-text";
          qData.expression = expressionInput?.value || "";
        }
        if (question.type !== "Expression") {
          const requiredVal = (document.getElementById(`questionRequired-${questionKey}`) as HTMLInputElement | null)?.value;
          const nonClinicalVal = (document.getElementById(`questionNonClinical-${questionKey}`) as HTMLInputElement | null)?.value;
          qData.required = requiredVal === "true";
          qData.nonClinical = nonClinicalVal === "true";
        }

        return qData;
      });

      return {
        id: section.id,
        name: sectionName,
        description: sectionDescription,
        questions: questionsData,
      };
    });

    // Create the form payload
    const formPayload: FormPayloadType = {
      name: formDetails.name,
      description: formDetails.description,
      categories: formDetails.categories,
      privacy: "",
      study: formDetails.study,
      password: formDetails.password,
      structure: { sections: sectionsData },
      user_identifier: currentUser?.identifier || ''
    };

    // If in edit mode, add the form_version_uuid if available
    if (isEditMode && formUuid && formData?.active_version?.uuid) {
      formPayload.form_version_uuid = formData.active_version.uuid;
      console.log('Adding form_version_uuid to update payload:', formData.active_version.uuid);
    }

    setIsSaving(true);
    
    if (isEditMode && formUuid) {
      console.log('Updating form with UUID:', formUuid);
      
      // Update existing form
      updateFormMutation.mutate(formPayload, {
        onSuccess: (data) => {
          console.log('Form updated successfully:', data);
          setIsSaving(false);
          navigate('/templates'); // Redirect to my templates page on success
        },
        onError: (error: AxiosError | Error) => {
          console.error("Error updating form:", error);
          // Extract detailed error information
          let errorMessage = "Failed to update form";
          
          if (axios.isAxiosError(error) && error.response) {
            console.error('Error response status:', error.response.status);
            console.error('Error response headers:', error.response.headers);
            
            if (error.response.data) {
              console.error('Error response data:', JSON.stringify(error.response.data, null, 2));
              
              if (typeof error.response.data === 'string') {
                errorMessage = error.response.data;
              } else if (error.response.data.detail) {
                errorMessage = error.response.data.detail;
              } else if (typeof error.response.data === 'object') {
                // Convert object errors to string
                if (error.response && error.response.data) {
                  const errorFields = Object.keys(error.response.data);
                  if (errorFields.length > 0) {
                    const errorMessages = errorFields.map(field => {
                      // Make sure we're safely accessing error.response.data
                      const fieldError = error.response && error.response.data && error.response.data[field];
                      return `${field}: ${Array.isArray(fieldError) ? fieldError.join(', ') : fieldError}`;
                    });
                    errorMessage = errorMessages.join('; ');
                  }
                } else {
                  errorMessage = JSON.stringify(error.response.data);
                }
              }
            }
            errorMessage += ` (Status: ${error.response.status})`;
          } else if (error.message) {
            errorMessage = error.message;
          }
          
          setSaveError(errorMessage);
          setIsSaving(false);
        }
      });
    } else {
      // Create new form
      createFormMutation.mutate(formPayload, {
        onSuccess: () => {
          setIsSaving(false);
          navigate('/templates'); // Redirect to templates page on success (changed from '/clinical')
        },
        onError: (error: AxiosError | Error) => {
          // ...existing error handling code...
          console.error("Error creating form:", error);
          // Extract detailed error information
          let errorMessage = "Failed to create form";
          
          // Try to extract response error details if available
          if (axios.isAxiosError(error) && error.response) {
            console.error('Error response:', error.response);
            if (error.response.data) {
              if (typeof error.response.data === 'string') {
                errorMessage = error.response.data;
              } else if (error.response.data.detail) {
                errorMessage = error.response.data.detail;
              } else {
                errorMessage = JSON.stringify(error.response.data);
              }
            }
            errorMessage += ` (Status: ${error.response.status})`;
          } else if (error.message) {
            errorMessage = error.message;
          }
          
          setSaveError(errorMessage);
          setIsSaving(false);
        }
      });
    }
  };

  if (isLoading || isLoadingForm) {
    return (
      <div className="survey-main-content">
        <Preloader />
        <div className="survey-form-wrapper">
          <div className="survey-form-container" style={{ display: 'flex', justifyContent: 'center', alignItems: 'center', minHeight: '50vh' }}>
            <div style={{ textAlign: 'center' }}>
              <Loader size={40} className="animate-spin" style={{ margin: '0 auto 20px' }} />
              <p>Loading form data...</p>
            </div>
          </div>
        </div>
        <LightFooter />
      </div>
    );
  }

  // Display error message if save failed
  const renderSaveError = () => {
    if (!saveError) return null;
    
    return (
      <div style={{ 
        color: '#F44336', 
        padding: '10px', 
        marginTop: '10px', 
        background: 'rgba(244, 67, 54, 0.1)',
        borderRadius: '4px',
        display: 'flex',
        alignItems: 'center',
        gap: '8px'
      }}>
        <AlertCircle size={16} />
        <span>Error: {saveError}</span>
      </div>
    );
  };

  return (
    <div className="survey-main-content">
      <Preloader />
      <div className="survey-form-wrapper">
        <div className="survey-form-container">
          <form className="survey-form-grid">
            <div className="survey-form-group">
              <label htmlFor="formName">Form Name</label>
              <input
                type="text"
                id="formName"
                name="formName"
                placeholder="Enter form name"
                className="survey-form-input"
                value={formDetails.name}
                onChange={(e) => setFormDetails({ ...formDetails, name: e.target.value })}
                disabled={isNewVersion}
              />
            </div>
            {!isCollapsed && (
              <>
                <div className="survey-form-group">
                  <label htmlFor="formDescription">Form Description</label>
                  <input
                    type="text"
                    id="formDescription"
                    name="formDescription"
                    placeholder="Enter form description"
                    className="survey-form-input"
                    value={formDetails.description}
                    onChange={(e) => setFormDetails({ ...formDetails, description: e.target.value })}
                  />
                </div>
                <div className="survey-form-group">
                  <label htmlFor="formCategories">Form Categories/Tags</label>
                  <div className="survey-form-control">
                    {isLoadingTags ? (
                      <div className="loading-tags" style={{ display: "flex", alignItems: "center", gap: "10px" }}>
                        <Loader size={16} className="animate-spin" />
                        <span>Loading categories...</span>
                      </div>
                    ) : tagsError ? (
                      <div className="error-tags" style={{ display: "flex", alignItems: "center", gap: "10px", color: "#F44336" }}>
                        <AlertCircle size={16} />
                        <span>Failed to load categories: {(tagsError as Error).message}</span>
                      </div>
                    ) : (
                      <>
                        <div style={{ fontSize: "12px", color: "#666", marginBottom: "5px" }}>
                          {formCategoryOptions.length} categories available
                        </div>
                        <NurtifyComboBox 
                          options={formCategoryOptions}
                          selectedValues={formDetails.categories}
                          onChange={handleCategoriesChange}
                        />
                      </>
                    )}
                  </div>
                </div>
                <div className="survey-form-group">
                  <label htmlFor="formStudy">Study</label>
                  <select
                    id="formStudy"
                    name="formStudy"
                    className="survey-form-input"
                    value={formDetails.study || ""}
                    onChange={(e) => setFormDetails({ ...formDetails, study: e.target.value || null })}
                  >
                    <option value="">No Study Selected</option>
                    {studies.map((study) => (
                      <option key={study.uuid} value={study.uuid}>
                        {study.name}
                      </option>
                    ))}
                  </select>
                </div>
                {formDetails.password && (
                  <div className="survey-form-group">
                    <label htmlFor="formPassword">Password</label>
                    <input
                      type="password"
                      id="formPassword"
                      name="formPassword"
                      placeholder="Enter password"
                      className="survey-form-input"
                      value={formDetails.password}
                      onChange={(e) => setFormDetails({ ...formDetails, password: e.target.value })}
                    />
                  </div>
                )}
              </>
            )}
            <div
              className="survey-collapse-icon"
              onClick={(e) => toggleCollapse(e)}
              style={{
                textAlign: "center",
                cursor: "pointer",
                backgroundColor: "#C7EBEF",
                borderRadius: !isCollapsed ? "110px 110px 0 0" : "0 0 110px 110px",
                width: "75px",
                height: "35px",
                display: "flex",
                justifyContent: "center",
                alignItems: "center",
                margin: "7px auto",
                paddingLeft: "30px",
                paddingRight: "30px",
              }}
            >
              <button
                style={{
                  background: "none",
                  border: "none",
                  cursor: "pointer",
                }}
              >
                {isCollapsed ? <ChevronDown size={25} /> : <ChevronUp size={25} />}
              </button>
            </div>
          </form>
        </div>

        {sections.length === 0 && renderGlobalNewSectionButton()}

        {sections.map((section) => (
          <div key={section.id} className="survey-section-container" style={{ marginTop: "30px" }}>
            <div className="survey-section-header" style={{ marginBottom: "20px", position: "relative" }}>
              <div className="survey-section-info">
                <div className="survey-form-group">
                  <label htmlFor={`sectionName-${section.id}`}>Section Name</label>
                  <input
                    type="text"
                    id={`sectionName-${section.id}`}
                    name={`sectionName-${section.id}`}
                    placeholder="Enter section name"
                    className="survey-form-input survey-section-input"
                    defaultValue={section.name || ""}
                  />
                </div>
                <div className="survey-form-group">
                  <label htmlFor={`sectionDescription-${section.id}`}>Section Description</label>
                  <input
                    type="text"
                    id={`sectionDescription-${section.id}`}
                    name={`sectionDescription-${section.id}`}
                    placeholder="Enter section description"
                    className="survey-form-input survey-section-input"
                    defaultValue={section.description || ""}
                  />
                </div>
              </div>
              <div
                className="survey-section-icons"
                style={{
                  position: "absolute",
                  top: "10px",
                  right: "10px",
                  display: "flex",
                  gap: "10px",
                }}
              >
                <Copy size={20} className="survey-icon" style={{ cursor: "pointer", color: "#2196F3" }} onClick={() => duplicateSection(section.id)} />
                <ArrowUp size={20} className="survey-icon" style={{ cursor: "pointer", color: "#4CAF50" }} onClick={() => moveSectionUp(section.id)} />
                <ArrowDown size={20} className="survey-icon" style={{ cursor: "pointer", color: "#FF9800" }} onClick={() => moveSectionDown(section.id)} />
                <Trash size={20} className="survey-icon" style={{ cursor: "pointer", color: "#F44336" }} onClick={() => deleteSection(section.id)} />
              </div>
            </div>

            {section.questions.map((question) => (
              <div key={question.id} className="survey-question-container">
                <QuestionComponent
                  section={section}
                  question={question}
                  questionKey={`${section.id}-${question.id}`}
                  handleQuestionTypeClick={handleQuestionTypeClick}
                  duplicateQuestion={duplicateQuestion}
                  moveQuestionUp={moveQuestionUp}
                  moveQuestionDown={moveQuestionDown}
                  deleteQuestion={deleteQuestion}
                  defaultValues={{
                    questionName: question.questionName || "",
                    answers: question.answers?.map((answer) => answer.value) || [],
                    expression: question.name || "", // For Expression type
                    required: question.type !== "Expression" && (
                      (() => {
                        // Safe access to form structure sections
                        const formStructureSections = formData?.active_version?.form_structure && 
                          typeof formData.active_version.form_structure === 'object' ? 
                          (formData.active_version.form_structure as { sections?: SavedSectionData[] }).sections : 
                          undefined;
                        
                        if (Array.isArray(formStructureSections)) {
                          const foundSection = formStructureSections.find((s: SavedSectionData) => s.id === section.id);
                          if (foundSection && Array.isArray(foundSection.questions)) {
                            const foundQuestion = foundSection.questions.find((q: SavedQuestionData) => q.id === question.id);
                            return !!foundQuestion?.required;
                          }
                        }
                        
                        // Try structure_data if form_structure didn't work
                        const structureDataSections = formData?.active_version && 
                          (formData.active_version as unknown as Record<string, unknown>).structure_data ? 
                          ((formData.active_version as unknown as Record<string, unknown>).structure_data as Record<string, unknown>).sections : 
                          undefined;
                        
                        if (Array.isArray(structureDataSections)) {
                          const foundSection = structureDataSections.find((s: SavedSectionData) => s.id === section.id);
                          if (foundSection && Array.isArray(foundSection.questions)) {
                            const foundQuestion = foundSection.questions.find((q: SavedQuestionData) => q.id === question.id);
                            return !!foundQuestion?.required;
                          }
                        }
                        
                        return false;
                      })()
                    ),
                    nonClinical: question.type !== "Expression" && (
                      (() => {
                        // Safe access to form structure sections
                        const formStructureSections = formData?.active_version?.form_structure && 
                          typeof formData.active_version.form_structure === 'object' ? 
                          (formData.active_version.form_structure as { sections?: SavedSectionData[] }).sections : 
                          undefined;
                        
                        if (Array.isArray(formStructureSections)) {
                          const foundSection = formStructureSections.find((s: SavedSectionData) => s.id === section.id);
                          if (foundSection && Array.isArray(foundSection.questions)) {
                            const foundQuestion = foundSection.questions.find((q: SavedQuestionData) => q.id === question.id);
                            return !!foundQuestion?.nonClinical;
                          }
                        }
                        
                        // Try structure_data if form_structure didn't work
                        const structureDataSections = formData?.active_version && 
                          (formData.active_version as unknown as Record<string, unknown>).structure_data ? 
                          ((formData.active_version as unknown as Record<string, unknown>).structure_data as Record<string, unknown>).sections : 
                          undefined;
                        
                        if (Array.isArray(structureDataSections)) {
                          const foundSection = structureDataSections.find((s: SavedSectionData) => s.id === section.id);
                          if (foundSection && Array.isArray(foundSection.questions)) {
                            const foundQuestion = foundSection.questions.find((q: SavedQuestionData) => q.id === question.id);
                            return !!foundQuestion?.nonClinical;
                          }
                        }
                        
                        return false;
                      })()
                    ),
                  }}
                />
              </div>
            ))}

            <div
              className="survey-button-container survey-center-button"
              style={{
                display: "flex",
                gap: "20px",
                justifyContent: "center",
                marginTop: "20px",
              }}
            >
              <button
                type="button"
                className="survey-add-question-button"
                onClick={() => addQuestion(section.id)}
              >
                Add Question
                <Plus size={30} className="survey-button-icon survey-plus-icon survey-right-icon" />
              </button>
              <button
                type="button"
                className="survey-add-question-button"
                onClick={addSection}
                disabled={isAddSectionDisabled}
                style={{
                  backgroundColor: isAddSectionDisabled ? "#96C7CF" : "#3dc6d6",
                  cursor: isAddSectionDisabled ? "not-allowed" : "pointer",
                }}
              >
                New Section
                <Plus size={30} className="survey-button-icon survey-plus-icon" style={{ marginLeft: "10px" }} />
              </button>
            </div>
          </div>
        ))}

        <div className="survey-button-container" style={{ justifyContent: "flex-end", marginTop: "20px" }}>
          {renderSaveError()}
          <button
            type="button"
            className="survey-save-form-button"
            onClick={saveForm}
            disabled={!canSaveForm || isSaving || createFormMutation.isPending || updateFormMutation.isPending}
            style={{
              backgroundColor: canSaveForm && !isSaving && !createFormMutation.isPending && !updateFormMutation.isPending ? "#3dc6d6" : "#96C7CF",
              border: "1px solid #3dc6d6",
              cursor: canSaveForm && !isSaving && !createFormMutation.isPending && !updateFormMutation.isPending ? "pointer" : "not-allowed",
              display: "flex",
              alignItems: "center",
              gap: "8px"
            }}
          >
            {isSaving || createFormMutation.isPending || updateFormMutation.isPending ? (
              <>
                <Loader size={20} className="animate-spin" />
                {isEditMode ? "Updating..." : "Saving..."}
              </>
            ) : createFormMutation.isSuccess || updateFormMutation.isSuccess ? (
              <>
                <CheckCircle size={20} />
                Saved
              </>
            ) : createFormMutation.isError || updateFormMutation.isError ? (
              <>
                <AlertCircle size={20} />
                Save Failed
              </>
            ) : (
              <>
                <Save size={20} />
                {isEditMode ? "Update Form" : "Save Form"}
              </>
            )}
          </button>
        </div>
      </div>
      <LightFooter />
    </div>
  );
}

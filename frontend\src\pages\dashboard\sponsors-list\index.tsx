import "./sponsor-orgs.css";
import { useNavigate } from "react-router-dom";
import { useState } from "react";
import {
  ArrowUpRight,
  Plus,
  Trash2,
  Building,
  UserCog,
} from "lucide-react";
import NoResult from "@/components/common/NoResult";
import { SponsorOrg as SponsorOrgType } from "@/services/api/types.ts";
import {
  useSponsorOrgsQuery,
  useDeleteSponsorOrgMutation,
} from "@/hooks/sponsorOrg.query";
import DeleteSponsorOrgModal from "@/components/modal/DeleteSponsorOrgModal";
import DataTable from "@/components/common/DataTable";
import NurtifyButton from "@/components/NurtifyButton";


export default function SponsorOrgsList() {
  const { data = [] } = useSponsorOrgsQuery();
  const deleteSponsorOrgMutation = useDeleteSponsorOrgMutation();
  const navigate = useNavigate();
  const [deletableSponsorOrgID, setDeletableSponsorOrgID] = useState<string>("");
  const [isModalOpen, setIsModalOpen] = useState(false);

  const handleCloseModal = () => {
    setIsModalOpen(false);
  };

  const handleSubmitModal = async () => {
    try {
      await deleteSponsorOrgMutation.mutateAsync({ uuid: deletableSponsorOrgID });
      setIsModalOpen(false);
    } catch (error) {
      console.error("Error deleting sponsor Org:", error);
    }
  };

  const SponsorOrgColumns: Array<{
    key: keyof SponsorOrgType;
    header: string;
    hidden?: boolean;
  }> = [
    { key: "uuid", header: "ID", hidden: true },
    { key: "name", header: "Sponsor Org Name" },
    { key: "postcode", header: "Postcode" },
    { key: "country", header: "Country" },
  ];

  const actions = [
    {
      icon: <ArrowUpRight size={20} />,
      onClick: (row: SponsorOrgType) => handleSponsorOrgDetails(row),
      tooltipText: "Edit this item",
    },
    {
      icon: <Trash2 size={20} color="#FF0000" />,
      onClick: (row: SponsorOrgType) => handleSponsorOrgDelete(row),
      tooltipText: "Delete this item",
    },
    {
      icon: <UserCog size={20} color="#FF0000" />,
      onClick: (row: SponsorOrgType) => handleSponsorOrgAdmin(row),
      tooltipText: "Manage Sponsors",
    },
  ];

  const handleSponsorOrgDetails = (sponsorOrg: SponsorOrgType) => {
    navigate(`/org/dashboard/sponsorOrg-details/${sponsorOrg.uuid}`);
  };

  const handleSponsorOrgDelete = (sponsorOrg: SponsorOrgType) => { // Renamed
    setIsModalOpen(true);
    setDeletableSponsorOrgID(sponsorOrg.uuid ?? ""); // Renamed state setter
  };

  const handleSponsorOrgAdmin = (sponsorOrg: SponsorOrgType) => { // Renamed
    // Assuming this navigation path is still correct for managing sponsors of an org
    navigate(`/org/dashboard/sponsorOrg-details/${sponsorOrg.uuid}/orgAdmins`);
  };

  return (
    <div className="sponsor-orgs-container"> {/* Renamed CSS class */}
      <div className="sponsor-orgs-header"> {/* Renamed CSS class */}
        <div className="sponsor-orgs-page-title"> {/* Renamed CSS class */}
          <h1>
            <Building size={24} style={{ marginRight: "10px" }} />
            Sponsor Organizations {/* Changed text */}
          </h1>
        </div>
        <div className="sponsor-orgs-subtitle"> {/* Renamed CSS class */}
          <h6>Manage and view all sponsor organizations in your network</h6> {/* Changed text */}
        </div>
      </div>

      {/* Search section removed */}
      <NurtifyButton
        variant="primary"
        size="medium"
        onClick={() => navigate("/org/dashboard/add-sponsor")}
        className="sponsor-orgs-add-button"
      >
        Add Sponsor Org <Plus size={20} />
      </NurtifyButton>

      <div className="sponsor-orgs-table-container"> {/* Renamed CSS class */}
        {data.length > 0 ? (
          <>
            <h4 className="sponsor-orgs-table-title">Sponsor Org List</h4> {/* Renamed CSS class & Changed text */}
            <DataTable
              data={data}
              columns={SponsorOrgColumns.map(col => ({ // Renamed columns variable
                key: col.key,
                header: col.header,
                hidden: col.hidden,
                sortable: true
              }))}
              actions={actions}
              noDataMessage="No sponsor orgs found" /* Changed message */
              defaultItemsPerPage={10}
            />
            {isModalOpen && (
              <DeleteSponsorOrgModal // Renamed Modal component
                isOpen={isModalOpen}
                onClose={handleCloseModal}
                onDelete={handleSubmitModal}
              />
            )}
          </>
        ) : (
          <NoResult
            addPath="/org/dashboard/add-sponsor-org" /* Changed path */
            title="Sponsor Org" /* Changed title */
          />
        )}
      </div>
    </div>
  );
}
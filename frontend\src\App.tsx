import React, { useEffect } from 'react';
import { BrowserRouter } from 'react-router-dom';
import { HelmetProvider } from 'react-helmet-async';
import AppRouter from './AppRouter'
import AppLayout from './shared/AppLayout';
import { Toaster } from 'sonner'; // Import Toaster
import './vendors.css'
import './App.css'
import "@fortawesome/fontawesome-svg-core/styles.css";
import { QueryClientProvider } from '@tanstack/react-query';
import { queryClient } from './queryClient';
import { ReactQueryDevtools } from '@tanstack/react-query-devtools';
import CookiesConsent from './components/CookiesConsent';
import RouteTracker from './components/SEO/RouteTracker';
//import SEOTester from './components/SEO/SEOTester';
import { initGA, trackPagePerformance } from './utils/analytics';

const App: React.FC = () => {
  useEffect(() => {
    // Initialize Google Analytics
    const gaId = import.meta.env.VITE_GA_MEASUREMENT_ID;
    if (gaId) {
      initGA(gaId);
      trackPagePerformance();
    }
  }, []);

  return (
    <HelmetProvider>
      <QueryClientProvider client={queryClient}>
        <div className="app-container">
          <BrowserRouter>
            <RouteTracker />
            <AppLayout>
              <AppRouter />
              <Toaster richColors position="top-right" /> {/* Add Toaster component */}
            </AppLayout>
            <CookiesConsent />
            {/*<SEOTester />*/}
          </BrowserRouter>
        </div>
        {import.meta.env.VITE_APP_MODE == "development" && <ReactQueryDevtools initialIsOpen={false} />}
      </QueryClientProvider>
    </HelmetProvider>
  )
}

export default App

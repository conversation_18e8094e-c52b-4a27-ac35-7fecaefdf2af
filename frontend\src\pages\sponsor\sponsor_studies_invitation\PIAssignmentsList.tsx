import { useState } from "react";
import { Search, Filter, Check, X, Users, Al<PERSON><PERSON>riangle, UserCheck } from "lucide-react";
import DataTable from "@/components/common/DataTable";
import { usePIAssignmentsByStudyQuery, useAcceptPIAssignmentMutation, useRejectPIAssignmentMutation } from "@/hooks/study.query";
import { PIAssignment } from "@/services/api/types";
import "./sponsor-studies-invitation.css";

interface PIAssignmentsListProps {
  studyUuid: string;
  studyName?: string;
}

export default function PIAssignmentsList({ studyUuid, studyName }: PIAssignmentsListProps) {
  const [searchTerm, setSearchTerm] = useState("");
  const [statusFilter, setStatusFilter] = useState<string>("all");
  const [confirmationModal, setConfirmationModal] = useState<{
    isOpen: boolean;
    action: "accept" | "reject" | null;
    assignment: PIAssignment | null;
  }>({ isOpen: false, action: null, assignment: null });
  
  const { data: piAssignments, isLoading } = usePIAssignmentsByStudyQuery(studyUuid);
  const acceptPIAssignmentMutation = useAcceptPIAssignmentMutation();
  const rejectPIAssignmentMutation = useRejectPIAssignmentMutation();

  // Filter assignments based on search term and status
  const filteredAssignments = piAssignments?.filter((assignment: PIAssignment) => {
    const matchesSearch = 
      (assignment.department_name?.toLowerCase() || "").includes(searchTerm.toLowerCase()) ||
      (assignment.investigator_name?.toLowerCase() || "").includes(searchTerm.toLowerCase());
    
    const matchesStatus = statusFilter === "all" || assignment.status === statusFilter;
    
    return matchesSearch && matchesStatus;
  }) || [];

  const handleAccept = (assignment: PIAssignment) => {
    setConfirmationModal({ isOpen: true, action: "accept", assignment });
  };

  const handleReject = (assignment: PIAssignment) => {
    setConfirmationModal({ isOpen: true, action: "reject", assignment });
  };

  const handleConfirmAction = async () => {
    if (!confirmationModal.assignment) return;

    try {
      if (confirmationModal.action === "accept") {
        await acceptPIAssignmentMutation.mutateAsync(confirmationModal.assignment.uuid);
      } else if (confirmationModal.action === "reject") {
        await rejectPIAssignmentMutation.mutateAsync(confirmationModal.assignment.uuid);
      }
      setConfirmationModal({ isOpen: false, action: null, assignment: null });
    } catch (error) {
      console.error("Error processing PI assignment:", error);
      // You might want to show an error message to the user here
    }
  };

  const handleCancelAction = () => {
    setConfirmationModal({ isOpen: false, action: null, assignment: null });
  };

  // Define columns for the DataTable
  const columns = [
    {
      key: "department_name" as keyof PIAssignment,
      header: "Department",
      sortable: true,
      render: (value: any) => {
        const departmentName = value as string | undefined;
        return departmentName || "Unknown Department";
      },
    },
    {
      key: "investigator_name" as keyof PIAssignment,
      header: "Principal Investigator",
      sortable: true,
      render: (value: any) => {
        const investigatorName = value as string | undefined;
        return investigatorName || "Unknown Investigator";
      },
    },
    {
      key: "status" as keyof PIAssignment,
      header: "Status",
      sortable: true,
      render: (value: any) => {
        const status = value as string;
        const statusColors = {
          pending: "orange",
          accepted: "green",
          rejected: "red",
        };
        return (
          <span 
            style={{ 
              backgroundColor: statusColors[status as keyof typeof statusColors] || "gray",
              color: "white",
              padding: "4px 8px",
              borderRadius: "4px",
              fontSize: "12px",
              fontWeight: "500"
            }}
          >
            {status.charAt(0).toUpperCase() + status.slice(1)}
          </span>
        );
      },
    },
    {
      key: "assigned_at" as keyof PIAssignment,
      header: "Assigned Date",
      sortable: true,
      render: (value: any) => {
        const assignedAt = value as string | undefined;
        return assignedAt ? new Date(assignedAt).toLocaleDateString() : "N/A";
      },
    },
    {
      key: "accepted_at" as keyof PIAssignment,
      header: "Accepted Date",
      sortable: true,
      render: (value: any) => {
        const acceptedAt = value as string | undefined;
        return acceptedAt ? new Date(acceptedAt).toLocaleDateString() : "N/A";
      },
    },
  ];

  // Define actions for the DataTable
  const actions = [
    {
      icon: <Check size={16} />,
      tooltipText: "Accept PI Assignment",
      onClick: (row: PIAssignment) => handleAccept(row),
      show: (row: PIAssignment) => row.status === "pending",
    },
    {
      icon: <X size={16} />,
      tooltipText: "Reject PI Assignment",
      onClick: (row: PIAssignment) => handleReject(row),
      show: (row: PIAssignment) => row.status === "pending",
    },
  ];

  return (
    <div className="ssi-assignments-container">
      {/* Enhanced Header */}
      <div className="ssi-assignments-header">
        <h2 className="ssi-assignments-title">
          <UserCheck size={24} />
          PI Assignments {studyName && `- ${studyName}`}
        </h2>
        <p className="ssi-assignments-description">
          Principal Investigator assignments for this study across different departments
        </p>
      </div>

      {/* Enhanced Controls */}
      <div className="ssi-assignments-controls">
        <div className="ssi-assignments-search">
          <div className="ssi-search-wrapper">
            <Search size={18} className="ssi-search-icon" />
            <input
              type="text"
              className="ssi-search-input"
              placeholder="Search by department or investigator..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
            />
            {searchTerm && (
              <button
                className="ssi-search-clear"
                onClick={() => setSearchTerm("")}
                title="Clear search"
              >
                ×
              </button>
            )}
          </div>
        </div>
        
        <div className="ssi-assignments-filter">
          <div className="ssi-filter-wrapper">
            <Filter size={16} />
            <select
              className="ssi-filter-select"
              value={statusFilter}
              onChange={(e) => setStatusFilter(e.target.value)}
            >
              <option value="all">All Statuses</option>
              <option value="pending">Pending</option>
              <option value="accepted">Accepted</option>
              <option value="rejected">Rejected</option>
            </select>
          </div>
        </div>
      </div>

      {/* Enhanced Content */}
      <div className="ssi-card">
        {isLoading ? (
          <div className="ssi-loading">
            <div className="ssi-loading-spinner"></div>
            <div className="ssi-loading-text">Loading PI Assignments</div>
            <div className="ssi-loading-subtext">Please wait while we fetch the assignments...</div>
          </div>
        ) : filteredAssignments.length > 0 ? (
          <DataTable
            data={filteredAssignments}
            columns={columns}
            actions={actions}
            defaultItemsPerPage={10}
            itemsPerPageOptions={[5, 10, 25, 50]}
            noDataMessage="No PI assignments found matching your criteria"
          />
        ) : (
          <div className="ssi-empty-state">
            <div className="ssi-empty-icon">
              <Users size={48} />
            </div>
            <h3 className="ssi-empty-title">No PI Assignments Found</h3>
            <p className="ssi-empty-description">
              {searchTerm || statusFilter !== "all" 
                ? "No PI assignments match your current filters. Try adjusting your search criteria."
                : "No PI assignments found for this study. Assignments will appear here once departments submit them."
              }
            </p>
          </div>
        )}
      </div>

      {/* Enhanced Confirmation Modal */}
      {confirmationModal.isOpen && confirmationModal.assignment && (
        <div className="ssi-modal-overlay">
          <div className="ssi-modal ssi-confirmation-modal">
            <div className="ssi-modal-header">
              <h3 className="ssi-modal-title">
                {confirmationModal.action === "accept" ? <Check size={20} /> : <X size={20} />}
                {confirmationModal.action === "accept" ? "Accept" : "Reject"} PI Assignment
              </h3>
              <button className="ssi-modal-close" onClick={handleCancelAction}>
                <X size={20} />
              </button>
            </div>
            <div className="ssi-modal-body">
              <div className="ssi-confirmation-content">
                <div className="ssi-confirmation-icon">
                  <AlertTriangle size={24} />
                </div>
                <h4 className="ssi-confirmation-title">Confirm Action</h4>
                <p className="ssi-confirmation-message">
                  Are you sure you want to{" "}
                  <strong>
                    {confirmationModal.action === "accept" ? "accept" : "reject"}
                  </strong>{" "}
                  the PI assignment for{" "}
                  <strong>{confirmationModal.assignment.investigator_name}</strong>{" "}
                  from <strong>{confirmationModal.assignment.department_name}</strong>?
                </p>
                <div className="ssi-confirmation-details">
                  <div className="ssi-confirmation-detail">
                    <span className="ssi-confirmation-detail-label">Investigator:</span>
                    <span className="ssi-confirmation-detail-value">{confirmationModal.assignment.investigator_name}</span>
                  </div>
                  <div className="ssi-confirmation-detail">
                    <span className="ssi-confirmation-detail-label">Department:</span>
                    <span className="ssi-confirmation-detail-value">{confirmationModal.assignment.department_name}</span>
                  </div>
                  <div className="ssi-confirmation-detail">
                    <span className="ssi-confirmation-detail-label">Current Status:</span>
                    <span className="ssi-confirmation-detail-value">
                      <span className={`ssi-status-badge ssi-status-${confirmationModal.assignment.status}`}>
                        {confirmationModal.assignment.status.charAt(0).toUpperCase() + confirmationModal.assignment.status.slice(1)}
                      </span>
                    </span>
                  </div>
                </div>
              </div>
            </div>
            <div className="ssi-modal-footer">
              <button
                className="ssi-btn ssi-btn-secondary"
                onClick={handleCancelAction}
                disabled={acceptPIAssignmentMutation.isPending || rejectPIAssignmentMutation.isPending}
              >
                Cancel
              </button>
              <button
                className={`ssi-btn ${confirmationModal.action === "accept" ? "ssi-btn-success" : "ssi-btn-danger"}`}
                onClick={handleConfirmAction}
                disabled={acceptPIAssignmentMutation.isPending || rejectPIAssignmentMutation.isPending}
              >
                {acceptPIAssignmentMutation.isPending || rejectPIAssignmentMutation.isPending
                  ? (confirmationModal.action === "accept" ? "Accepting..." : "Rejecting...")
                  : (confirmationModal.action === "accept" ? "Accept" : "Reject")
                }
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}

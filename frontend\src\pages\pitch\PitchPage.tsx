import React from "react";
import "./PitchPage.css";

const PitchPage: React.FC = () => {
  return (
    <div className="pitch-page">
      <div className="particles"></div>
      <section className="hero">
        <div className="container">
          <div className="hero-content">
            <div className="hero-text">
              <h1>Advancing Healthcare Through Clinical Research</h1>
              <p>
                Eliminates your clinical trial daily challenges with our comprehensive, user-friendly platform, delivering faster recruitment, real-time oversight, seamless data capture, and unmatched participant retention.
              </p>
            </div>
            <div className="video-container" style={{ marginTop: "2rem" }}>
              <div style={{ padding: "56.25% 0 0 0", position: "relative" }}>
                <iframe
                  src="https://player.vimeo.com/video/**********?badge=0&amp;autopause=0&amp;player_id=0&amp;app_id=58479"
                  frameBorder="0"
                  allow="autoplay; fullscreen; picture-in-picture; clipboard-write; encrypted-media; web-share"
                  style={{
                    position: "absolute",
                    top: 0,
                    left: 0,
                    width: "100%",
                    height: "100%",
                    borderRadius: "8px",
                  }}
                  title="60 sec Pitch Health Innovation Hub 2025"
                  allowFullScreen
                ></iframe>
              </div>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
};

export default PitchPage;

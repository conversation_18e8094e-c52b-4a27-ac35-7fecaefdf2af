/* PI Assignments Tracker Styles */
.pi-assignments-tracker {
  padding: 20px;
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.tracker-header {
  margin-bottom: 30px;
}

.header-content h1 {
  display: flex;
  align-items: center;
  font-size: 24px;
  font-weight: 600;
  color: #333;
  margin-bottom: 8px;
}

.header-content p {
  color: #666;
  font-size: 14px;
  margin: 0;
}

/* Summary Cards */
.summary-cards {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 20px;
  margin-bottom: 30px;
}

.summary-card {
  display: flex;
  align-items: center;
  padding: 20px;
  border-radius: 8px;
  background: #f8f9fa;
  border: 1px solid #e9ecef;
  transition: all 0.3s ease;
}

.summary-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.summary-card.total {
  border-left: 4px solid #007bff;
}

.summary-card.pending {
  border-left: 4px solid #ffc107;
}

.summary-card.accepted {
  border-left: 4px solid #28a745;
}

.summary-card.rejected {
  border-left: 4px solid #dc3545;
}

.card-icon {
  margin-right: 15px;
  padding: 10px;
  border-radius: 50%;
  background: rgba(0, 123, 255, 0.1);
  color: #007bff;
}

.summary-card.pending .card-icon {
  background: rgba(255, 193, 7, 0.1);
  color: #ffc107;
}

.summary-card.accepted .card-icon {
  background: rgba(40, 167, 69, 0.1);
  color: #28a745;
}

.summary-card.rejected .card-icon {
  background: rgba(220, 53, 69, 0.1);
  color: #dc3545;
}

.card-content h3 {
  font-size: 28px;
  font-weight: 700;
  margin: 0 0 5px 0;
  color: #333;
}

.card-content p {
  font-size: 14px;
  color: #666;
  margin: 0;
}

/* Controls */
.tracker-controls {
  display: flex;
  gap: 20px;
  margin-bottom: 30px;
  align-items: center;
  flex-wrap: wrap;
}

.search-container {
  position: relative;
  flex: 1;
  min-width: 300px;
}

.search-icon {
  position: absolute;
  left: 12px;
  top: 50%;
  transform: translateY(-50%);
  color: #666;
}

.search-input {
  width: 100%;
  padding: 12px 12px 12px 40px;
  border: 1px solid #ddd;
  border-radius: 6px;
  font-size: 14px;
  transition: border-color 0.3s ease;
}

.search-input:focus {
  outline: none;
  border-color: #007bff;
  box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);
}

.filter-container {
  position: relative;
  display: flex;
  align-items: center;
}

.filter-icon {
  position: absolute;
  left: 12px;
  color: #666;
  z-index: 1;
}

.status-filter {
  padding: 12px 12px 12px 40px;
  border: 1px solid #ddd;
  border-radius: 6px;
  font-size: 14px;
  background: #fff;
  cursor: pointer;
  min-width: 150px;
}

.status-filter:focus {
  outline: none;
  border-color: #007bff;
  box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);
}

/* Table Container */
.tracker-table-container {
  background: #fff;
  border-radius: 8px;
  overflow: hidden;
  border: 1px solid #e9ecef;
}

/* Status Badges */
.status-badge {
  display: inline-flex;
  align-items: center;
  gap: 4px;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;
  text-transform: uppercase;
}

.status-badge.pending {
  background: rgba(255, 193, 7, 0.1);
  color: #856404;
}

.status-badge.accepted {
  background: rgba(40, 167, 69, 0.1);
  color: #155724;
}

.status-badge.rejected {
  background: rgba(220, 53, 69, 0.1);
  color: #721c24;
}

.status-badge.unknown {
  background: rgba(108, 117, 125, 0.1);
  color: #495057;
}

/* Action Buttons */
.action-accept {
  color: #28a745 !important;
}

.action-reject {
  color: #dc3545 !important;
}

/* Modal Styles */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.confirmation-modal {
  background: #fff;
  border-radius: 8px;
  width: 90%;
  max-width: 500px;
  max-height: 90vh;
  overflow-y: auto;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px;
  border-bottom: 1px solid #e9ecef;
}

.modal-header h2 {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: #333;
}

.close-btn {
  background: none;
  border: none;
  cursor: pointer;
  padding: 5px;
  color: #666;
  border-radius: 4px;
  transition: background-color 0.3s ease;
}

.close-btn:hover {
  background: #f8f9fa;
}

.modal-content {
  padding: 20px;
}

.modal-content p {
  margin-bottom: 15px;
  color: #333;
}

.assignment-details {
  background: #f8f9fa;
  padding: 15px;
  border-radius: 6px;
  margin-top: 15px;
}

.assignment-details p {
  margin: 5px 0;
  font-size: 14px;
}

.assignment-details strong {
  color: #333;
}

.modal-actions {
  display: flex;
  gap: 10px;
  justify-content: flex-end;
  padding: 20px;
  border-top: 1px solid #e9ecef;
}

.cancel-btn {
  padding: 10px 20px;
  border: 1px solid #ddd;
  background: #fff;
  color: #666;
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
  transition: all 0.3s ease;
}

.cancel-btn:hover {
  background: #f8f9fa;
  border-color: #ccc;
}

.confirm-btn {
  padding: 10px 20px;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  transition: all 0.3s ease;
}

.confirm-btn.accept {
  background: #28a745;
  color: #fff;
}

.confirm-btn.accept:hover {
  background: #218838;
}

.confirm-btn.reject {
  background: #dc3545;
  color: #fff;
}

.confirm-btn.reject:hover {
  background: #c82333;
}

.confirm-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

/* Loading and Error States */
.loading-message {
  text-align: center;
  padding: 40px;
  color: #666;
  font-size: 16px;
}

/* Responsive Design */
@media (max-width: 768px) {
  .summary-cards {
    grid-template-columns: 1fr;
  }
  
  .tracker-controls {
    flex-direction: column;
    align-items: stretch;
  }
  
  .search-container {
    min-width: auto;
  }
  
  .modal-actions {
    flex-direction: column;
  }
  
  .confirm-btn,
  .cancel-btn {
    width: 100%;
  }
}

/* PI Tracker Container Styles for Schedule Event Page */
.pi-tracker-container {
  padding: 20px;
}

.pi-tracker-header {
  margin-bottom: 30px;
}

.pi-header-content h1 {
  display: flex;
  align-items: center;
  font-size: 24px;
  font-weight: 600;
  color: #333;
  margin-bottom: 8px;
}

.pi-header-content p {
  color: #666;
  font-size: 14px;
  margin: 0;
} 
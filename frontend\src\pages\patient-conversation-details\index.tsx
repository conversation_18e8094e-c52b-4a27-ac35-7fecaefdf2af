import React, { useState } from "react";
import { useParams, useNavigate } from "react-router-dom";
import {
  useConversation,
  useSetCompleted,
  useSetRejected,
} from "@/hooks/conversation.query";
import LightFooter from "@/shared/LightFooter";
import { motion } from "framer-motion";
import "./patientConversationDetails.css";
import CommentModal from "@/components/CommentModal";
import { Conversation } from "@/services/api/types";

type ModalAction = "complete" | "reject" | null;

interface Answer {
  answer: boolean | string;
}

// Extend the Conversation type to include 'completed' status
interface ExtendedConversation extends Omit<Conversation, 'status'> {
  status: "pending" | "inprogress" | "forwarded_to_finance" | "rejected" | "reimbursed" | "completed";
}

const PatientConversationDetails: React.FC = () => {
  const { uuid } = useParams<{ uuid: string }>();
  const navigate = useNavigate();
  const { data: conversationData, isLoading } = useConversation(uuid || "");
  const setCompletedMutation = useSetCompleted(uuid || "");
  const setRejectedMutation = useSetRejected(uuid || "");

  const [showCommentModal, setShowCommentModal] = useState(false);
  const [modalAction, setModalAction] = useState<ModalAction>(null);


  const handleActionClick = (action: "complete" | "reject") => {
    setModalAction(action);
    setShowCommentModal(true);
  };

  const handleCommentSubmit = async (comments: string) => {
    if (!uuid || !modalAction) return;

    try {
      if (modalAction === "complete") {
        await setCompletedMutation.mutateAsync({ comment: comments });
      } else if (modalAction === "reject") {
        await setRejectedMutation.mutateAsync({ comment: comments });
      }
      setShowCommentModal(false);
      setModalAction(null);
      navigate("/ask-nurtifiers");
    } catch (error) {
      console.error(`Error setting conversation to ${modalAction}:`, error);
    }
  };

  if (isLoading) {
    return <div className="loading">Loading conversation...</div>;
  }

  if (!conversationData) {
    return <div className="error">Conversation not found</div>;
  }

  // Cast the conversation data to ExtendedConversation type
  const conversation = conversationData as ExtendedConversation;

  // Check if the conversation is of type "refund" by looking for "Attach file" in questions
  const isRefundForm = Object.keys(conversation.questions || {}).includes("Attach file");

  const renderAttachment = () => {
    if (!isRefundForm || !conversation.attach_content) return null;

    const fileUrl = conversation.attach_content; // Assuming this is a URL string from backend
    const isImage = /\.(jpg|jpeg|png|gif)$/i.test(fileUrl);
    const isPdf = /\.pdf$/i.test(fileUrl);

    return (
      <div
        className="convdetails-attachment-section"
        style={{
          margin: "10px 0",
          padding: "15px",
          backgroundColor: "#f9f9f9",
          borderRadius: "10px",
        }}
      >
        <h2 style={{ fontWeight: "bold" }}>Attached File</h2>
        {isImage ? (
          <img
            src={fileUrl}
            alt="Attached file"
            style={{ maxWidth: "100%", height: "auto", borderRadius: "5px" }}
          />
        ) : isPdf ? (
          <embed
            src={fileUrl}
            type="application/pdf"
            width="100%"
            height="500px"
            style={{ borderRadius: "5px" }}
          />
        ) : (
          <a href={fileUrl} target="_blank" rel="noopener noreferrer">
            Download Attached File
          </a>
        )}
      </div>
    );
  };

  const renderAnswer = (value: unknown): string => {
    if (value && typeof value === 'object' && 'answer' in value) {
      const answer = value as Answer;
      if (typeof answer.answer === 'boolean') {
        return answer.answer ? "Yes" : "No";
      }
      return String(answer.answer);
    }
    return String(value);
  };

  return (
    <div className="convdetails-main">
        <motion.div
          className="convdetails-container"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
        >
          <div className="convdetails-header">
            <h1>Conversation Details</h1>
          </div>

          <div className="convdetails-card">
            <div className="convdetails-patient-info">
              <h2>Patient Information</h2>
              <div className="convdetails-info-grid">
                <div className="convdetails-info-item">
                  <label>Name:</label>
                  <span>
                    {conversation?.patient_details?.first_name}{" "}
                    {conversation?.patient_details?.last_name}
                  </span>
                </div>
                <div className="convdetails-info-item">
                  <label>Email:</label>
                  <span>{conversation?.patient_details?.email}</span>
                </div>
                {conversation?.treated_by_details && (
                  <div className="convdetails-info-item">
                    <label>Treated By:</label>
                    <span>
                      {conversation.treated_by_details.first_name}{" "}
                      {conversation.treated_by_details.last_name}
                    </span>
                  </div>
                )}
                <div className="convdetails-info-item">
                  <label>Status:</label>
                  <span>{conversation.status}</span>
                </div>
                <div className="convdetails-info-item">
                  <label>Last Update:</label>
                  <span>{conversation.last_update_person}</span>
                </div>
                <div className="convdetails-info-item">
                  <label>Created At:</label>
                  <span>
                    {new Date(conversation.created_at).toLocaleDateString()}
                  </span>
                </div>
              </div>
            </div>

            {conversation.comments && (
              <div
                className="convdetails-comments-section"
                style={{
                  backgroundColor: "#e0f7f9",
                  borderRadius: "10px",
                  padding: "15px",
                  margin: "10px 0",
                }}
              >
                <h2 style={{ fontWeight: "bold" }}>
                  {conversation.last_update_person} Comments
                </h2>
                <p className="convdetails-comment">{conversation.comments}</p>
              </div>
            )}

            {renderAttachment()}

            <div className="convdetails-questions-section">
              <h2>Questions</h2>
              {Object.entries(conversation.questions || {}).map(
                ([questionName, value], index) => (
                  <div key={index} className="convdetails-question-item">
                    <div className="convdetails-question-header">
                      <h3>{questionName}</h3>
                    </div>
                    <div className="convdetails-answer">
                      <p className="convdetails-answer-content">
                        {renderAnswer(value)}
                      </p>
                    </div>
                  </div>
                )
              )}
            </div>

            <div className="convdetails-action-buttons">
              <button
                className="convdetails-reject-button"
                onClick={() => handleActionClick("reject")}
                disabled={setRejectedMutation.isPending}
                hidden={
                  conversation.status === "rejected" ||
                  conversation.status === "completed" ||
                  conversation.status === "pending"
                }
              >
                Reject
              </button>
              <button
                className="convdetails-complete-button"
                onClick={() => handleActionClick("complete")}
                disabled={setCompletedMutation.isPending}
                hidden={
                  conversation.status === "rejected" ||
                  conversation.status === "completed" ||
                  conversation.status === "pending"
                }
              >
                Complete
              </button>
            </div>
          </div>
        </motion.div>
      
      <CommentModal
        isOpen={showCommentModal}
        onClose={() => {
          setShowCommentModal(false);
          setModalAction(null);
        }}
        onSubmit={handleCommentSubmit}
        action={modalAction || "complete"}
      />
      <LightFooter />
    </div>
  );
};

export default PatientConversationDetails;
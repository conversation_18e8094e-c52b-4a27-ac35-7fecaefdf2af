import { useState } from "react";
import { X, Building2, Users, Mail } from "lucide-react";
import { useDepartmentsQuery } from "@/hooks/department.query";
import { useStudyInvitationsByInviterQuery } from "@/hooks/study.query";
import { useCurrentUserQuery } from "@/hooks/user.query";
import { Department } from "@/services/api/types";
import NurtifyButton from "../NurtifyButton";
import "./modal.css";

interface Invitation {
  department: string;
  status: string;
  study: string;
}

interface InviteDepartmentModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSave: (selectedDepartments: string[]) => void;
  studyName: string;
  studyUuid: string;
}

export default function InviteDepartmentModal({
  isOpen,
  onClose,
  onSave,
  studyName,
  studyUuid,
}: InviteDepartmentModalProps) {
  const [selectedDepartments, setSelectedDepartments] = useState<string[]>([]);
  const { data: currentUser } = useCurrentUserQuery();
  const { data: departments, isLoading: isLoadingDepartments } = useDepartmentsQuery();
  const { data: invitations, isLoading: isLoadingInvitations } = useStudyInvitationsByInviterQuery(currentUser?.identifier || "");

  // Filter out departments that have pending or accepted invitations for THIS specific study
  const filteredDepartments = departments?.filter((department: Department) => {
    if (!department.uuid) return false;
    const departmentInvitations = invitations?.filter((inv: Invitation) => 
      inv.department === department.uuid && inv.study === studyUuid
    ) || [];
    return !departmentInvitations.some((inv: Invitation) => 
      inv.status === "pending" || inv.status === "accepted"
    );
  });

  const handleDepartmentChange = (departmentId: string) => {
    setSelectedDepartments(prev => {
      if (prev.includes(departmentId)) {
        return prev.filter(id => id !== departmentId);
      }
      return [...prev, departmentId];
    });
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    onSave(selectedDepartments);
  };

  if (!isOpen) return null;

  return (
    <div className="modal-overlay">
      <div className="modal-content">
        <div className="modal-header">
          <h2>
            <Mail size={24} />
            Invite Departments to {studyName}
          </h2>
          <button className="close-button" onClick={onClose}>
            <X size={20} />
          </button>
        </div>
        <form onSubmit={handleSubmit}>
          <div className="modal-body">
            {isLoadingDepartments || isLoadingInvitations ? (
              <div className="loading">
                <div></div>
                Loading departments...
              </div>
            ) : (
              <div className="department-selection">
                <div style={{ marginBottom: '16px' }}>
                  <div style={{ display: 'flex', alignItems: 'center', gap: '8px', marginBottom: '8px' }}>
                    <Building2 size={20} style={{ color: '#37B7C3' }} />
                    <label className="form-label" style={{ margin: 0 }}>Select Departments to Invite</label>
                  </div>
                  <p style={{ 
                    color: '#64748b', 
                    fontSize: '14px', 
                    margin: 0,
                    lineHeight: '1.5'
                  }}>
                    Choose which departments you'd like to invite to participate in this study. 
                    Only departments without existing invitations are shown.
                  </p>
                </div>
                
                {filteredDepartments && filteredDepartments.length > 0 ? (
                  <>
                    <div style={{
                      background: '#f8fafc',
                      border: '1px solid #e2e8f0',
                      borderRadius: '8px',
                      padding: '12px 16px',
                      marginBottom: '16px',
                      display: 'flex',
                      alignItems: 'center',
                      gap: '8px'
                    }}>
                      <Users size={16} style={{ color: '#37B7C3' }} />
                      <span style={{ fontSize: '14px', color: '#475569' }}>
                        {selectedDepartments.length} of {filteredDepartments.length} departments selected
                      </span>
                    </div>
                    
                    <div className="department-list">
                      {filteredDepartments.map((department: Department) => (
                        <div key={department.uuid} className="department-item">
                          <label className="checkbox-label">
                            <input
                              type="checkbox"
                              checked={selectedDepartments.includes(department.uuid || '')}
                              onChange={() => handleDepartmentChange(department.uuid || '')}
                            />
                            <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
                              <Building2 size={16} style={{ color: '#64748b' }} />
                              <span>{department.name}</span>
                            </div>
                          </label>
                        </div>
                      ))}
                    </div>
                  </>
                ) : (
                  <div style={{
                    textAlign: 'center',
                    padding: '40px 20px',
                    background: '#f8fafc',
                    borderRadius: '12px',
                    border: '2px dashed #e2e8f0'
                  }}>
                    <Building2 size={48} style={{ color: '#94a3b8', marginBottom: '16px' }} />
                    <h3 style={{ 
                      color: '#1e293b', 
                      fontSize: '18px', 
                      fontWeight: '600', 
                      margin: '0 0 8px 0' 
                    }}>
                      No Available Departments
                    </h3>
                    <p style={{ 
                      color: '#64748b', 
                      margin: 0, 
                      lineHeight: '1.5' 
                    }}>
                      All departments have already been invited to this study or there are no departments available.
                    </p>
                  </div>
                )}
              </div>
            )}
          </div>
          <div className="modal-footer">
            <NurtifyButton
              type="button"
              variant="light"
              size="regular"
              onClick={onClose}
            >
              Cancel
            </NurtifyButton>
            <NurtifyButton
              type="submit"
              variant="primary"
              size="regular"
              disabled={selectedDepartments.length === 0}
            >
              <Mail size={16} />
              Invite {selectedDepartments.length > 0 ? `${selectedDepartments.length} ` : ''}Department{selectedDepartments.length !== 1 ? 's' : ''}
            </NurtifyButton>
          </div>
        </form>
      </div>
    </div>
  );
}

.individual-vital-sign-history-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: 20px;
}

.individual-vital-sign-history-modal {
  background: white;
  border-radius: 12px;
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  width: 100%;
  max-width: 800px;
  max-height: 90vh;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.history-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24px 32px;
  border-bottom: 1px solid #e5e7eb;
  background: #f8f9fa;
}

.history-header h2 {
  margin: 0;
  font-size: 24px;
  font-weight: 600;
  color: #1f2937;
}

.close-button {
  background: none;
  border: none;
  font-size: 24px;
  color: #6b7280;
  cursor: pointer;
  padding: 8px;
  border-radius: 6px;
  transition: all 0.2s ease;
}

.close-button:hover {
  background: #e5e7eb;
  color: #374151;
}

.history-filters {
  display: flex;
  gap: 16px;
  padding: 20px 32px;
  border-bottom: 1px solid #e5e7eb;
  background: white;
  flex-wrap: wrap;
  align-items: end;
}

.filter-group {
  display: flex;
  flex-direction: column;
  gap: 8px;
  min-width: 150px;
}

.filter-group label {
  font-size: 14px;
  font-weight: 500;
  color: #374151;
}

.filter-group input {
  padding: 8px 12px;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  font-size: 14px;
  background: white;
  transition: border-color 0.2s ease;
}

.filter-group input:focus {
  outline: none;
  border-color: #37B7C3;
  box-shadow: 0 0 0 3px rgba(55, 183, 195, 0.1);
}

.date-range {
  display: flex;
  align-items: center;
  gap: 8px;
}

.date-range span {
  font-size: 14px;
  color: #6b7280;
}

.history-content {
  flex: 1;
  overflow-y: auto;
  padding: 0;
}

.history-loading,
.history-error,
.history-empty {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 32px;
  text-align: center;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid #e5e7eb;
  border-top: 4px solid #37B7C3;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 16px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.history-timeline {
  padding: 24px 32px;
}

.history-entry {
  display: flex;
  gap: 16px;
  margin-bottom: 24px;
  position: relative;
}

.history-entry:not(:last-child)::after {
  content: '';
  position: absolute;
  left: 20px;
  top: 40px;
  bottom: -24px;
  width: 2px;
  background: #e5e7eb;
}

.history-timeline-marker {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
  position: relative;
  z-index: 1;
}

.change-icon {
  font-size: 16px;
  color: white;
}

.history-entry-content {
  flex: 1;
  background: #f9fafb;
  border-radius: 8px;
  padding: 16px;
  border-left: 4px solid #e5e7eb;
}

.history-entry-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.history-entry-header h4 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #1f2937;
}

.change-type {
  font-size: 12px;
  font-weight: 500;
  padding: 4px 8px;
  border-radius: 4px;
  background: rgba(255, 255, 255, 0.8);
}

.history-entry-details {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.value-change,
.value-added {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
}

.old-value {
  color: #6b7280;
  text-decoration: line-through;
}

.arrow {
  color: #9ca3af;
  font-weight: bold;
}

.new-value {
  color: #1f2937;
  font-weight: 500;
}

.history-meta {
  display: flex;
  flex-direction: column;
  gap: 8px;
  font-size: 12px;
  color: #6b7280;
  padding-top: 8px;
  border-top: 1px solid #e5e7eb;
}

.history-user,
.history-timestamp,
.history-notes {
  display: flex;
  align-items: center;
  gap: 8px;
}

.user-label,
.timestamp-label,
.notes-label {
  font-weight: 500;
  min-width: 80px;
}

.user-name {
  font-weight: 500;
  color: #374151;
}

.user-email {
  color: #9ca3af;
}

.timestamp-value {
  font-weight: 500;
  color: #374151;
}

.notes-value {
  color: #374151;
  font-style: italic;
}

.btn {
  padding: 8px 16px;
  border: none;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.btn-primary {
  background: #37B7C3;
  color: white;
}

.btn-primary:hover {
  background: #2a8f99;
}

.btn-secondary {
  background: #6b7280;
  color: white;
}

.btn-secondary:hover {
  background: #4b5563;
}

/* Responsive design */
@media (max-width: 768px) {
  .individual-vital-sign-history-modal {
    max-width: 95vw;
    max-height: 95vh;
  }
  
  .history-filters {
    flex-direction: column;
    align-items: stretch;
  }
  
  .filter-group {
    min-width: auto;
  }
  
  .date-range {
    flex-direction: column;
    align-items: stretch;
  }
  
  .history-entry {
    flex-direction: column;
    gap: 12px;
  }
  
  .history-timeline-marker {
    align-self: flex-start;
  }
  
  .history-entry:not(:last-child)::after {
    left: 20px;
    top: 40px;
    bottom: -12px;
  }
} 
export interface RescheduleRequest {
  visit_uuid: string;
  requested_date: string;
  requested_time: string;
  reason: string;
}

export interface RescheduleResponse {
  id: number;
  visit_uuid: string;
  requested_date: string;
  requested_time: string;
  reason: string;
  status: 'pending' | 'approved' | 'rejected';
  created_at: string;
  updated_at: string;
}

export interface RescheduleRequestResponse {
  uuid: string;
  visit: {
    uuid: string;
    name: string;
    date: string;
    time: string;
    patient: {
      uuid: string;
      first_name: string;
      last_name: string;
    };
    study: {
      uuid: string;
      name: string;
    };
  };
  requested_date: string;
  requested_time: string;
  reason: string;
  status: 'Pending' | 'Approved' | 'Rejected';
  created_at: string;
  updated_at: string;
} 
import { useState, useEffect } from 'react';
import { Outlet } from 'react-router-dom';
import Loader from './Loader';

const PageLoader = () => {
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    const timer = setTimeout(() => {
      setIsLoading(false);
    }, 2000);

    return () => clearTimeout(timer);
  }, []);

  if (isLoading) {
    return <Loader />;
  }

  return <Outlet />;
};

export default PageLoader; 
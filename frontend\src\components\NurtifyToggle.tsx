import { useState, useEffect } from "react";
import "./NurtifyToggle.css";

interface NurtifyToggleProps {
    id? : string;
    name: string;
    value?: string;
    onChange?: (value: string) => void;
    label?: string;
    labels?: [string, string];
}

const NurtifyToggle: React.FC<NurtifyToggleProps> = ({ name, value, onChange, label, labels = ["True", "False"] }) => {
    const [checked, setChecked] = useState(value === "true");

    useEffect(() => {
        setChecked(value === "true");
    }, [value]);

    const handleToggle = () => {
        const newValue = !checked;
        setChecked(newValue);
        if (onChange) {
            onChange(newValue.toString());
        }
    };

    return (
        <div className="nurtify-toggle-wrapper">
            {label && <span className="nurtify-toggle-label">{label}</span>}
            <div className="nurtify-toggle" onClick={handleToggle}>
                <div className={`nurtify-toggle-slider ${checked ? "checked" : ""}`}>
                    <div className="nurtify-toggle-circle"></div>
                    <span className="nurtify-toggle-text" style={{ left: '-25%', color: !checked ? 'white' : undefined }}>
                        {labels[0]}
                    </span>
                    <span className="nurtify-toggle-text" style={{ left: '25%', color: checked ? 'white' : undefined }}>
                        {labels[1]}
                    </span>
                </div>
                <input
                    type="checkbox"
                    id={name}
                    name={name}
                    checked={checked}
                    onChange={handleToggle}
                    style={{ display: "none" }}
                />
            </div>
        </div>
    );
};

export default NurtifyToggle;

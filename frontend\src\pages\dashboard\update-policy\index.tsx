import Preloader from "@/components/common/Preloader";
import "./updatePolicy.css";
import { useEffect, useState } from "react";
import { FileText } from "lucide-react";
import NurtifyText from "@components/NurtifyText.tsx";
import NurtifyInput from "@components/NurtifyInput.tsx";
import NurtifySelect from "@components/NurtifySelect";
import NurtifyAttachFileBox from "@/components/common/NurtifyAttachFileBox";
import { useUpdatePolicyMutation } from "@/hooks/policy.query.ts";
import { useNavigate, useParams } from "react-router-dom";
import { getPolicyByUuid } from "@/services/api/policy.service";
import { POLICY_KEYS } from "@/hooks/keys";
import { useQuery } from "@tanstack/react-query";
import { Policy } from "@/types/types";
import { useStudiesQuery } from "@/hooks/study.query";

// Define policy categories to match backend model
const POLICY_CATEGORIES = [
  { label: "Clinical Trial Protocol", value: "CLINICAL_TRIAL_PROTOCOL" },
  { label: "Lab Manual", value: "LAB_MANUAL" },
  { label: "Pharmacy Manual", value: "PHARMACY_MANUAL" },
  { label: "Imaging Manual", value: "IMAGING_MANUAL" },
  { label: "ECG or Cardiac Monitoring Manual", value: "ECG_MANUAL" },
  { label: "Randomization and Unblinding Procedures", value: "RANDOMIZATION_PROCEDURES" },
  { label: "Patient Information Sheet", value: "PATIENT_INFO_SHEET" },
  { label: "Patient Education Brochures", value: "PATIENT_EDUCATION" },
  { label: "Safety Management Plan", value: "SAFETY_MANAGEMENT" },
  { label: "Site Visit Schedule", value: "SITE_VISIT_SCHEDULE" },
  { label: "Contact list of study personnel and site staff", value: "CONTACT_LIST_STUDY_PERSONNEL" },
  { label: "Contact details of external vendors", value: "CONTACT_DETAILS_EXTERNAL_VENDORS" },
  { label: "Current versions of the Investigator Brochure and Product Information Forms", value: "CURRENT_VERSIONS_INVESTIGATOR_BROCHURE" },
  { label: "Previous submitted versions", value: "PREVIOUS_SUBMITTED_VERSIONS" },
  { label: "Current approved version", value: "CURRENT_APPROVED_VERSION_1" },
  { label: "Previous approved versions and updates", value: "PREVIOUS_APPROVED_VERSIONS_UPDATES" },
  { label: "Signature pages", value: "SIGNATURE_PAGES" },
  { label: "Current approved versions", value: "CURRENT_APPROVED_VERSIONS_2" },
  { label: "Previous approved versions", value: "PREVIOUS_APPROVED_VERSIONS_2" },
  { label: "Signed Informed Consent forms", value: "SIGNED_INFORMED_CONSENT_FORMS" },
  { label: "Signed Informed Consent Tracking Log", value: "SIGNED_INFORMED_CONSENT_TRACKING_LOG" },
  { label: "Patient Card, Diary or Questionnaire", value: "PATIENT_CARD_DIARY_QUESTIONNAIRE" },
  { label: "Current approved versions", value: "CURRENT_APPROVED_VERSIONS_3" },
  { label: "Previous approved copies", value: "PREVIOUS_APPROVED_COPIES" },
  { label: "Translation certificates", value: "TRANSLATION_CERTIFICATES_1" },
  { label: "Current approved version", value: "CURRENT_APPROVED_VERSION_2" },
  { label: "Previous approved versions", value: "PREVIOUS_APPROVED_VERSIONS_3" },
  { label: "Translation certificates", value: "TRANSLATION_CERTIFICATES_2" },
  { label: "Current version (blank sample)", value: "CURRENT_VERSION_BLANK_SAMPLE" },
  { label: "Previous version (blank sample)", value: "PREVIOUS_VERSION_BLANK_SAMPLE" },
  { label: "Completion guidelines", value: "COMPLETION_GUIDELINES" },
  { label: "Signed, dated and completed CRFs", value: "SIGNED_DATED_COMPLETED_CRFS" },
  { label: "Documentation of CRF edits made", value: "DOCUMENTATION_CRF_EDITS" },
  { label: "Initial submission", value: "INITIAL_SUBMISSION" },
  { label: "Amendments", value: "AMENDMENTS" },
  { label: "Progress Reports", value: "PROGRESS_REPORTS" },
  { label: "Ethics Composition", value: "ETHICS_COMPOSITION" },
  { label: "Notification of Safety Reports", value: "NOTIFICATION_SAFETY_REPORTS" },
  { label: "Notification of Non-compliance and Protocol Deviations", value: "NOTIFICATION_NON_COMPLIANCE" },
  { label: "Correspondence", value: "CORRESPONDENCE_1" },
  { label: "Site Authorisation Letter", value: "SITE_AUTHORISATION_LETTER" },
  { label: "Post Authorisation Submission and Authorisation Letters", value: "POST_AUTHORISATION_SUBMISSION" },
  { label: "Clinical Trial Notification (CTN) or Clinical Trial Exemption (CTX) forms", value: "CLINICAL_TRIAL_NOTIFICATION_CTN_CTX" },
  { label: "Therapeutic Goods Administration (TGA) acknowledgement letter", value: "TGA_ACKNOWLEDGEMENT_LETTER" },
  { label: "Correspondence", value: "CORRESPONDENCE_2" },
  { label: "Delegation Log/Signature sheet", value: "DELEGATION_LOG_SIGNATURE_SHEET" },
  { label: "Curriculum Vitae (CV) (including GCP and Medical License, etc)", value: "CURRICULUM_VITAE_CV" },
  { label: "Training log and documentation", value: "TRAINING_LOG_DOCUMENTATION" },
  { label: "Signed Confidentiality Agreement", value: "SIGNED_CONFIDENTIALITY_AGREEMENT" },
  { label: "Signed Clinical Trial Agreement", value: "SIGNED_CLINICAL_TRIAL_AGREEMENT" },
  { label: "Other relevant agreements/contracts", value: "OTHER_RELEVANT_AGREEMENTS" },
  { label: "Insurance Certificate", value: "INSURANCE_CERTIFICATE" },
  { label: "Indemnity", value: "INDEMNITY" },
  { label: "Participant Screening Log", value: "PARTICIPANT_SCREENING_LOG" },
  { label: "Participant Enrolment Log", value: "PARTICIPANT_ENROLMENT_LOG" },
  { label: "Participant Identification Log", value: "PARTICIPANT_IDENTIFICATION_LOG" },
  { label: "Participant Visit Tracking Log", value: "PARTICIPANT_VISIT_TRACKING_LOG" },
  { label: "Instructions for handling the IP", value: "INSTRUCTIONS_HANDLING_IP" },
  { label: "Shipping and receipt records", value: "SHIPPING_RECEIPT_RECORDS" },
  { label: "Dispensing and Accountability logs", value: "DISPENSING_ACCOUNTABILITY_LOGS" },
  { label: "IP Destruction Logs", value: "IP_DESTRUCTION_LOGS" },
  { label: "IP Storage and Temperature Logs", value: "IP_STORAGE_TEMPERATURE_LOGS" },
  { label: "Decoding and Un-blinding Procedure", value: "DECODING_UNBLINDING_PROCEDURE" },
  { label: "Sample of labels attached to IP containers", value: "SAMPLE_LABELS_IP_CONTAINERS" },
  { label: "Instructions", value: "INSTRUCTIONS_3" },
  { label: "Un-blinding process", value: "UNBLINDING_PROCESS" },
  { label: "Normal values and ranges for medical procedures and tests included in protocol", value: "NORMAL_VALUES_RANGES" },
  { label: "Certification/Accreditation/Established Quality Control/Validation for medical procedures and tests", value: "CERTIFICATION_ACCREDITATION" },
  { label: "Sample Log", value: "SAMPLE_LOG" },
  { label: "Sample Handling Manual", value: "SAMPLE_HANDLING_MANUAL" },
  { label: "Sample Shipping Record", value: "SAMPLE_SHIPPING_RECORD" },
  { label: "Laboratory Manual and Certification", value: "LABORATORY_MANUAL_CERTIFICATION" },
  { label: "Shipping Materials", value: "SHIPPING_MATERIALS" },
  { label: "Samples Destruction/Return Records", value: "SAMPLES_DESTRUCTION_RETURN_RECORDS" },
  { label: "Serious Adverse Event (SAE) Tracking Log", value: "SAE_TRACKING_LOG" },
  { label: "SAE Reports which have been submitted to the Sponsor", value: "SAE_REPORTS_SUBMITTED_SPONSOR" },
  { label: "Safety Reports", value: "SAFETY_REPORTS" },
  { label: "Interim Report and Data Safety Monitoring Boards (DSMB) Reports", value: "INTERIM_REPORT_DSMB" },
  { label: "Final Clinical Study Report", value: "FINAL_CLINICAL_STUDY_REPORT" },
  { label: "Relevant Study Publications and References", value: "RELEVANT_STUDY_PUBLICATIONS" },
  { label: "Investigator Meeting (agenda and presentation)", value: "INVESTIGATOR_MEETING_AGENDA" },
  { label: "Site Initiation Visit (agenda and presentation)", value: "SITE_INITIATION_VISIT_AGENDA" },
  { label: "Other relevant meeting documentation", value: "OTHER_RELEVANT_MEETING_DOCS" },
  { label: "Correspondence with Trial Sponsor", value: "CORRESPONDENCE_TRIAL_SPONSOR" },
  { label: "Correspondence with Site Staff", value: "CORRESPONDENCE_SITE_STAFF" },
  { label: "Correspondence with Laboratory/Vendors", value: "CORRESPONDENCE_LABORATORY_VENDORS" },
  { label: "Other relevant correspondence", value: "OTHER_RELEVANT_CORRESPONDENCE" },
  { label: "Newsletters", value: "NEWSLETTERS" },
  { label: "Other", value: "OTHER" },
] as const;

interface FormData {
  title: string;
  category: string;
  author_name: string;
  job_title: string;
  description: string;
  attach_content: File | null;
  study_uuid: string;
}

interface ConfirmationUpdatePolicyModalProps {
  isOpen: boolean;
  onClose: () => void;
  onConfirm: () => void;
  title: string;
  message: string;
  subMessage?: string;
}

interface Study {
  uuid: string;
  name: string;
}

function ConfirmationModal({
  isOpen,
  onClose,
  onConfirm,
  title,
  message,
  subMessage,
}: ConfirmationUpdatePolicyModalProps) {
  if (!isOpen) return null;

  return (
    <div
      style={{
        position: "fixed",
        top: 0,
        left: 0,
        right: 0,
        bottom: 0,
        backgroundColor: "rgba(0, 0, 0, 0.5)",
        display: "flex",
        alignItems: "center",
        justifyContent: "center",
        zIndex: 1000,
      }}
    >
      <div
        style={{
          backgroundColor: "white",
          borderRadius: "16px",
          padding: "32px",
          width: "90%",
          maxWidth: "500px",
          boxShadow: "0 4px 6px rgba(0, 0, 0, 0.1)",
        }}
      >
        <h2
          style={{
            color: "#1B2559",
            fontSize: "24px",
            fontWeight: "600",
            marginBottom: "16px",
          }}
        >
          {title}
        </h2>
        <p
          style={{
            color: "#1B2559",
            fontSize: "16px",
            marginBottom: "8px",
          }}
        >
          {message}
        </p>
        {subMessage && (
          <p
            style={{
              color: "#64748B",
              fontSize: "14px",
              marginBottom: "24px",
            }}
          >
            {subMessage}
          </p>
        )}
        <div
          style={{
            display: "flex",
            justifyContent: "flex-end",
            gap: "12px",
            marginTop: "24px",
          }}
        >
          <button
            onClick={onClose}
            style={{
              padding: "12px 24px",
              borderRadius: "8px",
              border: "none",
              backgroundColor: "#94A3B8",
              color: "white",
              cursor: "pointer",
              fontSize: "14px",
              fontWeight: "500",
            }}
          >
            Cancel
          </button>
          <button
            onClick={onConfirm}
            style={{
              padding: "12px 24px",
              borderRadius: "8px",
              border: "none",
              backgroundColor: "#37B7C3",
              color: "white",
              cursor: "pointer",
              fontSize: "14px",
              fontWeight: "500",
            }}
          >
            Proceed
          </button>
        </div>
      </div>
    </div>
  );
}

export default function UpdatePolicy() {
  const navigate = useNavigate();
  const { uuid } = useParams<{ uuid: string }>();
  const updatePolicyMutation = useUpdatePolicyMutation();
  const { data: studiesData } = useStudiesQuery();
  const [formData, setFormData] = useState<FormData>({
    title: "",
    category: "",
    author_name: "",
    job_title: "",
    description: "",
    attach_content: null,
    study_uuid: "",
  });
  const [isConfirmModalOpen, setIsConfirmModalOpen] = useState(false);

  const handleCategoryChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    setFormData((prev) => ({
      ...prev,
      category: e.target.value,
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsConfirmModalOpen(true); // Show confirmation modal
  };

  const handleInputChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>
  ) => {
    const { name, value } = e.target;
    setFormData((prev) => ({
      ...prev,
      [name]: value,
    }));
  };
  const queryEnabled = Boolean(uuid);
  const { data: policyData, isLoading } = useQuery({
    queryKey: [POLICY_KEYS.GET_BY_UUID, uuid],
    queryFn: () => getPolicyByUuid(uuid!),
    enabled: queryEnabled,
  });

  // Convert studies data to options for the select component
  const studyOptions = studiesData?.map((study: Study) => ({
    value: study.uuid,
    label: study.name,
  })) || [];

  const handleStudyChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    setFormData((prev) => ({
      ...prev,
      study_uuid: e.target.value,
    }));
  };

  useEffect(() => {
    if (policyData) {
      setFormData((prev) => ({
        title: policyData.title || "",
        category: policyData.category || "",
        author_name: policyData.author_name || "",
        job_title: policyData.job_title || "",
        description: policyData.description || "",
        attach_content: prev.attach_content,
        study_uuid: policyData.study.uuid || "",
      }));
      console.log(formData);
      console.log(policyData);
    }
  }, [policyData]);

  const handleConfirmUpdate = async () => {
    if (!uuid) return;

    const submitData = new FormData();
    submitData.append("title", formData.title);
    submitData.append("category", formData.category);
    submitData.append("author_name", formData.author_name);
    submitData.append("job_title", formData.job_title);
    submitData.append("description", formData.description);

    submitData.append("study_uuid", formData.study_uuid);

    if (formData.attach_content) {
      submitData.append(
        "attach_content",
        formData.attach_content,
        formData.attach_content.name
      );
    }

    try {
      await updatePolicyMutation.mutateAsync({
        uuid,
        data: submitData as Partial<Policy>,
      });
      setIsConfirmModalOpen(false);
      navigate("/sponsor/policy");
    } catch (error) {
      console.error("Error updating policy:", error);
      setIsConfirmModalOpen(false);
    }
  };

  return (
    <div className="update-policy-container">
      <div>
        {/* Header with icon and title */}
        <div className="update-policy-header">
          <div className="update-policy-title">
            <h1>
              <FileText size={24} style={{ marginRight: "10px" }} />
              Update Policy
            </h1>
          </div>
          <div className="update-policy-subtitle">
            <h6>Modify and update existing policy information</h6>
          </div>
        </div>

        {/* Fragment pour éviter l'erreur JSX */}
        <>
          {isLoading && <Preloader />}
          {!isLoading && (
            <form className="add-policy-form" onSubmit={handleSubmit}>
              <div className="form-group col-md-12">
                <NurtifyText label="Policy Title*" />
                <NurtifyInput
                  type="text"
                  name="title"
                  value={formData.title}
                  onChange={(e) =>
                    setFormData({ ...formData, title: e.target.value })
                  }
                  placeholder="Policy Title"
                  required
                />
              </div>
              <div className="form-group col-md-12">
                <NurtifyText label="Category*" />
                <NurtifySelect
                  name="category"
                  value={formData.category}
                  onChange={handleCategoryChange}
                  options={[
                    { value: "", label: "Select a category" },
                    ...POLICY_CATEGORIES
                  ]}
                />
              </div>
              <div className="form-group col-md-12">
                <NurtifyText label="Author Name" />
                <NurtifyInput
                  type="text"
                  name="author_name"
                  id="author_name"
                  value={formData.author_name}
                  onChange={handleInputChange}
                  placeholder="Author Name"
                  required
                />
              </div>
              <div className="form-group col-md-12">
                <NurtifyText label="Job Title*" />
                <NurtifyInput
                  type="text"
                  name="job_title"
                  value={formData.job_title}
                  onChange={handleInputChange}
                  placeholder="Job Title"
                  required
                />
              </div>
              <div className="form-group col-md-12">
                <NurtifyText label="Short Description*" />
                <textarea
                  name="description"
                  value={formData.description}
                  onChange={handleInputChange}
                />
              </div>
              <div className="form-group col-md-12">
                <NurtifyText label="File Attachment" />
                <NurtifyAttachFileBox
                  onChange={(file) => {
                    console.log("File selected:", file);
                    setFormData((prev) => ({
                      ...prev,
                      attach_content: file,
                    }));
                  }}
                />
                <p>{formData.attach_content?.name} </p>

              </div>
              <div className="form-group col-md-12">
                <NurtifyText label="Study*" />
                <NurtifySelect
                  name="study_uuid"
                  value={formData.study_uuid}
                  onChange={handleStudyChange}
                  options={[
                    { value: "", label: "Select a study" },
                    ...studyOptions
                  ]}
                />
              </div>
              <button
                type="submit"
                className="button text-white btn-nurtify mr-10 px-4 py-2"
                style={{ alignSelf: "flex-end" }}
                disabled={updatePolicyMutation.isPending}
              >
                {updatePolicyMutation.isPending ? "Submitting..." : "Submit"}
              </button>
            </form>
          )}
        </>
      </div>
      <ConfirmationModal
        isOpen={isConfirmModalOpen}
        onClose={() => setIsConfirmModalOpen(false)}
        onConfirm={handleConfirmUpdate}
        title="Confirm Updating policy"
        message="Are you sure you want to update this policy?"
        subMessage="This action is permanent and cannot be undone. All associated data will be lost."
      />
    </div>
  );
}

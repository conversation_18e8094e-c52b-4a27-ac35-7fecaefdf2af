/* Create Consent Modal Styles - Prefixed with .create-consent-modal- */

/* Modal Overlay */
.create-consent-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.6);
  backdrop-filter: blur(4px);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: 20px;
  animation: create-consent-modal-fadeIn 0.3s ease-out;
}

@keyframes create-consent-modal-fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

/* Main Modal Container */
.create-consent-modal-container {
  background: var(--color-white);
  border-radius: 16px;
  box-shadow: 0 25px 80px rgba(0, 0, 0, 0.2);
  width: 100%;
  max-width: 900px;
  max-height: 90vh;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  animation: create-consent-modal-slideUp 0.4s cubic-bezier(0.34, 1.56, 0.64, 1);
  border: 1px solid var(--color-light-2);
}

@keyframes create-consent-modal-slideUp {
  from {
    opacity: 0;
    transform: translateY(30px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

/* Modal Header */
.create-consent-modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 28px 36px;
  border-bottom: 1px solid var(--color-light-2);
  background: linear-gradient(135deg, var(--color-purple-1) 0%, var(--color-blue-1) 100%);
  color: white;
  position: relative;
}

.create-consent-modal-header::before {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
}

.create-consent-modal-header h2 {
  margin: 0;
  font-size: var(--text-24);
  font-weight: 700;
  font-family: var(--font-primary);
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.create-consent-modal-close-button {
  background: none;
  border: none;
  color: white;
  cursor: pointer;
  padding: 10px;
  border-radius: 8px;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  display: flex;
  align-items: center;
  justify-content: center;
}

.create-consent-modal-close-button:hover {
  background-color: rgba(255, 255, 255, 0.15);
  transform: scale(1.05);
}

.create-consent-modal-close-button:active {
  transform: scale(0.95);
}

/* Progress Bar */
.create-consent-modal-progress-bar {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 24px 36px;
  background: linear-gradient(135deg, var(--color-light-6) 0%, var(--color-white) 100%);
  border-bottom: 1px solid var(--color-light-2);
  position: relative;
}

.create-consent-modal-progress-step {
  width: 48px;
  height: 48px;
  border-radius: 50%;
  background-color: var(--color-light-2);
  color: var(--color-light-1);
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 700;
  font-size: var(--text-16);
  margin: 0 12px;
  transition: all 0.4s cubic-bezier(0.34, 1.56, 0.64, 1);
  position: relative;
  border: 3px solid transparent;
  font-family: var(--font-primary);
}

.create-consent-modal-progress-step.active {
  background: linear-gradient(135deg, var(--color-purple-1) 0%, var(--color-blue-1) 100%);
  color: white;
  transform: scale(1.15);
  border-color: var(--color-white);
  box-shadow: 0 8px 24px rgba(55, 183, 195, 0.4);
}

.create-consent-modal-progress-step:not(:last-child)::after {
  content: '';
  position: absolute;
  right: -24px;
  top: 50%;
  transform: translateY(-50%);
  width: 24px;
  height: 3px;
  background: linear-gradient(90deg, var(--color-light-2), var(--color-light-8));
  border-radius: 2px;
  transition: all 0.3s ease;
}

.create-consent-modal-progress-step.active::after {
  background: linear-gradient(90deg, var(--color-purple-1), var(--color-blue-1));
  box-shadow: 0 2px 8px rgba(55, 183, 195, 0.3);
}

/* Modal Content */
.create-consent-modal-content {
  flex: 1;
  overflow-y: auto;
  padding: 36px;
  background: var(--color-white);
}

.create-consent-modal-content::-webkit-scrollbar {
  width: 6px;
}

.create-consent-modal-content::-webkit-scrollbar-track {
  background: var(--color-light-3);
  border-radius: 3px;
}

.create-consent-modal-content::-webkit-scrollbar-thumb {
  background: var(--color-light-1);
  border-radius: 3px;
}

.create-consent-modal-content::-webkit-scrollbar-thumb:hover {
  background: var(--color-purple-1);
}

.create-consent-modal-step-content h3 {
  margin: 0 0 24px 0;
  font-size: var(--text-20);
  font-weight: 700;
  color: var(--color-dark-1);
  font-family: var(--font-primary);
  display: flex;
  align-items: center;
  gap: 12px;
}

.create-consent-modal-step-content h3::before {
  content: '';
  width: 4px;
  height: 24px;
  background: linear-gradient(135deg, var(--color-purple-1) 0%, var(--color-blue-1) 100%);
  border-radius: 2px;
}

.create-consent-modal-step-description {
  color: var(--color-light-1);
  margin-bottom: 28px;
  line-height: 1.6;
  font-size: var(--text-15);
  padding: 16px 20px;
  background: var(--color-light-6);
  border-radius: 10px;
  border-left: 4px solid var(--color-purple-1);
}

/* Form Groups */
.create-consent-modal-form-group {
  margin-bottom: 24px;
}

.create-consent-modal-form-group label {
  display: block;
  margin-bottom: 10px;
  font-weight: 600;
  color: var(--color-dark-1);
  font-size: var(--text-14);
  font-family: var(--font-primary);
}

.create-consent-modal-form-group input,
.create-consent-modal-form-group textarea,
.create-consent-modal-form-group select {
  width: 100%;
  padding: 14px 18px;
  border: 2px solid var(--color-light-2);
  border-radius: 10px;
  font-size: var(--text-14);
  font-family: var(--font-primary);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  background: var(--color-white);
  color: var(--color-dark-1);
}

.create-consent-modal-form-group input:focus,
.create-consent-modal-form-group textarea:focus,
.create-consent-modal-form-group select:focus {
  outline: none;
  border-color: var(--color-purple-1);
  box-shadow: 0 0 0 4px rgba(55, 183, 195, 0.1);
  transform: translateY(-1px);
}

.create-consent-modal-form-group input.error,
.create-consent-modal-form-group textarea.error,
.create-consent-modal-form-group select.error {
  border-color: var(--color-red-1);
  box-shadow: 0 0 0 4px rgba(240, 30, 0, 0.1);
}

.create-consent-modal-error-message {
  color: var(--color-red-1);
  font-size: var(--text-13);
  margin-top: 6px;
  display: flex;
  align-items: center;
  gap: 6px;
  font-weight: 500;
}

/* Add Question Form */
.create-consent-modal-add-question-form {
  background: linear-gradient(135deg, var(--color-light-6) 0%, var(--color-white) 100%);
  padding: 28px;
  border-radius: 12px;
  margin-bottom: 28px;
  border: 2px solid var(--color-light-2);
  position: relative;
  overflow: hidden;
}

.create-consent-modal-add-question-form::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, var(--color-purple-1), var(--color-blue-1));
}

/* Checkbox Styles */
.create-consent-modal-checkbox-label {
  display: flex;
  align-items: flex-start;
  gap: 14px;
  cursor: pointer;
  padding: 12px 0;
  user-select: none;
  transition: all 0.2s ease;
}

.create-consent-modal-checkbox-label:hover {
  transform: translateX(2px);
}

.create-consent-modal-standard-checkbox {
  width: 20px;
  height: 20px;
  margin: 0;
  cursor: pointer;
  accent-color: var(--color-purple-1);
  flex-shrink: 0;
  margin-top: 2px;
  border-radius: 4px;
}

.create-consent-modal-checkbox-text {
  font-size: var(--text-14);
  color: var(--color-dark-1);
  line-height: 1.5;
  margin: 0;
  font-weight: 500;
}

.create-consent-modal-checkbox-label:hover .create-consent-modal-checkbox-text {
  color: var(--color-purple-1);
}

.create-consent-modal-add-question-btn {
  background: linear-gradient(135deg, var(--color-purple-1) 0%, var(--color-blue-1) 100%);
  color: white;
  border: none;
  padding: 14px 28px;
  border-radius: 10px;
  font-weight: 600;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 10px;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  font-size: var(--text-14);
  font-family: var(--font-primary);
  box-shadow: 0 4px 16px rgba(55, 183, 195, 0.3);
}

.create-consent-modal-add-question-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 24px rgba(55, 183, 195, 0.4);
}

.create-consent-modal-add-question-btn:active {
  transform: translateY(0);
}

/* Questions List */
.create-consent-modal-questions-list {
  margin-top: 28px;
}

.create-consent-modal-questions-list h4 {
  margin: 0 0 20px 0;
  font-size: var(--text-18);
  font-weight: 700;
  color: var(--color-dark-1);
  font-family: var(--font-primary);
  display: flex;
  align-items: center;
  gap: 10px;
}

.create-consent-modal-question-item {
  display: flex;
  align-items: flex-start;
  justify-content: space-between;
  padding: 20px;
  background: var(--color-white);
  border: 2px solid var(--color-light-2);
  border-radius: 12px;
  margin-bottom: 16px;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
}

.create-consent-modal-question-item::before {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  width: 4px;
  background: linear-gradient(135deg, var(--color-purple-1) 0%, var(--color-blue-1) 100%);
  transform: scaleY(0);
  transition: transform 0.3s ease;
}

.create-consent-modal-question-item:hover {
  border-color: var(--color-purple-1);
  box-shadow: 0 8px 24px rgba(55, 183, 195, 0.15);
  transform: translateY(-2px);
}

.create-consent-modal-question-item:hover::before {
  transform: scaleY(1);
}

.create-consent-modal-question-content {
  flex: 1;
}

.create-consent-modal-question-header {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 10px;
}

.create-consent-modal-question-number {
  background: linear-gradient(135deg, var(--color-purple-1) 0%, var(--color-blue-1) 100%);
  color: white;
  padding: 6px 12px;
  border-radius: 6px;
  font-size: var(--text-13);
  font-weight: 700;
  box-shadow: 0 2px 8px rgba(55, 183, 195, 0.3);
}

.create-consent-modal-required-badge {
  background: linear-gradient(135deg, var(--color-red-1) 0%, var(--color-orange-1) 100%);
  color: white;
  padding: 4px 10px;
  border-radius: 12px;
  font-size: var(--text-11);
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.create-consent-modal-question-text {
  margin: 0;
  color: var(--color-dark-1);
  line-height: 1.6;
  font-size: var(--text-14);
}

.create-consent-modal-remove-question-btn {
  background: none;
  border: none;
  color: var(--color-red-1);
  cursor: pointer;
  padding: 10px;
  border-radius: 8px;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  margin-left: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.create-consent-modal-remove-question-btn:hover {
  background-color: var(--color-red-2);
  transform: scale(1.1);
}

.create-consent-modal-no-questions {
  text-align: center;
  color: var(--color-light-1);
  font-style: italic;
  padding: 32px;
  background: linear-gradient(135deg, var(--color-light-6) 0%, var(--color-white) 100%);
  border-radius: 12px;
  border: 2px dashed var(--color-light-2);
  font-size: var(--text-15);
}

/* Review Section */
.create-consent-modal-review-section {
  background: linear-gradient(135deg, var(--color-light-6) 0%, var(--color-white) 100%);
  padding: 28px;
  border-radius: 12px;
  margin-bottom: 28px;
  border: 2px solid var(--color-light-2);
  position: relative;
  overflow: hidden;
}

.create-consent-modal-review-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, var(--color-green-4), var(--color-green-5));
}

.create-consent-modal-review-section h4 {
  margin: 0 0 20px 0;
  font-size: var(--text-18);
  font-weight: 700;
  color: var(--color-dark-1);
  font-family: var(--font-primary);
}

.create-consent-modal-review-item {
  margin-bottom: 16px;
  padding: 12px 0;
  border-bottom: 1px solid var(--color-light-2);
  display: flex;
  align-items: flex-start;
  gap: 12px;
}

.create-consent-modal-review-item:last-child {
  border-bottom: none;
  margin-bottom: 0;
}

.create-consent-modal-review-item strong {
  color: var(--color-dark-1);
  font-weight: 600;
  min-width: 100px;
  font-size: var(--text-14);
}

.create-consent-modal-warning-section {
  display: flex;
  align-items: flex-start;
  gap: 14px;
  padding: 20px;
  background: linear-gradient(135deg, var(--color-warning-1) 0%, var(--color-yellow-2) 100%);
  border: 2px solid var(--color-warning-2);
  border-radius: 12px;
  color: var(--color-warning-2);
  position: relative;
  overflow: hidden;
}

.create-consent-modal-warning-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, var(--color-yellow-1), var(--color-orange-1));
}

.create-consent-modal-warning-section strong {
  color: var(--color-warning-2);
  font-weight: 700;
}

.create-consent-modal-error-alert {
  display: flex;
  align-items: center;
  gap: 10px;
  padding: 16px 20px;
  background: linear-gradient(135deg, var(--color-error-1) 0%, var(--color-red-2) 100%);
  border: 2px solid var(--color-error-2);
  border-radius: 10px;
  color: var(--color-error-2);
  margin-top: 20px;
  font-weight: 500;
}

/* Modal Footer */
.create-consent-modal-footer {
  padding: 28px 36px;
  border-top: 1px solid var(--color-light-2);
  background: linear-gradient(135deg, var(--color-light-6) 0%, var(--color-white) 100%);
}

.create-consent-modal-footer-buttons {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 16px;
}

/* Button Styles */
.create-consent-modal-btn {
  padding: 14px 28px;
  border-radius: 10px;
  font-weight: 600;
  cursor: pointer;
  border: none;
  display: flex;
  align-items: center;
  gap: 10px;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  font-size: var(--text-14);
  font-family: var(--font-primary);
  position: relative;
  overflow: hidden;
}

.create-consent-modal-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s;
}

.create-consent-modal-btn:hover::before {
  left: 100%;
}

.create-consent-modal-btn-primary {
  background: linear-gradient(135deg, var(--color-purple-1) 0%, var(--color-blue-1) 100%);
  color: white;
  box-shadow: 0 4px 16px rgba(55, 183, 195, 0.3);
}

.create-consent-modal-btn-primary:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 24px rgba(55, 183, 195, 0.4);
}

.create-consent-modal-btn-secondary {
  background: linear-gradient(135deg, var(--color-light-1) 0%, var(--color-dark-3) 100%);
  color: white;
  box-shadow: 0 4px 16px rgba(111, 122, 153, 0.3);
}

.create-consent-modal-btn-secondary:hover {
  background: linear-gradient(135deg, var(--color-dark-3) 0%, var(--color-dark-4) 100%);
  transform: translateY(-2px);
  box-shadow: 0 8px 24px rgba(111, 122, 153, 0.4);
}

.create-consent-modal-btn-success {
  background: linear-gradient(135deg, var(--color-green-4) 0%, var(--color-green-5) 100%);
  color: white;
  box-shadow: 0 4px 16px rgba(4, 214, 151, 0.3);
}

.create-consent-modal-btn-success:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 8px 24px rgba(4, 214, 151, 0.4);
}

.create-consent-modal-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none !important;
  box-shadow: none !important;
}

.create-consent-modal-btn:active:not(:disabled) {
  transform: translateY(0);
}

/* Loading State */
.create-consent-modal-loading {
  display: inline-flex;
  align-items: center;
  gap: 8px;
}

.create-consent-modal-spinner {
  width: 16px;
  height: 16px;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-top: 2px solid white;
  border-radius: 50%;
  animation: create-consent-modal-spin 1s linear infinite;
}

@keyframes create-consent-modal-spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Responsive Design */
@media (max-width: 1024px) {
  .create-consent-modal-container {
    max-width: 95vw;
    margin: 10px;
  }
}

@media (max-width: 768px) {
  .create-consent-modal-container {
    max-height: 95vh;
    border-radius: 12px;
  }

  .create-consent-modal-header,
  .create-consent-modal-content,
  .create-consent-modal-footer {
    padding: 20px 24px;
  }

  .create-consent-modal-progress-bar {
    padding: 20px 24px;
  }

  .create-consent-modal-progress-step {
    width: 40px;
    height: 40px;
    font-size: var(--text-14);
    margin: 0 8px;
  }

  .create-consent-modal-progress-step:not(:last-child)::after {
    width: 16px;
    right: -16px;
  }

  .create-consent-modal-footer-buttons {
    flex-direction: column;
    gap: 12px;
  }

  .create-consent-modal-btn {
    width: 100%;
    justify-content: center;
  }

  .create-consent-modal-question-item {
    flex-direction: column;
    gap: 16px;
  }

  .create-consent-modal-remove-question-btn {
    align-self: flex-end;
    margin-left: 0;
  }
}

@media (max-width: 480px) {
  .create-consent-modal-header h2 {
    font-size: var(--text-20);
  }

  .create-consent-modal-content {
    padding: 16px 20px;
  }

  .create-consent-modal-add-question-form,
  .create-consent-modal-review-section {
    padding: 20px;
  }

  .create-consent-modal-progress-step {
    width: 36px;
    height: 36px;
    font-size: var(--text-13);
    margin: 0 6px;
  }

  .create-consent-modal-btn {
    padding: 12px 20px;
    font-size: var(--text-13);
  }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .create-consent-modal-container {
    border: 3px solid var(--color-dark-1);
  }

  .create-consent-modal-form-group input:focus,
  .create-consent-modal-form-group textarea:focus,
  .create-consent-modal-form-group select:focus {
    border-width: 3px;
  }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  .create-consent-modal-container,
  .create-consent-modal-overlay,
  .create-consent-modal-progress-step,
  .create-consent-modal-btn,
  .create-consent-modal-question-item {
    animation: none;
    transition: none;
  }
}

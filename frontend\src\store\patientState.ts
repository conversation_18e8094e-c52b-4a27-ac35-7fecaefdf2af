import { create } from "zustand";
import { Allergy, PatientCreateData } from "@/services/api/types";

// Define the patient state interface
interface PatientState {
  // Patient form data
  first_name: string;
  last_name: string;
  date_of_birth: string;
  gender: string;
  nhs_number: string;
  medical_record_number: string;
  address: string;
  phone_number: string;
  email: string;
  next_of_kin_name: string;
  next_of_kin_phone: string;
  medical_history: string;
  allergies: Allergy[];
  hospital: string;
  department: string;
  location: string;
  ethnic_background: string;
  postcode: string; 
  // Form state
  isConfirmModalOpen: boolean;

  // Actions
  setField: <T extends keyof Omit<PatientState, "setField" | "resetForm" | "setConfirmModalOpen" | "getPatientData">>(field: T, value: PatientState[T]) => void;
  resetForm: () => void;
  setConfirmModalOpen: (isOpen: boolean) => void;

  // Get patient data in API format
  getPatientData: () => PatientCreateData;
}

// Define initial state
const initialState = {
  first_name: "",
  last_name: "",
  date_of_birth: "",
  gender: "Select a gender",
  nhs_number: "",
  medical_record_number: "",
  address: "",
  phone_number: "",
  email: "",
  next_of_kin_name: "",
  next_of_kin_phone: "",
  medical_history: "",
  allergies: [{ name: "" }] as Allergy[],
  hospital: "",
  department: "",
  location: "",
  ethnic_background: "",
  isConfirmModalOpen: false,
  postcode: "",
};

// Create the store
export const usePatientStore = create<PatientState>((set, get) => ({
  ...initialState,

  setField: (field, value) => set({ [field]: value }),

  resetForm: () => set(initialState),

  setConfirmModalOpen: (isOpen) => set({ isConfirmModalOpen: isOpen }),

  getPatientData: () => {
    const state = get();
    return {
      first_name: state.first_name,
      last_name: state.last_name,
      date_of_birth: state.date_of_birth,
      gender: state.gender,
      nhs_number: state.nhs_number,
      medical_record_number: state.medical_record_number,
      address: state.address,
      phone_number: state.phone_number,
      email: state.email,
      next_of_kin_name: state.next_of_kin_name,
      next_of_kin_phone: state.next_of_kin_phone,
      medical_history: state.medical_history,
      allergies: state.allergies,
      ethnic_background: state.ethnic_background,
      postcode: state.postcode,
    };
  },
}));

export default usePatientStore;

/* Page Layout */
.dashboard__content {
  padding: 30px;
  min-height: calc(100vh - 80px); /* Adjust based on your header height */
}

/* Sidebar Styles */
.sidebar.-dashboard {
  position: sticky;
  top: 20px;
  background: #ffffff;
  border-radius: 16px;
  box-shadow: 0 8px 24px rgba(55, 183, 195, 0.08),
              0 2px 4px rgba(55, 183, 195, 0.04);
  transition: all 0.3s ease;
}

.sidebar__item {
  padding: 24px;
}

/* Sidebar Header */
.sidebar__header {
  margin-bottom: 24px;
}

.sidebar__title {
  font-size: 20px;
  font-weight: 600;
  color: #1a1a1a;
  margin-bottom: 16px;
}

.sidebar__subtitle {
  font-size: 16px;
  font-weight: 500;
  color: #333;
  margin: 24px 0 16px 0;
}

/* Search Field */
.sidebar__search {
  margin-bottom: 20px;
}

.search-field {
  position: relative;
  margin-top: 16px;
}

.search-input {
  width: 100%;
  padding: 14px 18px;
  padding-left: 44px;
  border: 1px solid rgba(55, 183, 195, 0.15);
  border-radius: 12px;
  font-size: 14px;
  transition: all 0.2s ease;
  background-color: rgba(55, 183, 195, 0.03);
}

.search-input:focus {
  border-color: #37B7C3;
  box-shadow: 0 0 0 4px rgba(55, 183, 195, 0.1);
  background-color: #fff;
}

.search-icon {
  position: absolute;
  left: 12px;
  top: 50%;
  transform: translateY(-50%);
  color: #9ca3af;
  font-size: 18px;
}

/* Checkbox Container */
.sidebar-checkbox {
  max-height: calc(100vh - 250px);
  overflow-y: auto;
  padding: 4px;
}

/* Scrollbar Styling */
.sidebar-checkbox::-webkit-scrollbar {
  width: 6px;
}

.sidebar-checkbox::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.sidebar-checkbox::-webkit-scrollbar-thumb {
  background: #37B7C3;
  border-radius: 3px;
}

.sidebar-checkbox::-webkit-scrollbar-thumb:hover {
  background: #2d919a;
}

/* Checkbox Styling */
.form-checkbox {
  position: relative;
  display: flex;
  align-items: center;
  padding: 10px 14px;
  margin-bottom: 8px;
  border-radius: 12px;
  transition: all 0.2s ease;
  background-color: #fff;
  border: 1px solid rgba(55, 183, 195, 0.08);
}

.form-checkbox:hover {
  background-color: rgba(55, 183, 195, 0.04);
  border-color: rgba(55, 183, 195, 0.2);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(55, 183, 195, 0.08);
}

.form-checkbox__input {
  position: absolute;
  opacity: 0;
  cursor: pointer;
  height: 0;
  width: 0;
}

.form-checkbox__label {
  position: relative;
  padding-left: 32px;
  cursor: pointer;
  font-size: 15px;
  line-height: 1.5;
  color: #333;
  user-select: none;
  width: 100%;
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin: 0;
  font-weight: 400;
}

.form-checkbox__label:before {
  content: '';
  position: absolute;
  left: 0;
  top: 55%;
  transform: translateY(-50%);
  width: 20px;
  height: 20px;
  border: 2px solid rgba(55, 183, 195, 0.4);
  border-radius: 6px;
  background-color: #fff;
  transition: all 0.2s ease;
  box-shadow: inset 0 1px 3px rgba(55, 183, 195, 0.1);
}

.form-checkbox__input:checked + .form-checkbox__label:before {
  background-color: #37B7C3;
  border-color: #37B7C3;
  box-shadow: 0 2px 4px rgba(55, 183, 195, 0.2);
}

.form-checkbox__input:checked + .form-checkbox__label:after {
  content: '';
  position: absolute;
  left: 7px;
  top: 50%;
  transform: translateY(-50%) rotate(45deg);
  width: 6px;
  height: 12px;
  border: solid white;
  border-width: 0 2px 2px 0;
}

.form-checkbox__count {
  font-size: 13px;
  color: #37B7C3;
  background: rgba(55, 183, 195, 0.08);
  padding: 4px 10px;
  border-radius: 20px;
  font-weight: 500;
  transition: all 0.2s ease;
}

.form-checkbox:hover .form-checkbox__count {
  background: rgba(55, 183, 195, 0.12);
}

/* Tags Styling */
.tags-wrapper {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.imageEtiquette {
  border-radius: 50%;
  height: 50px;
  width: 50px;
  border: #37B7C3 solid 1px;
  margin-right: 10px;
  margin-bottom:  15px;
}


.tag-item {
  display: inline-block;
  padding: 8px 14px;
  background: rgba(55, 183, 195, 0.08);
  color: #37B7C3;
  border-radius: 10px;
  font-size: 13px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  border: 1px solid rgba(55, 183, 195, 0.12);
}

.tag-item:hover {
  background: rgba(55, 183, 195, 0.12);
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(55, 183, 195, 0.12);
}

/* Form Cards Grid */
.forms-grid {
  display: grid;
  gap: 30px;
  grid-template-columns: repeat(4, 1fr);
  margin-bottom: 30px;
}

/* Responsive Adjustments */
@media (max-width: 1400px) {
  .forms-grid {
    grid-template-columns: repeat(3, 1fr);
  }
}

@media (max-width: 991px) {
  .forms-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 20px;
  }
}

@media (max-width: 767px) {
  .forms-grid {
    grid-template-columns: 1fr;
    gap: 15px;
  }
}

/* Form Card Animation */
.forms-grid > * {
  animation: fadeIn 0.3s ease-out forwards;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Transitions and Hover Effects */
.hover-shadow-2 {
  transition: all 0.3s ease;
}

.hover-shadow-2:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 20px rgba(0, 0, 0, 0.08) !important;
}

/* Utility Classes */
.shadow-1 {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
}

.border-light {
  border: 1px solid rgba(0, 0, 0, 0.08);
}

.bg-light-4 {
  background-color: #f5f7fa;
}

/* Loading States */
.form-checkbox__input:disabled + .form-checkbox__label {
  opacity: 0.5;
  cursor: not-allowed;
}

.tabs-wrapper {
  display: flex;
  gap: 2rem;
  border-bottom: 1px solid #e5e7eb;
  padding-bottom: 0;
}

.tab-button {
  background: none;
  border: none;
  padding: 0.75rem 0.5rem;
  font-size: 1rem;
  color: #6b7280;
  position: relative;
  cursor: pointer;
  transition: color 0.3s ease;
}

.tab-button:hover {
  color: #374151;
}

.tab-button.active {
  color: #37B7C3;
}

.tab-button.active::after {
  content: '';
  position: absolute;
  bottom: -1px;
  left: 0;
  width: 100%;
  height: 2px;
  background-color: #37B7C3;
}

.options-modal {
  display: flex;
  width: 100%;  
  height: 100vh;
  position: fixed;
  top: 0;
  left: 0;
  background-color: rgba(0, 0, 0, 0.5);
  backdrop-filter: blur(5px);
  align-items: center;
  justify-content: center;
  z-index: 100;
}

.options-modal .center {
  padding: 2rem;
  display: flex;
  flex-direction: column;
  background-color: white;
  box-shadow: 0px 0px 10px rgba(0, 0, 0, 0.3);
  border-radius: 8px;
}

.headinqQuestion{
  font-size: 1.2rem;
  font-weight: 600;
  background-color: #37B7C3;
  color: white;
  padding: 0.5rem 1rem;
  border-radius: 0.5rem;
  margin-bottom: 0.5rem;
  width: 100%;
}

.subheading{
  font-size: 1rem;
}

import React, { useState, useRef, useEffect } from "react";
import "./NurtifyFilter.css";
import { ChevronDown, ChevronUp } from "lucide-react";

export type NurtifyFilterOption = {
  label: string;
  value: string;
};

export type NurtifyFilterType = "checkbox" | "radio";

export type NurtifyFilterItem = {
  label: string;
  type: NurtifyFilterType;
  options: NurtifyFilterOption[];
  value: string[] | string;
  onChange: (value: string[] | string) => void;
};

export type NurtifyFilterProps = {
  layout: "sidebar" | "vertical" | "horizontal";
  filters: NurtifyFilterItem[];
  minimizable?: boolean;
  minimized?: boolean;
  onMinimize?: () => void;
  className?: string;
};

const NurtifyFilter: React.FC<NurtifyFilterProps> = ({
  layout,
  filters,
  minimizable = false,
  minimized = false,
  onMinimize,
  className = "",
}) => {
  const [openDropdowns, setOpenDropdowns] = useState<boolean[]>(
    filters.map(() => false)
  );

  // Add search state for each filter dropdown
  const [searchTerms, setSearchTerms] = useState<string[]>(
    filters.map(() => "")
  );

  // Refs for each dropdown
  const dropdownRefs = useRef<(HTMLDivElement | null)[]>([]);

  // Effect to handle click outside for all open dropdowns
  useEffect(() => {
    if (openDropdowns.some(Boolean)) {
      const handleClickOutside = (event: MouseEvent) => {
        // If any dropdown is open, check if click is outside all open dropdowns
        let clickedInsideAny = false;
        dropdownRefs.current.forEach((ref, idx) => {
          if (openDropdowns[idx] && ref && ref.contains(event.target as Node)) {
            clickedInsideAny = true;
          }
        });
        if (!clickedInsideAny) {
          setOpenDropdowns(filters.map(() => false));
        }
      };
      document.addEventListener("mousedown", handleClickOutside);
      return () => {
        document.removeEventListener("mousedown", handleClickOutside);
      };
    }
  }, [openDropdowns, filters.length]);

  // Reset search term when dropdown closes
  useEffect(() => {
    setSearchTerms((prev) =>
      prev.map((term, idx) => (openDropdowns[idx] ? term : ""))
    );
  }, [openDropdowns]);

  const handleDropdownToggle = (idx: number) => {
    setOpenDropdowns((prev) =>
      prev.map((open, i) => (i === idx ? !open : open))
    );
  };

  const handleCheckboxChange = (
    idx: number,
    optionValue: string,
    checked: boolean
  ) => {
    const filter = filters[idx];
    if (filter.type !== "checkbox") return;
    const current = Array.isArray(filter.value) ? filter.value : [];
    const newValue = checked
      ? [...current, optionValue]
      : current.filter((v) => v !== optionValue);
    filter.onChange(newValue);
  };

  const handleRadioChange = (idx: number, optionValue: string) => {
    const filter = filters[idx];
    if (filter.type !== "radio") return;
    filter.onChange(optionValue);
  };

  const handleSearchChange = (idx: number, value: string) => {
    setSearchTerms((prev) =>
      prev.map((term, i) => (i === idx ? value : term))
    );
  };

  const containerClass = [
    "nurtify-filter",
    `nurtify-filter--${layout}`,
    minimized ? "nurtify-filter--minimized" : "",
    className,
  ]
    .filter(Boolean)
    .join(" ");

  return (
    <div className={containerClass}>
      {minimizable && layout === "sidebar" && (
        <div className="nurtify-filter__minimize">
          {!minimized ? (
            <button type="button" onClick={onMinimize} aria-label="Minimize sidebar">
              {"<< Minimize"}
            </button>
          ) : (
            <button type="button" onClick={onMinimize} aria-label="Expand sidebar">
              {">> Expand"}
            </button>
          )}
        </div>
      )}
      {!minimized && (
        <div
          className="nurtify-filter__filters"
          style={{
            display: "flex",
            flexDirection: layout === "horizontal" ? "row" : "column",
            gap: "10px"
          }}
        >
          {filters.map((filter, idx) => {
            // Filter options by search term
            const searchTerm = searchTerms[idx] || "";
            const filteredOptions = filter.options.filter(option =>
              option.label.toLowerCase().includes(searchTerm.toLowerCase())
            );
            return (
              <div
                className="nurtify-filter__dropdown"
                key={filter.label + idx}
                style={{ marginBottom: "0" }} // Remove any default margin if present
                ref={el => (dropdownRefs.current[idx] = el)}
              >
                <div
                  className="nurtify-filter__dropdown-header"
                  onClick={() => handleDropdownToggle(idx)}
                  tabIndex={0}
                  role="button"
                  aria-expanded={openDropdowns[idx]}
                >
                  <span>{filter.label}</span>
                  <span className="nurtify-filter__dropdown-icon">
                    {openDropdowns[idx] ? <ChevronUp /> : <ChevronDown />}
                  </span>
                </div>
                {openDropdowns[idx] && (
                  <div className="nurtify-filter__dropdown-content">
                    {/* Add search input for both checkbox and radio */}
                    <input
                      type="text"
                      className="nurtify-filter__search"
                      placeholder="Search..."
                      value={searchTerms[idx] || ""}
                      onChange={e => handleSearchChange(idx, e.target.value)}
                      style={{
                        marginBottom: "8px",
                        padding: "6px 10px",
                        borderRadius: "6px",
                        border: "1px solid #D2DFE1",
                        fontSize: "14px",
                        width: "100%",
                        outline: "none"
                      }}
                    />
                    {filter.type === "checkbox" &&
                      filteredOptions.map((option) => (
                        <label
                          className="nurtify-filter__option"
                          key={option.value}
                        >
                          <input
                            type="checkbox"
                            checked={
                              Array.isArray(filter.value)
                                ? filter.value.includes(option.value)
                                : false
                            }
                            onChange={(e) =>
                              handleCheckboxChange(
                                idx,
                                option.value,
                                e.target.checked
                              )
                            }
                          />
                          {option.label}
                        </label>
                      ))}
                    {filter.type === "radio" &&
                      filteredOptions.map((option) => (
                        <label
                          className="nurtify-filter__option"
                          key={option.value}
                        >
                          <input
                            type="radio"
                            name={`nurtify-filter-radio-${idx}`}
                            checked={filter.value === option.value}
                            onChange={() =>
                              handleRadioChange(idx, option.value)
                            }
                          />
                          {option.label}
                        </label>
                      ))}
                    {filteredOptions.length === 0 && (
                      <div style={{ color: "#888", fontSize: "13px", padding: "4px 0" }}>
                        No options found
                      </div>
                    )}
                  </div>
                )}
              </div>
            );
          })}
        </div>
      )}
    </div>
  );
};

export default NurtifyFilter;

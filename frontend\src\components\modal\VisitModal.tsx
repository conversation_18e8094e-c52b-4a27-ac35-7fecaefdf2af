import React, { useState, useEffect } from "react";
import { X, Save, Calendar, Clock, AlertCircle, Activity, Edit } from "lucide-react";
import { Visit, Test, VisitTemplate, initialVisitTemplate } from "@/store/scheduleEventState";
import NurtifyCheckBox from "../NurtifyCheckBox";
import NurtifyInput from "../NurtifyInput";
import NurtifyTextArea from "../NurtifyTextArea";
import { useCurrentUserQuery } from "@/hooks/user.query";
import RequiredFieldIcon from "../RequiredFieldIcon";
import "./VisitModal.css";

interface VisitModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSave: (visitData: Partial<Visit>) => void;
  initialData?: Partial<VisitTemplate>;
  availableTests: Test[];
  isEditing?: boolean;
}

const VisitModal: React.FC<VisitModalProps> = ({
  isOpen,
  onClose,
  onSave,
  initialData = {},
  availableTests = [],
  isEditing = false,
}) => {
  // Get current user to use their ID for the patient field
  const { data: currentUser } = useCurrentUserQuery();
  const [visitData, setVisitData] = useState<Partial<VisitTemplate>>(initialVisitTemplate);
  const [selectedTests, setSelectedTests] = useState<string[]>([]);
  const [, setReminderDaysInput] = useState<string>("");

  // Initialize form data when modal opens or initialData changes
  useEffect(() => {
    if (isOpen) {
      // Set patient ID to current user's ID if not already set
      const updatedData = {
        ...initialData,
      };
      
      setVisitData(updatedData);

      // Set selected tests based on initialData
      if (initialData.activities && initialData.activities.length > 0) {
        // Map activity names to their corresponding test IDs
        const selectedTestIds = availableTests
          .filter(test => initialData.activities?.includes(test.name))
          .map(test => test.id);
        setSelectedTests(selectedTestIds);
      } else {
        setSelectedTests([]);
      }

      // Set reminder days input
      if (initialData.reminder_email && initialData.reminder_email.length > 0) {
        setReminderDaysInput(initialData.reminder_email);
      } else {
        setReminderDaysInput("");
      }
    }
  }, [isOpen, initialData, currentUser, availableTests]);

  if (!isOpen) return null;

  const handleInputChange = (
    e: React.ChangeEvent<
      HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement
    >
  ) => {
    const { name, value } = e.target;

    // Handle numeric inputs
    if (
      name === "day" ||
      name === "allowed_earlier_days" ||
      name === "allowed_later_days" ||
      name === "visit_number" ||
      name === "study"
    ) {
      setVisitData({
        ...visitData,
        [name]: value === "" ? undefined : parseInt(value, 10),
      });
    } else {
      setVisitData({
        ...visitData,
        [name]: value,
      });
    }
  };

  const handleTestSelection = (testId: string) => {
    if (selectedTests.includes(testId)) {
      setSelectedTests(selectedTests.filter((id) => id !== testId));
    } else {
      setSelectedTests([...selectedTests, testId]);
    }
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();

    // Get the selected test objects
    const selectedTestObjects = availableTests.filter((test) =>
      selectedTests.includes(test.id)
    );

    // Prepare the visit data with selected tests and ensure patient ID is set
    const updatedVisitData = {
      ...visitData,
      activities: selectedTestObjects.map(test => test.name),
    };

    onSave(updatedVisitData);
    onClose();
  };

  return (
    <div className="visit-modal-overlay">
      <div className="visit-modal">
        <div className="visit-modal-header">
          <h2 className="visit-modal-title">
            {isEditing ? (
              <>
                <Edit size={20} className="modal-icon" /> Edit Visit
              </>
            ) : (
              <>
                <Calendar size={20} className="modal-icon" /> Add New Visit
              </>
            )}
          </h2>
          <button
            type="button"
            className="visit-modal-close"
            onClick={onClose}
            aria-label="Close"
          >
            <X size={20} />
          </button>
        </div>

        <form onSubmit={handleSubmit}>
          <div className="visit-modal-body">
            <div className="form-section">
              <h3 className="form-section-title">
                <Calendar size={18} /> Visit Details
              </h3>
              
              <div className="form-row">
                <div className="form-group">
                  <label htmlFor="name">Visit Name <RequiredFieldIcon /></label>
                  <NurtifyInput
                    type="text"
                    id="name"
                    name="name"
                    value={visitData.name || ""}
                    onChange={handleInputChange}
                    required
                    placeholder="e.g., Initial Assessment, Follow-up"
                  />
                </div>
              </div>

              <div className="form-row">
                <div className="form-group">
                  <label htmlFor="day">Day Count <RequiredFieldIcon /></label>
                  <NurtifyInput
                    type="number"
                    id="day"
                    name="day"
                    value={
                      visitData.day !== undefined ? visitData.day : ""
                    }
                    onChange={handleInputChange}
                    required
                    min="0"
                    placeholder="e.g., 0, 7, 14"
                  />
                  <small>Day number from the start date</small>
                </div>

                <div className="form-group">
                  <label htmlFor="visit_number">Visit Number <RequiredFieldIcon /></label>
                  <NurtifyInput
                    type="number"
                    id="visit_number"
                    name="visit_number"
                    value={
                      visitData.visit_number !== undefined ? visitData.visit_number : ""
                    }
                    onChange={handleInputChange}
                    min="1"
                    placeholder="e.g., 1, 2, 3"
                  />
                  <small>Sequential visit number</small>
                </div>
              </div>
            </div>
            
            {/* Patient ID is now hidden and automatically set to current user's ID */}
            <input
              type="hidden"
              name="patient"
              value={visitData.patient !== undefined ? visitData.patient : (currentUser?.id || 0)}
            />

            <div className="form-section">
              <h3 className="form-section-title">
                <Clock size={18} /> Visit Window
              </h3>
              
              <div className="form-row">
                <div className="form-group">
                  <label htmlFor="allowed_earlier_days">No Earlier Than (days)</label>
                  <NurtifyInput
                    type="number"
                    id="allowed_earlier_days"
                    name="allowed_earlier_days"
                    value={
                      visitData.allowed_earlier_days !== undefined
                        ? visitData.allowed_earlier_days
                        : ""
                    }
                    onChange={handleInputChange}
                    min="0"
                    placeholder="e.g., 1"
                  />
                  <small>Minimum days before scheduled date</small>
                </div>

                <div className="form-group">
                  <label htmlFor="allowed_later_days">No Later Than (days)</label>
                  <NurtifyInput
                    type="number"
                    id="allowed_later_days"
                    name="allowed_later_days"
                    value={
                      visitData.allowed_later_days !== undefined
                        ? visitData.allowed_later_days
                        : ""
                    }
                    onChange={handleInputChange}
                    min="0"
                    placeholder="e.g., 3"
                  />
                  <small>Maximum days after scheduled date</small>
                </div>
              </div>
            </div>

            <div className="form-section">
              <h3 className="form-section-title">
                <AlertCircle size={18} /> Additional Information
              </h3>
              
              <div className="form-group">
                <label htmlFor="comments">Comments</label>
                <NurtifyTextArea
                  id="comments"
                  name="comments"
                  value={visitData.comments || ""}
                  onChange={handleInputChange}
                  rows={3}
                  placeholder="Add any notes or instructions for this visit"
                />
              </div>
            </div>

            <div className="form-section">
              <h3 className="form-section-title">
                <Activity size={18} /> Visit Activities
              </h3>
              
              <div className="tests-selection">
                {availableTests.map((test) => (
                  <div key={test.id} className="test-checkbox">
                    <NurtifyCheckBox
                      key={test.id}
                      label={test.name}
                      value={test.id}
                      checked={selectedTests.includes(test.id)}
                      onChange={() => handleTestSelection(test.id)}
                      description={test.description}
                    />
                  </div>
                ))}
              </div>
            </div>
          </div>

          <div className="visit-modal-footer">
            <button type="button" className="cancel-btn" onClick={onClose}>
              <X size={16} /> Cancel
            </button>
            <button type="submit" className="create-study-btn">
              <Save size={16} /> {isEditing ? "Update Visit" : "Add Visit"}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default VisitModal;

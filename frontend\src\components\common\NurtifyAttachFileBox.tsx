import { useState } from 'react';

interface NurtifyAttachFileBoxProps {
    onChange: (file: File | null) => void;
}

export default function NurtifyAttachFileBox({ onChange }: NurtifyAttachFileBoxProps) {
    const [hasFile, setHasFile] = useState(false);

    const handleFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
        const file = event.target.files?.[0] || null;
        console.log("File in NurtifyAttachFileBox:", file);
        setHasFile(!!file);
        onChange(file);
    };

    return (
        <div style={{
            margin: '15px 0',
            position: 'relative',
            width: '100%'
        }}>
            <label
                htmlFor="file-upload"
                style={{
                    display: 'flex',
                    alignItems: 'center',
                    gap: '10px',
                    padding: '12px 20px',
                    backgroundColor: hasFile ? '#DFF3F5' : '#DFF3F5',
                    border: `1px solid ${hasFile ? '#37B7C3' : '#ddd'}`,
                    borderRadius: '8px',
                    cursor: 'pointer',
                    transition: 'all 0.3s ease',
                    fontSize: '14px',
                    color: hasFile ? '#4F547B' : '#4F547B',
                    width: '100%'
                }}
            >
                <svg
                    width="30"
                    height="30"
                    viewBox="0 0 24 24"
                    fill="none"
                    xmlns="http://www.w3.org/2000/svg"
                >
                    <path
                        d="M14 2H6C4.9 2 4 2.9 4 4V20C4 21.1 4.89 22 5.99 22H18C19.1 22 20 21.1 20 20V8L14 2Z"
                        stroke="black"
                        strokeWidth="1.5"
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        fill="none"
                        opacity="0.45"
                    />
                    <path
                        d="M14 2V8H20"
                        stroke="black"
                        strokeWidth="1.5"
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        fill="none"
                        opacity="0.45"
                    />
                    <path
                        d="M16 13H8"
                        stroke="black"
                        strokeWidth="1.5"
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        opacity="0.45"
                    />
                    <path
                        d="M16 17H8"
                        stroke="black"
                        strokeWidth="1.5"
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        opacity="0.45"
                    />
                    <path
                        d="M10 9H9H8"
                        stroke="black"
                        strokeWidth="1.5"
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        opacity="0.45"
                    />
                </svg>
                {hasFile ? 'File selected' : 'Click to attach file (.pdf, .doc, .docx, .png, .jpg, .jpeg)'}
            </label>
            <input
                type="file"
                id="file-upload"
                onChange={handleFileChange}
                accept=".pdf,.doc,.docx, .png, .jpg, .jpeg"
                style={{
                    position: 'absolute',
                    width: '1px',
                    height: '1px',
                    padding: '0',
                    margin: '-1px',
                    overflow: 'hidden',
                    clip: 'rect(0,0,0,0)',
                    border: '0'
                }}
            />
        </div>
    );
}

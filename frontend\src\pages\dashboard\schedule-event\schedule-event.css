 /* Variables */
:root {
  --primary-color: #37B7C3;
  --primary-light: #d7f1f3;
  --primary-hover: #2a8a93;
  --primary-transparent: #37B7C359;
  --text-dark: #140342;
  --text-medium: #6B7280;
  --text-light: #9CA3AF;
  --border-color: #E5E7EB;
  --background-light: #f5f7fa;
  --danger-color: #ef4444;
  --danger-light: #fee2e2;
  --success-color: #10b981;
  --success-light: #d1fae5;
  --warning-color: #f59e0b;
  --warning-light: #fef3c7;
  --card-shadow: none; /* Changed */
  --card-shadow-hover: none; /* Changed */
  --border-radius: 0; /* Changed */
  --transition-default: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* PI Assignment Styles */
.pi-assignment-container {
  padding: 15px 24px 24px 24px;
  animation: fadeIn 0.3s ease-in-out;
}

.pi-assignment-header {
  margin-bottom: 32px;
  padding: 24px;
  background: linear-gradient(135deg, var(--primary-light) 0%, #ffffff 100%);
  border-radius: var(--border-radius);
  border: 1px solid var(--border-color);
}

.pi-header-content h1 {
  color: var(--text-dark);
  font-size: 28px;
  font-weight: 700;
  margin-bottom: 12px;
  display: flex;
  align-items: center;
}

.pi-header-content p {
  color: var(--text-medium);
  font-size: 16px;
  margin: 0;
  line-height: 1.6;
}

.pi-assignment-content {
  display: flex;
  flex-direction: column;
  gap: 32px;
}

/* PI Summary Cards */
.pi-summary-cards {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 24px;
  margin-bottom: 32px;
}

.pi-summary-card {
  background: #FFFFFF;
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
  padding: 24px;
  display: flex;
  align-items: center;
  gap: 20px;
  transition: var(--transition-default);
  position: relative;
  overflow: hidden;
}

.pi-summary-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: var(--primary-color);
}

.pi-summary-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(55, 183, 195, 0.15);
}

.pi-card-icon {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.pi-card-icon.pi-pending {
  background: var(--warning-light);
  color: var(--warning-color);
}

.pi-card-icon.pi-available {
  background: var(--success-light);
  color: var(--success-color);
}

.pi-card-content h3 {
  font-size: 32px;
  font-weight: 700;
  color: var(--text-dark);
  margin: 0 0 8px 0;
  line-height: 1;
}

.pi-card-content p {
  color: var(--text-medium);
  font-size: 14px;
  font-weight: 500;
  margin: 0;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

/* PI Studies Section */
.pi-studies-section {
  background: #FFFFFF;
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
  padding: 24px;
}

.pi-section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
  padding-bottom: 16px;
  border-bottom: 2px solid var(--primary-light);
}

.pi-section-header h2 {
  color: var(--text-dark);
  font-size: 22px;
  font-weight: 600;
  margin: 0;
}

.pi-search-container {
  position: relative;
  min-width: 300px;
}

.pi-search-icon {
  position: absolute;
  left: 12px;
  top: 50%;
  transform: translateY(-50%);
  color: var(--text-light);
  z-index: 1;
}

.pi-search-input {
  width: 100%;
  padding: 12px 12px 12px 40px;
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
  font-size: 14px;
  transition: var(--transition-default);
  background: #FFFFFF;
}

.pi-search-input:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px var(--primary-transparent);
}

/* PI Studies Grid */
.pi-studies-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(400px, 1fr));
  gap: 20px;
}

.pi-study-card {
  background: #FFFFFF;
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
  padding: 20px;
  transition: var(--transition-default);
  position: relative;
}

.pi-study-card:hover {
  border-color: var(--primary-color);
  box-shadow: 0 4px 20px rgba(55, 183, 195, 0.1);
}

.pi-study-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 16px;
}

.pi-study-header h3 {
  color: var(--text-dark);
  font-size: 18px;
  font-weight: 600;
  margin: 0;
  line-height: 1.4;
  flex: 1;
  margin-right: 16px;
}

.pi-study-status {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 6px 12px;
  border-radius: 20px;
  font-size: 12px;
  font-weight: 500;
  white-space: nowrap;
  flex-shrink: 0;
}

.pi-study-status.pi-pending {
  background: var(--warning-light);
  color: var(--warning-color);
}

.pi-study-details {
  margin-bottom: 20px;
}

.pi-study-description {
  color: var(--text-medium);
  font-size: 14px;
  line-height: 1.6;
  margin: 0 0 16px 0;
}

.pi-study-meta {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.pi-sponsor,
.pi-created-date {
  color: var(--text-light);
  font-size: 13px;
  display: flex;
  align-items: center;
  gap: 6px;
}

.pi-study-actions {
  display: flex;
  justify-content: flex-end;
}

.pi-assign-btn {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 10px 20px;
  background: var(--primary-color);
  color: #FFFFFF;
  border: none;
  border-radius: var(--border-radius);
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: var(--transition-default);
}

.pi-assign-btn:hover {
  background: var(--primary-hover);
  transform: translateY(-1px);
}

/* PI No Studies State */
.pi-no-studies {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
  text-align: center;
  background: var(--background-light);
  border-radius: var(--border-radius);
  border: 2px dashed var(--border-color);
}

.pi-placeholder-icon {
  color: var(--text-light);
  margin-bottom: 20px;
  opacity: 0.6;
}

.pi-no-studies h3 {
  color: var(--text-dark);
  font-size: 20px;
  font-weight: 600;
  margin: 0 0 12px 0;
}

.pi-no-studies p {
  color: var(--text-medium);
  font-size: 16px;
  margin: 0;
  max-width: 400px;
  line-height: 1.6;
}

/* PI Loading States */
.pi-loading-message {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 40px 20px;
  color: var(--text-medium);
  font-size: 16px;
  background: var(--background-light);
  border-radius: var(--border-radius);
}

/* PI Modal Styles */
.pi-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.6);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
  animation: fadeIn 0.2s ease-out;
}

.pi-assignment-modal {
  background: #FFFFFF;
  border-radius: var(--border-radius);
  width: 90%;
  max-width: 600px;
  max-height: 90vh;
  overflow-y: auto;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
  animation: slideUp 0.3s ease-out;
}

.pi-modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24px;
  border-bottom: 1px solid var(--border-color);
  background: var(--primary-light);
}

.pi-modal-header h2 {
  color: var(--text-dark);
  font-size: 20px;
  font-weight: 600;
  margin: 0;
}

.pi-close-btn {
  background: none;
  border: none;
  cursor: pointer;
  color: var(--text-medium);
  padding: 8px;
  border-radius: 4px;
  transition: var(--transition-default);
}

.pi-close-btn:hover {
  background: rgba(0, 0, 0, 0.1);
  color: var(--text-dark);
}

.pi-modal-content {
  padding: 24px;
}

.pi-study-info {
  margin-bottom: 24px;
  padding: 16px;
  background: var(--background-light);
  border-radius: var(--border-radius);
  border-left: 4px solid var(--primary-color);
}

.pi-study-info h3 {
  color: var(--text-dark);
  font-size: 16px;
  font-weight: 600;
  margin: 0 0 8px 0;
}

.pi-study-info p {
  color: var(--text-medium);
  font-size: 14px;
  margin: 0;
  line-height: 1.5;
}

.pi-investigator-selection h4 {
  color: var(--text-dark);
  font-size: 16px;
  font-weight: 600;
  margin: 0 0 16px 0;
}

.pi-investigators-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
  max-height: 300px;
  overflow-y: auto;
}

.pi-investigator-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px;
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
  cursor: pointer;
  transition: var(--transition-default);
  background: #FFFFFF;
}

.pi-investigator-item:hover {
  border-color: var(--primary-color);
  background: var(--primary-light);
}

.pi-investigator-item.pi-selected {
  border-color: var(--primary-color);
  background: var(--primary-light);
  box-shadow: 0 0 0 2px var(--primary-transparent);
}

.pi-investigator-info h5 {
  color: var(--text-dark);
  font-size: 16px;
  font-weight: 600;
  margin: 0 0 4px 0;
}

.pi-speciality,
.pi-email {
  color: var(--text-medium);
  font-size: 14px;
  margin: 0 0 2px 0;
}

.pi-selected-icon {
  color: var(--primary-color);
  flex-shrink: 0;
}

.pi-no-investigators {
  padding: 20px;
  text-align: center;
  color: var(--text-medium);
  background: var(--background-light);
  border-radius: var(--border-radius);
}

.pi-modal-actions {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  padding: 24px;
  border-top: 1px solid var(--border-color);
  background: var(--background-light);
}

.pi-cancel-btn {
  padding: 10px 20px;
  background: var(--text-light);
  color: #FFFFFF;
  border: none;
  border-radius: var(--border-radius);
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: var(--transition-default);
}

.pi-cancel-btn:hover {
  background: var(--text-medium);
}

.pi-confirm-btn {
  padding: 10px 20px;
  background: var(--primary-color);
  color: #FFFFFF;
  border: none;
  border-radius: var(--border-radius);
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: var(--transition-default);
}

.pi-confirm-btn:hover:not(:disabled) {
  background: var(--primary-hover);
}

.pi-confirm-btn:disabled {
  background: var(--text-light);
  cursor: not-allowed;
}

/* PI Tracker Styles */
.pi-tracker-container {
  padding: 15px 24px 24px 24px;
  animation: fadeIn 0.3s ease-in-out;
}

.pi-tracker-header {
  margin-bottom: 32px;
  padding: 24px;
  background: linear-gradient(135deg, var(--success-light) 0%, #ffffff 100%);
  border-radius: var(--border-radius);
  border: 1px solid var(--border-color);
}

.pi-tracker-header h1 {
  color: var(--text-dark);
  font-size: 28px;
  font-weight: 700;
  margin-bottom: 12px;
  display: flex;
  align-items: center;
}

.pi-tracker-header p {
  color: var(--text-medium);
  font-size: 16px;
  margin: 0;
  line-height: 1.6;
}

/* Enhanced Tab Styling */
.tabs-wrapper {
  display: flex;
  gap: 0;
  background: #FFFFFF;
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
  overflow: hidden;
  margin-bottom: 24px;
}

.tab-button {
  flex: 1;
  padding: 16px 24px;
  background: #FFFFFF;
  border: none;
  border-right: 1px solid var(--border-color);
  color: var(--text-medium);
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: var(--transition-default);
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  position: relative;
}

.tab-button:last-child {
  border-right: none;
}

.tab-button:hover {
  background: var(--primary-light);
  color: var(--primary-color);
}

.tab-button.active {
  background: var(--primary-color);
  color: #FFFFFF;
  font-weight: 600;
}

.tab-button.active::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: var(--primary-hover);
}

.tabs__content {
  background: #FFFFFF;
  border: 1px solid var(--border-color);
  border-top: none;
  border-radius: 0 0 var(--border-radius) var(--border-radius);
  min-height: 400px;
}

/* Responsive Design for PI Components */
@media (max-width: 768px) {
  .tabs-wrapper {
    flex-direction: column;
  }
  
  .tab-button {
    border-right: none;
    border-bottom: 1px solid var(--border-color);
  }
  
  .tab-button:last-child {
    border-bottom: none;
  }
  
  .pi-summary-cards {
    grid-template-columns: 1fr;
  }
  
  .pi-section-header {
    flex-direction: column;
    gap: 16px;
    align-items: stretch;
  }
  
  .pi-search-container {
    min-width: auto;
  }
  
  .pi-studies-grid {
    grid-template-columns: 1fr;
  }
  
  .pi-study-header {
    flex-direction: column;
    gap: 12px;
  }
  
  .pi-modal-actions {
    flex-direction: column;
  }
  
  .pi-assignment-modal {
    width: 95%;
    margin: 20px;
  }
}

@media (max-width: 576px) {
  .pi-assignment-container,
  .pi-tracker-container {
    padding: 16px;
  }
  
  .pi-assignment-header,
  .pi-tracker-header {
    padding: 20px;
  }
  
  .pi-summary-card {
    padding: 20px;
  }
  
  .pi-studies-section {
    padding: 20px;
  }
  
  .tab-button {
    padding: 12px 16px;
    font-size: 13px;
  }
}

/* Main Container */
.schedule-event-container {
  padding: 15px 24px 24px 24px; /* Changed top padding */
  animation: fadeIn 0.3s ease-in-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

.schedule-event-header {
  margin-bottom: 24px;
}

.schedule-event-header h1 {
  color: var(--text-dark);
  font-size: 24px;
  font-weight: 600;
  margin-bottom: 8px;
}

.schedule-event-content {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

/* Card Styles */
.schedule-event-form-container,
.schedule-event-list-container,
.visit-management-section,
.enrollment-section,
.patient-search-section {
  background: #FFFFFF;
  box-shadow: var(--card-shadow);
  border-radius: var(--border-radius);
  padding: 24px;
  border: 1px solid var(--border-color);
  transition: var(--transition-default);
  margin-bottom: 24px;
}

.schedule-event-form-container:hover,
.schedule-event-list-container:hover,
.visit-management-section:hover,
.enrollment-section:hover,
.patient-search-section:hover {
  box-shadow: var(--card-shadow-hover);
}

.schedule-event-form-container h2,
.schedule-event-list-container h2,
.visit-management-section h3,
.enrollment-section h2,
.patient-search-section h2 {
  color: var(--text-dark);
  font-size: 20px;
  font-weight: 600;
  margin-bottom: 20px;
  padding-bottom: 12px;
  border-bottom: 1px solid var(--border-color);
}

/* Form Styles */
.schedule-event-form {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
}

.schedule-event-form .form-group {
  margin-bottom: 16px;
}

.schedule-event-form .form-label {
  font-weight: 500;
  margin-bottom: 8px;
  display: block;
  color: var(--text-dark);
}

.schedule-event-form .form-control {
  width: 100%;
  padding: 10px 12px;
  border: 1px solid var(--border-color);
  border-radius: 0; /* Changed */
  transition: var(--transition-default);
}

.schedule-event-form .form-control:focus {
  border-color: var(--primary-color);
  box-shadow: none; /* Changed */
  outline: none;
}

.form-actions {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  margin-top: 24px;
  width: 100%;
}

/* Button Styles */
.create-study-btn,
.save-btn,
.add-unplanned-btn {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 10px 16px;
  border: none;
  border-radius: 0; /* Changed */
  color: #fff;
  background-color: var(--primary-color);
  cursor: pointer;
  font-weight: 500;
  transition: var(--transition-default);
}

.create-study-btn:hover,
.save-btn:hover,
.add-unplanned-btn:hover {
  background-color: var(--primary-hover);
  transform: none; /* Changed */
}

.cancel-btn {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 10px 16px;
  border: none;
  border-radius: 0; /* Changed */
  cursor: pointer;
  font-weight: 500;
  transition: var(--transition-default);
  color: var(--text-medium);
  background-color: var(--background-light);
}

.cancel-btn:hover {
  background-color: var(--border-color);
  transform: none; /* Changed */
}

/* Action Buttons (Select, Edit, Delete) */
.select-btn,
.edit-btn,
.delete-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 36px;
  height: 36px;
  border: none;
  border-radius: 0; /* Changed */
  cursor: pointer;
  transition: var(--transition-default);
}

.select-btn {
  color: #fff;
  background-color: var(--primary-color);
  width: auto;
  padding: 0 12px;
  gap: 6px;
}

.edit-btn {
  color: #fff;
  background-color: var(--primary-color);
}

.delete-btn {
  color: #fff;
  background-color: var(--danger-color);
}

.select-btn:hover,
.edit-btn:hover {
  background-color: var(--primary-hover);
  transform: none; /* Changed */
}

.delete-btn:hover {
  background-color: #dc2626;
  transform: none; /* Changed */
}

/* Study card action buttons */
.study-actions .select-btn,
.study-actions .edit-btn,
.study-actions .delete-btn {
  height: 36px;
}

.study-actions .select-btn {
  width: auto;
  padding: 0 12px;
}

.study-actions .edit-btn,
.study-actions .delete-btn {
  width: 36px;
}

/* Study Card Styles */
.studies-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.study-card {
  background: #FFFFFF;
  box-shadow: var(--card-shadow);
  border-radius: var(--border-radius);
  padding: 16px;
  border: 1px solid var(--border-color);
  transition: var(--transition-default);
}

.study-card:hover {
  box-shadow: var(--card-shadow-hover);
  transform: none; /* Changed */
}

.study-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.study-header h3 {
  font-size: 18px;
  font-weight: 600;
  color: var(--text-dark);
}

.study-actions {
  display: flex;
  gap: 8px;
}

.study-details {
  display: flex;
  flex-direction: column;
  gap: 12px;
  padding: 8px 0;
}

.study-full-title {
  font-size: 15px;
  font-weight: 500;
  color: var(--text-dark);
  margin-bottom: 4px;
  line-height: 1.4;
}

.study-description {
  color: var(--text-medium);
  font-size: 14px;
  line-height: 1.5;
  margin-bottom: 8px;
}

.study-metadata {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
  margin-top: 8px;
  padding: 8px 0;
  border-top: 1px solid var(--border-color);
}

.study-metadata-item {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 14px;
  color: var(--text-medium);
}

.study-metadata-item svg {
  color: var(--primary-color);
}

.study-team {
  font-size: 14px;
  color: var(--text-medium);
  margin-top: 4px;
  padding-top: 8px;
  border-top: 1px dashed var(--border-color);
}

/* Visit Management Section */
.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.visit-actions {
  display: flex;
  gap: 8px;
}

.view-toggle{
  display: flex;
  gap: 5px;
}

.view-toggle-container {
  display: flex;
  justify-content: flex-end;
  margin-bottom: 16px;
}

.visits-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.visit-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
  background-color: var(--background-light);
  border-radius: 0; /* Changed */
  transition: var(--transition-default);
}

.visit-item:hover {
  box-shadow: var(--card-shadow);
}

.visit-info {
  flex: 1;
}

.visit-header {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 8px;
}

.visit-name {
  font-weight: 500;
  color: var(--text-dark);
}

.visit-day {
  padding: 4px 8px;
  border-radius: 0; /* Changed */
  font-size: 12px;
  font-weight: 500;
  background-color: var(--primary-light);
  color: var(--primary-color);
}

.status-badge {
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
}

.status-badge.completed {
  background-color: var(--success-light);
  color: var(--success-color);
}

.status-badge.not-completed, 
.status-badge.cancelled {
  background-color: var(--danger-light);
  color: var(--danger-color);
}

.status-badge.pending, 
.status-badge.planned {
  background-color: var(--primary-light);
  color: var(--primary-color);
}

.status-badge.on-going {
  background-color: var(--warning-light);
  color: var(--warning-color);
}

.visit-details {
  display: flex;
  flex-wrap: wrap;
  gap: 12px;
  margin-bottom: 8px;
}

.visit-reminders, .visit-window {
  font-size: 14px;
  color: var(--text-medium);
}

.visit-comments {
  font-size: 14px;
  color: var(--text-medium);
  margin-bottom: 8px;
}

.visit-tests, .visit-activities {
  font-size: 14px;
  color: var(--text-medium);
}

/* Visit Templates Section */
.visit-templates-section, .visits-section {
  margin-bottom: 24px;
  padding: 16px;
  background-color: white;
  border-radius: 0; /* Changed */
  border: 1px solid var(--border-color);
}

.visit-templates-section h4, .visits-section h4 {
  font-size: 18px;
  font-weight: 600;
  color: var(--text-dark);
  margin-bottom: 16px;
  padding-bottom: 8px;
  border-bottom: 1px solid var(--border-color);
}

.visit-actions {
  display: flex;
  gap: 8px;
  align-items: center;
}

/* Visit action buttons */
.visit-actions .edit-btn,
.visit-actions .delete-btn {
  width: 36px;
  height: 36px;
}

.visit-actions .status-select {
  height: 36px;
  min-width: 120px;
}

.status-select {
  padding: 6px 8px;
  border: 1px solid var(--border-color);
  border-radius: 0; /* Changed */
  background-color: white;
  color: var(--text-dark);
  font-size: 14px;
}

/* Patient Enrollment Layout */
.patient-enrollment-content {
  display: flex;
  flex-direction: row;
  gap: 24px;
  flex-wrap: wrap;
}

.patient-search-container {
  flex: 1;
  min-width: 400px;
}

/* Patient Search Section */
.search-bar {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 0 12px;
  border: 1px solid var(--border-color);
  border-radius: 0; /* Changed */
  margin-bottom: 16px;
  background-color: white;
}

.search-bar input {
  flex: 1;
  padding: 12px 0;
  border: none;
  outline: none;
  font-size: 14px;
}

/* Enrollment Placeholder */
.enrollment-placeholder {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 300px;
  width: 100%;
}

.enrollment-placeholder-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  padding: 32px;
}

.placeholder-icon {
  color: var(--primary-color);
  opacity: 0.7;
  margin-bottom: 16px;
}

.enrollment-placeholder-content h3 {
  font-size: 18px;
  font-weight: 600;
  color: var(--text-dark);
  margin-bottom: 8px;
}

.enrollment-placeholder-content p {
  color: var(--text-medium);
  font-size: 14px;
  max-width: 300px;
}

.patients-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
  max-height: 400px;
  overflow-y: auto;
}

.patient-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px;
  border-radius: 0; /* Changed */
  background-color: var(--background-light);
  cursor: pointer;
  transition: var(--transition-default);
}

/* Patient item buttons */
.patient-item .select-btn {
  height: 36px;
  min-width: 100px;
}

.patient-item:hover {
  background-color: var(--primary-light);
}

.patient-item.selected {
  background-color: var(--primary-light);
  border: 1px solid var(--primary-color);
}

.patient-info {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.patient-name {
  font-weight: 500;
  color: var(--text-dark);
}

.patient-email, .patient-phone {
  font-size: 14px;
  color: var(--text-medium);
}

/* No Data States */
.no-visits, .no-studies, .no-patients, .loading-studies, .loading-patients {
  padding: 24px;
  text-align: center;
  color: var(--text-medium);
  background-color: var(--background-light);
  border-radius: 0; /* Changed */
  font-size: 16px;
}

/* Visit Modal Styles */
.visit-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
  animation: fadeIn 0.2s ease-out;
}

.visit-modal {
  background-color: white;
  border-radius: var(--border-radius);
  width: 90%;
  max-width: 700px;
  max-height: 90vh;
  overflow-y: auto;
  box-shadow: none; /* Changed */
  animation: slideUp 0.3s ease-out;
}

@keyframes slideUp {
  from {
    transform: translateY(50px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

.visit-modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 24px;
  border-bottom: 1px solid var(--border-color);
}

.visit-modal-title {
  font-size: 20px;
  font-weight: 600;
  color: var(--text-dark);
}

.visit-modal-close {
  background: none;
  border: none;
  cursor: pointer;
  color: var(--text-medium);
}

.visit-modal-body {
  padding: 24px;
}

.form-section {
  margin-bottom: 24px;
  padding-bottom: 16px;
  border-bottom: 1px solid var(--border-color);
}

.form-section:last-child {
  margin-bottom: 0;
  padding-bottom: 0;
  border-bottom: none;
}

.form-section-title {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 16px;
  font-weight: 600;
  color: var(--text-dark);
  margin-bottom: 16px;
}

.form-section-title svg {
  color: var(--primary-color);
}

.form-row {
  display: flex;
  gap: 16px;
  margin-bottom: 16px;
}

.visit-modal-body .form-group {
  flex: 1;
  margin-bottom: 16px;
}

.visit-modal-body label {
  display: block;
  margin-bottom: 8px;
  font-weight: 500;
  color: var(--text-dark);
}

.visit-modal-body small {
  display: block;
  margin-top: 4px;
  font-size: 12px;
  color: var(--text-medium);
}

.modal-icon {
  margin-right: 8px;
  color: var(--primary-color);
}

.tests-selection {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 12px;
  margin-top: 8px;
}

.test-checkbox {
  margin-bottom: 8px;
}

.visit-modal-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  padding: 16px 24px;
  border-top: 1px solid var(--border-color);
}

/* Responsive styles */
@media (max-width: 992px) {
  .form-row {
    flex-direction: column;
  }
  
  .visit-modal {
    width: 95%;
  }
}

@media (max-width: 768px) {
  .schedule-event-form .form-group {
    width: 100%;
  }
  
  .study-actions {
    flex-direction: column;
    gap: 8px;
  }
  
  .visit-item {
    flex-direction: column;
    align-items: flex-start;
  }
  
  .visit-actions {
    width: 100%;
    margin-top: 12px;
    justify-content: flex-end;
  }
  
  .tests-selection {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 576px) {
  .schedule-event-container {
    padding: 16px;
  }
  
  .schedule-event-form-container,
  .schedule-event-list-container,
  .visit-management-section,
  .enrollment-section,
  .patient-search-section {
    padding: 16px;
  }
  
  .visit-modal-body {
    padding: 16px;
  }
}

/* PI Assignment Styles */
.pi-assignment-container {
  padding: 15px 24px 24px 24px;
  animation: fadeIn 0.3s ease-in-out;
}

.pi-assignment-header {
  margin-bottom: 32px;
  padding: 24px;
  background: linear-gradient(135deg, var(--primary-light) 0%, #ffffff 100%);
  border-radius: var(--border-radius);
  border: 1px solid var(--border-color);
}

.pi-header-content h1 {
  color: var(--text-dark);
  font-size: 28px;
  font-weight: 700;
  margin-bottom: 12px;
  display: flex;
  align-items: center;
}

.pi-header-content p {
  color: var(--text-medium);
  font-size: 16px;
  margin: 0;
  line-height: 1.6;
}

.pi-assignment-content {
  display: flex;
  flex-direction: column;
  gap: 32px;
}

/* PI Summary Cards */
.pi-summary-cards {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 24px;
  margin-bottom: 32px;
}

.pi-summary-card {
  background: #FFFFFF;
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
  padding: 24px;
  display: flex;
  align-items: center;
  gap: 20px;
  transition: var(--transition-default);
  position: relative;
  overflow: hidden;
}

.pi-summary-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: var(--primary-color);
}

.pi-summary-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(55, 183, 195, 0.15);
}

.pi-card-icon {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.pi-card-icon.pi-pending {
  background: var(--warning-light);
  color: var(--warning-color);
}

.pi-card-icon.pi-available {
  background: var(--success-light);
  color: var(--success-color);
}

.pi-card-content h3 {
  font-size: 32px;
  font-weight: 700;
  color: var(--text-dark);
  margin: 0 0 8px 0;
  line-height: 1;
}

.pi-card-content p {
  color: var(--text-medium);
  font-size: 14px;
  font-weight: 500;
  margin: 0;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

/* PI Studies Section */
.pi-studies-section {
  background: #FFFFFF;
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
  padding: 24px;
}

.pi-section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
  padding-bottom: 16px;
  border-bottom: 2px solid var(--primary-light);
}

.pi-section-header h2 {
  color: var(--text-dark);
  font-size: 22px;
  font-weight: 600;
  margin: 0;
}

.pi-search-container {
  position: relative;
  min-width: 300px;
}

.pi-search-icon {
  position: absolute;
  left: 12px;
  top: 50%;
  transform: translateY(-50%);
  color: var(--text-light);
  z-index: 1;
}

.pi-search-input {
  width: 100%;
  padding: 12px 12px 12px 40px;
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
  font-size: 14px;
  transition: var(--transition-default);
  background: #FFFFFF;
}

.pi-search-input:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px var(--primary-transparent);
}

/* PI Studies Grid */
.pi-studies-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(400px, 1fr));
  gap: 20px;
}

.pi-study-card {
  background: #FFFFFF;
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
  padding: 20px;
  transition: var(--transition-default);
  position: relative;
}

.pi-study-card:hover {
  border-color: var(--primary-color);
  box-shadow: 0 4px 20px rgba(55, 183, 195, 0.1);
}

.pi-study-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 16px;
}

.pi-study-header h3 {
  color: var(--text-dark);
  font-size: 18px;
  font-weight: 600;
  margin: 0;
  line-height: 1.4;
  flex: 1;
  margin-right: 16px;
}

.pi-study-status {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 6px 12px;
  border-radius: 20px;
  font-size: 12px;
  font-weight: 500;
  white-space: nowrap;
  flex-shrink: 0;
}

.pi-study-status.pi-pending {
  background: var(--warning-light);
  color: var(--warning-color);
}

.pi-study-details {
  margin-bottom: 20px;
}

.pi-study-description {
  color: var(--text-medium);
  font-size: 14px;
  line-height: 1.6;
  margin: 0 0 16px 0;
}

.pi-study-meta {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.pi-sponsor,
.pi-created-date {
  color: var(--text-light);
  font-size: 13px;
  display: flex;
  align-items: center;
  gap: 6px;
}

.pi-study-actions {
  display: flex;
  justify-content: flex-end;
}

.pi-assign-btn {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 10px 20px;
  background: var(--primary-color);
  color: #FFFFFF;
  border: none;
  border-radius: var(--border-radius);
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: var(--transition-default);
}

.pi-assign-btn:hover {
  background: var(--primary-hover);
  transform: translateY(-1px);
}

/* PI No Studies State */
.pi-no-studies {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
  text-align: center;
  background: var(--background-light);
  border-radius: var(--border-radius);
  border: 2px dashed var(--border-color);
}

.pi-placeholder-icon {
  color: var(--text-light);
  margin-bottom: 20px;
  opacity: 0.6;
}

.pi-no-studies h3 {
  color: var(--text-dark);
  font-size: 20px;
  font-weight: 600;
  margin: 0 0 12px 0;
}

.pi-no-studies p {
  color: var(--text-medium);
  font-size: 16px;
  margin: 0;
  max-width: 400px;
  line-height: 1.6;
}

/* PI Loading States */
.pi-loading-message {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 40px 20px;
  color: var(--text-medium);
  font-size: 16px;
  background: var(--background-light);
  border-radius: var(--border-radius);
}

/* PI Modal Styles */
.pi-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.6);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
  animation: fadeIn 0.2s ease-out;
}

.pi-assignment-modal {
  background: #FFFFFF;
  border-radius: var(--border-radius);
  width: 90%;
  max-width: 600px;
  max-height: 90vh;
  overflow-y: auto;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
  animation: slideUp 0.3s ease-out;
}

.pi-modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24px;
  border-bottom: 1px solid var(--border-color);
  background: var(--primary-light);
}

.pi-modal-header h2 {
  color: var(--text-dark);
  font-size: 20px;
  font-weight: 600;
  margin: 0;
}

.pi-close-btn {
  background: none;
  border: none;
  cursor: pointer;
  color: var(--text-medium);
  padding: 8px;
  border-radius: 4px;
  transition: var(--transition-default);
}

.pi-close-btn:hover {
  background: rgba(0, 0, 0, 0.1);
  color: var(--text-dark);
}

.pi-modal-content {
  padding: 24px;
}

.pi-study-info {
  margin-bottom: 24px;
  padding: 16px;
  background: var(--background-light);
  border-radius: var(--border-radius);
  border-left: 4px solid var(--primary-color);
}

.pi-study-info h3 {
  color: var(--text-dark);
  font-size: 16px;
  font-weight: 600;
  margin: 0 0 8px 0;
}

.pi-study-info p {
  color: var(--text-medium);
  font-size: 14px;
  margin: 0;
  line-height: 1.5;
}

.pi-investigator-selection h4 {
  color: var(--text-dark);
  font-size: 16px;
  font-weight: 600;
  margin: 0 0 16px 0;
}

.pi-investigators-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
  max-height: 300px;
  overflow-y: auto;
}

.pi-investigator-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px;
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
  cursor: pointer;
  transition: var(--transition-default);
  background: #FFFFFF;
}

.pi-investigator-item:hover {
  border-color: var(--primary-color);
  background: var(--primary-light);
}

.pi-investigator-item.pi-selected {
  border-color: var(--primary-color);
  background: var(--primary-light);
  box-shadow: 0 0 0 2px var(--primary-transparent);
}

.pi-investigator-info h5 {
  color: var(--text-dark);
  font-size: 16px;
  font-weight: 600;
  margin: 0 0 4px 0;
}

.pi-speciality,
.pi-email {
  color: var(--text-medium);
  font-size: 14px;
  margin: 0 0 2px 0;
}

.pi-selected-icon {
  color: var(--primary-color);
  flex-shrink: 0;
}

.pi-no-investigators {
  padding: 20px;
  text-align: center;
  color: var(--text-medium);
  background: var(--background-light);
  border-radius: var(--border-radius);
}

.pi-modal-actions {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  padding: 24px;
  border-top: 1px solid var(--border-color);
  background: var(--background-light);
}

.pi-cancel-btn {
  padding: 10px 20px;
  background: var(--text-light);
  color: #FFFFFF;
  border: none;
  border-radius: var(--border-radius);
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: var(--transition-default);
}

.pi-cancel-btn:hover {
  background: var(--text-medium);
}

.pi-confirm-btn {
  padding: 10px 20px;
  background: var(--primary-color);
  color: #FFFFFF;
  border: none;
  border-radius: var(--border-radius);
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: var(--transition-default);
}

.pi-confirm-btn:hover:not(:disabled) {
  background: var(--primary-hover);
}

.pi-confirm-btn:disabled {
  background: var(--text-light);
  cursor: not-allowed;
}

/* PI Tracker Styles */
.pi-tracker-container {
  padding: 15px 24px 24px 24px;
  animation: fadeIn 0.3s ease-in-out;
}

.pi-tracker-header {
  margin-bottom: 32px;
  padding: 24px;
  background: linear-gradient(135deg, var(--success-light) 0%, #ffffff 100%);
  border-radius: var(--border-radius);
  border: 1px solid var(--border-color);
}

.pi-tracker-header h1 {
  color: var(--text-dark);
  font-size: 28px;
  font-weight: 700;
  margin-bottom: 12px;
  display: flex;
  align-items: center;
}

.pi-tracker-header p {
  color: var(--text-medium);
  font-size: 16px;
  margin: 0;
  line-height: 1.6;
}

/* Responsive Design */
@media (max-width: 768px) {
  .pi-summary-cards {
    grid-template-columns: 1fr;
  }
  
  .pi-section-header {
    flex-direction: column;
    gap: 16px;
    align-items: stretch;
  }
  
  .pi-search-container {
    min-width: auto;
  }
  
  .pi-studies-grid {
    grid-template-columns: 1fr;
  }
  
  .pi-study-header {
    flex-direction: column;
    gap: 12px;
  }
  
  .pi-modal-actions {
    flex-direction: column;
  }
  
  .pi-assignment-modal {
    width: 95%;
    margin: 20px;
  }
}

@media (max-width: 576px) {
  .pi-assignment-container,
  .pi-tracker-container {
    padding: 16px;
  }
  
  .pi-assignment-header,
  .pi-tracker-header {
    padding: 20px;
  }
  
  .pi-summary-card {
    padding: 20px;
  }
  
  .pi-studies-section {
    padding: 20px;
  }
}

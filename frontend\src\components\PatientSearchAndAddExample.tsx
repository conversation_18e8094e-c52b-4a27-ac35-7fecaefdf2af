import React, { useState } from 'react';
import PatientSearchModal from './modal/PatientSearchModal';
import AddPatientFormModal from './modal/AddPatientFormModal';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faUserPlus } from '@fortawesome/free-solid-svg-icons';

/**
 * Example component showing how to use the new patient search and add flow
 *
 * Flow:
 * 1. Click "Search/Add Patient" button
 * 2. PatientSearchModal opens for searching patients
 * 3. If patient found and selected -> navigate to patient details page
 * 4. If no patient found -> "Add New Patient" button appears
 * 5. Click "Add New Patient" -> closes search modal and opens AddPatientFormModal
 */
const PatientSearchAndAddExample: React.FC = () => {
  const [isSearchModalOpen, setIsSearchModalOpen] = useState(false);
  const [isAddPatientModalOpen, setIsAddPatientModalOpen] = useState(false);

  const handleOpenSearchModal = () => {
    setIsSearchModalOpen(true);
  };

  const handleCloseSearchModal = () => {
    setIsSearchModalOpen(false);
  };

  const handleOpenAddPatientModal = () => {
    setIsAddPatientModalOpen(true);
  };


  return (
    <div style={{ padding: '20px' }}>
      <h2>Patient Management</h2>
      <p>Click the button below to search for existing patients or add a new patient.</p>

      <button
        onClick={handleOpenSearchModal}
        style={{
          backgroundColor: '#4a6cf7',
          color: 'white',
          border: 'none',
          borderRadius: '6px',
          padding: '12px 24px',
          fontSize: '1rem',
          fontWeight: '500',
          cursor: 'pointer',
          display: 'flex',
          alignItems: 'center',
          gap: '8px',
          transition: 'background-color 0.2s'
        }}
      >
        <FontAwesomeIcon icon={faUserPlus} />
        Search / Add Patient
      </button>

      {/* Patient Search Modal */}
      <PatientSearchModal
        isOpen={isSearchModalOpen}
        onClose={handleCloseSearchModal}
        onOpenAddPatientModal={handleOpenAddPatientModal}
      />

      {/* Add Patient Form Modal */}
      <AddPatientFormModal
        isOpen={isAddPatientModalOpen}
        setIsModalOpen={setIsAddPatientModalOpen}
        setSwitchModal={() => {}} // Not used in this flow
        switchModal={false} // Not used in this flow
      />
    </div>
  );
};

export default PatientSearchAndAddExample;

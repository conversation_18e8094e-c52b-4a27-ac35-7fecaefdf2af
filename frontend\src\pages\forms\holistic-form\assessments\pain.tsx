import { useState } from "react";
import painImage from "./static/images/added/pain.png";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { faAdd } from "@fortawesome/free-solid-svg-icons";
import useHolisticFormStore from "@/store/holisticFormState"; // Import Zustand store
import NurtifyText from "@/components/NurtifyText";
import NurtifyInput from "@/components/NurtifyInput";
import NurtifyDateInput from "@/components/NurtifyDateInput";
import NurtifyRadio from "@/components/NurtifyRadio";

const Pain: React.FC = () => {
  const { assessment, setAssessment } = useHolisticFormStore();
  const [showAddPainDialog, setShowAddPainDialog] = useState(false);
  const [painName, setPainName] = useState("");
  const [painProvoke, setPainProvoke] = useState("");
  const [painQuality, setPainQuality] = useState("");
  const [painRadiation, setPainRadiation] = useState("");
  const [painSevereness, setPainSevereness] = useState("");
  const [painTime, setPainTime] = useState("");

  const handleAddPains = (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    const form = e.currentTarget;
    const formData = new FormData(form);
    const newPainData = {
      d1: formData.get("pain_name") as string,
      d2: formData.get("pain_provoke") as string,
      d3: formData.get("pain_quality") as string,
      d4: formData.get("pain_radiation") as string,
      d5: formData.get("pain_severeness") as string,
      d6: painTime || (formData.get("pain_time") as string) ,
    };
    setAssessment({
      ...assessment,
      pains: { pain: [...assessment.pains.pain, newPainData], hasPain: true },
    });
    setShowAddPainDialog(false); // Hide dialog after adding
    setPainName("");
    setPainProvoke("");
    setPainQuality("");
    setPainRadiation("");
    setPainSevereness("");
    setPainTime("");
  };

  return (
    <div className="block align-items-*-start flex-column flex-md-row p-4 gap-5">
      <div id="division-30">
        <div className="inlineBlock mb-2 headinqQuestion">
          <img src={painImage} className="imageEtiquette" alt="Pain picture" />
          <span className="mb-2 py-2 fs-2 text-start etiquetteHeadingForms">
            Pain Assessment
          </span>
        </div>

        <div className="list-group col-xl-6 col-lg-8 col-md-12 mt-3">
          <span className="headingQuestion mb-3">Does the patient report pain?</span>
          <NurtifyRadio
            name="hasPain"
            label="Yes"
            value="true"
            checked={assessment?.pains?.hasPain === true}
            onChange={() => {
              setAssessment({
                ...assessment,
                pains: { ...assessment.pains, hasPain: true },
              });
              setShowAddPainDialog(false);
            }}
          />
          <NurtifyRadio
            name="hasPain"
            label="No"
            value="false"
            checked={assessment?.pains?.hasPain === false}
            onChange={() => {
              setAssessment({
                ...assessment,
                pains: { ...assessment.pains, hasPain: false },
              });
              setShowAddPainDialog(false);
            }}
          />

          {assessment?.pains?.hasPain && (
            <>
              {showAddPainDialog ? (
                <div className="options-modal">
                  <div className="center">
                    <form
                      onSubmit={handleAddPains}
                      className="d-flex flex-column mt-4"
                    >
                      <NurtifyText label="Pain Location:" />
                      <NurtifyInput
                        type="text"
                        name="pain_name"
                        className="form-control mb-2"
                        value={painName}
                        onChange={(e) => setPainName(e.target.value)}
                        required
                      />
                      <NurtifyText label="P : What makes the Pain worse?" />
                      <NurtifyInput
                        type="text"
                        name="pain_provoke"
                        value={painProvoke}
                        onChange={(e) => setPainProvoke(e.target.value)}
                        className="form-control mb-2"
                        required
                      />
                      <NurtifyText label="Q: Quality/Type of pain? (Dull, Burning...)" />
                      <NurtifyInput
                        type="text"
                        name="pain_quality"
                        value={painQuality}
                        onChange={(e) => setPainQuality(e.target.value)}
                        className="form-control mb-2"
                        required
                      />
                      <NurtifyText label="R: Does the Pain radiate to other areas?" />
                      <NurtifyInput
                        type="text"
                        name="pain_radiation"
                        value={painRadiation}
                        onChange={(e) => setPainRadiation(e.target.value)}
                        className="form-control mb-2"
                        required
                      />
                      <NurtifyText label="S: How severe is the pain on a scale of 0 - 10?" />
                      <NurtifyInput
                        type="number"
                        name="pain_severeness"
                        value={painSevereness}
                        onChange={(e) => setPainSevereness(e.target.value)}
                        className="form-control mb-2"
                        required
                      />
                      <NurtifyText label="T: When did the Pain start?" />
                      <NurtifyDateInput
                        value={painTime}
                        name="pain_time"
                        onChange={(e) => {setPainTime(e.target.value);console.log(painTime)}}
                        className="form-control mb-2"
                      />
                      <div className="d-flex btns gap-3 mt-3 flex-row">
                        <button
                          onClick={(e) => {
                            e.preventDefault();
                            setShowAddPainDialog(false);
                          }}
                          className="btn btn-secondary"
                        >
                          CANCEL
                        </button>
                        <button
                          type="submit"
                          className="my-primary-btn"
                          onClick={() => handleAddPains}
                        >
                          save
                        </button>
                      </div>
                    </form>
                  </div>
                </div>
              ) : (
                <div className="d-flex flex-row mt-4 align-items-center justify-content-start mb-3">
                  <button
                    onClick={() => setShowAddPainDialog(true)}
                    className="btn-nurtify"
                  >
                    Add Pain <FontAwesomeIcon icon={faAdd} />
                  </button>
                </div>
              )}
              <div className="division d-flex flex-row row-cols-4 flex-wrap gap-2 mt-3">
                {assessment.pains.pain.map((pain, index) => (
                  <div
                    key={index}
                    className="d-flex flex-row gap-1 pain align-items-center"
                    style={{backgroundColor: "#112D4E", color: "white", padding: "10px", borderRadius: "10px", width: "15%"}}
                  >
                    <span className="pain-name">{pain.d1}</span>
                  </div>
                ))}
              </div>
            </>
          )}
        </div>
      </div>
    </div>
  );
};

export default Pain;

# Robots.txt for Nurtify Clinical Trial Platform
# https://nurtify.co.uk/robots.txt

# Allow all search engines to crawl public areas
User-agent: *
Allow: /
Crawl-delay: 1

# Disallow protected routes (prevents soft 404s)
Disallow: /home
Disallow: /clinical
Disallow: /patient/
Disallow: /sponsor/
Disallow: /org/dashboard/
Disallow: /dashboard/
Disallow: /templates
Disallow: /my-patients
Disallow: /patients
Disallow: /admin/
Disallow: /api/
Disallow: /auth/
Disallow: /login
Disallow: /register
Disallow: /logout

# Disallow temporary and cache files
Disallow: /tmp/
Disallow: /cache/
Disallow: /_next/
Disallow: /node_modules/

# Allow important static resources
Allow: /assets/
Allow: /favicon.png
Allow: /robots.txt
Allow: /sitemap.xml

# Specific rules for major search engines
User-agent: Googlebot
Allow: /
Crawl-delay: 1

User-agent: Bingbot
Allow: /
Crawl-delay: 1

User-agent: Slurp
Allow: /
Crawl-delay: 2

# Block unwanted bots
User-agent: AhrefsBot
Disallow: /

User-agent: MJ12bot
Disallow: /

User-agent: DotBot
Disallow: /

# Sitemaps
Sitemap: https://nurtify.co.uk/sitemap.xml

# Host directive (preferred domain)
Host: nurtify.co.uk

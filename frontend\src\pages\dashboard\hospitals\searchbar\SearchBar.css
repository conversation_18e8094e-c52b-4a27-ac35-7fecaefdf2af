.search-bar-container {
  position: relative;
  width: 100%; /* Adjust as needed */
}

.form {
  --input-bg: #fff;
  --padding: 1.5em;
  --rotate: 80deg;
  --gap: 2em;
  --icon-change-color: #37b7c3;
  --height: 40px;
  --border-radius: 0; /* Changed */
  width: 100%;
  padding-inline-end: 4em; /* Space for buttons */
  background: var(--input-bg);
  position: relative;
  border: 1px solid #EDEDED !important;
  border-radius: var(--border-radius);
  box-sizing: border-box;
}

.form label {
  display: flex;
  align-items: center;
  width: 100%;
  height: var(--height);
}

.form input {
  flex: 1;
  padding-inline-start: calc(var(--padding) + var(--gap));
  padding-inline-end: 1em;
  outline: none;
  background: none;
  border: 0;
  font-size: large;
  height: 100%;
}

.form svg {
  color: #111;
  transition: 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: absolute;
  height: 18px;
}

.icon {
  position: absolute;
  left: var(--padding);
  transition: 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  display: flex;
  justify-content: center;
  align-items: center;
}

.swap-off {
  transform: rotate(-80deg);
  opacity: 0;
  visibility: hidden;
}

.close-btn {
  background: none;
  border: none;
  position: absolute;
  right: 6.2em;
  box-sizing: border-box;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #111;
  padding: 0;
  width: 20px;
  height: 20px;
  border-radius: 0; /* Changed */
  transition: 0.3s;
  opacity: 0;
  transform: scale(0);
  visibility: hidden;
  cursor: pointer;
}

.hospitals-search-button {
  position: absolute;
  right: 5px;
  top: 50%;
  transform: translateY(-50%);
  background-color: #3ec1c9;
  color: white;
  border: none;
  padding: 8px 15px;
  border-radius: 0; /* Changed */
  cursor: pointer;
  font-size: medium;
  font-weight: bold;
}

.form input:focus ~ .icon {
  transform: rotate(var(--rotate)) scale(1.3);
}

.form input:focus ~ .icon .swap-off {
  opacity: 1;
  transform: rotate(-80deg);
  visibility: visible;
  color: var(--icon-change-color);
}

.form input:focus ~ .icon .swap-on {
  opacity: 0;
  visibility: hidden;
}

.form input:valid ~ .icon {
  transform: scale(1.3) rotate(var(--rotate));
}

.form input:valid ~ .icon .swap-off {
  opacity: 1;
  visibility: visible;
  color: var(--icon-change-color);
}

.form input:valid ~ .icon .swap-on {
  opacity: 0;
  visibility: hidden;
}

.form input:valid ~ .close-btn {
  opacity: 1;
  visibility: visible;
  transform: scale(1);
  transition: 0s;
}

/* Suggestion List Styling */
.suggestion-list {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  max-height: 200px;
  overflow-y: auto;
  background: rgb(255, 255, 255);
  border: 1px solid #EDEDED; /* Match form border */
  border-top: none;
  
  list-style: none;
  padding: 0 8px 8px 8px; /* Add padding to accommodate scrollbar */
  margin: 0;
  z-index: 50;
  box-sizing: border-box;
}

/* Adjust form border-radius when list is visible */
.search-bar-container:has(.suggestion-list) .form {
  border-radius: 0; /* Changed */
}

.suggestion-list li {
  padding: 8px calc(var(--padding) + var(--gap));
  padding-right: 16px; /* Extra space for scrollbar */
  cursor: pointer;
}

.suggestion-list li:hover {
  background-color: #f0f0f0;
}

/* Scrollbar Styling for better fit */
.suggestion-list::-webkit-scrollbar {
  width: 8px; /* Slimmer scrollbar */
}

.suggestion-list::-webkit-scrollbar-track {
  background: transparent; /* Match background */
}

.suggestion-list::-webkit-scrollbar-thumb {
  background: #ccc; /* Scrollbar color */
  border-radius: 0; /* Changed */
}

.suggestion-list::-webkit-scrollbar-thumb:hover {
  background: #aaa;
}

/* Disabled state */
input:disabled,
.hospitals-search-button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

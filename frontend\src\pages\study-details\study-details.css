/* Study Details Page Styles */

/* Dashboard Container */
.studydetails-dashboard-container {
  display: flex;
  min-height: 100vh;
  background-color: #f8f9fa;
  margin-top: 70px; /* Increased margin to move sidebar down more */
}

/* Sidebar Styles */
.studydetails-sidebar {
  width: 280px;
  background-color: #ffffff;
  border-right: 1px solid #e9ecef;
  transition: width 0.3s ease;
  box-shadow: 2px 0 4px rgba(0, 0, 0, 0.1);
  z-index: 1000;
  position: fixed;
  top: 80px; /* Increased top position to move sidebar down more */
  left: 0;
  height: calc(100vh - 80px); /* Adjusted height calculation */
  overflow-y: auto;
}

.studydetails-sidebar-minimized {
  width: 60px;
}

.studydetails-sidebar-header {
  padding: 16px;
  border-bottom: 1px solid #e9ecef;
}

.studydetails-minimize-btn {
  background: none;
  border: none;
  color: #6c757d;
  cursor: pointer;
  font-size: 14px;
  display: flex;
  align-items: center;
  gap: 8px;
  transition: color 0.2s ease;
}

.studydetails-minimize-btn:hover {
  color: #495057;
}

.studydetails-sidebar-menu {
  list-style: none;
  padding: 0;
  margin: 0;
}

.studydetails-sidebar-item {
  margin: 0;
  padding: 4px 0; /* Added padding between items */
}

.studydetails-sidebar-link {
  width: 100%;
  padding: 16px 20px;
  background: none;
  border: none;
  display: flex;
  align-items: center;
  gap: 12px;
  color: #495057;
  cursor: pointer;
  transition: all 0.2s ease;
  text-align: left;
  font-size: 14px;
  font-weight: 500;
  border-radius: 6px; /* Added border radius for better hover effect */
  margin: 0 8px; /* Added margin to prevent overlap */
  width: calc(100% - 16px); /* Adjusted width to account for margin */
}

.studydetails-sidebar-link:hover {
  background-color: #e8f4f8;
  color: #3f4361;
}

.studydetails-sidebar-link.studydetails-active {
  background-color: #e8f4f8;
  color: #3f4361;
}

.studydetails-sidebar-link.studydetails-active:hover {
  background-color: #e8f4f8;
}

.studydetails-sidebar-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: 20px;
}

/* Main Content Styles */
.studydetails-main-content {
  flex: 1;
  padding: 24px;
  transition: margin-left 0.3s ease;
  margin-left: 280px; /* Account for sidebar width */
}

.studydetails-main-content-expanded {
  margin-left: 60px; /* Account for minimized sidebar width */
}

.studydetails-header {
  margin-bottom: 32px;
}

.studydetails-header .header-content {
  display: flex;
  align-items: center;
  gap: 16px;
  margin-bottom: 24px;
}

.back-button {
  background: none;
  border: none;
  color: #6c757d;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
  transition: color 0.2s ease;
}

.back-button:hover {
  color: #495057;
}

.study-title {
  font-size: 28px;
  font-weight: 700;
  color: #212529;
  margin: 0 0 8px 0;
}

.study-subtitle {
  font-size: 16px;
  color: #6c757d;
  margin: 0;
}

.studydetails-content-section {
  background-color: #ffffff;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

/* Card Styles */
.study-details-card,
.study-resources-card,
.study-team-card,
.study-patients-card {
  background-color: #ffffff;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  width: 100%;
}

.card-header {
  padding: 24px 24px 16px 24px;
  border-bottom: 1px solid #e9ecef;
  background-color: #f8f9fa;
}

.card-title {
  display: flex;
  align-items: center;
  gap: 12px;
  font-size: 20px;
  font-weight: 600;
  color: #212529;
  margin: 0;
}

.card-subtitle {
  display: flex;
  align-items: center;
  gap: 16px;
  margin-top: 8px;
  font-size: 14px;
  color: #6c757d;
}

.patient-count {
  font-weight: 600;
  color: #3f4361;
}

.study-name {
  color: #6c757d;
}

.card-content {
  padding: 24px;
  width: 100%;
}

/* Study Info Grid */
.study-info-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 20px;
  width: 100%;
}

.info-item {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.info-item label {
  font-size: 14px;
  font-weight: 600;
  color: #6c757d;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.info-item span {
  font-size: 16px;
  color: #212529;
  font-weight: 500;
}

.status-badge {
  display: inline-block;
  padding: 4px 12px;
  border-radius: 20px;
  font-size: 12px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.status-badge.active {
  background-color: #d4edda;
  color: #155724;
}

.status-badge.inactive {
  background-color: #f8d7da;
  color: #721c24;
}

/* Loading and Error States */
.loading-state,
.error-state,
.no-data {
  text-align: center;
  padding: 40px 20px;
  color: #6c757d;
  font-size: 16px;
}

.error-state {
  color: #dc3545;
}

.loading-message,
.error-message {
  text-align: center;
  padding: 40px 20px;
}

.error-message h3 {
  color: #dc3545;
  margin-bottom: 16px;
}

.back-btn {
  background-color: #6c757d;
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 4px;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 8px;
  margin-top: 16px;
  transition: background-color 0.2s ease;
}

.back-btn:hover {
  background-color: #5a6268;
}

/* Policy Actions */
.policy-actions {
  display: flex !important;
  gap: 8px;
  align-items: center;
  justify-content: flex-start;
  min-width: 80px;
}

.policy-actions .action-btn {
  width: 32px !important;
  height: 32px !important;
  border: none !important;
  border-radius: 6px !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  cursor: pointer !important;
  transition: all 0.2s ease;
  background-color: #f3f4f6 !important;
  color: #374151 !important;
  margin: 0 !important;
  padding: 0 !important;
  min-width: 32px !important;
  box-shadow: none !important;
}

.policy-actions .action-btn:hover {
  background-color: #e5e7eb;
  color: #1f2937;
}

.policy-actions .action-btn.view-btn {
  background-color: #dbeafe !important;
  color: #3f4361 !important;
}

.policy-actions .action-btn.view-btn:hover {
  background-color: #3f4361;
  color: #ffffff;
}

.policy-actions .action-btn.text-primary {
  color: #2563eb;
}

.policy-actions .action-btn.download-btn {
  background-color: #4F547B;
  color: white;
}

.policy-actions .action-btn.download-btn:hover {
  background-color: #3f4361;
  color: white;
}

/* Filter Section Styles */
.filter-section {
  background-color: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 20px;
  position: relative;
  z-index: 100;
  width: 100%;
}

/* Ensure NurtifyFilter dropdown appears above table */
.filter-section .nurtify-filter {
  position: relative;
  z-index: 1000;
  width: 100%;
}

.filter-section .nurtify-filter__dropdown-content {
  z-index: 1001;
  position: absolute;
}

.results-count {
  background-color: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 6px;
  padding: 12px 16px;
  margin-bottom: 15px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 14px;
  color: #6c757d;
  width: 100%;
}

.clear-filters-btn {
  background: none;
  border: none;
  color: #007bff;
  cursor: pointer;
  font-size: 14px;
  text-decoration: underline;
  padding: 0;
  margin: 0;
  transition: color 0.2s ease;
}

.clear-filters-btn:hover {
  color: #0056b3;
  text-decoration: none;
}

/* DataTable full width styles */
.data-table-container {
  width: 100%;
}

.data-table {
  width: 100%;
}

.data-table table {
  width: 100%;
}

.data-table .table-responsive {
  width: 100%;
}

/* Enhanced Responsive Design */
@media (max-width: 1200px) {
  .studydetails-main-content {
    padding: 20px;
  }

  .card-content {
    padding: 20px;
  }

  .study-info-grid {
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 16px;
  }
}

@media (max-width: 992px) {
  .studydetails-sidebar {
    width: 240px;
  }

  .studydetails-main-content {
    margin-left: 240px;
    padding: 16px;
  }

  .studydetails-main-content-expanded {
    margin-left: 60px;
  }

  .study-title {
    font-size: 24px;
  }

  .card-header {
    padding: 20px 20px 12px 20px;
  }

  .card-content {
    padding: 16px;
  }
}

@media (max-width: 768px) {
  .studydetails-dashboard-container {
    margin-top: 0;
    flex-direction: column;
  }

  .studydetails-sidebar {
    width: 100%;
    position: fixed;
    top: 0;
    left: -100%;
    height: 100vh;
    z-index: 1001;
    transition: left 0.3s ease;
    box-shadow: 2px 0 8px rgba(0, 0, 0, 0.15);
  }

  .studydetails-sidebar.show {
    left: 0;
  }

  .studydetails-sidebar-minimized {
    width: 100%;
  }

  .studydetails-main-content {
    padding: 16px;
    margin-left: 0;
    width: 100%;
  }

  .studydetails-main-content-expanded {
    margin-left: 0;
  }

  .studydetails-header {
    margin-bottom: 24px;
  }

  .studydetails-header .header-content {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
  }

  .study-title {
    font-size: 22px;
  }

  .study-subtitle {
    font-size: 14px;
  }

  .card-header {
    padding: 16px;
  }

  .card-title {
    font-size: 18px;
  }

  .card-content {
    padding: 16px;
  }

  .study-info-grid {
    grid-template-columns: 1fr;
    gap: 16px;
  }

  .info-item label {
    font-size: 12px;
  }

  .info-item span {
    font-size: 14px;
  }

  .policy-actions {
    gap: 6px;
  }

  .policy-actions .action-btn {
    width: 28px;
    height: 28px;
  }

  .results-count {
    flex-direction: column;
    gap: 8px;
    align-items: flex-start;
    padding: 12px;
  }

  .filter-section {
    padding: 12px;
  }

  /* Mobile table improvements */
  .data-table {
    font-size: 14px;
  }

  .data-table th,
  .data-table td {
    padding: 8px 4px;
  }
}

@media (max-width: 576px) {
  .studydetails-main-content {
    padding: 12px;
  }

  .studydetails-header {
    margin-bottom: 20px;
  }

  .study-title {
    font-size: 20px;
  }

  .card-header {
    padding: 12px;
  }

  .card-title {
    font-size: 16px;
  }

  .card-content {
    padding: 12px;
  }

  .study-info-grid {
    gap: 12px;
  }

  .info-item {
    gap: 6px;
  }

  .info-item label {
    font-size: 11px;
  }

  .info-item span {
    font-size: 13px;
  }

  .policy-actions .action-btn {
    width: 24px;
    height: 24px;
  }

  .filter-section {
    padding: 8px;
  }

  .results-count {
    padding: 8px;
    font-size: 12px;
  }

  /* Small mobile table adjustments */
  .data-table {
    font-size: 12px;
  }

  .data-table th,
  .data-table td {
    padding: 6px 2px;
  }
}

@media (max-width: 480px) {
  .studydetails-main-content {
    padding: 8px;
  }

  .card-header {
    padding: 8px;
  }

  .card-content {
    padding: 8px;
  }

  .study-title {
    font-size: 18px;
  }

  .card-title {
    font-size: 14px;
  }

  .policy-actions {
    gap: 4px;
  }

  .policy-actions .action-btn {
    width: 20px;
    height: 20px;
  }
}

/* Landscape orientation adjustments */
@media (max-height: 600px) and (orientation: landscape) {
  .studydetails-sidebar {
    height: calc(100vh - 60px);
    top: 60px;
  }

  .studydetails-dashboard-container {
    margin-top: 60px;
  }

  .card-content {
    padding: 16px;
  }

  .study-info-grid {
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 12px;
  }
}

/* High DPI displays */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
  .studydetails-sidebar {
    border-right-width: 0.5px;
  }

  .card-header {
    border-bottom-width: 0.5px;
  }
}
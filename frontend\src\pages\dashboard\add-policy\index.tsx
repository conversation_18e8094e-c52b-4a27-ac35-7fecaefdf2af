import { useState, useMemo, useEffect, useRef } from "react";
import { motion } from "framer-motion";
import {
  FileText,
  User,
  Info,
  CheckCircle,
  AlertCircle,
  Paperclip,
  FileUp,
  Search,
  ChevronDown,
} from "lucide-react";

import NurtifyText from "@components/NurtifyText";
import NurtifyInput from "@components/NurtifyInput";
import NurtifySelect from "@components/NurtifySelect";
import NurtifyTextArea from "@components/NurtifyTextArea";
import NurtifyDateInput from "@components/NurtifyDateInput";
import NurtifyAttachFileBox from "@/components/common/NurtifyAttachFileBox";
import { useCreatePolicyMutation } from "@/hooks/policy.query";
import { useStudiesQuery } from "@/hooks/study.query";

import "./addPolicy.css";
import { useNavigate } from "react-router-dom";

interface FormData {
  title: string;
  category: string;
  author_name: string;
  job_title: string;
  description: string;
  date_c: string;
  access_level: string;
  attach_content: File | null;
  study_uuid: string;
}

interface ConfirmationModalProps {
  isOpen: boolean;
  onClose: () => void;
  onConfirm: () => void;
  title: string;
  message: string;
  subMessage?: string;
}

interface Study {
  uuid: string;
  name: string;
}

// Define policy categories to match backend model
const POLICY_CATEGORIES = [
  { label: "Clinical Trial Protocol", value: "CLINICAL_TRIAL_PROTOCOL" },
  { label: "Lab Manual", value: "LAB_MANUAL" },
  { label: "Pharmacy Manual", value: "PHARMACY_MANUAL" },
  { label: "Imaging Manual", value: "IMAGING_MANUAL" },
  { label: "ECG or Cardiac Monitoring Manual", value: "ECG_MANUAL" },
  { label: "Randomization and Unblinding Procedures", value: "RANDOMIZATION_PROCEDURES" },
  { label: "Patient Information Sheet", value: "PATIENT_INFO_SHEET" },
  { label: "Patient Education Brochures", value: "PATIENT_EDUCATION" },
  { label: "Safety Management Plan", value: "SAFETY_MANAGEMENT" },
  { label: "Site Visit Schedule", value: "SITE_VISIT_SCHEDULE" },
  { label: "Contact list of study personnel and site staff", value: "CONTACT_LIST_STUDY_PERSONNEL" },
  { label: "Contact details of external vendors", value: "CONTACT_DETAILS_EXTERNAL_VENDORS" },
  { label: "Current versions of the Investigator Brochure and Product Information Forms", value: "CURRENT_VERSIONS_INVESTIGATOR_BROCHURE" },
  { label: "Previous submitted versions", value: "PREVIOUS_SUBMITTED_VERSIONS" },
  { label: "Current approved version", value: "CURRENT_APPROVED_VERSION_1" },
  { label: "Previous approved versions and updates", value: "PREVIOUS_APPROVED_VERSIONS_UPDATES" },
  { label: "Signature pages", value: "SIGNATURE_PAGES" },
  { label: "Current approved versions", value: "CURRENT_APPROVED_VERSIONS_2" },
  { label: "Previous approved versions", value: "PREVIOUS_APPROVED_VERSIONS_2" },
  { label: "Signed Informed Consent forms", value: "SIGNED_INFORMED_CONSENT_FORMS" },
  { label: "Signed Informed Consent Tracking Log", value: "SIGNED_INFORMED_CONSENT_TRACKING_LOG" },
  { label: "Patient Card, Diary or Questionnaire", value: "PATIENT_CARD_DIARY_QUESTIONNAIRE" },
  { label: "Current approved versions", value: "CURRENT_APPROVED_VERSIONS_3" },
  { label: "Previous approved copies", value: "PREVIOUS_APPROVED_COPIES" },
  { label: "Translation certificates", value: "TRANSLATION_CERTIFICATES_1" },
  { label: "Current approved version", value: "CURRENT_APPROVED_VERSION_2" },
  { label: "Previous approved versions", value: "PREVIOUS_APPROVED_VERSIONS_3" },
  { label: "Translation certificates", value: "TRANSLATION_CERTIFICATES_2" },
  { label: "Current version (blank sample)", value: "CURRENT_VERSION_BLANK_SAMPLE" },
  { label: "Previous version (blank sample)", value: "PREVIOUS_VERSION_BLANK_SAMPLE" },
  { label: "Completion guidelines", value: "COMPLETION_GUIDELINES" },
  { label: "Signed, dated and completed CRFs", value: "SIGNED_DATED_COMPLETED_CRFS" },
  { label: "Documentation of CRF edits made", value: "DOCUMENTATION_CRF_EDITS" },
  { label: "Initial submission", value: "INITIAL_SUBMISSION" },
  { label: "Amendments", value: "AMENDMENTS" },
  { label: "Progress Reports", value: "PROGRESS_REPORTS" },
  { label: "Ethics Composition", value: "ETHICS_COMPOSITION" },
  { label: "Notification of Safety Reports", value: "NOTIFICATION_SAFETY_REPORTS" },
  { label: "Notification of Non-compliance and Protocol Deviations", value: "NOTIFICATION_NON_COMPLIANCE" },
  { label: "Correspondence", value: "CORRESPONDENCE_1" },
  { label: "Site Authorisation Letter", value: "SITE_AUTHORISATION_LETTER" },
  { label: "Post Authorisation Submission and Authorisation Letters", value: "POST_AUTHORISATION_SUBMISSION" },
  { label: "Clinical Trial Notification (CTN) or Clinical Trial Exemption (CTX) forms", value: "CLINICAL_TRIAL_NOTIFICATION_CTN_CTX" },
  { label: "Therapeutic Goods Administration (TGA) acknowledgement letter", value: "TGA_ACKNOWLEDGEMENT_LETTER" },
  { label: "Correspondence", value: "CORRESPONDENCE_2" },
  { label: "Delegation Log/Signature sheet", value: "DELEGATION_LOG_SIGNATURE_SHEET" },
  { label: "Curriculum Vitae (CV) (including GCP and Medical License, etc)", value: "CURRICULUM_VITAE_CV" },
  { label: "Training log and documentation", value: "TRAINING_LOG_DOCUMENTATION" },
  { label: "Signed Confidentiality Agreement", value: "SIGNED_CONFIDENTIALITY_AGREEMENT" },
  { label: "Signed Clinical Trial Agreement", value: "SIGNED_CLINICAL_TRIAL_AGREEMENT" },
  { label: "Other relevant agreements/contracts", value: "OTHER_RELEVANT_AGREEMENTS" },
  { label: "Insurance Certificate", value: "INSURANCE_CERTIFICATE" },
  { label: "Indemnity", value: "INDEMNITY" },
  { label: "Participant Screening Log", value: "PARTICIPANT_SCREENING_LOG" },
  { label: "Participant Enrolment Log", value: "PARTICIPANT_ENROLMENT_LOG" },
  { label: "Participant Identification Log", value: "PARTICIPANT_IDENTIFICATION_LOG" },
  { label: "Participant Visit Tracking Log", value: "PARTICIPANT_VISIT_TRACKING_LOG" },
  { label: "Instructions for handling the IP", value: "INSTRUCTIONS_HANDLING_IP" },
  { label: "Shipping and receipt records", value: "SHIPPING_RECEIPT_RECORDS" },
  { label: "Dispensing and Accountability logs", value: "DISPENSING_ACCOUNTABILITY_LOGS" },
  { label: "IP Destruction Logs", value: "IP_DESTRUCTION_LOGS" },
  { label: "IP Storage and Temperature Logs", value: "IP_STORAGE_TEMPERATURE_LOGS" },
  { label: "Decoding and Un-blinding Procedure", value: "DECODING_UNBLINDING_PROCEDURE" },
  { label: "Sample of labels attached to IP containers", value: "SAMPLE_LABELS_IP_CONTAINERS" },
  { label: "Instructions", value: "INSTRUCTIONS_3" },
  { label: "Un-blinding process", value: "UNBLINDING_PROCESS" },
  { label: "Normal values and ranges for medical procedures and tests included in protocol", value: "NORMAL_VALUES_RANGES" },
  { label: "Certification/Accreditation/Established Quality Control/Validation for medical procedures and tests", value: "CERTIFICATION_ACCREDITATION" },
  { label: "Sample Log", value: "SAMPLE_LOG" },
  { label: "Sample Handling Manual", value: "SAMPLE_HANDLING_MANUAL" },
  { label: "Sample Shipping Record", value: "SAMPLE_SHIPPING_RECORD" },
  { label: "Laboratory Manual and Certification", value: "LABORATORY_MANUAL_CERTIFICATION" },
  { label: "Shipping Materials", value: "SHIPPING_MATERIALS" },
  { label: "Samples Destruction/Return Records", value: "SAMPLES_DESTRUCTION_RETURN_RECORDS" },
  { label: "Serious Adverse Event (SAE) Tracking Log", value: "SAE_TRACKING_LOG" },
  { label: "SAE Reports which have been submitted to the Sponsor", value: "SAE_REPORTS_SUBMITTED_SPONSOR" },
  { label: "Safety Reports", value: "SAFETY_REPORTS" },
  { label: "Interim Report and Data Safety Monitoring Boards (DSMB) Reports", value: "INTERIM_REPORT_DSMB" },
  { label: "Final Clinical Study Report", value: "FINAL_CLINICAL_STUDY_REPORT" },
  { label: "Relevant Study Publications and References", value: "RELEVANT_STUDY_PUBLICATIONS" },
  { label: "Investigator Meeting (agenda and presentation)", value: "INVESTIGATOR_MEETING_AGENDA" },
  { label: "Site Initiation Visit (agenda and presentation)", value: "SITE_INITIATION_VISIT_AGENDA" },
  { label: "Other relevant meeting documentation", value: "OTHER_RELEVANT_MEETING_DOCS" },
  { label: "Correspondence with Trial Sponsor", value: "CORRESPONDENCE_TRIAL_SPONSOR" },
  { label: "Correspondence with Site Staff", value: "CORRESPONDENCE_SITE_STAFF" },
  { label: "Correspondence with Laboratory/Vendors", value: "CORRESPONDENCE_LABORATORY_VENDORS" },
  { label: "Other relevant correspondence", value: "OTHER_RELEVANT_CORRESPONDENCE" },
  { label: "Newsletters", value: "NEWSLETTERS" },
  { label: "Other", value: "OTHER" },
] as const;

// Define access level choices to match backend model
const ACCESS_LEVEL_CHOICES = [
  { label: "Staff Only", value: "staff" },
  { label: "Patients And Staff", value: "patientAndStaff" },
] as const;

function ConfirmationModal({
  isOpen,
  onClose,
  onConfirm,
  title,
  message,
  subMessage,
}: ConfirmationModalProps) {
  if (!isOpen) return null;

  return (
    <div className="confirmation-modal-overlay">
      <motion.div
        className="confirmation-modal"
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        exit={{ opacity: 0, y: -20 }}
      >
        <div className="modal-title">{title}</div>
        <div className="modal-message">{message}</div>
        {subMessage && <div className="modal-submessage">{subMessage}</div>}
        <div className="modal-actions">
          <button className="modal-btn-cancel" onClick={onClose}>
            Cancel
          </button>
          <button className="modal-btn-confirm" onClick={onConfirm}>
            Proceed
          </button>
        </div>
      </motion.div>
    </div>
  );
}

export default function AddPolicy() {
  const createPolicyMutation = useCreatePolicyMutation();
  const { data: studiesData } = useStudiesQuery();
  const [currentStep, setCurrentStep] = useState(1);
  const navigate = useNavigate();
  const [formData, setFormData] = useState<FormData>({
    title: "",
    category: "",
    author_name: "",
    job_title: "",
    description: "",
    date_c: "",
    access_level: "staff",
    attach_content: null,
    study_uuid: "",
  });
  const [isConfirmModalOpen, setIsConfirmModalOpen] = useState(false);

  // Category search state
  const [categorySearch, setCategorySearch] = useState("");
  const [isCategoryDropdownOpen, setIsCategoryDropdownOpen] = useState(false);
  const categoryDropdownRef = useRef<HTMLDivElement>(null);

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (categoryDropdownRef.current && !categoryDropdownRef.current.contains(event.target as Node)) {
        setIsCategoryDropdownOpen(false);
        setCategorySearch("");
      }
    };

    if (isCategoryDropdownOpen) {
      document.addEventListener("mousedown", handleClickOutside);
    }

    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, [isCategoryDropdownOpen]);

  // Filter categories based on search
  const filteredCategories = useMemo(() => {
    if (!categorySearch) return POLICY_CATEGORIES;
    return POLICY_CATEGORIES.filter(category =>
      category.label.toLowerCase().includes(categorySearch.toLowerCase())
    );
  }, [categorySearch]);

  // Form validation state
  const [formErrors, setFormErrors] = useState({
    title: false,
    category: false,
    author_name: false,
    job_title: false,
    description: false,
    attach_content: false,
  });

  // Check if form is valid
  const isFormValid = () => {
    const errors = {
      title: !formData.title,
      category: !formData.category,
      author_name: !formData.author_name,
      job_title: !formData.job_title,
      description: !formData.description,
      attach_content: !formData.attach_content,
    };

    setFormErrors(errors);
    return !Object.values(errors).some((error) => error);
  };

  // Convert studies data to options for the select component
  const studyOptions = studiesData?.map((study: Study) => ({
    value: study.uuid,
    label: study.name,
  })) || [];


  const handleCategorySelect = (value: string) => {
    setFormData((prev) => ({
      ...prev,
      category: value,
    }));
    setIsCategoryDropdownOpen(false);
    setCategorySearch("");

    // Clear error for this field when user selects
    if (formErrors.category) {
      setFormErrors((prev) => ({
        ...prev,
        category: false,
      }));
    }
  };

  const handleCategorySearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setCategorySearch(e.target.value);
  };

  const handleStudyChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    setFormData((prev) => ({
      ...prev,
      study_uuid: e.target.value,
    }));
  };

  const handleAccessLevelChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    setFormData((prev) => ({
      ...prev,
      access_level: e.target.value,
    }));
  };

  const handleDateChange = (event: { target: { name: string; value: string } }) => {
    setFormData((prev) => ({
      ...prev,
      [event.target.name]: event.target.value,
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (isFormValid()) {
      setIsConfirmModalOpen(true);
    }
  };

  const handleConfirmSubmit = async () => {
    const submitData = new FormData();
    submitData.append("title", formData.title);
    submitData.append("category", formData.category);
    submitData.append("author_name", formData.author_name);
    submitData.append("job_title", formData.job_title);
    submitData.append("description", formData.description);
    submitData.append("access_level", formData.access_level);
    submitData.append("study_uuid", formData.study_uuid);

    if (formData.date_c) {
      submitData.append("date_c", formData.date_c);
    }

    if (formData.attach_content) {
      submitData.append(
        "attach_content",
        formData.attach_content,
        formData.attach_content.name
      );
    }

    try {
      await createPolicyMutation.mutateAsync(submitData);
      navigate("/sponsor/policy");
    } catch (error) {
      console.error("Error creating policy:", error);
      setIsConfirmModalOpen(false);
    }
  };

  const handleInputChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>
  ) => {
    const { name, value } = e.target;
    setFormData((prev) => ({
      ...prev,
      [name]: value,
    }));

    // Clear error for this field when user types
    if (formErrors[name as keyof typeof formErrors]) {
      setFormErrors((prev) => ({
        ...prev,
        [name]: false,
      }));
    }
  };

  const nextStep = () => {
    if (currentStep < 3) {
      setCurrentStep(currentStep + 1);
    }
  };

  const prevStep = () => {
    if (currentStep > 1) {
      setCurrentStep(currentStep - 1);
    }
  };

  // Get selected study name for summary
  const selectedStudy = studiesData?.find((study: Study) => study.uuid === formData.study_uuid);

  return (
    <div className="add-policy-container">
      <div className="add-policy-header">
        <div className="add-policy-title">
          <h1>
            <FileUp size={24} style={{ marginRight: "10px" }} />
            Adding New Policy
          </h1>
        </div>
        <div className="add-sub-title">
          <h6>Create and share important guidelines with your team</h6>
        </div>
      </div>

      {/* Progress Indicator */}
      <div className="form-progress">
            <div
              className={`progress-step ${currentStep >= 1 ? "active" : ""} ${
                currentStep > 1 ? "completed" : ""
              }`}
            >
              <div className="step-indicator">
                {currentStep > 1 ? <CheckCircle size={16} /> : 1}
              </div>
              <div className="step-label">Basic Info</div>
            </div>
            <div
              className={`progress-step ${currentStep >= 2 ? "active" : ""} ${
                currentStep > 2 ? "completed" : ""
              }`}
            >
              <div className="step-indicator">
                {currentStep > 2 ? <CheckCircle size={16} /> : 2}
              </div>
              <div className="step-label">Details</div>
            </div>
            <div
              className={`progress-step ${currentStep >= 3 ? "active" : ""}`}
            >
              <div className="step-indicator">3</div>
              <div className="step-label">Attachments</div>
            </div>
          </div>

          {/* Form */}
          <form className="add-policy-form" onSubmit={handleSubmit}>
            {/* Step 1: Basic Info */}
            {currentStep === 1 && (
              <motion.div
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                exit={{ opacity: 0, x: 20 }}
                transition={{ duration: 0.3 }}
              >
                <div className="form-section">
                  <h3 className="form-section-title">
                    <FileText
                      size={18}
                      style={{ marginRight: "8px", verticalAlign: "middle" }}
                    />
                    Basic Information
                  </h3>

                  <div className="col-md-12">
                    <NurtifyText label="Policy Title*" />
                    <NurtifyInput
                      type="text"
                      name="title"
                      value={formData.title}
                      onChange={handleInputChange}
                      placeholder="Enter policy title"
                      required
                    />
                    {formErrors.title && (
                      <div
                        style={{
                          color: "#dc3545",
                          fontSize: "14px",
                          marginTop: "5px",
                        }}
                      >
                        <AlertCircle
                          size={14}
                          style={{
                            marginRight: "5px",
                            verticalAlign: "middle",
                          }}
                        />
                        Policy title is required
                      </div>
                    )}
                  </div>

                  <div className="col-md-12" style={{ marginTop: "20px" }}>
                    <NurtifyText label="Category*" />
                    <div className="category-dropdown-container" ref={categoryDropdownRef}>
                      <div
                        className="category-dropdown-trigger"
                        onClick={() => setIsCategoryDropdownOpen(!isCategoryDropdownOpen)}
                      >
                        <span className={`category-dropdown-text ${!formData.category ? 'placeholder' : ''}`}>
                          {formData.category
                            ? POLICY_CATEGORIES.find(cat => cat.value === formData.category)?.label
                            : "Select a category"
                          }
                        </span>
                        <ChevronDown
                          size={16}
                          className={`category-dropdown-icon ${isCategoryDropdownOpen ? 'open' : ''}`}
                        />
                      </div>

                      {isCategoryDropdownOpen && (
                        <div className="category-dropdown-menu">
                          <div className="category-search-container">
                            <div style={{ position: "relative" }}>
                              <Search
                                size={16}
                                className="category-search-icon"
                              />
                              <input
                                type="text"
                                placeholder="Search categories..."
                                value={categorySearch}
                                onChange={handleCategorySearchChange}
                                className="category-search-input"
                                onClick={(e) => e.stopPropagation()}
                              />
                            </div>
                          </div>

                          <div className="category-options-container">
                            {filteredCategories.length > 0 ? (
                              filteredCategories.map((category) => (
                                <div
                                  key={category.value}
                                  className={`category-option ${formData.category === category.value ? 'selected' : ''}`}
                                  onClick={() => handleCategorySelect(category.value)}
                                >
                                  {category.label}
                                </div>
                              ))
                            ) : (
                              <div className="category-no-results">
                                No categories found
                              </div>
                            )}
                          </div>
                        </div>
                      )}
                    </div>
                    {formErrors.category && (
                      <div
                        style={{
                          color: "#dc3545",
                          fontSize: "14px",
                          marginTop: "5px",
                        }}
                      >
                        <AlertCircle
                          size={14}
                          style={{
                            marginRight: "5px",
                            verticalAlign: "middle",
                          }}
                        />
                        Category is required
                      </div>
                    )}
                  </div>

                  <div className="col-md-12" style={{ marginTop: "20px" }}>
                    <NurtifyText label="Study*" />
                    <NurtifySelect
                      name="study_uuid"
                      value={formData.study_uuid || ""}
                      onChange={handleStudyChange}
                      options={[
                        { value: "", label: "Select a study" },
                        ...studyOptions
                      ]}
                    />
                  </div>
                </div>

                <div className="form-actions">
                  <button
                    type="button"
                    className="btn-submit"
                    onClick={nextStep}
                  >
                    Next
                  </button>
                </div>
              </motion.div>
            )}

            {/* Step 2: Author Details */}
            {currentStep === 2 && (
              <motion.div
                initial={{ opacity: 0, x: 20 }}
                animate={{ opacity: 1, x: 0 }}
                exit={{ opacity: 0, x: -20 }}
                transition={{ duration: 0.3 }}
              >
                <div className="form-section">
                  <h3 className="form-section-title">
                    <User
                      size={18}
                      style={{ marginRight: "8px", verticalAlign: "middle" }}
                    />
                    Author Information
                  </h3>

                  <div className="form-row">
                    <div className="form-col">
                      <NurtifyText label="Author Name*" />
                      <NurtifyInput
                        type="text"
                        name="author_name"
                        value={formData.author_name}
                        onChange={handleInputChange}
                        placeholder="Enter author name"
                        required
                      />
                      {formErrors.author_name && (
                        <div
                          style={{
                            color: "#dc3545",
                            fontSize: "14px",
                            marginTop: "5px",
                          }}
                        >
                          <AlertCircle
                            size={14}
                            style={{
                              marginRight: "5px",
                              verticalAlign: "middle",
                            }}
                          />
                          Author name is required
                        </div>
                      )}
                    </div>
                    <div className="form-col">
                      <NurtifyText label="Job Title*" />
                      <NurtifyInput
                        type="text"
                        name="job_title"
                        value={formData.job_title}
                        onChange={handleInputChange}
                        placeholder="Enter job title"
                        required
                      />
                      {formErrors.job_title && (
                        <div
                          style={{
                            color: "#dc3545",
                            fontSize: "14px",
                            marginTop: "5px",
                          }}
                        >
                          <AlertCircle
                            size={14}
                            style={{
                              marginRight: "5px",
                              verticalAlign: "middle",
                            }}
                          />
                          Job title is required
                        </div>
                      )}
                    </div>
                  </div>

                  <div className="col-md-12" style={{ marginTop: "20px" }}>
                    <NurtifyText label="Short Description*" />
                    <NurtifyTextArea
                      name="description"
                      value={formData.description}
                      onChange={handleInputChange}
                      placeholder="Enter a brief description of the policy"
                    />
                    {formErrors.description && (
                      <div
                        style={{
                          color: "#dc3545",
                          fontSize: "14px",
                          marginTop: "5px",
                        }}
                      >
                        <AlertCircle
                          size={14}
                          style={{
                            marginRight: "5px",
                            verticalAlign: "middle",
                          }}
                        />
                        Description is required
                      </div>
                    )}
                  </div>

                  <div className="form-row" style={{ marginTop: "20px" }}>
                    <div className="form-col">
                      <NurtifyText label="Date" />
                      <NurtifyDateInput
                        name="date_c"
                        value={formData.date_c}
                        onChange={handleDateChange}
                        placeholder="Select date"
                      />
                    </div>
                    <div className="form-col">
                      <NurtifyText label="Access Level" />
                      <NurtifySelect
                        name="access_level"
                        value={formData.access_level}
                        onChange={handleAccessLevelChange}
                        options={[...ACCESS_LEVEL_CHOICES]}
                      />
                    </div>
                  </div>
                </div>

                <div className="form-actions">
                  <button
                    type="button"
                    className="btn-cancel"
                    onClick={prevStep}
                  >
                    Back
                  </button>
                  <button
                    type="button"
                    className="btn-submit"
                    onClick={nextStep}
                  >
                    Next
                  </button>
                </div>
              </motion.div>
            )}

            {/* Step 3: Attachments */}
            {currentStep === 3 && (
              <motion.div
                initial={{ opacity: 0, x: 20 }}
                animate={{ opacity: 1, x: 0 }}
                exit={{ opacity: 0, x: -20 }}
                transition={{ duration: 0.3 }}
              >
                <div className="form-section">
                  <h3 className="form-section-title">
                    <Paperclip
                      size={18}
                      style={{ marginRight: "8px", verticalAlign: "middle" }}
                    />
                    Attachments
                  </h3>

                  <div className="col-md-12">
                    <NurtifyText label="File Attachment*" />
                    <NurtifyAttachFileBox
                      onChange={(file) => {
                        setFormData((prev) => ({
                          ...prev,
                          attach_content: file,
                        }));
                        // Clear error when file is selected
                        if (formErrors.attach_content) {
                          setFormErrors((prev) => ({
                            ...prev,
                            attach_content: false,
                          }));
                        }
                      }}
                    />
                    {formErrors.attach_content && (
                      <div
                        style={{
                          color: "#dc3545",
                          fontSize: "14px",
                          marginTop: "5px",
                        }}
                      >
                        <AlertCircle
                          size={14}
                          style={{
                            marginRight: "5px",
                            verticalAlign: "middle",
                          }}
                        />
                        File attachment is required
                      </div>
                    )}
                    <div
                      style={{
                        fontSize: "14px",
                        color: "#4F547B",
                        marginTop: "10px",
                      }}
                    >
                      <Info
                        size={14}
                        style={{ marginRight: "5px", verticalAlign: "middle" }}
                      />
                      Supported file types: PDF, DOC, DOCX, PNG, JPG, JPEG
                    </div>
                  </div>

                  <div className="col-md-12" style={{ marginTop: "30px" }}>
                    <div className="form-summary">
                      <h4 style={{ fontSize: "18px", marginBottom: "15px" }}>
                        Summary
                      </h4>
                      <div className="summary-item">
                        <span className="summary-label">Policy Title:</span>
                        <span className="summary-value">{formData.title}</span>
                      </div>
                      <div className="summary-item">
                        <span className="summary-label">Category:</span>
                        <span className="summary-value">
                          {POLICY_CATEGORIES.find(cat => cat.value === formData.category)?.label || "No category selected"}
                        </span>
                      </div>
                      <div className="summary-item">
                        <span className="summary-label">Study:</span>
                        <span className="summary-value">
                          {selectedStudy ? selectedStudy.name : "No study selected"}
                        </span>
                      </div>
                      <div className="summary-item">
                        <span className="summary-label">Author:</span>
                        <span className="summary-value">
                          {formData.author_name}
                        </span>
                      </div>
                      <div className="summary-item">
                        <span className="summary-label">Job Title:</span>
                        <span className="summary-value">
                          {formData.job_title}
                        </span>
                      </div>
                      <div className="summary-item">
                        <span className="summary-label">Attachment:</span>
                        <span className="summary-value">
                          {formData.attach_content
                            ? formData.attach_content.name
                            : "No file attached"}
                        </span>
                      </div>
                    </div>
                  </div>
                </div>

                <div className="form-actions">
                  <button
                    type="button"
                    className="btn-cancel"
                    onClick={prevStep}
                  >
                    Back
                  </button>
                  <button
                    type="submit"
                    className="btn-submit"
                    disabled={createPolicyMutation.isPending}
                  >
                    {createPolicyMutation.isPending
                      ? "Submitting..."
                      : "Submit Policy"}
                  </button>
                </div>
              </motion.div>
            )}
          </form>
      <ConfirmationModal
        isOpen={isConfirmModalOpen}
        onClose={() => setIsConfirmModalOpen(false)}
        onConfirm={handleConfirmSubmit}
        title="Confirm Policy Submission"
        message="Are you sure you want to submit this policy?"
        subMessage="Please review all information before proceeding."
      />
    </div>
  );
}

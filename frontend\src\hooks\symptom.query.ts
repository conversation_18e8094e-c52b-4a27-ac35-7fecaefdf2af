import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import {
  getAllSymptoms,
  getAllSymptomsWithFilters,
  createSymptom,
  updateSymptom,
  getSymptomLogs,
  getSymptomsByCategory,
  getSymptomsBySeverity,
  getSymptomsByReviewStatus,
  getHospitalizationRequiredSymptoms,
  markSymptomAsReviewed,
  type Symptom,
  type CreateSymptomData,
  type SymptomLog
} from "@/services/api/symptom.service";

export const SYMPTOM_KEYS = {
  all: ['symptoms'] as const,
  byPatient: (patientUuid: string) => [...SYMPTOM_KEYS.all, 'patient', patientUuid] as const,
  logs: (symptomUuid: string) => [...SYMPTOM_KEYS.all, 'logs', symptomUuid] as const,
  byCategory: (category: string, patientUuid?: string) => [...SYMPTOM_KEYS.all, 'category', category, patientUuid] as const,
  bySeverity: (severity: string, patientUuid?: string) => [...SYMPTOM_KEYS.all, 'severity', severity, patientUuid] as const,
  byReviewStatus: (reviewStatus: string, patientUuid?: string) => [...SYMPTOM_KEYS.all, 'reviewStatus', reviewStatus, patientUuid] as const,
  hospitalizationRequired: (patientUuid?: string) => [...SYMPTOM_KEYS.all, 'hospitalizationRequired', patientUuid] as const,
  withFilters: (patientUuid: string, filters?: any) => [...SYMPTOM_KEYS.all, 'filters', patientUuid, filters] as const,
};

export const useSymptomsQuery = (patientUuid: string) => {
  return useQuery<Symptom[], Error>({
    queryKey: SYMPTOM_KEYS.byPatient(patientUuid),
    queryFn: () => getAllSymptoms(patientUuid),
    enabled: !!patientUuid,
  });
};

export const useCreateSymptomMutation = (patientUuid: string) => {
  const queryClient = useQueryClient();
  return useMutation<Symptom, Error, CreateSymptomData>({
    mutationFn: (data) => {
      // Ensure patient UUID is included in the mutation data
      const mutationData = {
        ...data,
        patient: patientUuid,
      };
      console.log('Mutation data:', mutationData); // Debug log
      return createSymptom(mutationData);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: SYMPTOM_KEYS.byPatient(patientUuid)
      });
    },
  });
};

export const useUpdateSymptomMutation = () => {
  const queryClient = useQueryClient();
  return useMutation<Symptom, Error, { uuid: string; data: Partial<CreateSymptomData> }>({
    mutationFn: ({ uuid, data }) => updateSymptom(uuid, data),
    onSuccess: (data) => {
      queryClient.invalidateQueries({
        queryKey: SYMPTOM_KEYS.byPatient(data.patient)
      });
    },
  });
};

export const useSymptomLogsQuery = (symptomUuid: string) => {
  return useQuery<SymptomLog[], Error>({
    queryKey: SYMPTOM_KEYS.logs(symptomUuid),
    queryFn: () => getSymptomLogs(symptomUuid).then(response => response.results),
    enabled: !!symptomUuid,
  });
};

// New query hooks for the additional API endpoints
export const useSymptomsByCategoryQuery = (category: string, patientUuid?: string) => {
  return useQuery<Symptom[], Error>({
    queryKey: SYMPTOM_KEYS.byCategory(category, patientUuid),
    queryFn: () => getSymptomsByCategory(category, patientUuid),
    enabled: !!category,
  });
};

export const useSymptomsBySeverityQuery = (severity: string, patientUuid?: string) => {
  return useQuery<Symptom[], Error>({
    queryKey: SYMPTOM_KEYS.bySeverity(severity, patientUuid),
    queryFn: () => getSymptomsBySeverity(severity, patientUuid),
    enabled: !!severity,
  });
};

export const useSymptomsByReviewStatusQuery = (reviewStatus: string, patientUuid?: string) => {
  return useQuery<Symptom[], Error>({
    queryKey: SYMPTOM_KEYS.byReviewStatus(reviewStatus, patientUuid),
    queryFn: () => getSymptomsByReviewStatus(reviewStatus, patientUuid),
    enabled: !!reviewStatus,
  });
};

export const useHospitalizationRequiredSymptomsQuery = (patientUuid?: string) => {
  return useQuery<Symptom[], Error>({
    queryKey: SYMPTOM_KEYS.hospitalizationRequired(patientUuid),
    queryFn: () => getHospitalizationRequiredSymptoms(patientUuid),
  });
};

export const useSymptomsWithFiltersQuery = (patientUuid: string, filters?: any) => {
  return useQuery<Symptom[], Error>({
    queryKey: SYMPTOM_KEYS.withFilters(patientUuid, filters),
    queryFn: () => getAllSymptomsWithFilters(patientUuid, filters),
    enabled: !!patientUuid,
  });
};

export const useMarkSymptomAsReviewedMutation = () => {
  const queryClient = useQueryClient();
  return useMutation<Symptom, Error, string>({
    mutationFn: (symptomUuid: string) => markSymptomAsReviewed(symptomUuid),
    onSuccess: () => {
      // Invalidate all symptom queries to refresh the data
      queryClient.invalidateQueries({ queryKey: SYMPTOM_KEYS.all });
    },
  });
};
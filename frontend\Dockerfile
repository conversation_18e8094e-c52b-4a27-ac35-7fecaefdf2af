# Stage 0, "build-stage", based on Node.js, to build and compile the frontend
FROM node:20 AS build-stage

WORKDIR /app

COPY package*.json /app/

RUN npm install

COPY ./ /app/

# Accept a build argument to determine the build mode (default: production)
ARG BUILD_MODE=development

# Copy the correct .env file based on the build mode
RUN if [ "$BUILD_MODE" = "development" ]; then \
      cp .env.development .env; \
    else \
      cp .env.production .env; \
    fi

# Run the appropriate build script
RUN if [ "$BUILD_MODE" = "development" ]; then \
      npm run build:dev; \
    else \
      npm run build; \
    fi

# Stage 1, based on Nginx, to have only the compiled app, ready for production with Nginx
FROM nginx:1

COPY --from=build-stage /app/dist/ /usr/share/nginx/html

COPY ./nginx.conf /etc/nginx/conf.d/default.conf
COPY ./nginx-backend-not-found.conf /etc/nginx/extra-conf.d/backend-not-found.conf

import React from "react";
import { ChartNoAxesColumn } from "lucide-react";
import './DashboardKPICard.css';

interface DashboardKPICardProps {
  title: string;
  number: string|number;
}

const DashboardKPICard: React.FC<DashboardKPICardProps> = ({
  title,
  number,
}) => {
  return (
    
      <div className="KPIinnerCard">
        <div className="KPIcardMainContent">
          <div className="KPIformTitleSection">
            <div className="KPIformIcon">
              <ChartNoAxesColumn size={24}/>
            </div>
            <div className="row" >
              <span className="KPIformTitle">{title}</span>
              <span className="KPIformNumber">{number}</span>
            </div>
          </div>
        </div>
      </div>
 
  );
};

export default DashboardKPICard;
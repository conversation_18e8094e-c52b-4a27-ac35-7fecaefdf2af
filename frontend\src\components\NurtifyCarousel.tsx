import React, { useState, ReactNode } from 'react';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faChevronLeft, faChevronRight } from '@fortawesome/free-solid-svg-icons';
import './carousel.css'; // We'll create this CSS file next

interface NurtifyCarouselProps {
  items: ReactNode[]; // Array of items (React nodes) to display
  className?: string; // Optional class for the wrapper
}

const NurtifyCarousel: React.FC<NurtifyCarouselProps> = ({
  items = [],
  className = '',
}) => {
  const [currentIndex, setCurrentIndex] = useState(0);

  const goToPrevious = () => {
    const isFirstSlide = currentIndex === 0;
    const newIndex = isFirstSlide ? items.length - 1 : currentIndex - 1;
    setCurrentIndex(newIndex);
  };

  const goToNext = () => {
    const isLastSlide = currentIndex === items.length - 1;
    const newIndex = isLastSlide ? 0 : currentIndex + 1;
    setCurrentIndex(newIndex);
  };

  if (!items || items.length === 0) {
    return <div className={`nurtify-carousel-wrapper ${className}`}>No items to display.</div>;
  }

  return (
    <div className={`nurtify-carousel-wrapper ${className}`}>
      <button onClick={goToPrevious} className="carousel-arrow left-arrow" aria-label="Previous slide">
        <FontAwesomeIcon icon={faChevronLeft} />
      </button>
      <div className="carousel-slide-container">
        {/* Render only the current slide */}
        {items[currentIndex]}
      </div>
      <button onClick={goToNext} className="carousel-arrow right-arrow" aria-label="Next slide">
        <FontAwesomeIcon icon={faChevronRight} />
      </button>
      {/* Optional: Dots indicator */}
      <div className="carousel-dots">
        {items.map((_, index) => (
          <button
            key={index}
            className={`carousel-dot ${currentIndex === index ? 'active' : ''}`}
            onClick={() => setCurrentIndex(index)}
            aria-label={`Go to slide ${index + 1}`}
          />
        ))}
      </div>
    </div>
  );
};

export default NurtifyCarousel;

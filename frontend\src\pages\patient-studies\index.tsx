import { useState, useEffect } from "react";
import {
  Calendar,
  Edit,
  Plus,
  X,
  Save,
  Clock,
  FileText
} from "lucide-react";
import Preloader from "@/components/common/Preloader";
import Wrapper from "@/components/common/Wrapper";
import DataTable, { Column } from "@/components/common/DataTable";
import {
  useStudyEnrollmentsByPatientQuery,
  useVisitsByEnrollmentQuery,
  usePatchVisitMutation,
  useCreateVisitMutation,
  useUpdateEnrollmentStatusMutation,
  useMyApprovedStudiesQuery,
  usePatientEnrollmentsMutation,
  useAcceptedRescheduleRequestsByPatientQuery,
  usePendingRescheduleRequestsByPatientQuery,
  useRejectedRescheduleRequestsByPatientQuery,
  useAcceptRescheduleRequestMutation,
  useRejectRescheduleRequestMutation
} from "@/hooks/study.query";
import { useConsentFormsByPatientUuidQuery, useSubmitConsentByUuidMutation, useWithdrawConsentMutation } from "@/hooks/consent.query";
import useSelectedPatientStore from "@/store/SelectedPatientState";
import EditVisitModal from "@/components/modal/EditVisitModal";
import CreateVisitModal from "@/components/modal/CreateVisitModal";
import EditEnrollmentModal from "@/components/modal/EditEnrollmentModal";
import { RescheduleRequestResponse } from "@/types/study";
import { toast } from "react-hot-toast";
import "./patient-studies.css";
import NurtifyFilter, { NurtifyFilterItem } from "@/components/NurtifyFilter";

// Define type for enrollment data
interface Enrollment {
  uuid: string;
  study_uuid: string;
  study_id?: number;
  patient_uuid?: string;
  patient_nhs_number: string;
  first_visit: string;
  referred_by: string;
  referred_date: string;
  patient_code?: string;
  comments?: string;
  team_email?: string;
  reminder_email?: string;
  created_at: string;
  study_name: string;
  patient_name: string;
  study_status: string;
}

// Define type for visit data
interface Visit {
  uuid: string;
  name: string;
  study: string;
  study_uuid?: string;
  patient: string;
  date: string;
  time: string;
  number: number;
  allowed_earlier_days: number;
  allowed_later_days: number;
  activities: string[];
  refered_by?: string;
  comments?: string;
  reminder_email?: string;
  visit_status: 'Pending' | 'Completed' | 'Delayed' | 'Canceled';
}

// Define initial enrollment data
const initialEnrollment = {
  patient_code: "",
  first_visit: "",
  referred_by: "",
  referred_date: "",
  comments: "",
  team_email: "",
  reminder_email: ""
};

export default function PatientStudies() {
  const { selectedPatient } = useSelectedPatientStore();
  const { data: enrollments, isLoading: isLoadingEnrollments } = useStudyEnrollmentsByPatientQuery(selectedPatient?.uuid || "");
  const { data: studies, isLoading: isLoadingStudies, isError: isStudiesError } = useMyApprovedStudiesQuery();
  const patchVisitMutation = usePatchVisitMutation();
  const createVisitMutation = useCreateVisitMutation();
  const updateEnrollmentStatusMutation = useUpdateEnrollmentStatusMutation();
  const addEnrollmentMutation = usePatientEnrollmentsMutation();
  const [selectedRescheduleRequest, setSelectedRescheduleRequest] = useState<RescheduleRequestResponse | null>(null);
  const [isEditRescheduleModalOpen, setIsEditRescheduleModalOpen] = useState(false);
  const acceptRescheduleRequest = useAcceptRescheduleRequestMutation();
  const rejectRescheduleRequest = useRejectRescheduleRequestMutation();

  const [expandedEnrollmentUuid, setExpandedEnrollmentUuid] = useState<string | null>(null);
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const [isCreateModalOpen, setIsCreateModalOpen] = useState(false);
  const [isEditEnrollmentModalOpen, setIsEditEnrollmentModalOpen] = useState(false);
  const [isEnrollModalOpen, setIsEnrollModalOpen] = useState(false);
  const [selectedVisit, setSelectedVisit] = useState<Visit | null>(null);
  const [selectedEnrollment, setSelectedEnrollment] = useState<Enrollment | null>(null);
  const [currentStudyUuid, setCurrentStudyUuid] = useState<string | null>(null);
  const [enrollmentFormData, setEnrollmentFormData] = useState(initialEnrollment);
  const [selectedStudyId, setSelectedStudyId] = useState<string | null>(null);
  const [activeTab, setActiveTab] = useState<'enrollments' | 'pending' | 'accepted' | 'rejected'>('enrollments');

  // Consent state variables
  const [expandedConsentUuid, setExpandedConsentUuid] = useState<string | null>(null);
  const [selectedConsentForm, setSelectedConsentForm] = useState<any>(null);
  const [isViewConsentModalOpen, setIsViewConsentModalOpen] = useState(false);
  const [isUpdateConsentModalOpen, setIsUpdateConsentModalOpen] = useState(false);
  const [editConsentAnswers, setEditConsentAnswers] = useState<{ [questionId: string]: boolean }>({});
  const [editCurrentQuestionIndex, setEditCurrentQuestionIndex] = useState(0);
  const [isEditConsentStarted, setIsEditConsentStarted] = useState(false);
  
  // Withdraw consent state
  const [isWithdrawConsentModalOpen, setIsWithdrawConsentModalOpen] = useState(false);
  const [withdrawReason, setWithdrawReason] = useState("");
  const [selectedConsentForWithdrawal, setSelectedConsentForWithdrawal] = useState<any>(null);

  // Get visits for the expanded enrollment
  const { data: visits, isLoading: isLoadingVisits, refetch: refetchVisits } = useVisitsByEnrollmentQuery(
    expandedEnrollmentUuid || "",
    { enabled: !!expandedEnrollmentUuid }
  );

  // Fetch reschedule requests for each tab
  const { data: pendingRescheduleRequests, isLoading: isLoadingPending, refetch: refetchPending } = usePendingRescheduleRequestsByPatientQuery(selectedPatient?.uuid || "");
  const { data: acceptedRescheduleRequests, isLoading: isLoadingAccepted, refetch: refetchAccepted } = useAcceptedRescheduleRequestsByPatientQuery(selectedPatient?.uuid || "");
  const { data: rejectedRescheduleRequests, isLoading: isLoadingRejected, refetch: refetchRejected } = useRejectedRescheduleRequestsByPatientQuery(selectedPatient?.uuid || "");

  // Fetch consent forms for the selected patient
  const { data: consentData, isLoading: isLoadingConsents } = useConsentFormsByPatientUuidQuery(
    selectedPatient?.uuid || "",
    { include_questions: true }
  );

  const [availableStatus, setAvailableStatus] = useState<{ id: string; name: string }[]>([]);
  const [availableStudies, setAvailableStudies] = useState<{ id: string; name: string }[]>([]);
  const [selectedStatus, setSelectedStatus] = useState<string[]>([]);
  const [selectedStudies, setSelectedStudies] = useState<string[]>([]);


  useEffect(() => {
      if (enrollments) {
        setAvailableStatus(
          enrollments.map((enrollment:any) => ({
            id: enrollment.uuid,
            name: enrollment.study_status,
          }))
        );
        setAvailableStudies(
          enrollments.map((enrollment:any) => ({
            id: enrollment.uuid,
            name: enrollment.study_name,
          }))
        );
      }
    }, [enrollments]);

    

  const status = availableStatus.map((status) => ({
    id: status.id,
    label: status.name,
    count: enrollments?.filter((enrollment:any) => enrollment.study_status === status.name).length || 0,
  }));

  const studiesEnrol = availableStudies.map((study) => ({
    id: study.id,
    label: study.name,
    count: enrollments?.filter((enrollment:any) => enrollment.study_name === study.name).length || 0,
  }));

  const filters: NurtifyFilterItem[] = [
      {
        label: "Status",
        type: "checkbox",
        options: status.map((cat) => ({
          label: `${cat.label} (${cat.count})`,
          value: cat.id,
        })),
        value: selectedStatus,
        onChange: setSelectedStatus as any,
      },
      {
        label: "Studies",
        type: "checkbox",
        options: studiesEnrol.map((study:any) => ({
          label: `${study.label} (${study.count})`,
          value: study.id,
        })),
        value: selectedStudies,
        onChange: setSelectedStudies as any,
      },
    ];

  // Submit consent mutation
  const submitConsentMutation = useSubmitConsentByUuidMutation();
  const withdrawConsentMutation = useWithdrawConsentMutation();

  // Debug logging
  console.log('Consent data:', consentData);
  console.log('Enrollments:', enrollments);

  // Filter by study only (status is handled by endpoint)
  const filterByStudy = (requests: RescheduleRequestResponse[] | undefined) =>
    requests?.filter(request => {
      // If no enrollment is selected, show all requests
      if (!selectedEnrollment) return true;
      // Otherwise filter by study UUID
      return request.visit.study.uuid === selectedEnrollment.study_uuid;
    }) || [];

  let filteredRescheduleRequests: RescheduleRequestResponse[] = [];
  let isLoadingRescheduleRequests = false;

  // Helper to ensure array
  const toArray = (data: unknown): RescheduleRequestResponse[] => {
    if (!data) return [];
    if (Array.isArray(data)) return data;
    // If data is an object with a data property (common API response format)
    if (typeof data === 'object' && data !== null && 'data' in data) {
      const dataProperty = (data as any).data;
      return Array.isArray(dataProperty) ? dataProperty : [dataProperty];
    }
    // If data is a single object, wrap it in an array
    if (typeof data === 'object' && data !== null) {
      return [data as RescheduleRequestResponse];
    }
    return [];
  };

  if (activeTab === 'pending') {
    console.log('Raw pending requests:', pendingRescheduleRequests);
    filteredRescheduleRequests = filterByStudy(toArray(pendingRescheduleRequests));
    console.log('After toArray:', toArray(pendingRescheduleRequests));
    console.log('After filterByStudy:', filteredRescheduleRequests);
    isLoadingRescheduleRequests = isLoadingPending;
  } else if (activeTab === 'accepted') {
    filteredRescheduleRequests = filterByStudy(toArray(acceptedRescheduleRequests));
    isLoadingRescheduleRequests = isLoadingAccepted;
  } else if (activeTab === 'rejected') {
    filteredRescheduleRequests = filterByStudy(toArray(rejectedRescheduleRequests));
    isLoadingRescheduleRequests = isLoadingRejected;
  }

  // Handle enrollment form input changes
  const handleEnrollmentInputChange = (event: { target: { name: string; value: string } }) => {
    const { name, value } = event.target;
    setEnrollmentFormData({
      ...enrollmentFormData,
      [name]: value
    });
  };

  // Handle study selection
  const handleSelectStudy = (studyId: string) => {
    setSelectedStudyId(studyId);
  };

  // Handle enrollment submission
  const handleEnrollmentSubmit = (e: React.FormEvent) => {
    e.preventDefault();

    if (!selectedPatient?.uuid || !selectedStudyId) return;

    const enrollment = {
      ...enrollmentFormData,
      id: "", // Add empty id as it will be assigned by the backend
      patient_nhs_number: selectedPatient.nhs_number,
      patient_uuid: selectedPatient.uuid,
      study_uuid: selectedStudyId,
      referred_by: enrollmentFormData.referred_by || "Not specified"
    };

    addEnrollmentMutation.mutate(enrollment, {
      onSuccess: () => {
        setIsEnrollModalOpen(false);
        setEnrollmentFormData(initialEnrollment);
        setSelectedStudyId(null);
      }
    });
  };

  // Handle opening the edit modal
  const handleEditVisit = (visit: Visit) => {
    setSelectedVisit(visit);
    setIsEditModalOpen(true);
  };

  // Handle saving visit changes
  const handleSaveVisit = (updatedVisit: { visit_status: string; date: string }) => {
    if (selectedVisit) {
      // Call the API to update the visit
      patchVisitMutation.mutate({
        uuid: selectedVisit.uuid,
        data: {
          // Include visit_status as a custom property since it's expected by the modal
          ...updatedVisit
        }
      }, {
        onSuccess: () => {
          // Refetch visits to get updated data
          refetchVisits();
        },
        onError: (error) => {
          console.error("Error updating visit:", error);
          // You could add toast notification or alert here
        }
      });
    }
  };

  // Handle opening the edit enrollment modal
  const handleEditEnrollment = (enrollment: Enrollment) => {
    setSelectedEnrollment(enrollment);
    setIsEditEnrollmentModalOpen(true);
  };

  // Handle saving enrollment status changes
  const handleSaveEnrollmentStatus = (updatedEnrollment: { status: string }) => {
    if (selectedEnrollment) {
      // Call the API to update the enrollment status
      updateEnrollmentStatusMutation.mutate({
        enrollmentUuid: selectedEnrollment.uuid,
        statusData: {
          status: updatedEnrollment.status
        }
      }, {
        onSuccess: () => {
          // Close the modal after successful update
          setIsEditEnrollmentModalOpen(false);
        },
        onError: (error) => {
          console.error("Error updating enrollment status:", error);
          alert("Failed to update enrollment status. Please check the console for details.");
        }
      });
    }
  };

  // Handle reschedule request status change
  const handleRescheduleStatusChange = (status: 'Approved' | 'Rejected') => {
    if (!selectedRescheduleRequest) return;

    const mutation = status === 'Approved' ? acceptRescheduleRequest : rejectRescheduleRequest;

    mutation.mutate(selectedRescheduleRequest.uuid, {
      onSuccess: () => {
        toast.success(`Reschedule request ${status.toLowerCase()}`);
        setIsEditRescheduleModalOpen(false);
        setSelectedRescheduleRequest(null);

        // Refetch all reschedule request lists to update the tables
        refetchPending();
        refetchAccepted();
        refetchRejected();
      },
      onError: (error) => {
        console.error(`Error ${status.toLowerCase()}ing reschedule request:`, error);
        toast.error(`Failed to ${status.toLowerCase()} reschedule request`);
      }
    });
  };

  // Add this function to handle opening the reschedule modal
  const handleOpenRescheduleModal = (request: RescheduleRequestResponse) => {
    setSelectedRescheduleRequest(request);
    setIsEditRescheduleModalOpen(true);
  };

  // Edit consent functions
  const handleStartEditConsent = (consentForm: any) => {
    setSelectedConsentForm(consentForm);
    setIsEditConsentStarted(true);
    setEditCurrentQuestionIndex(0);

    // Initialize answers with existing patient answers
    const initialAnswers: { [questionId: string]: boolean } = {};
    if (consentForm.questions) {
      consentForm.questions.forEach((question: any) => {
        initialAnswers[question.question_uuid] = question.patient_answer;
      });
    }
    setEditConsentAnswers(initialAnswers);
    setIsUpdateConsentModalOpen(false);
  };

  const handleEditAnswerQuestion = (questionId: string, answer: boolean) => {
    setEditConsentAnswers(prev => ({
      ...prev,
      [questionId]: answer
    }));
  };

  const handleEditNextQuestion = () => {
    if (selectedConsentForm?.questions && editCurrentQuestionIndex < selectedConsentForm.questions.length - 1) {
      setEditCurrentQuestionIndex(prev => prev + 1);
    }
  };

  const handleEditPreviousQuestion = () => {
    if (editCurrentQuestionIndex > 0) {
      setEditCurrentQuestionIndex(prev => prev - 1);
    }
  };

  const handleSubmitEditConsent = async () => {
    if (!selectedConsentForm || !selectedPatient?.uuid) return;

    const requiredQuestions = selectedConsentForm.questions?.filter((q: any) => q.is_required) || [];
    const answeredRequiredQuestions = requiredQuestions.filter((q: any) =>
      editConsentAnswers[q.question_uuid] !== undefined
    );

    if (answeredRequiredQuestions.length !== requiredQuestions.length) {
      toast.error("Please answer all required questions before submitting.");
      return;
    }

    try {
      const answersArray = Object.entries(editConsentAnswers).map(([questionId, answer]) => ({
        question_id: questionId,
        answer
      }));

      await submitConsentMutation.mutateAsync({
        patientUuid: selectedPatient.uuid,
        consent_form_id: selectedConsentForm.consent_form_uuid,
        answers: answersArray
      });

      toast.success("Consent updated successfully!");
      setIsEditConsentStarted(false);
      setSelectedConsentForm(null);
      setEditConsentAnswers({});
      setEditCurrentQuestionIndex(0);

      // Refresh consent data
      // You might need to add a refetch function to the consent query
    } catch (error: any) {
      toast.error(error.response?.data?.message || "Failed to update consent. Please try again.");
    }
  };

  // Handle withdraw consent
  const handleWithdrawConsent = async () => {
    if (!selectedConsentForWithdrawal) return;

    try {
      await withdrawConsentMutation.mutateAsync({
        patientConsentUuid: selectedConsentForWithdrawal.patient_consent_uuid,
        reason: withdrawReason
      });

      toast.success("Consent withdrawn successfully!");
      setIsWithdrawConsentModalOpen(false);
      setSelectedConsentForWithdrawal(null);
      setWithdrawReason("");
      
      // Refresh consent data
      // You might need to add a refetch function to the consent query
    } catch (error: any) {
      toast.error(error.response?.data?.message || "Failed to withdraw consent. Please try again.");
    }
  };

  // Handle opening withdraw consent modal
  const handleOpenWithdrawModal = (consentForm: any) => {
    setSelectedConsentForWithdrawal(consentForm);
    setIsWithdrawConsentModalOpen(true);
  };

  // Define columns for the enrollments DataTable
  const enrollmentColumns = [
    {
      key: "study_name" as keyof Enrollment,
      header: "Study Name",
    },
    {
      key: "patient_code" as keyof Enrollment,
      header: "Patient ID",
      render: (value: any) => {
        const patientCode = value as string | undefined;
        return patientCode || "N/A";
      },
    },
    {
      key: "study_status" as keyof Enrollment,
      header: "Status",
      render: (value: any) => {
        const status = value as string;
        return (
          <span className={`status-badge status-${status?.toLowerCase()}`}>
            {status || "N/A"}
          </span>
        );
      },
    },
    {
      key: "first_visit" as keyof Enrollment,
      header: "First Visit Date",
      render: (value: any) => {
        const date = value as string;
        return new Date(date).toLocaleDateString();
      },
    },
    {
      key: "referred_by" as keyof Enrollment,
      header: "Referred By",
      render: (value: any) => {
        const referredBy = value as string | undefined;
        return referredBy || "N/A";
      },
    },
    {
      key: "referred_date" as keyof Enrollment,
      header: "Referred Date",
      render: (value: any) => {
        const date = value as string;
        return new Date(date).toLocaleDateString();
      },
    },
    {
      key: "created_at" as keyof Enrollment,
      header: "Enrollment Date",
      render: (value: any) => {
        const date = value as string;
        return new Date(date).toLocaleDateString();
      },
    },
  ];

  // Define columns for the visits DataTable
  const visitColumns = [
    {
      key: "name" as keyof Visit,
      header: "Visit Name",
    },
    {
      key: "number" as keyof Visit,
      header: "Visit Number",
    },
    {
      key: "date" as keyof Visit,
      header: "Date",
      render: (value: any) => {
        const date = value as string;
        return new Date(date).toLocaleDateString();
      },
    },
    {
      key: "visit_status" as keyof Visit,
      header: "Status",
      render: (value: any) => {
        const status = value as string;
        return (
          <span className={`status-badge status-${status.toLowerCase()}`}>
            {status}
          </span>
        );
      },
    },
    {
      key: "activities" as keyof Visit,
      header: "Activities",
      render: (value: any) => {
        const activities = value as string[];
        return activities && activities.length > 0
          ? activities.join(", ")
          : "No activities";
      },
    },
  ];

  // Define actions for the visits DataTable
  const visitActions = [
    {
      icon: <Edit size={16} />,
      tooltipText: "Edit Visit",
      onClick: (row: Visit) => handleEditVisit(row),
    },
  ];

  // Define columns for the consent forms DataTable
  const consentColumns = [
    {
      key: "consent_form_name" as any,
      header: "Consent Form Name",
    },
    {
      key: "consent_form_version" as any,
      header: "Version",
    },
    {
      key: "consent_form_description" as any,
      header: "Description",
      render: (value: any) => {
        const description = value as string | undefined;
        return description
          ? (description.length > 100
            ? `${description.substring(0, 100)}...`
            : description)
          : "No description";
      },
    },
    {
      key: "questions_count" as any,
      header: "Questions Count",
    },
    {
      key: "consent_status" as any,
      header: "Status",
      render: (value: any) => {
        const status = value as string;
        const getStatusColor = (status: string) => {
          switch (status?.toLowerCase()) {
            case 'consented':
              return 'status-consented';
            case 'not consented':
              return 'status-not-consented';
            case 'withdrawn':
              return 'status-withdrawn';
            default:
              return 'status-pending';
          }
        };
        return (
          <span className={`status-badge ${getStatusColor(status)}`}>
            {status || "N/A"}
          </span>
        );
      },
    },
    {
      key: "signed_at" as any,
      header: "Signed Date",
      render: (value: any) => {
        const date = value as string;
        return date ? new Date(date).toLocaleDateString() : "Not signed";
      },
    },
    {
      key: "created_at" as any,
      header: "Created Date",
      render: (value: any) => {
        const date = value as string;
        return new Date(date).toLocaleDateString();
      },
    },
  ];

  // Define actions for the consent forms DataTable
  const consentActions = [
    {
      icon: <FileText size={16} />,
      tooltipText: "View Consent",
      onClick: (row: any) => {
        setSelectedConsentForm(row);
        setIsViewConsentModalOpen(true);
      },
    },
    {
      icon: <Edit size={16} />,
      tooltipText: (row: any) => {
        return row.consent_status === 'Consented'
          ? "Cannot edit - already consented"
          : "Update Consent";
      },
      onClick: (row: any) => {
        handleStartEditConsent(row);
      },
      disabled: (row: any) => row.consent_status === 'Consented',
    },
    {
      icon: <X size={16} />,
      tooltipText: (row: any) => {
        if (row.consent_status === 'Withdrawn') {
          return "Already withdrawn";
        }
        if (row.consent_status !== 'Consented') {
          return "Cannot withdraw - not consented";
        }
        return "Withdraw Consent";
      },
      onClick: (row: any) => {
        handleOpenWithdrawModal(row);
      },
      disabled: (row: any) => row.consent_status !== 'Consented',
    },
  ];

  // Define actions for the enrollments DataTable
  const enrollmentActions = [
    {
      icon: <Edit size={18} />,
      tooltipText: "Edit Enrollment",
      onClick: (row: Enrollment) => {
        handleEditEnrollment(row);
      },
    },
    {
      icon: <Calendar size={16} />,
      tooltipText: "View Visits",
      onClick: (row: Enrollment) => {
        if (expandedEnrollmentUuid === row.uuid) {
          setExpandedEnrollmentUuid(null); // Collapse if already expanded
        } else {
          setExpandedEnrollmentUuid(row.uuid); // Expand this enrollment
        }
      },
    },
    {
      icon: <FileText size={16} />,
      tooltipText: "View Consents",
      onClick: (row: Enrollment) => {
        if (expandedConsentUuid === row.uuid) {
          setExpandedConsentUuid(null); // Collapse if already expanded
        } else {
          setExpandedConsentUuid(row.uuid); // Expand this enrollment's consents
        }
      },
    },
  ];

  // Define columns for reschedule requests with unique keys
  const getRescheduleRequestColumns = (activeTab: string): Column<RescheduleRequestResponse>[] => {
    const baseColumns: Column<RescheduleRequestResponse>[] = [
      {
        key: "visit_name" as keyof RescheduleRequestResponse,
        header: "Visit",
        render: (_, row?: RescheduleRequestResponse) => row?.visit.name,
      },
      {
        key: "visit_study" as keyof RescheduleRequestResponse,
        header: "Study",
        render: (_, row?: RescheduleRequestResponse) => row?.visit.study.name,
      },
      {
        key: "visit_date" as keyof RescheduleRequestResponse,
        header: "Current Date",
        render: (_, row?: RescheduleRequestResponse) => {
          const date = row?.visit.date;
          return date ? new Date(date).toLocaleDateString() : '';
        },
      },
      {
        key: "requested_date" as keyof RescheduleRequestResponse,
        header: "Requested Date",
        render: (value: any) => {
          if (typeof value === 'string') {
            return new Date(value).toLocaleDateString();
          }
          return '';
        },
      },
      {
        key: "requested_time" as keyof RescheduleRequestResponse,
        header: "Requested Time",
        render: (value: any) => {
          if (typeof value === 'string') {
            return value;
          }
          return '';
        },
      },
      {
        key: "reason" as keyof RescheduleRequestResponse,
        header: "Reason",
        render: (value: any) => {
          if (typeof value === 'string') {
            return value;
          }
          return '';
        },
      },
      {
        key: "status" as keyof RescheduleRequestResponse,
        header: "Status",
        render: (value: any) => {
          if (typeof value === 'string') {
            return (
              <span className={`status-badge status-${value.toLowerCase()}`}>
                {value}
              </span>
            );
          }
          return '';
        },
      },
      {
        key: "created_at" as keyof RescheduleRequestResponse,
        header: "Requested On",
        render: (value: any) => {
          if (typeof value === 'string') {
            return new Date(value).toLocaleDateString();
          }
          return '';
        },
      },
    ];

    // Only add the Actions column for pending requests
    if (activeTab === 'pending') {
      baseColumns.push({
        key: "uuid" as keyof RescheduleRequestResponse,
        header: "Actions",
        render: (_, row?: RescheduleRequestResponse) => {
          if (row && row.status === 'Pending') {
            return (
              <button
                onClick={() => handleOpenRescheduleModal(row)}
                className="action-button"
              >
                <Edit size={16} />
              </button>
            );
          }
          return null;
        },
      });
    }

    return baseColumns;
  };

  return (
    <>
      <Wrapper>
        <Preloader />

        <div className="content-wrapper js-content-wrapper">
          <div className="bg-light-4 px-3 py-5">
            <div className="container-fluid py-6 px-6">
              <div className="patient-details-container">
                <div className="patient-details-header">
                  <h1 className="page-title">Patient Studies</h1>
                  <button
                    className="enroll-patient-btn"
                    onClick={() => setIsEnrollModalOpen(true)}
                  >
                    <Plus size={16} /> Enroll in Study
                  </button>
                </div>

                {/* Tabs */}
                <div className="study-tabs" style={{ marginBottom: '20px' }}>
                  <button
                    onClick={() => setActiveTab('enrollments')}
                    style={{
                      padding: '10px 20px',
                      backgroundColor: activeTab === 'enrollments' ? '#3377FF' : 'transparent',
                      color: activeTab === 'enrollments' ? 'white' : '#333',
                      border: 'none',
                      borderBottom: activeTab === 'enrollments' ? '3px solid #3377FF' : '3px solid transparent',
                      cursor: 'pointer',
                      fontWeight: activeTab === 'enrollments' ? '600' : '400'
                    }}
                  >
                    Enrollments
                  </button>
                  <button
                    onClick={() => setActiveTab('pending')}
                    style={{
                      padding: '10px 20px',
                      backgroundColor: activeTab === 'pending' ? '#3377FF' : 'transparent',
                      color: activeTab === 'pending' ? 'white' : '#333',
                      border: 'none',
                      borderBottom: activeTab === 'pending' ? '3px solid #3377FF' : '3px solid transparent',
                      cursor: 'pointer',
                      fontWeight: activeTab === 'pending' ? '600' : '400'
                    }}
                  >
                    <Clock size={16} style={{ marginRight: '8px', verticalAlign: 'middle' }} />
                    Pending Requests
                  </button>
                  <button
                    onClick={() => setActiveTab('accepted')}
                    style={{
                      padding: '10px 20px',
                      backgroundColor: activeTab === 'accepted' ? '#3377FF' : 'transparent',
                      color: activeTab === 'accepted' ? 'white' : '#333',
                      border: 'none',
                      borderBottom: activeTab === 'accepted' ? '3px solid #3377FF' : '3px solid transparent',
                      cursor: 'pointer',
                      fontWeight: activeTab === 'accepted' ? '600' : '400'
                    }}
                  >
                    <Clock size={16} style={{ marginRight: '8px', verticalAlign: 'middle' }} />
                    Accepted Requests
                  </button>
                  <button
                    onClick={() => setActiveTab('rejected')}
                    style={{
                      padding: '10px 20px',
                      backgroundColor: activeTab === 'rejected' ? '#3377FF' : 'transparent',
                      color: activeTab === 'rejected' ? 'white' : '#333',
                      border: 'none',
                      borderBottom: activeTab === 'rejected' ? '3px solid #3377FF' : '3px solid transparent',
                      cursor: 'pointer',
                      fontWeight: activeTab === 'rejected' ? '600' : '400'
                    }}
                  >
                    <Clock size={16} style={{ marginRight: '8px', verticalAlign: 'middle' }} />
                    Rejected Requests
                  </button>
                </div>
                

                {activeTab === 'enrollments' ? (
                  isLoadingEnrollments ? (
                    <div className="loading-studies">Loading enrollments...</div>
                  ) : enrollments && enrollments.length > 0 ? (
                    <div>
                      <NurtifyFilter layout="horizontal" filters={filters} />
                      <div style={{
                        display: 'flex',
                        justifyContent: 'space-between',
                        alignItems: 'center',
                        marginBottom: '20px'
                      }}>
                        <h3 style={{ margin: 0, fontSize: '18px', fontWeight: 600 }}></h3>
                        
                      </div>

                      <DataTable
                        data={enrollments}
                        columns={enrollmentColumns}
                        actions={enrollmentActions}
                      />

                      {expandedEnrollmentUuid && (
                        <div className="visits-container">
                          <h3>Visit Schedule</h3>
                          <div className="visits-header">
                            {expandedEnrollmentUuid && (
                              <button
                                className="create-visit-btn"
                                onClick={() => {
                                  const enrollment = (enrollments || []).find((e: Enrollment) => e.uuid === expandedEnrollmentUuid);

                                  if (enrollment) {
                                    setCurrentStudyUuid(enrollment.study_uuid);
                                    setIsCreateModalOpen(true);
                                  }
                                }}
                              >
                                <Plus size={16} /> Create New Visit
                              </button>
                            )}
                          </div>
                          {isLoadingVisits ? (
                            <div className="loading-studies">Loading visits...</div>
                          ) : visits && visits.length > 0 ? (
                            <DataTable
                              data={visits}
                              columns={visitColumns}
                              actions={visitActions}
                            />
                          ) : (
                            <div className="no-studies">
                              No visits found for this enrollment.
                            </div>
                          )}
                        </div>
                      )}

                      {expandedConsentUuid && (
                        <div className="consents-container">
                          <h3>Consent Forms</h3>
                          {isLoadingConsents ? (
                            <div className="loading-studies">Loading consent forms...</div>
                          ) : consentData?.studies ? (
                            (() => {
                              const enrollment = (enrollments || []).find((e: Enrollment) => e.uuid === expandedConsentUuid);

                              console.log('Enrollment found:', enrollment);
                              console.log('Enrollment study_uuid:', enrollment?.study_uuid);
                              console.log('Available studies:', consentData.studies);

                              // Log each study UUID for comparison
                              consentData.studies.forEach((study, index) => {
                                console.log(`Study ${index}:`, study.study_uuid, 'vs enrollment:', enrollment?.study_uuid);
                                console.log('Match:', study.study_uuid === enrollment?.study_uuid);
                              });

                              // Try to find matching study by UUID
                              let studyConsents = consentData.studies.find(
                                study => study.study_uuid === enrollment?.study_uuid
                              );

                              // If no match by UUID, try to find by study name as fallback
                              if (!studyConsents && enrollment?.study_name) {
                                studyConsents = consentData.studies.find(
                                  study => study.study_name === enrollment.study_name
                                );
                                console.log('Fallback match by name:', studyConsents);
                              }

                              console.log('Study consents found:', studyConsents);

                              return studyConsents && studyConsents.consent_forms.length > 0 ? (
                                <DataTable
                                  data={studyConsents.consent_forms.map(consent => ({
                                    ...consent,
                                    uuid: consent.consent_form_uuid
                                  }))}
                                  columns={consentColumns}
                                  actions={consentActions}
                                />
                              ) : (
                                <div className="no-studies">
                                  No consent forms found for this study. (Study UUID: {enrollment?.study_uuid})
                                  <br />
                                  <small>Available studies: {consentData.studies.map(s => s.study_uuid).join(', ')}</small>
                                  <br />
                                  <small>Enrollment study name: {enrollment?.study_name}</small>
                                </div>
                              );
                            })()
                          ) : (
                            <div className="no-studies">
                              No consent data available.
                            </div>
                          )}
                        </div>
                      )}
                    </div>
                  ) : (
                    <div className="no-studies">
                      You are not currently enrolled in any studies.
                    </div>
                  )
                ) : (
                  // Reschedule Requests Tabs
                  isLoadingRescheduleRequests ? (
                    <div className="loading-studies">Loading reschedule requests...</div>
                  ) : filteredRescheduleRequests.length > 0 ? (
                    <DataTable
                      data={filteredRescheduleRequests}
                      columns={getRescheduleRequestColumns(activeTab)}
                    />
                  ) : (
                    <div className="no-studies">
                      {activeTab === 'pending' && 'No pending reschedule requests found.'}
                      {activeTab === 'accepted' && 'No accepted reschedule requests found.'}
                      {activeTab === 'rejected' && 'No rejected reschedule requests found.'}
                    </div>
                  )
                )}
              </div>
            </div>
          </div>
        </div>
      </Wrapper>

      {/* Edit Visit Modal */}
      {selectedVisit && (
        <EditVisitModal
          isOpen={isEditModalOpen}
          onClose={() => setIsEditModalOpen(false)}
          onSave={handleSaveVisit}
          visitData={{
            name: selectedVisit.name,
            visit_status: selectedVisit.visit_status,
            date: selectedVisit.date,
            time: selectedVisit.time,
          }}
        />
      )}

      {/* Create Visit Modal */}
      <CreateVisitModal
        isOpen={isCreateModalOpen}
        onClose={() => setIsCreateModalOpen(false)}
        studyUuid={
          visits && visits.length > 0 && visits[0].study_uuid ?
          visits[0].study_uuid : currentStudyUuid
        }
        enrollmentUuid={expandedEnrollmentUuid || ""}
        patientId={
          visits && visits.length > 0 && visits[0].patient ?
          parseInt(visits[0].patient) : 0
        }
        studyId={
          visits && visits.length > 0 && visits[0].study ?
          parseInt(visits[0].study) : 0
        }
        onSave={(visitData) => {
          const studyUuidFromVisits = visits && visits.length > 0 && visits[0].study_uuid ?
            visits[0].study_uuid : currentStudyUuid;

          const finalVisitData = {
            ...visitData,
            study_uuid_write: studyUuidFromVisits,
            enrollment_uuid: expandedEnrollmentUuid || ""  // Ensure it's never null
          };

          createVisitMutation.mutate(finalVisitData, {
            onSuccess: () => {
              refetchVisits();
              setIsCreateModalOpen(false);
            },
            onError: (error) => {
              console.error("Error creating visit:", error);
              alert("Failed to create visit. Please check the console for details.");
            }
          });
        }}
      />

      {/* Edit Enrollment Modal */}
      {selectedEnrollment && (
        <EditEnrollmentModal
          isOpen={isEditEnrollmentModalOpen}
          onClose={() => setIsEditEnrollmentModalOpen(false)}
          onSave={handleSaveEnrollmentStatus}
          enrollmentData={{
            uuid: selectedEnrollment.uuid,
            study_name: selectedEnrollment.study_name,
            study_status: selectedEnrollment.study_status
          }}
        />
      )}

      {/* Enroll Patient Modal */}
      {isEnrollModalOpen && (
        <div className="visit-modal-overlay">
          <div className="visit-modal">
            <div className="visit-modal-header">
              <h2 className="visit-modal-title">Enroll in Study</h2>
              <button
                className="visit-modal-close"
                onClick={() => setIsEnrollModalOpen(false)}
              >
                <X size={20} />
              </button>
            </div>
            <div className="visit-modal-body">
              <form onSubmit={handleEnrollmentSubmit} className="schedule-event-form">
                <div className="form-group">
                  <label htmlFor="studySelect">Select Study</label>
                  {isLoadingStudies ? (
                    <div className="loading-studies">Loading approved studies...</div>
                  ) : isStudiesError ? (
                    <div className="error-studies">Error loading studies. Please try again.</div>
                  ) : (
                    <select
                      id="studySelect"
                      value={selectedStudyId || ""}
                      onChange={(e) => handleSelectStudy(e.target.value)}
                      required
                      className="form-control"
                    >
                      <option value="">Select a study</option>
                      {studies?.studies && studies.studies.length > 0 ? (
                        studies.studies.map((study: any) => (
                          <option key={study.uuid} value={study.uuid}>
                            {study.name} - {study.iras}
                          </option>
                        ))
                      ) : (
                        <option value="" disabled>No approved studies available</option>
                      )}
                    </select>
                  )}
                </div>

                <div className="form-group">
                  <label htmlFor="patient_code">Study Patient ID (Optional)</label>
                  <input
                    type="text"
                    id="patient_code"
                    name="patient_code"
                    value={enrollmentFormData.patient_code}
                    onChange={handleEnrollmentInputChange}
                    className="form-control"
                    placeholder="Study-specific patient identifier"
                  />
                </div>

                <div className="form-group">
                  <label htmlFor="first_visit">First Visit Date</label>
                  <input
                    type="date"
                    id="first_visit"
                    name="first_visit"
                    value={enrollmentFormData.first_visit}
                    onChange={handleEnrollmentInputChange}
                    className="form-control"
                    required
                  />
                </div>

                <div className="form-group">
                  <label htmlFor="referred_by">Referred By</label>
                  <input
                    type="text"
                    id="referred_by"
                    name="referred_by"
                    value={enrollmentFormData.referred_by}
                    onChange={handleEnrollmentInputChange}
                    className="form-control"
                    placeholder="Who referred this patient to the study"
                  />
                </div>

                <div className="form-group">
                  <label htmlFor="referred_date">Referred Date</label>
                  <input
                    type="date"
                    id="referred_date"
                    name="referred_date"
                    value={enrollmentFormData.referred_date}
                    onChange={handleEnrollmentInputChange}
                    className="form-control"
                  />
                </div>

                <div className="form-actions">
                  <button
                    type="button"
                    className="cancel-btn"
                    onClick={() => setIsEnrollModalOpen(false)}
                  >
                    <X size={16} /> Cancel
                  </button>
                  <button
                    type="submit"
                    className="save-btn"
                    disabled={!selectedStudyId}
                  >
                    <Save size={16} /> Enroll Patient
                  </button>
                </div>
              </form>
            </div>
          </div>
        </div>
      )}

      {/* Edit Reschedule Request Modal */}
      {selectedRescheduleRequest && isEditRescheduleModalOpen && (
        <div className="visit-modal-overlay">
          <div className="visit-modal">
            <div className="visit-modal-header">
              <h2 className="visit-modal-title">Update Reschedule Request Status</h2>
              <button
                className="visit-modal-close"
                onClick={() => {
                  setIsEditRescheduleModalOpen(false);
                  setSelectedRescheduleRequest(null);
                }}
              >
                <X size={20} />
              </button>
            </div>
            <div className="visit-modal-body">
              <div style={{ marginBottom: '20px' }}>
                <p><strong>Visit:</strong> {selectedRescheduleRequest.visit.name}</p>
                <p><strong>Study:</strong> {selectedRescheduleRequest.visit.study.name}</p>
                <p><strong>Current Date:</strong> {new Date(selectedRescheduleRequest.visit.date).toLocaleDateString()}</p>
                <p><strong>Requested Date:</strong> {new Date(selectedRescheduleRequest.requested_date).toLocaleDateString()}</p>
                <p><strong>Requested Time:</strong> {selectedRescheduleRequest.requested_time}</p>
                <p><strong>Reason:</strong> {selectedRescheduleRequest.reason}</p>
                <p><strong>Current Status:</strong> {selectedRescheduleRequest.status}</p>
              </div>

              <div className="form-actions" style={{ justifyContent: 'center', gap: '10px' }}>
                <button
                  type="button"
                  className="cancel-btn"
                  onClick={() => {
                    setIsEditRescheduleModalOpen(false);
                    setSelectedRescheduleRequest(null);
                  }}
                >
                  <X size={16} /> Cancel
                </button>
                <button
                  type="button"
                  className="save-btn"
                  onClick={() => handleRescheduleStatusChange('Approved')}
                  disabled={selectedRescheduleRequest.status === 'Approved'}
                  style={{ backgroundColor: '#4CAF50' }}
                >
                  <Save size={16} /> Accept
                </button>
                <button
                  type="button"
                  className="save-btn"
                  onClick={() => handleRescheduleStatusChange('Rejected')}
                  disabled={selectedRescheduleRequest.status === 'Rejected'}
                  style={{ backgroundColor: '#f44336' }}
                >
                  <Save size={16} /> Reject
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* View Consent Modal */}
      {selectedConsentForm && isViewConsentModalOpen && (
        <div className="visit-modal-overlay">
          <div className="visit-modal" style={{ maxWidth: '800px', maxHeight: '80vh', overflow: 'auto' }}>
            <div className="visit-modal-header">
              <h2 className="visit-modal-title">View Consent Form</h2>
              <button
                className="visit-modal-close"
                onClick={() => {
                  setIsViewConsentModalOpen(false);
                  setSelectedConsentForm(null);
                }}
              >
                <X size={20} />
              </button>
            </div>
            <div className="visit-modal-body">
              <div style={{ marginBottom: '20px' }}>
                <div style={{
                  padding: '15px',
                  backgroundColor: selectedConsentForm.consent_status === 'Consented' ? '#f6ffed' : '#fff7e6',
                  border: `1px solid ${selectedConsentForm.consent_status === 'Consented' ? '#b7eb8f' : '#ffbb96'}`,
                  borderRadius: '8px',
                  marginBottom: '20px'
                }}>
                  <h3 style={{
                    margin: '0 0 10px 0',
                    color: selectedConsentForm.consent_status === 'Consented' ? '#52c41a' : '#fa8c16'
                  }}>
                    {selectedConsentForm.consent_form_name} (v{selectedConsentForm.consent_form_version})
                  </h3>
                  <p style={{ margin: '5px 0', fontSize: '14px' }}>
                    <strong>Status:</strong>
                    <span style={{
                      color: selectedConsentForm.consent_status === 'Consented' ? '#52c41a' : '#fa8c16',
                      fontWeight: 'bold',
                      marginLeft: '5px'
                    }}>
                      {selectedConsentForm.consent_status}
                    </span>
                  </p>
                  {selectedConsentForm.signed_at && (
                    <p style={{ margin: '5px 0', fontSize: '14px' }}>
                      <strong>Signed:</strong> {new Date(selectedConsentForm.signed_at).toLocaleString()}
                    </p>
                  )}
                  <p style={{ margin: '5px 0', fontSize: '14px' }}>
                    <strong>Questions:</strong> {selectedConsentForm.questions_count}
                  </p>
                </div>

                {selectedConsentForm.consent_form_description && (
                  <div style={{ marginBottom: '20px' }}>
                    <h4>Description</h4>
                    <p>{selectedConsentForm.consent_form_description}</p>
                  </div>
                )}

                {selectedConsentForm.questions && selectedConsentForm.questions.length > 0 ? (
                  <div>
                    <h4>Questions & Answers</h4>
                    {selectedConsentForm.questions.map((question: any, index: number) => (
                      <div key={question.question_uuid} style={{
                        padding: '15px',
                        border: '1px solid #e8e8e8',
                        borderRadius: '8px',
                        marginBottom: '10px',
                        backgroundColor: '#fafafa'
                      }}>
                        <div style={{ marginBottom: '10px' }}>
                          <strong>Question {index + 1}:</strong> {question.question_text}
                          {question.is_required && (
                            <span style={{ color: '#f5222d', marginLeft: '5px' }}>*</span>
                          )}
                        </div>
                        <div style={{
                          padding: '8px 12px',
                          backgroundColor: question.patient_answer ? '#f6ffed' : '#fff2f0',
                          border: `1px solid ${question.patient_answer ? '#b7eb8f' : '#ffccc7'}`,
                          borderRadius: '4px',
                          display: 'inline-block'
                        }}>
                          <strong>Answer:</strong>
                          <span style={{
                            color: question.patient_answer ? '#52c41a' : '#f5222d',
                            marginLeft: '5px'
                          }}>
                            {question.patient_answer ? 'Yes' : 'No'}
                          </span>
                        </div>
                        {question.answered_at && (
                          <div style={{ marginTop: '5px', fontSize: '12px', color: '#666' }}>
                            Answered: {new Date(question.answered_at).toLocaleString()}
                          </div>
                        )}
                      </div>
                    ))}
                  </div>
                ) : (
                  <div style={{
                    padding: '20px',
                    textAlign: 'center',
                    color: '#666',
                    backgroundColor: '#f5f5f5',
                    borderRadius: '8px'
                  }}>
                    No questions available for this consent form.
                  </div>
                )}
              </div>

              <div className="form-actions">
                <button
                  type="button"
                  className="cancel-btn"
                  onClick={() => {
                    setIsViewConsentModalOpen(false);
                    setSelectedConsentForm(null);
                  }}
                >
                  <X size={16} /> Close
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Update Consent Modal - Placeholder for future implementation */}
      {selectedConsentForm && isUpdateConsentModalOpen && (
        <div className="visit-modal-overlay">
          <div className="visit-modal">
            <div className="visit-modal-header">
              <h2 className="visit-modal-title">Update Consent</h2>
              <button
                className="visit-modal-close"
                onClick={() => {
                  setIsUpdateConsentModalOpen(false);
                  setSelectedConsentForm(null);
                }}
              >
                <X size={20} />
              </button>
            </div>
            <div className="visit-modal-body">
              <div style={{
                padding: '20px',
                textAlign: 'center',
                color: '#666',
                backgroundColor: '#f5f5f5',
                borderRadius: '8px',
                marginBottom: '20px'
              }}>
                Update consent functionality will be implemented in the next phase.
              </div>

              <div className="form-actions">
                <button
                  type="button"
                  className="cancel-btn"
                  onClick={() => {
                    setIsUpdateConsentModalOpen(false);
                    setSelectedConsentForm(null);
                  }}
                >
                  <X size={16} /> Close
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Edit Consent Modal */}
      {selectedConsentForm && isEditConsentStarted && (
        <div className="visit-modal-overlay">
          <div className="visit-modal" style={{ maxWidth: '800px', maxHeight: '90vh', overflow: 'auto' }}>
            <div className="visit-modal-header">
              <h2 className="visit-modal-title">Update Consent: {selectedConsentForm.consent_form_name}</h2>
              <button
                className="visit-modal-close"
                onClick={() => {
                  setIsEditConsentStarted(false);
                  setSelectedConsentForm(null);
                  setEditConsentAnswers({});
                  setEditCurrentQuestionIndex(0);
                }}
              >
                <X size={20} />
              </button>
            </div>
            <div className="visit-modal-body">
              {selectedConsentForm.questions && selectedConsentForm.questions.length > 0 ? (
                <div>
                  {/* Progress Bar */}
                  <div style={{ marginBottom: '20px' }}>
                    <div style={{
                      width: '100%',
                      height: '8px',
                      backgroundColor: '#e8e8e8',
                      borderRadius: '4px',
                      overflow: 'hidden'
                    }}>
                      <div style={{
                        width: `${((editCurrentQuestionIndex + 1) / selectedConsentForm.questions.length) * 100}%`,
                        height: '100%',
                        backgroundColor: '#36B6C2',
                        transition: 'width 0.3s ease'
                      }}></div>
                    </div>
                    <div style={{
                      textAlign: 'center',
                      marginTop: '8px',
                      fontSize: '14px',
                      color: '#666'
                    }}>
                      Question {editCurrentQuestionIndex + 1} of {selectedConsentForm.questions.length}
                    </div>
                  </div>

                  {/* Current Question */}
                  {(() => {
                    const currentQuestion = selectedConsentForm.questions[editCurrentQuestionIndex];
                    return (
                      <div style={{ marginBottom: '30px' }}>
                        <div style={{
                          padding: '20px',
                          border: '1px solid #e8e8e8',
                          borderRadius: '8px',
                          backgroundColor: '#fafafa'
                        }}>
                          <div style={{ marginBottom: '15px' }}>
                            <div style={{
                              display: 'flex',
                              alignItems: 'center',
                              gap: '10px',
                              marginBottom: '10px'
                            }}>
                              <span style={{
                                fontWeight: 'bold',
                                fontSize: '16px'
                              }}>
                                Q{editCurrentQuestionIndex + 1}
                              </span>
                              {currentQuestion.is_required && (
                                <span style={{
                                  color: '#f5222d',
                                  fontSize: '14px',
                                  fontWeight: 'bold'
                                }}>
                                  * Required
                                </span>
                              )}
                            </div>
                            <p style={{
                              fontSize: '16px',
                              lineHeight: '1.5',
                              margin: 0
                            }}>
                              {currentQuestion.question_text}
                            </p>
                          </div>

                          {/* Answer Options */}
                          <div style={{ display: 'flex', gap: '15px' }}>
                            <label style={{
                              display: 'flex',
                              alignItems: 'center',
                              gap: '8px',
                              cursor: 'pointer',
                              padding: '10px 15px',
                              border: `2px solid ${editConsentAnswers[currentQuestion.question_uuid] === true ? '#36B6C2' : '#e8e8e8'}`,
                              borderRadius: '6px',
                              backgroundColor: editConsentAnswers[currentQuestion.question_uuid] === true ? '#f0f9ff' : 'white',
                              transition: 'all 0.2s ease'
                            }}>
                              <input
                                type="radio"
                                name={`edit-answer-${currentQuestion.question_uuid}`}
                                checked={editConsentAnswers[currentQuestion.question_uuid] === true}
                                onChange={() => handleEditAnswerQuestion(currentQuestion.question_uuid, true)}
                                style={{ margin: 0 }}
                              />
                              <span style={{
                                color: editConsentAnswers[currentQuestion.question_uuid] === true ? '#36B6C2' : '#666',
                                fontWeight: '500'
                              }}>
                                Yes
                              </span>
                            </label>

                            <label style={{
                              display: 'flex',
                              alignItems: 'center',
                              gap: '8px',
                              cursor: 'pointer',
                              padding: '10px 15px',
                              border: `2px solid ${editConsentAnswers[currentQuestion.question_uuid] === false ? '#f5222d' : '#e8e8e8'}`,
                              borderRadius: '6px',
                              backgroundColor: editConsentAnswers[currentQuestion.question_uuid] === false ? '#fff2f0' : 'white',
                              transition: 'all 0.2s ease'
                            }}>
                              <input
                                type="radio"
                                name={`edit-answer-${currentQuestion.question_uuid}`}
                                checked={editConsentAnswers[currentQuestion.question_uuid] === false}
                                onChange={() => handleEditAnswerQuestion(currentQuestion.question_uuid, false)}
                                style={{ margin: 0 }}
                              />
                              <span style={{
                                color: editConsentAnswers[currentQuestion.question_uuid] === false ? '#f5222d' : '#666',
                                fontWeight: '500'
                              }}>
                                No
                              </span>
                            </label>
                          </div>
                        </div>
                      </div>
                    );
                  })()}

                  {/* Navigation Buttons */}
                  <div style={{
                    display: 'flex',
                    justifyContent: 'space-between',
                    gap: '10px'
                  }}>
                    <button
                      type="button"
                      className="cancel-btn"
                      onClick={() => {
                        setIsEditConsentStarted(false);
                        setSelectedConsentForm(null);
                        setEditConsentAnswers({});
                        setEditCurrentQuestionIndex(0);
                      }}
                      disabled={submitConsentMutation.isPending}
                    >
                      <X size={16} /> Cancel
                    </button>

                    <div style={{ display: 'flex', gap: '10px' }}>
                      <button
                        type="button"
                        className="cancel-btn"
                        onClick={handleEditPreviousQuestion}
                        disabled={editCurrentQuestionIndex === 0 || submitConsentMutation.isPending}
                      >
                        Previous
                      </button>

                      {editCurrentQuestionIndex < selectedConsentForm.questions.length - 1 ? (
                        <button
                          type="button"
                          className="save-btn"
                          onClick={handleEditNextQuestion}
                          disabled={editConsentAnswers[selectedConsentForm.questions[editCurrentQuestionIndex].question_uuid] === undefined || submitConsentMutation.isPending}
                        >
                          Next
                        </button>
                      ) : (
                        <button
                          type="button"
                          className="save-btn"
                          onClick={handleSubmitEditConsent}
                          disabled={submitConsentMutation.isPending || editConsentAnswers[selectedConsentForm.questions[editCurrentQuestionIndex].question_uuid] === undefined}
                        >
                          {submitConsentMutation.isPending ? "Updating..." : "Update Consent"}
                        </button>
                      )}
                    </div>
                  </div>
                </div>
              ) : (
                <div style={{
                  padding: '20px',
                  textAlign: 'center',
                  color: '#666',
                  backgroundColor: '#f5f5f5',
                  borderRadius: '8px'
                }}>
                  No questions available for this consent form.
                </div>
              )}
            </div>
          </div>
        </div>
      )}

      {/* Withdraw Consent Modal */}
      {selectedConsentForWithdrawal && isWithdrawConsentModalOpen && (
        <div className="visit-modal-overlay">
          <div className="visit-modal">
            <div className="visit-modal-header">
              <h2 className="visit-modal-title">Withdraw Consent</h2>
              <button
                className="visit-modal-close"
                onClick={() => {
                  setIsWithdrawConsentModalOpen(false);
                  setSelectedConsentForWithdrawal(null);
                  setWithdrawReason("");
                }}
              >
                <X size={20} />
              </button>
            </div>
            <div className="visit-modal-body">
              <div style={{ marginBottom: '20px' }}>
                <div style={{ 
                  padding: '15px', 
                  backgroundColor: '#fff2f0',
                  border: '1px solid #ffccc7',
                  borderRadius: '8px',
                  marginBottom: '20px'
                }}>
                  <h3 style={{ 
                    margin: '0 0 10px 0', 
                    color: '#f5222d'
                  }}>
                    {selectedConsentForWithdrawal.consent_form_name} (v{selectedConsentForWithdrawal.consent_form_version})
                  </h3>
                  <p style={{ margin: '5px 0', fontSize: '14px' }}>
                    <strong>Current Status:</strong> 
                    <span style={{ 
                      color: '#f5222d',
                      fontWeight: 'bold',
                      marginLeft: '5px'
                    }}>
                      {selectedConsentForWithdrawal.consent_status}
                    </span>
                  </p>
                  {selectedConsentForWithdrawal.signed_at && (
                    <p style={{ margin: '5px 0', fontSize: '14px' }}>
                      <strong>Signed:</strong> {new Date(selectedConsentForWithdrawal.signed_at).toLocaleString()}
                    </p>
                  )}
                </div>

                <div style={{ marginBottom: '20px' }}>
                  <h4>Warning</h4>
                  <p style={{ color: '#f5222d', fontWeight: 'bold' }}>
                    Withdrawing consent will immediately revoke your participation in this study. 
                    This action cannot be undone.
                  </p>
                </div>

                <div className="form-group">
                  <label htmlFor="withdrawReason">Reason for Withdrawal (Optional)</label>
                  <textarea
                    id="withdrawReason"
                    value={withdrawReason}
                    onChange={(e) => setWithdrawReason(e.target.value)}
                    className="form-control"
                    placeholder="Please provide a reason for withdrawing consent (max 1000 characters)"
                    rows={4}
                    maxLength={1000}
                    style={{ resize: 'vertical' }}
                  />
                  <small style={{ color: '#666', marginTop: '5px', display: 'block' }}>
                    {withdrawReason.length}/1000 characters
                  </small>
                </div>
              </div>

              <div className="form-actions">
                <button
                  type="button"
                  className="cancel-btn"
                  onClick={() => {
                    setIsWithdrawConsentModalOpen(false);
                    setSelectedConsentForWithdrawal(null);
                    setWithdrawReason("");
                  }}
                  disabled={withdrawConsentMutation.isPending}
                >
                  <X size={16} /> Cancel
                </button>
                <button
                  type="button"
                  className="save-btn"
                  onClick={handleWithdrawConsent}
                  disabled={withdrawConsentMutation.isPending}
                  style={{ backgroundColor: '#f5222d' }}
                >
                  {withdrawConsentMutation.isPending ? "Withdrawing..." : "Withdraw Consent"}
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </>
  );
}

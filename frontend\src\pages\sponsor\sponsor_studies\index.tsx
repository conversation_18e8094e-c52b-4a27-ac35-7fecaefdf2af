import { useState } from "react";
import { Plus, Calendar, Edit, Trash2, <PERSON><PERSON><PERSON><PERSON>, <PERSON>Edit, Clip<PERSON><PERSON>ist, FileText, AlertCircle, CheckCircle, Loader2, X } from "lucide-react";
import Preloader from "@/components/common/Preloader";
import Wrapper from "@/components/common/Wrapper";
import DataTable from "@/components/common/DataTable";
import NurtifyInput from "@/components/NurtifyInput";
import NurtifyTextArea from "@/components/NurtifyTextArea";
import VisitModal from "@/components/modal/VisitModal";
import { useStudiesBySponsorQuery, useCreateStudyMutation, useUpdateStudyMutation, useDeleteStudyMutation } from "@/hooks/study.query";
import { useCreateVisitTemplateMutation, useVisitTemplatesByStudyQuery, useDeleteVisitTemplateMutation, useUpdateVisitTemplateMutation } from "@/hooks/study.query";
import { useCurrentUserQuery } from "@/hooks/user.query";
import { availableTests, VisitTemplate } from "@/store/scheduleEventState";
import { useQueryClient } from "@tanstack/react-query";
import "./sponsor-studies.css";

// Define types
interface Task {
  name: string;
  description: string;
}

interface Study {
  uuid: string;
  name: string;
  description?: string;
  full_title?: string;
  team_email?: string;
  leading_team?: string;
  created_by?: {
    first_name?: string;
    last_name?: string;
  };
  iras: string;
  created_at: string;
  updated_at: string;
  tasks_data?: Task[];
}

interface VisitsListSectionProps {
  study: Study;
  onAddVisit: () => void;
  onEditVisit: (visit: VisitTemplate) => void;
  onDeleteVisit: (visitUuid: string) => void;
}

// VisitsListSection Component
function VisitsListSection({ study, onAddVisit, onEditVisit, onDeleteVisit }: VisitsListSectionProps) {
  const { data: visitTemplates, isLoading: isLoadingVisitTemplates } = useVisitTemplatesByStudyQuery(study.uuid);

  return (
    <div className="sponsor-studies-visits-section">
      <div className="sponsor-studies-visits-header">
        <h3 className="sponsor-studies-visits-title">Visit Templates for {study.name}</h3>
        <button className="sponsor-studies-add-visit-btn" onClick={onAddVisit}>
          <Plus size={16} /> Add Visit Template
        </button>
      </div>

      {isLoadingVisitTemplates ? (
        <div className="sponsor-studies-loading">Loading visits...</div>
      ) : visitTemplates && visitTemplates.length > 0 ? (
        <div className="sponsor-studies-visits-list">
          {visitTemplates.map((visit: VisitTemplate) => (
            <div key={visit.uuid} className="sponsor-studies-visit-card">
              <div className="sponsor-studies-visit-content">
                <div className="sponsor-studies-visit-info">
                  <h3>{visit.name}</h3>
                  <p>Day: {visit.day}</p>
                  <p>Visit Number: {visit.visit_number}</p>
                  <p>Activities: {visit.activities?.join(", ") || "None"}</p>
                </div>
                <div className="sponsor-studies-visit-actions">
                  <button
                    type="button"
                    className="sponsor-studies-visit-action-btn"
                    onClick={() => onEditVisit(visit)}
                  >
                    <Edit size={16} />
                  </button>
                  <button
                    type="button"
                    className="sponsor-studies-visit-action-btn delete"
                    onClick={() => onDeleteVisit(visit.uuid || "")}
                  >
                    <Trash2 size={16} />
                  </button>
                </div>
              </div>
            </div>
          ))}
        </div>
      ) : (
        <div className="sponsor-studies-visits-empty">
          <p>No visit templates available for this study.</p>
          <button className="sponsor-studies-add-visit-btn" onClick={onAddVisit}>
            <Plus size={16} /> Add First Visit Template
          </button>
        </div>
      )}
    </div>
  );
}

const SponsorStudiesSection = () => {
  const { data: currentUser } = useCurrentUserQuery();
  const { data: studies, isLoading: isLoadingStudies } = useStudiesBySponsorQuery(currentUser?.identifier || "");
  const [isCreating, setIsCreating] = useState(false);
  const [isEditing, setIsEditing] = useState(false);
  const [currentStudy, setCurrentStudy] = useState<Study | null>(null);
  const [isVisitModalOpen, setIsVisitModalOpen] = useState(false);
  const [currentVisit, setCurrentVisit] = useState<VisitTemplate | null>(null);
  const [showVisitsList, setShowVisitsList] = useState<{ [key: string]: boolean }>({});
  const [selectedStudyForVisit, setSelectedStudyForVisit] = useState<Study | null>(null);
  const [errorMessage, setErrorMessage] = useState<string | null>(null);
  const [fieldErrors, setFieldErrors] = useState<{ [key: string]: string[] }>({});
  const [successMessage, setSuccessMessage] = useState<string | null>(null);
  const [studyFormData, setStudyFormData] = useState<Partial<Study>>({
    name: "",
    description: "",
    full_title: "",
    team_email: "",
    leading_team: "",
    iras: "",
    tasks_data: [],
  });

  const queryClient = useQueryClient();
  const createStudyMutation = useCreateStudyMutation();
  const updateStudyMutation = useUpdateStudyMutation();
  const deleteStudyMutation = useDeleteStudyMutation();
  const createVisitTemplateMutation = useCreateVisitTemplateMutation();
  const updateVisitTemplateMutation = useUpdateVisitTemplateMutation();
  const deleteVisitTemplateMutation = useDeleteVisitTemplateMutation();

  // Handle form input changes
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setStudyFormData((prev) => ({ ...prev, [name]: value }));
  };

  // Handle task management
  const addTask = () => {
    const newTask: Task = {
      name: "",
      description: "",
    };
    setStudyFormData((prev) => ({
      ...prev,
      tasks_data: [...(prev.tasks_data || []), newTask],
    }));
  };

  const removeTask = (index: number) => {
    setStudyFormData((prev) => ({
      ...prev,
      tasks_data: prev.tasks_data?.filter((_, i) => i !== index) || [],
    }));
  };

  const updateTask = (index: number, field: keyof Task, value: any) => {
    setStudyFormData((prev) => ({
      ...prev,
      tasks_data: prev.tasks_data?.map((task, i) =>
        i === index ? { ...task, [field]: value } : task
      ) || [],
    }));
  };

  // Clear messages after timeout
  const clearMessages = () => {
    setTimeout(() => {
      setErrorMessage(null);
      setFieldErrors({});
      setSuccessMessage(null);
    }, 5000);
  };

  // Handle form submission
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    setErrorMessage(null);
    setFieldErrors({});
    setSuccessMessage(null);

    if (isEditing && currentStudy) {
      updateStudyMutation.mutate(
        { uuid: currentStudy.uuid, data: studyFormData },
        {
          onSuccess: () => {
            setIsEditing(false);
            setCurrentStudy(null);
            setStudyFormData({
              name: "",
              description: "",
              full_title: "",
              team_email: "",
              leading_team: "",
              iras: "",
              tasks_data: [],
            });
            setSuccessMessage("Study updated successfully!");
            clearMessages();
            // Invalidate and refetch studies query
            queryClient.invalidateQueries({ queryKey: ['studiesBySponsor', currentUser?.identifier] });
          },
          onError: (error: any) => {
            if (error?.response?.data) {
              const errorData = error.response.data;
              if (typeof errorData === 'object') {
                setFieldErrors(errorData);
              } else {
                setErrorMessage(errorData.message || "Failed to update study. Please try again.");
              }
            } else {
              setErrorMessage(error?.message || "Failed to update study. Please try again.");
            }
            clearMessages();
          },
        }
      );
    } else {
      createStudyMutation.mutate(studyFormData, {
        onSuccess: () => {
          setIsCreating(false);
          setStudyFormData({
            name: "",
            description: "",
            full_title: "",
            team_email: "",
            leading_team: "",
            iras: "",
            tasks_data: [],
          });
          setSuccessMessage("Study created successfully!");
          clearMessages();
          // Invalidate and refetch studies query
          queryClient.invalidateQueries({ queryKey: ['studiesBySponsor', currentUser?.identifier] });
        },
        onError: (error: any) => {
          if (error?.response?.data) {
            const errorData = error.response.data;
            if (typeof errorData === 'object') {
              setFieldErrors(errorData);
            } else {
              setErrorMessage(errorData.message || "Failed to create study. Please try again.");
            }
          } else {
            setErrorMessage(error?.message || "Failed to create study. Please try again.");
          }
          clearMessages();
        },
      });
    }
  };

  // Handle edit study
  const handleEditStudy = (study: Study) => {
    setCurrentStudy(study);
    setStudyFormData({
      name: study.name,
      description: study.description || "",
      full_title: study.full_title || "",
      team_email: study.team_email || "",
      leading_team: study.leading_team || "",
      iras: study.iras || "",
      tasks_data: study.tasks_data || [],
    });
    setIsEditing(true);
    setIsCreating(false);
  };

  // Handle delete study
  const handleDeleteStudy = (study: Study) => {
    if (window.confirm("Are you sure you want to delete this study?")) {
      deleteStudyMutation.mutate(study.uuid, {
        onSuccess: () => {
          // Invalidate and refetch studies query
          queryClient.invalidateQueries({ queryKey: ['studiesBySponsor', currentUser?.identifier] });
        },
      });
    }
  };

  // Handle manage visits - toggle visits list visibility
  const handleManageVisits = (study: Study) => {
    setShowVisitsList(prev => {
      // Create a new object with all visits closed
      const newState = Object.keys(prev).reduce((acc, key) => ({
        ...acc,
        [key]: false
      }), {});

      // Toggle the clicked study's visits
      return {
        ...newState,
        [study.uuid]: !prev[study.uuid]
      };
    });
    setSelectedStudyForVisit(study);
  };

  // Handle visit modal
  const handleOpenVisitModal = () => {
    if (!selectedStudyForVisit) return;
    setCurrentVisit(null);
    setIsVisitModalOpen(true);
  };

  const handleCloseVisitModal = () => {
    setIsVisitModalOpen(false);
    setCurrentVisit(null);
  };

  const handleSaveVisit = (visitData: Partial<VisitTemplate>) => {
    if (!selectedStudyForVisit) return;

    // Ensure study-related fields are set
    const enhancedVisitData = {
      ...visitData,
      study_uuid: selectedStudyForVisit.uuid,
    };

    if (currentVisit) {
      // Update existing visit template
      updateVisitTemplateMutation.mutate(
        {
          uuid: currentVisit.uuid || "",
          data: enhancedVisitData
        },
        {
          onSuccess: () => {
            handleCloseVisitModal();
            // Invalidate visit templates query to refetch data
            queryClient.invalidateQueries({
              queryKey: ['visitTemplatesByStudy', selectedStudyForVisit.uuid]
            });
          }
        }
      );
    } else {
      // Create new visit template
      createVisitTemplateMutation.mutate(
        enhancedVisitData,
        {
          onSuccess: () => {
            handleCloseVisitModal();
            // Invalidate visit templates query to refetch data
            queryClient.invalidateQueries({
              queryKey: ['visitTemplatesByStudy', selectedStudyForVisit.uuid]
            });
          }
        }
      );
    }
  };

  const handleDeleteVisit = (visitUuid: string) => {
    if (window.confirm("Are you sure you want to delete this visit template?")) {
      deleteVisitTemplateMutation.mutate(
        visitUuid,
        {
          onSuccess: () => {
            // The query invalidation is handled in the mutation hook
          }
        }
      );
    }
  };

  // Define columns for the DataTable
  const columns = [
    {
      key: "name" as keyof Study,
      header: "Study Name",
      sortable: true,
    },
    {
      key: "description" as keyof Study,
      header: "Description",
      sortable: true,
      render: (value: any) => {
        const description = value as string | undefined;
        return description
          ? description.length > 100
            ? `${description.substring(0, 100)}...`
            : description
          : "No description";
      },
    },
    {
      key: "full_title" as keyof Study,
      header: "Full Title",
      sortable: true,
      render: (value: any) => {
        const fullTitle = value as string | undefined;
        return fullTitle || "No full title";
      },
    },
    {
      key: "iras" as keyof Study,
      header: "IRAS",
      sortable: true,
      render: (value: any) => {
        const iras = value as string | undefined;
        return iras || "No IRAS";
      },
    },
    {
      key: "leading_team" as keyof Study,
      header: "Leading Team",
      sortable: true,
      render: (value: any) => {
        const leadingTeam = value as string | undefined;
        return leadingTeam || "No leading team";
      },
    },
  ];

  // Define actions for the DataTable
  const actions = [
    {
      icon: <Calendar size={16} />,
      tooltipText: "Manage Visits",
      onClick: (row: Study) => handleManageVisits(row),
    },
    {
      icon: <Edit size={16} />,
      tooltipText: "Edit Study",
      onClick: (row: Study) => handleEditStudy(row),
    },
    {
      icon: <Trash2 size={16} />,
      tooltipText: "Delete Study",
      onClick: (row: Study) => handleDeleteStudy(row),
    },
  ];

  return (
    <Wrapper>
      <Preloader />
      <div className="sponsor-studies-container">
        <div className="sponsor-studies-header">
          <div className="sponsor-studies-header-content">
            <h1 className="sponsor-studies-title">
              <BarChart3 size={24} />
              Studies
            </h1>
            <button
              className="sponsor-studies-create-btn"
              onClick={() => setIsCreating(true)}
              disabled={createStudyMutation.isPending}
            >
              {createStudyMutation.isPending ? (
                <Loader2 size={16} />
              ) : (
                <Plus size={16} />
              )}
              Create New Study
            </button>
          </div>
        </div>

        {(isCreating || isEditing) && (
          <div className="sponsor-studies-form-section">
            <h2 className="sponsor-studies-form-title">
              {isEditing ? (
                <><FileEdit size={20} /> Edit Study</>
              ) : (
                <><FileText size={20} /> Create New Study</>
              )}
            </h2>

            {/* Error Messages */}
            {errorMessage && (
              <div className="sponsor-studies-alert sponsor-studies-alert-error">
                <AlertCircle size={16} />
                {errorMessage}
              </div>
            )}

            {/* Field-specific Error Messages */}
            {Object.entries(fieldErrors).map(([field, errors]) => (
              <div key={field} className="sponsor-studies-alert sponsor-studies-alert-error">
                <AlertCircle size={16} />
                {errors.map((error, index) => (
                  <div key={index}>{error}</div>
                ))}
              </div>
            ))}

            {/* Success Message */}
            {successMessage && (
              <div className="sponsor-studies-alert sponsor-studies-alert-success">
                <CheckCircle size={16} />
                {successMessage}
              </div>
            )}

            <form className="sponsor-studies-form" onSubmit={handleSubmit}>
              <div className="sponsor-studies-form-row">
                <div className="sponsor-studies-form-group">
                  <label className="sponsor-studies-label" htmlFor="name">
                    Study Name <span className="sponsor-studies-required">*</span>
                  </label>
                  <NurtifyInput
                    type="text"
                    id="name"
                    name="name"
                    value={studyFormData.name}
                    onChange={handleInputChange}
                    required
                  />
                </div>
                <div className="sponsor-studies-form-group">
                  <label className="sponsor-studies-label" htmlFor="iras">
                    IRAS Number <span className="sponsor-studies-required">*</span>
                  </label>
                  <NurtifyInput
                    type="text"
                    id="iras"
                    name="iras"
                    value={studyFormData.iras}
                    onChange={handleInputChange}
                    required
                  />
                </div>
              </div>

              <div className="sponsor-studies-form-group full-width">
                <label className="sponsor-studies-label" htmlFor="description">
                  Description
                </label>
                <NurtifyTextArea
                  id="description"
                  name="description"
                  value={studyFormData.description}
                  onChange={handleInputChange}
                  rows={3}
                />
              </div>

              <div className="sponsor-studies-form-group full-width">
                <label className="sponsor-studies-label" htmlFor="full_title">
                  Full Title
                </label>
                <NurtifyInput
                  type="text"
                  id="full_title"
                  name="full_title"
                  value={studyFormData.full_title}
                  onChange={handleInputChange}
                />
              </div>

              <div className="sponsor-studies-form-row">
                <div className="sponsor-studies-form-group">
                  <label className="sponsor-studies-label" htmlFor="team_email">
                    Team Email
                  </label>
                  <NurtifyInput
                    type="email"
                    id="team_email"
                    name="team_email"
                    value={studyFormData.team_email}
                    onChange={handleInputChange}
                  />
                </div>
                <div className="sponsor-studies-form-group">
                  <label className="sponsor-studies-label" htmlFor="leading_team">
                    Leading Team
                  </label>
                  <NurtifyInput
                    type="text"
                    id="leading_team"
                    name="leading_team"
                    value={studyFormData.leading_team}
                    onChange={handleInputChange}
                  />
                </div>
              </div>

              {/* Tasks Section */}
              <div className="sponsor-studies-form-group full-width">
                <div className="sponsor-studies-tasks-section">
                  <div className="sponsor-studies-tasks-header">
                    <label className="sponsor-studies-label">
                      Study Tasks
                    </label>
                    <button
                      type="button"
                      className="sponsor-studies-add-task-btn"
                      onClick={addTask}
                    >
                      <Plus size={16} /> Add Task
                    </button>
                  </div>
                  
                  {studyFormData.tasks_data && studyFormData.tasks_data.length > 0 ? (
                    <div className="sponsor-studies-tasks-list">
                      {studyFormData.tasks_data.map((task, index) => (
                        <div key={index} className="sponsor-studies-task-item">
                          <div className="sponsor-studies-task-header">
                            <h4 className="sponsor-studies-task-title">Task {index + 1}</h4>
                            <button
                              type="button"
                              className="sponsor-studies-remove-task-btn"
                              onClick={() => removeTask(index)}
                            >
                              <X size={16} />
                            </button>
                          </div>
                          
                          <div className="sponsor-studies-task-fields">
                            <div className="sponsor-studies-form-group">
                              <label className="sponsor-studies-label">
                                Task Name <span className="sponsor-studies-required">*</span>
                              </label>
                              <NurtifyInput
                                type="text"
                                value={task.name}
                                onChange={(e) => updateTask(index, 'name', e.target.value)}
                                required
                              />
                            </div>
                            
                            <div className="sponsor-studies-form-group">
                              <label className="sponsor-studies-label">
                                Description
                              </label>
                              <NurtifyTextArea
                                value={task.description}
                                onChange={(e) => updateTask(index, 'description', e.target.value)}
                                rows={2}
                              />
                            </div>
                          </div>
                        </div>
                      ))}
                    </div>
                  ) : (
                    <div className="sponsor-studies-no-tasks">
                      <p>No tasks added yet. Click "Add Task" to get started.</p>
                    </div>
                  )}
                </div>
              </div>

              <div className="sponsor-studies-form-actions">
                <button
                  type="button"
                  className="sponsor-studies-cancel-btn"
                  onClick={() => {
                    setIsCreating(false);
                    setIsEditing(false);
                    setCurrentStudy(null);
                    setStudyFormData({
                      name: "",
                      description: "",
                      full_title: "",
                      team_email: "",
                      leading_team: "",
                      iras: "",
                      tasks_data: [],
                    });
                  }}
                >
                  Cancel
                </button>
                <button
                  type="submit"
                  className="sponsor-studies-submit-btn"
                  disabled={createStudyMutation.isPending || updateStudyMutation.isPending}
                >
                  {(createStudyMutation.isPending || updateStudyMutation.isPending) ? (
                    <Loader2 size={16} />
                  ) : isEditing ? (
                    <CheckCircle size={16} />
                  ) : (
                    <Plus size={16} />
                  )}
                  {isEditing ? "Update Study" : "Create Study"}
                </button>
              </div>
            </form>
          </div>
        )}

        {isLoadingStudies ? (
          <div className="sponsor-studies-loading">
            <Loader2 size={32} />
            Loading studies...
          </div>
        ) : studies && studies.length > 0 ? (
          <div className="sponsor-studies-table-container">
            <DataTable
              data={studies}
              columns={columns}
              actions={actions}
              defaultItemsPerPage={10}
              itemsPerPageOptions={[5, 10, 25, 50]}
              noDataMessage="No studies available"
            />
          </div>
        ) : (
          <div className="sponsor-studies-empty-state">
            <ClipboardList size={48} />
            No studies available.
          </div>
        )}

        {/* Visits List for each study */}
        {studies && studies.map((study: Study) =>
          showVisitsList[study.uuid] && (
            <VisitsListSection
              key={study.uuid}
              study={study}
              onAddVisit={handleOpenVisitModal}
              onEditVisit={(visit) => {
                setCurrentVisit(visit);
                setIsVisitModalOpen(true);
              }}
              onDeleteVisit={handleDeleteVisit}
            />
          )
        )}
      </div>

      {/* Visit Modal */}
      <VisitModal
        isOpen={isVisitModalOpen}
        onClose={handleCloseVisitModal}
        onSave={handleSaveVisit}
        initialData={currentVisit || { study_uuid: selectedStudyForVisit?.uuid }}
        availableTests={availableTests}
        isEditing={!!currentVisit}
      />
    </Wrapper>
  );
}

export default SponsorStudiesSection;

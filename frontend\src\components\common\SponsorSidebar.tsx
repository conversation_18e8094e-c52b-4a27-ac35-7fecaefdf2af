import React, { useState, useEffect, useCallback } from 'react';
import {
  ChevronLeft,
  ChevronRight,
  Bell,
//   MessageSquare,
  Send,
  BookOpen,
  ChartPie,
  LogOut,
  User,
  Users,
  Package,
  FileText,
  Siren,
  OctagonAlert,
  SquareUser,
  FileStack,
  FilePlus,
} from 'lucide-react';
import './PatientSidebar.css';
import { useCurrentUserQuery } from "@/hooks/user.query";
import { useKeycloak } from "@react-keycloak/web";
import { useQueryClient } from "@tanstack/react-query";

type SponsorSidebarProps = {
  onTabChange?: (tab: string) => void;
  currentTab?: string;
  notificationCount?: number;
};

const SponsorSidebar: React.FC<SponsorSidebarProps> = ({
  onTabChange,
  currentTab = 'report',
  notificationCount = 0
}) => {
  const { keycloak } = useKeycloak();
  const queryClient = useQueryClient();
  // State for collapsed sidebar
  const { data: currentUser } = useCurrentUserQuery();
  const [isCollapsed, setIsCollapsed] = useState(() => {
    const stored = localStorage.getItem('patientSidebarCollapsed') === 'true';
    return stored || window.innerWidth <= 991;
  });

  const [activeTab, setActiveTab] = useState(currentTab);
  const [isMobile, setIsMobile] = useState(window.innerWidth <= 991);

  const handleResize = useCallback(() => {
    setIsMobile(window.innerWidth <= 991);
  }, []);

  useEffect(() => {
    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, [handleResize]);

  useEffect(() => {
    localStorage.setItem('patientSidebarCollapsed', isCollapsed.toString());
  }, [isCollapsed]);

  useEffect(() => {
    if (currentTab) {
      setActiveTab(currentTab);
    }
  }, [currentTab]);

  const toggleSidebar = () => {
    setIsCollapsed(!isCollapsed);
  };

  const handleTabChange = (tab: string) => {
    setActiveTab(tab);
    if (onTabChange) {
      onTabChange(tab);
    }
  };


  const handleLogout = () => {
    const redirectUri = `${window.location.origin}/`;
    queryClient.clear();
    keycloak.logout({ redirectUri: redirectUri });
  };


  return (
    <div
      className={`patient-sidebar ${isCollapsed ? 'collapsed' : ''} ${isMobile ? 'mobile' : ''}`}
      role="navigation"
      aria-label="Patient navigation"
    >
      <div className="patient-toggle-button" onClick={toggleSidebar}>
        {isCollapsed ? <ChevronRight size={20} /> : <ChevronLeft size={20} />}
      </div>

      {isCollapsed ? (
        // Collapsed view - only icons
        <div className="patient-icons-container">
          <div className="user-avatar-collapsed">
            <User size={24} />
          </div>
          <div
            className={`patient-icon ${activeTab === '' ? 'active' : ''}`}
            onClick={() => handleTabChange('')}
          >
            <ChartPie size={20} />
          </div>

          <div
            className={`patient-icon ${activeTab === 'studies' ? 'active' : ''}`}
            onClick={() => handleTabChange('studies')}
          >
            <BookOpen size={20} />
          </div>

          <div
            className={`patient-icon ${activeTab === 'invitations' ? 'active' : ''}`}
            onClick={() => handleTabChange('invitations')}
          >
            <Send size={20} />
          </div>

          <div
            className={`patient-icon ${activeTab === 'study-patients' ? 'active' : ''}`}
            onClick={() => handleTabChange('study-patients')}
          >
            <Users size={20} />
          </div>

          <div
            className={`patient-icon ${activeTab === 'consents' ? 'active' : ''}`}
            onClick={() => handleTabChange('consents')}
          >
            <FileText size={20} />
          </div>

          <div
            className={`patient-icon ${activeTab === 'all-stuff' ? 'active' : ''}`}
            onClick={() => handleTabChange('all-stuff')}
          >
            <Package size={20} />
          </div>

          <div
            className={`patient-icon ${activeTab === 'activities' ? 'active' : ''}`}
            onClick={() => handleTabChange('activities')}
          >
            <Bell size={20} />
            {notificationCount > 0 && <span className="notification-badge">{notificationCount}</span>}
          </div>

          <div
            className={`patient-icon ${activeTab === 'open-query' ? 'active' : ''}`}
            onClick={() => handleTabChange('open-query')}
          >
            <OctagonAlert size={20} />
          </div>

          <div
            className={`patient-icon ${activeTab === 'policy' ? 'active' : ''}`}
            onClick={() => handleTabChange('policy')}
          >
            <FileStack size={20} />
          </div>

          <div
            className={`patient-icon ${activeTab === 'addpolicy' ? 'active' : ''}`}
            onClick={() => handleTabChange('addpolicy')}
          >
            <FilePlus size={20} />
          </div>

          <div
            className={`patient-icon ${activeTab === 'forms' ? 'active' : ''}`}
            onClick={() => handleTabChange('forms')}
          >
            <FileText size={20} />
          </div>

          <div className="patient-icon logout" onClick={handleLogout}>
            <LogOut size={20} />
          </div>
        </div>
      ) : (
        // Expanded view - full content
        <div className="patient-content-container">
          <div className="user-avatar-expanded">
            <div className="avatar-circle">
              <User size={24} />
            </div>
            <div className="user-info">
              <div className="user-name">{currentUser?.organization_name}</div>
            </div>
          </div>


          <div
            className={`patient-menu-item ${activeTab === '' ? 'active' : ''}`}
            onClick={() => handleTabChange('')}
          >
            <ChartPie size={18} />
            <span>Dashboard</span>
          </div>
          <div
            className={`patient-menu-item ${activeTab === 'studies' ? 'active' : ''}`}
            onClick={() => handleTabChange('studies')}
          >
            <BookOpen size={18} />
            <span>My Studies</span>
          </div>

          <div
            className={`patient-menu-item ${activeTab === 'invitations' ? 'active' : ''}`}
            onClick={() => handleTabChange('invitations')}
          >
            <Send size={18} />
            <span>My Invitations Studies</span>
          </div>

          <div
            className={`patient-menu-item ${activeTab === 'study-patients' ? 'active' : ''}`}
            onClick={() => handleTabChange('study-patients')}
          >
            <SquareUser size={18} />

            <span>Study Patients</span>
          </div>

          <div
            className={`patient-menu-item ${activeTab === 'consents' ? 'active' : ''}`}
            onClick={() => handleTabChange('consents')}
          >
            <FileText size={18} />
            <span>Consents</span>
          </div>

          <div
            className={`patient-menu-item ${activeTab === 'all-stuff' ? 'active' : ''}`}
            onClick={() => handleTabChange('all-stuff')}
          >
            <Users size={18} />
            <span>All Stuff</span>
          </div>


          <div
            className={`patient-menu-item ${activeTab === 'activities' ? 'active' : ''}`}
            onClick={() => handleTabChange('activities')}
          >
            <div className="menu-icon-wrapper">
              <Siren size={18} />
              {notificationCount > 0 && <span className="notification-badge">{notificationCount}</span>}
            </div>
            <span>Adverse Event</span>
          </div>
          <div
            className={`patient-menu-item ${activeTab === 'open-query' ? 'active' : ''}`}
            onClick={() => handleTabChange('open-query')}
          >
            <div className="menu-icon-wrapper">
            <OctagonAlert size={18} />
              {notificationCount > 0 && <span className="notification-badge">{notificationCount}</span>}
            </div>
            <span>Open query</span>
          </div>
          <div
            className={`patient-menu-item ${activeTab === 'policy' ? 'active' : ''}`}
            onClick={() => handleTabChange('policy')}
          >
            <FileStack size={18}/>
            <span>Policies</span>
          </div>
          <div
            className={`patient-menu-item ${activeTab === 'addpolicy' ? 'active' : ''}`}
            onClick={() => handleTabChange('addpolicy')}
          >
            <FilePlus size={18}/>
            <span>Add Policy</span>
          </div>

          <div
            className={`patient-menu-item ${activeTab === 'forms' ? 'active' : ''}`}
            onClick={() => handleTabChange('forms')}
          >
            <FileText size={18}/>
            <span>Forms</span>
          </div>

          <div className="patient-menu-item logout" onClick={handleLogout}>
            <LogOut size={18} />
            <span>Log Out</span>
          </div>
        </div>
      )}
    </div>
  );
};

export default SponsorSidebar;

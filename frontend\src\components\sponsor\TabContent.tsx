
import { motion } from "framer-motion";

import "./TabContent.css";

interface TabContentProps {
  activeTab: string;
}

const TabContent: React.FC<TabContentProps> = ({ activeTab }) => {
  // State for study search
 

  
  // Render content based on active tab
  switch (activeTab) {
    case 'studies':
      return (
        <motion.div
          className="patclin-tab-content"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ duration: 0.5 }}
        >
          <h2 className="patclin-section-title">My Studies</h2>
          <div className="patclin-empty-state">
            <p>You are not enrolled in any studies at this time.</p>
          </div>
        </motion.div>
      );
    
    default:
      return null;
  }
};

export default TabContent;

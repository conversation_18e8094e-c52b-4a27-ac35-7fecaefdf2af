import MobileFooter from "./MobileFooter";
import { Link } from "react-router-dom";
import { useEffect, useState, FC, useMemo } from "react";
import { useLocation } from "react-router-dom";
import { X } from "lucide-react";
//import { useCurrentUserQuery } from "@/hooks/user.query";


interface MenuItem {
  title: string;
  disabled?: boolean;
  links?: {
    title: string;
    href: string;
    links?: {
      title: string;
      href: string;
    }[];
  }[];
}

interface MobileMenuProps {
  setActiveMobileMenu: (active: boolean) => void;
  activeMobileMenu: boolean;
}

const MobileMenu: FC<MobileMenuProps> = ({ setActiveMobileMenu, activeMobileMenu }) => {
//const { data: currentUser } = useCurrentUserQuery();
const menuList: MenuItem[] = useMemo(() => [
    { title: "Home" },
    ...(import.meta.env.VITE_APP_MODE === "development" ? [{ title: "Ressources" }] : []),
    { title: "Blog" },
    { title: "Contact Us" },
    { title: "Dashboard" },
    { title: "Login" },
    { title: "Logout" },
  ], []);

  const [showMenu, setShowMenu] = useState<boolean>(false);
  const [, setMenuNesting] = useState<string[]>([]);
  const [menuItem, setMenuItem] = useState<string>("");
  const [, setSubmenu] = useState<string>("");
  const { pathname } = useLocation();

  const handleMobileMenuItemClick = (item: MenuItem): void => {
    if (item.disabled) {
      return; // Do nothing for disabled items
    }
    
    const basePath = "/org/dashboard";
    const title = item.title;
    
    setActiveMobileMenu(false); // Close mobile menu after click
    
    // Use direct navigation with window.location for a more reliable approach
    setTimeout(() => {
      if (title === "Dashboard") {
        window.location.href = basePath;
      } else if (title === "Logout") {
        // For logout, use full page refresh to ensure clean state
        localStorage.removeItem("authToken");
        window.location.href = "/login";
      } else if (title === "Login") {
        window.location.href = "/login";
      } else if (title === "Ressources") {
        window.location.href = "/sdk";
      } else if (title === "Blog") {
        window.location.href = "/blog";
      } else if (title === "Contact Us") {
        window.location.href = "/contact-us";
      }  else if (title === "About Us") {
        window.location.href = "/about-us";
      } else if (title === "Home") {
        window.location.href = "/home";
      }
    }, 10);
  };

  useEffect(() => {
    menuList.forEach((elm) => {
      elm.links?.forEach((elm2) => {
        if (elm2.href?.split("/")[1] === pathname?.split("/")[1]) {
          setMenuItem(elm.title);
        } else {
          elm2.links?.forEach((elm3) => {
            if (elm3.href?.split("/")[1] === pathname?.split("/")[1]) {
              setMenuItem(elm.title);
              setSubmenu(elm2.title);
            }
          });
        }
      });
    });
  }, [pathname, menuList]);

  useEffect(() => {
    setShowMenu(true);
  }, []);

  return (
    <div
      className={`header-menu js-mobile-menu-toggle ${
        activeMobileMenu ? "-is-el-visible" : ""
      }`}
    >
      <div className="header-menu__content">
        <div className="mobile-bg js-mobile-bg"></div>

        <div className="d-none xl:d-flex items-center px-20 py-20 border-bottom-light">
          <Link
            to="/login"
            className={`button text-white btn-nurtify mr-10 px-4 py-2 ${
              pathname === "/login" ? "" : ""
            } `}
          >
            Log in
          </Link>
          <Link
            to="/login"
            className={`button btn-nurtify-light px-4 py-2 ${
              pathname === "/login" ? "" : ""
            } `}
          >
            Sign Up
          </Link>
        </div>

        {showMenu && activeMobileMenu && (
          <div className="mobileMenu text-dark-1" style={{ padding: '20px 0' }}>
            {menuList.map((elm, i) => {
              if (elm.title) {
                return (
                  <div
                    key={i}
                    className="submenuOne"
                    onClick={() => handleMobileMenuItemClick(elm)}
                    style={{
                      padding: '0 24px',
                      cursor: 'pointer',
                      borderBottom: '1px solid #f0f0f0',
                      transition: 'background-color 0.2s ease'
                    }}
                    onMouseEnter={(e) => {
                      if (!elm.disabled) {
                        e.currentTarget.style.backgroundColor = '#f8f9fa';
                      }
                    }}
                    onMouseLeave={(e) => {
                      e.currentTarget.style.backgroundColor = 'transparent';
                    }}
                  >
                    <div
                      className="title"
                      onClick={() =>
                        setMenuNesting((pre) =>
                          pre[0] === elm.title ? [] : [elm.title]
                        )
                      }
                    >
                      <span
                        className={
                          elm.title === menuItem ? "activeMenu" : "inActiveMenu"
                        }
                        style={elm.disabled ? {
                          opacity: 0.5,
                          cursor: 'not-allowed',
                          textDecoration: 'none',
                          color: '#666666',
                          fontWeight: '400',
                          fontSize: '16px',
                          padding: '16px 0',
                          display: 'block'
                        } : elm.title === menuItem ? {
                          color: '#37B7C3',
                          fontWeight: '600',
                          fontSize: '16px',
                          padding: '16px 0',
                          display: 'block'
                        } : {
                          color: '#333333',
                          fontWeight: '500',
                          fontSize: '16px',
                          padding: '16px 0',
                          display: 'block'
                        }}
                      >
                        {elm.title}
                      </span>
                    </div>
                  </div>
                );
              }
              return null;
            })}
          </div>
        )}

        {/* mobile footer start */}
        <MobileFooter />
        {/* mobile footer end */}
      </div>

      <div
        className="header-menu-close"
        onClick={() => {
          setActiveMobileMenu(false);
        }}
        data-el-toggle=".js-mobile-menu-toggle"
      >
        <div className="size-40 d-flex items-center justify-center rounded-full bg-white">
        <X size={20} style={{color: "var(--color-dark-1)", strokeWidth: 2, fill: "var(--color-dark-1)", backgroundColor: "transparent"}} />
        </div>
      </div>

      <div
        className="header-menu-bg"
        onClick={() => setActiveMobileMenu(false)}
      ></div>
    </div>
  );
};

export default MobileMenu;

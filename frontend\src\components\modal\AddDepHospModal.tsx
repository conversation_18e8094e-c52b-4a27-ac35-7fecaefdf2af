import React from 'react';
import "@/components/modal/AddDepartmentModal.css";

type AddDepHospModalProps = {
  type: string;
  isOpen: boolean;
  onYes: () => void;
  onNo: () => void;
};

const AddDepHospModal: React.FC<AddDepHospModalProps> = ({ type, isOpen, onNo, onYes }) => {  

  if (!isOpen) return null;

  return (
    <div className="modal-overlay">
      <div className="modal-content">
        <div className="modal-header">
          <h2>{type} created successfully!</h2>
        </div>
        <div className="modal-body text-sm-start">
          <p className="m-y-5">Would you like to assign admin now?</p>
        </div>
        <div className="modal-footer">
          <button className="delete-modal-btn btn-secondary" onClick={onNo}>No</button>
          <button className="delete-modal-btn btn-danger" onClick={onYes}>Yes</button>
        </div>
      </div>
    </div>
  );
};

export default AddDepHospModal;

import api from "@/services/api.ts";

export interface InvolvedStudy {
  study_uuid: string;
  study_name: string;
  iras: string;
}

export interface SponsorStaff {
  staff_uuid: string;
  full_name: string;
  role: string;
  associated_hospital: string;
  department_name: string;
  email: string;
  phone_number: string;
  involved_studies: InvolvedStudy[];
  is_admin: boolean;
  is_hospital_admin: boolean;
}

export interface SponsorStaffResponse {
  count: number;
  next: string | null;
  previous: string | null;
  results: SponsorStaff[];
}

export interface SponsorStaffFilters {
  hospital?: string;
  department?: string;
  role?: string;
  search?: string;
  page?: number;
  page_size?: number;
}

export const getSponsorStaff = async (filters?: SponsorStaffFilters) => {
  try {
    const params = new URLSearchParams();
    
    if (filters?.hospital) {
      params.append('hospital', filters.hospital);
    }
    if (filters?.department) {
      params.append('department', filters.department);
    }
    if (filters?.role) {
      params.append('role', filters.role);
    }
    if (filters?.search) {
      params.append('search', filters.search);
    }
    if (filters?.page) {
      params.append('page', filters.page.toString());
    }
    if (filters?.page_size) {
      params.append('page_size', filters.page_size.toString());
    }

    const response = await api.get<SponsorStaffResponse>(`study/studies/sponsor-staff/${params.toString() ? `?${params.toString()}` : ''}`);
    return response.data;
  } catch (error) {
    console.error("Error fetching sponsor staff:", error);
    throw error;
  }
}; 
import React from 'react';
import { Edit, Trash } from 'lucide-react';

type TableProps = {
  headers: string[];
  data: { [key: string]: string | JSX.Element }[];
  tableIndex: number; // Add tableIndex prop to differentiate tables
};

const Table: React.FC<TableProps> = ({ headers, data }) => { // Removed tableIndex as it's no longer used for color
  // Always use the Nurtify brand color for the header background
  const headerBackgroundColor = '#37B7C3';

  return (
    <div className="table-wrapper" style={{ maxHeight: '200px', overflowY: 'auto' }}>
      <table className="custom-table">
        <thead>
          <tr>
            {headers.map((header, index) => (
              // Apply the consistent Nurtify background color
              <th key={index} style={{ backgroundColor: headerBackgroundColor, color: 'white', width: index === 0 ? '8%' : index === headers.length - 1 ? 'auto' : '15%' }}>
                {header}
              </th>
            ))}
          </tr>
        </thead>
        <tbody>
          {data.map((row, rowIndex) => (
            <tr key={rowIndex}>
              {headers.map((header, colIndex) => (
                <td key={colIndex}>
                  {header === 'action' ? (
                    <div style={{ display: 'flex', justifyContent: 'space-around', gap: '10px' }}>
                      <button className="table-button edit-button">
                        <span>Edit</span>
                        <Edit size={16} />
                      </button>
                      <button className="table-button delete-button">
                        <span>Delete</span>
                        <Trash size={16} />
                      </button>
                    </div>
                  ) : (
                    row[header]
                  )}
                </td>
              ))}
            </tr>
          ))}
        </tbody>
      </table>
    </div>
  );
};

export default Table;

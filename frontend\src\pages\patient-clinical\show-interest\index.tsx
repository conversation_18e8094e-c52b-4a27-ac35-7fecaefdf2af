import React, { useState } from "react";
import { motion } from "framer-motion";
import BporStudyWidget from "@/components/BporStudyWidget";
import "../../../components/patient/TabContent.css";

const ShowInterestSection: React.FC = () => {
  // State for study search
  const [studyQuery, setStudyQuery] = useState("");
  const [studyDistance, setStudyDistance] = useState("20");
  const [studyLocation, setStudyLocation] = useState("London");
  const [searchParams, setSearchParams] = useState({
    query: "",
    distance: "20",
    location: "London"
  });

  // Handle study search
  const handleStudySearch = (e: React.FormEvent) => {
    e.preventDefault();
    setSearchParams({
      query: studyQuery,
      distance: studyDistance,
      location: studyLocation
    });
  };

  return (
    <motion.div
      className="patclin-tab-content"
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      transition={{ duration: 0.5 }}
    >
      <h2 className="patclin-section-title">Show Interest in Studies</h2>
      
      <form className="patclin-search-form" onSubmit={handleStudySearch}>
        <div className="form-row">
          <div className="form-group">
            <label htmlFor="study-query">Type of Research</label>
            <input 
              id="study-query"
              type="text" 
              value={studyQuery}
              onChange={(e) => setStudyQuery(e.target.value)}
              placeholder="e.g. Diabetes, Cancer, etc." 
            />
          </div>
          <div className="form-group">
            <label htmlFor="study-distance">Distance (miles)</label>
            <input 
              id="study-distance"
              type="number" 
              min="0"
              value={studyDistance}
              onChange={(e) => setStudyDistance(e.target.value)}
              placeholder="20" 
            />
          </div>
          <div className="form-group">
            <label htmlFor="study-location">Location</label>
            <input 
              id="study-location"
              type="text" 
              value={studyLocation}
              onChange={(e) => setStudyLocation(e.target.value)}
              placeholder="London" 
            />
          </div>
        </div>
        <button type="submit" className="patclin-search-button">
          Search Studies
        </button>
      </form>

      <div className="patclin-widget-container">
        {/* <img src="https://unpkg.com/bpor-dev-resource@0.28.0/dist/bpor-widget/Assets/DesktopLogo.png" alt="Show Interest" /> */}
        <BporStudyWidget 
          distance={searchParams.distance} 
          query={searchParams.query} 
          location={searchParams.location} 
        />
      </div>
    </motion.div>
  );
};

export default ShowInterestSection;

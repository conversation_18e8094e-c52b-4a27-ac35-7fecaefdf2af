import { useState, useEffect } from "react";
import "./NurtifySwitch.css";

interface NurtifySwitchProps {
    name: string;
    value: string;
    onChange: (value: string) => void;
    label?: string;
}

const NurtifySwitch: React.FC<NurtifySwitchProps> = ({ name, value, onChange, label }) => {
    const [checked, setChecked] = useState(value === "true");

    useEffect(() => {
        setChecked(value === "true");
    }, [value]);

    const handleToggle = () => {
        const newValue = !checked;
        setChecked(newValue);
        if (onChange) {
            onChange(newValue.toString());
        }
    };

    return (
        <div className="nurtify-switch-wrapper">
            {label && <span className="nurtify-switch-label">{label}</span>}
            <div className="nurtify-switch" onClick={handleToggle}>
                <div className={`nurtify-switch-slider ${checked ? "checked" : ""}`}>
                    <div className="nurtify-switch-circle"></div>
                  
                </div>
                <input
                    type="checkbox"
                    id={name}
                    name={name}
                    checked={checked}
                    onChange={handleToggle}
                    style={{ display: "none" }}
                />
            </div>
        </div>
    );
};

export default NurtifySwitch;

/* System Notification Bell Styles */
.system-notification-bell {
  position: relative;
}

.system-notification-bell .menu-icon-wrapper {
  position: relative;
  cursor: pointer;
  margin: 0 10px;
  padding: 8px;
  border-radius: 50%;
  transition: background-color 0.2s ease;
}

.system-notification-bell .menu-icon-wrapper:hover {
  background-color: rgba(55, 183, 195, 0.1);
}

.system-notification-bell .notification-badge {
  position: absolute;
  top: -5px;
  right: -5px;
  background-color: #ff6b6b;
  color: white;
  border-radius: 50%;
  width: 18px;
  height: 18px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 10px;
  font-weight: bold;
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.1);
  }
  100% {
    transform: scale(1);
  }
}

.system-notification-bell .notification-dropdown {
  position: absolute;
  top: 100%;
  right: 0;
  background: white;
  border-radius: 8px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
  width: 350px;
  max-width: 90vw;
  z-index: 1000;
  border: 1px solid #e0e0e0;
  animation: fadeInDropdown 0.2s ease-out;
}

@keyframes fadeInDropdown {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.system-notification-bell .notification-dropdown-header {
  font-weight: 600;
  font-size: 16px;
  padding: 12px 16px;
  border-bottom: 1px solid #f0f0f0;
  background: #fff;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-radius: 8px 8px 0 0;
}

.system-notification-bell .notification-list {
  max-height: 400px;
  overflow-y: auto;
}

.system-notification-bell .notification-item {
  padding: 12px 16px;
  border-bottom: 1px solid #f0f0f0;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.system-notification-bell .notification-item:hover {
  background-color: #f8f9fa;
}

.system-notification-bell .notification-item.unread {
  background-color: #f8f9fa;
  border-left: 3px solid #37B7C3;
}

.system-notification-bell .notification-item:last-child {
  border-bottom: none;
}

.system-notification-bell .notification-content {
  width: 100%;
}

.system-notification-bell .notification-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 4px;
}

.system-notification-bell .notification-title {
  font-weight: 600;
  font-size: 14px;
  color: #333;
  flex: 1;
  margin-right: 8px;
  line-height: 1.3;
}

.system-notification-bell .notification-item:not(.unread) .notification-title {
  font-weight: 500;
}

.system-notification-bell .notification-priority {
  font-size: 10px;
  padding: 2px 6px;
  border-radius: 10px;
  color: white;
  text-transform: uppercase;
  font-weight: 600;
  white-space: nowrap;
}

.system-notification-bell .notification-message {
  font-size: 13px;
  color: #666;
  margin-bottom: 6px;
  line-height: 1.4;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.system-notification-bell .notification-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 11px;
  color: #999;
}

.system-notification-bell .notification-type {
  font-style: italic;
}

.system-notification-bell .notification-time {
  font-weight: 500;
}

.system-notification-bell .no-notifications {
  padding: 20px;
  text-align: center;
  color: #666;
  font-size: 14px;
}

.system-notification-bell .mark-all-read {
  background: none;
  border: none;
  color: #37B7C3;
  font-size: 12px;
  cursor: pointer;
  font-weight: 500;
  transition: color 0.2s ease;
}

.system-notification-bell .mark-all-read:hover {
  color: #088395;
}

/* Mobile responsive */
@media (max-width: 768px) {
  .system-notification-bell .notification-dropdown {
    width: 300px;
    right: -50px;
  }
  
  .system-notification-bell .notification-title {
    font-size: 13px;
  }
  
  .system-notification-bell .notification-message {
    font-size: 12px;
  }
}

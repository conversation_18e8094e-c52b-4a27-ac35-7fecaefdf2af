/* Staff Documents Modal - Enhanced UI/UX following project design system */

/* Modal Overlay with backdrop blur and smooth animations */
.staff-documents-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.6);
  backdrop-filter: blur(8px);
  -webkit-backdrop-filter: blur(8px);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
  padding: 20px;
  animation: fadeInOverlay 0.3s cubic-bezier(0.165, 0.84, 0.44, 1);
}

@keyframes fadeInOverlay {
  from {
    opacity: 0;
    backdrop-filter: blur(0px);
    -webkit-backdrop-filter: blur(0px);
  }
  to {
    opacity: 1;
    backdrop-filter: blur(8px);
    -webkit-backdrop-filter: blur(8px);
  }
}

@keyframes slideUpModal {
  from {
    opacity: 0;
    transform: translateY(30px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

/* Main Modal Container */
.staff-documents-modal {
  background: var(--color-white);
  border-radius: 16px;
  box-shadow: 
    0 25px 50px -12px rgba(0, 0, 0, 0.25),
    0 0 0 1px rgba(255, 255, 255, 0.05);
  width: 100%;
  max-width: 900px;
  max-height: 85vh;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  animation: slideUpModal 0.4s cubic-bezier(0.165, 0.84, 0.44, 1);
  font-family: var(--font-primary);
}

/* Modal Header */
.staff-documents-modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 32px 40px 24px;
  border-bottom: 1px solid var(--color-light-2);
  background: linear-gradient(135deg, var(--color-light-6) 0%, var(--color-white) 100%);
  position: relative;
}

.staff-documents-modal-header::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, var(--color-purple-1) 0%, var(--color-blue-1) 100%);
}

.staff-documents-modal-header h2 {
  margin: 0;
  font-size: var(--text-24);
  font-weight: 700;
  color: var(--color-dark-1);
  display: flex;
  align-items: center;
  gap: 12px;
}

.staff-documents-modal-header h2::before {
  content: '📄';
  font-size: var(--text-20);
}

/* Close Button */
.staff-documents-modal-close {
  background: var(--color-light-4);
  border: none;
  cursor: pointer;
  padding: 12px;
  border-radius: 12px;
  color: var(--color-dark-3);
  transition: all 0.3s cubic-bezier(0.165, 0.84, 0.44, 1);
  display: flex;
  align-items: center;
  justify-content: center;
  width: 44px;
  height: 44px;
}

.staff-documents-modal-close:hover {
  background: var(--color-light-2);
  color: var(--color-dark-1);
  transform: scale(1.05);
}

.staff-documents-modal-close:active {
  transform: scale(0.95);
}

/* Modal Content */
.staff-documents-modal-content {
  flex: 1;
  overflow-y: auto;
  padding: 32px 40px;
  background: var(--color-white);
}

/* Custom Scrollbar */
.staff-documents-modal-content::-webkit-scrollbar {
  width: 6px;
}

.staff-documents-modal-content::-webkit-scrollbar-track {
  background: var(--color-light-4);
  border-radius: 3px;
}

.staff-documents-modal-content::-webkit-scrollbar-thumb {
  background: var(--color-light-2);
  border-radius: 3px;
  transition: background 0.3s ease;
}

.staff-documents-modal-content::-webkit-scrollbar-thumb:hover {
  background: var(--color-dark-3);
}

/* Loading, Error, and Empty States */
.staff-documents-loading,
.staff-documents-error,
.staff-documents-empty {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 24px;
  text-align: center;
  min-height: 200px;
}

.staff-documents-loading {
  color: var(--color-dark-3);
}

.staff-documents-loading::before {
  content: '';
  width: 40px;
  height: 40px;
  border: 3px solid var(--color-light-2);
  border-top: 3px solid var(--color-purple-1);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 20px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.staff-documents-error {
  color: var(--color-red-3);
}

.staff-documents-error::before {
  content: '⚠️';
  font-size: 48px;
  margin-bottom: 16px;
}

.staff-documents-empty {
  color: var(--color-dark-3);
}

.staff-documents-empty::before {
  content: '📂';
  font-size: 64px;
  margin-bottom: 20px;
  opacity: 0.6;
}

.staff-documents-loading p,
.staff-documents-error p,
.staff-documents-empty p {
  margin: 0;
  font-size: var(--text-16);
  font-weight: 500;
  line-height: 1.5;
}

/* Documents List */
.staff-documents-list {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

/* Individual Document Item */
.staff-document-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24px;
  border: 1px solid var(--color-light-2);
  border-radius: 16px;
  background: var(--color-white);
  transition: all 0.3s cubic-bezier(0.165, 0.84, 0.44, 1);
  position: relative;
  overflow: hidden;
}

.staff-document-item::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 4px;
  height: 100%;
  background: linear-gradient(180deg, var(--color-purple-1) 0%, var(--color-blue-1) 100%);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.staff-document-item:hover {
  border-color: var(--color-purple-1);
  box-shadow: 
    0 10px 25px -5px rgba(55, 183, 195, 0.1),
    0 4px 6px -2px rgba(55, 183, 195, 0.05);
  transform: translateY(-2px);
}

.staff-document-item:hover::before {
  opacity: 1;
}

/* Document Info Section */
.staff-document-info {
  display: flex;
  align-items: center;
  gap: 20px;
  flex: 1;
  min-width: 0;
}

/* Document Icon */
.staff-document-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 56px;
  height: 56px;
  background: linear-gradient(135deg, var(--color-light-6) 0%, var(--color-light-4) 100%);
  border-radius: 16px;
  color: var(--color-purple-1);
  flex-shrink: 0;
  transition: all 0.3s ease;
  border: 1px solid var(--color-light-2);
}

.staff-document-item:hover .staff-document-icon {
  background: linear-gradient(135deg, var(--color-purple-1) 0%, var(--color-blue-1) 100%);
  color: var(--color-white);
  transform: scale(1.05);
}

/* Document Details */
.staff-document-details {
  flex: 1;
  min-width: 0;
}

.staff-document-details h4 {
  margin: 0 0 6px 0;
  font-size: var(--text-18);
  font-weight: 600;
  color: var(--color-dark-1);
  line-height: 1.3;
  word-break: break-word;
}

.staff-document-type {
  margin: 0 0 12px 0;
  font-size: var(--text-14);
  color: var(--color-dark-3);
  text-transform: capitalize;
  font-weight: 500;
  padding: 4px 12px;
  background: var(--color-light-4);
  border-radius: 20px;
  display: inline-block;
}

/* Document Meta Information */
.staff-document-meta {
  display: flex;
  flex-wrap: wrap;
  gap: 20px;
  font-size: var(--text-13);
  color: var(--color-dark-3);
}

.staff-document-size,
.staff-document-date {
  display: flex;
  align-items: center;
  gap: 6px;
  font-weight: 500;
}

.staff-document-size::before {
  content: '💾';
  font-size: var(--text-11);
}

.staff-document-date::before {
  content: '📅';
  font-size: var(--text-11);
}

/* Document Expiry Badge */
.staff-document-expiry {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 6px 12px;
  border-radius: 20px;
  background: var(--color-warning-1);
  color: var(--color-warning-2);
  font-weight: 600;
  font-size: var(--text-11);
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.staff-document-expiry.expired {
  background: var(--color-error-1);
  color: var(--color-error-2);
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.7; }
}

.staff-document-expiry::before {
  content: '⏰';
  font-size: var(--text-11);
}

.staff-document-expiry.expired::before {
  content: '⚠️';
}

/* Document Actions */
.staff-document-actions {
  display: flex;
  align-items: center;
  flex-shrink: 0;
  margin-left: 20px;
}

/* Download Button */
.staff-document-download-btn {
  display: flex;
  align-items: center;
  gap: 10px;
  padding: 14px 24px;
  background: linear-gradient(135deg, var(--color-purple-1) 0%, var(--color-blue-1) 100%);
  color: var(--color-white);
  border: none;
  border-radius: 12px;
  font-size: var(--text-14);
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.165, 0.84, 0.44, 1);
  position: relative;
  overflow: hidden;
  min-width: 140px;
  justify-content: center;
}

.staff-document-download-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s ease;
}

.staff-document-download-btn:hover::before {
  left: 100%;
}

.staff-document-download-btn:hover:not(:disabled) {
  background: linear-gradient(135deg, var(--color-blue-1) 0%, var(--color-purple-1) 100%);
  transform: translateY(-2px);
  box-shadow: 0 8px 25px -8px rgba(55, 183, 195, 0.4);
}

.staff-document-download-btn:active:not(:disabled) {
  transform: translateY(0);
}

.staff-document-download-btn:disabled {
  background: var(--color-light-2);
  color: var(--color-dark-3);
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

.staff-document-download-btn:disabled::before {
  display: none;
}

/* Responsive Design */
@media (max-width: 768px) {
  .staff-documents-modal {
    width: 95%;
    max-width: none;
    margin: 10px;
    max-height: 90vh;
  }

  .staff-documents-modal-header {
    padding: 24px 20px 20px;
  }

  .staff-documents-modal-header h2 {
    font-size: var(--text-20);
  }

  .staff-documents-modal-content {
    padding: 24px 20px;
  }

  .staff-document-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 20px;
    padding: 20px;
  }

  .staff-document-info {
    width: 100%;
    gap: 16px;
  }

  .staff-document-icon {
    width: 48px;
    height: 48px;
  }

  .staff-document-actions {
    width: 100%;
    margin-left: 0;
  }

  .staff-document-download-btn {
    width: 100%;
    padding: 16px 24px;
    font-size: var(--text-16);
  }

  .staff-document-meta {
    flex-direction: column;
    gap: 12px;
  }

  .staff-document-details h4 {
    font-size: var(--text-16);
  }
}

@media (max-width: 480px) {
  .staff-documents-modal-overlay {
    padding: 10px;
  }

  .staff-documents-modal {
    max-height: 95vh;
  }

  .staff-documents-modal-header {
    padding: 20px 16px 16px;
  }

  .staff-documents-modal-content {
    padding: 20px 16px;
  }

  .staff-document-item {
    padding: 16px;
  }

  .staff-document-info {
    gap: 12px;
  }

  .staff-document-icon {
    width: 44px;
    height: 44px;
  }

  .staff-documents-list {
    gap: 16px;
  }
}

/* Dark mode support (if implemented) */
@media (prefers-color-scheme: dark) {
  .staff-documents-modal {
    background: var(--color-dark-8);
    color: var(--color-white);
  }

  .staff-documents-modal-header {
    background: linear-gradient(135deg, var(--color-dark-6) 0%, var(--color-dark-8) 100%);
    border-bottom-color: var(--color-dark-6);
  }

  .staff-documents-modal-header h2 {
    color: var(--color-white);
  }

  .staff-document-item {
    background: var(--color-dark-6);
    border-color: var(--color-dark-5);
  }

  .staff-document-item:hover {
    border-color: var(--color-purple-1);
  }

  .staff-document-details h4 {
    color: var(--color-white);
  }

  .staff-document-icon {
    background: linear-gradient(135deg, var(--color-dark-5) 0%, var(--color-dark-4) 100%);
    border-color: var(--color-dark-5);
  }
}

/* Focus states for accessibility */
.staff-documents-modal-close:focus,
.staff-document-download-btn:focus {
  outline: 2px solid var(--color-purple-1);
  outline-offset: 2px;
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .staff-document-item {
    border-width: 2px;
  }
  
  .staff-document-download-btn {
    border: 2px solid var(--color-purple-1);
  }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  .staff-documents-modal-overlay,
  .staff-documents-modal,
  .staff-document-item,
  .staff-document-icon,
  .staff-document-download-btn {
    animation: none;
    transition: none;
  }
  
  .staff-document-expiry.expired {
    animation: none;
  }
}

import React, { useState } from 'react';
import './NurtifyFooter.css';

import { useSubmitSubscription } from '@/hooks/contact.query';
import { toast } from "sonner";

const NurtifyFooter: React.FC = () => {
    // Correctly deconstruct the hook's return value
    const { mutateAsync: submitSubscription, isPending } = useSubmitSubscription();
    const [email, setEmail] = useState('');

    const validateEmail = (email: string): boolean => {
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        return emailRegex.test(email);
    };

    const handleSubscribe = async (e: React.FormEvent) => {
        e.preventDefault();

        // Validate the email before sending
        if (!validateEmail(email)) {
            toast.error('Please enter a valid email address.');
            return;
        }

        try {
            // Await the asynchronous call and pass the email
            await submitSubscription(email);

            // Show a success message
            toast.success('Thank you for subscribing!');

            // Reset the form after successful submission
            setEmail('');
        } catch (error: any) {
            // Handle and display the error message from the backend
            const errorMessage = error?.response?.data?.email?.[0] || 'Subscription failed. Please try again.';
            toast.error(errorMessage);
            console.error('Submission error:', error);
        }
    };

    // Social links
    const socialLinks = [
        {
            label: 'Twitter',
            href: 'https://twitter.com/nurtify',
            svg: (
                <svg width="17" height="14" viewBox="0 0 17 14" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M14.6758 4.10352C14.6758 4.25977 14.6758 4.38477 14.6758 4.54102C14.6758 8.88477 11.3945 13.8535 5.36328 13.8535C3.48828 13.8535 1.76953 13.3223 0.332031 12.3848C0.582031 12.416 0.832031 12.4473 1.11328 12.4473C2.64453 12.4473 4.05078 11.916 5.17578 11.041C3.73828 11.0098 2.51953 10.0723 2.11328 8.75977C2.33203 8.79102 2.51953 8.82227 2.73828 8.82227C3.01953 8.82227 3.33203 8.75977 3.58203 8.69727C2.08203 8.38477 0.957031 7.07227 0.957031 5.47852V5.44727C1.39453 5.69727 1.92578 5.82227 2.45703 5.85352C1.55078 5.25977 0.988281 4.25977 0.988281 3.13477C0.988281 2.50977 1.14453 1.94727 1.42578 1.47852C3.05078 3.44727 5.48828 4.75977 8.20703 4.91602C8.14453 4.66602 8.11328 4.41602 8.11328 4.16602C8.11328 2.35352 9.58203 0.884766 11.3945 0.884766C12.332 0.884766 13.1758 1.25977 13.8008 1.91602C14.5195 1.75977 15.2383 1.47852 15.8633 1.10352C15.6133 1.88477 15.1133 2.50977 14.4258 2.91602C15.082 2.85352 15.7383 2.66602 16.3008 2.41602C15.8633 3.07227 15.3008 3.63477 14.6758 4.10352Z" fill="#088395" />
                </svg>
            ),
        },
        {
            label: 'Instagram',
            href: 'https://instagram.com/nurtify',
            svg: (
                <svg width="17" height="14" viewBox="0 0 15 15" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M7.33203 3.75977C9.30078 3.75977 10.9258 5.38477 10.9258 7.35352C10.9258 9.35352 9.30078 10.9473 7.33203 10.9473C5.33203 10.9473 3.73828 9.35352 3.73828 7.35352C3.73828 5.38477 5.33203 3.75977 7.33203 3.75977ZM7.33203 9.69727C8.61328 9.69727 9.64453 8.66602 9.64453 7.35352C9.64453 6.07227 8.61328 5.04102 7.33203 5.04102C6.01953 5.04102 4.98828 6.07227 4.98828 7.35352C4.98828 8.66602 6.05078 9.69727 7.33203 9.69727ZM11.8945 3.63477C11.8945 4.10352 11.5195 4.47852 11.0508 4.47852C10.582 4.47852 10.207 4.10352 10.207 3.63477C10.207 3.16602 10.582 2.79102 11.0508 2.79102C11.5195 2.79102 11.8945 3.16602 11.8945 3.63477ZM14.2695 4.47852C14.332 5.63477 14.332 9.10352 14.2695 10.2598C14.207 11.3848 13.957 12.3535 13.1445 13.1973C12.332 14.0098 11.332 14.2598 10.207 14.3223C9.05078 14.3848 5.58203 14.3848 4.42578 14.3223C3.30078 14.2598 2.33203 14.0098 1.48828 13.1973C0.675781 12.3535 0.425781 11.3848 0.363281 10.2598C0.300781 9.10352 0.300781 5.63477 0.363281 4.47852C0.425781 3.35352 0.675781 2.35352 1.48828 1.54102C2.33203 0.728516 3.30078 0.478516 4.42578 0.416016C5.58203 0.353516 9.05078 0.353516 10.207 0.416016C11.332 0.478516 12.332 0.728516 13.1445 1.54102C13.957 2.35352 14.207 3.35352 14.2695 4.47852ZM12.7695 11.4785C13.1445 10.5723 13.0508 8.38477 13.0508 7.35352C13.0508 6.35352 13.1445 4.16602 12.7695 3.22852C12.5195 2.63477 12.0508 2.13477 11.457 1.91602C10.5195 1.54102 8.33203 1.63477 7.33203 1.63477C6.30078 1.63477 4.11328 1.54102 3.20703 1.91602C2.58203 2.16602 2.11328 2.63477 1.86328 3.22852C1.48828 4.16602 1.58203 6.35352 1.58203 7.35352C1.58203 8.38477 1.48828 10.5723 1.86328 11.4785C2.11328 12.1035 2.58203 12.5723 3.20703 12.8223C4.11328 13.1973 6.30078 13.1035 7.33203 13.1035C8.33203 13.1035 10.5195 13.1973 11.457 12.8223C12.0508 12.5723 12.5508 12.1035 12.7695 11.4785Z" fill="#088395" />
                </svg>
            ),
        },
        {
            label: 'LinkedIn',
            href: 'https://www.linkedin.com/company/nurtifyltd',
            svg: (
                <svg width="17" height="14" viewBox="0 0 15 15" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M13.332 0.353516C13.8633 0.353516 14.332 0.822266 14.332 1.38477V13.3535C14.332 13.916 13.8633 14.3535 13.332 14.3535H1.30078C0.769531 14.3535 0.332031 13.916 0.332031 13.3535V1.38477C0.332031 0.822266 0.769531 0.353516 1.30078 0.353516H13.332ZM4.55078 12.3535V5.69727H2.48828V12.3535H4.55078ZM3.51953 4.75977C4.17578 4.75977 4.70703 4.22852 4.70703 3.57227C4.70703 2.91602 4.17578 2.35352 3.51953 2.35352C2.83203 2.35352 2.30078 2.91602 2.30078 3.57227C2.30078 4.22852 2.83203 4.75977 3.51953 4.75977ZM12.332 12.3535V8.69727C12.332 6.91602 11.9258 5.50977 9.83203 5.50977C8.83203 5.50977 8.14453 6.07227 7.86328 6.60352H7.83203V5.69727H5.86328V12.3535H7.92578V9.07227C7.92578 8.19727 8.08203 7.35352 9.17578 7.35352C10.2383 7.35352 10.2383 8.35352 10.2383 9.10352V12.3535H12.332Z" fill="#088395" />
                </svg>
            ),
        },
    ];

    const handleSocialClick = (href: string) => (e: React.MouseEvent) => {
        e.preventDefault();
        window.open(href, '_blank', 'noopener,noreferrer');
    };

    return (
        <footer className="nurtify-footer">
            <div className="nurtify-footer__container">
                {/* Top Section */}
                <div className="nurtify-footer__top">
                    {/* Logo and Subscription Section */}
                    <div className="nurtify-footer__brand">
                        <div className="nurtify-footer__logo">
                            <img
                                src="/assets/img/landing/nurtify-logo-footer.png"
                                alt="Nurtify Logo"
                                style={{ height: 60, marginRight: 12, width: 200 }}
                            />
                        </div>

                        <div className="nurtify-footer__subscription">
                            <h3>Subscribe to get the Latest Updates</h3>
                            <form onSubmit={handleSubscribe} className="nurtify-footer__subscribe-form" noValidate>
                                <input
                                    type="email"
                                    placeholder="Enter email address"
                                    value={email}
                                    onChange={(e) => setEmail(e.target.value)}
                                    className="nurtify-footer__email-input"
                                    disabled={isPending}
                                />
                                <button
                                    type="submit"
                                    className="nurtify-footer__subscribe-btn"
                                    onMouseEnter={(e) => {
                                        e.currentTarget.style.backgroundColor = '#2A9BAB';
                                        e.currentTarget.style.transform = 'translateY(-2px)';
                                    }}
                                    onMouseLeave={(e) => {
                                        e.currentTarget.style.backgroundColor = '#37B7C3';
                                        e.currentTarget.style.transform = 'translateY(0)';
                                    }}
                                    disabled={isPending}
                                >
                                    {isPending ? 'Submitting...' : 'Subscribe'}
                                </button>
                            </form>
                        </div>
                    </div>

                    {/* Footer Links */}
                    <div className="nurtify-footer__links">
                        <div className="nurtify-footer__column">
                            <h4>For Patients</h4>
                            <ul>
                                <li><a href="/contact-us">Join Trial</a></li>
                                <li><a href="https://bepartofresearch.nihr.ac.uk/results/search-results" target="_blank" rel="noopener noreferrer">Search Clinical Trial</a></li>
                                <li><a href="/blog">Resources</a></li>
                                <li><a href="/contact-us">Contact Us</a></li>
                            </ul>
                        </div>

                        <div className="nurtify-footer__column">
                            <h4>For Researchers</h4>
                            <ul>
                                <li><a href="/contact-us">Contact us</a></li>
                                <li><a href="/contact-us">Get Envolved</a></li>
                                <li><a href="/blog">Blog</a></li>
                            </ul>
                        </div>

                        <div className="nurtify-footer__column">
                            <h4>Company</h4>
                            <ul>
                                <li><a href="/about-us">About Us</a></li>
                                <li><a href="/about-us#career">Careers</a></li>
                                <li><a href="/blog">Blog</a></li>
                                <li><a href="https://meetings.hubspot.com/nurtify?embed=true" target="_blank" rel="noopener noreferrer">Book Demo</a></li>
                                <li><a href="/contact-us">FAQs</a></li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
            <hr className="nurtify-footer-hr" />
            {/* Bottom Section */}
            <div className="nurtify-footer__container">
                <div className="nurtify-footer__bottom">
                    <div className="nurtify-footer__copyright">
                        © 2025 Nurtify. All Rights Reserved
                    </div>

                    <div className="nurtify-footer__legal">
                        <a href="/privacy">Privacy Policy</a>
                        <a href="/terms-and-conditions">Terms of Service</a>
                        <a href="/cookies">Cookies Policy</a>
                    </div>

                    <div className="nurtify-footer__social">
                        {socialLinks.map((link) => (
                            <a
                                key={link.label}
                                href={link.href}
                                aria-label={link.label}
                                onClick={handleSocialClick(link.href)}
                                rel="noopener noreferrer"
                                target="_blank"
                            >
                                {link.svg}
                            </a>
                        ))}
                    </div>
                </div>
            </div>
        </footer>
    );
};

export default NurtifyFooter;
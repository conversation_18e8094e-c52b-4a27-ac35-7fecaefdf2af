import { ReactNode } from "react";
import { Navigate, useLocation } from "react-router-dom";
import { useCurrentUserQuery } from "@/hooks/user.query";

// Extended user interface to include additional properties from the API
interface ExtendedUser {
  uuid?: string;
  first_name?: string;
  last_name?: string;
  phone_number?: string;
  birth_date?: string;
  gender?: string;
  primary_address?: string;
  city?: string;
  state?: string;
  country?: string | null;
  registration_body?: string;
  expary_date?: string;
  organization_name?: string | null;
  speciality?: string;
  user_type?: string;
}

// Function to check if all required fields are filled
export const isProfileComplete = (user: ExtendedUser | undefined): boolean => {
  if (!user) return false;
  
  return !!(
    user.first_name &&
    user.last_name &&
    user.phone_number &&
    user.birth_date &&
    user.gender &&
    user.primary_address &&
    user.state &&
    user.country &&
    user.registration_body &&
    user.expary_date &&
    user.organization_name &&
    user.speciality
  );
};

interface ProfileGuardProps {
  children: ReactNode;
}

/**
 * ProfileGuard component
 * 
 * This component checks if the user's profile is complete.
 * If the profile is incomplete, it redirects to the profile page.
 * If the profile is complete, it renders the children.
 */
const ProfileGuard = ({ children }: ProfileGuardProps) => {
  const location = useLocation();
  const { data: currentUser, isLoading } = useCurrentUserQuery();
  
  // Determine the correct profile path based on user type
  const getProfilePath = () => {
    if (!currentUser) return "/profile";
    
    // Check user type and return appropriate profile path
    switch (currentUser.user_type?.toLowerCase()) {
      case "organization":
        return "/org/dashboard/profile";
      case "patient":
        return "/patient/profile";
      default:
        return "/profile";
    }
  };
  
  // If we're already on the profile page (any type), render the children
  const profilePath = getProfilePath();
  if (location.pathname === profilePath || 
      location.pathname === "/profile" || 
      location.pathname === "/org/dashboard/profile" || 
      location.pathname === "/patient/profile") {
    return <>{children}</>;
  }
  
  // If we're loading, show nothing yet
  if (isLoading) {
    return <div>Loading...</div>;
  }
  
  // If the user is not logged in, we'll let the auth guard handle it
  if (!currentUser) {
    return <>{children}</>;
  }
  
  // Check if the profile is complete
  const profileComplete = isProfileComplete(currentUser as ExtendedUser) || currentUser.is_superuser;;
  
  // If the profile is incomplete, redirect to the appropriate profile page
  if (!profileComplete) {
    return <Navigate to={profilePath} state={{ from: location }} replace />;
  }
  
  // If the profile is complete, render the children
  return <>{children}</>;
};

export default ProfileGuard;

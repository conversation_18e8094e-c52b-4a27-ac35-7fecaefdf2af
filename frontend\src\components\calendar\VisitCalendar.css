.visit-calendar-container {
  height: 100%;
  width: 100%;
  margin-top: 20px;
}

/* View-specific styles */
.fc-timeGridDay-view .fc-timegrid-slot,
.fc-timeGridWeek-view .fc-timegrid-slot {
  height: 40px;
}

.fc-dayGridMonth-view .fc-daygrid-day {
  height: 120px;
}

/* Calendar event styling */
.visit-calendar-container .fc-event {
  cursor: pointer;
  border-radius: 4px;
  padding: 2px 4px;
  font-size: 0.85em;
  line-height: 1.3;
  border-left-width: 4px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  transition: box-shadow 0.2s ease;
}

/* Month view specific event styling */
.fc-dayGridMonth-view .fc-event {
  margin: 1px 0;
  padding: 1px 3px;
  font-size: 0.8em;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.visit-calendar-container .fc-event:hover {
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

/* Time grid styling */
.visit-calendar-container .fc-timegrid-slot {
  height: 40px;
  border-bottom: 1px dashed #e0e0e0;
}

.visit-calendar-container .fc-timegrid-slot-lane {
  cursor: pointer;
}

.visit-calendar-container .fc-timegrid-slot-lane:hover {
  background-color: rgba(35, 183, 205, 0.05);
}

/* Header styling */
.visit-calendar-container .fc-col-header-cell {
  background-color: #f8f9fa;
  font-weight: 600;
  padding: 10px 0;
}

/* Today highlight */
.visit-calendar-container .fc-day-today {
  background-color: rgba(35, 183, 205, 0.05) !important;
}

/* Time labels */
.visit-calendar-container .fc-timegrid-axis {
  font-size: 0.85em;
  color: #6c757d;
}

/* Now indicator */
.visit-calendar-container .fc-timegrid-now-indicator-line {
  border-color: #dc3545;
}

.visit-calendar-container .fc-timegrid-now-indicator-arrow {
  border-color: #dc3545;
  border-top-color: transparent;
  border-bottom-color: transparent;
}

/* Responsive styles */
@media (max-width: 768px) {
  .visit-calendar-container {
    overflow-x: auto;
    overflow-y: auto;
    max-height: 600px;
  }

  .visit-calendar-container .fc-event {
    font-size: 0.75em;
    padding: 1px 2px;
  }

  /* Adjust column widths for better mobile experience */
  .fc-timeGridWeek-view .fc-col-header-cell,
  .fc-timeGridWeek-view .fc-timegrid-col {
    min-width: 100px;
  }

  .fc-dayGridMonth-view .fc-daygrid-day {
    min-width: 80px;
    height: 80px;
  }
}

/* Accessibility - focus states */
.visit-calendar-container .fc-event:focus,
.visit-calendar-container .fc-timegrid-slot-lane:focus,
.visit-calendar-container .fc-daygrid-day:focus {
  outline: 2px solid #23b7cd;
  outline-offset: 2px;
}

/* Keyboard navigation indicators */
.fc-day-today {
  background-color: rgba(35, 183, 205, 0.1) !important;
}

.fc-day:focus,
.fc-daygrid-day:focus {
  box-shadow: inset 0 0 0 2px #23b7cd;
}

/* Loading and error states */
.loading, .error {
  padding: 20px;
  text-align: center;
  background-color: #f8f9fa;
  border-radius: 8px;
  margin: 20px 0;
}

.error {
  color: #dc3545;
  border: 1px solid #f5c6cb;
}

import { PrescriptionWithDetails } from "@/services/api/types";
import { Plus, Eye } from "lucide-react";
import "./prescription.css";
import { format } from "date-fns";
import { ReactNode, useEffect, useState, useMemo } from "react";
import AddPrescriptionFormModal from "@/components/modal/AddPrescriptionFormModal";
import PrescriptionDescriptionModal from "@/components/modal/PrescriptionDescriptionModal";
import { useCurrentUserQuery } from "@/hooks/user.query";
import useSelectedPatientStore from '@/store/SelectedPatientState';
import { usePrescriptionsByPatientQuery } from "@/hooks/prescription.query";
import { usePatientAllergiesQuery } from "@/hooks/patient.query";
import NurtifyFilter, { NurtifyFilterItem } from "@/components/NurtifyFilter";
import DataTable from "@/components/common/DataTable";

export default function Prescriptions() {
  const { data } = useCurrentUserQuery();

  const { selectedPatient } = useSelectedPatientStore();
  const [isAddPresModalOpen, setIsAddPresModalOpen] = useState(false);
  const [isShowPresDescModalOpen, setIsShowPresDescModalOpen] = useState(false);
  const patient_uuid = selectedPatient?.uuid || "";
  const { data: Prescriptions = [], refetch: refetchPrescriptions } = usePrescriptionsByPatientQuery(patient_uuid);
  const { data: PatientAllergies, refetch: refetchPatientAllergies } = usePatientAllergiesQuery(selectedPatient?.nhs_number || "");

  const user_identifier = data?.identifier || "";

  const [prescriptionDetails, setPrescriptionDetails] = useState<PrescriptionWithDetails | null>(null);
  const [prescriptionUpdated, setPrescriptionUpdated] = useState(false);

  // State for filters
  const [selectedStatus, setSelectedStatus] = useState<string[]>([]);

  useEffect(() => {
    refetchPrescriptions();
    refetchPatientAllergies();
  }, [selectedPatient, refetchPrescriptions, refetchPatientAllergies]);

  useEffect(() => {
    if (prescriptionUpdated) {
      refetchPrescriptions();
      setPrescriptionUpdated(false);
    }
  }, [prescriptionUpdated, refetchPrescriptions]);

  const handlePrescriptionDescription = (row: PrescriptionWithDetails) => {
    setPrescriptionDetails(row);
    setIsShowPresDescModalOpen(true);
  };

  const normalizeStatus = (status?: string): string => {
    return status
      ? (status.charAt(0).toUpperCase() + status.slice(1).toLowerCase())
      : 'Prescribed';
  };

  // Memoized filter options with counts based on the raw prescriptions data
  const filterOptionsWithCounts = useMemo(() => {
    const statusCounts = new Map<string, number>();

    Prescriptions.forEach(prescription => {
      const normalizedStatus = normalizeStatus(prescription.status);
      statusCounts.set(normalizedStatus, (statusCounts.get(normalizedStatus) || 0) + 1);
    });

    const statusOptions = Array.from(statusCounts.keys()).map(key => ({
      label: `${key} (${statusCounts.get(key)})`,
      value: key
    }));

    return { statusOptions };
  }, [Prescriptions]);

  // Memoized filtered prescriptions based on selected status filter
  const filteredPrescriptions = useMemo(() => {
    let filtered = Prescriptions;

    if (selectedStatus.length > 0) {
      filtered = filtered.filter(prescription =>
        selectedStatus.includes(normalizeStatus(prescription.status))
      );
    }

    return filtered;
  }, [Prescriptions, selectedStatus]);

  const PrescriptionColums: Array<{
    key: keyof PrescriptionWithDetails;
    header: string;
    render?: (value: PrescriptionWithDetails[keyof PrescriptionWithDetails], row?: PrescriptionWithDetails) => ReactNode;
    hidden?: boolean;
  }> = [
    { key: "uuid", header: "ID", hidden: true },
    { key: "drug_name", header: "Drug Name" },
    {
      key: "dose",
      header: "Dose",
      render: (value, row) => row ? `${row.dose} ${row.unit}` : String(value),
    },
    {
      key: "prescribed_at",
      header: "Prescribed_at",
      render: (value) =>
        value && typeof value === 'string'
          ? format(new Date(value), 'MMMM d, yyyy, hh:mm a')
          : 'N/A',
    },
    {
      key: "administered_at",
      header: "Administered_at",
      render: (value) =>
        value && typeof value === 'string'
          ? format(new Date(value), 'MMMM d, yyyy, hh:mm a')
          : 'N/A',
    },
    {
      key: "status",
      header: "Status",
      render: (value) => normalizeStatus(String(value)),
    },
  ];

  const actions = [
    {
      icon: <Eye size={20} />,
      onClick: (row: PrescriptionWithDetails) => handlePrescriptionDescription(row),
      tooltipText: "Show Description",
    },
  ];

  const filters: NurtifyFilterItem[] = [
    {
      label: "Status",
      type: "checkbox",
      options: filterOptionsWithCounts.statusOptions,
      value: selectedStatus,
      onChange: setSelectedStatus as any,
    },
  ];


  return (
    <div className="content-wrapper js-content-wrapper">
      <div className="bg-light-4 px-3 py-5">
        <div className="container-fluid py-6 px-6">
          <div className="patient-details-container">
            <div className="patient-details-header">
              <h1 className="page-title">Prescription List</h1>
              <div className="patient-actions">
                <button
                  onClick={() => setIsAddPresModalOpen(true)}
                  className="btn btn-primary-nurtify"
                >
                  Add Prescription <Plus size={20} />
                </button>
              </div>
            </div>

            <div className="row g-5">
              {Prescriptions.length > 0 ? (
                <div>

                    <NurtifyFilter layout="horizontal" filters={filters} />
                  <DataTable<PrescriptionWithDetails>
                    data={filteredPrescriptions}
                    columns={PrescriptionColums}
                    actions={actions}
                    globalFilterPlaceholder="Search prescriptions..."
                  />
                </div>
              ) : (
                <div className="no-studies">
                  You are not currently enrolled in any prescriptions.
                </div>
              )}

              <AddPrescriptionFormModal
                isOpen={isAddPresModalOpen}
                setIsModalOpen={setIsAddPresModalOpen}
                patient_uuid={patient_uuid}
                prescriber_identifier={user_identifier}
                setPrescriptionUpdated={setPrescriptionUpdated}
                PatientAllergies={PatientAllergies}
              />

              <PrescriptionDescriptionModal
                isOpen={isShowPresDescModalOpen}
                setIsModalOpen={setIsShowPresDescModalOpen}
                prescriptionDetails={prescriptionDetails}
                userIdentifier={user_identifier}
                initialStatus={normalizeStatus(prescriptionDetails?.status)}
                setPrescriptionUpdated={setPrescriptionUpdated}
              />
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

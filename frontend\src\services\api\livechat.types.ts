// Live Chat Types - Based on Backend Serializers and Models

// User type for live chat (from UserSerializer with ref_name = 'LiveChatUser')
export interface LiveChatUser {
  identifier: string;
  email: string;
  first_name: string;
  last_name: string;
  image?: string;
}

// Department type (from DepartmentSerializer)
export interface LiveChatDepartment {
  uuid: string;
  name: string;
  hospital?: string | number;
  hospital_uuid?: string;
  phone_number?: string;
  extension?: string;
  primary_address?: string;
  secondary_address?: string;
  postcode?: string;
  country?: string;
}

export interface Study {
  uuid: string;
  iras: string;
  name: string;
  description?: string;
  full_title?: string;
  created_at: string;
  updated_at: string;
}

export interface StudyEnrollment {
  uuid: string;
  first_visit: string;
  referred_by: string;
  referred_date: string;
  patient_code: string;
  comments: string;
  team_email: string;
  reminder_email: string;
  created_at: string;
  study_name: string;
  patient_name: string;
  study_status: string;
  study_uuid_read: string;
}

// Participant type (from LiveChatParticipantSerializer)
export interface LiveChatParticipant {
  user: LiveChatUser;
  role: 'PATIENT' | 'STAFF' | 'ADMIN';
  joined_at: string;
  is_online: boolean;
  last_seen_at?: string;
}

// Message type (from LiveChatMessageSerializer)
export interface LiveChatMessage {
  uuid: string;
  content: string;
  sender: LiveChatUser;
  sender_role: 'PATIENT' | 'STAFF' | 'ADMIN' | null;
  created_at: string;
  is_system_message: boolean;
  is_read: boolean;
}

// Main LiveChat type (from LiveChatSerializer)
export interface LiveChat {
  uuid: string;
  subject: string;
  status: 'OPEN' | 'PENDING' | 'CLOSED';
  department: LiveChatDepartment;
  patient: LiveChatUser;
  participants: LiveChatParticipant[];
  created_at: string;
  updated_at: string;
  last_message_at?: string;
  is_active: boolean;
  last_message?: LiveChatMessage;
  unread_count: number;
  duration: string; // DurationField from backend
  study: Study;
}

// Notification type (from LiveChatNotificationSerializer)
export interface LiveChatNotification {
  id: number;
  message: LiveChatMessage;
  live_chat: LiveChat;
  is_read: boolean;
  created_at: string;
}

// Create/Update Types (from Create Serializers)
export interface LiveChatCreateData {
  department_id: string; // UUIDField
  subject: string; // max_length=255
  initial_message: string; // write_only
  study_id: string; // UUIDField
}

export interface LiveChatMessageCreateData {
  content: string; // TextField with MinLengthValidator(1)
}

export interface LiveChatUpdateData {
  subject?: string;
  status?: 'OPEN' | 'PENDING' | 'CLOSED';
  study_id?: string;
}

// Analytics type (from analytics action response)
export interface LiveChatAnalytics {
  total_chats: number;
  active_chats: number;
  closed_chats: number;
  new_chats_24h: number;
  new_chats_7d: number;
  avg_messages_per_chat: number;
  department_stats?: {
    department_name: string;
    total_chats: number;
    active_chats: number;
    new_chats_24h: number;
  };
}

// WebSocket Message Types (from consumers.py)
export interface WebSocketMessage {
  type: 'chat_message' | 'user_status' | 'typing_status';
  message?: {
    uuid: string;
    content: string;
    sender: {
      identifier: string;
      name: string;
      role: 'PATIENT' | 'STAFF' | 'ADMIN';
    };
    created_at: string;
    is_system_message: boolean;
  };
  user?: {
    identifier: string;
    name: string;
    is_online?: boolean;
    is_typing?: boolean;
  };
}

export interface WebSocketSendMessage {
  type: 'chat_message' | 'typing';
  content?: string;
  is_typing?: boolean;
}

// Paginated Response (following backend pagination pattern)
export interface LiveChatPaginatedResponse<T> {
  count: number;
  next: string | null;
  previous: string | null;
  results: T[];
}

// Filter/Search Parameters (from ViewSet filterset_fields and search_fields)
export interface LiveChatFilterParams {
  status?: 'OPEN' | 'PENDING' | 'CLOSED';
  is_active?: boolean;
  search?: string; // searches in "subject", "messages__content"
  ordering?: 'created_at' | 'updated_at' | 'last_message_at' | '-created_at' | '-updated_at' | '-last_message_at';
  limit?: number;
  offset?: number;
}

// Response types for specific actions (all return LiveChatSerializer data)
export type LiveChatActionResponse = LiveChat;

// Specific response types
export type LiveChatCloseResponse = LiveChatActionResponse;
export type LiveChatReopenResponse = LiveChatActionResponse;
export type LiveChatSendMessageResponse = LiveChatMessage;

export interface LiveChatMarkNotificationResponse {
  status: string; // "success"
}

// Department Study Team Users Response
export interface DepartmentStudyTeamUsersResponse {
  users: OrganizationUser[];
  count: number;
  patient: {
    uuid: string;
    name: string;
    nhs_number: string;
  };
  department: {
    uuid: string;
    name: string;
  };
}

// Organization User type (for filtered users)
export interface OrganizationUser {
  identifier: string;
  first_name: string;
  last_name: string;
  phone_number?: string;
  email: string;
  hospital?: {
    uuid: string;
    name: string;
  };
  department?: {
    uuid: string;
    name: string;
  };
  speciality?: string;
}

// Error response type
export interface LiveChatApiError {
  message?: string;
  detail?: string;
  non_field_errors?: string[];
  [key: string]: any; // For field-specific errors
}

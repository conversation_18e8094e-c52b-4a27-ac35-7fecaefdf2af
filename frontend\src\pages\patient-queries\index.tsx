import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import {
  MessageSquare,
  Send,
  AlertCircle,
  HelpCircle,
  ChevronLeft,
  CheckCircle2,
  Eye,
  Calendar,
  Clock,
  Tag,
  Hash,
  FileText,
  Archive,
  ListChecks,
  ExternalLink,
} from 'lucide-react';
import useSelectedPatientStore from '@/store/SelectedPatientState';
import {
  useGetFormSubmissionQueriesByPatient,
  useGetQueryResponses,
  useCreateQueryResponse,
} from '@/hooks/form.query';
import { useCurrentUserQuery } from '@/hooks/user.query';
import { resolveQuery, getSubmissionByUuid } from '@/services/api/form.service';
import FormSubmissionPreview from '../patient-form/FormSubmissionPreview';
import { formatDate } from '@/utils/date';
import './FormsQueries.css';
import NurtifyFilter, { NurtifyFilterItem } from '@/components/NurtifyFilter';

interface QueryResponse {
  uuid: string;
  message: string;
  created_at: string;
  responder: {
    first_name: string;
    last_name: string;
  };
  is_clarification: boolean;
}

interface PaginatedResponse<T> {
  count: number;
  next: string | null;
  previous: string | null;
  results: T[];
}

interface FormSubmissionQuery {
  uuid: string;
  question_number: string;
  description: string;
  priority: 'low' | 'medium' | 'high'; // Use a type for priority
  created_by: {
    identifier: string;
    first_name: string;
    last_name: string;
    email: string;
  };
  created_at: string;
  resolved_at: string | null;
  is_resolved: boolean;
  resolution_notes: string | null;
  assigned_to: string | null;
  responses: QueryResponse[];
  latest_response: QueryResponse | null;
  form_submission: {
    form_name: string;
    uuid: string;
  };
}

const FormsQueries: React.FC = () => {
  const { selectedPatient } = useSelectedPatientStore();
  const { data: currentUser } = useCurrentUserQuery();
  const [newResponse, setNewResponse] = useState<Record<string, string>>({});
  const [isClarification, setIsClarification] = useState<Record<string, boolean>>({});
  const [selectedQuery, setSelectedQuery] = useState<FormSubmissionQuery | null>(null);
  const [queryResponses, setQueryResponses] = useState<Record<string, QueryResponse[]>>({});
  const [showResolveModal, setShowResolveModal] = useState(false);
  const [resolutionNotes, setResolutionNotes] = useState('');
  const [activeTab, setActiveTab] = useState<'active' | 'resolved'>('active');
  const [previewSubmission, setPreviewSubmission] = useState<any>(null);
  const [isLoadingPreview, setIsLoadingPreview] = useState(false);
  const [previewError, setPreviewError] = useState<string | null>(null);

  // New state for priority filtering
  const [selectedPriorities, setSelectedPriorities] = useState<string[]>([]);

  // The 'is_resolved' parameter for the API call is now dynamically determined by the 'activeTab' state.
  const {
    data: queriesData,
    isLoading: isLoadingQueries,
    error: queriesError,
  } = useGetFormSubmissionQueriesByPatient(selectedPatient?.uuid || '', {
    is_resolved: activeTab === 'resolved',
  });

  const allQueries = ((queriesData as unknown) as PaginatedResponse<FormSubmissionQuery>)
    ?.results || [];

  // Filter queries based on selected priority
  const queries = selectedPriorities.length > 0
    ? allQueries.filter((query) => selectedPriorities.includes(query.priority))
    : allQueries;

  const {
    data: responsesData,
    isLoading: isLoadingResponses,
  } = useGetQueryResponses(selectedQuery ? { query_uuid: selectedQuery.uuid } : undefined);

  useEffect(() => {
    if (responsesData && selectedQuery) {
      const paginatedResponses = responsesData as unknown as PaginatedResponse<QueryResponse>;
      const responses = paginatedResponses?.results || [];

      setQueryResponses((prev) => ({
        ...prev,
        [selectedQuery.uuid]: responses,
      }));
    }
  }, [responsesData, selectedQuery]);

  const { mutate: createResponse, isPending: isSubmitting } = useCreateQueryResponse();

  const handleSubmitResponse = (queryUuid: string) => {
    if (!newResponse[queryUuid]?.trim() || !selectedPatient) return;

    createResponse(
      {
        query: queryUuid,
        message: newResponse[queryUuid],
        is_clarification: isClarification[queryUuid] || false,
      },
      {
        onSuccess: () => {
          setNewResponse((prev) => ({ ...prev, [queryUuid]: '' }));
          setIsClarification((prev) => ({ ...prev, [queryUuid]: false }));
          if (selectedQuery) {
            setQueryResponses((prev) => ({
              ...prev,
              [queryUuid]: [
                ...(prev[queryUuid] || []),
                {
                  uuid: Date.now().toString(),
                  message: newResponse[queryUuid],
                  created_at: new Date().toISOString(),
                  responder: {
                    first_name: 'You',
                    last_name: '',
                  },
                  is_clarification: isClarification[queryUuid] || false,
                },
              ],
            }));
          }
        },
      }
    );
  };

  const handleResolveQuery = async () => {
    if (!selectedQuery) return;

    try {
      await resolveQuery(selectedQuery.uuid, resolutionNotes);
      setSelectedQuery((prev) => (prev ? { ...prev, is_resolved: true } : null));
      setShowResolveModal(false);
      setResolutionNotes('');
    } catch (error) {
      console.error('Error resolving query:', error);
    }
  };

  const handlePreviewForm = async (formSubmissionUuid: string) => {
    setIsLoadingPreview(true);
    setPreviewError(null);
    try {
      const response = await getSubmissionByUuid(formSubmissionUuid);
      setPreviewSubmission(response);
    } catch (error) {
      console.error('Error fetching form submission:', error);
      setPreviewError('Failed to load form submission. Please try again.');
    } finally {
      setIsLoadingPreview(false);
    }
  };

  const handleClosePreview = () => {
    setPreviewSubmission(null);
    setPreviewError(null);
  };

  if (!selectedPatient) {
    return <div className="forms-queries-error">Please select a patient first</div>;
  }

  if (isLoadingQueries || isLoadingResponses) {
    return <div className="forms-queries-loading">Loading queries...</div>;
  }

  if (queriesError) {
    return <div className="forms-queries-error">Failed to load queries</div>;
  }

  const renderResolveModal = () => {
    if (!showResolveModal) return null;

    return (
      <div className="modal-overlay">
        <div className="modal-content">
          <h3>Resolve Query</h3>
          <p>Add any notes about how this query was resolved (optional)</p>
          <textarea
            value={resolutionNotes}
            onChange={(e) => setResolutionNotes(e.target.value)}
            placeholder="Enter resolution notes..."
            rows={4}
          />
          <div className="modal-actions">
            <button
              className="cancel-btn"
              onClick={() => {
                setShowResolveModal(false);
                setResolutionNotes('');
              }}
            >
              Cancel
            </button>
            <button className="resolve-btn" onClick={handleResolveQuery}>
              <CheckCircle2 size={16} />
              Resolve Query
            </button>
          </div>
        </div>
      </div>
    );
  };

  const renderQueryDetail = () => {
    if (!selectedQuery) return null;

    const responses = queryResponses[selectedQuery.uuid] || [];
    const isQueryCreator = selectedQuery.created_by.identifier === currentUser?.identifier;

    const formatQuestionNumber = (questionNumber: string) => {
      const [section, question] = questionNumber.split('-');
      return `Section ${section}, Question ${question}`;
    };

    return (
      <motion.div
        className="query-detail-view"
        initial={{ opacity: 0, x: 20 }}
        animate={{ opacity: 1, x: 0 }}
        transition={{ duration: 0.3 }}
      >
        <button className="back-button" onClick={() => setSelectedQuery(null)}>
          <ChevronLeft size={20} />
          Back to Queries
        </button>

        <div className="query-card">
          <div className="query-header">
            <div className="query-info">
              <h3 className="query-title">
                <AlertCircle size={16} className={`priority-${selectedQuery.priority}`} />
                {formatQuestionNumber(selectedQuery.question_number)}
              </h3>
              <p className="query-description">{selectedQuery.description}</p>
              <div className="query-creator">
                <span className="creator-label">Created by:</span>
                <span className="creator-name">
                  {selectedQuery.created_by.first_name} {selectedQuery.created_by.last_name}
                </span>
              </div>
            </div>
            <div className="query-meta">
              <span className="query-date">
                <Calendar size={14} />
                {formatDate(selectedQuery.created_at)}
              </span>
              <span className={`status-badge ${selectedQuery.is_resolved ? 'resolved' : 'pending'}`}>
                {selectedQuery.is_resolved ? (
                  <>
                    <CheckCircle2 size={14} />
                    Resolved
                  </>
                ) : (
                  <>
                    <Clock size={14} />
                    Pending
                  </>
                )}
              </span>
              {isQueryCreator && !selectedQuery.is_resolved && (
                <button
                  className="resolve-query-btn"
                  onClick={() => setShowResolveModal(true)}
                >
                  <CheckCircle2 size={16} />
                  Resolve Query
                </button>
              )}
            </div>
          </div>

          <div className="responses-section">
            {isLoadingResponses ? (
              <div className="loading-responses">Loading responses...</div>
            ) : responses.length === 0 ? (
              <div className="no-responses">No responses yet</div>
            ) : (
              responses.map((response: QueryResponse) => (
                <div key={response.uuid} className="response-item">
                  <div className="response-header">
                    <div className="response-meta">
                      <span className="responder-name">
                        {response.responder.first_name} {response.responder.last_name}
                      </span>
                      {response.is_clarification && (
                        <span className="clarification-badge" title="This is a clarification">
                          <HelpCircle size={14} />
                          Clarification
                        </span>
                      )}
                    </div>
                    <span className="response-date">
                      <Calendar size={14} />
                      {formatDate(response.created_at)}
                    </span>
                  </div>
                  <p className="response-message">{response.message}</p>
                </div>
              ))
            )}
          </div>

          {selectedQuery.is_resolved ? (
            <div className="resolved-query-notice">
              <CheckCircle2 size={20} />
              <p>This query has been resolved and cannot be edited.</p>
              {selectedQuery.resolution_notes && (
                <div className="resolution-notes">
                  <h4>Resolution Notes:</h4>
                  <p>{selectedQuery.resolution_notes}</p>
                </div>
              )}
            </div>
          ) : (
            <div className="response-form">
              <div className="response-input-container">
                <textarea
                  value={newResponse[selectedQuery.uuid] || ''}
                  onChange={(e) =>
                    setNewResponse((prev) => ({
                      ...prev,
                      [selectedQuery.uuid]: e.target.value,
                    }))
                  }
                  placeholder="Type your response..."
                  rows={3}
                />
                <div className="response-options">
                  <label className="clarification-toggle">
                    <input
                      type="checkbox"
                      checked={isClarification[selectedQuery.uuid] || false}
                      onChange={(e) =>
                        setIsClarification((prev) => ({
                          ...prev,
                          [selectedQuery.uuid]: e.target.checked,
                        }))
                      }
                    />
                    <span className="toggle-label">
                      <HelpCircle size={16} />
                      This is a clarification
                    </span>
                  </label>
                </div>
              </div>
              <button
                className="submit-response-btn"
                onClick={() => handleSubmitResponse(selectedQuery.uuid)}
                disabled={isSubmitting || !newResponse[selectedQuery.uuid]?.trim()}
              >
                <Send size={16} />
                {isSubmitting ? 'Sending...' : 'Send Response'}
              </button>
            </div>
          )}
        </div>
        {renderResolveModal()}
      </motion.div>
    );
  };

  const handleTabChange = (tab: 'active' | 'resolved') => {
    setActiveTab(tab);
    setSelectedQuery(null);
  };

  // Define the filters for NurtifyFilter
  const filters: NurtifyFilterItem[] = [
    {
      label: 'Priority',
      type: 'checkbox',
      options: [
        { label: 'High', value: 'high' },
        { label: 'Medium', value: 'medium' },
        { label: 'Low', value: 'low' },
      ],
      value: selectedPriorities,
      onChange: setSelectedPriorities as any,
    },
    
  ];

  return (
    <motion.div
      className="forms-queries-container"
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      transition={{ duration: 0.5 }}
    >
      <div className="forms-queries-header">
        <h2 className="forms-queries-title">Forms Queries</h2>

        <div className="query-tabs">
          <button
            className={`query-tab ${activeTab === 'active' ? 'active' : ''}`}
            onClick={() => handleTabChange('active')}
          >
            <ListChecks size={16} />
            Active Queries
          </button>
          <button
            className={`query-tab ${activeTab === 'resolved' ? 'active' : ''}`}
            onClick={() => handleTabChange('resolved')}
          >
            <Archive size={16} />
            Resolved Queries
          </button>
        </div>
      </div>

      {queries.length === 0 && selectedPriorities.length === 0 ? (
        <div className="forms-queries-empty">
          <MessageSquare size={48} />
          <p>
            {activeTab === 'active'
              ? `No active queries found for ${selectedPatient.first_name} ${selectedPatient.last_name}`
              : `No resolved queries found for ${selectedPatient.first_name} ${selectedPatient.last_name}`}
          </p>
        </div>
      ) : selectedQuery ? (
        renderQueryDetail()
      ) : (
        <div className="queries-table-container">
          <div style={{ padding: "20px" }}>
            <NurtifyFilter layout="horizontal" filters={filters} />
          </div>

          <table className="queries-table">
            <thead>
              <tr>
                <th>
                  <Hash size={14} /> Question #
                </th>
                <th>
                  <FileText size={14} /> Form Name
                </th>
                <th>
                  <FileText size={14} /> Description
                </th>
                <th>
                  <AlertCircle size={14} /> Priority
                </th>
                <th>
                  <Calendar size={14} /> Created Date
                </th>
                <th>
                  <Tag size={14} /> Status
                </th>
                <th>Actions</th>
              </tr>
            </thead>
            <tbody>
              {queries.length === 0 ? (
                <tr>
                  <td colSpan={7} className="no-filtered-queries">
                    No queries match the selected filters.
                  </td>
                </tr>
              ) : (
                queries.map((query: FormSubmissionQuery) => (
                  <tr key={query.uuid} className="query-row">
                    <td>{query.question_number}</td>
                    <td>{query.form_submission?.form_name || 'Unknown Form'}</td>
                    <td>{query.description}</td>
                    <td>
                      <span className={`priority-badge priority-${query.priority}`}>
                        <AlertCircle size={14} />
                        {query.priority}
                      </span>
                    </td>
                    <td>{formatDate(query.created_at)}</td>
                    <td>
                      <span
                        className={`status-badge ${query.is_resolved ? 'resolved' : 'pending'}`}
                      >
                        {query.is_resolved ? (
                          <>
                            <CheckCircle2 size={14} />
                            Resolved
                          </>
                        ) : (
                          <>
                            <Clock size={14} />
                            Pending
                          </>
                        )}
                      </span>
                    </td>
                    <td>
                      <div className="query-actions">
                        <button
                          className="view-query-btn"
                          onClick={() => setSelectedQuery(query)}
                        >
                          <Eye size={16} />
                          View Details
                        </button>
                        {query.form_submission?.uuid && (
                          <button
                            className="preview-form-btn"
                            onClick={() => handlePreviewForm(query.form_submission.uuid)}
                          >
                            <ExternalLink size={16} />
                            Preview Form
                          </button>
                        )}
                      </div>
                    </td>
                  </tr>
                ))
              )}
            </tbody>
          </table>
          <br />
        </div>
      )}

      {/* Form Submission Preview Modal */}
      {previewSubmission && (
        <div className="preview-modal-overlay">
          <div className="preview-modal-content">
            {isLoadingPreview ? (
              <div className="preview-loading">
                <div className="loading-spinner"></div>
                <p>Loading form submission...</p>
              </div>
            ) : previewError ? (
              <div className="preview-error">
                <AlertCircle size={24} />
                <p>{previewError}</p>
                <button
                  className="retry-button"
                  onClick={() => handlePreviewForm(previewSubmission.uuid)}
                >
                  Retry
                </button>
              </div>
            ) : (
              <FormSubmissionPreview
                submission={previewSubmission}
                onClose={handleClosePreview}
                hideRaiseQuery={true}
              />
            )}
          </div>
        </div>
      )}
    </motion.div>
  );
};

export default FormsQueries;

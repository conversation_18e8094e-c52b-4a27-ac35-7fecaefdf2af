import React, { useState, useEffect } from 'react';
import { X, AlertCircle, Save } from 'lucide-react';
import { v4 as uuidv4 } from 'uuid';
import { Visit, Test } from "@/store/scheduleEventState";
import NurtifyCheckBox from "../NurtifyCheckBox";
import { useCurrentUserQuery } from "@/hooks/user.query";

interface UnscheduledVisitModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSave: (visitData: Partial<Visit>) => void;
  availableTests: Test[];
  studyId: string;
}

const UnscheduledVisitModal: React.FC<UnscheduledVisitModalProps> = ({
  isOpen,
  onClose,
  onSave,
  availableTests,
  studyId
}) => {
  // Get current user to use their ID for the patient field
  const { data: currentUser } = useCurrentUserQuery();
  
  const [visitData, setVisitData] = useState<Partial<Visit>>({
    id: '',
    name: '',
    dayNumber: 0,
    date: new Date().toISOString().split('T')[0],
    status: 'planned',
    reminderDays: [7, 1],
    comments: '',
    tests: [],
    isUnscheduled: true,
    reason: '',
    study_uuid: studyId,
    referredBy: '',
    referredDateTime: new Date().toISOString(),
    commentDateTime: new Date().toISOString()
  });

  const [selectedTests, setSelectedTests] = useState<string[]>([]);
  const [reminderDaysInput, setReminderDaysInput] = useState<string>("7, 1");

  // Reset form when modal opens
  useEffect(() => {
    if (isOpen) {
      setVisitData({
        id: uuidv4(),
        name: 'Unscheduled Visit',
        dayNumber: 0,
        date: new Date().toISOString().split('T')[0],
        status: 'planned',
        reminderDays: [7, 1],
        comments: '',
        tests: [],
        isUnscheduled: true,
        reason: '',
        study_uuid: studyId,
        patient: currentUser?.id || 0,
        referredBy: '',
        referredDateTime: new Date().toISOString(),
        commentDateTime: new Date().toISOString()
      });
      setSelectedTests([]);
      setReminderDaysInput("7, 1");
    }
  }, [isOpen, studyId, currentUser]);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    
    if (name === 'status') {
      // Ensure status is one of the allowed values
      const statusValue = value as 'planned' | 'on-going' | 'completed' | 'cancelled';
      setVisitData({
        ...visitData,
        status: statusValue
      });
    } else if (name === "dayNumber" || name === "windowDays") {
      // Handle numeric inputs
      setVisitData({
        ...visitData,
        [name]: value === "" ? undefined : parseInt(value, 10),
      });
    } else {
      setVisitData({
        ...visitData,
        [name]: value
      });
    }
  };

  const handleReminderDaysChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { value } = e.target;
    setReminderDaysInput(value);

    // Parse comma-separated values into an array of numbers
    const days = value
      .split(",")
      .map((day) => parseInt(day.trim(), 10))
      .filter((day) => !isNaN(day));

    setVisitData({
      ...visitData,
      reminderDays: days,
    });
  };

  const handleTestSelection = (testId: string) => {
    if (selectedTests.includes(testId)) {
      setSelectedTests(selectedTests.filter((id) => id !== testId));
    } else {
      setSelectedTests([...selectedTests, testId]);
    }
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    // Add selected tests to visit data
    const testsToAdd = availableTests.filter(test => selectedTests.includes(test.id));
    
    const finalVisitData = {
      ...visitData,
      tests: testsToAdd,
      commentDateTime: new Date().toISOString(),
      patient: visitData.patient || (currentUser?.id || 0)
    };
    
    onSave(finalVisitData);
    onClose();
  };

  if (!isOpen) return null;

  return (
    <div className="visit-modal-overlay">
      <div className="visit-modal">
        <div className="visit-modal-header">
          <h2 className="visit-modal-title">Add Unscheduled Visit</h2>
          <button className="visit-modal-close" onClick={onClose}>
            <X size={24} />
          </button>
        </div>
        
        <form onSubmit={handleSubmit}>
          <div className="visit-modal-body">
            <div className="unscheduled-info">
              <AlertCircle size={20} />
              <small>
                Unscheduled visits are visits that occur outside the planned study schedule, 
                often due to adverse events, patient requests, or other unexpected circumstances.
              </small>
            </div>
            
            <div className="form-group">
              <label htmlFor="name">Visit Name</label>
              <input
                type="text"
                id="name"
                name="name"
                value={visitData.name}
                onChange={handleInputChange}
                required
              />
            </div>
            
            <div className="form-group">
              <label htmlFor="date">Visit Date</label>
              <input
                type="date"
                id="date"
                name="date"
                value={visitData.date}
                onChange={handleInputChange}
                required
              />
            </div>
            
            <div className="form-group">
              <label htmlFor="reason">Reason for Unscheduled Visit</label>
              <textarea
                id="reason"
                name="reason"
                value={visitData.reason}
                onChange={handleInputChange}
                placeholder="Explain why this unscheduled visit is needed"
                rows={3}
              />
            </div>
            
            <div className="form-group">
              <label htmlFor="status">Status</label>
              <select
                id="status"
                name="status"
                value={visitData.status}
                onChange={handleInputChange}
              >
                <option value="planned">Planned</option>
                <option value="on-going">On-going</option>
                <option value="completed">Completed</option>
                <option value="cancelled">Cancelled</option>
              </select>
            </div>
            
            <div className="form-row">
              <div className="form-group">
                <label htmlFor="referredBy">Referred By</label>
                <input
                  type="text"
                  id="referredBy"
                  name="referredBy"
                  value={visitData.referredBy || ""}
                  onChange={handleInputChange}
                  placeholder="Who referred this unscheduled visit"
                />
                <small>Name of the person who referred this visit</small>
              </div>

              <div className="form-group">
                <label htmlFor="referredDateTime">Referral Date</label>
                <input
                  type="datetime-local"
                  id="referredDateTime"
                  name="referredDateTime"
                  value={visitData.referredDateTime ? visitData.referredDateTime.substring(0, 16) : ""}
                  onChange={handleInputChange}
                  placeholder="When the referral was made"
                />
                <small>Date and time when the referral was made</small>
              </div>
            </div>

            <div className="form-group">
              <label htmlFor="comments">Comments</label>
              <textarea
                id="comments"
                name="comments"
                value={visitData.comments}
                onChange={handleInputChange}
                placeholder="Additional notes about this visit"
                rows={3}
              />
            </div>
            
            <div className="form-group">
              <label htmlFor="reminderDays">
                Reminder Days (comma-separated)
              </label>
              <input
                type="text"
                id="reminderDays"
                name="reminderDays"
                value={reminderDaysInput}
                onChange={handleReminderDaysChange}
                placeholder="e.g., 7, 3, 1"
              />
              <small>Days before the visit to send reminders</small>
            </div>

            <div className="form-group">
              <label>Tests to Perform</label>
              <div className="tests-selection">
                {availableTests.map((test) => (
                  <div key={test.id} className="test-checkbox">
                    <NurtifyCheckBox
                      key={test.id}
                      label={test.name}
                      value={test.id}
                      checked={selectedTests.includes(test.id)}
                      onChange={() => handleTestSelection(test.id)}
                      description={test.description}
                    />
                  </div>
                ))}
              </div>
            </div>
          </div>
          
          <div className="visit-modal-footer">
            <button type="button" className="cancel-btn" onClick={onClose}>
              Cancel
            </button>
            <button type="submit" className="btn btn-primary-nurtify">
              <Save size={16} /> Save Unscheduled Visit
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default UnscheduledVisitModal;

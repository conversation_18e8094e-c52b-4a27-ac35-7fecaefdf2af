import { ChangeEvent, KeyboardEvent, useState } from "react";
import NurtifyInput from "@/components/NurtifyInput";
import NurtifyRadio from "@/components/NurtifyRadio";
import { X } from "lucide-react";
import useHolisticFormStore from "@/store/holisticFormState";
import useHolisticFormTabStore from "@/store/holisticFormTabState";
import { MEDICAL_INTERVENTIONS } from "./constants";
import { useCreateHolisticFormSubmission, useGenerateHolisticFormPDF } from "@/hooks/form.query";

type RecommendationField =
  | "medicalInterventions"
  | "nursingImpressions"
  | "UpcomingPlans";

interface SearchInputs {
  medicalIntervention: string;
  impression: string;
  upcomingPlan: string;
}

export default function Recommendation() {
  const { 
    recommendations, 
    setRecommendations, 
    assessment, 
    background, 
    patientDetails, 
    situation, 
    matchedPastMedicalHistoryOptions, 
    whenSymptomsStarted 
  } = useHolisticFormStore();
  const { goToPrevious } = useHolisticFormTabStore();
  
  const { 
    mutate: createMutation, 
    isPending: createIsPending 
  } = useCreateHolisticFormSubmission();
  
  const { 
    mutate: generatePDFMutation, 
    isPending: generatePdfIsPending 
  } = useGenerateHolisticFormPDF();

  const [searchInputs, setSearchInputs] = useState<SearchInputs>({
    medicalIntervention: "",
    impression: "",
    upcomingPlan: "",
  });
  const [matchedMedicalInterventions, setMatchedMedicalInterventions] = useState<string[]>([]);
  const [formUuid, setFormUuid] = useState<string | null>(null);
  const [error, setError] = useState<string | null>(null);

  // Handle search input changes
  const handleSearchInput = (field: keyof SearchInputs) => (e: ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setSearchInputs((prev) => ({ ...prev, [field]: value }));

    if (field === "medicalIntervention") {
      const matchedOptions = MEDICAL_INTERVENTIONS.filter((option) =>
        option.toLowerCase().includes(value.toLowerCase())
      );
      setMatchedMedicalInterventions(matchedOptions);
    }
  };

  // Add new item to any list
  const handleAddItem = (field: RecommendationField, value: string) => {
    if (!value.trim()) return;

    setRecommendations({
      ...recommendations,
      [field]: [...recommendations[field], value],
    });

    const searchField =
      field === "medicalInterventions"
        ? "medicalIntervention"
        : field === "nursingImpressions"
        ? "impression"
        : "upcomingPlan";
    setSearchInputs((prev) => ({ ...prev, [searchField]: "" }));
  };

  // Remove item from any list
  const handleRemoveItem = (field: RecommendationField, indexToRemove: number) => {
    setRecommendations({
      ...recommendations,
      [field]: recommendations[field].filter((_, index) => index !== indexToRemove),
    });
  };

  // Handle keyboard events
  const handleKeyDown = (field: "nursingImpressions" | "UpcomingPlans") => 
    (e: KeyboardEvent<HTMLInputElement>) => {
      if (e.key === "Enter") {
        e.preventDefault();
        const value = e.currentTarget.value;
        handleAddItem(field, value);
      }
    };

  // Handle medical intervention selection
  const handleMedicalInterventionSelect = (intervention: string) => {
    if (!recommendations.medicalInterventions.includes(intervention)) {
      handleAddItem("medicalInterventions", intervention);
    }
    setSearchInputs((prev) => ({ ...prev, medicalIntervention: "" }));
    setMatchedMedicalInterventions([]);
  };

  // Handle doctor status change
  const handleDoctorStatusChange = (value: string) => {
    setRecommendations({
      ...recommendations,
      seenByDoctorStatus: value,
    });
  };

  const doctorStatusOptions = [
    "Awaiting to be seen by ED",
    "Seen by ED doctor(Not Referred)",
    "Referred to specialist",
    "Seen by specialist",
    "Other",
  ] as const;

  const handleSubmition = () => {
    setError(null);
    const holisticFormData = {
      assessment,
      background,
      patientDetails,
      situation,
      recommendations,
      matched_past_medical_history_options: matchedPastMedicalHistoryOptions,
      when_symptoms_started: whenSymptomsStarted
    };

    createMutation(holisticFormData, {
      onSuccess: (data) => {
        setFormUuid(data.uuid);
      },
      onError: (error: Error) => {
        setError('Submission failed: ' + error.message);
      }
    });
  };

  
  const handleGeneratePDF = () => {
    if (!formUuid) return;
    setError(null);
  
    generatePDFMutation(formUuid, {
      onSuccess: (pdfResponse) => {
        const blob = pdfResponse.data; 
        const url = window.URL.createObjectURL(blob);
        window.open(url, '_blank'); 
        setTimeout(() => {
          window.URL.revokeObjectURL(url);
        }, 5000); 
        console.log('PDF opened in new tab');
      },
      onError: (error: Error) => {
        setError('PDF generation failed: ' + error.message);
      }
    });
  };

  return (
    <div className="tabs__pane -tab-item-1">
      <div id="RecommendationSection" className="formHeadings">
        {error && (
          <div className="p-2 mb-4 bg-red-100 text-red-700 rounded">
            {error}
          </div>
        )}

        {/* Medical Interventions Section */}
        <section className="block p-4 gap-5">
          <h3 className="headinqQuestion mb-4">
            Completed Nursing / Medical Intervention
          </h3>
          <div className="d-flex flex-column gap-3 mt-3">
            <NurtifyInput
              type="text"
              placeholder="Search medical interventions..."
              value={searchInputs.medicalIntervention}
              onChange={handleSearchInput("medicalIntervention")}
            />

            <div className="d-flex flex-wrap gap-2">
              {recommendations.medicalInterventions.map((item, index) => (
                <div
                  key={index}
                  className="badge d-flex align-items-center gap-2 p-2"
                  style={{ backgroundColor: "#112D4E" }}
                >
                  {item}
                  <X
                    size={16}
                    className="rounded-4"
                    style={{
                      backgroundColor: "white",
                      color: "#112D4E",
                      cursor: "pointer",
                    }}
                    onClick={() => handleRemoveItem("medicalInterventions", index)}
                  />
                </div>
              ))}
            </div>

            {searchInputs.medicalIntervention && matchedMedicalInterventions.length > 0 && (
              <div className="mt-2">
                {matchedMedicalInterventions.map((option, index) => (
                  <div
                    key={index}
                    className="form-check cursor-pointer py-1 hover:bg-gray-100"
                    onClick={() => handleMedicalInterventionSelect(option)}
                  >
                    <input
                      type="checkbox"
                      className="form-check-input"
                      checked={recommendations.medicalInterventions.includes(option)}
                      readOnly
                    />
                    <label className="form-check-label ml-2">{option}</label>
                  </div>
                ))}
              </div>
            )}
          </div>
        </section>

        {/* Nursing Impressions Section */}
        <section className="block p-4 gap-5">
          <h3 className="headinqQuestion mb-4">
            Impression / Nursing Diagnosis (current visit)
          </h3>
          <div className="d-flex flex-column gap-3">
            <NurtifyInput
              type="text"
              placeholder="Add nursing impression..."
              value={searchInputs.impression}
              onChange={handleSearchInput("impression")}
              onKeyDown={handleKeyDown("nursingImpressions")}
            />
            <div className="d-flex flex-wrap gap-2">
              {recommendations.nursingImpressions.map((item, index) => (
                <div
                  key={index}
                  className="badge d-flex align-items-center gap-2 p-2"
                  style={{ backgroundColor: "#112D4E" }}
                >
                  {item}
                  <X
                    size={16}
                    className="rounded-4"
                    style={{
                      backgroundColor: "white",
                      color: "#112D4E",
                      cursor: "pointer",
                    }}
                    onClick={() => handleRemoveItem("nursingImpressions", index)}
                  />
                </div>
              ))}
            </div>
          </div>
        </section>

        {/* Doctor Status Section */}
        <section className="block p-4 gap-5">
          <h3 className="headinqQuestion mb-4">
            Has the patient been seen by a doctor?
          </h3>
          <div className="d-flex flex-column gap-2">
            {doctorStatusOptions.map((status) => (
              <NurtifyRadio
                key={status}
                label={status}
                name="doctorStatus"
                value={status}
                checked={recommendations.seenByDoctorStatus === status}
                onChange={() => handleDoctorStatusChange(status)}
              />
            ))}
          </div>
        </section>

        {/* Upcoming Plans Section */}
        <section className="block p-4 gap-5">
          <h3 className="headinqQuestion mb-4">Upcoming Plans</h3>
          <div className="d-flex flex-column gap-3">
            <NurtifyInput
              type="text"
              placeholder="Add upcoming plan..."
              value={searchInputs.upcomingPlan}
              onChange={handleSearchInput("upcomingPlan")}
              onKeyDown={handleKeyDown("UpcomingPlans")}
            />
            <div className="d-flex flex-wrap gap-2">
              {recommendations.UpcomingPlans.map((item, index) => (
                <div
                  key={index}
                  className="badge d-flex align-items-center gap-2 p-2"
                  style={{ backgroundColor: "#112D4E" }}
                >
                  {item}
                  <X
                    size={16}
                    className="rounded-4"
                    style={{
                      backgroundColor: "white",
                      color: "#112D4E",
                      cursor: "pointer alinh",
                    }}
                    onClick={() => handleRemoveItem("UpcomingPlans", index)}
                  />
                </div>
              ))}
            </div>
          </div>
        </section>
      </div>

      <div className="col-12 mt-60">
        <button
          className="button -md btn-nurtify-lighter"
          style={{ width: "100px" }}
          onClick={goToPrevious}
          disabled={createIsPending || generatePdfIsPending}
        >
          Prev
        </button>

        {!formUuid ? (
          <button
            onClick={handleSubmition}  // Fixed typo: handleSubmition -> handleSubmission
            disabled={createIsPending}
            className="button -md btn-nurtify text-white"
            style={{ width: "120px" }}
          >
            {createIsPending ? (
              <>
                <svg className="animate-spin -ml-1 mr-2 h-5 w-5 text-white" fill="none" viewBox="0 0 24 24">
                  <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4" />
                  <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z" />
                </svg>
                Submitting
              </>
            ) : (
              'Submit'
            )}
          </button>
        ) : (
          <button
            onClick={handleGeneratePDF}
            disabled={generatePdfIsPending}
            className="button -md btn-nurtify text-white flex items-center justify-center"
            style={{ width: "120px" }}
          >
            {generatePdfIsPending ? (
              <>
                <svg className="animate-spin -ml-1 mr-2 h-5 w-5 text-white" fill="none" viewBox="0 0 24 24">
                  <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4" />
                  <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z" />
                </svg>
                Generating
              </>
            ) : (
              'Generate PDF'
            )}
          </button>
        )}
      </div>
    </div>
  );
}
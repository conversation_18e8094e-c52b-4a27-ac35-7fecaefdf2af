import { Loader2 } from "lucide-react";

interface ConfirmationModalProps {
    isOpen: boolean;
    onClose: () => void;
    onConfirm: () => void;
    title: string;
    message: string;
    subMessage?: string;
    isLoading?: boolean;
}

export default function ConfirmationDeleteSnippetModal({
    isOpen,
    onClose,
    onConfirm,
    title,
    message,
    subMessage,
    isLoading = false
}: ConfirmationModalProps) {
    if (!isOpen) return null;

    return (
        <div style={{
            position: 'fixed',
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            backgroundColor: 'rgba(0, 0, 0, 0.5)',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            zIndex: 1000
        }}>
            <div style={{
                backgroundColor: '#E8F4F5',
                borderRadius: '16px',
                padding: '32px',
                width: '90%',
                maxWidth: '500px',
                boxShadow: '0 4px 6px rgba(0, 0, 0, 0.1)',
            }}>
                <h2 style={{
                    color: '#1B2559',
                    fontSize: '24px',
                    fontWeight: '600',
                    marginBottom: '16px'
                }}>
                    {title}
                </h2>
                <p style={{
                    color: '#1B2559',
                    fontSize: '16px',
                    marginBottom: '8px'
                }}>
                    {message}
                </p>
                {subMessage && (
                    <p style={{
                        color: '#64748B',
                        fontSize: '14px',
                        marginBottom: '24px'
                    }}>
                        {subMessage}
                    </p>
                )}
                <div style={{
                    display: 'flex',
                    justifyContent: 'flex-end',
                    gap: '12px',
                    marginTop: '24px'
                }}>
                    <button
                        onClick={onClose}
                        disabled={isLoading}
                        style={{
                            padding: '12px 24px',
                            borderRadius: '8px',
                            border: 'none',
                            backgroundColor: '#94A3B8',
                            color: 'white',
                            cursor: isLoading ? 'not-allowed' : 'pointer',
                            fontSize: '14px',
                            fontWeight: '500',
                            opacity: isLoading ? 0.7 : 1
                        }}
                    >
                        Cancel
                    </button>
                    <button
                        onClick={onConfirm}
                        disabled={isLoading}
                        style={{
                            padding: '12px 24px',
                            borderRadius: '8px',
                            border: 'none',
                            backgroundColor: '#d70909',
                            color: 'white',
                            cursor: isLoading ? 'not-allowed' : 'pointer',
                            fontSize: '14px',
                            fontWeight: '500',
                            display: 'flex',
                            alignItems: 'center',
                            gap: '8px'
                        }}
                    >
                        {isLoading ? (
                            <>
                                <Loader2 size={16} className="animate-spin" />
                                Deleting...
                            </>
                        ) : (
                            'Proceed'
                        )}
                    </button>
                </div>
            </div>
        </div>
    );
}

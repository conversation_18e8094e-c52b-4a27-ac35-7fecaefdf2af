import api from "@/services/api";
import type { Admin, User, UserCreateData } from "@/types/types";

export const getUserById = async (uuid: string) => {
  const { data } = await api.get(`user/users/${uuid}/`);
  return data;
};

// Get user by numeric ID (for delegation logs)
export const getUserByNumericId = async (numericId: number) => {
  const { data } = await api.get(`user/users/by-id/${numericId}/`);
  return data;
};

export const getAnalytics = async () => {
  try {
    const response = await api.get("user/super-user/dashboard/");
    return response.data;
  } catch (error) {
    console.error("Error fetching analytics:", error);
    throw error;
  }
};

export const getAnalyticsHospitalAdmin = async (hospitalUuid: string) => {
  try {
    const response = await api.get(`user/hospital-admin/dashboard/${hospitalUuid}/`);
    return response.data;
  } catch (error) {
    console.error("Error fetching analytics Hospital Admin:", error);
    throw error;
  }
};
// Hospital  admin dashboard : Start

export const getPatientsForHospitalDashboard = async (hospitalUuid: string, period?: string) => {
  try {
    const url = period ? `user/hospital-admin-dashboard/patients/${hospitalUuid}/?period=${period}` : `user/hospital-admin-dashboard/patients/${hospitalUuid}/`;
    const response = await api.get(url);
    return response.data;
  } catch (error) {
    console.error("Error fetching patients analytics hospital Admin:", error);
    throw error;
  }
};

export const getPoliciesForHospitalDashboard = async (hospitalUuid: string, period?: string) => {
  try {
    const url = period ? `user/hospital-admin-dashboard/policies/${hospitalUuid}/?period=${period}` : `user/hospital-admin-dashboard/policies/${hospitalUuid}/`;
    const response = await api.get(url);
    return response.data;
  } catch (error) {
    console.error("Error fetching policies analytics hospital Admin:", error);
    throw error;
  }
};

export const getSponsorsForHospitalDashboard = async (hospitalUuid: string, period?: string) => {
  try {
    const url = period ? `user/hospital-admin-dashboard/sponsors/${hospitalUuid}/?period=${period}` : `user/hospital-admin-dashboard/sponsors/${hospitalUuid}/`;
    const response = await api.get(url);
    return response.data;
  } catch (error) {
    console.error("Error fetching sponsors analytics hospital Admin:", error);
    throw error;
  }
};

export const getStaffForHospitalDashboard = async (hospitalUuid: string, period?: string) => {
  try {
    const url = period ? `user/hospital-admin-dashboard/staff/${hospitalUuid}/?period=${period}` : `user/hospital-admin-dashboard/staff/${hospitalUuid}/`;
    const response = await api.get(url);
    return response.data;
  } catch (error) {
    console.error("Error fetching staff analytics hospital Admin:", error);
    throw error;
  }
};

export const getStudiesForHospitalDashboard = async (hospitalUuid: string) => {
  try {
    const response = await api.get(`user/hospital-admin-dashboard/studies-invitations/${hospitalUuid}/`);
    return response.data;
  } catch (error) {
    console.error("Error fetching studies analytics hospital Admin:", error);
    throw error;
  }
};

export const getDepartmentsForHospitalDashboard = async (hospitalUuid: string) => {
  try {
    const response = await api.get(`user/hospital-admin-dashboard/departments/${hospitalUuid}/`);
    return response.data;
  } catch (error) {
    console.error("Error fetching analytics hospital Admin:", error);
    throw error;
  }
};

export const getRequestsForHospitalDashboard = async (hospitalUuid: string) => {
  try {
    const response = await api.get(`user/hospital-admin-dashboard/requests/${hospitalUuid}/`);
    return response.data;
  } catch (error) {
    console.error("Error fetching requests analytics hospital Admin:", error);
    throw error;  
  }
};

// Hospital  admin dashboard : End

// Department admin dashboard : Start

export const getPatientsForDepartmentDashboard = async (departmentUuid: string, period?: string) => {
  try {
    const url = period ? `user/department-admin-dashboard/patients/${departmentUuid}/?period=${period}` : `user/department-admin-dashboard/patients/${departmentUuid}/`;
    const response = await api.get(url);
    return response.data;
  } catch (error) {
    console.error("Error fetching patients analytics Department Admin:", error);
    throw error;
  }
};

export const getPoliciesForDepartmentDashboard = async (departmentUuid: string, period?: string) => {
  try {
    const url = period ? `user/department-admin-dashboard/policies/${departmentUuid}/?period=${period}` : `user/department-admin-dashboard/policies/${departmentUuid}/`;
    const response = await api.get(url);
    return response.data;
  } catch (error) {
    console.error("Error fetching policies analytics Department Admin:", error);
    throw error;
  }
};

export const getSponsorsForDepartmentDashboard = async (departmentUuid: string, period?: string) => {
  try {
    const url = period ? `user/department-admin-dashboard/sponsors/${departmentUuid}/?period=${period}` : `user/department-admin-dashboard/sponsors/${departmentUuid}/`;
    const response = await api.get(url);
    return response.data;
  } catch (error) {
    console.error("Error fetching sponsors analytics Department Admin:", error);
    throw error;
  }
};

export const getStaffForDepartmentDashboard = async (departmentUuid: string, period?: string) => {
  try {
    const url = period ? `user/department-admin-dashboard/staff/${departmentUuid}/?period=${period}` : `user/department-admin-dashboard/staff/${departmentUuid}/`;
    const response = await api.get(url);
    return response.data;
  } catch (error) {
    console.error("Error fetching staff analytics Department Admin:", error);
    throw error;
  }
};

export const getStudiesForDepartmentDashboard = async (departmentUuid: string) => {
  try {
    const response = await api.get(`user/department-admin-dashboard/studies-invitations/${departmentUuid}/`);
    return response.data;
  } catch (error) {
    console.error("Error fetching studies analytics Department Admin:", error);
    throw error;
  }
};

export const getAnalyticsDepartmentAdmin = async (departmentUuid: string) => {
  try {
    const response = await api.get(`user/department-admin/dashboard/${departmentUuid}/`);
    return response.data;
    console.log("response", response.data);
  } catch (error) {
    console.error("Error fetching analytics Department Admin:", error);
    throw error;
  }
};

export const getRequestsForDepartmentDashboard = async (departmentUuid: string) => {
  try {
    const response = await api.get(`user/department-admin-dashboard/requests/${departmentUuid}/`);
    return response.data;
  } catch (error) {
    console.error("Error fetching requests analytics Department Admin:", error);
    throw error;  
  }
};

// Department admin dashboard : End

export const getUsersByDepartment = async (departmentUuid: string) => {
  const response = await api.get(`user/users/department/${departmentUuid}/`);
  const { users } = response.data;
  return users;
};

export const createUserDepartment = async (userData: UserCreateData) => {
  const { data } = await api.post("/user/department/register/", userData);
  return data;
};

export const createUser = async (UserData: FormData) => {
  const { data } = await api.post("user/department/register/", UserData);
  return data;
};

export const createDepartmentAdmin = async (UserData: FormData) => {
  const { data } = await api.post("user/department/admin/register/", UserData);
  return data;
};

export const createHospitalAdmin = async (UserData: FormData) => {
  const { data } = await api.post("user/hospital/admin/register/", UserData);
  return data;
};


export const getCurrentUser = async () => {
  const { data } = await api.get("user/users/me/");
  return data;
};

export const deleteUserByUuid = async (uuid: string) => {
  const { data } = await api.delete(`user/users/${uuid}/delete/`);
  return data;
};

export const updateUser = async (uuid: string, userData: Partial<User>) => {
  const response = await api.patch(`user/users/${uuid}/edit/`, userData);
  return response.data;
};

export const getAllUsers = async () => {
  try {
    const response = await api.get("user/users/");
    return response.data;
  } catch (error) {
    console.error("Error fetching users:", error);
    throw error;
  }
};

export const activateAccount = async (
  uid: string,
  token: string,
  current_password: string,
  password: string,
  confirm_password: string
) => {
  try {
    const { data } = await api.post("/user/users/activate/", {
      uid,
      token,
      current_password: current_password,
      password: password,
      confirm_password: confirm_password,
    });
    return data;
     
  } catch (error: any) {
    console.error("Activation error:", error.response?.data || error.message);
    throw error;
  }
};

// Réinitialiser le mot de passe
export const requestResetPassword = async (email: string) => {
  try {
    const response = await api.post("/user/users/password-reset-request/", { email, host:import.meta.env.VITE_APP_MODE });
    return response.data;
  } catch (error) {
    console.error("Reset password error:", error);
    throw error;
  }
};

export const resetPassword = async (uid: string, token: string, password: string, confirm_password: string) => {
  try {
    const response = await api.post("/user/users/password-reset/", { uid, token, password, confirm_password });
    return response.data;
  } catch (error) {
    console.error("Reset password error:", error);
    throw error;
  }
};

export const loginUser = async (email: string, password: string) => {
  const response = await api.post("/user/token/", { email, password });
  return response.data;
};

export const verifyOtp = async (email: string, password: string,otp_code: string) => {
  const response = await api.post("/user/verify-otp/", { email, password, otp_code });
  return response.data;
};

export const getDepartmentAdmins = async (departmentUuid: string) => {
  const response = await api.get(`user/users/department-admins/${departmentUuid}/`);
  
  return response.data.admins;
};

export const getHospitalAdmin = async (hospitalUuid: string) => {
  const response = await api.get(`user/users/hospital-admin/${hospitalUuid}/`);
  
  return response.data.admins;
};

export const resendActivationEmail = async (identifier: string) => {
  const response = await api.post('/user/users/resend-activation-mail/', { identifier });
  return response.data;
};

export const deleteAdmin = async (identifier: string) => {
  try {
      await api.delete(`/user/users/${identifier}/delete/`)
      
  } catch (error) {
      console.error("Error deleting admin:", error);
      throw error;
  }
}

export const resendOtp = async (email: string, password: string) => {
  try {
    const response = await api.post("/user/token/", { email, password });
    return response.data;
  } catch (error) {
    console.error("Error resending OTP:", error);
    throw error;
  }
}

export const getSponsorListByOrg = async (uuid: string): Promise<Admin[]> => {
  const response = await api.get(`/user/users/sponsor/get-list-by-org/?uuid=${uuid}`);
  return response.data;
};

export const createSponsor = async (data: FormData): Promise<Admin> => {
  const response = await api.post("/user/sponsor/register/", data);
  return response.data;
};
export const getAnalyticsSponsor = async (globalStudyId?: string) => {
  try {
    const endpoint = globalStudyId
    ? `user/sponsor/kpi/${globalStudyId}/`
    : 'user/sponsor/kpi/';
    const response = await api.get(endpoint);
    return response.data;
  } catch (error) {
    console.error("Error fetching analytics Sponsor:", error);
    throw error;
  }
};

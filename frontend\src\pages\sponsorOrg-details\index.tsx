import "./sponsor-org.css";
import { useEffect, useState } from "react";
import { useParams } from "react-router-dom";

import SponsorOrgDetails from "./SponsorOrgDetails";
import SponsorOrgAdmins from "./SponsorOrgAdmins";

import { Users } from "lucide-react";

type TabName = "orgDetails" | "orgAdmins";

export default function SponsorOrgDetailsAndAdmins() {
  const { uuid, tab } = useParams<{ uuid: string; tab?: TabName }>();
  const [activeTab, setActiveTab] = useState<TabName>(tab || "orgDetails");

  useEffect(() => {
    if (tab && (tab === "orgDetails" || tab === "orgAdmins")) {
      setActiveTab(tab);
    }
  }, [tab]);

  return (
    <div className="sponsor-org__content">
      <div className="sponsor-card">
        <div className="sponsor-header">
          <div className="sponsor-title">
            <h1>
              <Users size={24} style={{ marginRight: "10px" }} />
              Sponsor Organization Details and Admins
            </h1>
          </div>
        </div>

        <div className="sponsor-tabs-wrapper">
          <button
            className={`sponsor-tab-button ${activeTab === "orgDetails" ? "active" : ""}`}
            onClick={() => setActiveTab("orgDetails")}
          >
            Organization Details
          </button>
          <button
            className={`sponsor-tab-button ${activeTab === "orgAdmins" ? "active" : ""}`}
            onClick={() => setActiveTab("orgAdmins")}
          >
            Organization Admins
          </button>
        </div>

        <div className="sponsor-form-section">
          {activeTab === "orgDetails" && <SponsorOrgDetails uuid={uuid!} />}
          {activeTab === "orgAdmins" && <SponsorOrgAdmins uuid={uuid!} />}
        </div>
      </div>
    </div>
  );
}

import api from "@/services/api.ts";
import {
  ConsentForm,
  ConsentFormCreate,
  ConsentFormUpdate,
  ConsentFormResponse,
  PatientConsent,
  PatientConsentCreate,
  PatientConsentResponse,
  ConsentQuestion,
  PatientConsentAnswerCreate,
} from "@/types/types";

// Consent Form Management
export const createConsentForm = async (data: ConsentFormCreate): Promise<ConsentForm> => {
  const response = await api.post("/consent/forms/", data);
  return response.data;
};

export const getConsentForms = async (params?: {
  sponsor_id?: string;
  study_id?: string;
  is_active?: boolean;
  page?: number;
  page_size?: number;
}): Promise<ConsentFormResponse> => {
  const queryParams = new URLSearchParams();
  if (params?.sponsor_id) queryParams.append("sponsor_id", params.sponsor_id);
  if (params?.study_id) queryParams.append("study_id", params.study_id);
  if (params?.is_active !== undefined) queryParams.append("is_active", params.is_active.toString());
  if (params?.page) queryParams.append("page", params.page.toString());
  if (params?.page_size) queryParams.append("page_size", params.page_size.toString());

  const response = await api.get(`/consent/forms/${queryParams.toString() ? `?${queryParams.toString()}` : ""}`);
  return response.data;
};

export const getConsentFormByUuid = async (uuid: string): Promise<ConsentForm> => {
  const response = await api.get(`/consent/forms/${uuid}/`);
  return response.data;
};

export const updateConsentForm = async (uuid: string, data: ConsentFormUpdate): Promise<ConsentForm> => {
  const response = await api.patch(`/consent/forms/${uuid}/`, data);
  return response.data;
};

export const duplicateConsentForm = async (uuid: string, data: Partial<ConsentFormCreate>): Promise<ConsentForm> => {
  const response = await api.post(`/consent/forms/${uuid}/duplicate/`, data);
  return response.data;
};

export const deleteConsentForm = async (uuid: string): Promise<void> => {
  await api.delete(`/consent/forms/${uuid}/`);
};

// Consent Questions Management
export const getConsentQuestions = async (consent_form_id: string): Promise<ConsentQuestion[]> => {
  const response = await api.get(`/consent/forms/${consent_form_id}/questions/`);
  return response.data;
};

// Patient Consent Management
export const createPatientConsent = async (data: PatientConsentCreate): Promise<PatientConsent> => {
  const response = await api.post("/consent/patient-consents/", data);
  return response.data;
};

export const getPatientConsents = async (params?: {
  patient_id?: string;
  consent_form_id?: string;
  consent_status?: 'Consented' | 'Not Consented' | 'Withdrawn';
  page?: number;
  page_size?: number;
}): Promise<PatientConsentResponse> => {
  const queryParams = new URLSearchParams();
  if (params?.patient_id) queryParams.append("patient_id", params.patient_id);
  if (params?.consent_form_id) queryParams.append("consent_form_id", params.consent_form_id);
  if (params?.consent_status) queryParams.append("consent_status", params.consent_status);
  if (params?.page) queryParams.append("page", params.page.toString());
  if (params?.page_size) queryParams.append("page_size", params.page_size.toString());

  const response = await api.get(`/consent/patient-consents/${queryParams.toString() ? `?${queryParams.toString()}` : ""}`);
  return response.data;
};

export const getPatientConsentByUuid = async (uuid: string): Promise<PatientConsent> => {
  const response = await api.get(`/consent/patient-consents/${uuid}/`);
  return response.data;
};

// Get patient consents by patient identifier (old endpoint)
export const getPatientConsentsByPatient = async (patientIdentifier: string): Promise<{
  patient_identifier: string;
  patient_name: string;
  total_consent_forms: number;
  consent_forms: Array<{
    uuid: string;
    name: string;
    version: string;
    description: string;
    questions: any[];
    patient_consent: {
      uuid: string;
      consent_status: 'Consented' | 'Not Consented' | 'Withdrawn';
      signed_at: string | null;
      answers: any[];
    } | null;
    has_consent_record: boolean;
    can_submit: boolean;
  }>;
}> => {
  const response = await api.get(`/consent/patient-consents/by-patient/${patientIdentifier}/`);
  return response.data;
};

// Get patient consent data (new endpoint)
export const getPatientConsentData = async (patientIdentifier: string): Promise<{
  patient_identifier: string;
  patient_name: string;
  total_consent_forms: number;
  consent_forms: Array<{
    uuid: string;
    name: string;
    version: string;
    description: string;
    study: {
      uuid: string;
      name: string;
    };
    questions: Array<{
      uuid: string;
      question_text: string;
      is_required: boolean;
      sequence: number;
    }>;
    patient_consent: {
      uuid: string;
      consent_status: 'Consented' | 'Not Consented' | 'Withdrawn';
      signed_at: string | null;
    };
    has_consent_record: boolean;
    can_submit: boolean;
  }>;
  enrolled_studies_count: number;
  enrolled_studies: Array<{
    uuid: string;
    name: string;
  }>;
}> => {
  const response = await api.get(`/consent/patients/${patientIdentifier}/consent/`);
  return response.data;
};

// Study Consent Management
export const getStudyConsent = async (study_id: string): Promise<ConsentForm | null> => {
  try {
    const response = await api.get(`/studies/${study_id}/consent/`);
    return response.data;
  } catch (error: any) {
    if (error.response?.status === 404) {
      return null; // No consent form associated with this study
    }
    throw error;
  }
};

export const attachConsentToStudy = async (study_id: string, consent_form_id: string): Promise<void> => {
  await api.post(`/studies/${study_id}/consent/`, { consent_form_id });
};

// Patient Consent Flow
export const startPatientConsent = async (patient_id: string, consent_form_id: string): Promise<{
  consent_form: ConsentForm;
  current_consent?: PatientConsent;
}> => {
  const response = await api.post(`/patients/${patient_id}/consent/start/`, { consent_form_id });
  return response.data;
};

export const submitPatientConsent = async (
  patient_id: string,
  consent_form_id: string,
  answers: PatientConsentAnswerCreate[]
): Promise<PatientConsent> => {
  const response = await api.post(`/consent/patients/${patient_id}/consent/submit/`, {
    consent_form_id,
    answers,
  });
  return response.data;
};

// Sponsor-specific consent endpoints
export const getSponsorStudiesPatientsConsent = async (params?: {
  study?: string;
  patient_search?: string;
  consent_status?: string;
  include_questions?: boolean;
}): Promise<any[]> => {
  const queryParams = new URLSearchParams();
  if (params?.study) queryParams.append("study", params.study);
  if (params?.patient_search) queryParams.append("patient_search", params.patient_search);
  if (params?.consent_status) queryParams.append("consent_status", params.consent_status);
  if (params?.include_questions !== undefined) queryParams.append("include_questions", params.include_questions.toString());

  const response = await api.get(`/consent/forms/sponsor-studies-patients/${queryParams.toString() ? `?${queryParams.toString()}` : ""}`);
  return response.data;
};

export const getSponsorPatientConsent = async (patientUuid: string): Promise<any> => {
  const response = await api.get(`/consent/forms/sponsor-patient-consent/${patientUuid}/`);
  return response.data;
};

// Get consent forms by patient UUID (General endpoint)
export const getConsentFormsByPatientUuid = async (patientUuid: string, params?: {
  study?: string;
  consent_status?: string;
  include_questions?: boolean;
}): Promise<{
  patient_uuid: string;
  patient_name: string;
  patient_initials: string;
  nhs_number: string;
  date_of_birth: string;
  studies: Array<{
    study_uuid: string;
    study_name: string;
    iras: string;
    sponsor_name: string;
    consent_forms: Array<{
      consent_form_uuid: string;
      consent_form_name: string;
      consent_form_version: string;
      consent_form_description: string;
      questions_count: number;
      patient_consent_uuid: string;
      consent_status: string;
      signed_at: string;
      created_at: string;
      questions?: Array<{
        question_uuid: string;
        question_text: string;
        is_required: boolean;
        sequence: number;
        patient_answer: boolean;
        answered_at: string;
      }>;
    }>;
  }>;
}> => {
  const queryParams = new URLSearchParams();
  if (params?.study) queryParams.append("study", params.study);
  if (params?.consent_status) queryParams.append("consent_status", params.consent_status);
  if (params?.include_questions !== undefined) queryParams.append("include_questions", params.include_questions.toString());

  const response = await api.get(`/consent/forms/by-patient-uuid/${patientUuid}/${queryParams.toString() ? `?${queryParams.toString()}` : ""}`);
  return response.data;
};

// Submit consent by patient UUID (for editing existing consent)
export const submitConsentByUuid = async (patientUuid: string, data: {
  consent_form_id: string;
  answers: Array<{
    question_id: string;
    answer: boolean;
  }>;
}): Promise<any> => {
  const response = await api.post(`/consent/patient-consents/submit-by-uuid/${patientUuid}/`, data);
  return response.data;
};

// Withdraw consent
export const withdrawConsent = async (patientConsentUuid: string, data: {
  reason?: string;
}): Promise<any> => {
  const response = await api.post(`/consent/patient-consents/${patientConsentUuid}/withdraw/`, data);
  return response.data;
};
/* Sponsor Studies Invitation - CSS with prefix: ssi- */
/* All classes prefixed with 'ssi-' to avoid conflicts with other pages */

/* ===== MAIN CONTAINER ===== */
.ssi-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 24px;
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  min-height: 100vh;
  font-family: var(--font-primary, 'Poppins', sans-serif);
}

/* ===== HEADER SECTION ===== */
.ssi-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 32px;
  border-radius: 16px;
  margin-bottom: 32px;
  box-shadow: 0 10px 25px rgba(102, 126, 234, 0.2);
  position: relative;
  overflow: hidden;
}

.ssi-header::before {
  content: '';
  position: absolute;
  top: 0;
  right: 0;
  width: 100px;
  height: 100px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 50%;
  transform: translate(30px, -30px);
}

.ssi-header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 24px;
  position: relative;
  z-index: 1;
}

.ssi-header-left h1 {
  font-size: 2.5rem;
  font-weight: 700;
  margin: 0 0 8px 0;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  display: flex;
  align-items: center;
  gap: 12px;
}

.ssi-header-left p {
  font-size: 1.1rem;
  opacity: 0.9;
  margin: 0;
  font-weight: 300;
}

.ssi-header-stats {
  display: flex;
  gap: 24px;
}

.ssi-stat-card {
  background: rgba(255, 255, 255, 0.15);
  backdrop-filter: blur(10px);
  border-radius: 12px;
  padding: 16px 20px;
  text-align: center;
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.ssi-stat-icon {
  background: rgba(255, 255, 255, 0.2);
  border-radius: 8px;
  padding: 8px;
  display: inline-flex;
  margin-bottom: 8px;
}

.ssi-stat-number {
  font-size: 1.5rem;
  font-weight: 700;
  margin: 0;
  line-height: 1;
}

.ssi-stat-label {
  font-size: 0.875rem;
  opacity: 0.9;
  margin: 4px 0 0 0;
}

/* ===== TAB NAVIGATION ===== */
.ssi-tabs {
  background: white;
  border-radius: 16px;
  padding: 8px;
  margin-bottom: 32px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  border: 1px solid #e2e8f0;
}

.ssi-tabs-nav {
  display: flex;
  gap: 8px;
}

.ssi-tab-button {
  flex: 1;
  background: transparent;
  border: none;
  border-radius: 12px;
  padding: 16px 24px;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 12px;
  font-weight: 500;
  color: #64748b;
}

.ssi-tab-button:hover {
  background: #f1f5f9;
  color: #475569;
}

.ssi-tab-button.active {
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
  color: white;
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
}

.ssi-tab-icon {
  background: rgba(255, 255, 255, 0.2);
  border-radius: 8px;
  padding: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.ssi-tab-content h3 {
  font-size: 1.1rem;
  font-weight: 600;
  margin: 0 0 4px 0;
}

.ssi-tab-content p {
  font-size: 0.875rem;
  margin: 0;
  opacity: 0.8;
}

/* ===== CARD LAYOUTS ===== */
.ssi-card {
  background: white;
  border-radius: 16px;
  padding: 24px;
  margin-bottom: 24px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  border: 1px solid #e2e8f0;
  transition: all 0.3s ease;
}

.ssi-card:hover {
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
  transform: translateY(-2px);
}

.ssi-card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 16px;
  border-bottom: 2px solid #f1f5f9;
}

.ssi-card-title {
  font-size: 1.5rem;
  font-weight: 600;
  color: #1e293b;
  margin: 0;
  display: flex;
  align-items: center;
  gap: 12px;
}

.ssi-card-subtitle {
  font-size: 1rem;
  color: #64748b;
  margin: 4px 0 0 0;
}

/* ===== SEARCH AND FILTER CONTROLS ===== */
.ssi-controls {
  background: white;
  border-radius: 16px;
  padding: 24px;
  margin-bottom: 24px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  border: 1px solid #e2e8f0;
}

.ssi-controls-header {
  margin-bottom: 20px;
}

.ssi-controls-header h3 {
  font-size: 1.25rem;
  font-weight: 600;
  color: #1e293b;
  margin: 0 0 4px 0;
}

.ssi-controls-header p {
  color: #64748b;
  margin: 0;
}

.ssi-controls-row {
  display: flex;
  gap: 16px;
  align-items: flex-end;
  flex-wrap: wrap;
}

.ssi-search-group {
  flex: 1;
  min-width: 300px;
}

.ssi-filter-group {
  min-width: 200px;
}

.ssi-search-wrapper {
  position: relative;
}

.ssi-search-icon {
  position: absolute;
  left: 16px;
  top: 50%;
  transform: translateY(-50%);
  color: #64748b;
  z-index: 1;
}

.ssi-search-input {
  width: 100%;
  padding: 14px 16px 14px 48px;
  border: 2px solid #e2e8f0;
  border-radius: 12px;
  font-size: 14px;
  transition: all 0.3s ease;
  background: #f8fafc;
}

.ssi-search-input:focus {
  outline: none;
  border-color: #3b82f6;
  background: white;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.ssi-search-clear {
  position: absolute;
  right: 12px;
  top: 50%;
  transform: translateY(-50%);
  background: #ef4444;
  color: white;
  border: none;
  border-radius: 50%;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  font-size: 14px;
  transition: all 0.2s ease;
}

.ssi-search-clear:hover {
  background: #dc2626;
  transform: translateY(-50%) scale(1.1);
}

.ssi-filter-wrapper {
  display: flex;
  align-items: center;
  gap: 8px;
  background: #f8fafc;
  border: 2px solid #e2e8f0;
  border-radius: 12px;
  padding: 8px 16px;
  transition: all 0.3s ease;
}

.ssi-filter-wrapper:focus-within {
  border-color: #3b82f6;
  background: white;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.ssi-filter-select {
  background: transparent;
  border: none;
  outline: none;
  font-size: 14px;
  color: #374151;
  cursor: pointer;
  flex: 1;
}

/* ===== LOADING STATES ===== */
.ssi-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 64px;
  background: white;
  border-radius: 16px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  border: 1px solid #e2e8f0;
}

.ssi-loading-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid #e2e8f0;
  border-top: 4px solid #3b82f6;
  border-radius: 50%;
  animation: ssi-spin 1s linear infinite;
  margin-bottom: 16px;
}

@keyframes ssi-spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.ssi-loading-text {
  color: #64748b;
  font-size: 1.1rem;
  font-weight: 500;
}

.ssi-loading-subtext {
  color: #94a3b8;
  font-size: 0.875rem;
  margin-top: 8px;
}

/* ===== EMPTY STATES ===== */
.ssi-empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 64px;
  background: white;
  border-radius: 16px;
  border: 2px dashed #e2e8f0;
  text-align: center;
}

.ssi-empty-icon {
  background: #f1f5f9;
  border-radius: 50%;
  padding: 20px;
  margin-bottom: 20px;
  color: #64748b;
}

.ssi-empty-title {
  font-size: 1.25rem;
  font-weight: 600;
  color: #1e293b;
  margin: 0 0 8px 0;
}

.ssi-empty-description {
  color: #64748b;
  margin: 0 0 24px 0;
  max-width: 400px;
  line-height: 1.5;
}

/* ===== BUTTONS ===== */
.ssi-btn {
  display: inline-flex;
  align-items: center;
  gap: 8px;
  padding: 12px 24px;
  border-radius: 12px;
  font-weight: 600;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.3s ease;
  border: none;
  text-decoration: none;
  justify-content: center;
}

.ssi-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none !important;
}

.ssi-btn-primary {
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
  color: white;
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
}

.ssi-btn-primary:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(59, 130, 246, 0.4);
}

.ssi-btn-secondary {
  background: #f1f5f9;
  color: #475569;
  border: 1px solid #cbd5e1;
}

.ssi-btn-secondary:hover:not(:disabled) {
  background: #e2e8f0;
  border-color: #94a3b8;
}

.ssi-btn-success {
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
  color: white;
  box-shadow: 0 4px 12px rgba(16, 185, 129, 0.3);
}

.ssi-btn-success:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(16, 185, 129, 0.4);
}

.ssi-btn-danger {
  background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
  color: white;
  box-shadow: 0 4px 12px rgba(239, 68, 68, 0.3);
}

.ssi-btn-danger:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(239, 68, 68, 0.4);
}

/* ===== MODAL STYLES ===== */
.ssi-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.6);
  backdrop-filter: blur(4px);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: 20px;
}

.ssi-modal {
  background: white;
  border-radius: 20px;
  max-width: 800px;
  width: 100%;
  max-height: 90vh;
  overflow: hidden;
  box-shadow: 0 25px 50px rgba(0, 0, 0, 0.25);
  animation: ssi-modal-appear 0.3s ease-out;
}

@keyframes ssi-modal-appear {
  from {
    opacity: 0;
    transform: scale(0.9) translateY(-20px);
  }
  to {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
}

.ssi-modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24px 32px;
  border-bottom: 1px solid #e2e8f0;
  background: #f8fafc;
}

.ssi-modal-title {
  font-size: 1.5rem;
  font-weight: 600;
  color: #1e293b;
  margin: 0;
  display: flex;
  align-items: center;
  gap: 12px;
}

.ssi-modal-close {
  background: #f1f5f9;
  border: none;
  border-radius: 8px;
  padding: 8px;
  cursor: pointer;
  color: #64748b;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.ssi-modal-close:hover {
  background: #e2e8f0;
  color: #374151;
}

.ssi-modal-body {
  padding: 32px;
  overflow-y: auto;
  max-height: calc(90vh - 140px);
}

.ssi-modal-footer {
  padding: 24px 32px;
  border-top: 1px solid #e2e8f0;
  background: #f8fafc;
  display: flex;
  gap: 12px;
  justify-content: flex-end;
}

/* ===== DELEGATION DOCUMENTS MODAL ===== */
.ssi-delegation-details {
  background: #f8fafc;
  border-radius: 12px;
  padding: 20px;
  margin-bottom: 24px;
  border: 1px solid #e2e8f0;
}

.ssi-delegation-details h3 {
  color: #1e293b;
  font-size: 1.25rem;
  font-weight: 600;
  margin: 0 0 16px 0;
}

.ssi-detail-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 16px;
}

.ssi-detail-item {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.ssi-detail-label {
  font-size: 0.875rem;
  font-weight: 600;
  color: #64748b;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.ssi-detail-value {
  font-size: 1rem;
  color: #1e293b;
  font-weight: 500;
}

.ssi-status-badge {
  display: inline-flex;
  align-items: center;
  gap: 6px;
  padding: 6px 12px;
  border-radius: 20px;
  font-size: 0.875rem;
  font-weight: 600;
  text-transform: capitalize;
}

.ssi-status-pending {
  background: #fef3c7;
  color: #d97706;
}

.ssi-status-approved {
  background: #d1fae5;
  color: #059669;
}

.ssi-status-rejected {
  background: #fee2e2;
  color: #dc2626;
}

/* ===== DOCUMENTS SECTION ===== */
.ssi-documents-section {
  margin-top: 24px;
}

.ssi-documents-section h3 {
  color: #1e293b;
  font-size: 1.25rem;
  font-weight: 600;
  margin: 0 0 16px 0;
}

.ssi-documents-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.ssi-document-item {
  background: white;
  border: 1px solid #e2e8f0;
  border-radius: 12px;
  padding: 16px;
  display: flex;
  align-items: center;
  gap: 16px;
  transition: all 0.2s ease;
}

.ssi-document-item:hover {
  border-color: #cbd5e1;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
}

.ssi-document-icon {
  background: #f1f5f9;
  border-radius: 8px;
  padding: 12px;
  color: #64748b;
  flex-shrink: 0;
}

.ssi-document-info {
  flex: 1;
}

.ssi-document-name {
  font-weight: 600;
  color: #1e293b;
  margin: 0 0 4px 0;
}

.ssi-document-type {
  color: #64748b;
  font-size: 0.875rem;
  margin: 0 0 8px 0;
  text-transform: capitalize;
}

.ssi-document-meta {
  display: flex;
  gap: 16px;
  font-size: 0.75rem;
  color: #94a3b8;
}

.ssi-document-meta-item {
  display: flex;
  align-items: center;
  gap: 4px;
}

.ssi-document-actions {
  display: flex;
  gap: 8px;
}

.ssi-download-btn {
  background: #3b82f6;
  color: white;
  border: none;
  border-radius: 8px;
  padding: 8px 16px;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  gap: 6px;
}

.ssi-download-btn:hover:not(:disabled) {
  background: #2563eb;
  transform: translateY(-1px);
}

.ssi-download-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

/* ===== ACTION FORMS ===== */
.ssi-action-form {
  background: #f8fafc;
  border-radius: 12px;
  padding: 24px;
  margin-top: 24px;
  border: 1px solid #e2e8f0;
}

.ssi-action-form h3 {
  color: #1e293b;
  font-size: 1.25rem;
  font-weight: 600;
  margin: 0 0 16px 0;
  display: flex;
  align-items: center;
  gap: 8px;
}

.ssi-form-group {
  margin-bottom: 20px;
}

.ssi-form-label {
  display: block;
  font-weight: 600;
  color: #374151;
  margin-bottom: 8px;
  font-size: 0.875rem;
}

.ssi-textarea {
  width: 100%;
  min-height: 120px;
  padding: 12px 16px;
  border: 2px solid #e2e8f0;
  border-radius: 8px;
  font-size: 14px;
  font-family: inherit;
  resize: vertical;
  transition: all 0.3s ease;
}

.ssi-textarea:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.ssi-form-help {
  font-size: 0.75rem;
  color: #64748b;
  margin-top: 6px;
}

.ssi-action-buttons {
  display: flex;
  gap: 12px;
  justify-content: flex-end;
  margin-top: 24px;
}

/* ===== DEPARTMENT DELEGATION LOGS ===== */
.ssi-stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 20px;
  margin-bottom: 24px;
}

.ssi-stat-card-alt {
  background: white;
  border-radius: 12px;
  padding: 20px;
  border: 1px solid #e2e8f0;
  transition: all 0.2s ease;
}

.ssi-stat-card-alt:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

.ssi-stat-card-alt-header {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 12px;
}

.ssi-stat-card-alt-icon {
  background: #f1f5f9;
  border-radius: 8px;
  padding: 8px;
  color: #64748b;
}

.ssi-stat-card-alt-content h3 {
  font-size: 1.75rem;
  font-weight: 700;
  color: #1e293b;
  margin: 0;
  line-height: 1;
}

.ssi-stat-card-alt-content p {
  font-size: 0.875rem;
  color: #64748b;
  margin: 4px 0 0 0;
}

/* ===== PI ASSIGNMENTS ===== */
.ssi-assignments-header {
  background: white;
  border-radius: 16px;
  padding: 24px;
  margin-bottom: 24px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  border: 1px solid #e2e8f0;
}

.ssi-assignments-title {
  font-size: 1.5rem;
  font-weight: 600;
  color: #1e293b;
  margin: 0 0 8px 0;
}

.ssi-assignments-description {
  color: #64748b;
  margin: 0;
}

.ssi-assignments-controls {
  display: flex;
  gap: 16px;
  margin-bottom: 24px;
  flex-wrap: wrap;
}

.ssi-assignments-search {
  flex: 1;
  min-width: 250px;
  position: relative;
}

.ssi-assignments-filter {
  min-width: 180px;
}

/* ===== CONFIRMATION MODAL ===== */
.ssi-confirmation-modal {
  max-width: 500px;
}

.ssi-confirmation-content {
  text-align: center;
  padding: 20px 0;
}

.ssi-confirmation-icon {
  background: #fef3c7;
  color: #d97706;
  border-radius: 50%;
  padding: 16px;
  display: inline-flex;
  margin-bottom: 16px;
}

.ssi-confirmation-title {
  font-size: 1.25rem;
  font-weight: 600;
  color: #1e293b;
  margin: 0 0 12px 0;
}

.ssi-confirmation-message {
  color: #64748b;
  margin: 0 0 20px 0;
  line-height: 1.5;
}

.ssi-confirmation-details {
  background: #f8fafc;
  border-radius: 8px;
  padding: 16px;
  margin: 20px 0;
  text-align: left;
}

.ssi-confirmation-detail {
  display: flex;
  justify-content: space-between;
  margin-bottom: 8px;
}

.ssi-confirmation-detail:last-child {
  margin-bottom: 0;
}

.ssi-confirmation-detail-label {
  font-weight: 600;
  color: #64748b;
}

.ssi-confirmation-detail-value {
  color: #1e293b;
}

.ssi-confirmation-actions {
  display: flex;
  gap: 12px;
  justify-content: center;
}

/* ===== SPONSOR DELEGATION REVIEW MODAL ===== */
.ssi-review-modal {
  max-width: 600px;
}

.ssi-review-header {
  display: flex;
  align-items: center;
  gap: 12px;
}

.ssi-review-icon {
  padding: 8px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.ssi-review-icon.approve {
  background: #d1fae5;
  color: #059669;
}

.ssi-review-icon.reject {
  background: #fee2e2;
  color: #dc2626;
}

.ssi-review-form {
  padding: 0;
}

.ssi-review-description {
  color: #64748b;
  margin: 0 0 24px 0;
  line-height: 1.5;
}

.ssi-review-details {
  background: #f8fafc;
  border-radius: 12px;
  padding: 20px;
  margin-bottom: 24px;
  border: 1px solid #e2e8f0;
}

.ssi-review-detail-grid {
  display: grid;
  gap: 16px;
}

.ssi-review-detail-item {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.ssi-review-detail-label {
  font-size: 0.875rem;
  font-weight: 600;
  color: #64748b;
}

.ssi-review-detail-value {
  color: #1e293b;
  font-weight: 500;
}

/* ===== RESPONSIVE DESIGN ===== */
@media (max-width: 768px) {
  .ssi-container {
    padding: 16px;
  }
  
  .ssi-header {
    padding: 24px;
  }
  
  .ssi-header-content {
    flex-direction: column;
    align-items: stretch;
    text-align: center;
  }
  
  .ssi-header-stats {
    justify-content: center;
  }
  
  .ssi-tabs-nav {
    flex-direction: column;
  }
  
  .ssi-controls-row {
    flex-direction: column;
    align-items: stretch;
  }
  
  .ssi-search-group,
  .ssi-filter-group {
    min-width: auto;
  }
  
  .ssi-stats-grid {
    grid-template-columns: 1fr;
  }
  
  .ssi-detail-grid {
    grid-template-columns: 1fr;
  }
  
  .ssi-assignments-controls {
    flex-direction: column;
  }
  
  .ssi-assignments-search,
  .ssi-assignments-filter {
    min-width: auto;
  }
  
  .ssi-modal {
    margin: 10px;
    max-width: calc(100vw - 20px);
  }
  
  .ssi-modal-header,
  .ssi-modal-body,
  .ssi-modal-footer {
    padding: 20px;
  }
  
  .ssi-confirmation-actions,
  .ssi-action-buttons {
    flex-direction: column;
  }
}

@media (max-width: 480px) {
  .ssi-header-left h1 {
    font-size: 2rem;
  }
  
  .ssi-header-stats {
    flex-direction: column;
    gap: 16px;
  }
  
  .ssi-stat-card {
    padding: 12px 16px;
  }
  
  .ssi-card,
  .ssi-controls {
    padding: 20px;
  }
  
  .ssi-search-input {
    padding: 12px 16px 12px 44px;
  }
  
  .ssi-modal-header,
  .ssi-modal-body,
  .ssi-modal-footer {
    padding: 16px;
  }
}

/* ===== FOCUS STATES FOR ACCESSIBILITY ===== */
.ssi-btn:focus,
.ssi-tab-button:focus,
.ssi-search-input:focus,
.ssi-filter-select:focus,
.ssi-textarea:focus {
  outline: 2px solid #3b82f6;
  outline-offset: 2px;
}

.ssi-modal-close:focus,
.ssi-search-clear:focus,
.ssi-download-btn:focus {
  outline: 2px solid #3b82f6;
  outline-offset: 2px;
}

/* ===== DELEGATION LOGS SPECIFIC STYLES ===== */
.ssi-delegation-logs {
  /* Container for delegation logs tab */
}

/* ===== PI ASSIGNMENTS SPECIFIC STYLES ===== */
.ssi-assignments-container {
  /* Container for PI assignments modal content */
}

.ssi-assignments-header {
  background: white;
  border-radius: 16px;
  padding: 24px;
  margin-bottom: 24px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  border: 1px solid #e2e8f0;
}

.ssi-assignments-title {
  font-size: 1.5rem;
  font-weight: 600;
  color: #1e293b;
  margin: 0 0 8px 0;
  display: flex;
  align-items: center;
  gap: 12px;
}

.ssi-assignments-description {
  color: #64748b;
  margin: 0;
  font-size: 1rem;
}

.ssi-assignments-controls {
  display: flex;
  gap: 16px;
  margin-bottom: 24px;
  flex-wrap: wrap;
}

.ssi-assignments-search {
  flex: 1;
  min-width: 250px;
  position: relative;
}

.ssi-assignments-filter {
  min-width: 180px;
}

/* ===== SPINNING ANIMATION ===== */
.ssi-spin {
  animation: ssi-spin 1s linear infinite;
}

/* ===== STATUS BADGES ===== */
.ssi-status-pending {
  background: #fef3c7;
  color: #d97706;
  border: 1px solid #f59e0b;
}

.ssi-status-approved,
.ssi-status-accepted {
  background: #d1fae5;
  color: #059669;
  border: 1px solid #10b981;
}

.ssi-status-rejected {
  background: #fee2e2;
  color: #dc2626;
  border: 1px solid #ef4444;
}

/* ===== UTILITY CLASSES ===== */
.ssi-text-center {
  text-align: center;
}

.ssi-text-left {
  text-align: left;
}

.ssi-text-right {
  text-align: right;
}

.ssi-mb-0 {
  margin-bottom: 0;
}

.ssi-mb-1 {
  margin-bottom: 8px;
}

.ssi-mb-2 {
  margin-bottom: 16px;
}

.ssi-mb-3 {
  margin-bottom: 24px;
}

.ssi-mt-0 {
  margin-top: 0;
}

.ssi-mt-1 {
  margin-top: 8px;
}

.ssi-mt-2 {
  margin-top: 16px;
}

.ssi-mt-3 {
  margin-top: 24px;
}

.ssi-flex {
  display: flex;
}

.ssi-flex-column {
  flex-direction: column;
}

.ssi-flex-wrap {
  flex-wrap: wrap;
}

.ssi-items-center {
  align-items: center;
}

.ssi-justify-center {
  justify-content: center;
}

.ssi-justify-between {
  justify-content: space-between;
}

.ssi-gap-1 {
  gap: 8px;
}

.ssi-gap-2 {
  gap: 16px;
}

.ssi-gap-3 {
  gap: 24px;
}

.ssi-w-full {
  width: 100%;
}

.ssi-h-full {
  height: 100%;
}

.ssi-hidden {
  display: none;
}

.ssi-visible {
  display: block;
}

/* ===== ANIMATION CLASSES ===== */
.ssi-fade-in {
  animation: ssi-fadeIn 0.3s ease-out;
}

@keyframes ssi-fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

.ssi-slide-up {
  animation: ssi-slideUp 0.3s ease-out;
}

@keyframes ssi-slideUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.ssi-scale-in {
  animation: ssi-scaleIn 0.2s ease-out;
}

@keyframes ssi-scaleIn {
  from {
    opacity: 0;
    transform: scale(0.95);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

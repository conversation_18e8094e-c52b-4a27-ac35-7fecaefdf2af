import React from 'react';
import { useLocation } from 'react-router-dom';
import SEOHead from './SEOHead';
import { getSEOConfig } from '@/config/seoConfig';

interface SEORouteProps {
  children: React.ReactNode;
  title?: string;
  description?: string;
  keywords?: string;
  image?: string;
  noIndex?: boolean;
  noFollow?: boolean;
  canonical?: string;
  structuredData?: object | object[];
  requiresAuth?: boolean;
}

const SEORoute: React.FC<SEORouteProps> = ({
  children,
  title,
  description,
  keywords,
  image,
  noIndex = false,
  noFollow = false,
  canonical,
  structuredData,
  requiresAuth = false,
}) => {
  const location = useLocation();
  
  // For authenticated routes, add noindex to prevent indexing
  const shouldNoIndex = noIndex || requiresAuth;
  
  // Get default SEO config for the current route
  const defaultConfig = getSEOConfig(location.pathname);
  
  return (
    <>
      <SEOHead
        title={title || defaultConfig.title}
        description={description || defaultConfig.description}
        keywords={keywords || defaultConfig.keywords}
        image={image || defaultConfig.image}
        noIndex={shouldNoIndex}
        noFollow={noFollow}
        canonical={canonical}
        structuredData={structuredData || defaultConfig.structuredData}
      />
      {children}
    </>
  );
};

export default SEORoute;

import React, { useState } from "react";
import { motion } from "framer-motion";
import { Plus } from "lucide-react";
import DataTable, { Column } from "@/components/common/DataTable";
import { type Symptom } from "@/services/api/symptom.service";
import { Navigate } from "react-router-dom";
import AddSymptomModal from "@/components/patient-clinical/AddSymptomModal";
import "@/components/patient-clinical/AddSymptomModal.css";
import { useSymptomsQuery, useUpdateSymptomMutation } from "@/hooks/symptom.query";
import { useCurrentUserQuery } from "@/hooks/user.query";
import { useQueryClient } from "@tanstack/react-query";
import { formatDate } from "@/utils/date";

const DiarySection: React.FC = () => {
  const queryClient = useQueryClient();
  const { data: currentUser } = useCurrentUserQuery();
  const patientUuid = (currentUser && typeof currentUser === 'object' && 'patient_uuid' in currentUser)
    ? (currentUser as { patient_uuid: string }).patient_uuid
    : "";
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [editingSymptom, setEditingSymptom] = useState<{
    uuid: string;
    status: string;
  } | null>(null);

  const updateSymptomMutation = useUpdateSymptomMutation();

  console.log('Patient UUID from current user:', patientUuid); // Debug log

  const { data: symptoms = [], isLoading: isLoadingSymptoms } = useSymptomsQuery(patientUuid);

  // Sort symptoms by created_at in descending order (newest first)
  const sortedSymptoms = React.useMemo(() => {
    return [...symptoms].sort((a, b) => {
      const dateA = new Date(a.created_at).getTime();
      const dateB = new Date(b.created_at).getTime();
      return dateB - dateA; // Descending order (newest first)
    });
  }, [symptoms]);

  if (!patientUuid) {
    return <Navigate to="/patient" replace />;
  }

  const handleEditSymptom = (symptom: Symptom) => {
    setEditingSymptom({
      uuid: symptom.uuid,
      status: symptom.status
    });
  };

  const handleCancelEditSymptom = () => {
    setEditingSymptom(null);
  };

  const handleSaveEditSymptom = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!editingSymptom) return;

    try {
      await updateSymptomMutation.mutateAsync({
        uuid: editingSymptom.uuid,
        data: {
          status: editingSymptom.status
        }
      });
      setEditingSymptom(null);
    } catch (error) {
      console.error("Error updating symptom:", error);
    }
  };

  const symptomColumns: Column<Symptom>[] = [
    { key: "name", header: "Symptom", sortable: true },
    { key: "description", header: "Description", sortable: true },
    {
      key: "start_date",
      header: "Start Date",
      sortable: true,
      render: (value: string | number | boolean | null | undefined) => {
        if (typeof value === 'string') {
          return formatDate(value);
        }
        return String(value ?? '');
      }
    },
    { key: "start_time", header: "Start Time", sortable: true },
    { key: "severity", header: "Severity", sortable: true },
    {
      key: "hospitalization_required",
      header: "Hospitalized",
      sortable: true,
      render: (value: string | number | boolean | null | undefined) => {
        if (typeof value === 'boolean') {
          return value ? "Yes" : "No";
        }
        return String(value ?? '');
      }
    },
    { key: "status_display", header: "Status", sortable: true },

    {
      key: "actions" as keyof Symptom,
      header: "Actions",
      sortable: false,
      render: (_: string | number | boolean | null | undefined, row?: Symptom) => row && (
        <div className="d-flex gap-2">
          <button
            className="button -sm btn-nurtify-lighter"
            onClick={() => handleEditSymptom(row)}
            disabled={row.status === '1'}
            title={row.status === '1' ? "Cannot edit resolved symptom" : "Edit symptom status"}
          >
            Edit
          </button>
        </div>
      )
    }
  ];

  const handleAddSymptom = () => {
    if (!patientUuid) {
      console.error('No patient UUID available');
      return;
    }
    setIsModalOpen(true);
  };

  const handleModalClose = () => {
    setIsModalOpen(false);
    // Reset the form when modal is closed
    if (isModalOpen) {
      // This will trigger the form reset in AddSymptomModal component
      queryClient.invalidateQueries({ queryKey: ['symptoms'] });
    }
  };

  return (
    <motion.div
      className="patclin-tab-content"
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      transition={{ duration: 0.5 }}
    >
      <div className="d-flex justify-content-between align-items-center mb-4">
        <h2 className="patclin-section-title">My Symptoms</h2>
        <button
          className="btn btn-primary d-flex align-items-center gap-2"
          onClick={handleAddSymptom}
        >
          <Plus size={20} />
          Add Symptom
        </button>
      </div>

      {/* Symptoms Content */}
      {isLoadingSymptoms ? (
        <div className="text-center">Loading symptoms...</div>
      ) : sortedSymptoms.length > 0 ? (
        <DataTable
          data={sortedSymptoms}
          columns={symptomColumns}
          noDataMessage="No symptoms recorded yet"
        />
      ) : (
        <div className="patclin-empty-state">
          <p>Your symptoms diary is empty. Start tracking your health journey today.</p>
        </div>
      )}

      <AddSymptomModal
        isOpen={isModalOpen}
        onClose={handleModalClose}
        patientUuid={patientUuid}
        onSuccess={handleModalClose}
      />

      {/* Edit Symptom Modal */}
      {editingSymptom && (
        <div className="modal-overlay">
          <div className="modal-content">
            <div className="modal-header">
              <h3>Update Symptom Status</h3>
              <button className="close-button" onClick={handleCancelEditSymptom}>×</button>
            </div>
            <form onSubmit={handleSaveEditSymptom}>
              <div className="form-group">
                <label htmlFor="status">Status</label>
                <select
                  className="form-control"
                  id="status"
                  value={editingSymptom.status}
                  onChange={(e) => setEditingSymptom(prev => ({ ...prev!, status: e.target.value }))}
                  required
                >
                  <option value="1">Resolved</option>
                  <option value="2">Recovered with sequelae</option>
                  <option value="3">Ongoing / Continuing treatment</option>
                  <option value="4">Condition worsening</option>
                  <option value="5">Unknown</option>
                </select>
              </div>
              <div className="modal-footer">
                <button type="submit" className="button -md btn-nurtify text-white me-2" disabled={updateSymptomMutation.isPending}>
                  {updateSymptomMutation.isPending ? "Saving..." : "Save"}
                </button>
                <button type="button" className="button -md btn-nurtify-lighter" onClick={handleCancelEditSymptom}>
                  Cancel
                </button>
              </div>
            </form>
          </div>
        </div>
      )}
    </motion.div>
  );
};

export default DiarySection;

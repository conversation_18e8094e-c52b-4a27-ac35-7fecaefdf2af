/* frontend/src/components/otp-input.css */

.nurtify-otp-container {
  display: flex;
  gap: 12px; /* Spacing between inputs */
  justify-content: center; /* Center the inputs */
}

.nurtify-otp-input {
  width: 50px; /* Input width */
  height: 60px; /* Input height */
  border-radius: 12px; /* Rounded corners */
  border: 2px solid #e4e7ea; /* Default border */
  background-color: #f7f8fb; /* Light background */
  font-size: 24px; /* Text size */
  font-weight: 600; /* Bold text */
  text-align: center; /* Center text inside */
  transition: all 0.3s ease; /* Smooth transitions */
  color: var(--color-dark-1, #333); /* Text color (with fallback) */
  outline: none; /* Remove default outline */
}

.nurtify-otp-input:focus {
  border-color: var(--color-purple-1, #7e22ce); /* Highlight border on focus (using purple fallback) */
  box-shadow: 0 0 0 3px rgba(55, 183, 195, 0.2); /* Focus shadow (using teal color from other styles) */
  background-color: white; /* White background on focus */
}

/* Hide number input spinners */
.nurtify-otp-input::-webkit-inner-spin-button,
.nurtify-otp-input::-webkit-outer-spin-button {
  display: none;
  margin: 0; /* Ensure no extra space */
}
/* For Firefox */
.nurtify-otp-input[type=number] {
  -moz-appearance: textfield;
}

/* Mobile responsiveness */
@media (max-width: 576px) {
  .nurtify-otp-container {
    gap: 8px; /* Smaller gap on mobile */
  }

  .nurtify-otp-input {
    width: 40px; /* Smaller inputs on mobile */
    height: 50px;
    font-size: 20px;
  }
}

.nurtify-footer {
    padding: 60px 0 30px;
    width: 100%;
    font-family: inherit;
    background-image: url("/assets/img/landing/Footer.png");
    background-size: cover;
    background-position: center bottom;
    background-repeat: no-repeat;
}

.nurtify-footer__container {
    max-width: 1440px;
    margin: 0 auto;
    padding: 0 20px;
}

.nurtify-footer__top {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    gap: 60px;
    margin-bottom: 60px;
}

.nurtify-footer__brand {
    flex: 1;
    max-width: 400px;
}

.nurtify-footer__logo {
    display: flex;
    align-items: center;
    gap: 12px;
    margin-bottom: 40px;
}

.nurtify-footer__logo svg {
    width: 48px;
    height: 48px;
}

.nurtify-footer__brand-text {
    font-size: 28px;
    font-weight: 600;
    color: #37B7C3;
    letter-spacing: -0.02em;
}

.nurtify-footer__subscription h3 {
    font-size: 18px;
    font-weight: 600;
    color: #071952;
    margin: 0 0 24px 0;
    line-height: 1.4;
}

.nurtify-footer__subscribe-form {
    display: flex;
    gap: 0;
    border-radius: 50px;
    overflow: hidden;
    border: 1px solid #2AA3B0;
    padding: 4px;
}

.nurtify-footer__email-input {
    flex: 1;
    padding: 16px 24px;
    border: 1px solid #D2DFE1;
    border-right: none;
    background: none;
    border-radius: 50px 0 0 50px;
    font-size: 14px;
    color: #090914;
    outline: none;
    min-width: 200px;
}

.nurtify-footer__email-input::placeholder {
    color: #9CA3AF;
}

.nurtify-footer__email-input:focus {
    border-color: #37B7C3;
    box-shadow: 0 0 0 2px rgba(55, 183, 195, 0.1);
}

.nurtify-footer__subscribe-btn {
    padding: 16px 32px;
    background: #37B7C3;
    color: white;
    border: none;
    border-radius: 50px 50px 50px 50px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: background 0.2s ease;
    white-space: nowrap;
}

.nurtify-footer__subscribe-btn:hover {
    background: #2A9BA5;
}

.nurtify-footer__links {
    display: flex;
    gap: 80px;
    flex: 1;
    justify-content: flex-end;
    margin-right: 100px;
    flex-wrap: wrap;
}

.nurtify-footer__column h4 {
    font-size: 18px;
    font-weight: 600;
    color: #090914;
    margin: 0 0 24px 0;
    line-height: 1.4;
}

.nurtify-footer__column ul {
    list-style: none;
    padding: 0;
    margin: 0;
}

.nurtify-footer__column li {
    margin-bottom: 16px;
}

.nurtify-footer__column li:last-child {
    margin-bottom: 0;
}

.nurtify-footer__column a {
    color: #090914;
    text-decoration: none;
    font-size: 16px;
    font-weight: 500;
    line-height: 1.5;
    transition: color 0.2s ease;
}

.nurtify-footer__column a:hover {
    color: #37B7C3;
}

.nurtify-footer__bottom {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-top: 30px;
    gap: 20px;
}

.nurtify-footer__copyright {
    color: #090914;
    font-size: 14px;
    font-weight: 500;
}

.nurtify-footer__legal {
    display: flex;
    gap: 32px;
    align-items: center;
}

.nurtify-footer__legal a {
    color: #090914;
    text-decoration: none;
    font-size: 14px;
    font-weight: 500;
    transition: color 0.2s ease;
}

.nurtify-footer__legal a:hover {
    color: #37B7C3;
}

.nurtify-footer__social {
    display: flex;
    gap: 16px;
    align-items: center;
}

.nurtify-footer__social a {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: transparent;
    transition: background 0.2s ease;
    text-decoration: none;
}

.nurtify-footer__social a {
    background: rgba(55, 183, 195, 0.1);
}

.nurtify-footer__social svg {
    width: 20px;
    height: 20px;
}

.nurtify-footer-hr{
    border: 1px solid #090914;
}

/* Responsive Design */
@media (max-width: 1200px) {
    .nurtify-footer__links {
        margin-right: 50px;
        gap: 60px;
    }
}

@media (max-width: 1024px) {
    .nurtify-footer {
        background-size: cover;
    }

    .nurtify-footer__top {
        flex-direction: column;
        gap: 40px;
        align-items: stretch;
    }

    .nurtify-footer__brand {
        max-width: 100%;
        text-align: center;
    }

    .nurtify-footer__links {
        justify-content: center;
        gap: 50px;
        margin-right: 0;
    }

    .nurtify-footer__subscribe-form {
        max-width: 500px;
        margin: 0 auto;
    }
}

@media (max-width: 768px) {
    .nurtify-footer {
        padding: 40px 0 20px;
    }

    .nurtify-footer__container {
        padding: 0 16px;
    }

    .nurtify-footer__top {
        gap: 30px;
        margin-bottom: 40px;
    }

    .nurtify-footer__brand {
        text-align: left;
    }

    .nurtify-footer__links {
        flex-direction: column;
        gap: 30px;
        justify-content: flex-start;
    }

    .nurtify-footer__subscribe-form {
        flex-direction: column;
        border-radius: 12px;
        padding: 4px;
        max-width: 100%;
    }

    .nurtify-footer__email-input {
        background: none;
        border-radius: 12px 12px 0 0;
        border-right: 1px solid #D2DFE1;
        min-width: unset;
        width: 100%;
    }

    .nurtify-footer__subscribe-btn {
        border-radius: 0 0 12px 12px;
        padding: 14px 24px;
    }

    .nurtify-footer__bottom {
        flex-direction: column;
        align-items: flex-start;
        gap: 20px;
        text-align: left;
    }

    .nurtify-footer__legal {
        gap: 20px;
        flex-wrap: wrap;
        width: 100%;
    }

    .nurtify-footer__social {
        align-self: flex-start;
        margin-top: 10px;
    }
}

@media (max-width: 480px) {
    .nurtify-footer {
        padding: 30px 0 15px;
    }

    .nurtify-footer__container {
        padding: 0 12px;
    }

    .nurtify-footer__top {
        gap: 25px;
        margin-bottom: 30px;
    }

    .nurtify-footer__logo {
        justify-content: center;
        margin-bottom: 25px;
    }

    .nurtify-footer__logo img {
        height: 50px !important;
        width: 170px !important;
    }

    .nurtify-footer__subscription h3 {
        font-size: 16px;
        text-align: center;
        margin-bottom: 20px;
    }

    .nurtify-footer__links {
        gap: 20px;
    }

    .nurtify-footer__column h4 {
        font-size: 16px;
        margin-bottom: 16px;
    }

    .nurtify-footer__column a {
        font-size: 14px;
    }

    .nurtify-footer__column li {
        margin-bottom: 12px;
    }

    .nurtify-footer__email-input {
        padding: 14px 20px;
        font-size: 14px;
    }

    .nurtify-footer__subscribe-btn {
        padding: 14px 20px;
        font-size: 14px;
    }

    .nurtify-footer__bottom {
        gap: 16px;
        padding-top: 20px;
        text-align: center;
        align-items: center;
    }

    .nurtify-footer__copyright {
        font-size: 12px;
        text-align: center;
    }

    .nurtify-footer__legal {
        flex-direction: column;
        align-items: center;
        gap: 12px;
        width: 100%;
        text-align: center;
    }

    .nurtify-footer__legal a {
        font-size: 12px;
    }

    .nurtify-footer__social {
        justify-content: center;
        align-self: center;
        margin-top: 16px;
    }

    .nurtify-footer__social a {
        width: 36px;
        height: 36px;
    }

    .nurtify-footer__social svg {
        width: 18px;
        height: 18px;
    }
}

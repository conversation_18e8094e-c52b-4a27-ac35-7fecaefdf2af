import React, { useState } from "react";
import { X, Plus, Trash2, <PERSON><PERSON><PERSON><PERSON>, ArrowRight, Save, AlertCircle } from "lucide-react";
import NurtifyInput from "@/components/NurtifyInput";
import NurtifyTextArea from "@/components/NurtifyTextArea";
import NurtifySelect from "@/components/NurtifySelect";
import { useCreateConsentFormMutation } from "@/hooks/consent.query";
import { ConsentFormCreate, ConsentQuestionCreate } from "@/types/types";
import "./CreateConsentModal.css";

interface CreateConsentModalProps {
  isOpen: boolean;
  onClose: () => void;
  studies: any[];
  onSuccess: () => void;
  currentUserIdentifier: string;
}

const CreateConsentModal: React.FC<CreateConsentModalProps> = ({
  isOpen,
  onClose,
  studies,
  onSuccess,
  currentUserIdentifier,
}) => {
  const [currentStep, setCurrentStep] = useState(1);
  const [formData, setFormData] = useState<Partial<ConsentFormCreate>>({
    name: "",
    description: "",
    study_id: "",
    questions: [],
  });
  const [currentQuestion, setCurrentQuestion] = useState<Partial<ConsentQuestionCreate>>({
    question_text: "",
    is_required: false,
    sequence: 1,
  });
  const [errors, setErrors] = useState<{ [key: string]: string }>({});

  const createConsentMutation = useCreateConsentFormMutation();

  const totalSteps = 3;

  const handleInputChange = (field: string, value: string | boolean) => {
    setFormData((prev) => ({ ...prev, [field]: value }));
    // Clear error when user starts typing
    if (errors[field]) {
      setErrors((prev) => ({ ...prev, [field]: "" }));
    }
  };

  const handleQuestionChange = (field: string, value: string | boolean) => {
    setCurrentQuestion((prev) => ({ ...prev, [field]: value }));
  };

  const addQuestion = () => {
    if (!currentQuestion.question_text?.trim()) {
      setErrors((prev) => ({ ...prev, question_text: "Question text is required" }));
      return;
    }

    const newQuestion: ConsentQuestionCreate = {
      question_text: currentQuestion.question_text!,
      is_required: currentQuestion.is_required || false,
      sequence: (formData.questions?.length || 0) + 1,
    };

    setFormData((prev) => ({
      ...prev,
      questions: [...(prev.questions || []), newQuestion],
    }));

    // Reset current question
    setCurrentQuestion({
      question_text: "",
      is_required: false,
      sequence: (formData.questions?.length || 0) + 2,
    });

    setErrors((prev) => ({ ...prev, question_text: "" }));
  };

  const removeQuestion = (index: number) => {
    setFormData((prev) => ({
      ...prev,
      questions: prev.questions?.filter((_, i) => i !== index) || [],
    }));
  };

  const validateStep = (step: number): boolean => {
    const newErrors: { [key: string]: string } = {};

    if (step === 1) {
      if (!formData.name?.trim()) {
        newErrors.name = "Consent form name is required";
      }
      if (!formData.study_id) {
        newErrors.study_id = "Please select a study";
      }
    }

    if (step === 2) {
      if (!formData.questions || formData.questions.length === 0) {
        newErrors.questions = "At least one question is required";
      }
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleNext = () => {
    if (validateStep(currentStep)) {
      setCurrentStep((prev) => Math.min(prev + 1, totalSteps));
    }
  };

  const handlePrevious = () => {
    setCurrentStep((prev) => Math.max(prev - 1, 1));
  };

  const handleSubmit = () => {
    if (!validateStep(currentStep)) return;

    const consentData: ConsentFormCreate = {
      sponsor_id: currentUserIdentifier,
      name: formData.name!,
      description: formData.description || "",
      study_id: formData.study_id!,
      questions: formData.questions || [],
    };

    createConsentMutation.mutate(consentData, {
      onSuccess: () => {
        onSuccess();
        handleClose();
      },
      onError: (error: any) => {
        const errorMessage = error?.response?.data?.message || "Failed to create consent form";
        setErrors((prev) => ({ ...prev, submit: errorMessage }));
      },
    });
  };

  const handleClose = () => {
    setCurrentStep(1);
    setFormData({
      name: "",
      description: "",
      study_id: "",
      questions: [],
    });
    setCurrentQuestion({
      question_text: "",
      is_required: false,
      sequence: 1,
    });
    setErrors({});
    onClose();
  };

  if (!isOpen) return null;

  return (
    <div className="create-consent-modal-overlay">
      <div className="create-consent-modal-container">
        <div className="create-consent-modal-header">
          <h2>Create New Consent Form</h2>
          <button className="create-consent-modal-close-button" onClick={handleClose}>
            <X size={20} />
          </button>
        </div>

        {/* Progress Bar */}
        <div className="create-consent-modal-progress-bar">
          {Array.from({ length: totalSteps }, (_, i) => (
            <div
              key={i}
              className={`create-consent-modal-progress-step ${i + 1 <= currentStep ? "active" : ""}`}
            >
              {i + 1}
            </div>
          ))}
        </div>

        {/* Step Content */}
        <div className="create-consent-modal-content">
          {currentStep === 1 && (
            <div className="create-consent-modal-step-content">
              <h3>Basic Information</h3>
              <div className="create-consent-modal-form-group">
                <label htmlFor="name">Consent Form Name *</label>
                <NurtifyInput
                  id="name"
                  value={formData.name}
                  onChange={(e) => handleInputChange("name", e.target.value)}
                  placeholder="Enter consent form name"
                  className={errors.name ? "error" : ""}
                />
                {errors.name && <span className="create-consent-modal-error-message">{errors.name}</span>}
              </div>

              <div className="create-consent-modal-form-group">
                <label htmlFor="description">Description</label>
                <NurtifyTextArea
                  id="description"
                  value={formData.description}
                  onChange={(e) => handleInputChange("description", e.target.value)}
                  placeholder="Enter description (optional)"
                  rows={3}
                />
              </div>

              <div className="create-consent-modal-form-group">
                <label htmlFor="study_id">Associated Study *</label>
                <NurtifySelect
                  name="study_id"
                  value={formData.study_id}
                  onChange={(e) => handleInputChange("study_id", e.target.value)}
                  options={[
                    { value: "", label: "Select a study" },
                    ...studies.map((study) => ({
                      value: study.uuid,
                      label: study.name,
                    })),
                  ]}
                  className={errors.study_id ? "error" : ""}
                />
                {errors.study_id && <span className="create-consent-modal-error-message">{errors.study_id}</span>}
              </div>
            </div>
          )}

          {currentStep === 2 && (
            <div className="create-consent-modal-step-content">
              <h3>Consent Questions</h3>
              <p className="create-consent-modal-step-description">
                Add questions that patients will need to answer during the consent process.
                Mark questions as required if patients must agree to proceed.
              </p>

              {/* Add Question Form */}
              <div className="create-consent-modal-add-question-form">
                <div className="create-consent-modal-form-group">
                  <label htmlFor="question_text">Question Text *</label>
                  <NurtifyTextArea
                    id="question_text"
                    value={currentQuestion.question_text}
                    onChange={(e) => handleQuestionChange("question_text", e.target.value)}
                    placeholder="Enter the consent question..."
                    rows={3}
                    className={errors.question_text ? "error" : ""}
                  />
                  {errors.question_text && <span className="create-consent-modal-error-message">{errors.question_text}</span>}
                </div>

                <div className="create-consent-modal-form-group">
                  <label className="create-consent-modal-checkbox-label">
                    <input
                      type="checkbox"
                      checked={currentQuestion.is_required}
                      onChange={(e) => handleQuestionChange("is_required", e.target.checked)}
                      className="create-consent-modal-standard-checkbox"
                    />
                    <span className="create-consent-modal-checkbox-text">This question is required (patient must agree to proceed)</span>
                  </label>
                </div>

                <button className="create-consent-modal-add-question-btn" onClick={addQuestion}>
                  <Plus size={16} />
                  Add Question
                </button>
              </div>

              {/* Questions List */}
              <div className="create-consent-modal-questions-list">
                <h4>Added Questions ({formData.questions?.length || 0})</h4>
                {formData.questions && formData.questions.length > 0 ? (
                  formData.questions.map((question, index) => (
                    <div key={index} className="create-consent-modal-question-item">
                      <div className="create-consent-modal-question-content">
                        <div className="create-consent-modal-question-header">
                          <span className="create-consent-modal-question-number">Q{index + 1}</span>
                          {question.is_required && (
                            <span className="create-consent-modal-required-badge">Required</span>
                          )}
                        </div>
                        <p className="create-consent-modal-question-text">{question.question_text}</p>
                      </div>
                      <button
                        className="create-consent-modal-remove-question-btn"
                        onClick={() => removeQuestion(index)}
                      >
                        <Trash2 size={16} />
                      </button>
                    </div>
                  ))
                ) : (
                  <p className="create-consent-modal-no-questions">No questions added yet.</p>
                )}
                {errors.questions && <span className="create-consent-modal-error-message">{errors.questions}</span>}
              </div>
            </div>
          )}

          {currentStep === 3 && (
            <div className="create-consent-modal-step-content">
              <h3>Review & Create</h3>
              <div className="create-consent-modal-review-section">
                <h4>Consent Form Details</h4>
                <div className="create-consent-modal-review-item">
                  <strong>Name:</strong> {formData.name}
                </div>
                {formData.description && (
                  <div className="create-consent-modal-review-item">
                    <strong>Description:</strong> {formData.description}
                  </div>
                )}
                <div className="create-consent-modal-review-item">
                  <strong>Study:</strong> {studies.find(s => s.uuid === formData.study_id)?.name}
                </div>
                <div className="create-consent-modal-review-item">
                  <strong>Questions:</strong> {formData.questions?.length || 0} questions
                </div>
              </div>

              <div className="create-consent-modal-warning-section">
                <AlertCircle size={20} />
                <div>
                  <strong>Important:</strong> Once created, consent forms cannot be edited to maintain audit trail integrity. 
                  You can duplicate this form to create a new version if changes are needed.
                </div>
              </div>
            </div>
          )}

          {errors.submit && (
            <div className="create-consent-modal-error-alert">
              <AlertCircle size={16} />
              {errors.submit}
            </div>
          )}
        </div>

        {/* Modal Footer */}
        <div className="create-consent-modal-footer">
          <div className="create-consent-modal-footer-buttons">
            {currentStep > 1 && (
              <button className="create-consent-modal-btn create-consent-modal-btn-secondary" onClick={handlePrevious}>
                <ArrowLeft size={16} />
                Previous
              </button>
            )}
            
            {currentStep < totalSteps ? (
              <button className="create-consent-modal-btn create-consent-modal-btn-primary" onClick={handleNext}>
                Next
                <ArrowRight size={16} />
              </button>
            ) : (
              <button
                className="create-consent-modal-btn create-consent-modal-btn-success"
                onClick={handleSubmit}
                disabled={createConsentMutation.isPending}
              >
                {createConsentMutation.isPending ? (
                  <div className="create-consent-modal-loading">
                    <div className="create-consent-modal-spinner"></div>
                    Creating...
                  </div>
                ) : (
                  <>
                    <Save size={16} />
                    Create Consent Form
                  </>
                )}
              </button>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default CreateConsentModal;

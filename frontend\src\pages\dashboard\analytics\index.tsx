import "./analytics.css";
import {
  Users,
  FileText,
  UserCheck,
  ChevronDown,
  Plus,
  CheckCircle,
  XCircle,
  Stethoscope,
  Heart,
  Phone,
  HelpCircle,
} from "lucide-react";
import { useCurrentUserQuery } from "@/hooks/user.query";

export default function Analytics() {
  const currentUser = useCurrentUserQuery();

  return (
    <div className="analytics-dashboard-content">
      {/* Welcome Header */}
      <div className="analytics-welcome-header">
        <h1 className="analytics-welcome-title">
          Welcome Back, <span className="analytics-user-name">{currentUser.data?.first_name || "<PERSON><PERSON>"}</span>
        </h1>
        <p className="analytics-welcome-subtitle">Lorem Ipsum</p>
      </div>

      {/* KPI Cards */}
      <div className="analytics-kpi-cards-row">
        <div className="analytics-kpi-card">
          <div className="analytics-kpi-header">
            <div className="analytics-kpi-icon analytics-patients-icon">
              <Users size={20} />
            </div>
            <div className="analytics-kpi-title-section">
              <div className="analytics-kpi-title-row">
                <span className="analytics-kpi-title">Patients</span>
                <div className="analytics-kpi-period">
                  This month <ChevronDown size={14} />
                </div>
              </div>
            </div>
          </div>
          <div className="analytics-kpi-main-row">
            <div className="analytics-kpi-number">154</div>
            <div className="analytics-kpi-percentage analytics-negative">7.1% ↓</div>
          </div>
          <div className="analytics-kpi-change analytics-negative">
            <span className="analytics-change-text">Decreased <span className="analytics-highlight">-10 Patients</span> This month!</span>
          </div>
        </div>

        <div className="analytics-kpi-card">
          <div className="analytics-kpi-header">
            <div className="analytics-kpi-icon analytics-policies-icon">
              <FileText size={20} />
            </div>
            <div className="analytics-kpi-title-section">
              <div className="analytics-kpi-title-row">
                <span className="analytics-kpi-title">Policies</span>
                <div className="analytics-kpi-period">
                  This month <ChevronDown size={14} />
                </div>
              </div>
            </div>
          </div>
          <div className="analytics-kpi-main-row">
            <div className="analytics-kpi-number">24</div>
            <div className="analytics-kpi-percentage analytics-negative">7.1% ↓</div>
          </div>
          <div className="analytics-kpi-change analytics-negative">
            <span className="analytics-change-text">Decreased <span className="analytics-highlight">-10 Policies</span> This month!</span>
          </div>
        </div>

        <div className="analytics-kpi-card">
          <div className="analytics-kpi-header">
            <div className="analytics-kpi-icon analytics-sponsors-icon">
              <UserCheck size={20} />
            </div>
            <div className="analytics-kpi-title-section">
              <div className="analytics-kpi-title-row">
                <span className="analytics-kpi-title">Sponsors</span>
                <div className="analytics-kpi-period">
                  This month <ChevronDown size={14} />
                </div>
              </div>
            </div>
          </div>
          <div className="analytics-kpi-main-row">
            <div className="analytics-kpi-number">64</div>
            <div className="analytics-kpi-percentage analytics-positive">8.3% ↑</div>
          </div>
          <div className="analytics-kpi-change analytics-positive">
            <span className="analytics-change-text">Gained <span className="analytics-highlight">+13 Sponsors</span> This month!</span>
          </div>
        </div>
      </div>

      {/* Middle Section */}
      <div className="analytics-middle-section">
        {/* Staff/Users Card */}
        <div className="analytics-staff-card">
          <div className="analytics-staff-header">
            <div className="analytics-staff-title-section">
              <h3>Staff/Users</h3>
              <div className="analytics-staff-period">
                This month <ChevronDown size={14} />
              </div>
            </div>
            <button className="analytics-add-user-btn">
              <Plus size={16} />
              Add New User
            </button>
          </div>

          <div className="analytics-staff-main-row">
            <div className="analytics-staff-number">32</div>
            <div className="analytics-staff-percentage analytics-positive">8.3% ↑</div>
          </div>
          <div className="analytics-staff-change analytics-positive">
            <span className="analytics-change-text">Added <span className="analytics-highlight">+3 Users</span> This month!</span>
          </div>

          <div className="analytics-staff-breakdown">
            <div className="analytics-staff-type">
              <div className="analytics-staff-type-header">
                <div className="analytics-staff-type-icon analytics-doctors-icon">
                  <Stethoscope size={16} />
                </div>
                <span>Doctors</span>
                <span className="analytics-staff-count">06</span>
              </div>
              <div className="analytics-progress-bar">
                <div className="analytics-progress-fill analytics-doctors-progress" style={{width: '56%'}}></div>
              </div>
              <span className="analytics-progress-percentage">56%</span>
            </div>

            <div className="analytics-staff-type">
              <div className="analytics-staff-type-header">
                <div className="analytics-staff-type-icon analytics-nurses-icon">
                  <Heart size={16} />
                </div>
                <span>Nurses</span>
                <span className="analytics-staff-count">08</span>
              </div>
              <div className="analytics-progress-bar">
                <div className="analytics-progress-fill analytics-nurses-progress" style={{width: '56%'}}></div>
              </div>
              <span className="analytics-progress-percentage">56%</span>
            </div>

            <div className="analytics-staff-type">
              <div className="analytics-staff-type-header">
                <div className="analytics-staff-type-icon analytics-receptionist-icon">
                  <Phone size={16} />
                </div>
                <span>Receptionist</span>
                <span className="analytics-staff-count">06</span>
              </div>
              <div className="analytics-progress-bar">
                <div className="analytics-progress-fill analytics-receptionist-progress" style={{width: '56%'}}></div>
              </div>
              <span className="analytics-progress-percentage">56%</span>
            </div>

            <div className="analytics-staff-type">
              <div className="analytics-staff-type-header">
                <div className="analytics-staff-type-icon analytics-others-icon">
                  <HelpCircle size={16} />
                </div>
                <span>Others</span>
                <span className="analytics-staff-count">12</span>
              </div>
              <div className="analytics-progress-bar">
                <div className="analytics-progress-fill analytics-others-progress" style={{width: '56%'}}></div>
              </div>
              <span className="analytics-progress-percentage">56%</span>
            </div>
          </div>
        </div>

        {/* Studies Invitations Card */}
        <div className="analytics-studies-card">
          <h3>Studies Invitations</h3>

          <div className="analytics-studies-stats">
            <div className="analytics-stat-item">
              <span className="analytics-stat-label">Total Invitations:</span>
              <span className="analytics-stat-value">04</span>
            </div>
            <div className="analytics-stat-item">
              <span className="analytics-stat-label">Accepted:</span>
              <span className="analytics-stat-value">04</span>
            </div>
            <div className="analytics-stat-item">
              <span className="analytics-stat-label">Rejected:</span>
              <span className="analytics-stat-value">04</span>
            </div>
          </div>

          <div className="analytics-studies-table-container">
            <table className="analytics-studies-table">
              <thead>
                <tr>
                  <th>Study Name</th>
                  <th>Source</th>
                  <th>Status</th>
                  <th>Action</th>
                </tr>
              </thead>
              <tbody>
                <tr>
                  <td>Name goes h</td>
                  <td>Name goes h</td>
                  <td><span className="analytics-status-badge analytics-pending">Pending</span></td>
                  <td>
                    <div className="analytics-action-buttons">
                      <button className="analytics-action-btn analytics-accept">
                        <CheckCircle size={16} />
                      </button>
                      <button className="analytics-action-btn analytics-reject">
                        <XCircle size={16} />
                      </button>
                    </div>
                  </td>
                </tr>
                <tr>
                  <td>Name goes h</td>
                  <td>Name goes h</td>
                  <td><span className="analytics-status-badge analytics-pending">Pending</span></td>
                  <td>
                    <div className="analytics-action-buttons">
                      <button className="analytics-action-btn analytics-accept">
                        <CheckCircle size={16} />
                      </button>
                      <button className="analytics-action-btn analytics-reject">
                        <XCircle size={16} />
                      </button>
                    </div>
                  </td>
                </tr>
                <tr>
                  <td>Name goes h</td>
                  <td>Name goes h</td>
                  <td><span className="analytics-status-badge analytics-pending">Pending</span></td>
                  <td>
                    <div className="analytics-action-buttons">
                      <button className="analytics-action-btn analytics-accept">
                        <CheckCircle size={16} />
                      </button>
                      <button className="analytics-action-btn analytics-reject">
                        <XCircle size={16} />
                      </button>
                    </div>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
      </div>

      {/* Requests Section */}
      <div className="analytics-requests-section">
        <div className="analytics-requests-main">
          <div className="analytics-requests-left">
            <div className="analytics-requests-header">
              <h3>Requests</h3>
              <button className="analytics-see-all-btn">See All</button>
            </div>

            <div className="analytics-requests-table-container">
              <table className="analytics-requests-table">
                <thead>
                  <tr>
                    <th>Name</th>
                    <th>NHS Id</th>
                    <th>Source</th>
                    <th>Status</th>
                    <th>Action</th>
                  </tr>
                </thead>
                <tbody>
                  <tr>
                    <td>Name goes h</td>
                    <td>1234545</td>
                    <td>Doctor</td>
                    <td><span className="analytics-status-badge analytics-pending">Pending</span></td>
                    <td>
                      <button className="analytics-details-btn">
                        Details
                      </button>
                    </td>
                  </tr>
                  <tr>
                    <td>Name goes h</td>
                    <td>1234545</td>
                    <td>Doctor</td>
                    <td><span className="analytics-status-badge analytics-pending">Pending</span></td>
                    <td>
                      <button className="analytics-details-btn">
                        Details
                      </button>
                    </td>
                  </tr>
                  <tr>
                    <td>Name goes h</td>
                    <td>1234545</td>
                    <td>Doctor</td>
                    <td><span className="analytics-status-badge analytics-pending">Pending</span></td>
                    <td>
                      <button className="analytics-details-btn">
                        Details
                      </button>
                    </td>
                  </tr>
                  <tr>
                    <td>Name goes h</td>
                    <td>1234545</td>
                    <td>Doctor</td>
                    <td><span className="analytics-status-badge analytics-pending">Pending</span></td>
                    <td>
                      <button className="analytics-details-btn">
                        Details
                      </button>
                    </td>
                  </tr>
                  <tr>
                    <td>Name goes h</td>
                    <td>1234545</td>
                    <td>Doctor</td>
                    <td><span className="analytics-status-badge analytics-pending">Pending</span></td>
                    <td>
                      <button className="analytics-details-btn">
                        Details
                      </button>
                    </td>
                  </tr>
                </tbody>
              </table>
            </div>
          </div>

          <div className="analytics-requests-right">
            <div className="analytics-requests-stats">
              <div className="analytics-total-requests">Total Requests: <span className="analytics-total-number">04</span></div>

              <div className="analytics-requests-chart-section">
                <div className="analytics-chart-bars">
                  <div className="analytics-chart-bar analytics-accepted" style={{height: '40px'}}>
                    <span className="analytics-bar-label">04</span>
                  </div>
                  <div className="analytics-chart-bar analytics-pending" style={{height: '40px'}}>
                    <span className="analytics-bar-label">03</span>
                  </div>
                  <div className="analytics-chart-bar analytics-rejected" style={{height: '40px'}}>
                    <span className="analytics-bar-label">02</span>
                  </div>
                </div>

                <div className="analytics-chart-legend">
                  <div className="analytics-legend-item">
                    <div className="analytics-legend-color analytics-accepted"></div>
                    <span>Accepted</span>
                  </div>
                  <div className="analytics-legend-item">
                    <div className="analytics-legend-color analytics-pending"></div>
                    <span>Pending</span>
                  </div>
                  <div className="analytics-legend-item">
                    <div className="analytics-legend-color analytics-rejected"></div>
                    <span>Rejected</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

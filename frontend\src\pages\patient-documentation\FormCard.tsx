import React, { useState } from 'react';
import { FileText, User, Calendar, Eye, Check, Link2 } from 'lucide-react';
import { useNavigate } from 'react-router-dom';
import './form-card.css';

interface FormCardProps {
  uuid: string;
  name: string;
  createdBy: string;
  createdAt: string;
  version: string;
  isSelected: boolean;
  onSelect: (formUuid: string) => void;
}

const FormCard: React.FC<FormCardProps> = ({
  uuid,
  name,
  createdBy,
  createdAt,
  version,
  isSelected,
  onSelect
}) => {
  const navigate = useNavigate();
  const [isHovered, setIsHovered] = useState(false);
  
  const handleClick = () => {
    navigate(`/form/preview/${uuid}`);
  };
  
  const handleSelectClick = (e: React.MouseEvent) => {
    e.stopPropagation();
    onSelect(uuid);
  };
  
  const handlePreviewClick = (e: React.MouseEvent) => {
    e.stopPropagation();
    navigate(`/form/preview/${uuid}`);
  };
  
  const handleShareClick = (e: React.MouseEvent) => {
    e.stopPropagation();
    const url = `${window.location.origin}/form/preview/${uuid}`;
    navigator.clipboard.writeText(url)
      .then(() => {
        // Show a toast notification here if you have a toast system
        console.log('Form link copied to clipboard');
      })
      .catch(err => {
        console.error('Error copying form link: ', err);
      });
  };
  
  
  return (
    <div 
      className={`form-card ${isSelected ? 'form-card--selected' : ''} ${isHovered ? 'form-card--hovered' : ''}`}
      onClick={handleClick}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
      tabIndex={0}
      onKeyDown={(e) => {
        if (e.key === 'Enter') handleClick();
        if (e.key === ' ') {
          e.preventDefault();
          handleSelectClick(e as unknown as React.MouseEvent);
        }
      }}
      aria-selected={isSelected}
      role="button"
    >
      <div 
        className="form-card__select"
        onClick={handleSelectClick}
        title={isSelected ? "Deselect form" : "Select form"}
      >
        <div className="form-card__checkbox">
          {isSelected && <Check size={16} color="#ffffff" />}
        </div>
      </div>
      
      {isSelected && <div className="form-card__selected-indicator"></div>}
      
      <div className="form-card__header">
        <div className="form-card__title" title={name}>
          <FileText size={18} />
          <span>{name}</span>
        </div>
        <div className="form-card__version" title={`Version ${version}`}>
          v{version}
        </div>
      </div>
      
      <div className="form-card__content">
        <div className="form-card__preview">
          <div className="form-card__line form-card__line--title"></div>
          
          <div className="form-card__question">
            <div className="form-card__line form-card__line--question"></div>
            <div className="form-card__options">
              <div className="form-card__option">
                <div className="form-card__radio"></div>
                <div className="form-card__line form-card__line--option"></div>
              </div>
              <div className="form-card__option">
                <div className="form-card__radio"></div>
                <div className="form-card__line form-card__line--option"></div>
              </div>
            </div>
          </div>
          
          <div className="form-card__question">
            <div className="form-card__line form-card__line--question"></div>
            <div className="form-card__input"></div>
          </div>
          
          {isHovered && (
            <div className="form-card__overlay">
              <button className="form-card__overlay-button" onClick={handlePreviewClick}>
                <Eye size={18} />
                <span>Preview</span>
              </button>
            </div>
          )}
        </div>
        
        <div className="form-card__meta">
          <div className="form-card__creator" title={`Created by ${createdBy}`}>
            <User size={14} />
            <span>{createdBy}</span>
          </div>
          <div className="form-card__date" title={`Created on ${createdAt}`}>
            <Calendar size={14} className="calendar-icon" />
            <span>{createdAt}</span>
          </div>
        </div>
      </div>
      
      <div className="form-card__footer">
        <div className="form-card__actions">
          <button 
            className="form-card__action" 
            onClick={handlePreviewClick}
            title="Preview form"
          >
            <Eye size={16} />
            <span>Preview</span>
          </button>
          
          <button 
            className="form-card__action form-card__action--secondary" 
            onClick={handleShareClick}
            title="Copy link to clipboard"
          >
            <Link2 size={16} />
          </button>
        </div>
      </div>
    </div>
  );
};

export default FormCard;

/* Page Layout */
.dashboard__content {
  padding: 20px;
  min-height: calc(100vh - 80px); /* Adjust based on your header height */
  transition: all 0.3s ease;
}

/* Main content adjustments */
.dashboard__content .row {
  display: flex;
  transition: width 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Column adjustments with smoother transitions */
.dashboard__content .col-xl-3,
.dashboard__content .col-lg-4,
.dashboard__content .col-xl-9,
.dashboard__content .col-lg-8 {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* When sidebar is collapsed */
.dashboard__content .row:has(.sidebar-dsbh.collapsed) .col-xl-3,
.dashboard__content .row:has(.sidebar-dsbh.collapsed) .col-lg-4 {
  width: 80px;
  flex: 0 0 80px;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.dashboard__content .row:has(.sidebar-dsbh.collapsed) .col-xl-9,
.dashboard__content .row:has(.sidebar-dsbh.collapsed) .col-lg-8 {
  width: calc(100% - 80px);
  flex: 0 0 calc(100% - 80px);
  max-width: calc(100% - 80px);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Mobile adjustments with smoother transitions */
@media (max-width: 991px) {
  .dashboard__content .row:has(.sidebar-dsbh.mobile) .col-lg-8,
  .dashboard__content .row:has(.sidebar-dsbh.mobile) .col-xl-9 {
    width: 100%;
    flex: 0 0 100%;
    max-width: 100%;
    /* padding-left: 100px; */
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  }
}



/* Sidebar Styles */
.sidebar.-dashboard {
  position: sticky;
  top: 20px;
  background: #ffffff;
  border-radius: 16px;
  box-shadow: 0 8px 24px rgba(55, 183, 195, 0.08),
              0 2px 4px rgba(55, 183, 195, 0.04);
  transition: all 0.3s ease;
}

.sidebar__item {
  padding: 24px;
}

/* Sidebar Header */
.sidebar__header {
  margin-bottom: 24px;
}

.sidebar__title {
  font-size: 20px;
  font-weight: 600;
  color: #1a1a1a;
  margin-bottom: 16px;
}

.sidebar__subtitle {
  font-size: 16px;
  font-weight: 500;
  color: #333;
  margin: 24px 0 16px 0;
}

/* Search Field */
.sidebar__search {
  margin-bottom: 20px;
}

.search-field {
  position: relative;
  margin-top: 16px;
}

.search-input {
  width: 100%;
  padding: 14px 18px;
  padding-left: 44px;
  border: 1px solid rgba(55, 183, 195, 0.15);
  border-radius: 12px;
  font-size: 14px;
  transition: all 0.2s ease;
  background-color: rgba(55, 183, 195, 0.03);
}

.search-input:focus {
  border-color: #37B7C3;
  box-shadow: 0 0 0 4px rgba(55, 183, 195, 0.1);
  background-color: #fff;
}

.search-icon {
  position: absolute;
  left: 12px;
  top: 50%;
  transform: translateY(-50%);
  color: #9ca3af;
  font-size: 18px;
}

/* Checkbox Container */
.sidebar-checkbox {
  max-height: calc(100vh - 250px);
  overflow-y: auto;
  padding: 4px;
}

/* Scrollbar Styling */
.sidebar-checkbox::-webkit-scrollbar {
  width: 6px;
}

.sidebar-checkbox::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.sidebar-checkbox::-webkit-scrollbar-thumb {
  background: #37B7C3;
  border-radius: 3px;
}

.sidebar-checkbox::-webkit-scrollbar-thumb:hover {
  background: #2d919a;
}

/* Checkbox Styling */
.form-checkbox {
  position: relative;
  display: flex;
  align-items: center;
  padding: 10px 14px;
  margin-bottom: 8px;
  border-radius: 12px;
  transition: all 0.2s ease;
  background-color: #fff;
  border: 1px solid rgba(55, 183, 195, 0.08);
}

.form-checkbox:hover {
  background-color: rgba(55, 183, 195, 0.04);
  border-color: rgba(55, 183, 195, 0.2);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(55, 183, 195, 0.08);
}

.form-checkbox__input {
  position: absolute;
  opacity: 0;
  cursor: pointer;
  height: 0;
  width: 0;
}

.form-checkbox__label {
  position: relative;
  padding-left: 32px;
  cursor: pointer;
  font-size: 15px;
  line-height: 1.5;
  color: #333;
  user-select: none;
  width: 100%;
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin: 0;
  font-weight: 400;
}

.form-checkbox__label:before {
  content: '';
  position: absolute;
  left: 0;
  top: 55%;
  transform: translateY(-50%);
  width: 20px;
  height: 20px;
  border: 2px solid rgba(55, 183, 195, 0.4);
  border-radius: 6px;
  background-color: #fff;
  transition: all 0.2s ease;
  box-shadow: inset 0 1px 3px rgba(55, 183, 195, 0.1);
}

.form-checkbox__input:checked + .form-checkbox__label:before {
  background-color: #37B7C3;
  border-color: #37B7C3;
  box-shadow: 0 2px 4px rgba(55, 183, 195, 0.2);
}

.form-checkbox__input:checked + .form-checkbox__label:after {
  content: '';
  position: absolute;
  left: 7px;
  top: 50%;
  transform: translateY(-50%) rotate(45deg);
  width: 6px;
  height: 12px;
  border: solid white;
  border-width: 0 2px 2px 0;
}

.form-checkbox__count {
  font-size: 13px;
  color: #37B7C3;
  background: rgba(55, 183, 195, 0.08);
  padding: 4px 10px;
  border-radius: 20px;
  font-weight: 500;
  transition: all 0.2s ease;
}

.form-checkbox:hover .form-checkbox__count {
  background: rgba(55, 183, 195, 0.12);
}

/* Tags Styling */
.tags-wrapper {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.tag-item {
  display: inline-block;
  padding: 8px 14px;
  background: rgba(55, 183, 195, 0.08);
  color: #37B7C3;
  border-radius: 10px;
  font-size: 13px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  border: 1px solid rgba(55, 183, 195, 0.12);
}

.tag-item:hover {
  background: rgba(55, 183, 195, 0.12);
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(55, 183, 195, 0.12);
}

/* Form Cards Grid */
.forms-grid {
  display: grid;
  gap: 30px;
  grid-template-columns: repeat(4, 1fr);
  margin-bottom: 30px;
}

/* Responsive Adjustments */
@media (max-width: 1400px) {
  .forms-grid {
    grid-template-columns: repeat(3, 1fr);
  }
}

@media (max-width: 991px) {
  .forms-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 20px;
  }
}

@media (max-width: 767px) {
  .forms-grid {
    grid-template-columns: 1fr;
    gap: 15px;
  }
}

/* Form Card Animation */
.forms-grid > * {
  opacity: 1;
  transform: none;
  animation: none;
}

/* Transitions and Hover Effects */
.hover-shadow-2 {
  transition: all 0.3s ease;
}

.hover-shadow-2:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 20px rgba(0, 0, 0, 0.08) !important;
}

/* Utility Classes */
.shadow-1 {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
}

.border-light {
  border: 1px solid rgba(0, 0, 0, 0.08);
}

.bg-light-4 {
  background-color: #f5f7fa;
}

/* Loading States */
.form-checkbox__input:disabled + .form-checkbox__label {
  opacity: 0.5;
  cursor: not-allowed;
}

/* Add spacing between cards */
.row.y-gap-30 {
  row-gap: 20px !important;
}

@media (max-width: 991px) {
  .row.y-gap-30 {
    row-gap: 15px !important;
  }
}

@media (max-width: 480px) {
  .dashboard__content {
    padding: 10px;
  }
}

/* Modal Styles */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.modal-content {
  background: white;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  max-width: 500px;
  width: 100%;
}
.modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1000;
  }
  
  .modal-content {
    background: white;
    padding: 20px;
    border-radius: 0; /* Changed */
    max-width: 500px;
    width: 90%;
  }
  
  .modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
  }
  
  .modal-close {
    background: none;
    border: none;
    cursor: pointer;
  }
  
  .modal-body {
    margin-bottom: 24px;
  }
  
  .modal-footer {
    display: flex;
    justify-content: flex-end;
    gap: 12px;
  }
  
  .confirm-btn {
    background: #28a745;
    color: white;
    padding: 8px 16px;
    border: none;
    border-radius: 0; /* Changed */
    cursor: pointer;
  }
  
  .confirm-btn:hover {
    background: #218838;
  }
  
  .cancel-btn {
    background: #6c757d;
    color: white;
    padding: 8px 16px;
    border: none;
    border-radius: 0; /* Changed */
    cursor: pointer;
  }
  
  .cancel-btn:hover {
    background: #5a6268;
  }
  
  .accept-btn {
    background: #28a745;
    color: white;
    border: none;
    padding: 4px 8px;
    border-radius: 0; /* Changed */
    cursor: pointer;
  }
  
  .accept-btn:hover {
    background: #218838;
  }
  
  .reject-btn {
    background: #dc3545;
    color: white;
    border: none;
    padding: 4px 8px;
    border-radius: 0; /* Changed */
    cursor: pointer;
  }
  
  .reject-btn:hover {
    background: #c82333;
  }
  
  .status-pending {
    color: orange;
  }
  
  .status-accepted {
    color: green;
  }
  
  .status-rejected {
    color: red;
  }

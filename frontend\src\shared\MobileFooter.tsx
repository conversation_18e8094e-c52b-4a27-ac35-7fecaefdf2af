import { FC } from 'react';
import { Globe } from 'lucide-react';
import Links from "@/components/common/Links";
import "./footer.css";

const MobileFooter: FC = () => {

  return (
    <div className="mobile-footer px-20 py-20 border-top-light">
      <div className="container">
        <div className="mobile-footer__content">
          <div className="mobile-footer__row">
            <div className="mobile-footer__copyright">
              © {new Date().getFullYear()} Nurtify. All Rights Reserved.
            </div>

            <div className="mobile-footer__right">
              <div className="mobile-footer__links">
                <Links allClasses="mobile-footer__link" />
              </div>

              <button className="mobile-footer__language">
                <Globe size={16} className="mobile-footer__icon" />
                <span>English</span>
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default MobileFooter;

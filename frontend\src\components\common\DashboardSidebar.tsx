import React, { useState, useEffect, useCallback } from 'react';
import { ChevronLeft, Menu, User, ChevronDown, ChevronRight } from 'lucide-react';
import { useLocation, useNavigate } from 'react-router-dom';
//import useSelectedPatientStore from '@/store/SelectedPatientState';
import { getProfilePictureUrl } from '@/utils/imageUtils';

import './DashboardSidebar.css';

interface MenuItem {
  icon: React.ReactNode;
  label: string;
  path: string;
  onClick?: () => void;
  subitems?: MenuItem[];
}

interface PatientInfo {
  name: string;
  gender: string;
  age: string;
  nhsNumber: string;
  dob: string;
  mrn: string;
  ethnicity: string;
  email: string;
  phone: string;
  avatar?: string;
}

interface DashboardSidebarProps {
  menuItems: MenuItem[];
  basePath?: string;
  patientInfo?: PatientInfo;
  onProfilePictureClick?: () => void;
  isCollapsed: boolean;
  onCollapseChange: (collapsed: boolean) => void;
}

const DashboardSidebar: React.FC<DashboardSidebarProps> = ({
  menuItems,
  basePath,
  patientInfo,
  onProfilePictureClick,
  isCollapsed,
  onCollapseChange,
}) => {
  const [isMobileActive, setIsMobileActive] = useState(false);
  const [isMobile, setIsMobile] = useState(window.innerWidth <= 991);
  const [imageError, setImageError] = useState(false);
  const [expandedItems, setExpandedItems] = useState<Set<number>>(new Set());

  const location = useLocation();
  const navigate = useNavigate();
  //const { clearSelectedPatient } = useSelectedPatientStore();

  const handleResize = useCallback(() => {
    const mobile = window.innerWidth <= 991;
    setIsMobile(mobile);
    if (!mobile && isMobileActive) {
      setIsMobileActive(false);
    }
  }, [isMobileActive]);

  useEffect(() => {
    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, [handleResize]);

  const toggleSidebar = () => {
    if (isMobile) {
      setIsMobileActive(!isMobileActive);
      onCollapseChange(!isCollapsed); // Notify parent of collapse state change
    } else {
      onCollapseChange(!isCollapsed);
    }
  };

  const handleItemClick = (item: MenuItem) => {
    if (item.onClick) {
      item.onClick();
    } else {
      const fullPath = `${basePath}${item.path}`;
      navigate(fullPath);
    }
    if (isMobile) {
      setIsMobileActive(false);
    }
  };

  const handleKeyPress = (e: React.KeyboardEvent, item: MenuItem) => {
    if (e.key === 'Enter' || e.key === ' ') {
      handleItemClick(item);
    }
  };

  const toggleExpanded = (index: number, e: React.MouseEvent) => {
    e.stopPropagation();
    setExpandedItems(prev => {
      const newSet = new Set(prev);
      if (newSet.has(index)) {
        newSet.delete(index);
      } else {
        newSet.add(index);
      }
      return newSet;
    });
  };

  const handleParentItemClick = (item: MenuItem, index: number) => {
    if (item.subitems && item.subitems.length > 0) {
      // If it has subitems, just toggle expansion
      setExpandedItems(prev => {
        const newSet = new Set(prev);
        if (newSet.has(index)) {
          newSet.delete(index);
        } else {
          newSet.add(index);
        }
        return newSet;
      });
    } else {
      // If no subitems, handle normal click
      handleItemClick(item);
    }
  };

  const isItemActive = (item: MenuItem): boolean => {
    if (item.subitems) {
      return item.subitems.some(subitem => location.pathname.includes(subitem.path));
    }
    return location.pathname.includes(item.path);
  };

  return (
    <>
      <div
        className={`sidebar-dsbh-admin ${isCollapsed ? 'collapsed' : ''} ${
          isMobileActive ? 'active' : ''
        } ${isMobile ? 'mobile' : ''}`}
        role="navigation"
        aria-label="Main navigation"
      >
        <div className="sidebar-header">
          <button
            className={`minimize-btn ${isCollapsed ? 'collapsed' : ''}`}
            onClick={toggleSidebar}
            title={isCollapsed ? 'Expand sidebar' : 'Minimize sidebar'}
          >
            {isCollapsed ? <Menu size={16} /> : <ChevronLeft size={16} />}
            {!isCollapsed && <span className="minimize-text">Minimize</span>}
          </button>
        </div>

        {patientInfo && !isCollapsed && (
          <div className="patient-info-section">
            <div className="patient-header">
              <div
                className="patient-avatar"
                onClick={onProfilePictureClick}
                style={{ cursor: onProfilePictureClick ? 'pointer' : 'default' }}
                title={onProfilePictureClick ? 'Click to change profile picture' : undefined}
              >
                {patientInfo.avatar && !imageError ? (
                  <img
                    src={getProfilePictureUrl(patientInfo.avatar) || ''}
                    alt={patientInfo.name}
                    onError={() => setImageError(true)}
                  />
                ) : (
                  <div className="avatar-placeholder">
                    <User size={24} />
                  </div>
                )}
              </div>
              <div className="patient-basic-info">
                <h3 className="patient-name">{patientInfo.name}</h3>
                <p className="patient-demographics">{patientInfo.gender} | {patientInfo.age}</p>
              </div>
            </div>
            <div className="patient-details compact-patient-details">
              <div className="detail-row">
                <span className="detail-label">NHS #</span>
                <a className="detail-value nhs-link" href={`#nhs-${patientInfo.nhsNumber}`}>{patientInfo.nhsNumber}</a>
              </div>
              <div className="detail-row">
                <span className="detail-label">DOB:</span>
                <span className="detail-value">{patientInfo.dob}</span>
              </div>
              <div className="detail-row">
                <span className="detail-label">MRN:</span>
                <span className="detail-value">{patientInfo.mrn}</span>
              </div>
              <div className="detail-row">
                <span className="detail-label">Ethnicity:</span>
                <span className="detail-value">{patientInfo.ethnicity}</span>
              </div>
              <div className="detail-row">
                <span className="detail-label">Email:</span>
                <span className="detail-value">{patientInfo.email}</span>
              </div>
              <div className="detail-row">
                <span className="detail-label">Phone:</span>
                <span className="detail-value">{patientInfo.phone}</span>
              </div>
            </div>
          </div>
        )}

        <ul className="menuList">
          {menuItems.map((item, index) => (
            <React.Fragment key={index}>
              <li
                onClick={() => handleParentItemClick(item, index)}
                onKeyPress={(e) => handleKeyPress(e, item)}
                role="button"
                style={{ width: '95%' }}
                tabIndex={0}
                aria-current={isItemActive(item) ? 'page' : undefined}
                className={`${isItemActive(item) ? 'active' : ''} ${item.subitems ? 'has-subitems' : ''}`}
              >
                {item.icon}
                <span className="menu-label">{item.label}</span>
                {item.subitems && !isCollapsed && (
                  <button
                    className="expand-arrow"
                    onClick={(e) => toggleExpanded(index, e)}
                    aria-label={expandedItems.has(index) ? 'Collapse' : 'Expand'}
                  >
                    {expandedItems.has(index) ? (
                      <ChevronDown size={16} />
                    ) : (
                      <ChevronRight size={16} />
                    )}
                  </button>
                )}
              </li>
              {item.subitems && expandedItems.has(index) && !isCollapsed && (
                <ul className="submenu">
                  {item.subitems.map((subitem, subIndex) => (
                    <li
                      key={`${index}-${subIndex}`}
                      onClick={() => handleItemClick(subitem)}
                      onKeyPress={(e) => handleKeyPress(e, subitem)}
                      role="button"
                      style={{ width: '95%' }}
                      tabIndex={0}
                      aria-current={location.pathname.includes(subitem.path) ? 'page' : undefined}
                      className={`subitem ${location.pathname.includes(subitem.path) ? 'active' : ''}`}
                    >
                      {subitem.icon}
                      <span className="menu-label">{subitem.label}</span>
                    </li>
                  ))}
                </ul>
              )}
            </React.Fragment>
          ))}
        </ul>
      </div>
      {isMobile && (
        <div
          className={`overlay ${isMobileActive ? 'active' : ''}`}
          onClick={() => setIsMobileActive(false)}
          role="presentation"
        />
      )}
    </>
  );
};

export default DashboardSidebar;

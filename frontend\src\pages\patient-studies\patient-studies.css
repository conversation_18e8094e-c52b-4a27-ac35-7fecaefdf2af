/* Patient Studies Styles */
.studies-container {
  background-color: #fff;
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  padding: 24px;
  margin-bottom: 30px;
}

.studies-controls {
  margin-bottom: 20px;
}

/* Status Badges */
.status-badge {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 6px 12px;
  border-radius: 20px;
  font-size: 13px;
  font-weight: 600;
  text-transform: capitalize;
}

.status-active, .status-completed {
  background-color: #f6ffed;
  color: #52c41a;
  border: 1px solid #b7eb8f;
}

.status-pending {
  background-color: #e6f7ff;
  color: #1890ff;
  border: 1px solid #91d5ff;
}

.status-delayed {
  background-color: #fff7e6;
  color: #fa8c16;
  border: 1px solid #ffbb96;
}

.status-canceled {
  background-color: #fff2f0;
  color: #f5222d;
  border: 1px solid #ffccc7;
}

.status-withdrawn {
  background-color: #fff2f0;
  color: #f5222d;
  border: 1px solid #ffccc7;
}

.status-consented {
  background-color: #f6ffed;
  color: #52c41a;
  border: 1px solid #b7eb8f;
}

.status-not-consented {
  background-color: #fff7e6;
  color: #fa8c16;
  border: 1px solid #ffbb96;
}

.status-inactive {
  background-color: #f5f5f5;
  color: #8c8c8c;
  border: 1px solid #d9d9d9;
}

/* Visits Container */
.visits-container {
  margin-top: 30px;
  padding: 20px;
  background-color: #f8fafc;
  border-radius: 12px;
  border: 1px solid #edf2f7;
}

.visits-container h3 {
  margin-bottom: 20px;
  font-size: 18px;
  font-weight: 600;
  color: #334155;
}

.visits-header {
  display: flex;
  justify-content: flex-end;
  margin-bottom: 20px;
}

.create-visit-btn {
  display: flex;
  align-items: center;
  gap: 8px;
  background-color: #36B6C2;
  color: white;
  border: none;
  border-radius: 8px;
  padding: 10px 16px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
}

.create-visit-btn:hover {
  background-color: #2ea0ab;
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(54, 182, 194, 0.3);
}

.create-visit-btn:active {
  transform: translateY(0);
}

/* Loading and Empty States */
.loading-studies, .no-studies {
  padding: 40px;
  text-align: center;
  color: #64748b;
  font-size: 16px;
  background-color: #f8fafc;
  border-radius: 8px;
  border: 1px dashed #e2e8f0;
}

.loading-studies {
  position: relative;
}

.loading-studies:after {
  content: "";
  position: absolute;
  width: 24px;
  height: 24px;
  border: 3px solid #e2e8f0;
  border-top-color: #36B6C2;
  border-radius: 50%;
  left: calc(50% - 12px);
  top: calc(50% + 20px);
  animation: spin 1s linear infinite;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

/* Responsive Adjustments */
@media (max-width: 768px) {
  .studies-container {
    padding: 16px;
  }
  
  .visits-container {
    padding: 16px;
  }
  
  .create-visit-btn {
    padding: 8px 12px;
    font-size: 13px;
  }
  
  .status-badge {
    padding: 4px 8px;
    font-size: 12px;
  }
}

.patient-details-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
  border-bottom: 1px solid #e5e7eb;
  padding-bottom: 16px;
}

.patient-details-header h1 {
  font-size: 24px;
  font-weight: 600;
  color: #1f2937;
  margin: 0;
}

.enroll-patient-btn {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 10px 16px;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 500;
  background-color: #36b6c2;
  color: white;
  border: 1px solid #2da1ac;
  cursor: pointer;
  transition: all 0.2s ease;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.enroll-patient-btn:hover {
  background-color: #2da1ac;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

/* Visit Modal Styles */
.visit-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.visit-modal {
  background-color: white;
  border-radius: 8px;
  width: 90%;
  max-width: 600px;
  max-height: 90vh;
  overflow-y: auto;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.visit-modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  border-bottom: 1px solid #e5e7eb;
}

.visit-modal-title {
  font-size: 18px;
  font-weight: 600;
  color: #1f2937;
  margin: 0;
}

.visit-modal-close {
  background: none;
  border: none;
  color: #6b7280;
  cursor: pointer;
  padding: 4px;
}

.visit-modal-body {
  padding: 20px;
}

.form-group {
  margin-bottom: 16px;
}

.form-group label {
  display: block;
  font-size: 14px;
  font-weight: 500;
  color: #4b5563;
  margin-bottom: 6px;
}

.form-control {
  width: 100%;
  padding: 8px 12px;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  font-size: 14px;
}

.form-actions {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  margin-top: 20px;
}

.cancel-btn,
.save-btn {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 16px;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.cancel-btn {
  background-color: #f3f4f6;
  color: #4b5563;
  border: 1px solid #e5e7eb;
}

.cancel-btn:hover {
  background-color: #e5e7eb;
}

.save-btn {
  background-color: #36b6c2;
  color: white;
  border: 1px solid #2da1ac;
}

.save-btn:hover {
  background-color: #2da1ac;
}

.save-btn:disabled {
  background-color: #9ca3af;
  border-color: #9ca3af;
  cursor: not-allowed;
}

/* Responsive styles */
@media (max-width: 768px) {
  .patient-details-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 16px;
  }
  
  .enroll-patient-btn {
    width: 100%;
    justify-content: center;
  }
  
  .visit-modal {
    width: 95%;
    margin: 10px;
  }
}

.action-button.completed {
  opacity: 0.5;
  cursor: not-allowed;
  pointer-events: none;
}

.action-button.completed:hover {
  background-color: transparent;
  border-color: transparent;
  color: #475569;
  transform: none;
}

/* Consents Container */
.consents-container {
  margin-top: 30px;
  padding: 20px;
  background-color: #f8fafc;
  border-radius: 12px;
  border: 1px solid #edf2f7;
}

.consents-container h3 {
  margin-bottom: 20px;
  font-size: 18px;
  font-weight: 600;
  color: #334155;
}

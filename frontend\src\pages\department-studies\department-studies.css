/* Department Studies Page Styles - Prefixed with .dept-studies- */

/* Main Container */
.dept-studies-container {
  font-family: var(--font-primary);
  color: var(--color-dark-1);
  background-color: var(--color-light-4);
  min-height: 100vh;
  max-width: 1400px;
  margin: 32px auto 0 auto;
  padding: 0 20px;
}

/* Header Section */
.dept-studies-header {
  background: var(--color-white);
  border-radius: 16px;
  padding: 32px;
  margin: 80px 0 64px 0;
  box-shadow: 0px 4px 20px rgba(0, 0, 0, 0.08);
  border: 1px solid var(--color-light-2);
}

.dept-studies-header-content {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  gap: 24px;
}

.dept-studies-header-left {
  flex: 1;
}

.dept-studies-title-section {
  display: flex;
  align-items: center;
  gap: 16px;
  margin-bottom: 32px;
}

.dept-studies-icon-wrapper {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 56px;
  height: 56px;
  background: linear-gradient(135deg, var(--color-purple-1) 0%, var(--color-blue-1) 100%);
  border-radius: 16px;
  color: white;
  box-shadow: 0px 8px 24px rgba(55, 183, 195, 0.3);
  animation: dept-studies-pulse 2s infinite;
}

.dept-studies-title-content h1 {
  font-size: var(--text-30);
  font-weight: 700;
  color: var(--color-dark-1);
  margin: 0 0 8px 0;
  line-height: 1.2;
}

.dept-studies-title-content p {
  font-size: var(--text-16);
  color: var(--color-light-1);
  margin: 0;
  line-height: 1.5;
}

/* Stats Section */
.dept-studies-stats {
  display: flex;
  gap: 20px;
  flex-wrap: wrap;
  margin-bottom: 0;
}

.dept-studies-stat-card {
  display: flex;
  align-items: center;
  gap: 16px;
  background: var(--color-white);
  padding: 20px 24px;
  border-radius: 16px;
  border: 1px solid var(--color-light-2);
  box-shadow: 0px 2px 8px rgba(0, 0, 0, 0.04);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  min-width: 180px;
  max-width: 220px;
  flex: 1;
  position: relative;
  overflow: hidden;
}

.dept-studies-stat-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, var(--color-purple-1), var(--color-blue-1));
  transform: scaleX(0);
  transition: transform 0.3s ease;
}

.dept-studies-stat-card:hover {
  transform: translateY(-4px);
  box-shadow: 0px 12px 32px rgba(0, 0, 0, 0.15);
}

.dept-studies-stat-card:hover::before {
  transform: scaleX(1);
}

.dept-studies-stat-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 48px;
  height: 48px;
  border-radius: 12px;
  color: white;
  flex-shrink: 0;
}

.dept-studies-stat-icon.total {
  background: linear-gradient(135deg, var(--color-blue-3) 0%, var(--color-blue-4) 100%);
}

.dept-studies-stat-icon.current {
  background: linear-gradient(135deg, var(--color-green-4) 0%, var(--color-green-5) 100%);
}

.dept-studies-stat-icon.per-page {
  background: linear-gradient(135deg, var(--color-orange-1) 0%, var(--color-orange-4) 100%);
}

.dept-studies-stat-content {
  display: flex;
  flex-direction: column;
  flex: 1;
}

.dept-studies-stat-number {
  font-size: var(--text-24);
  font-weight: 700;
  color: var(--color-dark-1);
  line-height: 1;
  margin-bottom: 4px;
}

.dept-studies-stat-label {
  font-size: var(--text-14);
  color: var(--color-light-1);
  font-weight: 500;
}

/* Search Section */
.dept-studies-search-section {
  background: var(--color-white);
  border-radius: 16px;
  padding: 24px;
  margin: 0 0 48px 0;
  box-shadow: 0px 2px 12px rgba(0, 0, 0, 0.06);
  border: 1px solid var(--color-light-2);
  animation: dept-studies-fadeInUp 0.6s ease-out 0.2s both;
  max-width: 800px;
}

.dept-studies-search-wrapper {
  position: relative;
  max-width: 600px;
}

.dept-studies-search-input {
  width: 100%;
  padding: 16px 20px 16px 52px;
  border: 2px solid var(--color-light-2);
  border-radius: 12px;
  font-size: var(--text-16);
  background: var(--color-white);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  font-family: var(--font-primary);
}

.dept-studies-search-input:focus {
  outline: none;
  border-color: var(--color-purple-1);
  box-shadow: 0px 0px 0px 4px rgba(55, 183, 195, 0.1);
  transform: translateY(-1px);
}

.dept-studies-search-input::placeholder {
  color: var(--color-light-1);
  font-weight: 400;
}

.dept-studies-search-icon {
  position: absolute;
  left: 18px;
  top: 50%;
  transform: translateY(-50%);
  color: var(--color-light-1);
  transition: color 0.3s ease;
}

.dept-studies-search-wrapper:focus-within .dept-studies-search-icon {
  color: var(--color-purple-1);
}

/* Table Section */
.dept-studies-table-section {
  background: var(--color-white);
  border-radius: 16px;
  overflow: hidden;
  box-shadow: 0px 2px 12px rgba(0, 0, 0, 0.06);
  border: 1px solid var(--color-light-2);
  animation: dept-studies-fadeInUp 0.6s ease-out 0.4s both;
  max-width: 100%;
  margin-bottom: 32px;
}

.dept-studies-table-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24px 28px;
  border-bottom: 1px solid var(--color-light-2);
  background: linear-gradient(135deg, var(--color-light-6) 0%, var(--color-white) 100%);
}

.dept-studies-table-title {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.dept-studies-table-title h3 {
  font-size: var(--text-20);
  font-weight: 600;
  color: var(--color-dark-1);
  margin: 0;
  display: flex;
  align-items: center;
  gap: 12px;
}

.dept-studies-table-title p {
  font-size: var(--text-14);
  color: var(--color-light-1);
  margin: 0;
}

.dept-studies-table-content {
  padding: 0;
  overflow-x: auto;
  max-width: 100%;
}

/* Ensure DataTable doesn't exceed container width */
.dept-studies-table-content .data-table {
  max-width: 100%;
  overflow-x: auto;
}

.dept-studies-table-content table {
  width: 100%;
  max-width: 100%;
  table-layout: fixed;
}

.dept-studies-table-content th,
.dept-studies-table-content td {
  max-width: 200px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

/* Pagination Info */
.dept-studies-pagination-info {
  padding: 20px 28px;
  background: var(--color-light-6);
  border-top: 1px solid var(--color-light-2);
  text-align: center;
}

.dept-studies-pagination-info p {
  margin: 0;
  font-size: var(--text-14);
  color: var(--color-light-1);
  font-weight: 500;
}

/* Loading States */
.dept-studies-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
  color: var(--color-light-1);
  background: var(--color-white);
  border-radius: 16px;
  margin: 64px 0;
  max-width: 600px;
  margin-left: auto;
  margin-right: auto;
}

.dept-studies-loading-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16px;
}

.dept-studies-spinner {
  width: 40px;
  height: 40px;
  border: 3px solid var(--color-light-2);
  border-top: 3px solid var(--color-purple-1);
  border-radius: 50%;
  animation: dept-studies-spin 1s linear infinite;
}

.dept-studies-loading-text {
  font-size: var(--text-16);
  font-weight: 500;
  color: var(--color-dark-1);
}

/* Error States */
.dept-studies-error {
  background: var(--color-white);
  border-radius: 16px;
  padding: 40px;
  text-align: center;
  box-shadow: 0px 2px 12px rgba(0, 0, 0, 0.06);
  border: 1px solid var(--color-light-2);
  margin: 64px 0;
  max-width: 600px;
  margin-left: auto;
  margin-right: auto;
}

.dept-studies-error-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16px;
  max-width: 400px;
  margin: 0 auto;
}

.dept-studies-error-icon {
  width: 64px;
  height: 64px;
  background: linear-gradient(135deg, var(--color-red-1) 0%, var(--color-red-2) 100%);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
}

.dept-studies-error h3 {
  font-size: var(--text-20);
  font-weight: 600;
  color: var(--color-dark-1);
  margin: 0;
}

.dept-studies-error p {
  font-size: var(--text-14);
  color: var(--color-light-1);
  margin: 0;
  line-height: 1.5;
}

/* Status Badges */
.dept-studies-status-badge {
  display: inline-flex;
  align-items: center;
  gap: 6px;
  padding: 6px 12px;
  border-radius: 20px;
  font-size: var(--text-11);
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  max-width: 150px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.dept-studies-status-not-applied {
  background: var(--color-light-2);
  color: var(--color-light-1);
}

.dept-studies-status-accepted {
  background: var(--color-green-6);
  color: var(--color-green-5);
}

.dept-studies-status-pending {
  background: var(--color-yellow-2);
  color: var(--color-yellow-1);
}

.dept-studies-status-rejected {
  background: var(--color-red-2);
  color: var(--color-red-1);
}

/* Notes Display */
.dept-studies-notes-content {
  display: flex;
  flex-direction: column;
  gap: 4px;
  max-width: 200px;
}

.dept-studies-notes-text {
  font-size: var(--text-13);
  color: var(--color-dark-1);
  line-height: 1.4;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.dept-studies-notes-meta {
  font-size: var(--text-11);
  color: var(--color-light-1);
  font-weight: 500;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

/* Action Buttons */
.dept-studies-action-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 36px;
  height: 36px;
  border-radius: 10px;
  border: none;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  font-size: 0;
}

.dept-studies-action-btn:disabled {
  opacity: 0.4;
  cursor: not-allowed;
  transform: none !important;
}

.dept-studies-action-btn:not(:disabled):hover {
  transform: translateY(-2px);
  box-shadow: 0px 8px 16px rgba(0, 0, 0, 0.15);
}

.dept-studies-action-view {
  background: linear-gradient(135deg, var(--color-blue-3) 0%, var(--color-blue-4) 100%);
  color: white;
}

.dept-studies-action-apply {
  background: linear-gradient(135deg, var(--color-purple-1) 0%, var(--color-blue-1) 100%);
  color: white;
}

.dept-studies-action-reapply {
  background: linear-gradient(135deg, var(--color-orange-1) 0%, var(--color-orange-4) 100%);
  color: white;
}

/* Tooltip */
.dept-studies-tooltip {
  position: relative;
}

.dept-studies-tooltip::after {
  content: attr(data-tooltip);
  position: absolute;
  bottom: 100%;
  left: 50%;
  transform: translateX(-50%);
  background: var(--color-dark-8);
  color: white;
  padding: 8px 12px;
  border-radius: 6px;
  font-size: var(--text-12);
  white-space: nowrap;
  z-index: 1000;
  margin-bottom: 8px;
  opacity: 0;
  pointer-events: none;
  transition: opacity 0.3s ease;
}

.dept-studies-tooltip::before {
  content: '';
  position: absolute;
  bottom: 100%;
  left: 50%;
  transform: translateX(-50%);
  border: 4px solid transparent;
  border-top-color: var(--color-dark-8);
  margin-bottom: 4px;
  opacity: 0;
  pointer-events: none;
  transition: opacity 0.3s ease;
}

.dept-studies-tooltip:hover::after,
.dept-studies-tooltip:hover::before {
  opacity: 1;
}

/* Animations */
@keyframes dept-studies-fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes dept-studies-pulse {
  0%, 100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
}

@keyframes dept-studies-spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Responsive Design */
@media (max-width: 1400px) {
  .dept-studies-container {
    max-width: 1200px;
  }
}

@media (max-width: 1200px) {
  .dept-studies-container {
    max-width: 1000px;
  }
  
  .dept-studies-stats {
    justify-content: center;
  }
  
  .dept-studies-stat-card {
    min-width: 160px;
    max-width: 200px;
  }
}

@media (max-width: 768px) {
  .dept-studies-container {
    padding: 0 16px;
  }
  
  .dept-studies-header {
    padding: 24px;
    margin: 24px 0 48px 0;
  }
  
  .dept-studies-header-content {
    flex-direction: column;
    align-items: stretch;
  }
  
  .dept-studies-title-section {
    flex-direction: column;
    align-items: center;
    text-align: center;
    gap: 12px;
    margin-bottom: 24px;
  }
  
  .dept-studies-title-content h1 {
    font-size: var(--text-24);
  }
  
  .dept-studies-stats {
    flex-direction: column;
    gap: 16px;
  }
  
  .dept-studies-stat-card {
    min-width: auto;
    max-width: none;
    justify-content: center;
  }
  
  .dept-studies-search-section {
    padding: 20px;
    margin-bottom: 32px;
  }
  
  .dept-studies-search-wrapper {
    max-width: none;
  }
  
  .dept-studies-table-header {
    flex-direction: column;
    gap: 16px;
    align-items: stretch;
    padding: 20px;
  }
  
  .dept-studies-table-title h3 {
    font-size: var(--text-18);
    justify-content: center;
  }
  
  .dept-studies-pagination-info {
    padding: 16px 20px;
  }
}

@media (max-width: 480px) {
  .dept-studies-container {
    padding: 0 12px;
  }
  
  .dept-studies-header {
    padding: 20px;
    margin: 20px 0 32px 0;
  }
  
  .dept-studies-icon-wrapper {
    width: 48px;
    height: 48px;
  }
  
  .dept-studies-title-content h1 {
    font-size: var(--text-20);
  }
  
  .dept-studies-search-section {
    padding: 16px;
    margin-bottom: 24px;
  }
  
  .dept-studies-search-input {
    padding: 14px 16px 14px 44px;
    font-size: var(--text-14);
  }
  
  .dept-studies-search-icon {
    left: 14px;
  }
  
  .dept-studies-table-header {
    padding: 16px;
  }
  
  .dept-studies-stat-number {
    font-size: var(--text-20);
  }
  
  .dept-studies-action-btn {
    width: 32px;
    height: 32px;
  }
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
  .dept-studies-container {
    background-color: var(--color-dark-8);
    color: var(--color-white);
  }
  
  .dept-studies-header,
  .dept-studies-search-section,
  .dept-studies-table-section,
  .dept-studies-loading,
  .dept-studies-error {
    background: var(--color-dark-4);
    border-color: var(--color-dark-3);
  }
  
  .dept-studies-search-input {
    background: var(--color-dark-4);
    border-color: var(--color-dark-3);
    color: var(--color-white);
  }
  
  .dept-studies-table-header {
    background: linear-gradient(135deg, var(--color-dark-3) 0%, var(--color-dark-4) 100%);
  }
  
  .dept-studies-pagination-info {
    background: var(--color-dark-3);
  }
}

/* Focus styles for accessibility */
.dept-studies-search-input:focus,
.dept-studies-action-btn:focus {
  outline: 2px solid var(--color-purple-1);
  outline-offset: 2px;
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  .dept-studies-header,
  .dept-studies-search-section,
  .dept-studies-table-section,
  .dept-studies-stat-card,
  .dept-studies-action-btn,
  .dept-studies-spinner {
    animation: none;
    transition: none;
  }
  
  .dept-studies-icon-wrapper {
    animation: none;
  }
}

/* Print styles */
@media print {
  .dept-studies-search-section,
  .dept-studies-action-btn {
    display: none;
  }
  
  .dept-studies-container {
    background: white;
  }
  
  .dept-studies-header,
  .dept-studies-table-section {
    box-shadow: none;
    border: 1px solid #ccc;
  }
}

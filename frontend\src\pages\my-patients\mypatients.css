/* MyPatients Dashboard Container */
.mypatients-dashboard-container {
  display: flex;
  min-height: 100vh;
  background-color: #f8f9fa;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
  color: #333;
  font-size: 14px;
}

.mypatients-dashboard-container * {
  box-sizing: border-box;
}

/* MyPatients Sidebar Styles */
.mypatients-sidebar {
  width: 280px;
  background-color: #ffffff;
  border-right: 1px solid #e9ecef;
  padding: 20px 0;
  position: fixed;
  height: calc(100vh - 60px);
  margin-top: 60px;
  overflow-y: auto;
  box-shadow: 2px 0 4px rgba(0,0,0,0.05);
  transition: width 0.3s ease;
  z-index: 100;
}

.mypatients-sidebar-minimized {
  width: 70px;
}

.mypatients-sidebar-header {
  padding: 0 20px 20px;
  border-bottom: 1px solid #e9ecef;
  margin-bottom: 20px;
  display: flex;
  justify-content: flex-end;
  align-items: center;
}

.mypatients-minimize-btn {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 12px;
  background: none;
  border: none;
  color: #37b7c3;
  font-size: 13px;
  font-weight: 500;
  cursor: pointer;
  border-radius: 6px;
  transition: all 0.2s ease;
}

.mypatients-minimize-btn:hover {
  background-color: #f0f9ff;
  color: #2563eb;
}

.mypatients-sidebar-minimized .mypatients-minimize-btn {
  justify-content: center;
  padding: 8px;
  margin: 0 auto;
}

.mypatients-sidebar-menu {
  list-style: none;
  padding: 0;
  margin: 0;
}

.mypatients-sidebar-item {
  margin-bottom: 2px;
}

.mypatients-sidebar-link {
  display: flex;
  align-items: center;
  padding: 12px 20px;
  text-decoration: none;
  color: #6c757d;
  font-size: 14px;
  font-weight: 500;
  transition: all 0.2s ease;
  border-left: 3px solid transparent;
  background: none;
  border: none;
  width: 100%;
  text-align: left;
  cursor: pointer;
}

.mypatients-sidebar-link:hover {
  background-color: #f8f9fa;
  color: #495057;
}

.mypatients-sidebar-link.mypatients-active {
  background-color: #e8f4f8;
  color: #17a2b8;
  border-left-color: #17a2b8;
  font-weight: 600;
}

.mypatients-sidebar-icon {
  margin-right: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 20px;
  height: 20px;
  flex-shrink: 0;
  color: inherit;
}

.mypatients-sidebar-icon svg {
  width: 20px;
  height: 20px;
  stroke-width: 1.5;
  color: inherit;
}

.mypatients-sidebar-link .mypatients-sidebar-icon {
  color: #6c757d;
}

.mypatients-sidebar-link:hover .mypatients-sidebar-icon {
  color: #495057;
}

.mypatients-sidebar-link.mypatients-active .mypatients-sidebar-icon {
  color: #17a2b8;
}

.mypatients-sidebar-minimized .mypatients-sidebar-icon {
  margin-right: 0;
}

.mypatients-sidebar-minimized .mypatients-sidebar-link {
  padding: 12px;
  justify-content: center;
}

.mypatients-sidebar-minimized .mypatients-sidebar-header {
  padding: 0 10px 20px;
  justify-content: center;
}

/* Main Content */
.mypatients-main-content {
  flex: 1;
  margin-left: 280px;
  margin-top: 60px;
  padding: 20px;
  background-color: #f8f9fa;
  transition: margin-left 0.3s ease;
  min-height: calc(100vh - 60px);
}

.mypatients-main-content-expanded {
  margin-left: 70px;
}

/* Header Section */
.mypatients-header {
  background: white;
  border-radius: 12px;
  padding: 24px;
  margin-bottom: 24px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
  border: 1px solid #e9ecef;
}

.mypatients-breadcrumb {
  margin-bottom: 16px;
}

.mypatients-breadcrumb-link {
  color: #37b7c3;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: color 0.2s ease;
}

.mypatients-breadcrumb-link:hover {
  color: #2c929e;
}

.mypatients-title {
  font-size: 28px;
  font-weight: 700;
  color: #2c3e50;
  margin: 0 0 24px 0;
}

.mypatients-controls {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 24px;
}

.mypatients-search-section {
  display: flex;
  align-items: center;
  gap: 12px;
  flex: 1;
}

.mypatients-search-dropdown {
  padding: 10px 16px;
  border: 1px solid #e9ecef;
  border-radius: 8px;
  background: white;
  font-size: 14px;
  color: #2c3e50;
  min-width: 140px;
  cursor: pointer;
  transition: border-color 0.2s ease;
}

.mypatients-search-dropdown:focus {
  outline: none;
  border-color: #37b7c3;
  box-shadow: 0 0 0 2px rgba(55, 183, 195, 0.1);
}

.mypatients-search-input-container {
  position: relative;
  flex: 1;
}

.mypatients-search-input {
  padding: 10px 40px 10px 16px;
  border: 1px solid #e9ecef;
  border-radius: 8px;
  font-size: 14px;
  width: 100%;
  outline: none;
  transition: border-color 0.2s ease;
}

.mypatients-search-input:focus {
  border-color: #37b7c3;
  box-shadow: 0 0 0 2px rgba(55, 183, 195, 0.1);
}

.mypatients-search-icon {
  position: absolute;
  right: 12px;
  top: 50%;
  transform: translateY(-50%);
  color: #6c757d;
  pointer-events: none;
}

.mypatients-add-btn {
  background: #37b7c3;
  color: white;
  border: none;
  border-radius: 8px;
  padding: 10px 20px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  white-space: nowrap;
}

.mypatients-add-btn:hover {
  background: #2c929e;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(55, 183, 195, 0.3);
}

/* Table Section */
.mypatients-table-section {
  background: white;
  border-radius: 12px;
  padding: 0;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
  border: 1px solid #e9ecef;
  overflow: hidden;
}

.mypatients-table-container {
  overflow-x: auto;
}

.mypatients-table {
  width: 100%;
  border-collapse: collapse;
  font-size: 14px;
}

.mypatients-table th {
  background: #EBF4F6;
  color: #2c3e50;
  font-weight: 600;
  padding: 16px 20px;
  text-align: left;
  border-bottom: 1px solid #e9ecef;
  font-size: 14px;
  white-space: nowrap;
}

.mypatients-table td {
  padding: 16px 20px;
  border-bottom: 1px solid #e9ecef;
  color: #2c3e50;
  transition: background-color 0.2s ease;
  vertical-align: middle;
}

.mypatients-table tbody tr:hover {
  background-color: #f8f9fa;
}

.mypatients-table tbody tr:last-child td {
  border-bottom: none;
}

.mypatients-remove-btn {
  background: #dc3545;
  color: white;
  border: none;
  border-radius: 6px;
  padding: 6px 12px;
  font-size: 12px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.mypatients-remove-btn:hover {
  background: #c82333;
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(220, 53, 69, 0.3);
}

/* Pagination */
.mypatients-pagination {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 8px;
  padding: 20px;
  background: #f8f9fa;
  border-top: 1px solid #e9ecef;
}

.mypatients-pagination-btn {
  background: white;
  border: 1px solid #e9ecef;
  border-radius: 6px;
  padding: 8px 12px;
  font-size: 14px;
  color: #2c3e50;
  cursor: pointer;
  transition: all 0.2s ease;
  min-width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.mypatients-pagination-btn:hover {
  background: #f8f9fa;
  border-color: #37b7c3;
  color: #37b7c3;
}

.mypatients-pagination-btn.mypatients-pagination-active {
  background: #37b7c3;
  border-color: #37b7c3;
  color: white;
}

.mypatients-pagination-dots {
  color: #6c757d;
  padding: 0 8px;
  font-size: 14px;
}

/* Responsive Design */
@media (max-width: 1200px) {
  .mypatients-controls {
    flex-direction: column;
    align-items: stretch;
    gap: 16px;
  }
  
  .mypatients-search-section {
    justify-content: center;
  }
}

@media (max-width: 992px) {
  .mypatients-search-section {
    flex-direction: column;
    gap: 12px;
  }
  
  .mypatients-search-input {
    width: 100%;
  }
}

@media (max-width: 768px) {
  .mypatients-sidebar {
    width: 100%;
    position: relative;
    height: auto;
    margin-top: 0;
  }
  
  .mypatients-main-content {
    margin-left: 0;
    margin-top: 0;
    padding: 15px;
  }
  
  .mypatients-header {
    padding: 16px;
  }
  
  .mypatients-title {
    font-size: 24px;
  }
  
  .mypatients-table th,
  .mypatients-table td {
    padding: 12px 16px;
    font-size: 13px;
  }
  
  .mypatients-pagination {
    padding: 16px;
    gap: 4px;
  }
  
  .mypatients-pagination-btn {
    padding: 6px 10px;
    font-size: 12px;
    min-width: 32px;
    height: 32px;
  }
}

@media (max-width: 480px) {
  .mypatients-dashboard-container {
    flex-direction: column;
  }
  
  .mypatients-sidebar {
    position: relative;
    width: 100%;
    height: auto;
    margin-top: 0;
  }
  
  .mypatients-main-content {
    margin-left: 0;
    margin-top: 0;
  }
  
  .mypatients-header,
  .mypatients-table-section {
    padding: 12px;
  }
  
  .mypatients-table {
    font-size: 12px;
  }
  
  .mypatients-table th,
  .mypatients-table td {
    padding: 8px 12px;
  }
}

/* Scrollbar Styling */
.mypatients-sidebar::-webkit-scrollbar {
  width: 6px;
}

.mypatients-sidebar::-webkit-scrollbar-track {
  background: #f1f1f1;
}

.mypatients-sidebar::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.mypatients-sidebar::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* Focus states for accessibility */
.mypatients-add-btn:focus,
.mypatients-remove-btn:focus,
.mypatients-pagination-btn:focus {
  outline: 2px solid #37b7c3;
  outline-offset: 2px;
}

/* Animation for hover effects */
.mypatients-sidebar-link,
.mypatients-table tbody tr,
.mypatients-pagination-btn {
  transition: all 0.2s ease;
}

/* Integration with existing app layout */
.mypatients-dashboard-container {
  position: relative;
  z-index: 1;
}

/* Ensure proper spacing with existing header */
.mypatients-dashboard-container {
  padding-top: 0;
}

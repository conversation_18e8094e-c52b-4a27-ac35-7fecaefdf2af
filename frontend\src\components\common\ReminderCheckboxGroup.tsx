import React from 'react';

interface ReminderCheckboxGroupProps {
  selectedDays: number[];
  onChange: (days: number[]) => void;
  className?: string;
}

const ReminderCheckboxGroup: React.FC<ReminderCheckboxGroupProps> = ({
  selectedDays,
  onChange,
  className = '',
}) => {
  // Common reminder intervals
  const reminderOptions = [
    { value: 30, label: '1 month' },
    { value: 14, label: '2 weeks' },
    { value: 7, label: '1 week' },
    { value: 3, label: '3 days' },
    { value: 2, label: '2 days' },
    { value: 1, label: '1 day' },
    { value: 0, label: 'Same day' },
  ];

  const handleToggle = (day: number) => {
    if (selectedDays.includes(day)) {
      // Remove the day if already selected
      onChange(selectedDays.filter((d) => d !== day));
    } else {
      // Add the day if not selected
      onChange([...selectedDays, day].sort((a, b) => b - a));
    }
  };

  return (
    <div className={`checkbox-group ${className}`}>
      {reminderOptions.map((option) => (
        <div
          key={option.value}
          className={`checkbox-item ${selectedDays.includes(option.value) ? 'selected' : ''}`}
          onClick={() => handleToggle(option.value)}
        >
          <span>{option.label}</span>
        </div>
      ))}
    </div>
  );
};

export default ReminderCheckboxGroup;

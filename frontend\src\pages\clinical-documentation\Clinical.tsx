import React, { useEffect, useState } from "react";
import { motion } from "framer-motion";
import ClinicalCard from "@/components/ClinicalCard";
import ClinicalCard2 from "@/components/ClinicalCard2";
import PatientSearchModal from "@/components/modal/PatientSearchModal";
import AddPatientFormModal from "@/components/modal/AddPatientFormModal";
import { useCurrentUserQuery } from "@/hooks/user.query";
import "./clinical.css";

type CardProps = {
  title: string;
  description: string;
  link: string;
  icon: string;
};

type StatProps = {
  value: string;
  label: string;
  icon: string;
};

const Clinical: React.FC = () => {
  const [isSearchModalOpen, setIsSearchModalOpen] = useState(false);
  const [isAddPatientModalOpen, setIsAddPatientModalOpen] = useState(false);
  const [, setIsMobile] = useState<boolean>(window.innerWidth < 768);
  const [activeTab, setActiveTab] = useState<string>("all");
  const { data: currentUser, isLoading } = useCurrentUserQuery();

  const handleResize = () => {
    setIsMobile(window.innerWidth < 768);
  };

  useEffect(() => {
    window.addEventListener("resize", handleResize);
    return () => {
      window.removeEventListener("resize", handleResize);
    };
  }, []);

  const stats: StatProps[] = [
    {
      value: "24",
      label: "Visits today",
      icon: "👥"
    },
    {
      value: "12",
      label: "Forms Completed",
      icon: "📝"
    },
    {
      value: "8",
      label: "Templates",
      icon: "📋"
    }
  ];

  const cards: CardProps[] = [
    {
      title: "My Patients List",
      description: "Find and manage your list of patients",
      link: "/my-patients",
      icon: "/assets/img/clinical-documentation/add-patients.png",
    },
    {
      title: "Patient visits",
      description: "Access your daily visit schedule",
      link: "/patients",
      icon: "/assets/img/clinical-documentation/list-patients.png",
    },

  ];
  const cards3: CardProps[] = [

    {
      title: "Create Worksheet",
      description: "Create your study worksheet",
      link: "/survey-forms",
      icon: "/assets/img/clinical-documentation/create-form.png",
    },
  ];

  const cards2: CardProps[] = [
    {
      title: "Nursing Care Plan",
      description: "Browse our ready care plan",
      link: "/forms",
      icon: "/assets/img/clinical-documentation/nurtify-templates.png",
    },
    {
      title: "My Worksheet",
      description: "Edit, review your created worksheet",
      link: "/templates",
      icon: "/assets/img/clinical-documentation/my-templates.png",
    },
    {
      title: "Ask for assistance",
      description: "We can help you creating your worksheet",
      link: "#",
      icon: "/assets/img/clinical-documentation/ask-assistance.png",
    },
  ];

  return (
    <main className="clinical-dashboard" style={{height:"100vh"}}>
      {/* Header Section */}
      <div className="dashboard-header">
        <div className="welcome-section">
          <motion.div
            className="welcome-text-container"
            initial={{ opacity: 0, y: -20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
          >
            <h1 className="welcome-text">
              <span className="greeting">Welcome back,</span>
              <span className="username">
                {isLoading ? "User" : `${currentUser?.first_name} ${currentUser?.last_name}`}
              </span>
              <span className="wave-emoji" role="img" aria-label="waving hand">👋</span>
            </h1>
            <p className="welcome-subtitle">
              Clinical Trial Dashboard - Manage your patients and documentation efficiently 📊
            </p>
          </motion.div>

          <motion.div
            className="stats-container"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.2 }}
          >
            {stats.map((stat, index) => (
              <div className="stat-card" key={index}>
                <div className="stat-icon">{stat.icon}</div>
                <div className="stat-value">{stat.value}</div>
                <div className="stat-label">{stat.label}</div>
              </div>
            ))}
          </motion.div>
        </div>

        <motion.div
          className="dashboard-tabs"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.3 }}
        >
          <button
            className={`tab-button ${activeTab === 'all' ? 'active' : ''}`}
            onClick={() => setActiveTab('all')}
          >
            All
          </button>
          <button
            className={`tab-button ${activeTab === 'patients' ? 'active' : ''}`}
            onClick={() => setActiveTab('patients')}
          >
            Patients
          </button>
          <button
            className={`tab-button ${activeTab === 'templates' ? 'active' : ''}`}
            onClick={() => setActiveTab('templates')}
          >
            Worksheet
          </button>
        </motion.div>
      </div>

      {/* Quick Actions Section */}

      {activeTab === 'all' &&
        <>
          <motion.div
            className="quick-actions-section"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.4 }}
          >
            <h2 className="section-title">All Quick Actions (Patients & Templates)</h2>
            <div className="cards-container">
              {cards.map((card, index) => (
                <motion.div
                  key={`all-patient-${index}`}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.5, delay: 0.1 * index + 0.5 }}
                >
                  <ClinicalCard
                    {...card}
                    isModalOpen={isSearchModalOpen}
                    setIsModalOpen={setIsSearchModalOpen}
                  />
                </motion.div>
              ))}
              {cards3.map((card, index) => (
                <motion.div
                  key={`all-work-${index}`}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.5, delay: 0.1 * (index + cards.length) + 0.5 }}
                >
                  <ClinicalCard
                    {...card}
                    isModalOpen={isSearchModalOpen}
                    setIsModalOpen={setIsSearchModalOpen}
                  />
                </motion.div>
              ))}
            </div>
          </motion.div>
          <motion.div
            className="templates-section"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.6 }}
          >
            <h2 className="section-title">All Templates & Resources</h2>
            <div className="templates-container">
              {cards2.map((card, index) => (
                <motion.div
                  key={`all-template-${index}`}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.5, delay: 0.1 * index + 0.7 }}
                >
                  <ClinicalCard2 {...card} />
                </motion.div>
              ))}
            </div>
          </motion.div>
        </>
      }

      {activeTab === 'patients' &&
        <motion.div
          className="quick-actions-section"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.4 }}
        >
          <h2 className="section-title">Quick Actions</h2>
          <div className="cards-container">
            {cards.map((card, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: 0.1 * index + 0.5 }}
              >
                <ClinicalCard
                  {...card}
                  isModalOpen={isSearchModalOpen}
                  setIsModalOpen={setIsSearchModalOpen}
                />
              </motion.div>
            ))}
          </div>
        </motion.div>
      }
      {activeTab === 'templates' &&
        <motion.div
          className="quick-actions-section"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.4 }}
        >
          <h2 className="section-title">Quick Actions</h2>
          <div className="cards-container">
            {cards3.map((card, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: 0.1 * index + 0.5 }}
              >
                <ClinicalCard
                  {...card}
                  isModalOpen={isSearchModalOpen}
                  setIsModalOpen={setIsSearchModalOpen}
                />
              </motion.div>
            ))}
          </div>
        </motion.div>
      }
      {/* Templates Section */}
      {activeTab === 'templates' &&
        <motion.div
          className="templates-section"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.6 }}
        >
          <h2 className="section-title">Templates & Resources</h2>
          <div className="templates-container">
            {cards2.map((card, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: 0.1 * index + 0.7 }}
              >
                <ClinicalCard2 {...card} />
              </motion.div>
            ))}
          </div>
        </motion.div>
      }

      {/* Modals */}
      <PatientSearchModal
        isOpen={isSearchModalOpen}
        onClose={() => setIsSearchModalOpen(false)}
        onOpenAddPatientModal={() => {
          setIsSearchModalOpen(false);
          setIsAddPatientModalOpen(true);
        }}
      />

      <AddPatientFormModal
        isOpen={isAddPatientModalOpen}
        setIsModalOpen={setIsAddPatientModalOpen}
        setSwitchModal={() => {}}
        switchModal={false}
      />
    </main>
  );
};

export default Clinical;

:root {
  --primary-color: #007bff;
  --primary-gradient: linear-gradient(135deg, #007bff, #0056b3);
  --secondary-color: #e9ecef;
  --text-color: #212529;
  --text-secondary: #6c757d;
  --background-color: #ffffff;
  --border-color: #ced4da;
  --border-radius: 12px;
  --hover-bg: rgba(0, 0, 0, 0.08);
  --animation-duration: 0.2s;
  --dark-mode-bg: #1a1a1a; /* Not directly used in modal, but good for consistency */
  --dark-mode-text: #f8f9fa;
  --dark-mode-secondary: #adb5bd;
  --dark-mode-input-bg: #2c2c2c;

  --button-hover-bg: #0056b3;
  --button-active-bg: #004085;
  --icon-button-hover-bg: rgba(0, 0, 0, 0.1);
  --icon-button-active-bg: rgba(0, 0, 0, 0.15);
  --input-focus-border: #80bdff; /* Bootstrap-like focus color */
  --input-focus-shadow: rgba(0, 123, 255, 0.25);
}

.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.6);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
  backdrop-filter: blur(5px);
}

.start-livechat-modal-content {
  background: var(--background-color);
  padding: 28px; /* Slightly adjusted padding */
  border-radius: var(--border-radius); /* Consistent border radius */
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.12); /* Refined shadow */
  max-width: 500px;
  width: 100%;
  display: flex;
  flex-direction: column;
  gap: 20px; /* Adjusted gap */
  position: relative;
  animation: modalFadeIn var(--animation-duration) ease-out;
  border: 1px solid var(--border-color); /* Subtle border */
}

@keyframes modalFadeIn {
  from { opacity: 0; transform: scale(0.9); }
  to { opacity: 1; transform: scale(1); }
}

.modal-title {
  margin: 0;
  font-size: 24px; /* Slightly smaller title */
  font-weight: 600; /* Adjusted weight */
  color: var(--text-color); /* Use text color variable */
  display: flex;
  align-items: center;
  gap: 10px; /* Adjusted gap */
}

.modal-title svg {
  color: var(--primary-color); /* Color the icon in the title */
}

.close-button {
  position: absolute;
  top: 16px; /* Adjusted position */
  right: 16px; /* Adjusted position */
  background: transparent;
  border: none;
  cursor: pointer;
  color: var(--text-secondary);
  padding: 8px; /* Add padding for better click area */
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: color var(--animation-duration) ease, transform var(--animation-duration) ease, background-color var(--animation-duration) ease;
}

.close-button:hover {
  color: var(--primary-color);
  background-color: var(--icon-button-hover-bg);
  transform: scale(1.1);
}

.close-button:active {
  background-color: var(--icon-button-active-bg);
  transform: scale(1.05);
}

.form-container {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.form-group label {
  font-size: 15px; /* Adjusted size */
  font-weight: 500; /* Adjusted weight */
  color: var(--text-secondary); /* Use text secondary color */
  display: flex;
  align-items: center;
  gap: 8px;
}

.form-group label svg {
  color: var(--primary-color); /* Color icons in labels */
}

.form-group select,
.form-group input,
.form-group textarea {
  padding: 10px 14px; /* Adjusted padding */
  border: 1px solid var(--border-color); /* Use variable and thinner border */
  border-radius: var(--border-radius); /* Consistent border radius */
  font-size: 15px; /* Adjusted font size */
  width: 100%;
  box-sizing: border-box;
  transition: border-color var(--animation-duration) ease, box-shadow var(--animation-duration) ease;
  background-color: var(--background-color);
  color: var(--text-color);
}

.form-group select:focus,
.form-group input:focus,
.form-group textarea:focus {
  outline: none;
  border-color: var(--input-focus-border);
  box-shadow: 0 0 0 0.2rem var(--input-focus-shadow);
}

.form-group select:hover,
.form-group input:hover,
.form-group textarea:hover {
  border-color: var(--text-secondary); /* Darken border slightly on hover */
}

.form-group select:disabled,
.form-group input:disabled,
.form-group textarea:disabled {
  background-color: #ecf0f1;
  color: #95a5a6;
  cursor: not-allowed;
}

.form-group select {
  cursor: pointer;
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236c757d' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e"); /* Use var(--text-secondary) for arrow */
  background-position: right 14px center; /* Adjusted position */
  background-repeat: no-repeat;
  background-size: 18px; /* Adjusted size */
  padding-right: 40px; /* Adjusted padding */
  appearance: none;
}

.form-group textarea {
  resize: vertical;
  min-height: 120px;
  font-family: inherit;
}

.modal-actions {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  gap: 16px;
  margin-top: 16px;
}

.start-conversation-btn { /* Renamed from .send-button to match TSX */
  padding: 12px 20px; /* Adjusted padding */
  background-color: var(--primary-color);
  background-image: var(--primary-gradient);
  color: white;
  border: none;
  border-radius: var(--border-radius);
  cursor: pointer;
  font-size: 16px;
  font-weight: 500; /* Adjusted weight */
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px; /* Adjusted gap */
  transition: background-color var(--animation-duration) ease, transform var(--animation-duration) ease, box-shadow var(--animation-duration) ease;
  width: 100%;
  box-shadow: 0 2px 4px rgba(0, 123, 255, 0.2);
}

.start-conversation-btn:hover:not(:disabled) {
  background-color: var(--button-hover-bg);
  transform: translateY(-1px);
  box-shadow: 0 3px 6px rgba(0, 123, 255, 0.3);
}

.start-conversation-btn:active:not(:disabled) {
  background-color: var(--button-active-bg);
  transform: translateY(0px);
  box-shadow: 0 1px 3px rgba(0, 123, 255, 0.2);
}

.start-conversation-btn:disabled {
  background-color: #d1d5da;
  background-image: none;
  color: #868e96;
  cursor: not-allowed;
  box-shadow: none;
}

.start-conversation-btn svg {
  transition: transform var(--animation-duration) ease;
}

.start-conversation-btn:hover:not(:disabled) svg {
  transform: translateX(3px);
}

/* Preview Recipients Button */
.preview-recipients-btn {
  padding: 10px 16px;
  background-color: var(--secondary-color);
  color: var(--text-color);
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  transition: background-color var(--animation-duration) ease, border-color var(--animation-duration) ease;
  width: 100%;
}

.preview-recipients-btn:hover:not(:disabled) {
  background-color: var(--hover-bg);
  border-color: var(--text-secondary);
}

.preview-recipients-btn:disabled {
  background-color: #f8f9fa;
  color: #868e96;
  cursor: not-allowed;
}

/* Filtered Users Section */
.filtered-users-section {
  background-color: #f8f9fa;
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
  padding: 16px;
  margin-top: 8px;
}

.filtered-users-section h4 {
  margin: 0 0 12px 0;
  font-size: 16px;
  font-weight: 600;
  color: var(--text-color);
}

.loading-users {
  display: flex;
  align-items: center;
  gap: 8px;
  color: var(--text-secondary);
  font-size: 14px;
}

.loading-spinner-small {
  width: 16px;
  height: 16px;
  border: 2px solid var(--border-color);
  border-top: 2px solid var(--primary-color);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.users-count {
  background-color: var(--primary-color);
  color: white;
  padding: 6px 12px;
  border-radius: 20px;
  font-size: 12px;
  font-weight: 500;
  display: inline-block;
  margin-bottom: 12px;
}

.users-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 12px;
}

.user-card {
  background-color: white;
  border: 1px solid var(--border-color);
  border-radius: 8px;
  padding: 12px;
  display: flex;
  align-items: center;
  gap: 10px;
  transition: box-shadow var(--animation-duration) ease;
}

.user-card:hover {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.user-avatar {
  width: 36px;
  height: 36px;
  border-radius: 50%;
  background-color: var(--primary-color);
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  font-weight: 600;
  flex-shrink: 0;
}

.user-info {
  flex: 1;
  min-width: 0;
}

.user-name {
  font-size: 14px;
  font-weight: 500;
  color: var(--text-color);
  margin-bottom: 2px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.user-role {
  font-size: 12px;
  color: var(--text-secondary);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.no-users-message {
  text-align: center;
  padding: 20px;
  color: var(--text-secondary);
}

.no-users-message p {
  margin: 8px 0;
  font-size: 14px;
}

.no-users-message p:first-child {
  color: #dc3545;
  font-weight: 500;
}

/* Responsive design */
@media (max-width: 640px) {
  .start-livechat-modal-content {
    margin: 16px;
    max-width: calc(100vw - 32px);
    padding: 24px;
  }

  .modal-title {
    font-size: 24px;
  }

  .form-group label {
    font-size: 14px;
  }

  .form-group select,
  .form-group input,
  .form-group textarea {
    font-size: 14px;
    padding: 10px 14px;
  }

  .send-button {
    font-size: 14px;
    padding: 10px 20px;
  }

  .users-grid {
    grid-template-columns: 1fr;
  }

  .user-card {
    padding: 10px;
  }

  .user-avatar {
    width: 32px;
    height: 32px;
    font-size: 11px;
  }
}

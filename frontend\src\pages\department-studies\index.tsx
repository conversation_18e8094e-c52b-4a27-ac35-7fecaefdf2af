import { useState, useMemo, useEffect, useCallback } from "react";
import { useNavigate } from "react-router-dom";
import { Search, FileText, Calendar, User, ArrowUpRight, AlertTriangle } from "lucide-react";
import { toast } from "sonner";
import DataTable from "@/components/common/DataTable";
import { useCurrentUserQuery } from "@/hooks/user.query";
import { useStudiesByDepartmentQuery } from "@/hooks/study.query";
import { useCreateDelegationLogMutation, useDelegationLogsQuery, useReapplyDelegationLogMutation } from "@/hooks/delegation.query";
import Preloader from "@/components/common/Preloader";
import Wrapper from "@/components/common/Wrapper";
import ApplyStudyModal from "./ApplyStudyModal";
import "./department-studies.css";

interface Study {
  uuid: string;
  iras: string;
  name: string;
  description: string;
  sponsor: {
    identifier: string;
    first_name: string;
    last_name: string;
  };
  created_at: string;
}

export default function DepartmentStudies(): JSX.Element {
  const navigate = useNavigate();
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedStudy, setSelectedStudy] = useState<Study | null>(null);
  const [showApplyModal, setShowApplyModal] = useState(false);
  
  const { data: currentUser } = useCurrentUserQuery();
  const departmentUuid = currentUser?.department?.uuid || '';
  
  const { data: studiesData, isLoading, isError } = useStudiesByDepartmentQuery(departmentUuid);
  const { data: delegationLogs, isLoading: isLoadingDelegationLogs } = useDelegationLogsQuery();
  const createDelegationLogMutation = useCreateDelegationLogMutation();
  const reapplyDelegationLogMutation = useReapplyDelegationLogMutation();

  // Filter delegation logs to only include current user's applications
  const userDelegationLogs = useMemo(() => {
    if (!delegationLogs?.results || !currentUser) return [];
    
    return delegationLogs.results.filter(log => 
      log.team_member_name === `${currentUser.first_name} ${currentUser.last_name}`
    );
  }, [delegationLogs, currentUser]);

  // Debug logging for delegation logs
  useEffect(() => {
    if (delegationLogs) {
      console.log('Delegation logs loaded:', delegationLogs);
      
      // Filter delegation logs for current user to see what's available
      if (currentUser) {
        const userDelegationLogs = delegationLogs.results?.filter(log => 
          log.team_member_name === `${currentUser.first_name} ${currentUser.last_name}`
        ) || [];
        
        console.log('Current user delegation logs:', {
          currentUser: `${currentUser.first_name} ${currentUser.last_name}`,
          userDelegationLogs: userDelegationLogs.map(log => ({
            study_name: log.study_name,
            status: log.status,
            department_name: log.department_name
          }))
        });
      }
    }
  }, [delegationLogs, currentUser]);

  // Debug logging for studies data
  useEffect(() => {
    if (studiesData) {
      console.log('Studies data loaded:', studiesData);
    }
  }, [studiesData]);

  // Debug logging for current user
  useEffect(() => {
    if (currentUser) {
      console.log('Current user loaded:', currentUser);
    }
  }, [currentUser]);

  // Filter studies based on search term
  const filteredStudies = useMemo(() => {
    // Handle different possible response structures
    const studies = studiesData?.results || studiesData || [];
    
    if (!Array.isArray(studies)) return [];
    
    return studies.filter((study: Study) => {
      const matchesSearch = 
        study.name?.toLowerCase().includes(searchTerm.toLowerCase()) ||
        study.description?.toLowerCase().includes(searchTerm.toLowerCase()) ||
        study.iras?.toLowerCase().includes(searchTerm.toLowerCase()) ||
        `${study.sponsor?.first_name || ''} ${study.sponsor?.last_name || ''}`.toLowerCase().includes(searchTerm.toLowerCase());
      
      return matchesSearch;
    });
  }, [studiesData, searchTerm]);

  const handleApplyForStudy = useCallback((study: Study) => {
    setSelectedStudy(study);
    setShowApplyModal(true);
  }, []);

  const handleConfirmApply = async (selectedTaskUuids: string[]) => {
    if (!selectedStudy || !currentUser) return;

    try {
      // Check if this is a reapplication
      const existingDelegationLog = getDelegationLogForStudy(selectedStudy.uuid);
      const isReapplication = existingDelegationLog && (
        existingDelegationLog.status === 'rejected' || 
        existingDelegationLog.status === 'pi_rejected' || 
        existingDelegationLog.status === 'sponsor_rejected'
      );

      if (isReapplication && existingDelegationLog) {
        // Reapply with updated information
        await reapplyDelegationLogMutation.mutateAsync({
          delegationUuid: existingDelegationLog.uuid,
          data: {
            study_task: "Patient recruitment and data collection for the study",
            selected_task_uuids: selectedTaskUuids
          }
        });

        // Close modal and reset state
        setShowApplyModal(false);
        setSelectedStudy(null);
        
        // Show success toast
        toast.success(`Successfully reapplied for study: ${selectedStudy.name} with ${selectedTaskUuids.length} selected task${selectedTaskUuids.length !== 1 ? 's' : ''}`);
      } else {
        // New application
        await createDelegationLogMutation.mutateAsync({
          study: selectedStudy.uuid,
          department: departmentUuid,
          team_member: currentUser.identifier,
          study_task: "Patient recruitment and data collection for the study",
          selected_task_uuids: selectedTaskUuids
        });

        // Close modal and reset state
        setShowApplyModal(false);
        setSelectedStudy(null);
        
        // Show success toast
        toast.success(`Successfully applied for study: ${selectedStudy.name} with ${selectedTaskUuids.length} selected task${selectedTaskUuids.length !== 1 ? 's' : ''}`);
      }
    } catch (error) {
      console.error("Error applying for study:", error);
      // Show error toast
      toast.error("Failed to apply for study. Please try again.");
    }
  };

  const handleCloseApplyModal = () => {
    setShowApplyModal(false);
    setSelectedStudy(null);
  };

  // Helper function to check if user has already applied for a study
  const hasAppliedForStudy = useCallback((studyUuid: string): boolean => {
    if (!userDelegationLogs || !currentUser) return false;
    
    // Find the study by UUID to get its name, then check delegation logs
    const study = filteredStudies.find(s => s.uuid === studyUuid);
    if (!study) return false;
    
    // Debug logging to understand the data
    console.log('Checking application for study:', {
      studyUuid,
      studyName: study.name,
      currentUserName: `${currentUser.first_name} ${currentUser.last_name}`,
      currentUserIdentifier: currentUser.identifier,
      totalUserDelegationLogs: userDelegationLogs.length,
      userDelegationLogs: userDelegationLogs.map(log => ({
        study_name: log.study_name,
        team_member_name: log.team_member_name,
        status: log.status,
        department_name: log.department_name
      }))
    });
    
    // Check if there's any delegation log for this study and user
    // We need to match by study name
    const hasApplied = userDelegationLogs.some(log => {
      const studyNameMatch = log.study_name?.toLowerCase().trim() === study.name?.toLowerCase().trim();
      
      console.log('Checking user delegation log:', {
        logStudyName: log.study_name,
        studyName: study.name,
        studyNameMatch,
        status: log.status
      });
      
      return studyNameMatch;
    });
    
    console.log('Has applied result:', hasApplied);
    return hasApplied;
  }, [userDelegationLogs, currentUser, filteredStudies]);

  // Helper function to get delegation log for a study
  const getDelegationLogForStudy = useCallback((studyUuid: string) => {
    if (!userDelegationLogs || !currentUser) return null;
    // Find the study by UUID to get its name, then find delegation log
    const study = filteredStudies.find(s => s.uuid === studyUuid);
    if (!study) return null;
    
    // Find delegation log by matching study name
    const delegationLog = userDelegationLogs.find(log => {
      const studyNameMatch = log.study_name?.toLowerCase().trim() === study.name?.toLowerCase().trim();
      
      return studyNameMatch;
    });
    
    console.log('Found delegation log for study:', {
      studyUuid,
      studyName: study.name,
      delegationLog
    });
    
    return delegationLog;
  }, [userDelegationLogs, currentUser, filteredStudies]);

  // Check if the selected study is a reapplication
  const isReapplication = selectedStudy ? (() => {
    const existingDelegationLog = getDelegationLogForStudy(selectedStudy.uuid);
    return Boolean(existingDelegationLog && (
      existingDelegationLog.status === 'rejected' || 
      existingDelegationLog.status === 'pi_rejected' || 
      existingDelegationLog.status === 'sponsor_rejected'
    ));
  })() : false;

  // Helper function to check if delegation is accepted
  const isDelegationAccepted = useCallback((studyUuid: string): boolean => {
    const delegationLog = getDelegationLogForStudy(studyUuid);
    return delegationLog?.status === 'accepted' || delegationLog?.status === 'sponsor_approved' || false;
  }, [getDelegationLogForStudy]);

  // Helper function to get the latest notes for a delegation log
  const getLatestNotes = useCallback((delegationLog: any): { notes: string; author: string; date: string } | null => {
    if (!delegationLog) return null;
    
    const piRespondedAt = delegationLog.pi_responded_at ? new Date(delegationLog.pi_responded_at) : null;
    const sponsorRespondedAt = delegationLog.sponsor_responded_at ? new Date(delegationLog.sponsor_responded_at) : null;
    
    // If both have responded, show the most recent one
    if (piRespondedAt && sponsorRespondedAt) {
      if (sponsorRespondedAt > piRespondedAt) {
        return {
          notes: delegationLog.sponsor_notes || '',
          author: 'Sponsor',
          date: delegationLog.sponsor_responded_at
        };
      } else {
        return {
          notes: delegationLog.pi_notes || '',
          author: 'PI',
          date: delegationLog.pi_responded_at
        };
      }
    }
    
    // If only PI has responded
    if (piRespondedAt && !sponsorRespondedAt) {
      return {
        notes: delegationLog.pi_notes || '',
        author: 'PI',
        date: delegationLog.pi_responded_at
      };
    }
    
    // If only sponsor has responded
    if (sponsorRespondedAt && !piRespondedAt) {
      return {
        notes: delegationLog.sponsor_notes || '',
        author: 'Sponsor',
        date: delegationLog.sponsor_responded_at
      };
    }
    
    return null;
  }, []);

  const handleViewStudy = useCallback((study: Study) => {
    // Only allow viewing if delegation is accepted
    if (!isDelegationAccepted(study.uuid)) {
      toast.error("You can only view study details after your application is accepted.");
      return;
    }
    
    // Navigate to study details page
    navigate(`/study-details/${study.uuid}`);
  }, [isDelegationAccepted, navigate]);

  // Define columns for the DataTable
  const columns = useMemo(() => [
    {
      key: "iras" as keyof Study,
      header: "IRAS Number",
      sortable: true,
      render: (value: unknown): React.ReactNode => {
        return value ? String(value) : "N/A";
      },
    },
    {
      key: "name" as keyof Study,
      header: "Study Name",
      sortable: true,
      render: (value: unknown): React.ReactNode => {
        return value ? String(value) : "N/A";
      },
    },
    {
      key: "description" as keyof Study,
      header: "Description",
      sortable: true,
      render: (value: unknown): React.ReactNode => {
        const description = value ? String(value) : "";
        return description.length > 100 
          ? `${description.substring(0, 100)}...` 
          : description || "N/A";
      },
    },
    {
      key: "sponsor" as keyof Study,
      header: "Sponsor",
      sortable: true,
      render: (value: unknown): React.ReactNode => {
        const sponsor = value as Study["sponsor"];
        return sponsor ? `${sponsor.first_name} ${sponsor.last_name}` : "N/A";
      },
    },
    {
      key: "created_at" as keyof Study,
      header: "Created Date",
      sortable: true,
      render: (value: unknown): React.ReactNode => {
        return value ? new Date(String(value)).toLocaleDateString() : "N/A";
      },
    },
    {
      key: "status" as keyof Study,
      header: "Application Status",
      sortable: false,
      render: (_: unknown, row?: Study): React.ReactNode => {
        if (!row || !hasAppliedForStudy(row.uuid)) {
          return <span className="dept-studies-status-badge dept-studies-status-not-applied">Not Applied</span>;
        }
        
        const delegationLog = getDelegationLogForStudy(row.uuid);
        if (!delegationLog) return <span className="dept-studies-status-badge dept-studies-status-not-applied">Not Applied</span>;
        
        switch (delegationLog.status) {
          case 'accepted':
          case 'sponsor_approved':
            return <span className="dept-studies-status-badge dept-studies-status-accepted">Accepted</span>;
          case 'pending_pi':
          case 'pi_pending':
          case 'pending_sponsor':
          case 'sponsor_pending':
            return <span className="dept-studies-status-badge dept-studies-status-pending">Pending Review</span>;
          case 'rejected':
          case 'pi_rejected':
          case 'sponsor_rejected':
            return <span className="dept-studies-status-badge dept-studies-status-rejected">Rejected</span>;
          default:
            return <span className="dept-studies-status-badge dept-studies-status-pending">{delegationLog.status}</span>;
        }
      },
    },
    {
      key: "notes" as keyof Study,
      header: "Latest Notes",
      sortable: false,
      render: (_: unknown, row?: Study): React.ReactNode => {
        if (!row || !hasAppliedForStudy(row.uuid)) {
          return <span>-</span>;
        }
        
        const delegationLog = getDelegationLogForStudy(row.uuid);
        if (!delegationLog) return <span>-</span>;
        
        const latestNotes = getLatestNotes(delegationLog);
        if (!latestNotes || !latestNotes.notes) {
          return <span>No notes</span>;
        }
        
        const notesText = latestNotes.notes.length > 50 
          ? `${latestNotes.notes.substring(0, 50)}...` 
          : latestNotes.notes;
        
        return (
          <div className="dept-studies-notes-content">
            <div className="dept-studies-notes-text" title={latestNotes.notes}>
              {notesText}
            </div>
            <div className="dept-studies-notes-meta">
              {latestNotes.author} - {new Date(latestNotes.date).toLocaleDateString()}
            </div>
          </div>
        );
      },
    },
  ], [hasAppliedForStudy, getDelegationLogForStudy, getLatestNotes]);

  // Define actions for the DataTable
  const actions = useMemo(() => [
    {
      icon: <ArrowUpRight size={16} />,
      tooltipText: (study: Study) => {
        if (hasAppliedForStudy(study.uuid)) {
          const delegationLog = getDelegationLogForStudy(study.uuid);
          if (delegationLog?.status === 'accepted' || delegationLog?.status === 'sponsor_approved') {
            return "View Study Details";
          } else if (delegationLog?.status === 'pending_pi' || delegationLog?.status === 'pi_pending') {
            return "Application Pending PI Review";
          } else if (delegationLog?.status === 'pending_sponsor' || delegationLog?.status === 'sponsor_pending') {
            return "Application Pending Sponsor Review";
          } else if (delegationLog?.status === 'rejected' || delegationLog?.status === 'pi_rejected' || delegationLog?.status === 'sponsor_rejected') {
            return "Application Rejected";
          }
        }
        return "View Study Details (Apply first)";
      },
      onClick: (study: Study) => handleViewStudy(study),
      disabled: (study: Study) => !isDelegationAccepted(study.uuid)
    },
    {
      icon: <FileText size={16} />,
      tooltipText: (study: Study) => {
        if (hasAppliedForStudy(study.uuid)) {
          const delegationLog = getDelegationLogForStudy(study.uuid);
          if (delegationLog?.status === 'accepted' || delegationLog?.status === 'sponsor_approved') {
            return "Already Accepted";
          } else if (delegationLog?.status === 'pending_pi' || delegationLog?.status === 'pi_pending') {
            return "Application Pending PI Review";
          } else if (delegationLog?.status === 'pending_sponsor' || delegationLog?.status === 'sponsor_pending') {
            return "Application Pending Sponsor Review";
          } else if (delegationLog?.status === 'rejected' || delegationLog?.status === 'pi_rejected' || delegationLog?.status === 'sponsor_rejected') {
            return "Application Rejected - Click to Reapply";
          }
        }
        return "Apply for Study";
      },
      onClick: (study: Study) => handleApplyForStudy(study),
      disabled: (study: Study) => {
        if (!hasAppliedForStudy(study.uuid)) return false;
        
        const delegationLog = getDelegationLogForStudy(study.uuid);
        if (!delegationLog) return true;
        
        // Allow re-application if the status is rejected
        const isRejected = delegationLog.status === 'rejected' || 
                          delegationLog.status === 'pi_rejected' || 
                          delegationLog.status === 'sponsor_rejected';
        
        // Disable if accepted, pending, or any other status (but allow re-application for rejected)
        return !isRejected;
      }
    }
  ], [hasAppliedForStudy, getDelegationLogForStudy, isDelegationAccepted, handleViewStudy, handleApplyForStudy]);

  if (isLoading || isLoadingDelegationLogs || createDelegationLogMutation.isPending || reapplyDelegationLogMutation.isPending) {
    return (
      <Wrapper>
        <Preloader />
        <div className="dept-studies-container" style={{paddingBottom: "88px"}}>
          <div className="dept-studies-loading">
            <div className="dept-studies-loading-content">
              <div className="dept-studies-spinner"></div>
              <div className="dept-studies-loading-text">Loading department studies...</div>
            </div>
          </div>
        </div>
      </Wrapper>
    );
  }

  if (isError) {
    return (
      <Wrapper>
        <Preloader />
        <div className="dept-studies-container" style={{paddingBottom: "88px"}}>
          <div className="dept-studies-error">
            <div className="dept-studies-error-content">
              <div className="dept-studies-error-icon">
                <AlertTriangle size={24} />
              </div>
              <h3>Error Loading Studies</h3>
              <p>There was an error loading the studies for your department. Please try again later.</p>
            </div>
          </div>
        </div>
      </Wrapper>
    );
  }

  return (
    <Wrapper>
      <Preloader />
      <div className="dept-studies-container" style={{paddingBottom: "88px"}}>
        {/* Header Section */}
        <div className="dept-studies-header">
          <div className="dept-studies-header-content">
            <div className="dept-studies-header-left">
              <div className="dept-studies-title-section">
                <div className="dept-studies-icon-wrapper">
                  <FileText size={24} />
                </div>
                <div className="dept-studies-title-content">
                  <h1>Department Studies</h1>
                  <p>Browse and apply for studies available in your department</p>
                </div>
              </div>

              {/* Summary Stats */}
              <div className="dept-studies-stats">
                <div className="dept-studies-stat-card">
                  <div className="dept-studies-stat-icon total">
                    <FileText size={20} />
                  </div>
                  <div className="dept-studies-stat-content">
                    <div className="dept-studies-stat-number">{studiesData?.count || filteredStudies.length}</div>
                    <div className="dept-studies-stat-label">Total Studies</div>
                  </div>
                </div>
                <div className="dept-studies-stat-card">
                  <div className="dept-studies-stat-icon current">
                    <Calendar size={20} />
                  </div>
                  <div className="dept-studies-stat-content">
                    <div className="dept-studies-stat-number">{studiesData?.current_page || 1}</div>
                    <div className="dept-studies-stat-label">Current Page</div>
                  </div>
                </div>
                <div className="dept-studies-stat-card">
                  <div className="dept-studies-stat-icon per-page">
                    <User size={20} />
                  </div>
                  <div className="dept-studies-stat-content">
                    <div className="dept-studies-stat-number">{studiesData?.page_size || 10}</div>
                    <div className="dept-studies-stat-label">Studies per Page</div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Search Section */}
        <div className="dept-studies-search-section">
          <div className="dept-studies-search-wrapper">
            <Search size={18} className="dept-studies-search-icon" />
            <input
              className="dept-studies-search-input"
              type="text"
              placeholder="Search studies by name, description, IRAS number, or sponsor..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
            />
          </div>
        </div>

        {/* Studies Table */}
        <div className="dept-studies-table-section">
          <div className="dept-studies-table-header">
            <div className="dept-studies-table-title">
              <h3>
                <FileText size={20} />
                Available Studies
              </h3>
              <p>Select a study to view details or apply for participation</p>
            </div>
          </div>
          <div className="dept-studies-table-content">
            <DataTable
              data={filteredStudies}
              columns={columns}
              actions={actions}
              noDataMessage="No studies found for your department"
              defaultItemsPerPage={10}
              globalFilterPlaceholder="Search studies..."
            />
          </div>

          {/* Pagination Info */}
          {studiesData && studiesData.total_pages > 1 && (
            <div className="dept-studies-pagination-info">
              <p>
                Page {studiesData.current_page} of {studiesData.total_pages} 
                ({studiesData.count} total studies)
              </p>
            </div>
          )}
        </div>

        {/* Apply Study Modal */}
        <ApplyStudyModal
          isOpen={showApplyModal}
          study={selectedStudy}
          onClose={handleCloseApplyModal}
          onConfirm={handleConfirmApply}
          isLoading={createDelegationLogMutation.isPending || reapplyDelegationLogMutation.isPending}
          isReapplication={isReapplication}
        />
      </div>
    </Wrapper>
  );
}

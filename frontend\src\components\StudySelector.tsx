import React from 'react';
import { useStudiesQuery } from '@/hooks/study.query';
import { Study } from '@/store/scheduleEventState';
import NurtifySelect from './NurtifySelect';

interface StudySelectorProps {
  value: string;
  onChange: (value: string) => void;
}

const StudySelector: React.FC<StudySelectorProps> = ({ value, onChange }) => {
  const { data: studies, isLoading, isError } = useStudiesQuery();

  // Create options for the select component
  const options = React.useMemo(() => {
    const defaultOption = { value: '', label: 'Select a study' };

    if (!studies || isLoading || isError) {
      return [defaultOption];
    }

    return [
      defaultOption,
      ...studies.map((study: Study) => ({
        value: study.uuid || study.id,
        label: study.name
      }))
    ];
  }, [studies, isLoading, isError]);

  const handleChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    onChange(e.target.value);
  };

  return (
    <NurtifySelect
      name="study"
      value={value}
      onChange={handleChange}
      options={options}
      disabled={isLoading || isError}
    />
  );
};

export default StudySelector;

/* Enhanced Form Builder Styling */
.rebuild-form-container {
  position: relative;
  display: flex;
  justify-content: center;
  padding: 24px;
  width: 100%;
  background-color: #f9f9f9;
}

.rebuild-form {
  padding: 28px;
  background-color: #fff;
  max-width: 1100px;
  margin: 0 auto;
  border-radius: 12px;
  box-shadow: 0 5px 30px rgba(0, 0, 0, 0.08);
  width: 100%;
  border: 1px solid rgba(0, 0, 0, 0.05);
}

.rebuild-form header {
  text-align: center;
  margin-bottom: 28px;
  padding-bottom: 20px;
  border-bottom: 1px solid rgba(0, 0, 0, 0.08);
  position: relative;
}

.rebuild-form header::after {
  content: '';
  position: absolute;
  bottom: -1px;
  left: 50%;
  transform: translateX(-50%);
  width: 100px;
  height: 3px;
  background: linear-gradient(to right, #37b7c3, #2ea2b0);
  border-radius: 3px;
}

.rebuild-form header h1 {
  font-size: 2.2em;
  margin: 0;
  color: #2c3e50;
  font-weight: 700;
  margin-bottom: 12px;
  letter-spacing: -0.5px;
}

.rebuild-form header p {
  font-size: 1.15em;
  color: #666;
  line-height: 1.6;
  max-width: 80%;
  margin: 0 auto;
}

/* Form content adjustments */
.form-content-wrapper {
  display: flex;
  margin-top: 24px;
  align-items: flex-start;
  width: 100%;
}

.form-section {
  margin-bottom: 28px;
  padding: 24px;
  background-color: #fff;
  border-radius: 12px;
  box-shadow: 0 3px 15px rgba(0, 0, 0, 0.06);
  flex: 1;
  transition: all 0.3s ease;
  width: 100%;
  border: 1px solid rgba(0, 0, 0, 0.03);
}

.form-section h2 {
  font-size: 1.6em;
  margin-bottom: 14px;
  color: #2c3e50;
  font-weight: 700;
  padding-bottom: 10px;
  border-bottom: 2px solid #37b7c3;
  position: relative;
}

.form-section p {
  font-size: 1.05em;
  color: #666;
  margin-bottom: 22px;
  line-height: 1.7;
}

.form-question {
  margin-bottom: 28px;
  padding: 24px;
  border: none;
  border-radius: 12px;
  background-color: #f0fcfc;
  transition: all 0.3s ease;
  box-shadow: 0 3px 10px rgba(55, 183, 195, 0.15);
  position: relative;
  overflow: hidden;
  border: 1px solid rgba(55, 183, 195, 0.1);
  display: flex;
  flex-direction: column;
  gap: 1rem;
  padding: 1rem;
  border-radius: 0.5rem;
  margin-bottom: 1rem;
}

.form-question:hover {
  box-shadow: 0 5px 15px rgba(55, 183, 195, 0.25);
  transform: translateY(-3px);
  border-color: rgba(55, 183, 195, 0.2);
}

.form-question:before {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  height: 100%;
  width: 4px;
  background-color: #37b7c3;
}

.question-label {
  display: block;
  font-weight: 600;
  margin-bottom: 20px;
  font-size: 1.1em;
  color: #2c3e50;
  line-height: 1.5;
  letter-spacing: 0.2px;
}

/* Non-clinical badge */
.non-clinical-badge {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(45deg, #ff9800, #ff7043);
  color: white;
  font-size: 0.75em;
  font-weight: 600;
  padding: 4px 12px;
  border-radius: 20px;
  margin-left: 10px;
  vertical-align: middle;
  box-shadow: 0 2px 4px rgba(255, 152, 0, 0.3);
  letter-spacing: 0.5px;
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% { box-shadow: 0 0 0 0 rgba(255, 152, 0, 0.4); }
  70% { box-shadow: 0 0 0 5px rgba(255, 152, 0, 0); }
  100% { box-shadow: 0 0 0 0 rgba(255, 152, 0, 0); }
}

/* Question number */
.question-number {
  display: inline-block;
  font-size: 0.85em;
  color: #37b7c3;
  margin-bottom: 10px;
  font-weight: 600;
  background-color: rgba(55, 183, 195, 0.1);
  padding: 5px 12px;
  border-radius: 20px;
  letter-spacing: 0.3px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

/* Single Choice Question */
.single-choice-question {
  display: flex;
  flex-direction: column;
  gap: 14px;
}

.single-choice-question .nurtify-radio {
  margin-bottom: 0;
  border-radius: 8px;
  transition: all 0.2s ease;
  padding: 12px 16px;
  border: 1px solid transparent;
}

.single-choice-question .nurtify-radio:hover {
  background-color: rgba(55, 183, 195, 0.05);
  transform: translateY(-2px);
  border-color: rgba(55, 183, 195, 0.2);
}

.single-choice-question .nurtify-radio.selected {
  background-color: rgba(55, 183, 195, 0.15);
  border-color: #37b7c3;
  box-shadow: 0 2px 5px rgba(55, 183, 195, 0.1);
}

/* Multiple Choice Question */
.multiple-choice-question {
  display: flex;
  flex-direction: column;
  gap: 14px;
}

.multiple-choice-question .nurtify-radio.custom-checkbox {
  border-radius: 8px;
  transition: all 0.2s ease;
  padding: 12px 16px;
  border: 1px solid transparent;
}

.multiple-choice-question .nurtify-radio.custom-checkbox:hover {
  background-color: rgba(55, 183, 195, 0.05);
  transform: translateY(-2px);
  border-color: rgba(55, 183, 195, 0.2);
}

.multiple-choice-question .nurtify-radio.custom-checkbox.selected {
  background-color: rgba(55, 183, 195, 0.15);
  border-color: #37b7c3;
  box-shadow: 0 2px 5px rgba(55, 183, 195, 0.1);
}

/* Boolean Question */
.boolean-question {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
}

.boolean-question .nurtify-radio {
  min-width: 120px;
  text-align: center;
  justify-content: center;
}

/* Input styles */
.short-text-question input,
.long-text-question textarea,
.multiple-text-boxes-question input,
.dropdown-question select {
  border: 2px solid #dde5e7 !important;
  border-radius: 8px;
  padding: 14px;
  font-size: 16px;
  transition: all 0.2s ease;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.03);
}

.short-text-question input:focus,
.long-text-question textarea:focus,
.multiple-text-boxes-question input:focus,
.signature-inputs-left input:focus,
.table-question input:focus {
  border-color: #37b7c3 !important;
  box-shadow: 0 0 0 2px rgba(55, 183, 195, 0.2) !important;
  outline: none;
}

/* Add clearer hover effects for inputs */
.short-text-question input:hover,
.long-text-question textarea:hover,
.multiple-text-boxes-question input:hover,
.signature-inputs-left input:hover,
.table-question input:hover {
  border-color: #91d5ff !important;
  box-shadow: 0 3px 8px rgba(0, 0, 0, 0.05) !important;
}

/* Table styling */
.table-question .form-table {
  width: 100%;
  border-collapse: separate;
  border-spacing: 0;
  margin-top: 15px;
  border-radius: 10px;
  overflow: hidden;
  box-shadow: 0 3px 10px rgba(0, 0, 0, 0.1);
}

.table-question .form-table th,
.table-question .form-table td {
  border: 1px solid #dde5e7;
  padding: 14px;
  text-align: center;
}

.table-question .form-table th {
  background: linear-gradient(to right, #37b7c3, #2ea2b0);
  color: white;
  font-weight: 600;
  padding: 16px 14px;
  letter-spacing: 0.3px;
  text-transform: uppercase;
  font-size: 0.9em;
}

.table-question .form-table td {
  background-color: white;
  transition: background-color 0.2s ease;
}

.table-question .form-table tr:hover td {
  background-color: rgba(55, 183, 195, 0.02);
}

.table-question .form-table tr:nth-child(even) td {
  background-color: #f9fafb;
}

.table-question .form-table tr:nth-child(even):hover td {
  background-color: #f0f7f8;
}

/* Delete row icon */
.delete-row-icon {
  background: none;
  border: none;
  color: #e74c3c;
  cursor: pointer;
  padding: 8px;
  border-radius: 50%;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.delete-row-icon:hover {
  background-color: rgba(231, 76, 60, 0.1);
  transform: scale(1.15);
  box-shadow: 0 2px 5px rgba(231, 76, 60, 0.2);
}

/* Range input styling */
.range-question .range-input {
  padding: 16px;
  background-color: white;
  border-radius: 10px;
  box-shadow: 0 3px 8px rgba(0, 0, 0, 0.05);
  border: 1px solid #dde5e7;
}

.range-labels {
  display: flex;
  justify-content: space-between;
  margin-top: 12px;
  font-weight: 500;
  color: #666;
}

/* Tab bar */
.tab-bar {
  display: flex;
  flex-wrap: wrap;
  gap: 6px;
  border-bottom: 1px solid #dde5e7;
  margin-bottom: 24px;
  padding-bottom: 1px;
  position: relative;
}

.tab {
  padding: 14px 24px;
  cursor: pointer;
  border: 1px solid transparent;
  border-bottom: none;
  margin-right: 5px;
  background-color: #f9f9f9;
  transition: all 0.2s ease;
  border-radius: 10px 10px 0 0;
  font-weight: 500;
  color: #666;
  position: relative;
  overflow: hidden;
}

.tab:hover {
  background-color: #e0f7f9;
  color: #37b7c3;
}

.tab.active {
  background-color: #fff;
  border: 1px solid #dde5e7;
  border-bottom: 3px solid #37b7c3;
  font-weight: 600;
  color: #37b7c3;
  box-shadow: 0 -2px 5px rgba(0, 0, 0, 0.03);
}

/* Navigation buttons */
.navigation-buttons {
  margin-top: 36px;
  display: flex;
  justify-content: space-between;
  gap: 20px;
}

.navigation-buttons button {
  padding: 14px 28px;
  font-size: 16px;
  border: none;
  border-radius: 10px;
  background: linear-gradient(to right, #37b7c3, #2ea2b0);
  color: #fff;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 10px;
  min-width: 140px;
  justify-content: center;
  box-shadow: 0 4px 10px rgba(55, 183, 195, 0.3);
  font-weight: 500;
  letter-spacing: 0.5px;
  position: relative;
  overflow: hidden;
}

.navigation-buttons button::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: all 0.3s ease;
}

.navigation-buttons button:hover::before {
  left: 100%;
}

.navigation-buttons button:hover {
  background: linear-gradient(to right, #2ea2b0, #268891);
  transform: translateY(-2px);
  box-shadow: 0 6px 15px rgba(55, 183, 195, 0.4);
}

.navigation-buttons button:active {
  transform: translateY(1px);
  box-shadow: 0 2px 5px rgba(55, 183, 195, 0.3);
}

.navigation-buttons button:disabled {
  background: linear-gradient(to right, #b3b3b3, #9e9e9e);
  cursor: not-allowed;
  box-shadow: none;
  transform: none;
}

.navigation-buttons button:focus {
  outline: none;
  box-shadow: 0 0 0 3px rgba(55, 183, 195, 0.4), 0 4px 10px rgba(55, 183, 195, 0.3);
}

/* Add row button */
.add-row-button {
  margin-top: 20px;
  padding: 12px 20px;
  background: linear-gradient(to right, #37b7c3, #2ea2b0);
  color: white;
  border: none;
  border-radius: 8px;
  cursor: pointer;
  font-weight: 500;
  transition: all 0.3s ease;
  box-shadow: 0 2px 6px rgba(55, 183, 195, 0.3);
  display: flex;
  align-items: center;
  gap: 8px;
  position: relative;
  overflow: hidden;
}

.add-row-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: all 0.3s ease;
}

.add-row-button:hover::before {
  left: 100%;
}

.add-row-button:hover {
  background: linear-gradient(to right, #2ea2b0, #268891);
  transform: translateY(-2px);
  box-shadow: 0 4px 10px rgba(55, 183, 195, 0.35);
}

.add-row-button:active {
  transform: translateY(1px);
}

.add-row-button:focus {
  outline: none;
  box-shadow: 0 0 0 3px rgba(55, 183, 195, 0.4), 0 2px 6px rgba(55, 183, 195, 0.3);
}

/* Status messages */
.submission-error,
.submission-success,
.submission-loading {
  padding: 18px;
  border-radius: 10px;
  margin-bottom: 24px;
  display: flex;
  align-items: center;
  gap: 14px;
  animation: slideDown 0.4s ease-out;
  box-shadow: 0 3px 8px rgba(0, 0, 0, 0.05);
}

.submission-error {
  background-color: #fdeaea;
  border-left: 5px solid #f44336;
  color: #d32f2f;
}

.submission-success {
  background-color: #e8f5e9;
  border-left: 5px solid #4caf50;
  color: #2e7d32;
}

.submission-loading {
  background-color: #e3f2fd;
  border-left: 5px solid #2196f3;
  color: #1976d2;
}

@keyframes slideDown {
  from {
    transform: translateY(-20px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

/* Required field indicator - pulsing star */
.question-label::after {
  content: " *";
  color: #f44336;
  font-weight: bold;
  display: inline-block;
  margin-left: 4px;
  animation: pulse-star 1.5s infinite;
}

.question-label:not(:has(+ [required="true"]))::after {
  content: "";
  margin-left: 0;
}

@keyframes pulse-star {
  0% { opacity: 1; transform: scale(1); }
  50% { opacity: 0.6; transform: scale(1.2); }
  100% { opacity: 1; transform: scale(1); }
}

/* Hover tooltip for clear field action */
.short-text-question:hover::after,
.long-text-question:hover::after {
  content: "Press Escape to clear";
  position: absolute;
  top: 10px;
  right: 10px;
  background-color: rgba(0, 0, 0, 0.8);
  color: white;
  font-size: 12px;
  padding: 6px 10px;
  border-radius: 6px;
  opacity: 0;
  transition: opacity 0.3s ease;
  z-index: 5;
}

.short-text-question:hover:hover::after,
.long-text-question:hover:hover::after {
  opacity: 1;
}

/* Filter information styles */
.filter-info {
  margin-top: 12px;
  padding: 10px 18px;
  background-color: #e8f5e9;
  border-left: 4px solid #4caf50;
  border-radius: 6px;
  font-size: 0.95rem;
  color: #2e7d32;
  display: inline-block;
  margin-right: 12px;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
  animation: fadeIn 0.5s ease-out;
}

.filter-info.patients-mode {
  background-color: #e3f2fd;
  border-left: 4px solid #2196f3;
  color: #1976d2;
}

@keyframes fadeIn {
  from { opacity: 0; transform: translateY(10px); }
  to { opacity: 1; transform: translateY(0); }
}

/* Styles for questions that were previously answered */
.previously-answered {
  position: relative;
  padding-left: 12px;
  border-left: 4px solid #37B7C3;
  background-color: rgba(55, 183, 195, 0.05);
}

.answered-indicator {
  color: #37B7C3;
  font-size: 0.85rem;
  font-weight: normal;
  background-color: rgba(55, 183, 195, 0.1);
  padding: 2px 8px;
  border-radius: 10px;
  margin-left: 6px;
}

.clear-answer-option {
  margin-top: 10px;
  display: flex;
  justify-content: flex-end;
}

.clear-answer-btn {
  background-color: transparent;
  color: #6c757d;
  border: 1px solid #dee2e6;
  border-radius: 6px;
  padding: 5px 12px;
  font-size: 0.85rem;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 6px;
  transition: all 0.2s ease;
}

.clear-answer-btn:hover {
  background-color: #f8f9fa;
  color: #dc3545;
  border-color: #dc3545;
  transform: translateY(-1px);
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
}

/* Banner for edit mode */
.edit-mode-banner {
  background-color: #fff3cd;
  border: 1px solid #ffeeba;
  color: #856404;
  border-radius: 8px;
  padding: 12px 18px;
  margin-bottom: 24px;
  display: flex;
  align-items: center;
  gap: 12px;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
  animation: fadeIn 0.5s ease-out;
}

/* Styles for invalid/required fields */
.invalid-required {
  border-left: 4px solid #dc3545;
  padding-left: 12px;
  background-color: rgba(220, 53, 69, 0.05);
  animation: shake 0.5s ease-in-out;
}

@keyframes shake {
  0%, 100% { transform: translateX(0); }
  10%, 30%, 50%, 70%, 90% { transform: translateX(-2px); }
  20%, 40%, 60%, 80% { transform: translateX(2px); }
}

.invalid-label {
  color: #dc3545;
}

.required-indicator {
  color: #dc3545;
  font-size: 0.85rem;
  font-weight: normal;
  background-color: rgba(220, 53, 69, 0.1);
  padding: 2px 8px;
  border-radius: 10px;
  margin-left: 6px;
}

.unanswered-indicator {
  color: #6c757d;
  font-size: 0.85rem;
  font-weight: normal;
  background-color: rgba(108, 117, 125, 0.1);
  padding: 2px 8px;
  border-radius: 10px;
  margin-left: 6px;
}

.error-message {
  color: #dc3545;
  font-size: 0.9rem;
  margin-top: 6px;
  display: flex;
  align-items: center;
  gap: 6px;
}

/* Styles for attached files list */
.attached-files-list {
  margin-top: 12px;
  padding: 14px;
  border: 1px solid #dee2e6;
  border-radius: 8px;
  background-color: #f8f9fa;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
}

.attached-files-list h4 {
  margin-top: 0;
  margin-bottom: 12px;
  font-size: 0.95rem;
  color: #495057;
  font-weight: 600;
}

.attached-files-list ul {
  list-style: none;
  padding: 0;
  margin: 0;
}

.attached-file-item {
  display: flex;
  align-items: center;
  padding: 8px 0;
  margin-bottom: 6px;
  border-bottom: 1px solid #e9ecef;
  transition: all 0.2s ease;
}

.attached-file-item:hover {
  background-color: rgba(55, 183, 195, 0.02);
  padding-left: 5px;
}

.file-type-icon {
  margin-right: 10px;
  font-size: 1.2rem;
}

.file-name {
  flex-grow: 1;
  font-size: 0.9rem;
  color: #495057;
}

.file-size {
  color: #6c757d;
  font-size: 0.8rem;
  margin: 0 12px;
}

.remove-file-btn {
  background: none;
  border: none;
  color: #6c757d;
  cursor: pointer;
  padding: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: all 0.2s ease;
}

.remove-file-btn:hover {
  color: #dc3545;
  background-color: rgba(220, 53, 69, 0.1);
  transform: scale(1.1);
}

/* No questions message */
.no-questions-message {
  padding: 20px;
  background-color: #e3f2fd;
  border-radius: 8px;
  color: #1976d2;
  text-align: center;
  margin: 20px 0;
  border: 1px solid rgba(33, 150, 243, 0.2);
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
}

.no-questions-message p {
  margin-top: 10px;
  margin-bottom: 0;
  font-size: 0.95em;
  color: #1976d2;
}

/* Required fields warning */
.required-fields-warning {
  display: flex;
  align-items: center;
  gap: 10px;
  padding: 12px 16px;
  background-color: rgba(220, 53, 69, 0.05);
  border-radius: 8px;
  color: #dc3545;
  margin-top: 10px;
  margin-bottom: 20px;
  border: 1px solid rgba(220, 53, 69, 0.1);
}

/* Comment styles */
.question-content {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  width: 100%;
}

.question-comment {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  margin-top: 0.5rem;
}

.comment-button {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 1rem;
  border: 1px solid #e2e8f0;
  border-radius: 0.375rem;
  background-color: #f8fafc;
  color: #64748b;
  font-size: 0.875rem;
  cursor: pointer;
  transition: all 0.2s ease;
  width: fit-content;
}

.comment-button:hover {
  background-color: #f1f5f9;
  color: #475569;
}

.comment-button.has-comment {
  background-color: #e0f2fe;
  color: #0369a1;
  border-color: #bae6fd;
}

.comment-button.has-comment:hover {
  background-color: #bae6fd;
  color: #075985;
}

.comment-textarea-container {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  margin-top: 0.5rem;
  padding: 1rem;
  background-color: #f8fafc;
  border: 1px solid #e2e8f0;
  border-radius: 0.375rem;
}

.comment-label {
  font-size: 0.875rem;
  font-weight: 500;
  color: #475569;
}

/* Ensure the textarea takes full width */
.comment-textarea-container textarea {
  width: 100%;
  min-height: 100px;
  padding: 0.75rem;
  border: 1px solid #e2e8f0;
  border-radius: 0.375rem;
  font-size: 0.875rem;
  resize: vertical;
}

.comment-textarea-container textarea:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.1);
}

/* Add some spacing between the question and comment section */
.form-question {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  padding: 1rem;
  border: 1px solid #e2e8f0;
  border-radius: 0.5rem;
  margin-bottom: 1rem;
}

/* Ensure the question content takes full width */
.form-question > * {
  width: 100%;
}

/* Comment details styles */
.comment-details {
  margin-top: 0.5rem;
  font-size: 0.85em;
  color: #64748b;
  display: flex;
  gap: 1.5em;
  align-items: center;
}
.comment-by {
  font-weight: 500;
}
.comment-at {
  color: #94a3b8;
}

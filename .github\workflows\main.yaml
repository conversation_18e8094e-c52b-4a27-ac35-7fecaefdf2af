name: CI/CD Pipeline Dev FrontEnd

on:
  push:
    branches:
      - dev

jobs:
  build-and-test:
    runs-on: ubuntu-latest
    
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4
        
      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '20'
          cache: 'npm'
          cache-dependency-path: 'frontend/package.json'
          
      - name: Install dependencies
        run: cd frontend && npm ci
        
      - name: Lint code
        run: cd frontend && npm run lint
        
      - name: Build application
        run: cd frontend && npm run build
          
  deploy:
    needs: build-and-test
    runs-on: ubuntu-latest

    steps:
      - name: Checkout repository
        uses: actions/checkout@v4

      - name: Setup SSH
        run: |
          mkdir -p ~/.ssh
          echo "${{ secrets.SERVER_KEY }}" > ~/.ssh/id_rsa
          chmod 600 ~/.ssh/id_rsa
          ssh-keyscan -H ${{ secrets.SERVER_IP }} >> ~/.ssh/known_hosts

      - name: Deploy to Server
        run: |
          ssh -i ~/.ssh/id_rsa ${{ secrets.SERVER_USER }}@${{ secrets.SERVER_IP }} << 'EOF'
            cd /home/<USER>/nurtify-health-app-dev
            git checkout dev
            git pull origin dev
            docker compose up -d --build
          EOF

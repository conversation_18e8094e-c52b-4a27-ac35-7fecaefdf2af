/* Add Sponsor Org Page Styles */
.add-sponsor-org-container {
    background-color: #ffffff;
    border-radius: 0; /* Changed */
    box-shadow: none; /* Changed */
    padding: 15px 30px 30px 30px; /* Changed top padding */
    margin-bottom: 30px;
    width: 100%;
}

/* Progress Indicator */
.sponsor-org-progress { /* Renamed */
    display: flex;
    justify-content: space-between;
    margin-bottom: 40px;
    position: relative;
    width: 100%;
    max-width: 800px;
}

.sponsor-org-progress::before { /* Renamed */
    content: '';
    position: absolute;
    top: 15px;
    left: 0;
    width: 100%;
    height: 3px;
    background-color: #E9ECEF;
    z-index: 1;
}

.sponsor-org-step { /* Renamed */
    position: relative;
    z-index: 2;
    display: flex;
    flex-direction: column;
    align-items: center;
    width: 33.33%; /* Assuming 2 steps means this might need adjustment if steps change */
}

.step-indicator { /* Generic - no change */
    width: 34px;
    height: 34px;
    border-radius: 50%;
    background-color: white;
    border: 2px solid #E9ECEF;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 10px;
    font-weight: 600;
    color: #4F547B;
    transition: all 0.3s ease;
    box-shadow: none; /* Changed */
}

.step-label { /* Generic - no change */
    font-size: 14px;
    color: #4F547B;
    text-align: center;
    font-weight: 500;
}

.sponsor-org-step.active .step-indicator { /* Renamed */
    background-color: #37B7C3;
    border-color: #37B7C3;
    color: white;
    box-shadow: none; /* Changed */
}

.sponsor-org-step.active .step-label { /* Renamed */
    color: #37B7C3;
    font-weight: 600;
}

.sponsor-org-step.completed .step-indicator { /* Renamed */
    background-color: #37B7C3;
    border-color: #37B7C3;
    color: white;
    box-shadow: none; /* Changed */
}

.add-sponsor-org-header { /* Renamed */
    display: flex;
    flex-direction: column;
    margin-bottom: 30px;
    padding-bottom: 15px;
    border-bottom: 1px solid #e5e7eb;
}

.add-sponsor-org-title { /* Renamed */
    margin-bottom: 10px;
}

.add-sponsor-org-title h1 { /* Renamed */
    font-size: 24px;
    font-weight: 600;
    color: #1a1a1a;
    display: flex;
    align-items: center;
    gap: 10px;
    margin: 0;
}

.add-sponsor-org-subtitle { /* Renamed */
    color: #4F547B;
    font-size: 16px;
    font-weight: 400;
    margin-top: 5px;
}

.add-sponsor-org-subtitle h6 { /* Renamed */
    margin: 0;
    font-weight: 400;
}

.add-sponsor-org-form { /* Renamed */
    display: flex;
    flex-direction: column;
    gap: 20px;
    width: 100%;
    max-width: 800px;
}

.form-section { /* Generic - no change */
    margin-bottom: 30px;
    background-color: #f9fafb;
    border-radius: 0; /* Changed */
    padding: 25px;
    border: 1px solid #e5e7eb;
}

.form-section-title { /* Generic - no change */
    font-size: 18px;
    font-weight: 600;
    color: #1a1a1a;
    margin-bottom: 20px;
    padding-bottom: 12px;
    border-bottom: 1px solid #e5e7eb;
    display: flex;
    align-items: center;
}

.sponsor-org-form-actions { /* Renamed */
    display: flex;
    justify-content: flex-end;
    margin-top: 30px;
    gap: 15px;
}

.sponsor-org-btn-cancel { /* Renamed */
    min-width: 120px;
    height: 48px;
    background-color: #f5f7fa;
    color: #4F547B;
    border: 1px solid #e5e7eb;
    border-radius: 0; /* Changed */
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
}

.sponsor-org-btn-cancel:hover { /* Renamed */
    background-color: #e5e7eb;
    color: #1a1a1a;
    box-shadow: none; /* Changed */
}

.sponsor-org-btn-submit { /* Renamed */
    min-width: 180px;
    height: 48px;
    background-color: #37B7C3;
    color: white;
    border: none;
    border-radius: 0; /* Changed */
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: none; /* Changed */
}

.sponsor-org-btn-submit:hover { /* Renamed */
    background-color: #2A9A9F;
    transform: none; /* Changed */
    box-shadow: none; /* Changed */
}

.sponsor-org-btn-submit:disabled { /* Renamed */
    background-color: #A8D5DA;
    cursor: not-allowed;
    box-shadow: none;
    transform: none;
}

.row,
.col-md-6 {
  overflow: visible !important;
  position: relative !important;
}

/* Responsive Styles */
@media (max-width: 992px) {
    .add-sponsor-org-container { /* Renamed */
        padding: 25px;
    }
    
    .form-section { /* Generic - no change */
        padding: 20px;
    }
}

@media (max-width: 768px) {
    .add-sponsor-org-container { /* Renamed */
        padding: 20px;
    }
    
    .add-sponsor-org-title h1 { /* Renamed */
        font-size: 20px;
    }
    
    .add-sponsor-org-subtitle { /* Renamed */
        font-size: 14px;
    }
    
    .form-section { /* Generic - no change */
        padding: 15px;
    }
    
    .sponsor-org-form-actions { /* Renamed */
        flex-direction: column;
        gap: 10px;
    }
    
    .sponsor-org-btn-cancel, .sponsor-org-btn-submit { /* Renamed */
        width: 100%;
    }
}

@media (max-width: 576px) {
    .add-sponsor-org-container { /* Renamed */
        padding: 15px;
    }
}

.field-error {
    color: red;
    font-size: 0.875em; /* 14px if base is 16px */
    margin-top: 5px;
  }

.field-warning {
    color: #ff9800; /* Warning color */
    font-size: 0.875em; /* 14px if base is 16px */
    margin-top: 5px;
}
  
  /* Style for NurtifySelect if you want it to look like NurtifyInput */
  .nurtify-select {
      width: 100%;
      padding: 10px 15px; /* Adjust to match NurtifyInput */
      border: 1px solid #ced4da; /* Adjust to match NurtifyInput */
      border-radius: 0; /* Changed */
      background-color: #fff;
      font-size: 1rem; /* Adjust to match NurtifyInput */
      line-height: 1.5; /* Adjust to match NurtifyInput */
      transition: border-color .15s ease-in-out,box-shadow .15s ease-in-out;
  }
  
  .nurtify-select:focus {
      border-color: #37B7C3; /* Or your focus color */
      outline: 0;
      box-shadow: none; /* Changed */
  }

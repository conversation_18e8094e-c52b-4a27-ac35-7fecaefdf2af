import { useState, useEffect } from "react";
import { useSearchParams } from "react-router-dom";
import Preloader from "@/components/common/Preloader";
import Wrapper from "@/components/common/Wrapper";
import DataTable, { Column, Action } from "@/components/common/DataTable";
import { 
  useVisitsByEnrollmentQuery,
  useStudyEnrollmentsByPatientQuery,
  useCreateRescheduleRequestMutation,
  useRescheduleRequestsByPatientQuery
} from "@/hooks/study.query";
import { useCurrentUserQuery } from "@/hooks/user.query";
import { toast } from "react-hot-toast";
import { RescheduleRequestResponse } from "@/types/study";

// Define type for visit data
interface Visit {
  uuid: string;
  name: string;
  study: string;
  study_uuid?: string;
  patient: string;
  date: string;
  number: number;
  allowed_earlier_days: number;
  allowed_later_days: number;
  activities: string[];
  refered_by?: string;
  comments?: string;
  reminder_email?: string;
  visit_status: 'Pending' | 'Completed' | 'Delayed' | 'Canceled';
  refered_date?: string;
}

interface Enrollment {
  uuid: string;
  study_uuid: string;
  study_id?: number;
  study_name: string;
  patient_uuid?: string;
  patient_name: string;
  first_visit: string;
  referred_by: string;
  referred_date: string;
  patient_code?: string;
  study?: {
    uuid: string;
    name: string;
  };
}

interface RescheduleModalProps {
  isOpen: boolean;
  onClose: () => void;
  visit: Visit | null;
  onSubmit: (data: { requested_date: string; requested_time: string; reason: string }) => void;
}

const RescheduleModal: React.FC<RescheduleModalProps> = ({ isOpen, onClose, visit, onSubmit }) => {
  const [requestedDate, setRequestedDate] = useState("");
  const [requestedTime, setRequestedTime] = useState("");
  const [reason, setReason] = useState("");
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [errorMessage, setErrorMessage] = useState("");

  // Check if the visit is in the past
  const isPastVisit = () => {
    if (!visit) return false;
    const visitDate = new Date(visit.date);
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    return visitDate < today;
  };

  // Get date range based on whether it's a past or upcoming visit
  const getDateRange = () => {
    if (!visit) return { min: "", max: "" };
    
    if (isPastVisit()) {
      // For past visits, use the flexible range
      const appointmentDate = new Date(visit.date);
      const earliestDate = new Date(appointmentDate);
      earliestDate.setDate(earliestDate.getDate() - visit.allowed_earlier_days);
      const latestDate = new Date(appointmentDate);
      latestDate.setDate(latestDate.getDate() + visit.allowed_later_days);
      
      return {
        min: earliestDate.toISOString().split('T')[0],
        max: latestDate.toISOString().split('T')[0]
      };
    } else {
      // For upcoming visits, allow any future date
      const today = new Date();
      today.setHours(0, 0, 0, 0);
      return {
        min: today.toISOString().split('T')[0],
        max: "" // No maximum date for upcoming visits
      };
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);
    setErrorMessage(""); // Clear any previous errors
    
    try {
      await onSubmit({ requested_date: requestedDate, requested_time: requestedTime, reason });
      // Reset form
      setRequestedDate("");
      setRequestedTime("");
      setReason("");
    } catch (error: any) {
      console.error('Error in modal:', error);
      const errorData = error?.response?.data;
      if (errorData?.non_field_errors) {
        const message = Array.isArray(errorData.non_field_errors) 
          ? errorData.non_field_errors[0] 
          : errorData.non_field_errors;
        setErrorMessage(message);
      } else {
        setErrorMessage('Failed to submit reschedule request');
      }
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleClose = () => {
    if (!isSubmitting) {
      setRequestedDate("");
      setRequestedTime("");
      setReason("");
      setErrorMessage("");
      onClose();
    }
  };

  if (!isOpen || !visit) return null;

  const dateRange = getDateRange();

  return (
    <div style={{
      position: 'fixed',
      top: 0,
      left: 0,
      right: 0,
      bottom: 0,
      backgroundColor: 'rgba(0, 0, 0, 0.6)',
      display: 'flex',
      justifyContent: 'center',
      alignItems: 'center',
      zIndex: 1000,
      backdropFilter: 'blur(4px)',
      animation: 'fadeIn 0.2s ease-out'
    }}>
      <div style={{
        backgroundColor: 'white',
        padding: '32px',
        borderRadius: '16px',
        width: '520px',
        maxWidth: '90vw',
        maxHeight: '90vh',
        overflowY: 'auto',
        boxShadow: '0 20px 60px rgba(0, 0, 0, 0.15), 0 8px 32px rgba(0, 0, 0, 0.1)',
        border: '1px solid rgba(255, 255, 255, 0.2)',
        animation: 'slideUp 0.3s ease-out'
      }}>
        {/* Header */}
        <div style={{
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'space-between',
          marginBottom: '24px',
          paddingBottom: '16px',
          borderBottom: '1px solid #f0f0f0'
        }}>
          <div>
            <h2 style={{
              margin: 0,
              fontSize: '24px',
              fontWeight: '700',
              color: '#1a1a1a',
              display: 'flex',
              alignItems: 'center',
              gap: '8px'
            }}>
              <span style={{
                display: 'inline-block',
                width: '6px',
                height: '6px',
                backgroundColor: '#3377FF',
                borderRadius: '50%'
              }}></span>
              Reschedule Appointment
            </h2>
            <p style={{
              margin: '4px 0 0 0',
              fontSize: '14px',
              color: '#666',
              fontWeight: '400'
            }}>
              Request a new date and time for your appointment
            </p>
          </div>
          <button
            onClick={handleClose}
            disabled={isSubmitting}
            style={{
              background: 'none',
              border: 'none',
              fontSize: '24px',
              color: '#999',
              cursor: isSubmitting ? 'not-allowed' : 'pointer',
              padding: '4px',
              borderRadius: '4px',
              transition: 'all 0.2s ease',
              opacity: isSubmitting ? 0.5 : 1
            }}
            onMouseEnter={(e) => {
              if (!isSubmitting) {
                e.currentTarget.style.backgroundColor = '#f5f5f5';
                e.currentTarget.style.color = '#333';
              }
            }}
            onMouseLeave={(e) => {
              e.currentTarget.style.backgroundColor = 'transparent';
              e.currentTarget.style.color = '#999';
            }}
          >
            ×
          </button>
        </div>

        {/* Error Message */}
        {errorMessage && (
          <div style={{
            backgroundColor: '#fee2e2',
            border: '1px solid #fecaca',
            borderRadius: '8px',
            padding: '12px 16px',
            marginBottom: '20px',
            color: '#dc2626',
            fontSize: '14px',
            display: 'flex',
            alignItems: 'center',
            gap: '8px'
          }}>
            <svg 
              style={{ flexShrink: 0 }} 
              width="20" 
              height="20" 
              viewBox="0 0 20 20" 
              fill="none" 
              xmlns="http://www.w3.org/2000/svg"
            >
              <path 
                d="M10 18.3333C14.6024 18.3333 18.3334 14.6024 18.3334 10C18.3334 5.39763 14.6024 1.66667 10 1.66667C5.39765 1.66667 1.66669 5.39763 1.66669 10C1.66669 14.6024 5.39765 18.3333 10 18.3333Z" 
                stroke="currentColor" 
                strokeWidth="1.5" 
                strokeLinecap="round" 
                strokeLinejoin="round"
              />
              <path 
                d="M10 6.66667V10" 
                stroke="currentColor" 
                strokeWidth="1.5" 
                strokeLinecap="round" 
                strokeLinejoin="round"
              />
              <path 
                d="M10 13.3333H10.0083" 
                stroke="currentColor" 
                strokeWidth="1.5" 
                strokeLinecap="round" 
                strokeLinejoin="round"
              />
            </svg>
            {errorMessage}
          </div>
        )}

        {/* Current Appointment Info */}
        <div style={{
          backgroundColor: '#f8fafc',
          padding: '16px',
          borderRadius: '12px',
          marginBottom: '24px',
          border: '1px solid #e2e8f0'
        }}>
          <h4 style={{
            margin: '0 0 8px 0',
            fontSize: '16px',
            fontWeight: '600',
            color: '#374151'
          }}>Current Appointment</h4>
          <div style={{ display: 'flex', flexDirection: 'column', gap: '4px' }}>
            <p style={{ margin: 0, fontSize: '14px', color: '#6b7280' }}>
              <strong style={{ color: '#374151' }}>Visit:</strong> {visit.name} (#{visit.number})
            </p>
            <p style={{ margin: 0, fontSize: '14px', color: '#6b7280' }}>
              <strong style={{ color: '#374151' }}>Date:</strong> {new Date(visit.date).toLocaleDateString('en-US', {
                weekday: 'long',
                year: 'numeric',
                month: 'long',
                day: 'numeric'
              })}
            </p>
            {isPastVisit() && (
              <p style={{ margin: 0, fontSize: '14px', color: '#6b7280' }}>
                <strong style={{ color: '#374151' }}>Flexible Range:</strong> {new Date(dateRange.min).toLocaleDateString()} - {new Date(dateRange.max).toLocaleDateString()}
              </p>
            )}
          </div>
        </div>

        <form onSubmit={handleSubmit}>
          {/* Date Input */}
          <div style={{ marginBottom: '20px' }}>
            <label style={{
              display: 'block',
              marginBottom: '8px',
              fontSize: '14px',
              fontWeight: '600',
              color: '#374151'
            }}>Preferred Date *</label>
            <input
              type="date"
              value={requestedDate}
              onChange={(e) => setRequestedDate(e.target.value)}
              min={dateRange.min}
              max={dateRange.max}
              required
              disabled={isSubmitting}
              style={{
                width: '100%',
                padding: '12px 16px',
                borderRadius: '8px',
                border: '2px solid #e5e7eb',
                fontSize: '14px',
                transition: 'all 0.2s ease',
                backgroundColor: isSubmitting ? '#f9fafb' : 'white',
                cursor: isSubmitting ? 'not-allowed' : 'text',
                outline: 'none'
              }}
              onFocus={(e) => {
                if (!isSubmitting) {
                  e.target.style.borderColor = '#3377FF';
                  e.target.style.boxShadow = '0 0 0 3px rgba(51, 119, 255, 0.1)';
                }
              }}
              onBlur={(e) => {
                e.target.style.borderColor = '#e5e7eb';
                e.target.style.boxShadow = 'none';
              }}
            />
            <p style={{
              margin: '4px 0 0 0',
              fontSize: '12px',
              color: '#6b7280'
            }}>
              {isPastVisit() 
                ? 'Please select a date within the allowed range'
                : 'Please select any future date'}
            </p>
          </div>

          {/* Time Input */}
          <div style={{ marginBottom: '20px' }}>
            <label style={{
              display: 'block',
              marginBottom: '8px',
              fontSize: '14px',
              fontWeight: '600',
              color: '#374151'
            }}>Preferred Time *</label>
            <input
              type="time"
              value={requestedTime}
              onChange={(e) => setRequestedTime(e.target.value)}
              required
              disabled={isSubmitting}
              style={{
                width: '100%',
                padding: '12px 16px',
                borderRadius: '8px',
                border: '2px solid #e5e7eb',
                fontSize: '14px',
                transition: 'all 0.2s ease',
                backgroundColor: isSubmitting ? '#f9fafb' : 'white',
                cursor: isSubmitting ? 'not-allowed' : 'text',
                outline: 'none'
              }}
              onFocus={(e) => {
                if (!isSubmitting) {
                  e.target.style.borderColor = '#3377FF';
                  e.target.style.boxShadow = '0 0 0 3px rgba(51, 119, 255, 0.1)';
                }
              }}
              onBlur={(e) => {
                e.target.style.borderColor = '#e5e7eb';
                e.target.style.boxShadow = 'none';
              }}
            />
          </div>

          {/* Reason Textarea */}
          <div style={{ marginBottom: '28px' }}>
            <label style={{
              display: 'block',
              marginBottom: '8px',
              fontSize: '14px',
              fontWeight: '600',
              color: '#374151'
            }}>Reason for Rescheduling *</label>
            <textarea
              value={reason}
              onChange={(e) => setReason(e.target.value)}
              required
              disabled={isSubmitting}
              placeholder="Please provide a brief explanation for why you need to reschedule..."
              style={{
                width: '100%',
                padding: '12px 16px',
                borderRadius: '8px',
                border: '2px solid #e5e7eb',
                fontSize: '14px',
                minHeight: '100px',
                resize: 'vertical',
                fontFamily: 'inherit',
                transition: 'all 0.2s ease',
                backgroundColor: isSubmitting ? '#f9fafb' : 'white',
                cursor: isSubmitting ? 'not-allowed' : 'text',
                outline: 'none'
              }}
              onFocus={(e) => {
                if (!isSubmitting) {
                  e.target.style.borderColor = '#3377FF';
                  e.target.style.boxShadow = '0 0 0 3px rgba(51, 119, 255, 0.1)';
                }
              }}
              onBlur={(e) => {
                e.target.style.borderColor = '#e5e7eb';
                e.target.style.boxShadow = 'none';
              }}
            />
          </div>

          {/* Action Buttons */}
          <div style={{
            display: 'flex',
            justifyContent: 'flex-end',
            gap: '12px',
            paddingTop: '16px',
            borderTop: '1px solid #f0f0f0'
          }}>
            <button
              type="button"
              onClick={handleClose}
              disabled={isSubmitting}
              style={{
                padding: '12px 24px',
                borderRadius: '8px',
                border: '2px solid #e5e7eb',
                backgroundColor: 'white',
                color: '#374151',
                fontSize: '14px',
                fontWeight: '600',
                cursor: isSubmitting ? 'not-allowed' : 'pointer',
                transition: 'all 0.2s ease',
                opacity: isSubmitting ? 0.5 : 1
              }}
              onMouseEnter={(e) => {
                if (!isSubmitting) {
                  e.currentTarget.style.backgroundColor = '#f9fafb';
                  e.currentTarget.style.borderColor = '#d1d5db';
                }
              }}
              onMouseLeave={(e) => {
                e.currentTarget.style.backgroundColor = 'white';
                e.currentTarget.style.borderColor = '#e5e7eb';
              }}
            >
              Cancel
            </button>
            <button
              type="submit"
              disabled={isSubmitting}
              style={{
                padding: '12px 24px',
                borderRadius: '8px',
                border: 'none',
                backgroundColor: isSubmitting ? '#9ca3af' : '#3377FF',
                color: 'white',
                fontSize: '14px',
                fontWeight: '600',
                cursor: isSubmitting ? 'not-allowed' : 'pointer',
                transition: 'all 0.2s ease',
                display: 'flex',
                alignItems: 'center',
                gap: '8px',
                minWidth: '140px',
                justifyContent: 'center'
              }}
              onMouseEnter={(e) => {
                if (!isSubmitting) {
                  e.currentTarget.style.backgroundColor = '#2563eb';
                  e.currentTarget.style.transform = 'translateY(-1px)';
                  e.currentTarget.style.boxShadow = '0 4px 12px rgba(51, 119, 255, 0.3)';
                }
              }}
              onMouseLeave={(e) => {
                if (!isSubmitting) {
                  e.currentTarget.style.backgroundColor = '#3377FF';
                  e.currentTarget.style.transform = 'translateY(0)';
                  e.currentTarget.style.boxShadow = 'none';
                }
              }}
            >
              {isSubmitting ? (
                <>
                  <div style={{
                    width: '16px',
                    height: '16px',
                    border: '2px solid #ffffff',
                    borderTop: '2px solid transparent',
                    borderRadius: '50%',
                    animation: 'spin 1s linear infinite'
                  }}></div>
                  Submitting...
                </>
              ) : (
                'Submit Request'
              )}
            </button>
          </div>
        </form>

        {/* Add CSS animations */}
        <style>{`
          @keyframes fadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
          }
          @keyframes slideUp {
            from {
              opacity: 0;
              transform: translateY(20px) scale(0.95);
            }
            to {
              opacity: 1;
              transform: translateY(0) scale(1);
            }
          }
          @keyframes spin {
            from { transform: rotate(0deg); }
            to { transform: rotate(360deg); }
          }
        `}</style>
      </div>
    </div>
  );
};

const AppointmentsSection = () => {
  const [searchParams] = useSearchParams();
  const enrollmentUuid = searchParams.get('enrollmentUuid') || "";
  const [activeTab, setActiveTab] = useState<'upcoming' | 'history' | 'reschedule'>('upcoming');
  
  const { data: currentUser } = useCurrentUserQuery();
  const patientUuid = (currentUser as any)?.patient_uuid || "";
  
  const { data: enrollments } = useStudyEnrollmentsByPatientQuery(patientUuid);
  const [selectedEnrollment, setSelectedEnrollment] = useState<Enrollment | null>(null);
  const [, setCurrentStudyUuid] = useState<string>("");
  const [studyList, setStudyList] = useState<Enrollment[]>([]);
  
  const { 
    data: visits, 
    isLoading: isLoadingVisits
  } = useVisitsByEnrollmentQuery(
    enrollmentUuid || selectedEnrollment?.uuid || "",
    { enabled: !!(enrollmentUuid || selectedEnrollment?.uuid) }
  );

  const {
    data: rescheduleRequests,
    isLoading: isLoadingRescheduleRequests,
    refetch: refetchRescheduleRequests
  } = useRescheduleRequestsByPatientQuery(patientUuid);

  // Add debug logs
  console.log('Patient UUID:', patientUuid);
  console.log('Raw Reschedule Requests:', rescheduleRequests);
  console.log('Selected Enrollment:', selectedEnrollment);

  // Set selected enrollment based on the URL parameter or choose the first one
  useEffect(() => {
    if (enrollments && enrollments.length > 0) {
      console.log('Available enrollments:', JSON.stringify(enrollments, null, 2));
      setStudyList(enrollments);
      if (enrollmentUuid) {
        const foundEnrollment = enrollments.find((e: any) => e.uuid === enrollmentUuid);
        console.log('Found enrollment from URL:', JSON.stringify(foundEnrollment, null, 2));
        if (foundEnrollment) {
          console.log('Setting enrollment with study UUID:', foundEnrollment.study_uuid);
          setSelectedEnrollment(foundEnrollment);
          setCurrentStudyUuid(foundEnrollment.study_uuid);
        }
      } else if (!selectedEnrollment) {
        // If no enrollment is selected and none in URL, default to first one
        console.log('Setting default enrollment:', JSON.stringify(enrollments[0], null, 2));
        console.log('Default enrollment study UUID:', enrollments[0].study_uuid);
        setSelectedEnrollment(enrollments[0]);
        setCurrentStudyUuid(enrollments[0].study_uuid);
      }
    }
  }, [enrollments, enrollmentUuid, selectedEnrollment]);

  // Filter and sort visits
  const today = new Date();
  today.setHours(0, 0, 0, 0); // Reset time part for accurate date comparison

  const getUpcomingVisits = () => {
    if (!visits) return [];
    return visits
      .filter((visit: Visit) => {
        const visitDate = new Date(visit.date);
        visitDate.setHours(0, 0, 0, 0);
        return visitDate >= today && visit.visit_status !== 'Canceled';
      })
      .sort((a: Visit, b: Visit) => new Date(a.date).getTime() - new Date(b.date).getTime());
  };

  const getPastVisits = () => {
    if (!visits) return [];
    return visits
      .filter((visit: Visit) => {
        const visitDate = new Date(visit.date);
        visitDate.setHours(0, 0, 0, 0);
        return visitDate < today || visit.visit_status === 'Completed' || visit.visit_status === 'Canceled';
      })
      .sort((a: Visit, b: Visit) => new Date(b.date).getTime() - new Date(a.date).getTime()); // Most recent first
  };

  const upcomingVisits = getUpcomingVisits();
  const pastVisits = getPastVisits();
  const nextVisit = upcomingVisits.length > 0 ? upcomingVisits[0] : null;

  // Calculate days until next appointment
  const getDaysUntilAppointment = (date: string) => {
    const visitDate = new Date(date);
    visitDate.setHours(0, 0, 0, 0);
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    
    const diffTime = visitDate.getTime() - today.getTime();
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    return diffDays;
  };

  // Get the date range when the patient can come for the visit
  const getVisitDateRange = (visit: Visit) => {
    const appointmentDate = new Date(visit.date);
    
    const earliestDate = new Date(appointmentDate);
    earliestDate.setDate(earliestDate.getDate() - visit.allowed_earlier_days);
    
    const latestDate = new Date(appointmentDate);
    latestDate.setDate(latestDate.getDate() + visit.allowed_later_days);
    
    return {
      earliest: earliestDate.toLocaleDateString(),
      latest: latestDate.toLocaleDateString(),
      target: appointmentDate.toLocaleDateString()
    };
  };

  // Handle enrollment change
  const handleEnrollmentChange = (enrollment: Enrollment) => {
    setSelectedEnrollment(enrollment);
    setCurrentStudyUuid(enrollment.study_uuid);
  };

  const handleRescheduleRequest = async (data: { requested_date: string; requested_time: string; reason: string }) => {
    if (!selectedVisit?.uuid) return;

    try {
      await createRescheduleRequest.mutateAsync({
        visit_uuid: selectedVisit.uuid,
        requested_date: data.requested_date,
        requested_time: data.requested_time,
        reason: data.reason
      });
      
      toast.success('Reschedule request submitted successfully');
      setIsRescheduleModalOpen(false);
      setSelectedVisit(null);
      
      // Refetch reschedule requests to update the table
      await refetchRescheduleRequests();
    } catch (error: any) {
      console.error('Error submitting reschedule request:', error);
      // Re-throw the error to be handled by the modal
      throw error;
    }
  };

  // Define columns for the visits DataTable
  const visitColumns: Column<Visit>[] = [
    {
      key: "name",
      header: "Visit Name",
    },
    {
      key: "number",
      header: "Visit Number",
    },
    {
      key: "date",
      header: "Date",
      render: (value: string | number | string[] | undefined) => {
        if (typeof value === 'string') {
          return new Date(value).toLocaleDateString();
        }
        return '';
      },
    },
    {
      key: "visit_status",
      header: "Status",
      render: (value: string | number | string[] | undefined) => {
        if (typeof value === 'string') {
          return (
            <span className={`status-badge status-${value.toLowerCase()}`}>
              {value}
            </span>
          );
        }
        return '';
      },
    },
    {
      key: "activities",
      header: "Activities",
      render: (value: string | number | string[] | undefined) => {
        if (Array.isArray(value)) {
          return value.length > 0 ? value.join(", ") : "No activities";
        }
        return "No activities";
      },
    },
  ];

  const visitActions: Action<Visit>[] = [
    {
      label: "Reschedule",
      onClick: (row) => {
        setSelectedVisit(row);
        setIsRescheduleModalOpen(true);
      },
      tooltipText: "Request to reschedule this appointment",
      className: "reschedule-action-btn"
    }
  ];

  // Define columns for reschedule requests
  const rescheduleRequestColumns: Column<RescheduleRequestResponse>[] = [
    {
      key: "visit",
      header: "Visit",
      render: (_, row?: RescheduleRequestResponse) => row?.visit.name,
    },
    {
      key: "visit",
      header: "Study",
      render: (_, row?: RescheduleRequestResponse) => row?.visit.study.name,
    },
    {
      key: "visit",
      header: "Current Date",
      render: (_, row?: RescheduleRequestResponse) => {
        const date = row?.visit.date;
        return date ? new Date(date).toLocaleDateString() : '';
      },
    },
    {
      key: "requested_date",
      header: "Requested Date",
      render: (value: any) => {
        if (typeof value === 'string') {
          return new Date(value).toLocaleDateString();
        }
        return '';
      },
    },
    {
      key: "requested_time",
      header: "Requested Time",
      render: (value: any) => {
        if (typeof value === 'string') {
          return value;
        }
        return '';
      },
    },
    {
      key: "reason",
      header: "Reason",
      render: (value: any) => {
        if (typeof value === 'string') {
          return value;
        }
        return '';
      },
    },
    {
      key: "status",
      header: "Status",
      render: (value: any) => {
        if (typeof value === 'string') {
          return (
            <span className={`status-badge status-${value.toLowerCase()}`}>
              {value}
            </span>
          );
        }
        return '';
      },
    },
    {
      key: "created_at",
      header: "Requested On",
      render: (value: any) => {
        if (typeof value === 'string') {
          return new Date(value).toLocaleDateString();
        }
        return '';
      },
    },
  ];

  // Filter reschedule requests by selected study
  const filteredRescheduleRequests = rescheduleRequests || [];

  console.log('Filtered Reschedule Requests:', filteredRescheduleRequests);

  const [selectedVisit, setSelectedVisit] = useState<Visit | null>(null);
  const [isRescheduleModalOpen, setIsRescheduleModalOpen] = useState(false);
  const createRescheduleRequest = useCreateRescheduleRequestMutation();

  return (
    <>
      <Wrapper>
        <Preloader />

        <div className="content-wrapper js-content-wrapper">
          <div className="bg-light-4 px-3 py-5">
            <div className="container-fluid py-6 px-6">
              <div className="patient-details-container">
                <div className="patient-details-header">
                  <h1 className="page-title">Patient Appointments</h1>
                </div>

                {/* Study Selection Section */}
                <div className="study-selection-container" style={{ marginBottom: '30px' }}>
                  <div style={{ marginBottom: '15px' }}>
                    <h3 style={{ fontSize: '18px', margin: '0 0 10px 0' }}>Select Study</h3>
                    {studyList && studyList.length > 0 ? (
                      <div className="study-tabs" style={{ 
                        display: 'flex', 
                        gap: '10px',
                        flexWrap: 'wrap'
                      }}>
                        {studyList.map((enrollment) => (
                          <button 
                            key={enrollment.uuid}
                            onClick={() => handleEnrollmentChange(enrollment)}
                            style={{
                              padding: '8px 16px',
                              borderRadius: '4px',
                              cursor: 'pointer',
                              backgroundColor: selectedEnrollment?.uuid === enrollment.uuid ? '#3377FF' : '#f5f5f5',
                              color: selectedEnrollment?.uuid === enrollment.uuid ? 'white' : 'black',
                              border: 'none',
                              fontWeight: selectedEnrollment?.uuid === enrollment.uuid ? '600' : '400'
                            }}
                          >
                            {enrollment.study_name}
                          </button>
                        ))}
                      </div>
                    ) : (
                      <div>No studies found.</div>
                    )}
                  </div>
                </div>

                {selectedEnrollment && (
                  <div className="study-info-container" style={{ marginBottom: '20px' }}>
                    <h3>Appointments for: {selectedEnrollment.study_name}</h3>
                  </div>
                )}

                {/* Next Appointment Reminder */}
                {nextVisit && activeTab !== 'reschedule' && (
                  <div className="next-appointment-reminder" style={{
                    backgroundColor: '#e9f5ff',
                    border: '1px solid #c5e4ff',
                    borderLeft: '4px solid #3377FF',
                    padding: '15px 20px',
                    borderRadius: '4px',
                    marginBottom: '25px'
                  }}>
                    <h3 style={{ color: '#1a5fb4', margin: '0 0 8px 0', fontSize: '16px' }}>
                      Your Next Appointment
                    </h3>
                    <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                      <div>
                        <p style={{ margin: '0 0 5px 0', fontSize: '18px', fontWeight: '600' }}>
                          {nextVisit.name} (Visit #{nextVisit.number})
                        </p>
                        <p style={{ margin: '0 0 5px 0' }}>
                          <strong>Date:</strong> {new Date(nextVisit.date).toLocaleDateString()}
                          {getDaysUntilAppointment(nextVisit.date) === 0 && (
                            <span style={{ marginLeft: '8px', backgroundColor: '#ff6b6b', color: 'white', padding: '2px 8px', borderRadius: '10px', fontSize: '12px' }}>
                              TODAY!
                            </span>
                          )}
                          {getDaysUntilAppointment(nextVisit.date) > 0 && getDaysUntilAppointment(nextVisit.date) <= 3 && (
                            <span style={{ marginLeft: '8px', backgroundColor: '#ffa94d', color: 'white', padding: '2px 8px', borderRadius: '10px', fontSize: '12px' }}>
                              COMING SOON!
                            </span>
                          )}
                        </p>
                        <p style={{ margin: '0', fontSize: '14px' }}>
                          <strong>Activities:</strong> {nextVisit.activities && nextVisit.activities.length > 0 
                            ? nextVisit.activities.join(", ") 
                            : "No activities"}
                        </p>
                      </div>
                      <div style={{
                        backgroundColor: '#1a5fb4',
                        color: 'white',
                        padding: '12px 20px',
                        borderRadius: '8px',
                        textAlign: 'center'
                      }}>
                        {getDaysUntilAppointment(nextVisit.date) === 0 ? (
                          <div>
                            <div style={{ fontSize: '24px', fontWeight: '700' }}>Today</div>
                            <div style={{ fontSize: '14px' }}>Your appointment is today!</div>
                          </div>
                        ) : getDaysUntilAppointment(nextVisit.date) === 1 ? (
                          <div>
                            <div style={{ fontSize: '24px', fontWeight: '700' }}>Tomorrow</div>
                            <div style={{ fontSize: '14px' }}>Your appointment is tomorrow!</div>
                          </div>
                        ) : (
                          <div>
                            <div style={{ fontSize: '24px', fontWeight: '700' }}>{getDaysUntilAppointment(nextVisit.date)}</div>
                            <div style={{ fontSize: '14px' }}>Days until appointment</div>
                          </div>
                        )}
                      </div>
                    </div>
                    <div style={{ marginTop: '10px', fontSize: '14px', color: '#555' }}>
                      <p style={{ margin: '0' }}>
                        <strong>Flexible Date Range:</strong> You can attend between {getVisitDateRange(nextVisit).earliest} and {getVisitDateRange(nextVisit).latest}
                      </p>
                    </div>
                  </div>
                )}

                {/* Appointment Tabs */}
                <div className="appointment-tabs" style={{ marginBottom: '15px' }}>
                  <div style={{ display: 'flex', borderBottom: '1px solid #ddd' }}>
                    <button 
                      onClick={() => setActiveTab('upcoming')}
                      style={{
                        padding: '10px 20px',
                        backgroundColor: 'transparent',
                        border: 'none',
                        borderBottom: activeTab === 'upcoming' ? '3px solid #3377FF' : '3px solid transparent',
                        color: activeTab === 'upcoming' ? '#3377FF' : '#333',
                        fontWeight: activeTab === 'upcoming' ? '600' : '400',
                        cursor: 'pointer'
                      }}
                    >
                      Upcoming Appointments
                    </button>
                    <button 
                      onClick={() => setActiveTab('history')}
                      style={{
                        padding: '10px 20px',
                        backgroundColor: 'transparent',
                        border: 'none',
                        borderBottom: activeTab === 'history' ? '3px solid #3377FF' : '3px solid transparent',
                        color: activeTab === 'history' ? '#3377FF' : '#333',
                        fontWeight: activeTab === 'history' ? '600' : '400',
                        cursor: 'pointer'
                      }}
                    >
                      Appointment History
                    </button>
                    <button 
                      onClick={() => setActiveTab('reschedule')}
                      style={{
                        padding: '10px 20px',
                        backgroundColor: 'transparent',
                        border: 'none',
                        borderBottom: activeTab === 'reschedule' ? '3px solid #3377FF' : '3px solid transparent',
                        color: activeTab === 'reschedule' ? '#3377FF' : '#333',
                        fontWeight: activeTab === 'reschedule' ? '600' : '400',
                        cursor: 'pointer'
                      }}
                    >
                      Reschedule Requests
                    </button>
                  </div>
                </div>

                <div className="visits-container">
                  <div className="visits-header" style={{ 
                    display: 'flex', 
                    justifyContent: 'space-between', 
                    alignItems: 'center',
                    marginBottom: '20px'
                  }}>
                    <h3 style={{ margin: 0, fontSize: '18px', fontWeight: 600 }}>
                      {activeTab === 'upcoming' ? 'Upcoming Appointments' : 
                       activeTab === 'history' ? 'Appointment History' : 
                       'Reschedule Requests'}
                    </h3>
                  </div>
                  
                  {activeTab === 'reschedule' ? (
                    isLoadingRescheduleRequests ? (
                      <div className="loading-studies">Loading reschedule requests...</div>
                    ) : filteredRescheduleRequests && filteredRescheduleRequests.length > 0 ? (
                      <DataTable 
                        data={filteredRescheduleRequests}
                        columns={rescheduleRequestColumns}
                      />
                    ) : (
                      <div className="no-studies">
                        No reschedule requests found for this study.
                      </div>
                    )
                  ) : isLoadingVisits ? (
                    <div className="loading-studies">Loading appointments...</div>
                  ) : activeTab === 'upcoming' ? (
                    upcomingVisits.length > 0 ? (
                      <DataTable 
                        data={upcomingVisits}
                        columns={visitColumns}
                        actions={visitActions}
                      />
                    ) : (
                      <div className="no-studies">
                        No upcoming appointments found for this study.
                      </div>
                    )
                  ) : (
                    pastVisits.length > 0 ? (
                      <DataTable 
                        data={pastVisits}
                        columns={visitColumns}
                        actions={visitActions}
                      />
                    ) : (
                      <div className="no-studies">
                        No appointment history found for this study.
                      </div>
                    )
                  )}
                </div>
              </div>
            </div>
          </div>
        </div>

        <RescheduleModal
          isOpen={isRescheduleModalOpen}
          onClose={() => {
            setIsRescheduleModalOpen(false);
            setSelectedVisit(null);
          }}
          visit={selectedVisit}
          onSubmit={handleRescheduleRequest}
        />
      </Wrapper>
    </>
  );
};

export default AppointmentsSection;

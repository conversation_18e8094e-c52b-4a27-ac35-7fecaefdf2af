/* frontend/src/components/carousel.css */

.nurtify-carousel-wrapper {
  position: relative;
  width: 100%;
  overflow: hidden; /* Hide overflow if slides have different heights */
  border-radius: 8px; /* Optional: rounded corners */
  background-color: #f9fafb; /* Optional: light background */
  padding: 1rem 0; /* Add some vertical padding */
}

.carousel-slide-container {
  display: flex; /* Use flex to manage slide display (though only one is shown) */
  justify-content: center; /* Center the current slide */
  align-items: center;
  min-height: 150px; /* Ensure a minimum height */
  padding: 0 40px; /* Padding to avoid content hitting arrows */
}

/* Style for the content within the slide if needed */
.carousel-slide-container > * {
  width: 100%; /* Make slide content take full width */
  text-align: center;
}

.carousel-arrow {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  background-color: rgba(0, 0, 0, 0.3);
  color: white;
  border: none;
  border-radius: 50%;
  width: 30px;
  height: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  z-index: 10;
  transition: background-color 0.2s ease;
}

.carousel-arrow:hover {
  background-color: rgba(0, 0, 0, 0.5);
}

.carousel-arrow.left-arrow {
  left: 10px;
}

.carousel-arrow.right-arrow {
  right: 10px;
}

.carousel-dots {
  position: absolute;
  bottom: 10px;
  left: 50%;
  transform: translateX(-50%);
  display: flex;
  gap: 8px;
}

.carousel-dot {
  width: 10px;
  height: 10px;
  border-radius: 50%;
  background-color: rgba(0, 0, 0, 0.2);
  border: none;
  padding: 0;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.carousel-dot:hover {
  background-color: rgba(0, 0, 0, 0.4);
}

.carousel-dot.active {
  background-color: rgba(0, 0, 0, 0.6);
}
